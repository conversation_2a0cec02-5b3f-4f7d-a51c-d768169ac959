# 员工管理和多级权限系统说明

## 概述

系统使用加密文件存储员工信息，支持**服务器端细粒度权限分级管理**。2025/07/03 + 14:00 版本引入了多级权限控制和服务器端验证机制。

## 🔐 2025/07/03 + 14:00 + 多级权限系统架构

### 服务器端权限验证（安全增强）
- **权限验证位置**: 服务器端（server3/api/program/authorize）
- **验证时机**: 每次程序启动时实时验证
- **安全特性**: 客户端无法绕过权限检查

### 多级权限等级系统

#### h9 - 最高权限（超级管理员）
- **员工姓名**: mike
- **员工ID**: 215829
- **密码**: test123
- **权限**: h9（最高权限，可启动所有UI程序）
- **可访问程序**: program1 + program2

#### h8 - 高级权限（管理层）
- **员工姓名**: manager
- **员工ID**: 200001
- **密码**: manager123
- **权限**: h8（高级权限，可启动员工操作界面）
- **可访问程序**: program1

#### h7 - 专业权限（技术人员）
- **员工姓名**: engineer
- **员工ID**: 300001
- **密码**: engineer123
- **权限**: h7（专业权限，可启动PLC编程工具）
- **可访问程序**: program2

#### normal - 普通权限（操作员）
- **员工姓名**: operator, test_user
- **员工ID**: 400001, 100001
- **密码**: operator123, test456
- **权限**: normal（普通权限，仅基础功能）
- **可访问程序**: 无

## 程序权限映射表

| 程序名称 | 描述 | 需要权限 | 说明 |
|---------|------|----------|------|
| program1 | 员工操作界面 | h9, h8 | 工时数据录入和管理 |
| program2 | PLC编程工具 | h9, h7 | 设备编程和配置 |

## 如何添加新员工

### 方法1：删除加密文件自动重新生成（推荐）

1. 停止server3服务
2. 删除加密文件：`server3/app/config/employees.encrypted`
3. 修改代码文件：`server3/app/routers/auth_login.py`
4. 找到 `ensure_employee_data_exists()` 函数中的员工数据定义
5. 按以下格式添加新员工：

```python
employee_data = {
    "mike": {
        "id": "215829",
        "password": "test123",
        "permission": "h9"
    },
    "test_user": {
        "id": "100001", 
        "password": "test456",
        "permission": "normal"
    },
    # 添加新员工
    "new_employee": {
        "id": "300001",
        "password": "newpass123",
        "permission": "normal"  # 或 "h9"
    }
}
```

6. 重启server3服务，系统会自动生成新的加密文件

### 方法2：手动解密编辑（高级用户）

1. 运行测试端点获取当前员工数据：
   ```
   GET http://localhost:8006/test-decrypt
   ```

2. 使用相同的加密方法重新生成加密文件

## 权限分配原则

- **h9权限**: 仅分配给系统管理员和高级用户
- **normal权限**: 分配给一般操作员工

## 🛡️ 安全架构升级（2025/07/03 + 14:00）

### 服务器端权限验证流程
1. **登录验证**: 客户端提交员工ID和密码
2. **JWT签发**: 服务器验证成功后签发包含权限信息的JWT token
3. **程序启动**: 客户端向服务器请求程序启动授权
4. **权限验证**: 服务器端验证JWT token和权限等级
5. **双重确认**: 检查token权限与员工数据文件权限一致性
6. **授权决定**: 服务器决定是否允许程序启动

### 权限验证API端点
- `POST /api/program/authorize` - 程序启动授权验证
- `GET /api/program/permissions/{employee_id}` - 获取员工权限信息
- `GET /api/program/available-programs` - 获取可用程序列表

## 安全注意事项

1. **权限验证**: 所有权限检查在服务器端进行，客户端无法绕过
2. **加密存储**: 加密密钥固定在代码中，生产环境建议使用环境变量
3. **密码安全**: 密码明文存储，建议改为哈希存储
4. **定期审核**: 定期更换密码和权限审核
5. **数据备份**: 备份员工数据文件
6. **日志记录**: 所有权限请求都会在服务器端记录日志

## 数据流程更新说明

### 用户操作流程（source='user' → source='system'）
1. 用户在UI界面操作数据 → 写入entries表，source='user'
2. PostgreSQL触发器检测到source='user'，创建同步任务
3. f2服务处理同步任务，将数据同步到MDB
4. 同步成功后，f2自动将source更新为'system'

### 系统拉取流程（始终source='system'）
1. f3服务从MDB拉取数据
2. 通过staging_entries表处理
3. 写入entries表时，source='system'
4. 不触发PostgreSQL触发器，避免循环同步

## 故障排除

### 权限问题
- 确认员工ID对应的权限等级
- 检查JWT token中的权限信息
- 验证客户端权限检查逻辑

### 登录问题
- 确认员工ID和密码正确
- 检查硬件指纹是否已注册
- 验证server3服务是否正常运行

### 数据同步问题
- 检查source字段的正确设置
- 确认PostgreSQL触发器是否正常工作
- 验证f2和f3服务的运行状态 