#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
MySuite 视频监控功能演示脚本
20250619.18:00 视频监控演示
"""

import time
import requests
import json
from datetime import datetime

def print_banner():
    """打印横幅"""
    print("=" * 60)
    print("🎥 MySuite 视频监控功能演示")
    print("=" * 60)
    print()

def check_services():
    """检查所有微服务状态"""
    print("🏥 检查微服务状态...")
    services = [
        ("主服务", "http://localhost:8003/health"),
        ("聊天微服务", "http://localhost:8005/health"),
        ("认证微服务", "http://localhost:8006/health"),
        ("视频监控微服务", "http://localhost:8007/api/video/health")
    ]
    
    all_healthy = True
    for name, url in services:
        try:
            response = requests.get(url, timeout=3)
            if response.status_code == 200:
                print(f"✅ {name} - 运行正常")
            else:
                print(f"❌ {name} - 状态异常 ({response.status_code})")
                all_healthy = False
        except Exception as e:
            print(f"❌ {name} - 连接失败: {e}")
            all_healthy = False
    
    return all_healthy

def get_video_service_info():
    """获取视频服务信息"""
    print("\n🔍 获取视频服务详细信息...")
    try:
        # 获取服务信息
        info_response = requests.get("http://localhost:8007/api/video/info", timeout=3)
        if info_response.status_code == 200:
            info_data = info_response.json()
            print(f"📋 服务名称: {info_data.get('service_name', 'N/A')}")
            print(f"📋 版本: {info_data.get('version', 'N/A')}")
            print(f"📋 端口: {info_data.get('port', 'N/A')}")
            
            video_config = info_data.get('video_config', {})
            print(f"🎥 视频配置: {video_config.get('width', 'N/A')}x{video_config.get('height', 'N/A')} @ {video_config.get('fps', 'N/A')}FPS")
            print(f"🎥 质量: {video_config.get('quality', 'N/A')}%")
        
        # 获取统计信息
        stats_response = requests.get("http://localhost:8007/api/video/stats", timeout=3)
        if stats_response.status_code == 200:
            stats_data = stats_response.json()
            print(f"📊 摄像头状态: {stats_data.get('camera_status', 'N/A')}")
            print(f"📊 连接客户端: {stats_data.get('connected_clients', 0)}")
            print(f"📊 总帧数: {stats_data.get('total_frames', 0)}")
            
    except Exception as e:
        print(f"❌ 获取视频服务信息失败: {e}")

def test_snapshot():
    """测试快照功能"""
    print("\n📸 测试快照功能...")
    try:
        response = requests.get("http://localhost:8007/api/video/snapshot", timeout=5)
        if response.status_code == 200:
            data = response.json()
            if data.get("success"):
                print("✅ 快照获取成功")
                print(f"📸 时间戳: {data.get('timestamp', 'N/A')}")
                print(f"📸 图像大小: {len(data.get('data', {}).get('image', '')) // 1024}KB (base64)")
            else:
                print(f"⚠️  快照获取失败: {data.get('error', '未知错误')}")
        else:
            print(f"❌ 快照请求失败: HTTP {response.status_code}")
    except Exception as e:
        print(f"❌ 快照测试失败: {e}")

def show_client_instructions():
    """显示客户端使用说明"""
    print("\n" + "=" * 60)
    print("📱 客户端使用说明")
    print("=" * 60)
    print()
    print("1. 启动客户端:")
    print("   python client/client_fixed.py")
    print()
    print("2. 登录账户:")
    print("   员工ID: 215829")
    print("   密码: test123")
    print()
    print("3. 访问视频监控:")
    print("   ➤ 点击第3个标签页 '视频监控'")
    print("   ➤ 点击 '连接视频' 按钮")
    print("   ➤ 观看实时视频画面")
    print("   ➤ 点击 '获取快照' 保存画面")
    print()
    print("4. 界面功能:")
    print("   🔴 连接状态显示")
    print("   📺 640x480 视频画面")
    print("   📊 实时FPS和统计信息")
    print("   📸 快照功能")
    print()

def show_technical_details():
    """显示技术细节"""
    print("=" * 60)
    print("🔧 技术实现细节")
    print("=" * 60)
    print()
    print("🏗️ 架构:")
    print("   Server4 (端口8007) - FastAPI + OpenCV + WebSocket")
    print("   客户端 - PyQt6 + WebSocket + Base64解码")
    print()
    print("🌐 API端点:")
    print("   GET  /api/video/health   - 健康检查")
    print("   GET  /api/video/info     - 服务信息")
    print("   GET  /api/video/stats    - 统计数据")
    print("   GET  /api/video/snapshot - 获取快照")
    print("   WS   /ws/video          - 实时视频流")
    print()
    print("🎥 视频流程:")
    print("   1. OpenCV捕获摄像头")
    print("   2. JPEG压缩编码")
    print("   3. Base64字符串编码")
    print("   4. WebSocket实时传输")
    print("   5. 客户端解码显示")
    print()

def show_future_plans():
    """显示未来计划"""
    print("=" * 60)
    print("🔮 未来扩展计划")
    print("=" * 60)
    print()
    print("📈 短期目标:")
    print("   ✅ 基础视频流传输")
    print("   ✅ 多客户端支持")
    print("   ✅ REST API接口")
    print("   🔄 录像功能")
    print("   🔄 多摄像头支持")
    print()
    print("🤖 长期规划:")
    print("   🎯 YOLO目标检测")
    print("   🧠 智能行为分析")
    print("   ☁️  云端存储")
    print("   📱 移动端支持")
    print()

def main():
    """主函数"""
    print_banner()
    
    # 检查服务状态
    if not check_services():
        print("\n❌ 部分服务未运行，请先启动所有微服务:")
        print("   ./start_microservices.sh")
        return
    
    # 获取视频服务信息
    get_video_service_info()
    
    # 测试快照功能
    test_snapshot()
    
    # 显示使用说明
    show_client_instructions()
    
    # 显示技术细节
    show_technical_details()
    
    # 显示未来计划
    show_future_plans()
    
    print("=" * 60)
    print("🎉 演示完成！现在可以启动客户端体验视频监控功能")
    print("=" * 60)

if __name__ == "__main__":
    main() 