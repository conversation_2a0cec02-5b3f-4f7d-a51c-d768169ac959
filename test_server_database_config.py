#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试server数据库配置
验证program1.py使用server配置是否能正常连接数据库
"""

import sys
import asyncio
import asyncpg
from pathlib import Path

def test_server_database_config():
    """测试server数据库配置"""
    print("=== 测试Server数据库配置 ===")
    
    try:
        # 添加server/app路径
        config_path = Path(__file__).parent / "server" / "app"
        sys.path.append(str(config_path))
        
        # 导入配置
        from config import DATABASE_URL, IMDB_DATABASE_URL, TABLE_CONFIG
        
        print(f"✅ 成功导入server配置")
        print(f"主数据库URL: {DATABASE_URL}")
        print(f"IMDB数据库URL: {IMDB_DATABASE_URL}")
        print(f"表格配置: {list(TABLE_CONFIG.keys())}")
        
        return True
        
    except Exception as e:
        print(f"❌ 导入server配置失败: {e}")
        return False

async def test_database_connections():
    """测试数据库连接"""
    print("\n=== 测试数据库连接 ===")
    
    try:
        # 添加server/app路径
        config_path = Path(__file__).parent / "server" / "app"
        sys.path.append(str(config_path))
        
        # 导入配置
        from config import DATABASE_URL, IMDB_DATABASE_URL
        
        # 测试主数据库连接
        print("测试主数据库连接...")
        conn1 = await asyncpg.connect(DATABASE_URL)
        print("✅ 主数据库连接成功")
        
        # 测试IMDB数据库连接
        print("测试IMDB数据库连接...")
        conn2 = await asyncpg.connect(IMDB_DATABASE_URL)
        print("✅ IMDB数据库连接成功")
        
        # 测试entries表
        print("测试entries表...")
        entries_count = await conn2.fetchval("SELECT COUNT(*) FROM entries WHERE employee_id = $1", "215829")
        print(f"✅ 员工215829有 {entries_count} 条entries记录")
        
        # 测试timeprotab分区表
        print("测试timeprotab分区表...")
        partitions = await conn1.fetch("""
            SELECT table_name 
            FROM information_schema.tables 
            WHERE table_name LIKE 'timeprotab_%' 
              AND table_schema = 'public'
            ORDER BY table_name
        """)
        print(f"✅ 找到 {len(partitions)} 个timeprotab分区表:")
        for partition in partitions:
            print(f"  - {partition['table_name']}")
        
        await conn1.close()
        await conn2.close()
        
        return True
        
    except Exception as e:
        print(f"❌ 数据库连接测试失败: {e}")
        return False

async def test_table_config():
    """测试表格配置"""
    print("\n=== 测试表格配置 ===")
    
    try:
        # 添加server/app路径
        config_path = Path(__file__).parent / "server" / "app"
        sys.path.append(str(config_path))
        
        # 导入配置
        from config import TABLE_CONFIG, IMDB_DATABASE_URL
        
        conn = await asyncpg.connect(IMDB_DATABASE_URL)
        
        # 测试entries表结构
        print("测试entries表结构...")
        entries_columns = await conn.fetch("""
            SELECT column_name, data_type 
            FROM information_schema.columns 
            WHERE table_name = 'entries' 
              AND table_schema = 'public'
            ORDER BY ordinal_position
        """)
        
        print("✅ entries表实际字段:")
        actual_columns = [col['column_name'] for col in entries_columns]
        for col in entries_columns:
            print(f"  - {col['column_name']} ({col['data_type']})")
        
        # 检查配置中的字段是否存在
        config_columns = TABLE_CONFIG['entries']['columns']
        print(f"\n配置中的字段: {config_columns}")
        
        missing_columns = []
        for col in config_columns:
            if col not in actual_columns:
                missing_columns.append(col)
        
        if missing_columns:
            print(f"⚠️ 配置中有但实际不存在的字段: {missing_columns}")
        else:
            print("✅ 所有配置字段都存在")
        
        await conn.close()
        return True
        
    except Exception as e:
        print(f"❌ 表格配置测试失败: {e}")
        return False

async def main():
    """主测试函数"""
    print("开始测试Server数据库配置...")
    
    # 测试配置导入
    config_ok = test_server_database_config()
    if not config_ok:
        print("❌ 配置导入失败，停止测试")
        return
    
    # 测试数据库连接
    connection_ok = await test_database_connections()
    if not connection_ok:
        print("❌ 数据库连接失败，停止测试")
        return
    
    # 测试表格配置
    table_ok = await test_table_config()
    if not table_ok:
        print("❌ 表格配置测试失败")
        return
    
    print("\n🎉 所有测试通过！Server数据库配置正确")

if __name__ == "__main__":
    asyncio.run(main()) 