#!/usr/bin/env python3
"""
数据库初始化脚本
25/06/25 17：00 创建 - 用于postgres 16到17升级后重建丢失的表结构
支持初始化所有微服务需要的数据库和表结构
"""

import asyncio
import asyncpg
import sys
from pathlib import Path

# 数据库配置
DB_CONFIG = {
    'host': '************',
    'port': 5432,  # 25/06/25 17：00 更新为postgres17.5标准端口
    'user': 'postgres',
    'password': 'pojiami0602'
}

# 需要创建的数据库列表
DATABASES = ['postgres', 'auth', 'imdb']

async def create_database_if_not_exists(database_name: str):
    """创建数据库如果不存在"""
    try:
        # 连接到默认的postgres数据库
        conn = await asyncpg.connect(
            host=DB_CONFIG['host'],
            port=DB_CONFIG['port'],
            user=DB_CONFIG['user'],
            password=DB_CONFIG['password'],
            database='postgres'
        )
        
        # 检查数据库是否存在
        exists = await conn.fetchval(
            "SELECT 1 FROM pg_database WHERE datname = $1",
            database_name
        )
        
        if not exists:
            print(f"📊 创建数据库: {database_name}")
            await conn.execute(f'CREATE DATABASE "{database_name}"')
            print(f"✅ 数据库 {database_name} 创建成功")
        else:
            print(f"✅ 数据库 {database_name} 已存在")
            
        await conn.close()
        
    except Exception as e:
        print(f"❌ 创建数据库 {database_name} 失败: {e}")
        return False
    return True

async def init_auth_database():
    """初始化认证数据库表结构"""
    try:
        conn = await asyncpg.connect(
            host=DB_CONFIG['host'],
            port=DB_CONFIG['port'],
            user=DB_CONFIG['user'],
            password=DB_CONFIG['password'],
            database='auth'
        )
        
        print("📊 初始化认证数据库表结构...")
        
        # 创建users表 (标准用户表 - JWT认证系统)
        await conn.execute('''
            CREATE TABLE IF NOT EXISTS users (
                username VARCHAR PRIMARY KEY,
                employee_id VARCHAR,
                encrypted_password BYTEA NOT NULL,
                email VARCHAR,
                full_name VARCHAR,
                is_active VARCHAR DEFAULT 'true',
                created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
            )
        ''')
        
        # 创建索引
        await conn.execute('CREATE INDEX IF NOT EXISTS idx_users_employee_id ON users(employee_id)')
        await conn.execute('CREATE INDEX IF NOT EXISTS idx_users_email ON users(email)')
        
        # 创建硬件指纹表
        await conn.execute('''
            CREATE TABLE IF NOT EXISTS hardware_fingerprints (
                employee_id VARCHAR,
                fingerprint VARCHAR,
                registration_date TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
                PRIMARY KEY (employee_id, fingerprint)
            )
        ''')
        
        # 创建索引
        await conn.execute('CREATE INDEX IF NOT EXISTS idx_hardware_employee_id ON hardware_fingerprints(employee_id)')
        await conn.execute('CREATE INDEX IF NOT EXISTS idx_hardware_fingerprint ON hardware_fingerprints(fingerprint)')
        
        print("✅ 认证数据库表结构初始化完成")
        await conn.close()
        
    except Exception as e:
        print(f"❌ 初始化认证数据库失败: {e}")
        return False
    return True

async def init_main_database():
    """初始化主业务数据库表结构"""
    try:
        conn = await asyncpg.connect(
            host=DB_CONFIG['host'],
            port=DB_CONFIG['port'],
            user=DB_CONFIG['user'],
            password=DB_CONFIG['password'],
            database='postgres'
        )
        
        print("📊 初始化主业务数据库表结构...")
        
        # 创建功能标志表
        await conn.execute('''
            CREATE TABLE IF NOT EXISTS feature_flags (
                id SERIAL PRIMARY KEY,
                flag_name VARCHAR UNIQUE NOT NULL,
                is_enabled BOOLEAN DEFAULT FALSE,
                description TEXT,
                created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
                updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
            )
        ''')
        
        # 创建任务表
        await conn.execute('''
            CREATE TABLE IF NOT EXISTS tasks (
                id SERIAL PRIMARY KEY,
                title VARCHAR NOT NULL,
                description TEXT,
                status VARCHAR DEFAULT 'pending',
                priority VARCHAR DEFAULT 'medium',
                assigned_to VARCHAR,
                created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
                updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
                due_date TIMESTAMP WITH TIME ZONE
            )
        ''')
        
        # 创建索引
        await conn.execute('CREATE INDEX IF NOT EXISTS idx_tasks_status ON tasks(status)')
        await conn.execute('CREATE INDEX IF NOT EXISTS idx_tasks_assigned_to ON tasks(assigned_to)')
        await conn.execute('CREATE INDEX IF NOT EXISTS idx_tasks_due_date ON tasks(due_date)')
        
        print("✅ 主业务数据库表结构初始化完成")
        await conn.close()
        
    except Exception as e:
        print(f"❌ 初始化主业务数据库失败: {e}")
        return False
    return True

async def init_imdb_database():
    """初始化IMDB数据库表结构"""
    try:
        conn = await asyncpg.connect(
            host=DB_CONFIG['host'],
            port=DB_CONFIG['port'],
            user=DB_CONFIG['user'],
            password=DB_CONFIG['password'],
            database='imdb'
        )
        
        print("📊 初始化IMDB数据库表结构...")
        
        # 创建add25表 - 工时添加记录
        await conn.execute('''
            CREATE TABLE IF NOT EXISTS add25 (
                id SERIAL PRIMARY KEY,
                employee_id VARCHAR NOT NULL,
                date DATE NOT NULL,
                hours DECIMAL(4,2) NOT NULL,
                project_code VARCHAR,
                description TEXT,
                created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
            )
        ''')
        
        # 创建change25表 - 工时变更记录
        await conn.execute('''
            CREATE TABLE IF NOT EXISTS change25 (
                id SERIAL PRIMARY KEY,
                employee_id VARCHAR NOT NULL,
                date DATE NOT NULL,
                old_hours DECIMAL(4,2),
                new_hours DECIMAL(4,2) NOT NULL,
                project_code VARCHAR,
                change_reason TEXT,
                created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
            )
        ''')
        
        # 创建del25表 - 工时删除记录
        await conn.execute('''
            CREATE TABLE IF NOT EXISTS del25 (
                id SERIAL PRIMARY KEY,
                employee_id VARCHAR NOT NULL,
                date DATE NOT NULL,
                deleted_hours DECIMAL(4,2) NOT NULL,
                project_code VARCHAR,
                delete_reason TEXT,
                created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
            )
        ''')
        
        # 创建索引
        await conn.execute('CREATE INDEX IF NOT EXISTS idx_add25_employee_date ON add25(employee_id, date)')
        await conn.execute('CREATE INDEX IF NOT EXISTS idx_change25_employee_date ON change25(employee_id, date)')
        await conn.execute('CREATE INDEX IF NOT EXISTS idx_del25_employee_date ON del25(employee_id, date)')
        
        print("✅ IMDB数据库表结构初始化完成")
        await conn.close()
        
    except Exception as e:
        print(f"❌ 初始化IMDB数据库失败: {e}")
        return False
    return True

async def test_connections():
    """测试所有数据库连接"""
    print("\n🔍 测试数据库连接...")
    
    for db_name in DATABASES:
        try:
            conn = await asyncpg.connect(
                host=DB_CONFIG['host'],
                port=DB_CONFIG['port'],
                user=DB_CONFIG['user'],
                password=DB_CONFIG['password'],
                database=db_name
            )
            
            # 执行简单查询测试连接
            result = await conn.fetchval("SELECT version()")
            print(f"✅ 数据库 {db_name} 连接成功")
            print(f"   PostgreSQL版本: {result[:50]}...")
            
            await conn.close()
            
        except Exception as e:
            print(f"❌ 数据库 {db_name} 连接失败: {e}")
            return False
    
    return True

async def main():
    """主函数"""
    print("=" * 80)
    print("🚀 MySuite 数据库初始化工具")
    print("25/06/25 17：00 - PostgreSQL 16 升级到 17 数据库重建工具")
    print("=" * 80)
    
    try:
        # 1. 创建所有需要的数据库
        print("\n📊 第1步: 创建数据库...")
        for db_name in DATABASES:
            success = await create_database_if_not_exists(db_name)
            if not success:
                print(f"❌ 数据库创建失败，退出初始化")
                return False
        
        # 2. 初始化认证数据库表结构
        print("\n📊 第2步: 初始化认证数据库表结构...")
        success = await init_auth_database()
        if not success:
            print("❌ 认证数据库初始化失败")
            return False
        
        # 3. 初始化主业务数据库表结构
        print("\n📊 第3步: 初始化主业务数据库表结构...")
        success = await init_main_database()
        if not success:
            print("❌ 主业务数据库初始化失败")
            return False
        
        # 4. 初始化IMDB数据库表结构
        print("\n📊 第4步: 初始化IMDB数据库表结构...")
        success = await init_imdb_database()
        if not success:
            print("❌ IMDB数据库初始化失败")
            return False
            
        # 5. 测试所有连接
        success = await test_connections()
        if not success:
            print("❌ 数据库连接测试失败")
            return False
        
        print("\n" + "=" * 80)
        print("🎉 数据库初始化完成！")
        print("✅ 所有表结构已重建")
        print("✅ 微服务8006 (认证服务) 现在应该可以正常启动")
        print("📝 建议: 现在可以重新启动微服务进行测试")
        print("=" * 80)
        
        return True
        
    except Exception as e:
        print(f"❌ 数据库初始化过程中发生错误: {e}")
        return False

if __name__ == "__main__":
    # 运行初始化
    success = asyncio.run(main())
    
    if success:
        print("\n🚀 接下来的操作建议:")
        print("1. 运行: python start_microservices.sh")
        print("2. 检查微服务8006是否正常启动")
        print("3. 测试客户端登录功能")
        sys.exit(0)
    else:
        print("\n❌ 初始化失败，请检查:")
        print("1. PostgreSQL 17.5 服务是否正在运行")
        print("2. 数据库连接配置是否正确")
        print("3. 用户权限是否足够创建数据库")
        sys.exit(1) 