import os
import win32com.client
import pythoncom
import threading
import calendar
from datetime import datetime, timedelta
import tkinter as tk
from tkinter import ttk, messagebox, simpledialog
from tkcalendar import Calendar
import os
import xml.etree.ElementTree as ET

db_path = r"D:\actest25\6.mdb"
    
# --- Database writing function ---
def write_work_time(employee_data):
    """
    插入一条记录到 Access，并返回 (ok, msg, new_id)
    """
    pythoncom.CoInitialize()
    access = None
    try:
        #db_path = r"D:\actest25\6.mdb"
        access = win32com.client.Dispatch("Access.Application")
        access.OpenCurrentDatabase(db_path)
        db = access.CurrentDb()

        # 构造 INSERT 语句（保持和你原来一致）
        sql = f"""INSERT INTO 元作業時間
          (従業員ｺｰﾄﾞ, 日付, 機種, 号機, 工場製番, 工事番号,
           ﾕﾆｯﾄ番号, 区分, 項目, 時間, 所属ｺｰﾄﾞ)
         VALUES
          ('{employee_data['employee_id']}', 
           #{employee_data['date']}#,
           {f"'{employee_data['model']}'" if employee_data['model'] else 'NULL'},
           {f"'{employee_data['number']}'" if employee_data['number'] else 'NULL'},
           {f"'{employee_data['factory_number']}'" if employee_data['factory_number'] else 'NULL'},
           {f"'{employee_data['project_number']}'" if employee_data['project_number'] else 'NULL'},
           {f"'{employee_data['unit_number']}'" if employee_data['unit_number'] else 'NULL'},
           '{employee_data['category']}',
           '{employee_data['item']}',
           {employee_data['time']},
           '{employee_data['department']}'
          )"""
        db.Execute(sql)

        # 取回新插入行的自增 ID
        rs = db.OpenRecordset("SELECT @@IDENTITY AS NewID")
        new_id = rs.Fields("NewID").Value

        return True, "写入数据库成功", new_id

    except Exception as e:
        return False, f"写入数据库时出错: {e}", None

    finally:
        if access:
            try:
                access.CloseCurrentDatabase()
                access.Quit()
            except: pass
        pythoncom.CoUninitialize()

def delete_work_time_by_id(entry_id: str):
    """
    根据 ID 从 Access 删除一条记录，并返回 (ok, msg)
    """
    pythoncom.CoInitialize()
    access = None
    try:
        # db_path is global
        access = win32com.client.Dispatch("Access.Application")
        access.OpenCurrentDatabase(db_path)
        db = access.CurrentDb()

        # 假设主键列名为 ID，且 entry_id 是字符串，如果它是数字，则不需要引号
        # 为安全起见，最好验证 entry_id 的格式或类型
        sql = f"DELETE FROM 元作業時間 WHERE ID = {entry_id}"
        
        db.Execute(sql)
        
        # 检查是否真的删除了记录 (可选, Execute不返回受影响的行数)
        # 如果需要确认，可能需要先SELECT COUNT(*) ... WHERE ID = entry_id
        # 然后执行删除，再SELECT COUNT(*), 但对于简单删除，通常不这么做

        return True, "レコード削除成功。"

    except Exception as e:
        return False, f"データベースからの削除中にエラー発生: {e}"

    finally:
        if access:
            try:
                access.CloseCurrentDatabase()
                access.Quit()
            except: pass
        pythoncom.CoUninitialize()

