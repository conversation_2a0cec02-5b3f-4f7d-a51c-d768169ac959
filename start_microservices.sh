#!/bin/bash
# MySuite 微服务启动脚本
# 创建时间: $(date)
# 用途: 启动和测试所有微服务

set -e  # 遇到错误立即退出

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
PURPLE='\033[0;35m'
CYAN='\033[0;36m'
NC='\033[0m' # No Color

# 服务配置
MAIN_SERVICE_DIR="server"
CHAT_SERVICE_DIR="server2"
AUTH_SERVICE_DIR="server3"
VIDEO_SERVICE_DIR="server4"
MAIN_SERVICE_PORT=8003
CHAT_SERVICE_PORT=8005
AUTH_SERVICE_PORT=8006
VIDEO_SERVICE_PORT=8007

# 日志文件
LOG_DIR="logs"
MAIN_LOG="$LOG_DIR/main_service.log"
CHAT_LOG="$LOG_DIR/chat_service.log"
AUTH_LOG="$LOG_DIR/auth_service.log"
VIDEO_LOG="$LOG_DIR/video_service.log"

# PID文件
PID_DIR="pids"
MAIN_PID="$PID_DIR/main_service.pid"
CHAT_PID="$PID_DIR/chat_service.pid"
AUTH_PID="$PID_DIR/auth_service.pid"
VIDEO_PID="$PID_DIR/video_service.pid"

# 创建必要的目录
mkdir -p "$LOG_DIR" "$PID_DIR"

# 打印带颜色的消息
print_message() {
    local color=$1
    local message=$2
    echo -e "${color}${message}${NC}"
}

# 打印标题
print_title() {
    echo
    print_message $CYAN "========================================"
    print_message $CYAN "$1"
    print_message $CYAN "========================================"
    echo
}

# 检查端口是否被占用
check_port() {
    local port=$1
    if lsof -Pi :$port -sTCP:LISTEN -t >/dev/null 2>&1; then
        return 0  # 端口被占用
    else
        return 1  # 端口空闲
    fi
}

# 等待服务启动
wait_for_service() {
    local port=$1
    local service_name=$2
    local max_attempts=30
    local attempt=1
    
    print_message $YELLOW "等待 $service_name 启动 (端口 $port)..."
    
    while [ $attempt -le $max_attempts ]; do
        if check_port $port; then
            print_message $GREEN "✅ $service_name 已启动 (端口 $port)"
            return 0
        fi
        
        echo -n "."
        sleep 1
        ((attempt++))
    done
    
    print_message $RED "❌ $service_name 启动超时"
    return 1
}

# 测试服务健康状态
test_service_health() {
    local port=$1
    local service_name=$2
    local endpoint=${3:-"/"}
    
    print_message $YELLOW "测试 $service_name 健康状态..."
    
    if curl -s "http://localhost:$port$endpoint" > /dev/null; then
        print_message $GREEN "✅ $service_name 健康检查通过"
        return 0
    else
        print_message $RED "❌ $service_name 健康检查失败"
        return 1
    fi
}

# 显示服务信息
show_service_info() {
    local port=$1
    local service_name=$2
    
    print_message $BLUE "📋 $service_name 服务信息:"
    echo "   🌐 服务地址: http://localhost:$port"
    echo "   📚 API文档: http://localhost:$port/docs"
    echo "   🔍 健康检查: http://localhost:$port/health"
    if [ $port -eq $CHAT_SERVICE_PORT ]; then
        echo "   💬 WebSocket: ws://localhost:$port/ws/chat"
    elif [ $port -eq $AUTH_SERVICE_PORT ]; then
        echo "   🔑 认证端点: /auth/register, /auth/login"
    fi
}

# 20250619.12:30 聊天组 - 启动主服务（支持conda环境）
start_main_service() {
    print_title "启动主服务 (端口 $MAIN_SERVICE_PORT)"
    
    if check_port $MAIN_SERVICE_PORT; then
        print_message $YELLOW "⚠️  端口 $MAIN_SERVICE_PORT 已被占用，尝试停止现有服务..."
        pkill -f "uvicorn.*main:app.*--port $MAIN_SERVICE_PORT" || true
        sleep 2
    fi
    
    cd "$MAIN_SERVICE_DIR"
    
    # 20250619.14:00 统一环境 - 检查并激活统一conda环境
    if [ -f "../environment_unified.yml" ]; then
        print_message $YELLOW "检测到统一 conda 环境配置..."
        if command -v conda >/dev/null 2>&1; then
            print_message $GREEN "使用统一 conda 环境"
            # 激活统一conda环境
            source "$(conda info --base)/etc/profile.d/conda.sh"
            conda activate my_suite_unified 2>/dev/null || {
                print_message $YELLOW "创建统一conda环境..."
                cd .. && conda env create -f environment_unified.yml && cd server
                conda activate my_suite_unified
            }
        else
            print_message $YELLOW "未找到conda，使用当前Python环境"
        fi
    fi
    
    # 启动服务
    print_message $BLUE "启动主服务..."
    nohup python -m uvicorn app.main:app --host 0.0.0.0 --port $MAIN_SERVICE_PORT --reload > "../$MAIN_LOG" 2>&1 &
    echo $! > "../$MAIN_PID"
    
    cd ..
    
    # 等待服务启动
    if wait_for_service $MAIN_SERVICE_PORT "主服务"; then
        show_service_info $MAIN_SERVICE_PORT "主服务"
        return 0
    else
        return 1
    fi
}

# 启动认证服务
start_auth_service() {
    print_title "启动认证微服务 (端口 $AUTH_SERVICE_PORT)"
    
    if check_port $AUTH_SERVICE_PORT; then
        print_message $YELLOW "⚠️  端口 $AUTH_SERVICE_PORT 已被占用，尝试停止现有服务..."
        pkill -f "start_auth_service.py" || true
        pkill -f "uvicorn.*main:app.*--port $AUTH_SERVICE_PORT" || true
        sleep 2
    fi
    
    cd "$AUTH_SERVICE_DIR"
    
    # 统一环境激活 (如果需要)
    # ... (可以从其他服务函数复制环境激活逻辑)

    print_message $BLUE "启动认证微服务..."
    nohup python start_auth_service.py > "../$AUTH_LOG" 2>&1 &
    echo $! > "../$AUTH_PID"
    
    cd ..
    
    if wait_for_service $AUTH_SERVICE_PORT "认证微服务"; then
        show_service_info $AUTH_SERVICE_PORT "认证微服务"
        return 0
    else
        return 1
    fi
}

# 启动视频监控服务
start_video_service() {
    print_title "启动视频监控微服务 (端口 $VIDEO_SERVICE_PORT)"
    
    if check_port $VIDEO_SERVICE_PORT; then
        print_message $YELLOW "⚠️  端口 $VIDEO_SERVICE_PORT 已被占用，尝试停止现有服务..."
        pkill -f "start_video_service.py" || true
        pkill -f "uvicorn.*main:app.*--port $VIDEO_SERVICE_PORT" || true
        sleep 2
    fi
    
    cd "$VIDEO_SERVICE_DIR"
    
    # 统一环境激活 (如果需要)
    if [ -f "../environment_unified.yml" ]; then
        print_message $YELLOW "检测到统一 conda 环境配置..."
        if command -v conda >/dev/null 2>&1; then
            print_message $GREEN "使用统一 conda 环境"
            # 激活统一conda环境
            source "$(conda info --base)/etc/profile.d/conda.sh"
            conda activate my_suite_unified 2>/dev/null || {
                print_message $YELLOW "创建统一conda环境..."
                cd .. && conda env create -f environment_unified.yml && cd server4
                conda activate my_suite_unified
            }
        else
            print_message $YELLOW "未找到conda，使用当前Python环境"
        fi
    fi
    
    # 检查依赖
    if [ -f "requirements.txt" ]; then
        print_message $YELLOW "检查Python依赖..."
        if ! python -c "import cv2, fastapi, uvicorn" 2>/dev/null; then
            print_message $YELLOW "安装缺失的依赖..."
            pip install -r requirements.txt
        fi
    fi

    print_message $BLUE "启动视频监控微服务..."
    nohup python start_video_service.py > "../$VIDEO_LOG" 2>&1 &
    echo $! > "../$VIDEO_PID"
    
    cd ..
    
    if wait_for_service $VIDEO_SERVICE_PORT "视频监控微服务"; then
        show_service_info $VIDEO_SERVICE_PORT "视频监控微服务"
        return 0
    else
        return 1
    fi
}

# 20250619.12:30 聊天组 - 启动聊天服务（支持独立conda环境）
start_chat_service() {
    print_title "启动聊天微服务 (端口 $CHAT_SERVICE_PORT)"
    
    if check_port $CHAT_SERVICE_PORT; then
        print_message $YELLOW "⚠️  端口 $CHAT_SERVICE_PORT 已被占用，尝试停止现有服务..."
        pkill -f "start_chat_service.py" || true
        pkill -f "uvicorn.*main:app.*--port $CHAT_SERVICE_PORT" || true
        sleep 2
    fi
    
    cd "$CHAT_SERVICE_DIR"
    
    # 20250619.14:00 统一环境 - 检查并激活统一conda环境
    if [ -f "../environment_unified.yml" ]; then
        print_message $YELLOW "检测到统一 conda 环境配置..."
        if command -v conda >/dev/null 2>&1; then
            print_message $GREEN "使用统一 conda 环境"
            # 激活统一conda环境
            source "$(conda info --base)/etc/profile.d/conda.sh"
            conda activate my_suite_unified 2>/dev/null || {
                print_message $YELLOW "创建统一conda环境..."
                cd .. && conda env create -f environment_unified.yml && cd server2
                conda activate my_suite_unified
            }
        else
            print_message $YELLOW "未找到conda，使用当前Python环境"
        fi
    fi
    
    # 20250619.12:30 聊天组 - 检查依赖
    if [ -f "requirements.txt" ]; then
        print_message $YELLOW "检查Python依赖..."
        if ! python -c "import fastapi, uvicorn, redis, asyncpg" 2>/dev/null; then
            print_message $YELLOW "安装缺失的依赖..."
            pip install -r requirements.txt
        fi
    fi
    
    # 启动服务
    print_message $BLUE "启动聊天微服务..."
    if [ -f "start_chat_service.py" ]; then
        nohup python start_chat_service.py > "../$CHAT_LOG" 2>&1 &
    else
        nohup python -m uvicorn app.main:app --host 0.0.0.0 --port $CHAT_SERVICE_PORT --reload > "../$CHAT_LOG" 2>&1 &
    fi
    echo $! > "../$CHAT_PID"
    
    cd ..
    
    # 等待服务启动
    if wait_for_service $CHAT_SERVICE_PORT "聊天微服务"; then
        show_service_info $CHAT_SERVICE_PORT "聊天微服务"
        return 0
    else
        return 1
    fi
}

# 运行服务测试
run_service_tests() {
    print_title "运行服务测试"
    
    # 测试主服务
    if test_service_health $MAIN_SERVICE_PORT "主服务" "/health"; then
        print_message $GREEN "✅ 主服务测试通过"
    else
        print_message $RED "❌ 主服务测试失败"
    fi
    
    # 测试聊天服务
    if test_service_health $CHAT_SERVICE_PORT "聊天微服务" "/health"; then
        print_message $GREEN "✅ 聊天微服务测试通过"
    else
        print_message $RED "❌ 聊天微服务测试失败"
    fi
    
    # 测试认证服务
    if test_service_health $AUTH_SERVICE_PORT "认证微服务" "/health"; then
        print_message $GREEN "✅ 认证微服务测试通过"
    else
        print_message $RED "❌ 认证微服务测试失败"
    fi
    
    # 测试视频监控服务
    if test_service_health $VIDEO_SERVICE_PORT "视频监控微服务" "/health"; then
        print_message $GREEN "✅ 视频监控微服务测试通过"
    else
        print_message $RED "❌ 视频监控微服务测试失败"
    fi
    
    # 显示详细的API响应
    print_message $CYAN "\n📊 服务状态详情:"
    echo
    
    print_message $BLUE "主服务根路径响应:"
    curl -s "http://localhost:$MAIN_SERVICE_PORT/" | python -m json.tool 2>/dev/null || echo "无法获取响应"
    echo
    
    print_message $BLUE "聊天微服务根路径响应:"
    curl -s "http://localhost:$CHAT_SERVICE_PORT/" | python -m json.tool 2>/dev/null || echo "无法获取响应"
    echo

    print_message $BLUE "认证微服务健康检查:"
    curl -s "http://localhost:$AUTH_SERVICE_PORT/health" | python -m json.tool 2>/dev/null || echo "无法获取响应"
    echo
}

# 显示服务状态
show_status() {
    print_title "服务状态概览"
    
    echo "📁 项目目录: $(pwd)"
    echo "📊 运行状态:"
    
    if check_port $MAIN_SERVICE_PORT; then
        print_message $GREEN "✅ 主服务运行中 (端口 $MAIN_SERVICE_PORT)"
    else
        print_message $RED "❌ 主服务未运行"
    fi
    
    if check_port $CHAT_SERVICE_PORT; then
        print_message $GREEN "✅ 聊天微服务运行中 (端口 $CHAT_SERVICE_PORT)"
    else
        print_message $RED "❌ 聊天微服务未运行"
    fi

    if check_port $AUTH_SERVICE_PORT; then
        print_message $GREEN "✅ 认证微服务运行中 (端口 $AUTH_SERVICE_PORT)"
    else
        print_message $RED "❌ 认证微服务未运行"
    fi
    
    if check_port $VIDEO_SERVICE_PORT; then
        print_message $GREEN "✅ 视频监控微服务运行中 (端口 $VIDEO_SERVICE_PORT)"
    else
        print_message $RED "❌ 视频监控微服务未运行"
    fi
    
    echo
    echo "📝 日志文件:"
    echo "   主服务: $MAIN_LOG"
    echo "   聊天服务: $CHAT_LOG"
    echo "   认证服务: $AUTH_LOG"
    echo "   视频监控服务: $VIDEO_LOG"
    
    echo
    echo "🔧 管理命令:"
    echo "   查看日志: tail -f $MAIN_LOG"
    echo "   停止服务: ./start_microservices.sh stop"
    echo "   重启服务: ./start_microservices.sh restart"
}

# 停止所有服务
stop_services() {
    print_title "停止所有微服务"
    
    # 改进：使用更可靠的方法来停止服务
    local pids_to_kill=""
    
    # 停止主服务
    if [ -f "$MAIN_PID" ]; then
        local main_pid=$(cat "$MAIN_PID")
        if kill -0 "$main_pid" 2>/dev/null; then
            print_message $YELLOW "准备停止主服务 (PID: $main_pid)..."
            pids_to_kill="$pids_to_kill $main_pid"
        fi
        rm -f "$MAIN_PID"
    fi
    
    # 停止聊天服务
    if [ -f "$CHAT_PID" ]; then
        local chat_pid=$(cat "$CHAT_PID")
        if kill -0 "$chat_pid" 2>/dev/null; then
            print_message $YELLOW "准备停止聊天微服务 (PID: $chat_pid)..."
            pids_to_kill="$pids_to_kill $chat_pid"
        fi
        rm -f "$CHAT_PID"
    fi
    
    # 停止认证服务
    if [ -f "$AUTH_PID" ]; then
        local auth_pid=$(cat "$AUTH_PID")
        if kill -0 "$auth_pid" 2>/dev/null; then
            print_message $YELLOW "准备停止认证微服务 (PID: $auth_pid)..."
            pids_to_kill="$pids_to_kill $auth_pid"
        fi
        rm -f "$AUTH_PID"
    fi
    
    # 停止视频监控服务
    if [ -f "$VIDEO_PID" ]; then
        local video_pid=$(cat "$VIDEO_PID")
        if kill -0 "$video_pid" 2>/dev/null; then
            print_message $YELLOW "准备停止视频监控微服务 (PID: $video_pid)..."
            pids_to_kill="$pids_to_kill $video_pid"
        fi
        rm -f "$VIDEO_PID"
    fi
    
    # 强制停止可能残留的进程
    # 查找监听指定端口的uvicorn进程
    pkill -f "uvicorn.*main:app.*--port $MAIN_SERVICE_PORT" || true
    pkill -f "start_chat_service.py" || true
    pkill -f "uvicorn.*main:app.*--port $CHAT_SERVICE_PORT" || true
    pkill -f "start_auth_service.py" || true
    pkill -f "uvicorn.*main:app.*--port $AUTH_SERVICE_PORT" || true
    pkill -f "start_video_service.py" || true
    pkill -f "uvicorn.*main:app.*--port $VIDEO_SERVICE_PORT" || true
    
    # 从PID文件杀死进程
    if [ -n "$pids_to_kill" ]; then
        kill $pids_to_kill 2>/dev/null
    fi
    
    sleep 2
    
    # 最终确认
    if ! check_port $MAIN_SERVICE_PORT && ! check_port $CHAT_SERVICE_PORT && ! check_port $AUTH_SERVICE_PORT; then
        print_message $GREEN "✅ 所有服务已成功停止"
    else
        print_message $YELLOW "⚠️  部分服务可能仍在运行，请手动检查端口"
        lsof -i :$MAIN_SERVICE_PORT -i :$CHAT_SERVICE_PORT -i :$AUTH_SERVICE_PORT || true
    fi
}

# 显示使用帮助
show_help() {
    print_title "MySuite 微服务管理脚本"
    echo "用法: $0 [命令]"
    echo
    echo "命令:"
    echo "  start     启动所有微服务 (默认)"
    echo "  stop      停止所有微服务"
    echo "  restart   重启所有微服务"
    echo "  status    显示服务状态"
    echo "  test      运行服务测试"
    echo "  logs      显示服务日志"
    echo "  help      显示此帮助信息"
    echo
    echo "服务端口:"
    echo "  主服务:       http://localhost:$MAIN_SERVICE_PORT"
    echo "  聊天微服务:   http://localhost:$CHAT_SERVICE_PORT"
    echo "  认证微服务:   http://localhost:$AUTH_SERVICE_PORT"
    echo "  视频监控服务: http://localhost:$VIDEO_SERVICE_PORT"
}

# 显示日志
show_logs() {
    print_title "服务日志"
    
    echo "选择要查看的日志:"
    echo "1) 主服务日志"
    echo "2) 聊天微服务日志"
    echo "3) 认证微服务日志"
    echo "4) 视频监控微服务日志"
    echo "5) 同时显示所有日志"
    read -p "请选择 (1-5): " choice
    
    case $choice in
        1)
            print_message $BLUE "主服务日志:"
            tail -f "$MAIN_LOG"
            ;;
        2)
            print_message $BLUE "聊天微服务日志:"
            tail -f "$CHAT_LOG"
            ;;
        3)
            print_message $BLUE "认证微服务日志:"
            tail -f "$AUTH_LOG"
            ;;
        4)
            print_message $BLUE "视频监控微服务日志:"
            tail -f "$VIDEO_LOG"
            ;;
        5)
            print_message $BLUE "所有服务日志:"
            tail -f "$MAIN_LOG" "$CHAT_LOG" "$AUTH_LOG" "$VIDEO_LOG"
            ;;
        *)
            print_message $RED "无效选择"
            ;;
    esac
}

# 主函数
main() {
    local command=${1:-start}
    
    case $command in
        start)
            print_title "MySuite 微服务启动器"
            print_message $GREEN "开始启动所有微服务..."
            
            # 启动服务
            start_main_service
            start_chat_service
            start_auth_service
            start_video_service
            
            # 运行测试
            sleep 3
            run_service_tests
            
            # 显示状态
            show_status
            
            print_message $GREEN "\n🎉 所有微服务启动完成！"
            ;;
        stop)
            stop_services
            ;;
        restart)
            stop_services
            sleep 2
            main start
            ;;
        status)
            show_status
            ;;
        test)
            run_service_tests
            ;;
        logs)
            show_logs
            ;;
        help|--help|-h)
            show_help
            ;;
        *)
            print_message $RED "未知命令: $command"
            show_help
            exit 1
            ;;
    esac
}

# 捕获中断信号
trap 'print_message $YELLOW "\n\n🛑 收到中断信号，正在停止服务..."; stop_services; exit 0' INT TERM

# 运行主函数
main "$@" 