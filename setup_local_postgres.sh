#!/bin/bash
# 本地PostgreSQL安装脚本 - 临时替代方案
# 25/06/25 17:35 创建 - 用于临时替代远程Windows PostgreSQL

set -e

echo "================================="
echo "🔄 本地PostgreSQL临时安装向导"
echo "================================="
echo "⚠️  这是临时方案，用于在修复远程连接前测试微服务"
echo

# 检查是否已安装PostgreSQL
if command -v psql >/dev/null 2>&1; then
    echo "✅ 检测到已安装PostgreSQL"
    sudo systemctl start postgresql || echo "⚠️  启动PostgreSQL服务"
else
    echo "📦 安装PostgreSQL..."
    sudo apt update
    sudo apt install -y postgresql postgresql-contrib
    echo "✅ PostgreSQL安装完成"
fi

# 创建数据库用户和密码
echo "🔧 配置数据库用户..."
sudo -u postgres psql -c "ALTER USER postgres PASSWORD 'pojiami0602';"

# 创建所需的数据库
echo "📊 创建数据库..."
sudo -u postgres createdb auth 2>/dev/null || echo "数据库auth已存在"
sudo -u postgres createdb imdb 2>/dev/null || echo "数据库imdb已存在"

# 配置本地连接
echo "🔧 配置本地连接..."
sudo sed -i "s/#listen_addresses = 'localhost'/listen_addresses = 'localhost'/" /etc/postgresql/*/main/postgresql.conf
sudo systemctl restart postgresql

echo "✅ 本地PostgreSQL配置完成"

# 更新配置文件指向本地
echo "📝 更新应用配置文件..."

# 备份原配置
cp server/app/config.py server/app/config.py.remote_backup
cp server3/app/config.py server3/app/config.py.remote_backup

# 修改为本地连接
sed -i 's/DB_HOST = "************"/DB_HOST = "localhost"/' server/app/config.py
sed -i 's/DB_HOST = "************"/DB_HOST = "localhost"/' server3/app/config.py

sed -i 's/IMDB_HOST = "************"/IMDB_HOST = "localhost"/' server/app/config.py

echo "🔧 本地数据库配置完成"
echo "📝 原配置已备份为 *.remote_backup"

# 运行数据库初始化
echo "🚀 初始化数据库结构..."
python init_databases.py

echo
echo "================================="
echo "🎉 本地PostgreSQL设置完成！"
echo "================================="
echo "现在可以运行微服务进行测试:"
echo "  bash start_microservices.sh"
echo
echo "⚠️  恢复远程连接:"
echo "1. 修复Windows 10上的PostgreSQL配置"
echo "2. 运行: ./restore_remote_config.sh"
echo "=================================" 