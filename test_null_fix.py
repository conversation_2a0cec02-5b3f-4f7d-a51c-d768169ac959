#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试NULL值修复的脚本
验证server5发送字符串"NULL"到server6时，是否能正确生成NULL而不是'NULL'
"""

import asyncio
import aiohttp
import json
from datetime import datetime

async def test_null_values():
    """测试NULL值处理"""
    
    # 测试数据：包含字符串"NULL"的记录
    test_data = {
        'employee_id': '215829',
        'entry_date': '2025/07/15',
        'model': "NULL",  # 这应该生成NULL
        'number': "NULL",  # 这应该生成NULL
        'factory_number': "NULL",  # 这应该生成NULL
        'project_number': "NULL",  # 这应该生成NULL
        'unit_number': "NULL",  # 这应该生成NULL
        'category': '1',
        'item': '1',
        'duration': 1.0,
        'department': '131'
    }
    
    print("🧪 测试NULL值修复")
    print(f"📤 发送到Server6的数据: {json.dumps(test_data, ensure_ascii=False, indent=2)}")
    
    try:
        # 连接到Server6
        async with aiohttp.ClientSession() as session:
            # 测试连接
            async with session.get('http://192.168.1.100:8010/mdb/test') as response:
                if response.status == 200:
                    print("✅ Server6连接正常")
                else:
                    print(f"❌ Server6连接失败: {response.status}")
                    return
            
            # 发送插入请求
            async with session.post(
                'http://192.168.1.100:8010/mdb/entries/insert',
                json=test_data,
                headers={'Content-Type': 'application/json'}
            ) as response:
                result = await response.json()
                print(f"📥 Server6响应: {json.dumps(result, ensure_ascii=False, indent=2)}")
                
                if result.get('success'):
                    print("✅ 插入成功，NULL值处理正确")
                    print("🔍 请检查Server6日志，确认SQL中生成的是NULL而不是'NULL'")
                else:
                    print(f"❌ 插入失败: {result.get('message', '未知错误')}")
                    
    except Exception as e:
        print(f"❌ 测试失败: {e}")

if __name__ == "__main__":
    asyncio.run(test_null_values()) 