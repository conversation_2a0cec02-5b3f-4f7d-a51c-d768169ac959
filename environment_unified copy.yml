name: my_suite_unified
channels:
  - conda-forge
  - defaults
dependencies:
  - python=3.12

  # 20250619.14:00 统一环境 - FastAPI + Uvicorn（支持 HTTPS/WSS）
  - fastapi
  - uvicorn[standard]
  - httptools  # 高性能HTTP解析器

  # 20250619.14:00 统一环境 - SQLAlchemy + PostgreSQL 依赖（异步支持）
  - sqlalchemy
  - asyncpg
  - psycopg2

  # 20250619.14:00 统一环境 - Pydantic配置管理
  - pydantic
  - pydantic-settings

  # 20250619.14:00 统一环境 - WebSocket支持
  - websockets

  # 20250619.14:00 统一环境 - JWT 认证
  - python-jose[cryptography]
  - passlib

  # 20250619.14:00 统一环境 - MongoDB 客户端（微服务1需要）
  - pymongo

  # 20250619.14:00 统一环境 - Redis缓存和消息队列
  - redis>=4.5.0
  - hiredis

  # 20250619.14:00 统一环境 - 任务调度（微服务1需要）
  - apscheduler

  # 20250619.14:00 统一环境 - 文件处理
  - pillow
  - python-magic

  # 20250619.14:00 统一环境 - 加密支持
  - cryptography
  - pycryptodome

  # 20250619.14:00 统一环境 - 环境变量
  - python-dotenv

  # 20250619.14:00 统一环境 - 处理 Excel、监控文件（微服务1需要）
  - pandas
  - openpyxl

  # 20250619.14:00 统一环境 - ODBC (MDB) 连接（微服务1需要，Windows兼容）
  - pywin32

  # 20250619.14:00 统一环境 - HTTP 客户端
  - httpx
  - certifi

  # 20250619.14:00 统一环境 - 日期时间处理
  - python-dateutil

  # 20250619.14:00 统一环境 - 测试工具
  - pytest
  - pytest-asyncio

  # 20250619.14:00 统一环境 - pip 安装其他依赖项
  - pip
  - pip:
    - watchgod>=0.8.2    # 用于热重载
    - aiojobs>=0.3.0     # 异步任务管理
    - aiofiles>=23.2.1   # 异步文件操作
    - alembic>=1.13.1    # 数据库迁移
    - python-json-logger>=2.0.7  # JSON格式日志
    - typing-extensions>=4.8.0   # 类型扩展
    - uuid>=1.30         # UUID生成
    - redis[hiredis]>=4.5.0  # 确保Redis异步支持 
    - bcrypt