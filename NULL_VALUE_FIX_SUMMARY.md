# NULL值修复总结

## 问题描述

在客户端-微服务系统中，当客户端program1执行insert操作时，SQL生成出现了问题：

**问题SQL（修复前）：**
```sql
INSERT INTO [元作業時間]
  (従業員ｺｰﾄﾞ, 日付, 機種, 号機,
   工場製番, 工事番号, ﾕﾆｯﾄ番号,
   区分, 項目, 時間, 所属ｺｰﾄﾞ)
 VALUES
  ('215829',
   #2025/07/15#,
   'NULL',  -- 问题：这是字符串'NULL'，不是真正的NULL
   'NULL',  -- 问题：这是字符串'NULL'，不是真正的NULL
   'NULL',  -- 问题：这是字符串'NULL'，不是真正的NULL
   'NULL',  -- 问题：这是字符串'NULL'，不是真正的NULL
   'NULL',  -- 问题：这是字符串'NULL'，不是真正的NULL
   '1',
   '1',
   1.0,
   '131'
```

**期望SQL（修复后）：**
```sql
INSERT INTO [元作業時間]
  (従業員ｺｰﾄﾞ, 日付, 機種, 号機,
   工場製番, 工事番号, ﾕﾆｯﾄ番号,
   区分, 項目, 時間, 所属ｺｰﾄﾞ)
 VALUES
  ('215829',
   #2025/07/15#,
   NULL,    -- 修复：真正的NULL值
   NULL,    -- 修复：真正的NULL值
   NULL,    -- 修复：真正的NULL值
   NULL,    -- 修复：真正的NULL值
   NULL,    -- 修复：真正的NULL值
   '1',
   '1',
   1.0,
   '131'
```

## 问题根源

问题出现在server6的`mdb_client.py`中：

1. **字段值获取问题**：当接收到字符串"NULL"时，`or`操作符会返回"NULL"字符串
2. **SQL生成问题**：由于"NULL"字符串是真值，所以会生成`'NULL'`而不是`NULL`

**修复前的代码：**
```python
# 获取其他字段值，支持英文字段名和日文字段名
model = record_data.get('model') or record_data.get('機種', '')  # 问题：字符串"NULL"会被当作真值
number = record_data.get('number') or record_data.get('号機', '')  # 问题：字符串"NULL"会被当作真值
# ... 其他字段类似

# SQL生成
{f"'{model}'" if model else 'NULL'},  # 问题：字符串"NULL"会被生成'NULL'
{f"'{number}'" if number else 'NULL'},  # 问题：字符串"NULL"会被生成'NULL'
# ... 其他字段类似
```

## 修复方案

### 修复后的代码

**1. 字段值获取逻辑：**
```python
# 修复：正确处理字符串"NULL"，将其转换为None
def get_field_value(record_data, eng_key, jpn_key, default=''):
    value = record_data.get(eng_key) or record_data.get(jpn_key, default)
    # 如果值是字符串"NULL"，转换为None
    if isinstance(value, str) and value == "NULL":
        return None
    return value

model = get_field_value(record_data, 'model', '機種')
number = get_field_value(record_data, 'number', '号機')
factory_number = get_field_value(record_data, 'factory_number', '工場製番')
project_number = get_field_value(record_data, 'project_number', '工事番号')
unit_number = get_field_value(record_data, 'unit_number', 'ﾕﾆｯﾄ番号')
```

**2. SQL生成逻辑：**
```python
# 修复：正确处理None值，确保生成真正的NULL而不是'NULL'字符串
{f"'{model}'" if model is not None and model != '' else 'NULL'},
{f"'{number}'" if number is not None and number != '' else 'NULL'},
{f"'{factory_number}'" if factory_number is not None and factory_number != '' else 'NULL'},
{f"'{project_number}'" if project_number is not None and project_number != '' else 'NULL'},
{f"'{unit_number}'" if unit_number is not None and unit_number != '' else 'NULL'},
```

## 修复的文件

**server6/app/core/mdb_client.py**
- 修复了`insert_record`方法中的字段值获取逻辑
- 修复了SQL生成逻辑，确保字符串"NULL"被转换为真正的NULL

## 测试验证

创建了测试脚本`test_null_fix.py`来验证修复效果：

```python
# 测试数据：包含字符串"NULL"的记录
test_data = {
    'employee_id': '215829',
    'entry_date': '2025/07/15',
    'model': "NULL",  # 这应该生成NULL
    'number': "NULL",  # 这应该生成NULL
    'factory_number': "NULL",  # 这应该生成NULL
    'project_number': "NULL",  # 这应该生成NULL
    'unit_number': "NULL",  # 这应该生成NULL
    'category': '1',
    'item': '1',
    'duration': 1.0,
    'department': '131'
}
```

## 影响范围

这个修复会影响以下操作：
- ✅ INSERT操作：字符串"NULL"现在会正确转换为真正的NULL
- ✅ 数据一致性：MDB中的NULL值现在与PostgreSQL中的NULL值保持一致
- ✅ 向后兼容性：修复后的代码仍然支持其他正常的字符串值

## 注意事项

1. **Server5保持不变**：Server5继续发送字符串"NULL"，这是符合预期的
2. **Server6处理转换**：Server6负责将字符串"NULL"转换为真正的NULL
3. **日志记录**：修复后的代码会生成正确的SQL日志

## 部署建议

1. 部署Server6的修复
2. 使用测试脚本验证修复效果
3. 监控生产环境的日志，确保SQL生成正确
4. 验证MDB数据库中的NULL值是否正确

## 总结

通过这个修复，客户端-微服务系统现在能够正确处理字符串"NULL"，确保MDB数据库中存储的是真正的NULL值而不是字符串'NULL'，从而保证了数据的一致性和正确性。

**关键改进：**
- 字符串"NULL" → 真正的NULL
- SQL生成：`'NULL'` → `NULL`
- 数据一致性：MDB与PostgreSQL保持一致 