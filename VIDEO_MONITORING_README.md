# 视频监控微服务 (Server4)

## 概述

本文档描述了MySuite系统的第4个微服务 - 视频监控服务的实现和使用方法。

## 功能特性

### 🎥 实时视频流
- 支持摄像头实时捕获
- WebSocket实时视频传输
- Base64编码的JPEG格式
- 可配置的分辨率和帧率

### 🔧 技术特性
- **FastAPI** 框架构建
- **OpenCV** 视频处理
- **WebSocket** 实时通信
- **异步处理** 高性能
- **独立部署** 可跨平台运行

### 🎯 未来扩展
- YOLO目标检测集成
- 多摄像头支持
- 录像功能
- 智能分析

## 架构设计

```
MySuite 微服务架构
├── Server (8003)   - 主服务 + nginx
├── Server2 (8005)  - 聊天服务
├── Server3 (8006)  - 认证服务
└── Server4 (8007)  - 视频监控服务 ⭐ NEW
```

## 目录结构

```
server4/
├── __init__.py
├── requirements.txt
├── start_video_service.py
└── app/
    ├── __init__.py
    ├── config.py
    ├── main.py
    ├── core/
    │   ├── __init__.py
    │   └── services/
    │       ├── __init__.py
    │       └── video_service.py
    └── routers/
        ├── __init__.py
        ├── video_api.py
        └── video_websocket.py
```

## 安装和配置

### 1. 安装依赖

```bash
cd server4
pip install -r requirements.txt
```

主要依赖：
- `fastapi>=0.100.0` - Web框架
- `uvicorn[standard]>=0.20.0` - ASGI服务器
- `opencv-python>=4.8.0` - 视频处理
- `websockets` - WebSocket支持

### 2. 配置参数

编辑 `server4/app/config.py` 修改配置：

```python
# 视频配置
CAMERA_INDEX: int = 0          # 摄像头索引
VIDEO_WIDTH: int = 640         # 视频宽度
VIDEO_HEIGHT: int = 480        # 视频高度
VIDEO_FPS: int = 30           # 帧率
VIDEO_QUALITY: int = 80       # JPEG质量

# 服务配置
SERVICE_PORT: int = 8007      # 服务端口
SERVICE_HOST: str = "0.0.0.0" # 监听地址
```

## 启动服务

### 方法1: 使用统一启动脚本

```bash
./start_microservices.sh
```

这将启动所有4个微服务，包括新的视频监控服务。

### 方法2: 单独启动视频服务

```bash
cd server4
python start_video_service.py
```

## API接口

### REST API

| 端点 | 方法 | 描述 |
|------|------|------|
| `/` | GET | 服务根信息 |
| `/api/video/health` | GET | 健康检查 |
| `/api/video/info` | GET | 服务信息 |
| `/api/video/stats` | GET | 统计信息 |
| `/api/video/snapshot` | GET | 获取快照 |
| `/api/video/camera/start` | POST | 启动摄像头 |
| `/api/video/camera/stop` | POST | 停止摄像头 |
| `/api/video/camera/restart` | POST | 重启摄像头 |

### WebSocket API

**连接地址**: `ws://localhost:8007/ws/video`

**消息类型**:
- `welcome` - 连接成功，包含配置信息
- `video_frame` - 视频帧数据 (base64)
- `stats` - 统计信息
- `ping/pong` - 心跳检测

## 客户端集成

### 新增第3个Tab

在客户端 `EmployeeInterfaceWindow` 中新增了"视频监控"标签页：

#### UI组件
- **连接控制**: 连接/断开按钮
- **视频显示**: 640x480像素显示区域
- **状态信息**: 连接状态、FPS、帧数统计
- **快照功能**: 获取当前画面快照

#### 主要功能
```python
# 连接到视频服务
def connect_to_video(self):
    # WebSocket连接到 ws://localhost:8007/ws/video

# 显示视频帧
def display_video_frame(self, frame_data: str):
    # Base64解码并显示到QLabel

# 获取快照
def get_video_snapshot(self):
    # HTTP API获取静态快照
```

## 使用指南

### 1. 启动所有服务

```bash
./start_microservices.sh
```

验证视频服务状态：
```bash
curl http://localhost:8007/api/video/health
```

### 2. 登录客户端

使用员工ID `215829` 和密码 `test123` 登录。

### 3. 访问视频监控

1. 点击第3个标签页 "视频监控"
2. 点击 "连接视频" 按钮
3. 等待连接成功，开始接收视频流
4. 可以点击 "获取快照" 获取当前画面

### 4. 监控信息

- **连接状态**: 显示与服务器的连接状态
- **视频配置**: 分辨率、质量、FPS等参数
- **实时统计**: 当前FPS、总帧数、连接数

## 故障排除

### 常见问题

1. **摄像头无法打开**
   ```
   ERROR: Failed to open camera with index: 0
   ```
   - 检查摄像头是否被其他程序占用
   - 尝试修改 `CAMERA_INDEX` 参数
   - 在虚拟机环境中可能无法访问摄像头

2. **WebSocket连接失败**
   ```
   视频WebSocket连接错误: [Errno 111] Connection refused
   ```
   - 确认视频服务正在运行
   - 检查防火墙设置
   - 验证端口8007是否可访问

3. **依赖安装失败**
   ```
   No module named 'cv2'
   ```
   - 安装OpenCV: `pip install opencv-python`
   - 确保使用正确的Python环境

### 调试方法

1. **查看服务日志**
   ```bash
   tail -f logs/video_service.log
   ```

2. **测试API接口**
   ```bash
   # 健康检查
   curl http://localhost:8007/api/video/health
   
   # 获取服务信息
   curl http://localhost:8007/api/video/info
   
   # 获取快照
   curl http://localhost:8007/api/video/snapshot
   ```

3. **测试WebSocket连接**
   ```bash
   # 使用websocat测试
   websocat ws://localhost:8007/ws/video
   ```

## 技术细节

### 视频处理流程

1. **初始化**: 打开摄像头设备
2. **捕获**: 独立线程循环读取帧
3. **编码**: 转换为JPEG格式
4. **传输**: Base64编码通过WebSocket发送
5. **显示**: 客户端解码并显示

### 性能优化

- **异步处理**: 视频捕获和网络传输分离
- **帧率控制**: 可配置的FPS限制
- **质量调节**: JPEG压缩质量可调
- **连接管理**: 支持多客户端同时连接

### 扩展性设计

- **模块化架构**: 易于添加新功能
- **配置驱动**: 参数可通过配置文件修改
- **YOLO预留**: 已预留目标检测接口
- **多摄像头**: 架构支持扩展到多摄像头

## 未来计划

### 短期目标
- [ ] 添加录像功能
- [ ] 支持多种视频格式
- [ ] 添加摄像头参数调节
- [ ] 优化网络传输效率

### 长期目标
- [ ] 集成YOLO目标检测
- [ ] 支持多摄像头管理
- [ ] 添加智能分析功能
- [ ] 支持云端存储

## 相关链接

- [FastAPI文档](https://fastapi.tiangolo.com/)
- [OpenCV Python文档](https://docs.opencv.org/4.x/)
- [WebSocket协议](https://tools.ietf.org/html/rfc6455)

---

**创建时间**: 2025-06-19  
**版本**: 1.0.0  
**作者**: MySuite Team 