#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
调试数据流脚本
追踪从客户端到Server6的完整数据流
"""

import asyncio
import aiohttp
import json
from datetime import datetime

async def debug_data_flow():
    """调试数据流"""
    
    print("🔍 调试数据流 - 追踪category和item字段的变化")
    print("=" * 60)
    
    # 测试数据
    test_data = {
        'employee_id': '215829',
        'entry_date': '2025/07/15',
        'model': None,
        'number': None,
        'factory_number': 'HA0484',
        'project_number': None,
        'unit_number': None,
        'category': '0',  # 客户端发送字符串'0'
        'item': '0',      # 客户端发送字符串'0'
        'duration': 1.0,
        'department': '131'
    }
    
    print(f"📤 客户端发送的原始数据:")
    print(f"  category: '{test_data['category']}' (类型: {type(test_data['category'])})")
    print(f"  item: '{test_data['item']}' (类型: {type(test_data['item'])})")
    print()
    
    try:
        async with aiohttp.ClientSession() as session:
            # 1. 测试client_entries_gateway.py的处理
            print("🔍 步骤1: 测试client_entries_gateway.py")
            print("发送到 /client/entries/create")
            
            async with session.post(
                'http://localhost:8009/client/entries/create',
                json=test_data,
                headers={'Content-Type': 'application/json'}
            ) as response:
                result = await response.json()
                print(f"响应: {json.dumps(result, ensure_ascii=False, indent=2)}")
                
                if result.get('success'):
                    entry_id = result.get('entry_id')
                    print(f"✅ 插入成功，entry_id: {entry_id}")
                    
                    # 2. 查询entries表中的实际数据
                    print("\n🔍 步骤2: 查询entries表中的实际数据")
                    print("发送到 /api/entries/ 查询")
                    
                    async with session.get(
                        f'http://localhost:8009/api/entries/?employee_id=215829&limit=1',
                        headers={'Content-Type': 'application/json'}
                    ) as query_response:
                        query_result = await query_response.json()
                        if query_result:
                            latest_entry = query_result[0]
                            print(f"entries表中的数据:")
                            print(f"  category: {latest_entry.get('category')} (类型: {type(latest_entry.get('category'))})")
                            print(f"  item: {latest_entry.get('item')} (类型: {type(latest_entry.get('item'))})")
                            print(f"  source: {latest_entry.get('source')}")
                            print(f"  external_id: {latest_entry.get('external_id')}")
                        else:
                            print("❌ 未找到entries记录")
                    
                    # 3. 等待f2处理队列
                    print("\n🔍 步骤3: 等待f2处理队列")
                    print("等待5秒让f2处理队列...")
                    await asyncio.sleep(5)
                    
                    # 4. 再次查询entries表，检查external_id是否已设置
                    print("\n🔍 步骤4: 检查同步状态")
                    async with session.get(
                        f'http://localhost:8009/api/entries/?employee_id=215829&limit=1',
                        headers={'Content-Type': 'application/json'}
                    ) as final_response:
                        final_result = await final_response.json()
                        if final_result:
                            final_entry = final_result[0]
                            print(f"同步后的数据:")
                            print(f"  category: {final_entry.get('category')} (类型: {type(final_entry.get('category'))})")
                            print(f"  item: {final_entry.get('item')} (类型: {type(final_entry.get('item'))})")
                            print(f"  external_id: {final_entry.get('external_id')}")
                            print(f"  source: {final_entry.get('source')}")
                        else:
                            print("❌ 未找到entries记录")
                else:
                    print(f"❌ 插入失败: {result.get('message', '未知错误')}")
                    
    except Exception as e:
        print(f"❌ 调试失败: {e}")

if __name__ == "__main__":
    asyncio.run(debug_data_flow()) 