#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试category和item字段修复的脚本
验证当category和item为None时，是否能正确设置为默认值'0'
"""

import asyncio
import aiohttp
import json
from datetime import datetime

async def test_category_item_fix():
    """测试category和item字段修复"""
    
    # 测试数据：包含None值的category和item
    test_data = {
        'employee_id': '215829',
        'entry_date': '2025/07/15',
        'model': None,  # 这应该生成NULL
        'number': None,  # 这应该生成NULL
        'factory_number': 'HA0484',  # 有值
        'project_number': None,  # 这应该生成NULL
        'unit_number': None,  # 这应该生成NULL
        'category': None,  # 这应该生成'0'而不是'None'
        'item': '7',  # 有值
        'duration': 8.0,
        'department': '131'
    }
    
    print("🧪 测试category和item字段修复")
    print(f"📤 发送到Server6的数据: {json.dumps(test_data, ensure_ascii=False, indent=2)}")
    
    try:
        # 连接到Server6
        async with aiohttp.ClientSession() as session:
            # 测试连接
            async with session.get('http://192.168.1.100:8010/mdb/test') as response:
                if response.status == 200:
                    print("✅ Server6连接正常")
                else:
                    print(f"❌ Server6连接失败: {response.status}")
                    return
            
            # 发送插入请求
            async with session.post(
                'http://192.168.1.100:8010/mdb/entries/insert',
                json=test_data,
                headers={'Content-Type': 'application/json'}
            ) as response:
                result = await response.json()
                print(f"📥 Server6响应: {json.dumps(result, ensure_ascii=False, indent=2)}")
                
                if result.get('success'):
                    print("✅ 插入成功，category和item字段处理正确")
                    print("🔍 请检查Server6日志，确认SQL中生成的是:")
                    print("   - category: '0' 而不是 'None'")
                    print("   - item: '7' (保持原值)")
                    print("   - 其他空值字段: NULL")
                else:
                    print(f"❌ 插入失败: {result.get('message', '未知错误')}")
                    
    except Exception as e:
        print(f"❌ 测试失败: {e}")

if __name__ == "__main__":
    asyncio.run(test_category_item_fix()) 