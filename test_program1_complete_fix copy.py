#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试Program1完整修复效果
验证所有API调用都能正常工作，不会再卡住
"""

import requests
import json
import time
from datetime import datetime

# 测试配置
SERVER_URL = "http://localhost:8009"
TEST_EMPLOYEE_ID = "215829"
TEST_YEAR = 2025
TEST_MONTH = 7

def test_api_endpoint(endpoint, params=None, expected_ok=None):
    """测试API端点"""
    url = f"{SERVER_URL}{endpoint}"
    print(f"\n🔍 测试: {endpoint}")
    print(f"   URL: {url}")
    print(f"   参数: {params}")
    
    try:
        response = requests.get(url, params=params, timeout=10)
        print(f"   状态码: {response.status_code}")
        
        if response.status_code == 200:
            data = response.json()
            print(f"   响应格式: {type(data)}")
            
            if isinstance(data, dict):
                print(f"   ✅ 返回字典格式 (正确)")
                print(f"   'ok' 字段: {data.get('ok')}")
                print(f"   'data' 字段类型: {type(data.get('data'))}")
                
                if expected_ok is not None:
                    if data.get('ok') == expected_ok:
                        print(f"   ✅ 'ok' 值符合预期: {expected_ok}")
                    else:
                        print(f"   ❌ 'ok' 值不符合预期: 期望{expected_ok}, 实际{data.get('ok')}")
                
                if data.get('ok') and data.get('data'):
                    data_length = len(data['data']) if isinstance(data['data'], (list, dict)) else 1
                    print(f"   数据条数: {data_length}")
                    if isinstance(data['data'], list) and data['data']:
                        print(f"   首条数据示例: {list(data['data'][0].keys())[:5]}...")
                
                return True, data
            else:
                print(f"   ❌ 返回非字典格式 (错误): {type(data)}")
                print(f"   原始响应: {response.text[:200]}...")
                return False, None
        else:
            print(f"   ❌ HTTP错误: {response.status_code}")
            print(f"   响应: {response.text[:200]}...")
            return False, None
            
    except requests.exceptions.Timeout:
        print(f"   ❌ 超时 (10秒)")
        return False, None
    except Exception as e:
        print(f"   ❌ 异常: {e}")
        return False, None

def main():
    """主测试函数"""
    print("=" * 60)
    print("🧪 Program1 完整修复效果测试")
    print("=" * 60)
    
    # 测试所有关键API
    tests = [
        # 1. timeprotab API - Table1数据源
        {
            "endpoint": "/api/timeprotab/",
            "params": {
                "employee_id": TEST_EMPLOYEE_ID,
                "year": TEST_YEAR,
                "month": TEST_MONTH
            },
            "expected_ok": True,
            "description": "Table1 - timeprotab出勤数据"
        },
        
        # 2. entries API - Table3数据源
        {
            "endpoint": "/api/entries/",
            "params": {
                "employee_id": TEST_EMPLOYEE_ID,
                "start_date": f"{TEST_YEAR}-{TEST_MONTH:02d}-01",
                "end_date": f"{TEST_YEAR}-{TEST_MONTH:02d}-31",
                "limit": 1000
            },
            "expected_ok": True,
            "description": "Table3 - entries工作记录"
        },
        
        # 3. department API - 部门信息
        {
            "endpoint": f"/api/department/employee/{TEST_EMPLOYEE_ID}",
            "params": None,
            "expected_ok": False,  # 预期失败，因为数据为空
            "description": "部门信息获取"
        },
        
        # 4. entries/months API - 可用月份
        {
            "endpoint": "/api/entries/months",
            "params": {"employee_id": TEST_EMPLOYEE_ID},
            "expected_ok": True,
            "description": "可用月份列表"
        },
        
        # 5. chart/months API - 图表月份
        {
            "endpoint": "/api/chart/months",
            "params": {"employee_id": TEST_EMPLOYEE_ID},
            "expected_ok": True,
            "description": "图表可用月份"
        },
        
        # 6. chart/generate API - 图表数据
        {
            "endpoint": "/api/chart/generate",
            "params": {
                "employee_id": TEST_EMPLOYEE_ID,
                "start_date": f"{TEST_YEAR}-{TEST_MONTH:02d}-01",
                "end_date": f"{TEST_YEAR}-{TEST_MONTH:02d}-31",
                "chart_type": "daily"
            },
            "expected_ok": True,
            "description": "图表数据生成"
        }
    ]
    
    # 执行测试
    results = []
    for test in tests:
        print(f"\n{'='*40}")
        print(f"📋 {test['description']}")
        
        success, data = test_api_endpoint(
            test["endpoint"], 
            test.get("params"), 
            test.get("expected_ok")
        )
        
        results.append({
            "description": test["description"],
            "success": success,
            "endpoint": test["endpoint"]
        })
        
        # 避免请求过快
        time.sleep(0.5)
    
    # 汇总结果
    print(f"\n{'='*60}")
    print("📊 测试结果汇总")
    print("="*60)
    
    success_count = sum(1 for r in results if r["success"])
    total_count = len(results)
    
    for result in results:
        status = "✅ 通过" if result["success"] else "❌ 失败"
        print(f"{status} {result['description']}")
    
    print(f"\n总计: {success_count}/{total_count} 个API测试通过")
    
    if success_count == total_count:
        print("\n🎉 所有API测试通过！Program1客户端应该不会再卡住了！")
        print("\n✅ 修复效果:")
        print("   - timeprotab API 返回正确格式 → Table1正常显示")
        print("   - entries API 返回正确格式 → Table3正常显示")
        print("   - department API 修复SQL错误 → 不再卡住，继续下一步")
        print("   - 月份API 返回正确格式 → 下拉菜单正常工作")
        print("   - 图表API 返回正确格式 → 图表正常显示")
        print("   - 所有API都返回字典格式 → 不再有'list'错误")
    else:
        print(f"\n⚠️  还有 {total_count - success_count} 个API需要修复")
    
    return success_count == total_count

if __name__ == "__main__":
    main() 