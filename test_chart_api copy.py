#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试chart数据API
20250709 - 验证entries_api.py的chart-data端点是否正常工作
"""

import requests
import json
from datetime import datetime, date

def test_chart_api():
    """测试chart数据API"""
    
    # API配置
    base_url = "http://localhost:8009"
    employee_id = "215829"  # 测试员工ID
    
    # 测试参数
    start_date = "2025-07-01"
    end_date = "2025-07-31"
    
    print(f"=== 测试Chart API ===")
    print(f"Base URL: {base_url}")
    print(f"Employee ID: {employee_id}")
    print(f"Date Range: {start_date} to {end_date}")
    print()
    
    # 构建请求
    url = f"{base_url}/api/entries/chart-data"
    payload = {
        "employee_id": employee_id,
        "start_date": start_date,
        "end_date": end_date,
        "chart_type": "daily"
    }
    
    try:
        print("📡 发送POST请求...")
        response = requests.post(url, json=payload, timeout=30)
        
        print(f"📊 响应状态码: {response.status_code}")
        print(f"📊 响应头: {dict(response.headers)}")
        
        if response.status_code == 200:
            data = response.json()
            print(f"✅ API调用成功")
            print(f"📊 响应数据结构: {json.dumps(data, indent=2, ensure_ascii=False)}")
            
            # 检查数据结构
            if "daily_data" in data:
                daily_data = data["daily_data"]
                print(f"📊 daily_data长度: {len(daily_data)}")
                
                if daily_data:
                    print(f"📊 第一条数据: {daily_data[0]}")
                    
                    # 检查是否有clock_in和clock_out数据
                    sample_data = daily_data[0]
                    if "clock_in" in sample_data and "clock_out" in sample_data:
                        print(f"✅ 包含clock_in和clock_out数据")
                        print(f"📊 clock_in: {sample_data['clock_in']}")
                        print(f"📊 clock_out: {sample_data['clock_out']}")
                    else:
                        print(f"⚠️ 缺少clock_in或clock_out数据")
                        
                    # 检查entries和timeprotab数据
                    if "entries_hours" in sample_data and "timeprotab_hours" in sample_data:
                        print(f"✅ 包含entries_hours和timeprotab_hours数据")
                        print(f"📊 entries_hours: {sample_data['entries_hours']}")
                        print(f"📊 timeprotab_hours: {sample_data['timeprotab_hours']}")
                    else:
                        print(f"⚠️ 缺少entries_hours或timeprotab_hours数据")
                else:
                    print(f"⚠️ daily_data为空")
            else:
                print(f"❌ 响应中缺少daily_data字段")
                
        else:
            print(f"❌ API调用失败")
            print(f"📊 错误响应: {response.text}")
            
    except requests.exceptions.ConnectionError:
        print(f"❌ 连接失败 - 请确保Server5正在运行在 {base_url}")
    except requests.exceptions.Timeout:
        print(f"❌ 请求超时")
    except Exception as e:
        print(f"❌ 请求异常: {e}")

def test_timeprotab_api():
    """测试timeprotab API"""
    
    base_url = "http://localhost:8009"
    employee_id = "215829"
    
    print(f"\n=== 测试Timeprotab API ===")
    
    # 测试timeprotab数据
    url = f"{base_url}/api/timeprotab/"
    params = {
        "employee_id": employee_id,
        "year": 2025,
        "month": 7,
        "limit": 10
    }
    
    try:
        print("📡 发送GET请求...")
        response = requests.get(url, params=params, timeout=30)
        
        print(f"📊 响应状态码: {response.status_code}")
        
        if response.status_code == 200:
            data = response.json()
            print(f"✅ Timeprotab API调用成功")
            print(f"📊 响应数据结构: {json.dumps(data, indent=2, ensure_ascii=False)}")
            
            if "data" in data and data["data"]:
                sample_record = data["data"][0]
                print(f"📊 第一条记录: {sample_record}")
                
                # 检查关键字段
                key_fields = ["日付", "所定時間", "早出残業", "出勤時刻", "退勤時刻"]
                for field in key_fields:
                    if field in sample_record:
                        print(f"✅ 包含字段: {field} = {sample_record[field]}")
                    else:
                        print(f"⚠️ 缺少字段: {field}")
            else:
                print(f"⚠️ 没有timeprotab数据")
        else:
            print(f"❌ Timeprotab API调用失败")
            print(f"📊 错误响应: {response.text}")
            
    except Exception as e:
        print(f"❌ Timeprotab API请求异常: {e}")

def test_entries_api():
    """测试entries API"""
    
    base_url = "http://localhost:8009"
    employee_id = "215829"
    
    print(f"\n=== 测试Entries API ===")
    
    # 测试entries数据
    url = f"{base_url}/api/entries/"
    params = {
        "employee_id": employee_id,
        "start_date": "2025-07-01",
        "end_date": "2025-07-31",
        "limit": 10
    }
    
    try:
        print("📡 发送GET请求...")
        response = requests.get(url, params=params, timeout=30)
        
        print(f"📊 响应状态码: {response.status_code}")
        
        if response.status_code == 200:
            data = response.json()
            print(f"✅ Entries API调用成功")
            print(f"📊 响应数据结构: {json.dumps(data, indent=2, ensure_ascii=False)}")
            
            if data:
                sample_record = data[0]
                print(f"📊 第一条记录: {sample_record}")
                
                # 检查关键字段
                key_fields = ["entry_date", "duration", "employee_id"]
                for field in key_fields:
                    if field in sample_record:
                        print(f"✅ 包含字段: {field} = {sample_record[field]}")
                    else:
                        print(f"⚠️ 缺少字段: {field}")
            else:
                print(f"⚠️ 没有entries数据")
        else:
            print(f"❌ Entries API调用失败")
            print(f"📊 错误响应: {response.text}")
            
    except Exception as e:
        print(f"❌ Entries API请求异常: {e}")

if __name__ == "__main__":
    print("🚀 开始测试Chart相关API...")
    
    # 测试各个API
    test_chart_api()
    test_timeprotab_api()
    test_entries_api()
    
    print("\n🏁 测试完成") 