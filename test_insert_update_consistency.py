#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试INSERT和UPDATE操作一致性的脚本
验证INSERT操作现在使用与UPDATE相同的逻辑
"""

import asyncio
import aiohttp
import json
from datetime import datetime

async def test_insert_update_consistency():
    """测试INSERT和UPDATE操作的一致性"""
    
    print("🧪 测试INSERT和UPDATE操作的一致性")
    print("=" * 50)
    
    # 测试数据：包含各种值的category和item
    test_cases = [
        {
            'name': '测试1: category=0, item=7',
            'data': {
                'employee_id': '215829',
                'entry_date': '2025/07/15',
                'model': None,
                'number': None,
                'factory_number': 'HA0484',
                'project_number': None,
                'unit_number': None,
                'category': 0,  # 整数0
                'item': 7,      # 整数7
                'duration': 8.0,
                'department': '131'
            }
        },
        {
            'name': '测试2: category=None, item=None',
            'data': {
                'employee_id': '215829',
                'entry_date': '2025/07/15',
                'model': None,
                'number': None,
                'factory_number': 'HA0484',
                'project_number': None,
                'unit_number': None,
                'category': None,  # None值
                'item': None,      # None值
                'duration': 8.0,
                'department': '131'
            }
        },
        {
            'name': '测试3: category=5, item=10',
            'data': {
                'employee_id': '215829',
                'entry_date': '2025/07/15',
                'model': None,
                'number': None,
                'factory_number': 'HA0484',
                'project_number': None,
                'unit_number': None,
                'category': 5,   # 正常值
                'item': 10,      # 正常值
                'duration': 8.0,
                'department': '131'
            }
        }
    ]
    
    try:
        async with aiohttp.ClientSession() as session:
            # 测试连接
            async with session.get('http://192.168.1.100:8010/mdb/test') as response:
                if response.status == 200:
                    print("✅ Server6连接正常")
                else:
                    print(f"❌ Server6连接失败: {response.status}")
                    return
            
            for i, test_case in enumerate(test_cases, 1):
                print(f"\n📋 {test_case['name']}")
                print(f"📤 发送数据: {json.dumps(test_case['data'], ensure_ascii=False, indent=2)}")
                
                # 发送插入请求
                async with session.post(
                    'http://192.168.1.100:8010/mdb/entries/insert',
                    json=test_case['data'],
                    headers={'Content-Type': 'application/json'}
                ) as response:
                    result = await response.json()
                    print(f"📥 Server6响应: {json.dumps(result, ensure_ascii=False, indent=2)}")
                    
                    if result.get('success'):
                        print("✅ 插入成功")
                        external_id = result.get('inserted_id')
                        
                        if external_id:
                            print(f"🔄 测试UPDATE操作，external_id={external_id}")
                            
                            # 测试UPDATE操作
                            update_data = {
                                'category': test_case['data']['category'],
                                'item': test_case['data']['item']
                            }
                            
                            async with session.put(
                                f'http://192.168.1.100:8010/mdb/entries/update/{external_id}',
                                json=update_data,
                                headers={'Content-Type': 'application/json'}
                            ) as update_response:
                                update_result = await update_response.json()
                                print(f"📥 UPDATE响应: {json.dumps(update_result, ensure_ascii=False, indent=2)}")
                                
                                if update_result.get('success'):
                                    print("✅ UPDATE成功")
                                else:
                                    print(f"❌ UPDATE失败: {update_result.get('message', '未知错误')}")
                    else:
                        print(f"❌ 插入失败: {result.get('message', '未知错误')}")
                        
    except Exception as e:
        print(f"❌ 测试失败: {e}")

if __name__ == "__main__":
    asyncio.run(test_insert_update_consistency()) 