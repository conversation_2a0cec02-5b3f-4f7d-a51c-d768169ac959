#!/usr/bin/env python3
"""
数据库连接测试工具
25/06/25 17：00 创建 - 用于测试postgres 16到17升级后的连接状态
快速诊断所有微服务的数据库连接问题
"""

import asyncio
import asyncpg
import sys
from datetime import datetime

# 数据库配置
DB_CONFIG = {
    'host': '************',
    'port': 5432,  # 25/06/25 17：00 更新为postgres17.5标准端口
    'user': 'postgres',
    'password': 'pojiami0602'
}

# 所有微服务使用的数据库
MICROSERVICE_DBS = {
    'server (主业务)': 'postgres',
    'server3 (认证服务)': 'auth',
    'server (IMDB)': 'imdb'
}

async def test_basic_connection():
    """测试基础PostgreSQL连接"""
    print("🔍 测试基础PostgreSQL连接...")
    try:
        conn = await asyncpg.connect(
            host=DB_CONFIG['host'],
            port=DB_CONFIG['port'],
            user=DB_CONFIG['user'],
            password=DB_CONFIG['password']
        )
        
        version = await conn.fetchval("SELECT version()")
        print(f"✅ PostgreSQL连接成功")
        print(f"   版本信息: {version}")
        
        await conn.close()
        return True
        
    except Exception as e:
        print(f"❌ PostgreSQL连接失败: {e}")
        return False

async def test_database_exists(db_name: str):
    """测试数据库是否存在"""
    try:
        conn = await asyncpg.connect(
            host=DB_CONFIG['host'],
            port=DB_CONFIG['port'],
            user=DB_CONFIG['user'],
            password=DB_CONFIG['password'],
            database='postgres'
        )
        
        exists = await conn.fetchval(
            "SELECT 1 FROM pg_database WHERE datname = $1",
            db_name
        )
        
        await conn.close()
        return bool(exists)
        
    except Exception as e:
        print(f"❌ 检查数据库 {db_name} 存在性失败: {e}")
        return False

async def test_database_connection(db_name: str):
    """测试特定数据库连接"""
    try:
        conn = await asyncpg.connect(
            host=DB_CONFIG['host'],
            port=DB_CONFIG['port'],
            user=DB_CONFIG['user'],
            password=DB_CONFIG['password'],
            database=db_name
        )
        
        # 测试简单查询
        result = await conn.fetchval("SELECT current_database()")
        
        await conn.close()
        return True, result
        
    except Exception as e:
        return False, str(e)

async def test_auth_tables():
    """测试认证数据库表结构"""
    print("\n🔍 测试认证数据库表结构...")
    try:
        conn = await asyncpg.connect(
            host=DB_CONFIG['host'],
            port=DB_CONFIG['port'],
            user=DB_CONFIG['user'],
            password=DB_CONFIG['password'],
            database='auth'
        )
        
        # 检查users表
        users_exists = await conn.fetchval("""
            SELECT EXISTS (
                SELECT FROM information_schema.tables 
                WHERE table_schema = 'public' 
                AND table_name = 'users'
            )
        """)
        
        # 检查hardware_fingerprints表
        fingerprints_exists = await conn.fetchval("""
            SELECT EXISTS (
                SELECT FROM information_schema.tables 
                WHERE table_schema = 'public' 
                AND table_name = 'hardware_fingerprints'
            )
        """)
        
        print(f"   users表: {'✅ 存在' if users_exists else '❌ 不存在'}")
        print(f"   hardware_fingerprints表: {'✅ 存在' if fingerprints_exists else '❌ 不存在'}")
        
        await conn.close()
        return users_exists and fingerprints_exists
        
    except Exception as e:
        print(f"❌ 测试认证表结构失败: {e}")
        return False

async def test_main_tables():
    """测试主业务数据库表结构"""
    print("\n🔍 测试主业务数据库表结构...")
    try:
        conn = await asyncpg.connect(
            host=DB_CONFIG['host'],
            port=DB_CONFIG['port'],
            user=DB_CONFIG['user'],
            password=DB_CONFIG['password'],
            database='postgres'
        )
        
        # 检查feature_flags表
        flags_exists = await conn.fetchval("""
            SELECT EXISTS (
                SELECT FROM information_schema.tables 
                WHERE table_schema = 'public' 
                AND table_name = 'feature_flags'
            )
        """)
        
        # 检查tasks表
        tasks_exists = await conn.fetchval("""
            SELECT EXISTS (
                SELECT FROM information_schema.tables 
                WHERE table_schema = 'public' 
                AND table_name = 'tasks'
            )
        """)
        
        print(f"   feature_flags表: {'✅ 存在' if flags_exists else '❌ 不存在'}")
        print(f"   tasks表: {'✅ 存在' if tasks_exists else '❌ 不存在'}")
        
        await conn.close()
        return flags_exists and tasks_exists
        
    except Exception as e:
        print(f"❌ 测试主业务表结构失败: {e}")
        return False

async def test_imdb_tables():
    """测试IMDB数据库表结构"""
    print("\n🔍 测试IMDB数据库表结构...")
    try:
        conn = await asyncpg.connect(
            host=DB_CONFIG['host'],
            port=DB_CONFIG['port'],
            user=DB_CONFIG['user'],
            password=DB_CONFIG['password'],
            database='imdb'
        )
        
        # 检查工时相关表
        tables = ['add25', 'change25', 'del25']
        table_status = {}
        
        for table in tables:
            exists = await conn.fetchval(f"""
                SELECT EXISTS (
                    SELECT FROM information_schema.tables 
                    WHERE table_schema = 'public' 
                    AND table_name = '{table}'
                )
            """)
            table_status[table] = exists
            print(f"   {table}表: {'✅ 存在' if exists else '❌ 不存在'}")
        
        await conn.close()
        return all(table_status.values())
        
    except Exception as e:
        print(f"❌ 测试IMDB表结构失败: {e}")
        return False

async def main():
    """主函数"""
    print("=" * 80)
    print("🔍 MySuite 数据库连接测试工具")
    print("25/06/25 17：00 - PostgreSQL 16 升级到 17 连接诊断")
    print(f"测试时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print("=" * 80)
    
    # 总体测试结果
    all_tests_passed = True
    
    # 1. 测试基础连接
    print("\n📊 第1步: 测试基础PostgreSQL连接")
    basic_connection = await test_basic_connection()
    if not basic_connection:
        print("❌ 基础连接失败，请检查PostgreSQL服务状态")
        return False
    
    # 2. 测试各个数据库存在性和连接
    print(f"\n📊 第2步: 测试微服务数据库连接")
    for service_name, db_name in MICROSERVICE_DBS.items():
        print(f"\n🔍 测试 {service_name} 数据库 ({db_name})...")
        
        # 检查数据库是否存在
        exists = await test_database_exists(db_name)
        if not exists:
            print(f"❌ 数据库 {db_name} 不存在")
            all_tests_passed = False
            continue
        
        # 测试连接
        success, info = await test_database_connection(db_name)
        if success:
            print(f"✅ 数据库 {db_name} 连接成功")
            print(f"   当前数据库: {info}")
        else:
            print(f"❌ 数据库 {db_name} 连接失败: {info}")
            all_tests_passed = False
    
    # 3. 测试表结构
    print(f"\n📊 第3步: 测试数据库表结构")
    
    # 测试认证表
    auth_tables = await test_auth_tables()
    if not auth_tables:
        print("⚠️  认证数据库表结构不完整")
        all_tests_passed = False
    
    # 测试主业务表
    main_tables = await test_main_tables()
    if not main_tables:
        print("⚠️  主业务数据库表结构不完整")
        all_tests_passed = False
    
    # 测试IMDB表
    imdb_tables = await test_imdb_tables()
    if not imdb_tables:
        print("⚠️  IMDB数据库表结构不完整")
        all_tests_passed = False
    
    # 4. 输出诊断结果
    print("\n" + "=" * 80)
    if all_tests_passed:
        print("🎉 所有数据库连接和表结构测试通过！")
        print("✅ PostgreSQL 17.5 升级配置正确")
        print("✅ 微服务应该可以正常启动")
    else:
        print("⚠️  发现数据库配置问题！")
        print("📝 解决建议:")
        print("   1. 如果数据库不存在，运行: python init_databases.py")
        print("   2. 如果表结构缺失，运行: python init_databases.py")
        print("   3. 检查PostgreSQL 17.5服务状态")
        print("   4. 确认端口5432开放且可访问")
    
    print("=" * 80)
    
    return all_tests_passed

if __name__ == "__main__":
    # 运行测试
    success = asyncio.run(main())
    
    if success:
        print("\n🚀 接下来可以:")
        print("1. 启动微服务: bash start_microservices.sh")
        print("2. 测试客户端登录功能")
        sys.exit(0)
    else:
        print("\n❌ 请根据上述建议解决问题后重新测试")
        sys.exit(1) 