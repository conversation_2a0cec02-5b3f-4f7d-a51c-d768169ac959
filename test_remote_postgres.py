#!/usr/bin/env python3
"""
远程PostgreSQL连接诊断工具
25/06/25 17:30 创建 - 专门诊断Windows 10上PostgreSQL 17.5的连接问题
"""

import asyncio
import asyncpg
import sys
import socket
from datetime import datetime

# 数据库配置
DB_CONFIG = {
    'host': '************',
    'port': 5432,
    'user': 'postgres',
    'password': 'pojiami0602'
}

def test_tcp_connection():
    """测试TCP连接"""
    print("🔍 测试TCP连接...")
    try:
        sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
        sock.settimeout(5)
        result = sock.connect_ex((DB_CONFIG['host'], DB_CONFIG['port']))
        sock.close()
        
        if result == 0:
            print("✅ TCP连接成功")
            return True
        else:
            print(f"❌ TCP连接失败，错误代码: {result}")
            return False
    except Exception as e:
        print(f"❌ TCP连接异常: {e}")
        return False

async def test_basic_connection():
    """测试基础数据库连接"""
    print("\n🔍 测试基础数据库连接...")
    try:
        conn = await asyncpg.connect(
            host=DB_CONFIG['host'],
            port=DB_CONFIG['port'],
            user=DB_CONFIG['user'],
            password=DB_CONFIG['password']
        )
        
        version = await conn.fetchval("SELECT version()")
        print(f"✅ 数据库连接成功")
        print(f"   版本: {version}")
        
        await conn.close()
        return True
        
    except asyncpg.exceptions.InvalidPasswordError as e:
        print(f"❌ 密码错误: {e}")
        return False
    except asyncpg.exceptions.InvalidAuthorizationSpecificationError as e:
        print(f"❌ 认证失败: {e}")
        return False
    except asyncpg.exceptions.PostgresConnectionError as e:
        print(f"❌ PostgreSQL连接错误: {e}")
        return False
    except Exception as e:
        print(f"❌ 未知错误: {e}")
        print(f"   错误类型: {type(e)}")
        return False

async def test_different_auth_methods():
    """测试不同的认证方法"""
    print("\n🔍 测试不同认证方法...")
    
    # 测试1: 不指定数据库
    print("测试1: 连接到默认数据库")
    try:
        conn = await asyncpg.connect(
            host=DB_CONFIG['host'],
            port=DB_CONFIG['port'],
            user=DB_CONFIG['user'],
            password=DB_CONFIG['password']
        )
        
        current_db = await conn.fetchval("SELECT current_database()")
        print(f"✅ 连接成功，当前数据库: {current_db}")
        await conn.close()
        
    except Exception as e:
        print(f"❌ 连接失败: {e}")
    
    # 测试2: 连接到postgres数据库
    print("\n测试2: 连接到postgres数据库")
    try:
        conn = await asyncpg.connect(
            host=DB_CONFIG['host'],
            port=DB_CONFIG['port'],
            user=DB_CONFIG['user'],
            password=DB_CONFIG['password'],
            database='postgres'
        )
        
        current_db = await conn.fetchval("SELECT current_database()")
        print(f"✅ 连接成功，当前数据库: {current_db}")
        await conn.close()
        
    except Exception as e:
        print(f"❌ 连接失败: {e}")

async def test_database_existence():
    """测试数据库是否存在"""
    print("\n🔍 检查数据库是否存在...")
    
    try:
        conn = await asyncpg.connect(
            host=DB_CONFIG['host'],
            port=DB_CONFIG['port'],
            user=DB_CONFIG['user'],
            password=DB_CONFIG['password'],
            database='postgres'
        )
        
        # 查询所有数据库
        databases = await conn.fetch("SELECT datname FROM pg_database WHERE datistemplate = false")
        
        print("✅ 现有数据库列表:")
        for db in databases:
            print(f"   - {db['datname']}")
        
        # 检查我们需要的数据库
        db_names = [row['datname'] for row in databases]
        required_dbs = {'postgres', 'auth', 'imdb'}
        
        print(f"\n🔍 检查必需数据库:")
        for db_name in required_dbs:
            if db_name in db_names:
                print(f"   ✅ {db_name} - 存在")
            else:
                print(f"   ❌ {db_name} - 不存在")
        
        await conn.close()
        return True
        
    except Exception as e:
        print(f"❌ 检查数据库失败: {e}")
        return False

async def test_user_permissions():
    """测试用户权限"""
    print("\n🔍 测试用户权限...")
    
    try:
        conn = await asyncpg.connect(
            host=DB_CONFIG['host'],
            port=DB_CONFIG['port'],
            user=DB_CONFIG['user'],
            password=DB_CONFIG['password'],
            database='postgres'
        )
        
        # 检查当前用户
        current_user = await conn.fetchval("SELECT current_user")
        print(f"✅ 当前用户: {current_user}")
        
        # 检查用户权限
        is_superuser = await conn.fetchval(
            "SELECT usesuper FROM pg_user WHERE usename = current_user"
        )
        print(f"   超级用户权限: {'✅ 是' if is_superuser else '❌ 否'}")
        
        # 检查创建数据库权限
        can_create_db = await conn.fetchval(
            "SELECT usecreatedb FROM pg_user WHERE usename = current_user"
        )
        print(f"   创建数据库权限: {'✅ 是' if can_create_db else '❌ 否'}")
        
        await conn.close()
        return True
        
    except Exception as e:
        print(f"❌ 检查用户权限失败: {e}")
        return False

async def test_connection_settings():
    """测试连接设置"""
    print("\n🔍 测试连接设置...")
    
    try:
        conn = await asyncpg.connect(
            host=DB_CONFIG['host'],
            port=DB_CONFIG['port'],
            user=DB_CONFIG['user'],
            password=DB_CONFIG['password'],
            database='postgres'
        )
        
        # 检查PostgreSQL配置
        max_connections = await conn.fetchval("SHOW max_connections")
        print(f"✅ 最大连接数: {max_connections}")
        
        # 检查监听地址
        listen_addresses = await conn.fetchval("SHOW listen_addresses")
        print(f"✅ 监听地址: {listen_addresses}")
        
        # 检查端口
        port = await conn.fetchval("SHOW port")
        print(f"✅ 端口: {port}")
        
        await conn.close()
        return True
        
    except Exception as e:
        print(f"❌ 检查连接设置失败: {e}")
        return False

def provide_solutions():
    """提供解决方案"""
    print("\n" + "=" * 80)
    print("🛠️  可能的解决方案:")
    print("=" * 80)
    
    print("1. 检查Windows 10上的PostgreSQL 17.5设置:")
    print("   - 确认PostgreSQL服务正在运行")
    print("   - 检查防火墙是否允许5432端口")
    print("   - 检查pg_hba.conf配置文件")
    
    print("\n2. PostgreSQL配置文件位置(Windows):")
    print("   - 数据目录: C:\\Program Files\\PostgreSQL\\17\\data\\")
    print("   - pg_hba.conf: C:\\Program Files\\PostgreSQL\\17\\data\\pg_hba.conf")
    print("   - postgresql.conf: C:\\Program Files\\PostgreSQL\\17\\data\\postgresql.conf")
    
    print("\n3. 检查pg_hba.conf配置:")
    print("   确保有类似以下的行:")
    print("   host    all             all             ***********/24          md5")
    print("   或者:")
    print("   host    all             all             0.0.0.0/0               md5")
    
    print("\n4. 检查postgresql.conf配置:")
    print("   确保有以下设置:")
    print("   listen_addresses = '*'")
    print("   port = 5432")
    
    print("\n5. 重启PostgreSQL服务(Windows):")
    print("   - 打开服务管理器")
    print("   - 找到PostgreSQL服务")
    print("   - 右键重启")

async def main():
    """主函数"""
    print("=" * 80)
    print("🔍 远程PostgreSQL连接诊断工具")
    print("25/06/25 17:30 - Windows 10 PostgreSQL 17.5 连接测试")
    print(f"测试时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print("=" * 80)
    
    print(f"目标服务器: {DB_CONFIG['host']}:{DB_CONFIG['port']}")
    print(f"用户名: {DB_CONFIG['user']}")
    print(f"密码: {'*' * len(DB_CONFIG['password'])}")
    
    # 1. 测试TCP连接
    tcp_ok = test_tcp_connection()
    if not tcp_ok:
        print("❌ TCP连接失败，请检查网络和防火墙")
        provide_solutions()
        return False
    
    # 2. 测试数据库连接
    db_ok = await test_basic_connection()
    if not db_ok:
        print("❌ 数据库连接失败，请检查认证设置")
        provide_solutions()
        return False
    
    # 3. 测试不同认证方法
    await test_different_auth_methods()
    
    # 4. 测试数据库存在性
    await test_database_existence()
    
    # 5. 测试用户权限
    await test_user_permissions()
    
    # 6. 测试连接设置
    await test_connection_settings()
    
    print("\n" + "=" * 80)
    print("🎉 远程PostgreSQL连接诊断完成！")
    print("如果所有测试都通过，微服务应该可以正常启动")
    print("=" * 80)
    
    return True

if __name__ == "__main__":
    try:
        success = asyncio.run(main())
        if not success:
            sys.exit(1)
    except KeyboardInterrupt:
        print("\n\n🛑 用户中断测试")
        sys.exit(1)
    except Exception as e:
        print(f"\n❌ 测试过程中发生错误: {e}")
        sys.exit(1) 