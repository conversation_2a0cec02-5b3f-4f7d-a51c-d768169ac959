# PostgreSQL 16 到 17 升级修复指南

**创建时间**: 25/06/25 17:00  
**修复目的**: 解决PostgreSQL升级导致的微服务8006启动失败问题

## 🔧 问题描述

- **原因**: PostgreSQL从16升级到17.5，端口从5435变更为5432
- **影响**: 数据库表结构丢失，导致认证服务(server3)无法启动
- **症状**: 微服务8006启动超时，认证功能不可用

## 📋 解决方案

### 第1步: 测试数据库连接
```bash
# 运行数据库连接测试
python test_database_connections.py
```

### 第2步: 初始化数据库结构
```bash
# 重建所有丢失的数据库和表结构
python init_databases.py
```

### 第3步: 验证微服务启动
```bash
# 启动所有微服务
bash start_microservices.sh
```

## 🛠️ 已修复的配置文件

### 1. server/app/config.py
```python
# 25/06/25 17：00 更改postgres 16到17，端口从5435改为5432
DB_PORT = 5432  # 从5435改为5432，postgres17.5标准端口
```

### 2. server3/app/config.py
```python
# 25/06/25 17：00 更改postgres 16到17，端口从5435改为5432
DB_PORT = 5432  # 从5435改为5432，postgres17.5标准端口
```

## 📊 数据库结构说明

### 认证数据库 (auth)
- **users表**: 标准用户认证 (JWT系统)
- **hardware_fingerprints表**: 硬件指纹绑定

### 主业务数据库 (postgres)
- **feature_flags表**: 系统功能开关
- **tasks表**: 任务管理

### IMDB数据库 (imdb)
- **add25表**: 工时添加记录
- **change25表**: 工时变更记录
- **del25表**: 工时删除记录

## 🔍 故障排除

### 如果微服务8006仍然无法启动:

1. **检查PostgreSQL服务状态**
```bash
sudo systemctl status postgresql
```

2. **确认端口开放**
```bash
sudo netstat -tuln | grep 5432
```

3. **手动测试连接**
```bash
psql -h 192.168.3.93 -p 5432 -U postgres -d auth
```

4. **查看详细错误日志**
```bash
# 查看认证服务日志
tail -f logs/auth_service.log
```

## ⚠️ 重要提醒

- 本次升级已更新所有配置文件的端口设置
- 数据库初始化脚本会自动创建所有必要的表结构
- 如果有特殊数据需要迁移，请在运行初始化脚本前备份
- JWT密钥配置已确认与客户端保持一致

## 🎯 预期结果

修复完成后，您应该看到:
- ✅ 所有4个微服务正常启动
- ✅ 认证服务8006响应正常
- ✅ 客户端可以正常登录
- ✅ 硬件指纹验证功能正常

## 📞 如果问题持续

如果按照上述步骤操作后问题仍然存在，请检查:
1. PostgreSQL 17.5安装是否完整
2. 网络连接到192.168.3.93:5432
3. 数据库用户权限设置
4. 防火墙设置

---
*此文档记录了25/06/25 17:00的PostgreSQL升级修复过程* 