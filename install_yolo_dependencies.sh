#!/bin/bash
# install_yolo_dependencies.sh
# 20250619.18:30 安装YOLO AI检测依赖脚本

echo "🤖 MySuite YOLO AI检测依赖安装脚本"
echo "=========================================="

# 检查Python版本
echo "1. 检查Python版本..."
python_version=$(python3 --version 2>/dev/null)
if [ $? -eq 0 ]; then
    echo "✅ Python版本: $python_version"
else
    echo "❌ Python3未安装，请先安装Python3"
    exit 1
fi

# 检查pip
echo "2. 检查pip..."
pip_version=$(pip3 --version 2>/dev/null)
if [ $? -eq 0 ]; then
    echo "✅ Pip版本: $pip_version"
else
    echo "❌ pip3未安装，请先安装pip3"
    exit 1
fi

# 升级pip
echo "3. 升级pip..."
pip3 install --upgrade pip

# 安装PyTorch (CPU版本，如果需要GPU版本请手动安装)
echo "4. 安装PyTorch..."
pip3 install torch torchvision --index-url https://download.pytorch.org/whl/cpu

# 安装Ultralytics YOLO
echo "5. 安装Ultralytics YOLO..."
pip3 install ultralytics

# 安装其他图像处理依赖
echo "6. 安装图像处理依赖..."
pip3 install pillow opencv-python numpy pandas

# 安装server4的依赖
echo "7. 安装server4依赖..."
cd server4
pip3 install -r requirements.txt
cd ..

# 测试安装
echo "8. 测试YOLO安装..."
python3 -c "
try:
    import torch
    import ultralytics
    from ultralytics import YOLO
    print('✅ PyTorch版本:', torch.__version__)
    print('✅ Ultralytics版本:', ultralytics.__version__)
    print('✅ CUDA可用:', torch.cuda.is_available())
    print('✅ YOLO安装成功！')
except ImportError as e:
    print('❌ 导入失败:', e)
    exit(1)
"

if [ $? -eq 0 ]; then
    echo ""
    echo "🎉 YOLO依赖安装完成！"
    echo ""
    echo "下一步操作:"
    echo "1. 启动视频监控服务: python3 server4/start_video_service.py"
    echo "2. 测试YOLO功能: python3 test_yolo_detection.py"
    echo "3. 启动客户端: python3 client/client_fixed.py"
    echo ""
    echo "注意事项:"
    echo "- 首次使用时会自动下载YOLO模型文件(约6MB)"
    echo "- 如需GPU加速，请安装CUDA版本的PyTorch"
    echo "- 检测支持80+种物体类别(人员、车辆、动物等)"
else
    echo "❌ 安装过程中出现错误，请检查日志"
    exit 1
fi 