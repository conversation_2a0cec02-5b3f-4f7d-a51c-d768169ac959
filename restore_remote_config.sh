#!/bin/bash
# 恢复远程PostgreSQL配置
# 25/06/25 17:35 创建 - 恢复到Windows 10远程数据库连接

echo "================================="
echo "🔄 恢复远程PostgreSQL配置"
echo "================================="

if [ -f "server/app/config.py.remote_backup" ]; then
    echo "📝 恢复server配置..."
    cp server/app/config.py.remote_backup server/app/config.py
    echo "✅ server配置已恢复"
else
    echo "⚠️  未找到server配置备份，手动恢复..."
    sed -i 's/DB_HOST = "localhost"/DB_HOST = "************"/' server/app/config.py
    sed -i 's/IMDB_HOST = "localhost"/IMDB_HOST = "************"/' server/app/config.py
fi

if [ -f "server3/app/config.py.remote_backup" ]; then
    echo "📝 恢复server3配置..."
    cp server3/app/config.py.remote_backup server3/app/config.py
    echo "✅ server3配置已恢复"
else
    echo "⚠️  未找到server3配置备份，手动恢复..."
    sed -i 's/DB_HOST = "localhost"/DB_HOST = "************"/' server3/app/config.py
fi

echo "🔍 测试远程连接..."
python test_remote_postgres.py

echo "================================="
echo "🎉 远程配置恢复完成！"
echo "=================================" 