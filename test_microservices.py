#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
MySuite 微服务测试脚本
用于测试所有微服务的API功能
"""

import asyncio
import aiohttp
import json
import time
import sys
from typing import Dict, List, Optional
from dataclasses import dataclass
from colorama import init, Fore, Back, Style
import websockets
import argparse

# 初始化colorama用于跨平台颜色输出
init(autoreset=True)

@dataclass
class ServiceConfig:
    """服务配置"""
    name: str
    host: str = "localhost"
    port: int = 8000
    base_url: str = ""
    
    def __post_init__(self):
        if not self.base_url:
            self.base_url = f"http://{self.host}:{self.port}"

class MicroservicesTester:
    """微服务测试器"""
    
    def __init__(self):
        self.services = {
            'main': ServiceConfig("主服务", port=8003),
            'chat': ServiceConfig("聊天微服务", port=8005)
        }
        self.session: Optional[aiohttp.ClientSession] = None
        self.test_results = []
    
    def print_colored(self, message: str, color: str = Fore.WHITE, style: str = ""):
        """打印带颜色的消息"""
        print(f"{style}{color}{message}{Style.RESET_ALL}")
    
    def print_title(self, title: str):
        """打印标题"""
        print("\n" + "=" * 60)
        self.print_colored(title, Fore.CYAN, Style.BRIGHT)
        print("=" * 60)
    
    def print_success(self, message: str):
        """打印成功消息"""
        self.print_colored(f"✅ {message}", Fore.GREEN)
    
    def print_error(self, message: str):
        """打印错误消息"""
        self.print_colored(f"❌ {message}", Fore.RED)
    
    def print_warning(self, message: str):
        """打印警告消息"""
        self.print_colored(f"⚠️  {message}", Fore.YELLOW)
    
    def print_info(self, message: str):
        """打印信息消息"""
        self.print_colored(f"ℹ️  {message}", Fore.BLUE)
    
    async def create_session(self):
        """创建HTTP会话"""
        timeout = aiohttp.ClientTimeout(total=10)
        self.session = aiohttp.ClientSession(timeout=timeout)
    
    async def close_session(self):
        """关闭HTTP会话"""
        if self.session:
            await self.session.close()
    
    async def test_service_health(self, service_key: str) -> Dict:
        """测试服务健康状态"""
        service = self.services[service_key]
        result = {
            'service': service.name,
            'url': service.base_url,
            'healthy': False,
            'response_time': 0,
            'status_code': 0,
            'response': None,
            'error': None
        }
        
        try:
            start_time = time.time()
            async with self.session.get(f"{service.base_url}/") as response:
                result['response_time'] = round((time.time() - start_time) * 1000, 2)
                result['status_code'] = response.status
                result['response'] = await response.json()
                result['healthy'] = response.status == 200
                
        except Exception as e:
            result['error'] = str(e)
        
        return result
    
    async def test_service_endpoints(self, service_key: str) -> List[Dict]:
        """测试服务端点"""
        service = self.services[service_key]
        
        # 定义要测试的端点
        endpoints = {
            'main': [
                ('/', 'GET', '根路径'),
                ('/health', 'GET', '健康检查'),
                ('/docs', 'GET', 'API文档'),
                ('/api/client/version', 'GET', '客户端版本'),
                ('/api/db/status', 'GET', '数据库状态'),
            ],
            'chat': [
                ('/', 'GET', '根路径'),
                ('/health', 'GET', '健康检查'),
                ('/docs', 'GET', 'API文档'),
                ('/api/chat/rooms', 'GET', '聊天室列表'),
            ]
        }
        
        results = []
        service_endpoints = endpoints.get(service_key, [])
        
        for endpoint, method, description in service_endpoints:
            result = {
                'endpoint': endpoint,
                'method': method,
                'description': description,
                'success': False,
                'status_code': 0,
                'response_time': 0,
                'error': None
            }
            
            try:
                start_time = time.time()
                url = f"{service.base_url}{endpoint}"
                
                if method == 'GET':
                    async with self.session.get(url) as response:
                        result['response_time'] = round((time.time() - start_time) * 1000, 2)
                        result['status_code'] = response.status
                        result['success'] = response.status < 400
                        
            except Exception as e:
                result['error'] = str(e)
            
            results.append(result)
        
        return results
    
    async def test_websocket_connection(self, service_key: str) -> Dict:
        """测试WebSocket连接"""
        if service_key != 'chat':
            return {'supported': False, 'reason': '此服务不支持WebSocket'}
        
        service = self.services[service_key]
        ws_url = f"ws://{service.host}:{service.port}/ws/chat"
        
        result = {
            'supported': True,
            'connected': False,
            'error': None,
            'url': ws_url
        }
        
        try:
            async with websockets.connect(ws_url, timeout=5) as websocket:
                # 尝试发送测试消息
                test_message = {
                    'type': 'test',
                    'message': 'Hello from test client'
                }
                await websocket.send(json.dumps(test_message))
                
                # 等待响应
                try:
                    response = await asyncio.wait_for(websocket.recv(), timeout=2)
                    result['connected'] = True
                    result['response'] = response
                except asyncio.TimeoutError:
                    result['connected'] = True  # 连接成功，但没有响应
                    
        except Exception as e:
            result['error'] = str(e)
        
        return result
    
    async def run_comprehensive_tests(self):
        """运行综合测试"""
        self.print_title("MySuite 微服务综合测试")
        
        await self.create_session()
        
        try:
            # 1. 测试服务健康状态
            self.print_title("服务健康状态检查")
            
            for service_key in self.services:
                self.print_info(f"测试 {self.services[service_key].name}...")
                result = await self.test_service_health(service_key)
                self.test_results.append(result)
                
                if result['healthy']:
                    self.print_success(
                        f"{result['service']} 健康 "
                        f"(响应时间: {result['response_time']}ms)"
                    )
                else:
                    self.print_error(
                        f"{result['service']} 不健康 "
                        f"(错误: {result['error'] or '状态码: ' + str(result['status_code'])})"
                    )
            
            # 2. 测试API端点
            self.print_title("API端点测试")
            
            for service_key in self.services:
                self.print_info(f"测试 {self.services[service_key].name} API端点...")
                endpoints_results = await self.test_service_endpoints(service_key)
                
                for endpoint_result in endpoints_results:
                    if endpoint_result['success']:
                        self.print_success(
                            f"{endpoint_result['method']} {endpoint_result['endpoint']} "
                            f"({endpoint_result['description']}) - "
                            f"{endpoint_result['response_time']}ms"
                        )
                    else:
                        self.print_error(
                            f"{endpoint_result['method']} {endpoint_result['endpoint']} "
                            f"({endpoint_result['description']}) - "
                            f"失败: {endpoint_result['error'] or '状态码: ' + str(endpoint_result['status_code'])}"
                        )
            
            # 3. 测试WebSocket连接
            self.print_title("WebSocket连接测试")
            
            ws_result = await self.test_websocket_connection('chat')
            if ws_result['supported']:
                if ws_result['connected']:
                    self.print_success(f"WebSocket连接成功: {ws_result['url']}")
                else:
                    self.print_error(f"WebSocket连接失败: {ws_result['error']}")
            else:
                self.print_info(f"WebSocket测试跳过: {ws_result['reason']}")
            
            # 4. 显示测试总结
            self.print_test_summary()
            
        finally:
            await self.close_session()
    
    def print_test_summary(self):
        """打印测试总结"""
        self.print_title("测试总结")
        
        healthy_services = sum(1 for result in self.test_results if result['healthy'])
        total_services = len(self.test_results)
        
        if healthy_services == total_services:
            self.print_success(f"所有服务运行正常 ({healthy_services}/{total_services})")
        else:
            self.print_warning(f"部分服务异常 ({healthy_services}/{total_services})")
        
        print("\n📊 详细结果:")
        for result in self.test_results:
            status = "🟢 正常" if result['healthy'] else "🔴 异常"
            print(f"  {status} {result['service']}: {result['url']}")
            if result['healthy']:
                print(f"    响应时间: {result['response_time']}ms")
                if result['response']:
                    print(f"    功能: {', '.join(result['response'].get('available_endpoints', []))}")
            else:
                print(f"    错误: {result['error'] or '状态码: ' + str(result['status_code'])}")
    
    async def test_specific_service(self, service_name: str):
        """测试特定服务"""
        if service_name not in self.services:
            self.print_error(f"未知服务: {service_name}")
            return
        
        self.print_title(f"测试 {self.services[service_name].name}")
        
        await self.create_session()
        
        try:
            # 健康检查
            result = await self.test_service_health(service_name)
            if result['healthy']:
                self.print_success(f"服务健康 (响应时间: {result['response_time']}ms)")
                
                # 显示服务信息
                if result['response']:
                    print("\n📋 服务信息:")
                    print(json.dumps(result['response'], indent=2, ensure_ascii=False))
                
                # 测试端点
                endpoints_results = await self.test_service_endpoints(service_name)
                print(f"\n🔗 API端点测试结果:")
                for endpoint_result in endpoints_results:
                    status = "✅" if endpoint_result['success'] else "❌"
                    print(f"  {status} {endpoint_result['method']} {endpoint_result['endpoint']}")
            else:
                self.print_error(f"服务不健康: {result['error']}")
                
        finally:
            await self.close_session()

async def main():
    """主函数"""
    parser = argparse.ArgumentParser(description='MySuite 微服务测试工具')
    parser.add_argument('--service', '-s', choices=['main', 'chat'], 
                       help='测试特定服务')
    parser.add_argument('--quick', '-q', action='store_true',
                       help='快速测试模式')
    
    args = parser.parse_args()
    
    tester = MicroservicesTester()
    
    try:
        if args.service:
            await tester.test_specific_service(args.service)
        else:
            await tester.run_comprehensive_tests()
            
    except KeyboardInterrupt:
        tester.print_warning("\n测试被用户中断")
    except Exception as e:
        tester.print_error(f"测试过程中发生错误: {e}")
        sys.exit(1)

if __name__ == "__main__":
    # 检查依赖
    try:
        import colorama
        import websockets
        import aiohttp
    except ImportError as e:
        print(f"❌ 缺少依赖: {e}")
        print("请安装依赖: pip install colorama websockets aiohttp")
        sys.exit(1)
    
    asyncio.run(main()) 