#!/usr/bin/env python3
"""
#20250710 卡顿修复 - 测试客户端重启稳定性和异步加载功能
验证修复后的program1.py能否正确处理:
1. 异步分段加载
2. 超时和重试机制
3. 错误容错
4. 资源清理
"""
import sys
import time
import subprocess
import requests
import threading
from pathlib import Path

def test_server5_api():
    """测试Server5 API连接"""
    print("🔄 测试Server5 API连接...")
    try:
        response = requests.get("http://localhost:8009/health", timeout=5)
        if response.status_code == 200:
            print("✅ Server5 API连接正常")
            return True
        else:
            print(f"❌ Server5 API返回错误: {response.status_code}")
            return False
    except Exception as e:
        print(f"❌ Server5 API连接失败: {e}")
        return False

def test_main_server_api():
    """测试主服务器API连接"""
    print("🔄 测试主服务器API连接...")
    try:
        response = requests.get("https://localhost/health", verify=False, timeout=5)
        if response.status_code == 200:
            print("✅ 主服务器API连接正常")
            return True
        else:
            print(f"❌ 主服务器API返回错误: {response.status_code}")
            return False
    except Exception as e:
        print(f"❌ 主服务器API连接失败: {e}")
        return False

def simulate_client_restart():
    """模拟客户端重启"""
    print("\n🔄 开始模拟客户端重启测试...")
    
    # 模拟的启动参数
    test_token = "test_token_12345"
    test_employee_id = "TEST001"
    test_employee_name = "测试员工"
    
    client_dir = Path(__file__).parent / "client"
    program1_path = client_dir / "program1.py"
    
    if not program1_path.exists():
        print(f"❌ 找不到program1.py: {program1_path}")
        return False
    
    print(f"📂 客户端路径: {program1_path}")
    
    # 测试多次重启
    restart_count = 3
    for i in range(restart_count):
        print(f"\n🔄 第 {i+1}/{restart_count} 次重启测试...")
        
        try:
            # 启动客户端进程
            process = subprocess.Popen([
                sys.executable,
                str(program1_path),
                test_token,
                test_employee_id,
                test_employee_name
            ], 
            cwd=str(client_dir),
            stdout=subprocess.PIPE,
            stderr=subprocess.PIPE,
            text=True
            )
            
            print(f"✅ 客户端进程已启动 (PID: {process.pid})")
            
            # 等待客户端初始化
            print("⏱️ 等待客户端初始化 (10秒)...")
            time.sleep(10)
            
            # 检查进程是否还在运行
            if process.poll() is None:
                print("✅ 客户端进程运行正常")
                
                # 强制终止进程
                print("🔄 正在终止客户端进程...")
                process.terminate()
                
                # 等待进程终止
                try:
                    process.wait(timeout=5)
                    print("✅ 客户端进程正常终止")
                except subprocess.TimeoutExpired:
                    print("⚠️ 进程未在5秒内终止，强制杀死...")
                    process.kill()
                    process.wait()
                    print("✅ 客户端进程已强制终止")
            else:
                # 进程提前退出，检查错误
                stdout, stderr = process.communicate()
                print(f"❌ 客户端进程提前退出 (返回码: {process.returncode})")
                if stdout:
                    print(f"📤 标准输出:\n{stdout[:500]}")
                if stderr:
                    print(f"📤 标准错误:\n{stderr[:500]}")
                return False
            
            # 重启间隔
            if i < restart_count - 1:
                print("⏱️ 等待重启间隔 (3秒)...")
                time.sleep(3)
                
        except Exception as e:
            print(f"❌ 重启测试失败: {e}")
            return False
    
    print(f"\n✅ 完成 {restart_count} 次重启测试，无卡顿现象")
    return True

def test_async_loading_functionality():
    """测试异步加载功能"""
    print("\n🔄 测试异步加载功能...")
    
    # 这里我们可以测试各个API端点的响应时间
    endpoints = [
        ("Server5 timeprotab", "http://localhost:8009/api/timeprotab", {"employee_id": "TEST001", "year": 2025, "month": 1}),
        ("Server5 entries", "http://localhost:8009/api/entries", {"employee_id": "TEST001", "start_date": "2025-01-01", "end_date": "2025-01-31"}),
        ("Server5 chart months", "http://localhost:8009/api/chart/months", {"employee_id": "TEST001"}),
        ("Server5 chart generate", "http://localhost:8009/api/chart/generate", {"employee_id": "TEST001", "start_date": "2025-01-01", "end_date": "2025-01-31", "chart_type": "daily"}),
    ]
    
    for name, url, params in endpoints:
        try:
            print(f"🔄 测试 {name}...")
            start_time = time.time()
            response = requests.get(url, params=params, timeout=10)
            end_time = time.time()
            
            if response.status_code == 200:
                print(f"✅ {name} 响应正常 ({end_time - start_time:.2f}秒)")
            else:
                print(f"⚠️ {name} 返回错误: {response.status_code}")
        except Exception as e:
            print(f"❌ {name} 测试失败: {e}")
    
    return True

def test_timeout_and_retry():
    """测试超时和重试机制"""
    print("\n🔄 测试超时和重试机制...")
    
    # 测试一个不存在的端点来触发超时
    try:
        print("🔄 测试API超时处理...")
        start_time = time.time()
        response = requests.get("http://localhost:8009/api/nonexistent", timeout=2)
        end_time = time.time()
        print(f"⚠️ 意外收到响应: {response.status_code}")
    except requests.exceptions.Timeout:
        end_time = time.time()
        print(f"✅ 超时机制正常工作 ({end_time - start_time:.2f}秒)")
    except Exception as e:
        print(f"✅ 网络错误正常处理: {type(e).__name__}")
    
    return True

def test_resource_cleanup():
    """测试资源清理"""
    print("\n🔄 测试资源清理...")
    
    # 检查是否有残留的Python进程
    try:
        import psutil
        
        python_processes = []
        for proc in psutil.process_iter(['pid', 'name', 'cmdline']):
            try:
                if proc.info['name'] and 'python' in proc.info['name'].lower():
                    cmdline = proc.info.get('cmdline', [])
                    if cmdline and 'program1.py' in ' '.join(cmdline):
                        python_processes.append(proc.info)
            except (psutil.NoSuchProcess, psutil.AccessDenied):
                pass
        
        if python_processes:
            print(f"⚠️ 发现 {len(python_processes)} 个program1进程:")
            for proc in python_processes:
                print(f"  PID {proc['pid']}: {' '.join(proc.get('cmdline', []))}")
        else:
            print("✅ 没有发现残留的program1进程")
            
    except ImportError:
        print("⚠️ 无法导入psutil，跳过进程检查")
    
    return True

def main():
    """主测试函数"""
    print("=" * 60)
    print("🧪 #20250710 卡顿修复 - 客户端重启稳定性测试")
    print("=" * 60)
    
    # 测试结果收集
    test_results = {}
    
    # 1. 测试API连接
    test_results['server5_api'] = test_server5_api()
    test_results['main_server_api'] = test_main_server_api()
    
    # 2. 测试异步加载功能
    test_results['async_loading'] = test_async_loading_functionality()
    
    # 3. 测试超时和重试
    test_results['timeout_retry'] = test_timeout_and_retry()
    
    # 4. 测试客户端重启 (核心测试)
    if test_results['server5_api']:
        test_results['client_restart'] = simulate_client_restart()
    else:
        print("⚠️ 跳过客户端重启测试 (Server5 API不可用)")
        test_results['client_restart'] = None
    
    # 5. 测试资源清理
    test_results['resource_cleanup'] = test_resource_cleanup()
    
    # 输出测试总结
    print("\n" + "=" * 60)
    print("📊 测试结果总结")
    print("=" * 60)
    
    passed = 0
    total = 0
    
    for test_name, result in test_results.items():
        if result is not None:
            total += 1
            if result:
                passed += 1
                status = "✅ 通过"
            else:
                status = "❌ 失败"
        else:
            status = "⏭️ 跳过"
        
        print(f"{test_name:20} : {status}")
    
    print(f"\n🎯 测试通过率: {passed}/{total} ({100*passed/total if total > 0 else 0:.1f}%)")
    
    if passed == total and total > 0:
        print("\n🎉 所有测试通过！#20250710 卡顿修复成功")
        return True
    else:
        print("\n⚠️ 部分测试失败，需要进一步调试")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1) 