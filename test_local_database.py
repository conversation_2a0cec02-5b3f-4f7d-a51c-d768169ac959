#!/usr/bin/env python3
"""
测试本地数据库连接
验证config_local.py配置是否正确
"""

import sys
import asyncio
from pathlib import Path

def test_config_import():
    """测试配置文件导入"""
    print("🧪 测试配置文件导入...")
    
    try:
        # 添加server/app路径
        config_path = Path(__file__).parent / "server" / "app"
        sys.path.append(str(config_path))
        
        # 尝试导入本地配置
        try:
            from config_local import IMDB_DATABASE_URL, DATABASE_URL
            print("✅ 成功导入本地配置")
            print(f"   IMDB_DATABASE_URL: {IMDB_DATABASE_URL}")
            print(f"   DATABASE_URL: {DATABASE_URL}")
            return True
        except ImportError as e:
            print(f"❌ 导入本地配置失败: {e}")
            
            # 尝试导入远程配置
            try:
                from config import IMDB_DATABASE_URL, DATABASE_URL
                print("⚠️ 使用远程配置")
                print(f"   IMDB_DATABASE_URL: {IMDB_DATABASE_URL}")
                print(f"   DATABASE_URL: {DATABASE_URL}")
                return True
            except ImportError as e2:
                print(f"❌ 导入远程配置也失败: {e2}")
                return False
                
    except Exception as e:
        print(f"❌ 配置测试失败: {e}")
        return False

async def test_database_connection():
    """测试数据库连接"""
    print("\n🧪 测试数据库连接...")
    
    try:
        import asyncpg
        
        # 获取配置
        config_path = Path(__file__).parent / "server" / "app"
        sys.path.append(str(config_path))
        
        try:
            from config_local import IMDB_DATABASE_URL
            print("使用本地数据库配置")
        except ImportError:
            from config import IMDB_DATABASE_URL
            print("使用远程数据库配置")
        
        print(f"连接URL: {IMDB_DATABASE_URL}")
        
        # 尝试连接
        conn = await asyncpg.connect(IMDB_DATABASE_URL)
        
        # 测试查询
        version = await conn.fetchval("SELECT version()")
        print(f"✅ 数据库连接成功")
        print(f"   数据库版本: {version}")
        
        # 检查表是否存在
        tables = await conn.fetch("""
            SELECT table_name 
            FROM information_schema.tables 
            WHERE table_schema = 'public'
            ORDER BY table_name
        """)
        
        print(f"   可用表: {[t['table_name'] for t in tables]}")
        
        await conn.close()
        return True
        
    except Exception as e:
        print(f"❌ 数据库连接失败: {e}")
        return False

def test_postgresql_service():
    """测试PostgreSQL服务状态"""
    print("\n🧪 测试PostgreSQL服务...")
    
    try:
        import subprocess
        
        # 检查PostgreSQL服务状态
        result = subprocess.run(
            ["pg_isready", "-h", "localhost", "-p", "5432"],
            capture_output=True,
            text=True,
            timeout=10
        )
        
        if result.returncode == 0:
            print("✅ PostgreSQL服务正在运行")
            return True
        else:
            print(f"❌ PostgreSQL服务未运行: {result.stderr}")
            return False
            
    except FileNotFoundError:
        print("⚠️ pg_isready命令不可用，跳过服务检查")
        return True
    except Exception as e:
        print(f"❌ 服务检查失败: {e}")
        return False

def main():
    """主测试函数"""
    print("🔧 本地数据库配置测试")
    print("=" * 50)
    
    # 测试1: 配置文件导入
    config_ok = test_config_import()
    
    # 测试2: PostgreSQL服务
    service_ok = test_postgresql_service()
    
    # 测试3: 数据库连接
    if config_ok and service_ok:
        connection_ok = asyncio.run(test_database_connection())
    else:
        connection_ok = False
    
    # 总结
    print("\n" + "=" * 50)
    print("📊 测试结果总结:")
    print(f"   配置文件导入: {'✅ 通过' if config_ok else '❌ 失败'}")
    print(f"   PostgreSQL服务: {'✅ 通过' if service_ok else '❌ 失败'}")
    print(f"   数据库连接: {'✅ 通过' if connection_ok else '❌ 失败'}")
    
    if config_ok and service_ok and connection_ok:
        print("\n🎉 所有测试通过！本地数据库配置正确")
        return True
    else:
        print("\n⚠️ 部分测试失败，需要检查配置")
        if not config_ok:
            print("   ❌ 配置文件有问题")
        if not service_ok:
            print("   ❌ PostgreSQL服务未运行")
        if not connection_ok:
            print("   ❌ 数据库连接失败")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1) 