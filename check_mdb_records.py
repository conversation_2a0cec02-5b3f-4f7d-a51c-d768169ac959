#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
查询MDB中特定external_id的记录
连接到局域网的Win10 Server6实例
"""

import asyncio
import aiohttp
import json
from datetime import datetime

# Server6配置 - 连接到局域网的Win10实例
SERVER6_BASE_URL = "http://************:8009"  # 假设Win10的IP是************
API_KEY = "server6-mdb-gateway-key"

# 要查询的external_id列表
EXTERNAL_IDS_TO_CHECK = [
    602580,  # ID: 10010 (2025-06-09)
    602584,  # ID: 10011 (2025-06-09) 
    602586,  # ID: 10012 (2025-06-09)
    602016   # ID: 10013 (2025-06-10)
]

class MDBQueryClient:
    def __init__(self, base_url, api_key):
        self.base_url = base_url
        self.api_key = api_key
        self.session = None
    
    async def __aenter__(self):
        self.session = aiohttp.ClientSession(
            headers={"X-API-Key": self.api_key},
            timeout=aiohttp.ClientTimeout(total=30)
        )
        return self
    
    async def __aexit__(self, exc_type, exc_val, exc_tb):
        if self.session:
            await self.session.close()
    
    async def check_health(self):
        """检查Server6健康状态"""
        try:
            async with self.session.get(f"{self.base_url}/health") as response:
                if response.status == 200:
                    data = await response.json()
                    return data
                else:
                    print(f"❌ 健康检查失败: {response.status}")
                    return None
        except Exception as e:
            print(f"❌ 连接Server6失败: {e}")
            return None
    
    async def query_by_external_id(self, external_id):
        """根据external_id查询记录"""
        try:
            url = f"{self.base_url}/mdb/entries/external/{external_id}"
            async with self.session.get(url) as response:
                if response.status == 200:
                    data = await response.json()
                    return data
                elif response.status == 404:
                    return None  # 记录不存在
                else:
                    print(f"❌ 查询external_id {external_id} 失败: {response.status}")
                    return None
        except Exception as e:
            print(f"❌ 查询external_id {external_id} 异常: {e}")
            return None

async def main():
    print("🔍 查询MDB中的特定记录")
    print("=" * 50)
    print(f"📡 连接到Server6: {SERVER6_BASE_URL}")
    print(f"🔑 API Key: {API_KEY}")
    print()
    
    async with MDBQueryClient(SERVER6_BASE_URL, API_KEY) as client:
        # 1. 检查Server6健康状态
        print("🏥 检查Server6健康状态...")
        health = await client.check_health()
        if health:
            print(f"✅ Server6状态: {health.get('status', 'unknown')}")
            print(f"📊 MDB可用: {health.get('mdb_available', False)}")
            print(f"⏱️  运行时间: {health.get('uptime', 0):.1f}秒")
        else:
            print("❌ 无法连接到Server6")
            return
        print()
        
        # 2. 查询每个external_id
        print("🔍 查询指定记录...")
        print("-" * 50)
        
        found_records = []
        not_found_records = []
        
        for external_id in EXTERNAL_IDS_TO_CHECK:
            print(f"🔎 查询 external_id: {external_id}")
            record = await client.query_by_external_id(external_id)
            
            if record:
                print(f"✅ 找到记录:")
                print(f"   ID: {record.get('ID', 'N/A')}")
                print(f"   员工ID: {record.get('员工ID', 'N/A')}")
                print(f"   日期: {record.get('日期', 'N/A')}")
                print(f"   类别: {record.get('类别', 'N/A')}")
                print(f"   项目: {record.get('项目', 'N/A')}")
                print(f"   时长: {record.get('时长', 'N/A')}")
                print(f"   部门: {record.get('部门', 'N/A')}")
                found_records.append((external_id, record))
            else:
                print(f"❌ 未找到记录")
                not_found_records.append(external_id)
            print()
        
        # 3. 汇总结果
        print("📊 查询结果汇总")
        print("=" * 50)
        print(f"✅ 找到的记录: {len(found_records)} 条")
        for external_id, record in found_records:
            print(f"   external_id: {external_id} -> ID: {record.get('ID', 'N/A')} (日期: {record.get('日期', 'N/A')})")
        
        print(f"❌ 未找到的记录: {len(not_found_records)} 条")
        for external_id in not_found_records:
            print(f"   external_id: {external_id}")
        
        print()
        print("🎯 结论:")
        if found_records:
            print("⚠️  这些记录仍然存在于MDB中，需要手动删除")
        else:
            print("✅ 所有记录都已从MDB中删除")

if __name__ == "__main__":
    asyncio.run(main()) 