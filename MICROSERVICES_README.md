# MySuite 微服务启动和测试指南

## 概述

本项目包含两个主要微服务：
- **主服务** (端口 8003): 核心业务逻辑和数据库操作
- **聊天微服务** (端口 8005): 实时聊天和信息交流功能

## 快速启动

### 1. 使用启动脚本 (推荐)

```bash
# 启动所有微服务
./start_microservices.sh

# 或者指定命令
./start_microservices.sh start
```

### 2. 手动启动

#### 启动主服务
```bash
cd server
python -m uvicorn app.main:app --host 0.0.0.0 --port 8003 --reload
```

#### 启动聊天微服务
```bash
cd server2
python start_chat_service.py
# 或者
python -m uvicorn app.main:app --host 0.0.0.0 --port 8005 --reload
```

## 管理命令

### 启动脚本命令

```bash
./start_microservices.sh start     # 启动所有微服务
./start_microservices.sh stop      # 停止所有微服务
./start_microservices.sh restart   # 重启所有微服务
./start_microservices.sh status    # 查看服务状态
./start_microservices.sh test      # 运行服务测试
./start_microservices.sh logs      # 查看服务日志
./start_microservices.sh help      # 显示帮助信息
```

### 服务测试

#### 使用Python测试脚本
```bash
# 安装测试依赖
pip install -r test_requirements.txt

# 运行综合测试
python test_microservices.py

# 测试特定服务
python test_microservices.py --service main
python test_microservices.py --service chat
```

#### 使用curl测试
```bash
# 测试主服务
curl http://localhost:8003/
curl http://localhost:8003/health

# 测试聊天微服务
curl http://localhost:8005/
curl http://localhost:8005/health
```

## 服务信息

### 主服务 (端口 8003)
- **服务地址**: http://localhost:8003
- **API文档**: http://localhost:8003/docs
- **健康检查**: http://localhost:8003/health
- **主要功能**: 
  - 数据库操作
  - 文件上传处理
  - 任务管理
  - 用户认证
  - ODBC连接

### 聊天微服务 (端口 8005)
- **服务地址**: http://localhost:8005
- **API文档**: http://localhost:8005/docs
- **健康检查**: http://localhost:8005/health
- **WebSocket**: ws://localhost:8005/ws/chat
- **主要功能**:
  - 实时聊天
  - 私人消息
  - 聊天室管理
  - 文件共享
  - 消息加密

## 依赖要求

### 主服务依赖
- Python 3.12
- FastAPI
- SQLAlchemy + AsyncPG (PostgreSQL)
- Redis
- 其他依赖见 `server/environment.yml`

### 聊天微服务依赖
- Python 3.8+
- FastAPI
- Redis
- WebSocket支持
- 其他依赖见 `server2/requirements.txt`

## 环境配置

### 数据库配置
```bash
# PostgreSQL (主服务)
DB_HOST=localhost
DB_PORT=5432
DB_NAME=my_suite_db
DB_USER=your_user
DB_PASSWORD=your_password

# Redis (两个服务都需要)
REDIS_HOST=localhost
REDIS_PORT=6379
```

### 服务端口配置
- 主服务: 8003
- 聊天微服务: 8005
- PostgreSQL: 5432
- Redis: 6379

## 日志和监控

### 日志文件位置
- 主服务日志: `logs/main_service.log`
- 聊天服务日志: `logs/chat_service.log`

### 查看日志
```bash
# 实时查看主服务日志
tail -f logs/main_service.log

# 实时查看聊天服务日志
tail -f logs/chat_service.log

# 使用启动脚本查看日志
./start_microservices.sh logs
```

## 故障排除

### 常见问题

1. **端口被占用**
   ```bash
   # 查看端口占用
   lsof -i :8003
   lsof -i :8005
   
   # 停止占用端口的进程
   pkill -f "uvicorn.*main:app"
   ```

2. **依赖缺失**
   ```bash
   # 主服务
   cd server
   conda env create -f environment.yml
   conda activate my_suite_server
   
   # 聊天微服务
   cd server2
   pip install -r requirements.txt
   ```

3. **数据库连接失败**
   - 检查PostgreSQL是否运行
   - 验证数据库配置
   - 检查防火墙设置

4. **Redis连接失败**
   - 检查Redis服务状态
   - 验证Redis配置
   - 检查网络连接

### 调试模式

启用详细日志输出：
```bash
# 主服务调试模式
cd server
python -m uvicorn app.main:app --host 0.0.0.0 --port 8003 --reload --log-level debug

# 聊天微服务调试模式
cd server2
python -m uvicorn app.main:app --host 0.0.0.0 --port 8005 --reload --log-level debug
```

## 开发指南

### 添加新的API端点

1. **主服务**: 在 `server/app/routers/` 目录下添加新的路由文件
2. **聊天微服务**: 在 `server2/app/routers/` 目录下添加新的路由文件

### 数据库迁移

```bash
cd server
alembic revision --autogenerate -m "描述变更"
alembic upgrade head
```

### 测试新功能

1. 更新 `test_microservices.py` 中的测试端点
2. 运行测试验证功能正常

## 部署建议

### 生产环境部署

1. **使用进程管理器**
   ```bash
   # 使用systemd或supervisor管理服务
   # 或使用Docker容器化部署
   ```

2. **反向代理配置**
   ```nginx
   # Nginx配置示例
   upstream main_service {
       server localhost:8003;
   }
   
   upstream chat_service {
       server localhost:8005;
   }
   ```

3. **监控和日志**
   - 使用ELK stack或类似工具收集日志
   - 设置健康检查和告警

### 安全考虑

- 使用HTTPS/WSS协议
- 配置防火墙规则
- 启用认证和授权
- 定期更新依赖包

## 支持

如有问题，请检查：
1. 服务日志文件
2. 运行测试脚本诊断
3. 查看API文档了解接口详情

---

**最后更新**: $(date)
**版本**: 1.0.0 