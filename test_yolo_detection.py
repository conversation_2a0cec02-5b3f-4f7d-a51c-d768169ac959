# test_yolo_detection.py
# 20250619.18:30 YOLO AI物体检测测试脚本
"""
MySuite YOLO AI物体检测功能测试脚本
测试视频监控系统的AI物体检测功能
"""

import requests
import time
import json
from typing import Dict, Any

def test_yolo_api():
    """测试YOLO API端点"""
    base_url = "http://localhost:8007/api/video"
    
    print("🤖 MySuite YOLO AI物体检测测试")
    print("=" * 50)
    
    # 1. 检查视频服务状态
    print("\n1. 检查视频服务状态...")
    try:
        response = requests.get(f"{base_url}/health", timeout=5)
        if response.status_code == 200:
            print("✅ 视频服务运行正常")
        else:
            print(f"❌ 视频服务状态异常: {response.status_code}")
            return False
    except Exception as e:
        print(f"❌ 无法连接视频服务: {e}")
        return False
    
    # 2. 获取YOLO状态
    print("\n2. 获取YOLO检测状态...")
    try:
        response = requests.get(f"{base_url}/yolo/status", timeout=5)
        if response.status_code == 200:
            data = response.json()
            if data.get("success"):
                yolo_data = data["data"]
                print(f"   YOLO可用: {yolo_data['yolo_available']}")
                print(f"   模型已加载: {yolo_data['model_loaded']}")
                print(f"   检测启用: {yolo_data['is_enabled']}")
                print(f"   视频YOLO启用: {yolo_data['video_yolo_enabled']}")
                print(f"   总检测次数: {yolo_data['total_detections']}")
                print(f"   当前检测物体数: {yolo_data['objects_detected']}")
                
                if not yolo_data['yolo_available']:
                    print("❌ YOLO依赖不可用，请安装ultralytics和torch")
                    return False
            else:
                print(f"❌ 获取YOLO状态失败: {data}")
                return False
        else:
            print(f"❌ YOLO状态请求失败: {response.status_code}")
            return False
    except Exception as e:
        print(f"❌ 获取YOLO状态错误: {e}")
        return False
    
    # 3. 初始化YOLO模型（如果未加载）
    if not yolo_data['model_loaded']:
        print("\n3. 初始化YOLO模型...")
        try:
            print("   正在下载和加载YOLO模型，请稍候...")
            response = requests.post(f"{base_url}/yolo/initialize", timeout=60)
            if response.status_code == 200:
                data = response.json()
                if data.get("success"):
                    print("✅ YOLO模型初始化成功")
                    model_info = data.get("model_info", {})
                    if model_info:
                        print(f"   模型路径: {model_info.get('model_path', 'N/A')}")
                        print(f"   设备: {model_info.get('device', 'N/A')}")
                        print(f"   模型类型: {model_info.get('model_type', 'N/A')}")
                else:
                    print(f"❌ YOLO模型初始化失败: {data.get('error', '未知错误')}")
                    return False
            else:
                print(f"❌ 初始化请求失败: {response.status_code}")
                return False
        except Exception as e:
            print(f"❌ 初始化YOLO模型错误: {e}")
            return False
    else:
        print("\n3. YOLO模型已加载 ✅")
    
    # 4. 启用YOLO检测
    if not yolo_data['video_yolo_enabled']:
        print("\n4. 启用YOLO检测...")
        try:
            response = requests.post(f"{base_url}/yolo/enable", timeout=10)
            if response.status_code == 200:
                data = response.json()
                if data.get("success"):
                    print("✅ YOLO检测已启用")
                else:
                    print(f"❌ 启用YOLO检测失败: {data.get('error', '未知错误')}")
                    return False
            else:
                print(f"❌ 启用请求失败: {response.status_code}")
                return False
        except Exception as e:
            print(f"❌ 启用YOLO检测错误: {e}")
            return False
    else:
        print("\n4. YOLO检测已启用 ✅")
    
    # 5. 监控检测结果
    print("\n5. 监控YOLO检测结果...")
    print("   (监控30秒，查看检测统计)")
    
    for i in range(6):  # 监控30秒，每5秒查询一次
        try:
            time.sleep(5)
            response = requests.get(f"{base_url}/yolo/status", timeout=5)
            if response.status_code == 200:
                data = response.json()
                if data.get("success"):
                    yolo_data = data["data"]
                    objects_detected = yolo_data['objects_detected']
                    total_detections = yolo_data['total_detections']
                    detection_classes = yolo_data.get('detection_classes', {})
                    last_detections = yolo_data.get('last_detections', [])
                    
                    print(f"   [{i+1}/6] 当前物体: {objects_detected}, 总检测: {total_detections}")
                    
                    if detection_classes:
                        class_summary = []
                        for class_name, count in list(detection_classes.items())[:3]:
                            class_summary.append(f"{class_name}({count})")
                        print(f"         检测类别: {', '.join(class_summary)}")
                    
                    if last_detections:
                        print(f"         最新检测: {len(last_detections)}个物体")
                        for detection in last_detections[:2]:  # 显示前2个
                            class_name = detection.get('class_name', 'unknown')
                            confidence = detection.get('confidence', 0)
                            print(f"           - {class_name}: {confidence:.2f}")
                else:
                    print(f"   [{i+1}/6] 获取状态失败")
            else:
                print(f"   [{i+1}/6] 请求失败: {response.status_code}")
        except Exception as e:
            print(f"   [{i+1}/6] 监控错误: {e}")
    
    # 6. 获取视频快照（带检测框）
    print("\n6. 获取带检测框的视频快照...")
    try:
        response = requests.get(f"{base_url}/snapshot", timeout=10)
        if response.status_code == 200:
            data = response.json()
            if data.get("success"):
                frame_data = data["data"]["image"]
                frame_size = len(frame_data) // 1024  # KB
                print(f"✅ 快照获取成功，大小: {frame_size}KB")
                print("   (快照包含YOLO检测框，可在客户端查看)")
            else:
                print(f"❌ 快照获取失败: {data.get('error', '未知错误')}")
        else:
            print(f"❌ 快照请求失败: {response.status_code}")
    except Exception as e:
        print(f"❌ 获取快照错误: {e}")
    
    print("\n" + "=" * 50)
    print("🎉 YOLO AI物体检测测试完成！")
    print("\n使用说明:")
    print("1. 启动客户端: python client/client_fixed.py")
    print("2. 登录后点击第3个标签页 '视频监控'")
    print("3. 点击 '连接视频' 按钮")
    print("4. 点击 '初始化YOLO' 按钮加载AI模型")
    print("5. 点击 '启用AI检测' 按钮开始物体检测")
    print("6. 观察视频画面中的检测框和底部统计信息")
    print("\n支持检测的物体类别:")
    print("人员、车辆、动物、家具、电子设备等80+种物体")
    
    return True

def test_yolo_disable():
    """测试禁用YOLO检测"""
    base_url = "http://localhost:8007/api/video"
    
    print("\n🛑 测试禁用YOLO检测...")
    try:
        response = requests.post(f"{base_url}/yolo/disable", timeout=5)
        if response.status_code == 200:
            data = response.json()
            if data.get("success"):
                print("✅ YOLO检测已禁用")
                return True
            else:
                print(f"❌ 禁用失败: {data.get('error', '未知错误')}")
                return False
        else:
            print(f"❌ 禁用请求失败: {response.status_code}")
            return False
    except Exception as e:
        print(f"❌ 禁用YOLO检测错误: {e}")
        return False

if __name__ == "__main__":
    success = test_yolo_api()
    
    if success:
        # 询问是否禁用YOLO检测
        user_input = input("\n是否禁用YOLO检测? (y/N): ").strip().lower()
        if user_input in ['y', 'yes']:
            test_yolo_disable()
    
    print("\n测试完成！") 