#!/bin/bash

# 20250619.14:00 统一环境 - MySuite 微服务快速测试脚本
# 用于在统一conda环境中快速测试两个微服务

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
CYAN='\033[0;36m'
NC='\033[0m' # No Color

# 打印带颜色的消息
print_message() {
    local color=$1
    local message=$2
    echo -e "${color}${message}${NC}"
}

# 打印标题
print_title() {
    echo
    print_message $CYAN "========================================"
    print_message $CYAN "$1"
    print_message $CYAN "========================================"
    echo
}

# 激活统一环境
activate_unified_env() {
    print_message $YELLOW "🔄 激活统一conda环境..."
    
    if command -v conda >/dev/null 2>&1; then
        source "$(conda info --base)/etc/profile.d/conda.sh"
        
        if conda env list | grep -q "my_suite_unified"; then
            conda activate my_suite_unified
            print_message $GREEN "✅ 统一环境已激活: my_suite_unified"
        else
            print_message $YELLOW "⚠️  统一环境不存在，正在创建..."
            conda env create -f environment_unified.yml
            conda activate my_suite_unified
            print_message $GREEN "✅ 统一环境创建并激活成功"
        fi
    else
        print_message $RED "❌ 未找到conda命令"
        exit 1
    fi
}

# 测试微服务1
test_service1() {
    print_title "测试微服务1 (主服务)"
    
    cd server
    
    print_message $YELLOW "🧪 测试导入..."
    if python -c "from app.core.services.redis_service import redis_service; print('Redis服务导入成功')" 2>/dev/null; then
        print_message $GREEN "✅ 微服务1导入测试通过"
    else
        print_message $RED "❌ 微服务1导入测试失败"
    fi
    
    print_message $YELLOW "🧪 测试配置..."
    if python -c "from app.config import REDIS_HOST, REDIS_PORT; print(f'Redis配置: {REDIS_HOST}:{REDIS_PORT}')" 2>/dev/null; then
        print_message $GREEN "✅ 微服务1配置测试通过"
    else
        print_message $RED "❌ 微服务1配置测试失败"
    fi
    
    cd ..
}

# 测试微服务2
test_service2() {
    print_title "测试微服务2 (聊天服务)"
    
    cd server2
    
    print_message $YELLOW "🧪 测试导入..."
    if python -c "from app.config import settings; print('配置导入成功')" 2>/dev/null; then
        print_message $GREEN "✅ 微服务2导入测试通过"
    else
        print_message $RED "❌ 微服务2导入测试失败"
    fi
    
    print_message $YELLOW "🧪 测试服务模块..."
    if python -c "from app.core.services.database_service import database_service; print('数据库服务导入成功')" 2>/dev/null; then
        print_message $GREEN "✅ 微服务2服务模块测试通过"
    else
        print_message $RED "❌ 微服务2服务模块测试失败"
    fi
    
    cd ..
}

# 快速启动测试
quick_start_test() {
    print_title "快速启动测试"
    
    print_message $YELLOW "🚀 测试微服务1启动..."
    cd server
    timeout 10s python -c "
import asyncio
from app.main import app
print('✅ 微服务1应用创建成功')
" 2>/dev/null && print_message $GREEN "✅ 微服务1启动测试通过" || print_message $RED "❌ 微服务1启动测试失败"
    cd ..
    
    print_message $YELLOW "🚀 测试微服务2启动..."
    cd server2
    timeout 10s python -c "
import asyncio
from app.main import app
print('✅ 微服务2应用创建成功')
" 2>/dev/null && print_message $GREEN "✅ 微服务2启动测试通过" || print_message $RED "❌ 微服务2启动测试失败"
    cd ..
}

# 显示服务状态
show_service_status() {
    print_title "服务状态检查"
    
    if lsof -Pi :8003 -sTCP:LISTEN -t >/dev/null 2>&1; then
        print_message $GREEN "✅ 微服务1 (端口8003) 正在运行"
        curl -s http://localhost:8003/health | python -m json.tool 2>/dev/null || echo "健康检查响应获取失败"
    else
        print_message $YELLOW "⚠️  微服务1 (端口8003) 未运行"
    fi
    
    echo
    
    if lsof -Pi :8005 -sTCP:LISTEN -t >/dev/null 2>&1; then
        print_message $GREEN "✅ 微服务2 (端口8005) 正在运行"
        curl -s http://localhost:8005/health | python -m json.tool 2>/dev/null || echo "健康检查响应获取失败"
    else
        print_message $YELLOW "⚠️  微服务2 (端口8005) 未运行"
    fi
}

# 显示帮助信息
show_help() {
    print_title "MySuite 微服务快速测试工具"
    echo "使用方法: $0 [选项]"
    echo
    echo "选项:"
    echo "  test          - 运行完整测试套件"
    echo "  service1      - 仅测试微服务1"
    echo "  service2      - 仅测试微服务2"
    echo "  start-test    - 快速启动测试"
    echo "  status        - 显示服务状态"
    echo "  env           - 激活统一环境"
    echo "  help          - 显示此帮助信息"
    echo
    echo "示例:"
    echo "  $0 test       # 运行所有测试"
    echo "  $0 status     # 检查服务状态"
    echo "  $0 env        # 激活环境并进入交互shell"
}

# 主函数
main() {
    case "${1:-test}" in
        "test")
            activate_unified_env
            test_service1
            test_service2
            quick_start_test
            show_service_status
            ;;
        "service1")
            activate_unified_env
            test_service1
            ;;
        "service2")
            activate_unified_env
            test_service2
            ;;
        "start-test")
            activate_unified_env
            quick_start_test
            ;;
        "status")
            show_service_status
            ;;
        "env")
            activate_unified_env
            print_message $GREEN "🎉 统一环境已激活，您现在可以测试两个微服务了！"
            print_message $BLUE "💡 提示："
            echo "   - 进入server目录测试微服务1: cd server && python -m app.main"
            echo "   - 进入server2目录测试微服务2: cd server2 && python start_chat_service.py"
            echo "   - 使用启动脚本: ./start_microservices.sh start"
            exec bash
            ;;
        "help"|"-h"|"--help")
            show_help
            ;;
        *)
            print_message $RED "❌ 未知选项: $1"
            show_help
            exit 1
            ;;
    esac
}

# 运行主函数
main "$@" 