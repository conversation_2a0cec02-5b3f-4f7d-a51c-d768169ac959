# server6/config/config.py
# Server6 MDB网关配置
# 专门处理MDB数据库访问的微服务

import os
import platform
from pathlib import Path

# 基础配置
SERVER_NAME = "MySuite Server6 - MDB Gateway"
VERSION = "1.0.0"
DEBUG = os.getenv("DEBUG", "false").lower() == "true"

# 服务配置
HOST = os.getenv("SERVER6_HOST", "0.0.0.0")
PORT = int(os.getenv("SERVER6_PORT", "8019"))  # 使用8019端口避免与Server5冲突

# 平台检测
PLATFORM = platform.system()
IS_WINDOWS = PLATFORM == "Windows"

# MDB数据库配置
if IS_WINDOWS:
    # Windows环境 - 真实MDB连接
    MDB_FILE_PATH = os.getenv("MDB_FILE_PATH", r"D:\actest25\6.mdb")
    MDB_CONNECTION_STRING = f"DRIVER={{Microsoft Access Driver (*.mdb, *.accdb)}};DBQ={MDB_FILE_PATH};"
    MDB_AVAILABLE = True
else:
    # 非Windows环境 - 模拟模式
    MDB_FILE_PATH = "/tmp/mock_6.mdb"
    MDB_CONNECTION_STRING = "MOCK_CONNECTION"
    MDB_AVAILABLE = False

# ODBC配置
ODBC_TIMEOUT = 30
ODBC_MAX_RETRIES = 3
ODBC_RETRY_DELAY = 1  # 秒

# API配置
API_TITLE = "MySuite Server6 - MDB Gateway API"
API_DESCRIPTION = """
MDB数据库网关微服务

专门处理Microsoft Access数据库的CRUD操作，为Server5提供MDB访问能力。

特性:
- Windows原生MDB支持
- 非Windows环境模拟模式
- RESTful API接口
- 连接池管理
- 错误重试机制
"""

# 日志配置
LOG_LEVEL = os.getenv("LOG_LEVEL", "INFO")
LOG_FORMAT = "%(asctime)s - %(name)s - %(levelname)s - %(message)s"
LOG_FILE = "logs/server6.log"

# 性能配置
MAX_CONNECTIONS = 5  # MDB连接池大小
CONNECTION_TIMEOUT = 30  # 连接超时
QUERY_TIMEOUT = 60  # 查询超时

# 缓存配置
ENABLE_CACHE = True
CACHE_TTL = 300  # 5分钟缓存

# 安全配置
ALLOWED_HOSTS = ["*"]  # 生产环境应该限制
API_KEY_HEADER = "X-API-Key"
API_KEY = os.getenv("SERVER6_API_KEY", "server6-mdb-gateway-key")

# 监控配置
HEALTH_CHECK_INTERVAL = 30  # 秒
METRICS_ENABLED = True

# 数据同步配置
BATCH_SIZE = 1000  # 批量操作大小
MAX_RECORDS_PER_REQUEST = 5000  # 单次请求最大记录数

# 错误处理配置
MAX_ERROR_RETRIES = 3
ERROR_RETRY_DELAY = 2  # 秒
ENABLE_CIRCUIT_BREAKER = True

# 开发配置
if DEBUG:
    LOG_LEVEL = "DEBUG"
    ODBC_TIMEOUT = 10
    MAX_CONNECTIONS = 2

# 配置验证
def validate_config():
    """验证配置的有效性"""
    errors = []
    
    if IS_WINDOWS and not os.path.exists(MDB_FILE_PATH):
        errors.append(f"MDB文件不存在: {MDB_FILE_PATH}")
    
    if PORT < 1024 or PORT > 65535:
        errors.append(f"无效的端口号: {PORT}")
    
    if MAX_CONNECTIONS < 1:
        errors.append("连接池大小必须大于0")
    
    return errors

# 获取配置摘要
def get_config_summary():
    """获取配置摘要用于启动时显示"""
    return {
        "server": SERVER_NAME,
        "version": VERSION,
        "platform": PLATFORM,
        "host": HOST,
        "port": PORT,
        "mdb_available": MDB_AVAILABLE,
        "mdb_path": MDB_FILE_PATH,
        "debug": DEBUG,
        "log_level": LOG_LEVEL
    }

# 导出配置
__all__ = [
    "SERVER_NAME", "VERSION", "DEBUG", "HOST", "PORT",
    "PLATFORM", "IS_WINDOWS", "MDB_FILE_PATH", "MDB_CONNECTION_STRING", "MDB_AVAILABLE",
    "ODBC_TIMEOUT", "ODBC_MAX_RETRIES", "ODBC_RETRY_DELAY",
    "API_TITLE", "API_DESCRIPTION", "LOG_LEVEL", "LOG_FORMAT", "LOG_FILE",
    "MAX_CONNECTIONS", "CONNECTION_TIMEOUT", "QUERY_TIMEOUT",
    "ENABLE_CACHE", "CACHE_TTL", "ALLOWED_HOSTS", "API_KEY_HEADER", "API_KEY",
    "HEALTH_CHECK_INTERVAL", "METRICS_ENABLED", "BATCH_SIZE", "MAX_RECORDS_PER_REQUEST",
    "MAX_ERROR_RETRIES", "ERROR_RETRY_DELAY", "ENABLE_CIRCUIT_BREAKER",
    "validate_config", "get_config_summary"
] 