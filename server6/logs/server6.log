2025-06-26 16:41:11,583 - app.utils.logger - INFO - ???????? - ??: INFO, ??: logs/server6.log
2025-06-26 16:41:11,600 - app.main - INFO - ============================================================
2025-06-26 16:41:11,601 - app.main - INFO - Server6 MDB?????...
2025-06-26 16:41:11,601 - app.main - INFO - ????:
2025-06-26 16:41:11,601 - app.main - INFO -   server: MySuite Server6 - MDB Gateway
2025-06-26 16:41:11,601 - app.main - INFO -   version: 1.0.0
2025-06-26 16:41:11,601 - app.main - INFO -   platform: Linux
2025-06-26 16:41:11,601 - app.main - INFO -   host: 127.0.0.1
2025-06-26 16:41:11,601 - app.main - INFO -   port: 8009
2025-06-26 16:41:11,601 - app.main - INFO -   mdb_available: False
2025-06-26 16:41:11,601 - app.main - INFO -   mdb_path: /tmp/mock_6.mdb
2025-06-26 16:41:11,601 - app.main - INFO -   debug: False
2025-06-26 16:41:11,601 - app.main - INFO -   log_level: INFO
2025-06-26 16:41:11,601 - app.main - INFO - Server6 MDB??????
2025-06-26 16:41:11,601 - app.main - INFO - ============================================================
2025-06-26 16:41:21,420 - app.main - INFO - Server6 MDB??????...
2025-06-26 16:41:21,420 - app.main - INFO - Server6 MDB?????
2025-06-26 16:41:59,847 - app.utils.logger - INFO - ???????? - ??: INFO, ??: logs/server6.log
2025-06-26 16:41:59,864 - app.main - INFO - ============================================================
2025-06-26 16:41:59,864 - app.main - INFO - Server6 MDB?????...
2025-06-26 16:41:59,864 - app.main - INFO - ????:
2025-06-26 16:41:59,864 - app.main - INFO -   server: MySuite Server6 - MDB Gateway
2025-06-26 16:41:59,864 - app.main - INFO -   version: 1.0.0
2025-06-26 16:41:59,864 - app.main - INFO -   platform: Linux
2025-06-26 16:41:59,864 - app.main - INFO -   host: 127.0.0.1
2025-06-26 16:41:59,864 - app.main - INFO -   port: 8009
2025-06-26 16:41:59,864 - app.main - INFO -   mdb_available: False
2025-06-26 16:41:59,864 - app.main - INFO -   mdb_path: /tmp/mock_6.mdb
2025-06-26 16:41:59,864 - app.main - INFO -   debug: False
2025-06-26 16:41:59,864 - app.main - INFO -   log_level: INFO
2025-06-26 16:41:59,864 - app.main - INFO - Server6 MDB??????
2025-06-26 16:41:59,864 - app.main - INFO - ============================================================
2025-06-26 16:42:04,566 - app.routers.mdb_api - INFO - ????: SELECT id, name FROM [employees] TOP 5
2025-06-26 16:42:04,675 - app.main - INFO - Server6 MDB??????...
2025-06-26 16:42:04,675 - app.main - INFO - Server6 MDB?????
2025-06-27 14:11:28,270 - app.main - INFO - ============================================================
2025-06-27 14:11:28,303 - app.main - INFO - ?z?u?E?v:
2025-06-27 14:11:28,304 - app.main - INFO -   server: MySuite Server6 - MDB Gateway
2025-06-27 14:11:28,304 - app.main - INFO -   version: 1.0.0
2025-06-27 14:11:28,305 - app.main - INFO -   platform: Windows
2025-06-27 14:11:28,305 - app.main - INFO -   host: 127.0.0.1
2025-06-27 14:11:28,306 - app.main - INFO -   port: 8009
2025-06-27 14:11:28,306 - app.main - INFO -   mdb_available: True
2025-06-27 14:11:28,306 - app.main - INFO -   mdb_path: D:\actest25\6.mdb
2025-06-27 14:11:28,307 - app.main - INFO -   debug: False
2025-06-27 14:11:28,307 - app.main - INFO -   log_level: INFO
2025-06-27 14:11:28,335 - app.main - INFO - ============================================================
2025-06-27 16:42:06,089 - app.main - INFO - ============================================================
2025-06-27 16:42:06,116 - app.main - INFO - ?z?u?E?v:
2025-06-27 16:42:06,116 - app.main - INFO -   server: MySuite Server6 - MDB Gateway
2025-06-27 16:42:06,116 - app.main - INFO -   version: 1.0.0
2025-06-27 16:42:06,116 - app.main - INFO -   platform: Windows
2025-06-27 16:42:06,117 - app.main - INFO -   host: 0.0.0.0
2025-06-27 16:42:06,117 - app.main - INFO -   port: 8009
2025-06-27 16:42:06,117 - app.main - INFO -   mdb_available: True
2025-06-27 16:42:06,117 - app.main - INFO -   mdb_path: D:\actest25\6.mdb
2025-06-27 16:42:06,117 - app.main - INFO -   debug: False
2025-06-27 16:42:06,118 - app.main - INFO -   log_level: INFO
2025-06-27 16:42:06,131 - app.main - INFO - ============================================================
2025-06-27 16:42:19,598 - app.core.mdb_client - INFO - Executing distinct employee ID query: SELECT DISTINCT [?]???????] FROM [????????]
2025-06-27 16:58:33,321 - app.core.mdb_client - INFO - Executing distinct employee ID query: SELECT DISTINCT [?]???????] FROM [????????]
2025-06-27 17:00:58,972 - app.core.mdb_client - INFO - Executing distinct employee ID query: SELECT DISTINCT [?]???????] FROM [????????]
2025-06-27 17:04:08,343 - app.main - INFO - ============================================================
2025-06-27 17:04:08,366 - app.main - INFO - ?z?u?E?v:
2025-06-27 17:04:08,366 - app.main - INFO -   server: MySuite Server6 - MDB Gateway
2025-06-27 17:04:08,366 - app.main - INFO -   version: 1.0.0
2025-06-27 17:04:08,366 - app.main - INFO -   platform: Windows
2025-06-27 17:04:08,367 - app.main - INFO -   host: 0.0.0.0
2025-06-27 17:04:08,367 - app.main - INFO -   port: 8009
2025-06-27 17:04:08,367 - app.main - INFO -   mdb_available: True
2025-06-27 17:04:08,367 - app.main - INFO -   mdb_path: D:\actest25\6.mdb
2025-06-27 17:04:08,367 - app.main - INFO -   debug: False
2025-06-27 17:04:08,368 - app.main - INFO -   log_level: INFO
2025-06-27 17:04:08,377 - app.main - INFO - ============================================================
2025-06-27 17:04:18,145 - app.core.mdb_client - INFO - Executing distinct employee ID query: SELECT DISTINCT [?]???????] FROM [????????]
2025-06-27 17:10:00,101 - app.main - INFO - ============================================================
2025-06-27 17:10:00,122 - app.main - INFO - ?z?u?E?v:
2025-06-27 17:10:00,123 - app.main - INFO -   server: MySuite Server6 - MDB Gateway
2025-06-27 17:10:00,123 - app.main - INFO -   version: 1.0.0
2025-06-27 17:10:00,123 - app.main - INFO -   platform: Windows
2025-06-27 17:10:00,124 - app.main - INFO -   host: 0.0.0.0
2025-06-27 17:10:00,124 - app.main - INFO -   port: 8009
2025-06-27 17:10:00,124 - app.main - INFO -   mdb_available: True
2025-06-27 17:10:00,125 - app.main - INFO -   mdb_path: D:\actest25\6.mdb
2025-06-27 17:10:00,125 - app.main - INFO -   debug: False
2025-06-27 17:10:00,125 - app.main - INFO -   log_level: INFO
2025-06-27 17:10:00,133 - app.main - INFO - ============================================================
2025-06-27 17:10:08,051 - app.core.mdb_client - INFO - Executing distinct employee ID query: SELECT DISTINCT [?]???????] FROM [????????]
2025-06-27 17:12:19,502 - app.main - INFO - ============================================================
2025-06-27 17:12:19,526 - app.main - INFO - ?z?u?E?v:
2025-06-27 17:12:19,526 - app.main - INFO -   server: MySuite Server6 - MDB Gateway
2025-06-27 17:12:19,526 - app.main - INFO -   version: 1.0.0
2025-06-27 17:12:19,527 - app.main - INFO -   platform: Windows
2025-06-27 17:12:19,527 - app.main - INFO -   host: 0.0.0.0
2025-06-27 17:12:19,527 - app.main - INFO -   port: 8009
2025-06-27 17:12:19,527 - app.main - INFO -   mdb_available: True
2025-06-27 17:12:19,528 - app.main - INFO -   mdb_path: D:\actest25\6.mdb
2025-06-27 17:12:19,528 - app.main - INFO -   debug: False
2025-06-27 17:12:19,528 - app.main - INFO -   log_level: INFO
2025-06-27 17:12:19,536 - app.main - INFO - ============================================================
2025-06-27 17:12:28,698 - app.core.mdb_client - INFO - Executing distinct employee ID query: SELECT DISTINCT [?]???????] FROM [????????]
2025-06-27 17:16:12,773 - app.core.mdb_client - INFO - Executing distinct employee ID query: SELECT DISTINCT [?]???????] FROM [????????]
2025-06-27 17:18:53,340 - app.main - INFO - ============================================================
2025-06-27 17:18:53,371 - app.main - INFO - ?z?u?E?v:
2025-06-27 17:18:53,371 - app.main - INFO -   server: MySuite Server6 - MDB Gateway
2025-06-27 17:18:53,372 - app.main - INFO -   version: 1.0.0
2025-06-27 17:18:53,372 - app.main - INFO -   platform: Windows
2025-06-27 17:18:53,373 - app.main - INFO -   host: 0.0.0.0
2025-06-27 17:18:53,374 - app.main - INFO -   port: 8009
2025-06-27 17:18:53,375 - app.main - INFO -   mdb_available: True
2025-06-27 17:18:53,375 - app.main - INFO -   mdb_path: D:\actest25\6.mdb
2025-06-27 17:18:53,376 - app.main - INFO -   debug: False
2025-06-27 17:18:53,376 - app.main - INFO -   log_level: INFO
2025-06-27 17:18:53,392 - app.main - INFO - ============================================================
2025-06-27 17:19:17,077 - app.core.mdb_client - INFO - Executing distinct employee ID query: SELECT DISTINCT [?]???????] FROM [????????]
2025-06-27 17:23:38,617 - app.main - INFO - ============================================================
2025-06-27 17:23:38,657 - app.main - INFO - ?z?u?E?v:
2025-06-27 17:23:38,657 - app.main - INFO -   server: MySuite Server6 - MDB Gateway
2025-06-27 17:23:38,658 - app.main - INFO -   version: 1.0.0
2025-06-27 17:23:38,658 - app.main - INFO -   platform: Windows
2025-06-27 17:23:38,658 - app.main - INFO -   host: 0.0.0.0
2025-06-27 17:23:38,658 - app.main - INFO -   port: 8009
2025-06-27 17:23:38,659 - app.main - INFO -   mdb_available: True
2025-06-27 17:23:38,660 - app.main - INFO -   mdb_path: D:\actest25\6.mdb
2025-06-27 17:23:38,660 - app.main - INFO -   debug: False
2025-06-27 17:23:38,660 - app.main - INFO -   log_level: INFO
2025-06-27 17:23:38,674 - app.main - INFO - ============================================================
2025-06-27 17:23:48,961 - app.core.mdb_client - INFO - Executing distinct employee ID query: SELECT DISTINCT [?]???????] FROM [????????]
2025-06-27 17:24:07,314 - app.core.mdb_client - INFO - Executing fetch query: SELECT ID, ?]???????, ???t, ?@??, ???@, ?H????, ?H?????, ?????, ??, ????, ????, ???????? FROM [????????] WHERE [?]???????] = '102407' AND [???t] >= #2025/06/25# AND [???t] <= #2025/06/25#
2025-06-27 17:24:11,369 - app.core.mdb_client - INFO - Executing fetch query: SELECT ID, ?]???????, ???t, ?@??, ???@, ?H????, ?H?????, ?????, ??, ????, ????, ???????? FROM [????????] WHERE [?]???????] = '190586' AND [???t] >= #2025/06/25# AND [???t] <= #2025/06/25#
2025-06-27 17:24:15,483 - app.core.mdb_client - INFO - Executing fetch query: SELECT ID, ?]???????, ???t, ?@??, ???@, ?H????, ?H?????, ?????, ??, ????, ????, ???????? FROM [????????] WHERE [?]???????] = '190640' AND [???t] >= #2025/06/25# AND [???t] <= #2025/06/25#
2025-06-27 17:24:19,522 - app.core.mdb_client - INFO - Executing fetch query: SELECT ID, ?]???????, ???t, ?@??, ???@, ?H????, ?H?????, ?????, ??, ????, ????, ???????? FROM [????????] WHERE [?]???????] = '190950' AND [???t] >= #2025/06/25# AND [???t] <= #2025/06/25#
2025-06-27 17:24:23,572 - app.core.mdb_client - INFO - Executing fetch query: SELECT ID, ?]???????, ???t, ?@??, ???@, ?H????, ?H?????, ?????, ??, ????, ????, ???????? FROM [????????] WHERE [?]???????] = '191027' AND [???t] >= #2025/06/25# AND [???t] <= #2025/06/25#
2025-06-27 17:24:27,665 - app.core.mdb_client - INFO - Executing fetch query: SELECT ID, ?]???????, ???t, ?@??, ???@, ?H????, ?H?????, ?????, ??, ????, ????, ???????? FROM [????????] WHERE [?]???????] = '191133' AND [???t] >= #2025/06/25# AND [???t] <= #2025/06/25#
2025-06-27 17:24:31,694 - app.core.mdb_client - INFO - Executing fetch query: SELECT ID, ?]???????, ???t, ?@??, ???@, ?H????, ?H?????, ?????, ??, ????, ????, ???????? FROM [????????] WHERE [?]???????] = '191138' AND [???t] >= #2025/06/25# AND [???t] <= #2025/06/25#
2025-06-27 17:24:35,839 - app.core.mdb_client - INFO - Executing fetch query: SELECT ID, ?]???????, ???t, ?@??, ???@, ?H????, ?H?????, ?????, ??, ????, ????, ???????? FROM [????????] WHERE [?]???????] = '191146' AND [???t] >= #2025/06/25# AND [???t] <= #2025/06/25#
2025-06-27 17:24:40,339 - app.core.mdb_client - INFO - Executing fetch query: SELECT ID, ?]???????, ???t, ?@??, ???@, ?H????, ?H?????, ?????, ??, ????, ????, ???????? FROM [????????] WHERE [?]???????] = '205281' AND [???t] >= #2025/06/25# AND [???t] <= #2025/06/25#
2025-06-27 17:24:44,364 - app.core.mdb_client - INFO - Executing fetch query: SELECT ID, ?]???????, ???t, ?@??, ???@, ?H????, ?H?????, ?????, ??, ????, ????, ???????? FROM [????????] WHERE [?]???????] = '208043' AND [???t] >= #2025/06/25# AND [???t] <= #2025/06/25#
2025-06-27 17:24:48,392 - app.core.mdb_client - INFO - Executing fetch query: SELECT ID, ?]???????, ???t, ?@??, ???@, ?H????, ?H?????, ?????, ??, ????, ????, ???????? FROM [????????] WHERE [?]???????] = '209228' AND [???t] >= #2025/06/25# AND [???t] <= #2025/06/25#
2025-06-27 17:24:52,528 - app.core.mdb_client - INFO - Executing fetch query: SELECT ID, ?]???????, ???t, ?@??, ???@, ?H????, ?H?????, ?????, ??, ????, ????, ???????? FROM [????????] WHERE [?]???????] = '209600' AND [???t] >= #2025/06/25# AND [???t] <= #2025/06/25#
2025-06-27 17:24:56,664 - app.core.mdb_client - INFO - Executing fetch query: SELECT ID, ?]???????, ???t, ?@??, ???@, ?H????, ?H?????, ?????, ??, ????, ????, ???????? FROM [????????] WHERE [?]???????] = '209724' AND [???t] >= #2025/06/25# AND [???t] <= #2025/06/25#
2025-06-27 17:25:00,807 - app.core.mdb_client - INFO - Executing fetch query: SELECT ID, ?]???????, ???t, ?@??, ???@, ?H????, ?H?????, ?????, ??, ????, ????, ???????? FROM [????????] WHERE [?]???????] = '209783' AND [???t] >= #2025/06/25# AND [???t] <= #2025/06/25#
2025-06-27 17:25:04,890 - app.core.mdb_client - INFO - Executing fetch query: SELECT ID, ?]???????, ???t, ?@??, ???@, ?H????, ?H?????, ?????, ??, ????, ????, ???????? FROM [????????] WHERE [?]???????] = '210013' AND [???t] >= #2025/06/25# AND [???t] <= #2025/06/25#
2025-06-27 17:25:11,362 - app.core.mdb_client - INFO - Executing fetch query: SELECT ID, ?]???????, ???t, ?@??, ???@, ?H????, ?H?????, ?????, ??, ????, ????, ???????? FROM [????????] WHERE [?]???????] = '210315' AND [???t] >= #2025/06/25# AND [???t] <= #2025/06/25#
2025-06-27 17:25:15,190 - app.core.mdb_client - INFO - Executing fetch query: SELECT ID, ?]???????, ???t, ?@??, ???@, ?H????, ?H?????, ?????, ??, ????, ????, ???????? FROM [????????] WHERE [?]???????] = '210730' AND [???t] >= #2025/06/25# AND [???t] <= #2025/06/25#
2025-06-27 17:25:19,286 - app.core.mdb_client - INFO - Executing fetch query: SELECT ID, ?]???????, ???t, ?@??, ???@, ?H????, ?H?????, ?????, ??, ????, ????, ???????? FROM [????????] WHERE [?]???????] = '210854' AND [???t] >= #2025/06/25# AND [???t] <= #2025/06/25#
2025-06-27 17:25:23,530 - app.core.mdb_client - INFO - Executing fetch query: SELECT ID, ?]???????, ???t, ?@??, ???@, ?H????, ?H?????, ?????, ??, ????, ????, ???????? FROM [????????] WHERE [?]???????] = '211141' AND [???t] >= #2025/06/25# AND [???t] <= #2025/06/25#
2025-06-27 17:25:27,373 - app.core.mdb_client - INFO - Executing fetch query: SELECT ID, ?]???????, ???t, ?@??, ???@, ?H????, ?H?????, ?????, ??, ????, ????, ???????? FROM [????????] WHERE [?]???????] = '211818' AND [???t] >= #2025/06/25# AND [???t] <= #2025/06/25#
2025-06-27 17:25:31,559 - app.core.mdb_client - INFO - Executing fetch query: SELECT ID, ?]???????, ???t, ?@??, ???@, ?H????, ?H?????, ?????, ??, ????, ????, ???????? FROM [????????] WHERE [?]???????] = '212180' AND [???t] >= #2025/06/25# AND [???t] <= #2025/06/25#
2025-06-27 17:25:35,698 - app.core.mdb_client - INFO - Executing fetch query: SELECT ID, ?]???????, ???t, ?@??, ???@, ?H????, ?H?????, ?????, ??, ????, ????, ???????? FROM [????????] WHERE [?]???????] = '212202' AND [???t] >= #2025/06/25# AND [???t] <= #2025/06/25#
2025-06-27 17:25:39,658 - app.core.mdb_client - INFO - Executing fetch query: SELECT ID, ?]???????, ???t, ?@??, ???@, ?H????, ?H?????, ?????, ??, ????, ????, ???????? FROM [????????] WHERE [?]???????] = '212245' AND [???t] >= #2025/06/25# AND [???t] <= #2025/06/25#
2025-06-27 17:25:43,964 - app.core.mdb_client - INFO - Executing fetch query: SELECT ID, ?]???????, ???t, ?@??, ???@, ?H????, ?H?????, ?????, ??, ????, ????, ???????? FROM [????????] WHERE [?]???????] = '212393' AND [???t] >= #2025/06/25# AND [???t] <= #2025/06/25#
2025-06-27 17:25:48,150 - app.core.mdb_client - INFO - Executing fetch query: SELECT ID, ?]???????, ???t, ?@??, ???@, ?H????, ?H?????, ?????, ??, ????, ????, ???????? FROM [????????] WHERE [?]???????] = '212512' AND [???t] >= #2025/06/25# AND [???t] <= #2025/06/25#
2025-06-27 17:25:52,221 - app.core.mdb_client - INFO - Executing fetch query: SELECT ID, ?]???????, ???t, ?@??, ???@, ?H????, ?H?????, ?????, ??, ????, ????, ???????? FROM [????????] WHERE [?]???????] = '212687' AND [???t] >= #2025/06/25# AND [???t] <= #2025/06/25#
2025-06-27 17:25:56,316 - app.core.mdb_client - INFO - Executing fetch query: SELECT ID, ?]???????, ???t, ?@??, ???@, ?H????, ?H?????, ?????, ??, ????, ????, ???????? FROM [????????] WHERE [?]???????] = '213047' AND [???t] >= #2025/06/25# AND [???t] <= #2025/06/25#
2025-06-27 17:26:00,588 - app.core.mdb_client - INFO - Executing fetch query: SELECT ID, ?]???????, ???t, ?@??, ???@, ?H????, ?H?????, ?????, ??, ????, ????, ???????? FROM [????????] WHERE [?]???????] = '213195' AND [???t] >= #2025/06/25# AND [???t] <= #2025/06/25#
2025-06-27 17:26:04,648 - app.core.mdb_client - INFO - Executing fetch query: SELECT ID, ?]???????, ???t, ?@??, ???@, ?H????, ?H?????, ?????, ??, ????, ????, ???????? FROM [????????] WHERE [?]???????] = '213217' AND [???t] >= #2025/06/25# AND [???t] <= #2025/06/25#
2025-06-27 17:26:08,690 - app.core.mdb_client - INFO - Executing fetch query: SELECT ID, ?]???????, ???t, ?@??, ???@, ?H????, ?H?????, ?????, ??, ????, ????, ???????? FROM [????????] WHERE [?]???????] = '213284' AND [???t] >= #2025/06/25# AND [???t] <= #2025/06/25#
2025-06-27 17:26:15,035 - app.core.mdb_client - INFO - Executing fetch query: SELECT ID, ?]???????, ???t, ?@??, ???@, ?H????, ?H?????, ?????, ??, ????, ????, ???????? FROM [????????] WHERE [?]???????] = '213357' AND [???t] >= #2025/06/25# AND [???t] <= #2025/06/25#
2025-06-27 17:26:19,046 - app.core.mdb_client - INFO - Executing fetch query: SELECT ID, ?]???????, ???t, ?@??, ???@, ?H????, ?H?????, ?????, ??, ????, ????, ???????? FROM [????????] WHERE [?]???????] = '213497' AND [???t] >= #2025/06/25# AND [???t] <= #2025/06/25#
2025-06-27 17:26:23,126 - app.core.mdb_client - INFO - Executing fetch query: SELECT ID, ?]???????, ???t, ?@??, ???@, ?H????, ?H?????, ?????, ??, ????, ????, ???????? FROM [????????] WHERE [?]???????] = '213500' AND [???t] >= #2025/06/25# AND [???t] <= #2025/06/25#
2025-06-27 17:26:27,173 - app.core.mdb_client - INFO - Executing fetch query: SELECT ID, ?]???????, ???t, ?@??, ???@, ?H????, ?H?????, ?????, ??, ????, ????, ???????? FROM [????????] WHERE [?]???????] = '213543' AND [???t] >= #2025/06/25# AND [???t] <= #2025/06/25#
2025-06-27 17:31:50,177 - app.main - INFO - ============================================================
2025-06-27 17:31:50,202 - app.main - INFO - ?z?u?E?v:
2025-06-27 17:31:50,202 - app.main - INFO -   server: MySuite Server6 - MDB Gateway
2025-06-27 17:31:50,202 - app.main - INFO -   version: 1.0.0
2025-06-27 17:31:50,202 - app.main - INFO -   platform: Windows
2025-06-27 17:31:50,203 - app.main - INFO -   host: 0.0.0.0
2025-06-27 17:31:50,203 - app.main - INFO -   port: 8009
2025-06-27 17:31:50,203 - app.main - INFO -   mdb_available: True
2025-06-27 17:31:50,204 - app.main - INFO -   mdb_path: D:\actest25\6.mdb
2025-06-27 17:31:50,204 - app.main - INFO -   debug: False
2025-06-27 17:31:50,205 - app.main - INFO -   log_level: INFO
2025-06-27 17:31:50,214 - app.main - INFO - ============================================================
2025-06-27 17:34:12,600 - app.core.mdb_client - INFO - Executing fetch query: SELECT ID, ?]???????, ???t, ?@??, ???@, ?H????, ?H?????, ?????, ??, ????, ????, ???????? FROM [????????] WHERE [???t] >= #2025/06/25# AND [???t] <= #2025/06/25#
2025-06-27 17:39:40,443 - app.main - INFO - ============================================================
2025-06-27 17:39:40,464 - app.main - INFO - ?z?u?E?v:
2025-06-27 17:39:40,464 - app.main - INFO -   server: MySuite Server6 - MDB Gateway
2025-06-27 17:39:40,464 - app.main - INFO -   version: 1.0.0
2025-06-27 17:39:40,465 - app.main - INFO -   platform: Windows
2025-06-27 17:39:40,465 - app.main - INFO -   host: 0.0.0.0
2025-06-27 17:39:40,465 - app.main - INFO -   port: 8009
2025-06-27 17:39:40,465 - app.main - INFO -   mdb_available: True
2025-06-27 17:39:40,465 - app.main - INFO -   mdb_path: D:\actest25\6.mdb
2025-06-27 17:39:40,466 - app.main - INFO -   debug: False
2025-06-27 17:39:40,466 - app.main - INFO -   log_level: INFO
2025-06-27 17:39:40,473 - app.main - INFO - ============================================================
2025-06-27 17:39:50,005 - app.core.mdb_client - INFO - Executing fetch query: SELECT ID, ?]???????, ???t, ?@??, ???@, ?H????, ?H?????, ?????, ??, ????, ????, ???????? FROM [????????] WHERE [???t] >= #2025/06/25# AND [???t] <= #2025/06/25#
2025-06-27 17:44:23,611 - app.main - INFO - ============================================================
2025-06-27 17:44:23,638 - app.main - INFO - ?z?u?E?v:
2025-06-27 17:44:23,638 - app.main - INFO -   server: MySuite Server6 - MDB Gateway
2025-06-27 17:44:23,639 - app.main - INFO -   version: 1.0.0
2025-06-27 17:44:23,639 - app.main - INFO -   platform: Windows
2025-06-27 17:44:23,640 - app.main - INFO -   host: 0.0.0.0
2025-06-27 17:44:23,640 - app.main - INFO -   port: 8009
2025-06-27 17:44:23,641 - app.main - INFO -   mdb_available: True
2025-06-27 17:44:23,641 - app.main - INFO -   mdb_path: D:\actest25\6.mdb
2025-06-27 17:44:23,641 - app.main - INFO -   debug: False
2025-06-27 17:44:23,641 - app.main - INFO -   log_level: INFO
2025-06-27 17:44:23,651 - app.main - INFO - ============================================================
2025-06-27 17:44:28,083 - app.core.mdb_client - INFO - Executing fetch query: SELECT ID, ?]???????, ???t, ?@??, ???@, ?H????, ?H?????, ?????, ??, ????, ????, ???????? FROM [????????] WHERE [???t] >= #2025/06/25# AND [???t] <= #2025/06/25#
2025-06-27 17:48:11,190 - app.core.mdb_client - INFO - Executing fetch query: SELECT ID, ?]???????, ???t, ?@??, ???@, ?H????, ?H?????, ?????, ??, ????, ????, ???????? FROM [????????] WHERE [???t] >= #2025/06/25# AND [???t] <= #2025/06/25#
2025-06-27 17:49:59,630 - app.core.mdb_client - INFO - Executing fetch query: SELECT ID, ?]???????, ???t, ?@??, ???@, ?H????, ?H?????, ?????, ??, ????, ????, ???????? FROM [????????] WHERE [???t] >= #2025/06/25# AND [???t] <= #2025/06/25#
2025-06-27 17:50:49,722 - app.core.mdb_client - INFO - Executing fetch query: SELECT ID, ?]???????, ???t, ?@??, ???@, ?H????, ?H?????, ?????, ??, ????, ????, ???????? FROM [????????] WHERE [???t] >= #2025/06/25# AND [???t] <= #2025/06/25#
2025-06-27 18:00:22,229 - app.core.mdb_client - INFO - Executing fetch query: SELECT ID, ?]???????, ???t, ?@??, ???@, ?H????, ?H?????, ?????, ??, ????, ????, ???????? FROM [????????] WHERE [?]???????] = '215753' AND [???t] >= #2025/05/28# AND [???t] <= #2025/06/27#
2025-06-27 18:00:41,835 - app.core.mdb_client - INFO - Executing fetch query: SELECT ID, ?]???????, ???t, ?@??, ???@, ?H????, ?H?????, ?????, ??, ????, ????, ???????? FROM [????????] WHERE [?]???????] = '953563' AND [???t] >= #2025/05/28# AND [???t] <= #2025/06/27#
2025-06-27 18:00:49,277 - app.core.mdb_client - INFO - Executing fetch query: SELECT ID, ?]???????, ???t, ?@??, ???@, ?H????, ?H?????, ?????, ??, ????, ????, ???????? FROM [????????] WHERE [?]???????] = '272051' AND [???t] >= #2025/05/28# AND [???t] <= #2025/06/27#
2025-06-27 18:00:57,027 - app.core.mdb_client - INFO - Executing fetch query: SELECT ID, ?]???????, ???t, ?@??, ???@, ?H????, ?H?????, ?????, ??, ????, ????, ???????? FROM [????????] WHERE [?]???????] = '215112' AND [???t] >= #2025/05/28# AND [???t] <= #2025/06/27#
2025-06-27 18:01:08,267 - app.core.mdb_client - INFO - Executing fetch query: SELECT ID, ?]???????, ???t, ?@??, ???@, ?H????, ?H?????, ?????, ??, ????, ????, ???????? FROM [????????] WHERE [?]???????] = '215236' AND [???t] >= #2025/05/28# AND [???t] <= #2025/06/27#
2025-06-27 18:01:18,095 - app.core.mdb_client - INFO - Executing fetch query: SELECT ID, ?]???????, ???t, ?@??, ???@, ?H????, ?H?????, ?????, ??, ????, ????, ???????? FROM [????????] WHERE [?]???????] = '215384' AND [???t] >= #2025/05/28# AND [???t] <= #2025/06/27#
2025-06-27 18:01:26,244 - app.core.mdb_client - INFO - Executing fetch query: SELECT ID, ?]???????, ???t, ?@??, ???@, ?H????, ?H?????, ?????, ??, ????, ????, ???????? FROM [????????] WHERE [?]???????] = '215795' AND [???t] >= #2025/05/28# AND [???t] <= #2025/06/27#
2025-06-27 18:01:43,906 - app.core.mdb_client - INFO - Executing fetch query: SELECT ID, ?]???????, ???t, ?@??, ???@, ?H????, ?H?????, ?????, ??, ????, ????, ???????? FROM [????????] WHERE [?]???????] = '953393' AND [???t] >= #2025/05/28# AND [???t] <= #2025/06/27#
2025-06-27 18:01:50,229 - app.core.mdb_client - INFO - Executing fetch query: SELECT ID, ?]???????, ???t, ?@??, ???@, ?H????, ?H?????, ?????, ??, ????, ????, ???????? FROM [????????] WHERE [?]???????] = '215771' AND [???t] >= #2025/05/28# AND [???t] <= #2025/06/27#
2025-06-27 18:01:59,126 - app.core.mdb_client - INFO - Executing fetch query: SELECT ID, ?]???????, ???t, ?@??, ???@, ?H????, ?H?????, ?????, ??, ????, ????, ???????? FROM [????????] WHERE [?]???????] = '215867' AND [???t] >= #2025/05/28# AND [???t] <= #2025/06/27#
2025-06-27 18:02:09,748 - app.core.mdb_client - INFO - Executing fetch query: SELECT ID, ?]???????, ???t, ?@??, ???@, ?H????, ?H?????, ?????, ??, ????, ????, ???????? FROM [????????] WHERE [?]???????] = '215791' AND [???t] >= #2025/05/28# AND [???t] <= #2025/06/27#
2025-06-27 18:02:17,143 - app.core.mdb_client - INFO - Executing fetch query: SELECT ID, ?]???????, ???t, ?@??, ???@, ?H????, ?H?????, ?????, ??, ????, ????, ???????? FROM [????????] WHERE [?]???????] = '215546' AND [???t] >= #2025/05/28# AND [???t] <= #2025/06/27#
2025-06-27 18:02:24,174 - app.core.mdb_client - INFO - Executing fetch query: SELECT ID, ?]???????, ???t, ?@??, ???@, ?H????, ?H?????, ?????, ??, ????, ????, ???????? FROM [????????] WHERE [?]???????] = '215829' AND [???t] >= #2025/05/28# AND [???t] <= #2025/06/27#
2025-06-27 18:02:29,715 - app.core.mdb_client - INFO - Executing fetch query: SELECT ID, ?]???????, ???t, ?@??, ???@, ?H????, ?H?????, ?????, ??, ????, ????, ???????? FROM [????????] WHERE [?]???????] = '215735' AND [???t] >= #2025/05/28# AND [???t] <= #2025/06/27#
2025-06-27 18:02:38,153 - app.core.mdb_client - INFO - Executing fetch query: SELECT ID, ?]???????, ???t, ?@??, ???@, ?H????, ?H?????, ?????, ??, ????, ????, ???????? FROM [????????] WHERE [?]???????] = '215503' AND [???t] >= #2025/05/28# AND [???t] <= #2025/06/27#
2025-06-27 18:02:50,619 - app.core.mdb_client - INFO - Executing fetch query: SELECT ID, ?]???????, ???t, ?@??, ???@, ?H????, ?H?????, ?????, ??, ????, ????, ???????? FROM [????????] WHERE [?]???????] = '272108' AND [???t] >= #2025/05/28# AND [???t] <= #2025/06/27#
2025-06-27 18:02:58,547 - app.core.mdb_client - INFO - Executing fetch query: SELECT ID, ?]???????, ???t, ?@??, ???@, ?H????, ?H?????, ?????, ??, ????, ????, ???????? FROM [????????] WHERE [?]???????] = '215858' AND [???t] >= #2025/05/28# AND [???t] <= #2025/06/27#
2025-06-27 18:03:08,147 - app.core.mdb_client - INFO - Executing fetch query: SELECT ID, ?]???????, ???t, ?@??, ???@, ?H????, ?H?????, ?????, ??, ????, ????, ???????? FROM [????????] WHERE [?]???????] = '953326' AND [???t] >= #2025/05/28# AND [???t] <= #2025/06/27#
2025-06-27 18:03:14,770 - app.core.mdb_client - INFO - Executing fetch query: SELECT ID, ?]???????, ???t, ?@??, ???@, ?H????, ?H?????, ?????, ??, ????, ????, ???????? FROM [????????] WHERE [?]???????] = '213950' AND [???t] >= #2025/05/28# AND [???t] <= #2025/06/27#
2025-06-27 18:14:48,499 - app.main - INFO - ============================================================
2025-06-27 18:14:48,606 - app.main - INFO - ?z?u?E?v:
2025-06-27 18:14:48,606 - app.main - INFO -   server: MySuite Server6 - MDB Gateway
2025-06-27 18:14:48,606 - app.main - INFO -   version: 1.0.0
2025-06-27 18:14:48,606 - app.main - INFO -   platform: Windows
2025-06-27 18:14:48,607 - app.main - INFO -   host: 0.0.0.0
2025-06-27 18:14:48,607 - app.main - INFO -   port: 8009
2025-06-27 18:14:48,607 - app.main - INFO -   mdb_available: True
2025-06-27 18:14:48,608 - app.main - INFO -   mdb_path: D:\actest25\6.mdb
2025-06-27 18:14:48,609 - app.main - INFO -   debug: False
2025-06-27 18:14:48,609 - app.main - INFO -   log_level: INFO
2025-06-27 18:14:48,621 - app.main - INFO - ============================================================
2025-06-27 18:15:24,366 - app.core.mdb_client - INFO - Executing fetch query: SELECT ID, ?]???????, ???t, ?@??, ???@, ?H????, ?H?????, ?????, ??, ????, ????, ???????? FROM [????????] WHERE [?]???????] = '215753' AND [???t] >= #2025/05/28# AND [???t] <= #2025/06/27#
2025-06-27 18:16:05,959 - app.core.mdb_client - INFO - Executing fetch query: SELECT ID, ?]???????, ???t, ?@??, ???@, ?H????, ?H?????, ?????, ??, ????, ????, ???????? FROM [????????] WHERE [?]???????] = '953563' AND [???t] >= #2025/05/28# AND [???t] <= #2025/06/27#
2025-06-27 18:16:13,219 - app.core.mdb_client - INFO - Executing fetch query: SELECT ID, ?]???????, ???t, ?@??, ???@, ?H????, ?H?????, ?????, ??, ????, ????, ???????? FROM [????????] WHERE [?]???????] = '272051' AND [???t] >= #2025/05/28# AND [???t] <= #2025/06/27#
2025-06-27 18:16:20,055 - app.core.mdb_client - INFO - Executing fetch query: SELECT ID, ?]???????, ???t, ?@??, ???@, ?H????, ?H?????, ?????, ??, ????, ????, ???????? FROM [????????] WHERE [?]???????] = '215112' AND [???t] >= #2025/05/28# AND [???t] <= #2025/06/27#
2025-06-27 18:16:29,883 - app.core.mdb_client - INFO - Executing fetch query: SELECT ID, ?]???????, ???t, ?@??, ???@, ?H????, ?H?????, ?????, ??, ????, ????, ???????? FROM [????????] WHERE [?]???????] = '215236' AND [???t] >= #2025/05/28# AND [???t] <= #2025/06/27#
2025-06-27 18:16:37,989 - app.core.mdb_client - INFO - Executing fetch query: SELECT ID, ?]???????, ???t, ?@??, ???@, ?H????, ?H?????, ?????, ??, ????, ????, ???????? FROM [????????] WHERE [?]???????] = '215795' AND [???t] >= #2025/05/28# AND [???t] <= #2025/06/27#
2025-06-27 18:16:51,713 - app.core.mdb_client - INFO - Executing fetch query: SELECT ID, ?]???????, ???t, ?@??, ???@, ?H????, ?H?????, ?????, ??, ????, ????, ???????? FROM [????????] WHERE [?]???????] = '215384' AND [???t] >= #2025/05/28# AND [???t] <= #2025/06/27#
2025-06-27 18:16:59,314 - app.core.mdb_client - INFO - Executing fetch query: SELECT ID, ?]???????, ???t, ?@??, ???@, ?H????, ?H?????, ?????, ??, ????, ????, ???????? FROM [????????] WHERE [?]???????] = '953393' AND [???t] >= #2025/05/28# AND [???t] <= #2025/06/27#
2025-06-27 18:17:08,581 - app.core.mdb_client - INFO - Executing fetch query: SELECT ID, ?]???????, ???t, ?@??, ???@, ?H????, ?H?????, ?????, ??, ????, ????, ???????? FROM [????????] WHERE [?]???????] = '215771' AND [???t] >= #2025/05/28# AND [???t] <= #2025/06/27#
2025-06-27 18:17:18,314 - app.core.mdb_client - INFO - Executing fetch query: SELECT ID, ?]???????, ???t, ?@??, ???@, ?H????, ?H?????, ?????, ??, ????, ????, ???????? FROM [????????] WHERE [?]???????] = '215867' AND [???t] >= #2025/05/28# AND [???t] <= #2025/06/27#
2025-06-27 18:17:29,223 - app.core.mdb_client - INFO - Executing fetch query: SELECT ID, ?]???????, ???t, ?@??, ???@, ?H????, ?H?????, ?????, ??, ????, ????, ???????? FROM [????????] WHERE [?]???????] = '215791' AND [???t] >= #2025/05/28# AND [???t] <= #2025/06/27#
2025-06-27 18:17:36,721 - app.core.mdb_client - INFO - Executing fetch query: SELECT ID, ?]???????, ???t, ?@??, ???@, ?H????, ?H?????, ?????, ??, ????, ????, ???????? FROM [????????] WHERE [?]???????] = '215829' AND [???t] >= #2025/05/28# AND [???t] <= #2025/06/27#
2025-06-27 18:17:42,509 - app.core.mdb_client - INFO - Executing fetch query: SELECT ID, ?]???????, ???t, ?@??, ???@, ?H????, ?H?????, ?????, ??, ????, ????, ???????? FROM [????????] WHERE [?]???????] = '215546' AND [???t] >= #2025/05/28# AND [???t] <= #2025/06/27#
2025-06-27 18:17:55,996 - app.main - INFO - ============================================================
2025-06-27 18:17:56,097 - app.main - INFO - ?z?u?E?v:
2025-06-27 18:17:56,097 - app.main - INFO -   server: MySuite Server6 - MDB Gateway
2025-06-27 18:17:56,098 - app.main - INFO -   version: 1.0.0
2025-06-27 18:17:56,098 - app.main - INFO -   platform: Windows
2025-06-27 18:17:56,098 - app.main - INFO -   host: 0.0.0.0
2025-06-27 18:17:56,099 - app.main - INFO -   port: 8009
2025-06-27 18:17:56,099 - app.main - INFO -   mdb_available: True
2025-06-27 18:17:56,099 - app.main - INFO -   mdb_path: D:\actest25\6.mdb
2025-06-27 18:17:56,099 - app.main - INFO -   debug: False
2025-06-27 18:17:56,100 - app.main - INFO -   log_level: INFO
2025-06-27 18:17:56,108 - app.main - INFO - ============================================================
2025-06-27 18:20:19,593 - app.core.mdb_client - INFO - Executing fetch query: SELECT ID, ?]???????, ???t, ?@??, ???@, ?H????, ?H?????, ?????, ??, ????, ????, ???????? FROM [????????] WHERE [?]???????] = '215753' AND [???t] >= #2025/03/29# AND [???t] <= #2025/06/27#
2025-06-27 18:20:48,756 - app.core.mdb_client - INFO - Executing fetch query: SELECT ID, ?]???????, ???t, ?@??, ???@, ?H????, ?H?????, ?????, ??, ????, ????, ???????? FROM [????????] WHERE [?]???????] = '953563' AND [???t] >= #2025/03/29# AND [???t] <= #2025/06/27#
2025-06-27 18:21:01,566 - app.core.mdb_client - INFO - Executing fetch query: SELECT ID, ?]???????, ???t, ?@??, ???@, ?H????, ?H?????, ?????, ??, ????, ????, ???????? FROM [????????] WHERE [?]???????] = '272051' AND [???t] >= #2025/03/29# AND [???t] <= #2025/06/27#
2025-06-27 18:21:12,153 - app.core.mdb_client - INFO - Executing fetch query: SELECT ID, ?]???????, ???t, ?@??, ???@, ?H????, ?H?????, ?????, ??, ????, ????, ???????? FROM [????????] WHERE [?]???????] = '215112' AND [???t] >= #2025/03/29# AND [???t] <= #2025/06/27#
2025-06-27 18:21:32,781 - app.core.mdb_client - INFO - Executing fetch query: SELECT ID, ?]???????, ???t, ?@??, ???@, ?H????, ?H?????, ?????, ??, ????, ????, ???????? FROM [????????] WHERE [?]???????] = '215236' AND [???t] >= #2025/03/29# AND [???t] <= #2025/06/27#
2025-06-27 18:21:47,864 - app.main - INFO - ============================================================
2025-06-27 18:21:47,950 - app.main - INFO - ?z?u?E?v:
2025-06-27 18:21:47,950 - app.main - INFO -   server: MySuite Server6 - MDB Gateway
2025-06-27 18:21:47,950 - app.main - INFO -   version: 1.0.0
2025-06-27 18:21:47,950 - app.main - INFO -   platform: Windows
2025-06-27 18:21:47,951 - app.main - INFO -   host: 0.0.0.0
2025-06-27 18:21:47,951 - app.main - INFO -   port: 8009
2025-06-27 18:21:47,952 - app.main - INFO -   mdb_available: True
2025-06-27 18:21:47,952 - app.main - INFO -   mdb_path: D:\actest25\6.mdb
2025-06-27 18:21:47,953 - app.main - INFO -   debug: False
2025-06-27 18:21:47,953 - app.main - INFO -   log_level: INFO
2025-06-27 18:21:47,961 - app.main - INFO - ============================================================
2025-06-27 18:23:55,983 - app.core.mdb_client - INFO - Executing fetch query: SELECT ID, ?]???????, ???t, ?@??, ???@, ?H????, ?H?????, ?????, ??, ????, ????, ???????? FROM [????????] WHERE [???t] >= #2025/03/29# AND [???t] <= #2025/06/27#
2025-06-27 18:26:43,971 - app.main - INFO - ============================================================
2025-06-27 18:26:43,994 - app.main - INFO - ?z?u?E?v:
2025-06-27 18:26:43,995 - app.main - INFO -   server: MySuite Server6 - MDB Gateway
2025-06-27 18:26:43,995 - app.main - INFO -   version: 1.0.0
2025-06-27 18:26:43,995 - app.main - INFO -   platform: Windows
2025-06-27 18:26:43,996 - app.main - INFO -   host: 0.0.0.0
2025-06-27 18:26:43,996 - app.main - INFO -   port: 8009
2025-06-27 18:26:43,996 - app.main - INFO -   mdb_available: True
2025-06-27 18:26:43,996 - app.main - INFO -   mdb_path: D:\actest25\6.mdb
2025-06-27 18:26:43,996 - app.main - INFO -   debug: False
2025-06-27 18:26:43,997 - app.main - INFO -   log_level: INFO
2025-06-27 18:26:44,004 - app.main - INFO - ============================================================
2025-06-27 18:27:56,056 - app.core.mdb_client - INFO - Executing fetch query: SELECT ID, ?]???????, ???t, ?@??, ???@, ?H????, ?H?????, ?????, ??, ????, ????, ???????? FROM [????????] WHERE [???t] >= #2025/04/04# AND [???t] <= #2025/04/09#
2025-06-27 18:28:21,861 - app.core.mdb_client - INFO - Executing fetch query: SELECT ID, ?]???????, ???t, ?@??, ???@, ?H????, ?H?????, ?????, ??, ????, ????, ???????? FROM [????????] WHERE [???t] >= #2025/04/10# AND [???t] <= #2025/04/15#
2025-06-27 18:28:48,353 - app.core.mdb_client - INFO - Executing fetch query: SELECT ID, ?]???????, ???t, ?@??, ???@, ?H????, ?H?????, ?????, ??, ????, ????, ???????? FROM [????????] WHERE [???t] >= #2025/04/16# AND [???t] <= #2025/04/21#
2025-06-27 18:29:15,616 - app.core.mdb_client - INFO - Executing fetch query: SELECT ID, ?]???????, ???t, ?@??, ???@, ?H????, ?H?????, ?????, ??, ????, ????, ???????? FROM [????????] WHERE [???t] >= #2025/04/22# AND [???t] <= #2025/04/27#
2025-06-27 18:29:45,011 - app.core.mdb_client - INFO - Executing fetch query: SELECT ID, ?]???????, ???t, ?@??, ???@, ?H????, ?H?????, ?????, ??, ????, ????, ???????? FROM [????????] WHERE [???t] >= #2025/04/28# AND [???t] <= #2025/05/03#
2025-06-27 18:29:48,598 - app.core.mdb_client - INFO - Executing fetch query: SELECT ID, ?]???????, ???t, ?@??, ???@, ?H????, ?H?????, ?????, ??, ????, ????, ???????? FROM [????????] WHERE [???t] >= #2025/05/04# AND [???t] <= #2025/05/09#
2025-06-27 18:30:13,830 - app.core.mdb_client - INFO - Executing fetch query: SELECT ID, ?]???????, ???t, ?@??, ???@, ?H????, ?H?????, ?????, ??, ????, ????, ???????? FROM [????????] WHERE [???t] >= #2025/05/10# AND [???t] <= #2025/05/15#
2025-06-27 18:30:37,554 - app.core.mdb_client - INFO - Executing fetch query: SELECT ID, ?]???????, ???t, ?@??, ???@, ?H????, ?H?????, ?????, ??, ????, ????, ???????? FROM [????????] WHERE [???t] >= #2025/05/16# AND [???t] <= #2025/05/21#
2025-06-27 18:31:02,689 - app.core.mdb_client - INFO - Executing fetch query: SELECT ID, ?]???????, ???t, ?@??, ???@, ?H????, ?H?????, ?????, ??, ????, ????, ???????? FROM [????????] WHERE [???t] >= #2025/05/22# AND [???t] <= #2025/05/27#
2025-06-27 18:31:23,027 - app.core.mdb_client - INFO - Executing fetch query: SELECT ID, ?]???????, ???t, ?@??, ???@, ?H????, ?H?????, ?????, ??, ????, ????, ???????? FROM [????????] WHERE [???t] >= #2025/05/28# AND [???t] <= #2025/06/02#
2025-06-27 18:31:54,759 - app.core.mdb_client - INFO - Executing fetch query: SELECT ID, ?]???????, ???t, ?@??, ???@, ?H????, ?H?????, ?????, ??, ????, ????, ???????? FROM [????????] WHERE [???t] >= #2025/06/03# AND [???t] <= #2025/06/08#
2025-06-27 18:32:26,631 - app.core.mdb_client - INFO - Executing fetch query: SELECT ID, ?]???????, ???t, ?@??, ???@, ?H????, ?H?????, ?????, ??, ????, ????, ???????? FROM [????????] WHERE [???t] >= #2025/06/09# AND [???t] <= #2025/06/14#
2025-06-27 18:33:00,525 - app.core.mdb_client - INFO - Executing fetch query: SELECT ID, ?]???????, ???t, ?@??, ???@, ?H????, ?H?????, ?????, ??, ????, ????, ???????? FROM [????????] WHERE [???t] >= #2025/06/15# AND [???t] <= #2025/06/20#
2025-06-27 18:33:31,267 - app.core.mdb_client - INFO - Executing fetch query: SELECT ID, ?]???????, ???t, ?@??, ???@, ?H????, ?H?????, ?????, ??, ????, ????, ???????? FROM [????????] WHERE [???t] >= #2025/06/21# AND [???t] <= #2025/06/26#
2025-06-27 18:33:50,508 - app.core.mdb_client - INFO - Executing fetch query: SELECT ID, ?]???????, ???t, ?@??, ???@, ?H????, ?H?????, ?????, ??, ????, ????, ???????? FROM [????????] WHERE [???t] >= #2025/06/27# AND [???t] <= #2025/06/27#
2025-06-27 18:39:54,144 - app.main - INFO - ============================================================
2025-06-27 18:39:54,168 - app.main - INFO - ?z?u?E?v:
2025-06-27 18:39:54,169 - app.main - INFO -   server: MySuite Server6 - MDB Gateway
2025-06-27 18:39:54,169 - app.main - INFO -   version: 1.0.0
2025-06-27 18:39:54,169 - app.main - INFO -   platform: Windows
2025-06-27 18:39:54,170 - app.main - INFO -   host: 0.0.0.0
2025-06-27 18:39:54,170 - app.main - INFO -   port: 8009
2025-06-27 18:39:54,170 - app.main - INFO -   mdb_available: True
2025-06-27 18:39:54,171 - app.main - INFO -   mdb_path: D:\actest25\6.mdb
2025-06-27 18:39:54,171 - app.main - INFO -   debug: False
2025-06-27 18:39:54,172 - app.main - INFO -   log_level: INFO
2025-06-27 18:39:54,240 - app.main - INFO - ============================================================
2025-06-27 18:40:38,669 - app.core.mdb_client - INFO - Executing fetch query: SELECT ID, ?]???????, ???t, ?@??, ???@, ?H????, ?H?????, ?????, ??, ????, ????, ???????? FROM [????????] WHERE [???t] >= #2025/06/17# AND [???t] <= #2025/06/21#
2025-06-27 18:41:06,812 - app.core.mdb_client - INFO - Executing fetch query: SELECT ID, ?]???????, ???t, ?@??, ???@, ?H????, ?H?????, ?????, ??, ????, ????, ???????? FROM [????????] WHERE [???t] >= #2025/06/22# AND [???t] <= #2025/06/26#
2025-06-27 18:41:24,429 - app.core.mdb_client - INFO - Executing fetch query: SELECT ID, ?]???????, ???t, ?@??, ???@, ?H????, ?H?????, ?????, ??, ????, ????, ???????? FROM [????????] WHERE [???t] >= #2025/06/27# AND [???t] <= #2025/06/27#
2025-06-27 18:49:46,469 - app.core.mdb_client - INFO - Executing fetch query: SELECT ID, ?]???????, ???t, ?@??, ???@, ?H????, ?H?????, ?????, ??, ????, ????, ???????? FROM [????????] WHERE [???t] >= #2025/03/01# AND [???t] <= #2025/03/05#
2025-06-27 18:50:17,137 - app.core.mdb_client - INFO - Executing fetch query: SELECT ID, ?]???????, ???t, ?@??, ???@, ?H????, ?H?????, ?????, ??, ????, ????, ???????? FROM [????????] WHERE [?]???????] = '215753' AND [???t] >= #2025/05/28# AND [???t] <= #2025/06/27#
2025-06-27 18:50:48,513 - app.main - INFO - ============================================================
2025-06-27 18:50:48,545 - app.main - INFO - ?z?u?E?v:
2025-06-27 18:50:48,546 - app.main - INFO -   server: MySuite Server6 - MDB Gateway
2025-06-27 18:50:48,546 - app.main - INFO -   version: 1.0.0
2025-06-27 18:50:48,547 - app.main - INFO -   platform: Windows
2025-06-27 18:50:48,547 - app.main - INFO -   host: 0.0.0.0
2025-06-27 18:50:48,547 - app.main - INFO -   port: 8009
2025-06-27 18:50:48,548 - app.main - INFO -   mdb_available: True
2025-06-27 18:50:48,548 - app.main - INFO -   mdb_path: D:\actest25\6.mdb
2025-06-27 18:50:48,548 - app.main - INFO -   debug: False
2025-06-27 18:50:48,548 - app.main - INFO -   log_level: INFO
2025-06-27 18:50:48,562 - app.main - INFO - ============================================================
2025-06-27 18:51:00,969 - app.core.mdb_client - INFO - Executing fetch query: SELECT ID, ?]???????, ???t, ?@??, ???@, ?H????, ?H?????, ?????, ??, ????, ????, ???????? FROM [????????] WHERE [???t] >= #2025/03/01# AND [???t] <= #2025/03/05#
2025-06-27 18:51:31,227 - app.core.mdb_client - INFO - Executing fetch query: SELECT ID, ?]???????, ???t, ?@??, ???@, ?H????, ?H?????, ?????, ??, ????, ????, ???????? FROM [????????] WHERE [?]???????] = '215753' AND [???t] >= #2025/05/28# AND [???t] <= #2025/06/27#
2025-06-27 18:51:47,236 - app.main - INFO - ============================================================
2025-06-27 18:51:47,282 - app.main - INFO - ?z?u?E?v:
2025-06-27 18:51:47,283 - app.main - INFO -   server: MySuite Server6 - MDB Gateway
2025-06-27 18:51:47,283 - app.main - INFO -   version: 1.0.0
2025-06-27 18:51:47,284 - app.main - INFO -   platform: Windows
2025-06-27 18:51:47,284 - app.main - INFO -   host: 0.0.0.0
2025-06-27 18:51:47,284 - app.main - INFO -   port: 8009
2025-06-27 18:51:47,285 - app.main - INFO -   mdb_available: True
2025-06-27 18:51:47,285 - app.main - INFO -   mdb_path: D:\actest25\6.mdb
2025-06-27 18:51:47,286 - app.main - INFO -   debug: False
2025-06-27 18:51:47,286 - app.main - INFO -   log_level: INFO
2025-06-27 18:51:47,302 - app.main - INFO - ============================================================
2025-06-27 18:52:52,706 - app.core.mdb_client - INFO - Executing fetch query: SELECT ID, ?]???????, ???t, ?@??, ???@, ?H????, ?H?????, ?????, ??, ????, ????, ???????? FROM [????????] WHERE [???t] >= #2025/03/01# AND [???t] <= #2025/03/05#
2025-06-27 18:53:23,483 - app.core.mdb_client - INFO - Executing fetch query: SELECT ID, ?]???????, ???t, ?@??, ???@, ?H????, ?H?????, ?????, ??, ????, ????, ???????? FROM [????????] WHERE [?]???????] = '215753' AND [???t] >= #2025/05/28# AND [???t] <= #2025/06/27#
2025-06-27 18:53:59,836 - app.main - INFO - ============================================================
2025-06-27 18:53:59,863 - app.main - INFO - ?z?u?E?v:
2025-06-27 18:53:59,863 - app.main - INFO -   server: MySuite Server6 - MDB Gateway
2025-06-27 18:53:59,863 - app.main - INFO -   version: 1.0.0
2025-06-27 18:53:59,864 - app.main - INFO -   platform: Windows
2025-06-27 18:53:59,864 - app.main - INFO -   host: 0.0.0.0
2025-06-27 18:53:59,864 - app.main - INFO -   port: 8009
2025-06-27 18:53:59,864 - app.main - INFO -   mdb_available: True
2025-06-27 18:53:59,865 - app.main - INFO -   mdb_path: D:\actest25\6.mdb
2025-06-27 18:53:59,865 - app.main - INFO -   debug: False
2025-06-27 18:53:59,865 - app.main - INFO -   log_level: INFO
2025-06-27 18:53:59,879 - app.main - INFO - ============================================================
2025-06-27 18:54:37,378 - app.core.mdb_client - INFO - Executing fetch query: SELECT ID, ?]???????, ???t, ?@??, ???@, ?H????, ?H?????, ?????, ??, ????, ????, ???????? FROM [????????] WHERE [?]???????] = '215753' AND [???t] >= #2025/05/28# AND [???t] <= #2025/06/27#
2025-06-27 18:54:55,537 - app.main - INFO - ============================================================
2025-06-27 18:54:55,575 - app.main - INFO - ?z?u?E?v:
2025-06-27 18:54:55,576 - app.main - INFO -   server: MySuite Server6 - MDB Gateway
2025-06-27 18:54:55,576 - app.main - INFO -   version: 1.0.0
2025-06-27 18:54:55,577 - app.main - INFO -   platform: Windows
2025-06-27 18:54:55,577 - app.main - INFO -   host: 0.0.0.0
2025-06-27 18:54:55,577 - app.main - INFO -   port: 8009
2025-06-27 18:54:55,578 - app.main - INFO -   mdb_available: True
2025-06-27 18:54:55,578 - app.main - INFO -   mdb_path: D:\actest25\6.mdb
2025-06-27 18:54:55,578 - app.main - INFO -   debug: False
2025-06-27 18:54:55,579 - app.main - INFO -   log_level: INFO
2025-06-27 18:54:55,594 - app.main - INFO - ============================================================
2025-06-27 18:55:21,085 - app.core.mdb_client - INFO - Executing fetch query: SELECT ID, ?]???????, ???t, ?@??, ???@, ?H????, ?H?????, ?????, ??, ????, ????, ???????? FROM [????????] WHERE [???t] >= #2025/01/10# AND [???t] <= #2025/01/14#
2025-06-27 18:55:46,486 - app.core.mdb_client - INFO - Executing fetch query: SELECT ID, ?]???????, ???t, ?@??, ???@, ?H????, ?H?????, ?????, ??, ????, ????, ???????? FROM [????????] WHERE [?]???????] = '215753' AND [???t] >= #2025/05/28# AND [???t] <= #2025/06/27#
2025-06-27 18:57:10,821 - app.core.mdb_client - INFO - Executing fetch query: SELECT ID, ?]???????, ???t, ?@??, ???@, ?H????, ?H?????, ?????, ??, ????, ????, ???????? FROM [????????] WHERE [???t] >= #2025/03/01# AND [???t] <= #2025/03/05#
2025-06-27 19:05:21,472 - app.core.mdb_client - INFO - Executing fetch query: SELECT ID, ?]???????, ???t, ?@??, ???@, ?H????, ?H?????, ?????, ??, ????, ????, ???????? FROM [????????] WHERE [???t] >= #2025/06/25# AND [???t] <= #2025/06/25#
2025-06-27 19:17:39,156 - app.core.mdb_client - INFO - Executing fetch query: SELECT ID, ?]???????, ???t, ?@??, ???@, ?H????, ?H?????, ?????, ??, ????, ????, ???????? FROM [????????] WHERE [???t] >= #2025/06/25# AND [???t] <= #2025/06/25#
2025-06-27 19:23:17,072 - app.core.mdb_client - INFO - Executing fetch query: SELECT ID, ?]???????, ???t, ?@??, ???@, ?H????, ?H?????, ?????, ??, ????, ????, ???????? FROM [????????] WHERE [?]???????] = '215753' AND [???t] >= #2025/05/28# AND [???t] <= #2025/06/27#
2025-06-27 19:24:07,492 - app.core.mdb_client - INFO - Executing fetch query: SELECT ID, ?]???????, ???t, ?@??, ???@, ?H????, ?H?????, ?????, ??, ????, ????, ???????? FROM [????????] WHERE [?]???????] = '215753' AND [???t] >= #2025/05/28# AND [???t] <= #2025/06/27#
2025-06-27 19:24:21,903 - app.core.mdb_client - INFO - Executing fetch query: SELECT ID, ?]???????, ???t, ?@??, ???@, ?H????, ?H?????, ?????, ??, ????, ????, ???????? FROM [????????] WHERE [???t] >= #2025/06/26# AND [???t] <= #2025/06/26#
2025-06-27 19:29:11,064 - app.core.mdb_client - INFO - Executing fetch query: SELECT ID, ?]???????, ???t, ?@??, ???@, ?H????, ?H?????, ?????, ??, ????, ????, ???????? FROM [????????] WHERE [?]???????] = '215753' AND [???t] >= #2025/05/28# AND [???t] <= #2025/06/27#
2025-06-27 19:29:29,049 - app.core.mdb_client - INFO - Executing fetch query: SELECT ID, ?]???????, ???t, ?@??, ???@, ?H????, ?H?????, ?????, ??, ????, ????, ???????? FROM [????????] WHERE [???t] >= #2025/06/26# AND [???t] <= #2025/06/26#
2025-06-27 19:30:09,863 - app.core.mdb_client - INFO - Executing fetch query: SELECT ID, ?]???????, ???t, ?@??, ???@, ?H????, ?H?????, ?????, ??, ????, ????, ???????? FROM [????????] WHERE [?]???????] = '215753' AND [???t] >= #2025/05/28# AND [???t] <= #2025/06/27#
2025-06-27 19:30:24,352 - app.core.mdb_client - INFO - Executing fetch query: SELECT ID, ?]???????, ???t, ?@??, ???@, ?H????, ?H?????, ?????, ??, ????, ????, ???????? FROM [????????] WHERE [???t] >= #2025/06/26# AND [???t] <= #2025/06/26#
2025-06-30 07:32:37,105 - app.core.mdb_client - INFO - Executing fetch query: SELECT ID, ?]???????, ???t, ?@??, ???@, ?H????, ?H?????, ?????, ??, ????, ????, ???????? FROM [????????] WHERE [???t] >= #2025/02/01# AND [???t] <= #2025/02/07#
2025-06-30 07:33:25,376 - app.core.mdb_client - INFO - Executing fetch query: SELECT ID, ?]???????, ???t, ?@??, ???@, ?H????, ?H?????, ?????, ??, ????, ????, ???????? FROM [????????] WHERE [???t] >= #2025/02/08# AND [???t] <= #2025/02/10#
2025-06-30 07:45:33,819 - app.core.mdb_client - INFO - Executing fetch query: SELECT ID, ?]???????, ???t, ?@??, ???@, ?H????, ?H?????, ?????, ??, ????, ????, ???????? FROM [????????] WHERE [?]???????] = '215753' AND [???t] >= #2025/05/31# AND [???t] <= #2025/06/30#
2025-06-30 07:45:50,557 - app.core.mdb_client - INFO - Executing fetch query: SELECT ID, ?]???????, ???t, ?@??, ???@, ?H????, ?H?????, ?????, ??, ????, ????, ???????? FROM [????????] WHERE [???t] >= #2025/06/26# AND [???t] <= #2025/06/26#
2025-06-30 07:46:29,698 - app.core.mdb_client - INFO - Executing fetch query: SELECT ID, ?]???????, ???t, ?@??, ???@, ?H????, ?H?????, ?????, ??, ????, ????, ???????? FROM [????????] WHERE [?]???????] = '215753' AND [???t] >= #2025/05/31# AND [???t] <= #2025/06/30#
2025-06-30 07:46:45,175 - app.core.mdb_client - INFO - Executing fetch query: SELECT ID, ?]???????, ???t, ?@??, ???@, ?H????, ?H?????, ?????, ??, ????, ????, ???????? FROM [????????] WHERE [???t] >= #2025/06/26# AND [???t] <= #2025/06/26#
2025-06-30 07:57:20,784 - app.core.mdb_client - INFO - Executing fetch query: SELECT ID, ?]???????, ???t, ?@??, ???@, ?H????, ?H?????, ?????, ??, ????, ????, ???????? FROM [????????] WHERE [?]???????] = '215753' AND [???t] >= #2025/05/31# AND [???t] <= #2025/06/30#
2025-06-30 07:57:37,934 - app.core.mdb_client - INFO - Executing fetch query: SELECT ID, ?]???????, ???t, ?@??, ???@, ?H????, ?H?????, ?????, ??, ????, ????, ???????? FROM [????????] WHERE [???t] >= #2025/06/26# AND [???t] <= #2025/06/26#
2025-06-30 07:58:06,046 - app.main - INFO - ============================================================
2025-06-30 07:58:06,151 - app.main - INFO - ?z?u?E?v:
2025-06-30 07:58:06,151 - app.main - INFO -   server: MySuite Server6 - MDB Gateway
2025-06-30 07:58:06,152 - app.main - INFO -   version: 1.0.0
2025-06-30 07:58:06,152 - app.main - INFO -   platform: Windows
2025-06-30 07:58:06,152 - app.main - INFO -   host: 0.0.0.0
2025-06-30 07:58:06,152 - app.main - INFO -   port: 8009
2025-06-30 07:58:06,153 - app.main - INFO -   mdb_available: True
2025-06-30 07:58:06,153 - app.main - INFO -   mdb_path: D:\actest25\6.mdb
2025-06-30 07:58:06,153 - app.main - INFO -   debug: False
2025-06-30 07:58:06,153 - app.main - INFO -   log_level: INFO
2025-06-30 07:58:06,162 - app.main - INFO - ============================================================
2025-06-30 07:58:13,526 - app.core.mdb_client - INFO - Executing fetch query: SELECT ID, ?]???????, ???t, ?@??, ???@, ?H????, ?H?????, ?????, ??, ????, ????, ???????? FROM [????????] WHERE [?]???????] = '215753' AND [???t] >= #2025/05/31# AND [???t] <= #2025/06/30#
2025-06-30 07:58:27,596 - app.core.mdb_client - INFO - Executing fetch query: SELECT ID, ?]???????, ???t, ?@??, ???@, ?H????, ?H?????, ?????, ??, ????, ????, ???????? FROM [????????] WHERE [???t] >= #2025/06/26# AND [???t] <= #2025/06/26#
2025-06-30 08:30:19,808 - app.core.mdb_client - INFO - Executing fetch query: SELECT ID, ?]???????, ???t, ?@??, ???@, ?H????, ?H?????, ?????, ??, ????, ????, ???????? FROM [????????] WHERE [???t] >= #2025/06/01# AND [???t] <= #2025/06/03#
2025-06-30 08:31:20,236 - app.core.mdb_client - INFO - Executing fetch query: SELECT ID, ?]???????, ???t, ?@??, ???@, ?H????, ?H?????, ?????, ??, ????, ????, ???????? FROM [????????] WHERE [???t] >= #2025/06/23# AND [???t] <= #2025/06/25#
2025-06-30 08:31:43,697 - app.core.mdb_client - INFO - Executing fetch query: SELECT ID, ?]???????, ???t, ?@??, ???@, ?H????, ?H?????, ?????, ??, ????, ????, ???????? FROM [????????] WHERE [???t] >= #2025/06/26# AND [???t] <= #2025/06/28#
2025-06-30 08:31:55,137 - app.core.mdb_client - INFO - Executing fetch query: SELECT ID, ?]???????, ???t, ?@??, ???@, ?H????, ?H?????, ?????, ??, ????, ????, ???????? FROM [????????] WHERE [???t] >= #2025/06/29# AND [???t] <= #2025/06/30#
2025-06-30 08:56:35,034 - app.core.mdb_client - INFO - Executing fetch query: SELECT ID, ?]???????, ???t, ?@??, ???@, ?H????, ?H?????, ?????, ??, ????, ????, ???????? FROM [????????] WHERE [???t] >= #2025/01/01# AND [???t] <= #2025/01/05#
2025-06-30 09:25:06,756 - app.core.mdb_client - INFO - Executing fetch query: SELECT ID, ?]???????, ???t, ?@??, ???@, ?H????, ?H?????, ?????, ??, ????, ????, ???????? FROM [????????] WHERE [???t] >= #2025/01/01# AND [???t] <= #2025/01/07#
2025-06-30 09:25:26,732 - app.core.mdb_client - INFO - Executing fetch query: SELECT ID, ?]???????, ???t, ?@??, ???@, ?H????, ?H?????, ?????, ??, ????, ????, ???????? FROM [????????] WHERE [???t] >= #2025/01/08# AND [???t] <= #2025/01/12#
2025-06-30 09:27:42,799 - app.core.mdb_client - INFO - Executing fetch query: SELECT ID, ?]???????, ???t, ?@??, ???@, ?H????, ?H?????, ?????, ??, ????, ????, ???????? FROM [????????] WHERE [???t] >= #2024/12/01# AND [???t] <= #2024/12/05#
2025-06-30 09:29:31,445 - app.core.mdb_client - INFO - Executing fetch query: SELECT ID, ?]???????, ???t, ?@??, ???@, ?H????, ?H?????, ?????, ??, ????, ????, ???????? FROM [????????] WHERE [???t] >= #2024/11/01# AND [???t] <= #2024/11/04#
2025-06-30 10:34:52,163 - app.core.mdb_client - INFO - Executing fetch query: SELECT ID, ?]???????, ???t, ?@??, ???@, ?H????, ?H?????, ?????, ??, ????, ????, ???????? FROM [????????] WHERE [???t] >= #2025/06/01# AND [???t] <= #2025/06/30#
2025-06-30 10:37:07,976 - app.core.mdb_client - INFO - Executing fetch query: SELECT ID, ?]???????, ???t, ?@??, ???@, ?H????, ?H?????, ?????, ??, ????, ????, ???????? FROM [????????] WHERE [???t] >= #2025/06/01# AND [???t] <= #2025/06/30#
2025-06-30 10:37:53,796 - app.main - INFO - ============================================================
2025-06-30 10:37:53,886 - app.main - INFO - ?z?u?E?v:
2025-06-30 10:37:53,886 - app.main - INFO -   server: MySuite Server6 - MDB Gateway
2025-06-30 10:37:53,886 - app.main - INFO -   version: 1.0.0
2025-06-30 10:37:53,886 - app.main - INFO -   platform: Windows
2025-06-30 10:37:53,887 - app.main - INFO -   host: 0.0.0.0
2025-06-30 10:37:53,887 - app.main - INFO -   port: 8009
2025-06-30 10:37:53,887 - app.main - INFO -   mdb_available: True
2025-06-30 10:37:53,888 - app.main - INFO -   mdb_path: D:\actest25\6.mdb
2025-06-30 10:37:53,888 - app.main - INFO -   debug: False
2025-06-30 10:37:53,888 - app.main - INFO -   log_level: INFO
2025-06-30 10:37:53,896 - app.main - INFO - ============================================================
2025-06-30 10:38:12,084 - app.main - INFO - ============================================================
2025-06-30 10:38:12,109 - app.main - INFO - ?z?u?E?v:
2025-06-30 10:38:12,109 - app.main - INFO -   server: MySuite Server6 - MDB Gateway
2025-06-30 10:38:12,110 - app.main - INFO -   version: 1.0.0
2025-06-30 10:38:12,110 - app.main - INFO -   platform: Windows
2025-06-30 10:38:12,110 - app.main - INFO -   host: 0.0.0.0
2025-06-30 10:38:12,110 - app.main - INFO -   port: 8009
2025-06-30 10:38:12,111 - app.main - INFO -   mdb_available: True
2025-06-30 10:38:12,111 - app.main - INFO -   mdb_path: D:\actest25\6.mdb
2025-06-30 10:38:12,111 - app.main - INFO -   debug: False
2025-06-30 10:38:12,112 - app.main - INFO -   log_level: INFO
2025-06-30 10:38:12,124 - app.main - INFO - ============================================================
2025-06-30 10:38:20,421 - app.core.mdb_client - INFO - Executing fetch query: SELECT ID, ?]???????, ???t, ?@??, ???@, ?H????, ?H?????, ?????, ??, ????, ????, ???????? FROM [????????] WHERE [???t] >= #2025/06/01# AND [???t] <= #2025/06/30#
2025-06-30 10:39:12,712 - app.main - INFO - ============================================================
2025-06-30 10:39:12,737 - app.main - INFO - ?z?u?E?v:
2025-06-30 10:39:12,737 - app.main - INFO -   server: MySuite Server6 - MDB Gateway
2025-06-30 10:39:12,738 - app.main - INFO -   version: 1.0.0
2025-06-30 10:39:12,738 - app.main - INFO -   platform: Windows
2025-06-30 10:39:12,738 - app.main - INFO -   host: 0.0.0.0
2025-06-30 10:39:12,738 - app.main - INFO -   port: 8009
2025-06-30 10:39:12,739 - app.main - INFO -   mdb_available: True
2025-06-30 10:39:12,739 - app.main - INFO -   mdb_path: D:\actest25\6.mdb
2025-06-30 10:39:12,739 - app.main - INFO -   debug: False
2025-06-30 10:39:12,739 - app.main - INFO -   log_level: INFO
2025-06-30 10:39:12,747 - app.main - INFO - ============================================================
2025-06-30 10:39:26,822 - app.core.mdb_client - INFO - Executing fetch query: SELECT ID, ?]???????, ???t, ?@??, ???@, ?H????, ?H?????, ?????, ??, ????, ????, ???????? FROM [????????] WHERE [?]???????] = 'test_employee_fixed' AND [???t] >= #2025/06/01# AND [???t] <= #2025/06/30#
2025-06-30 10:40:28,952 - app.main - INFO - ============================================================
2025-06-30 10:40:28,979 - app.main - INFO - ?z?u?E?v:
2025-06-30 10:40:28,979 - app.main - INFO -   server: MySuite Server6 - MDB Gateway
2025-06-30 10:40:28,980 - app.main - INFO -   version: 1.0.0
2025-06-30 10:40:28,980 - app.main - INFO -   platform: Windows
2025-06-30 10:40:28,980 - app.main - INFO -   host: 0.0.0.0
2025-06-30 10:40:28,981 - app.main - INFO -   port: 8009
2025-06-30 10:40:28,981 - app.main - INFO -   mdb_available: True
2025-06-30 10:40:28,981 - app.main - INFO -   mdb_path: D:\actest25\6.mdb
2025-06-30 10:40:28,982 - app.main - INFO -   debug: False
2025-06-30 10:40:28,982 - app.main - INFO -   log_level: INFO
2025-06-30 10:40:28,991 - app.main - INFO - ============================================================
2025-06-30 10:40:48,395 - app.core.mdb_client - INFO - Executing fetch query: SELECT ID, ?]???????, ???t, ?@??, ???@, ?H????, ?H?????, ?????, ??, ????, ????, ???????? FROM [????????] WHERE [???t] >= #2025/06/01# AND [???t] <= #2025/06/30#
2025-06-30 10:41:29,561 - app.main - INFO - ============================================================
2025-06-30 10:41:29,588 - app.main - INFO - ?z?u?E?v:
2025-06-30 10:41:29,589 - app.main - INFO -   server: MySuite Server6 - MDB Gateway
2025-06-30 10:41:29,589 - app.main - INFO -   version: 1.0.0
2025-06-30 10:41:29,589 - app.main - INFO -   platform: Windows
2025-06-30 10:41:29,589 - app.main - INFO -   host: 0.0.0.0
2025-06-30 10:41:29,590 - app.main - INFO -   port: 8009
2025-06-30 10:41:29,590 - app.main - INFO -   mdb_available: True
2025-06-30 10:41:29,590 - app.main - INFO -   mdb_path: D:\actest25\6.mdb
2025-06-30 10:41:29,590 - app.main - INFO -   debug: False
2025-06-30 10:41:29,591 - app.main - INFO -   log_level: INFO
2025-06-30 10:41:29,606 - app.main - INFO - ============================================================
2025-06-30 10:44:31,545 - app.core.mdb_client - INFO - Executing fetch query: SELECT ID, ?]???????, ???t, ?@??, ???@, ?H????, ?H?????, ?????, ??, ????, ????, ???????? FROM [????????] WHERE [???t] >= #2025/06/26# AND [???t] <= #2025/06/30#
2025-06-30 10:45:40,320 - app.core.mdb_client - INFO - Executing fetch query: SELECT ID, ?]???????, ???t, ?@??, ???@, ?H????, ?H?????, ?????, ??, ????, ????, ???????? FROM [????????] WHERE [???t] >= #2025/06/26# AND [???t] <= #2025/06/30#
2025-06-30 14:02:27,062 - app.utils.logger - INFO - 日志系统已初始化 - 级别: INFO, 文件: logs/server6.log
2025-06-30 14:02:27,081 - app.main - INFO - ============================================================
2025-06-30 14:02:27,081 - app.main - INFO - Server6 MDB网关启动中...
2025-06-30 14:02:27,081 - app.main - INFO - 配置摘要:
2025-06-30 14:02:27,081 - app.main - INFO -   server: MySuite Server6 - MDB Gateway
2025-06-30 14:02:27,081 - app.main - INFO -   version: 1.0.0
2025-06-30 14:02:27,081 - app.main - INFO -   platform: Linux
2025-06-30 14:02:27,081 - app.main - INFO -   host: 0.0.0.0
2025-06-30 14:02:27,081 - app.main - INFO -   port: 8009
2025-06-30 14:02:27,081 - app.main - INFO -   mdb_available: False
2025-06-30 14:02:27,081 - app.main - INFO -   mdb_path: /tmp/mock_6.mdb
2025-06-30 14:02:27,081 - app.main - INFO -   debug: False
2025-06-30 14:02:27,081 - app.main - INFO -   log_level: INFO
2025-06-30 14:02:27,081 - app.main - INFO - Server6 MDB网关启动完成
2025-06-30 14:02:27,082 - app.main - INFO - ============================================================
2025-06-30 14:04:42,993 - app.utils.logger - INFO - 日志系统已初始化 - 级别: INFO, 文件: logs/server6.log
2025-06-30 14:04:43,011 - app.main - INFO - ============================================================
2025-06-30 14:04:43,012 - app.main - INFO - Server6 MDB网关启动中...
2025-06-30 14:04:43,012 - app.main - INFO - 配置摘要:
2025-06-30 14:04:43,012 - app.main - INFO -   server: MySuite Server6 - MDB Gateway
2025-06-30 14:04:43,012 - app.main - INFO -   version: 1.0.0
2025-06-30 14:04:43,012 - app.main - INFO -   platform: Linux
2025-06-30 14:04:43,012 - app.main - INFO -   host: 0.0.0.0
2025-06-30 14:04:43,012 - app.main - INFO -   port: 8009
2025-06-30 14:04:43,012 - app.main - INFO -   mdb_available: False
2025-06-30 14:04:43,012 - app.main - INFO -   mdb_path: /tmp/mock_6.mdb
2025-06-30 14:04:43,012 - app.main - INFO -   debug: False
2025-06-30 14:04:43,012 - app.main - INFO -   log_level: INFO
2025-06-30 14:04:43,012 - app.main - INFO - Server6 MDB网关启动完成
2025-06-30 14:04:43,012 - app.main - INFO - ============================================================
