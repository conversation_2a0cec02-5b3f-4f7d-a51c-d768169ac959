# Server6 - MDB网关微服务

## 🎯 概览

Server6是MySuite系统的MDB数据库网关微服务，专门处理Microsoft Access数据库的CRUD操作，为Server5提供MDB访问能力。

## 🏗️ 架构设计

```
┌─────────────────┐    HTTP API    ┌─────────────────┐
│   Server5       │◄─────────────►│   Server6       │
│  (多平台核心)    │                │  (MDB网关)      │
│                 │                │                 │
│ - PostgreSQL    │                │ - MDB ODBC      │
│ - Redis         │                │ - 数据转换      │
│ - MongoDB       │                │ - API服务       │
│ - f1-f6服务     │                │ - 连接池        │
└─────────────────┘                └─────────────────┘
   Ubuntu/Linux                       Windows 10
```

## ✨ 特性

- **跨平台支持**: Windows真实MDB连接 + 非Windows模拟模式
- **RESTful API**: 标准HTTP接口，易于集成
- **连接池管理**: 高效的数据库连接管理
- **错误重试**: 自动重试机制，提高稳定性
- **完整CRUD**: 支持查询、插入、更新、删除操作
- **批量操作**: 支持批量数据处理
- **健康监控**: 完整的健康检查和状态监控

## 📋 API端点

### 基础信息
- `GET /` - 服务信息
- `GET /health` - 健康检查
- `GET /status` - 详细状态信息
- `GET /docs` - API文档

### MDB操作
- `GET /mdb/test` - 测试MDB连接
- `POST /mdb/query` - 执行查询
- `POST /mdb/insert` - 插入记录
- `PUT /mdb/update` - 更新记录
- `DELETE /mdb/delete` - 删除记录

## 🚀 快速开始

### 1. 环境要求

**Windows环境** (推荐):
- Python 3.8+
- Microsoft Access驱动程序
- pyodbc库

**非Windows环境** (模拟模式):
- Python 3.8+
- 仅支持模拟操作

### 2. 安装依赖

```bash
pip install -r requirements.txt
```

### 3. 启动服务

```bash
python start_server6.py
```

### 4. 验证服务

```bash
# 健康检查
curl http://localhost:8009/health

# MDB连接测试
curl http://localhost:8009/mdb/test

# 查看API文档
open http://localhost:8009/docs
```

## 📚 API使用示例

### 查询数据

```bash
curl -X POST "http://localhost:8009/mdb/query" \
  -H "Content-Type: application/json" \
  -d '{
    "table_name": "employees",
    "fields": ["id", "name", "department"],
    "where_clause": "status = '\''在职'\''",
    "limit": 10
  }'
```

### 插入数据

```bash
curl -X POST "http://localhost:8009/mdb/insert" \
  -H "Content-Type: application/json" \
  -d '{
    "table_name": "employees",
    "data": {
      "name": "张三",
      "department": "技术部",
      "position": "工程师",
      "status": "在职"
    }
  }'
```

### 更新数据

```bash
curl -X PUT "http://localhost:8009/mdb/update" \
  -H "Content-Type: application/json" \
  -d '{
    "table_name": "employees",
    "data": {
      "department": "研发部",
      "position": "高级工程师"
    },
    "where_clause": "id = 1"
  }'
```

### 删除数据

```bash
curl -X DELETE "http://localhost:8009/mdb/delete" \
  -H "Content-Type: application/json" \
  -d '{
    "table_name": "employees",
    "where_clause": "id = 1"
  }'
```

## 🔧 配置说明

### 环境变量

| 变量名 | 默认值 | 说明 |
|--------|--------|------|
| `SERVER6_HOST` | 127.0.0.1 | 服务监听地址 |
| `SERVER6_PORT` | 8009 | 服务端口 |
| `MDB_FILE_PATH` | D:\actest25\6.mdb | MDB文件路径 |
| `DEBUG` | false | 调试模式 |
| `LOG_LEVEL` | INFO | 日志级别 |

### 配置文件

主要配置在 `config/config.py` 中：

- **平台检测**: 自动识别Windows/非Windows环境
- **连接配置**: MDB文件路径和连接字符串
- **性能参数**: 连接池大小、超时时间等
- **安全设置**: API密钥、CORS配置等

## 🧪 测试

### 运行测试

```bash
# 运行所有测试
pytest

# 运行特定测试
pytest tests/test_basic.py -v

# 运行测试并显示覆盖率
pytest --cov=app tests/
```

### 手动测试

```bash
# 启动服务
python start_server6.py

# 在另一个终端测试
curl http://localhost:8009/health
curl http://localhost:8009/mdb/test
```

## 📊 监控和日志

### 日志文件

- **位置**: `logs/server6.log`
- **格式**: 时间戳 - 模块名 - 级别 - 消息
- **轮转**: 按大小自动轮转

### 监控指标

- **健康状态**: `/health` 端点
- **连接状态**: MDB连接可用性
- **性能指标**: 响应时间、错误率
- **系统信息**: CPU、内存、平台信息

## 🔒 安全考虑

1. **API密钥**: 可选的API密钥验证
2. **CORS**: 配置允许的源域名
3. **输入验证**: Pydantic模型验证所有输入
4. **SQL注入**: 使用参数化查询防止注入
5. **错误处理**: 避免敏感信息泄露

## 🚨 故障排除

### 常见问题

1. **MDB连接失败**
   - 检查MDB文件是否存在
   - 确认Access驱动程序已安装
   - 验证文件权限

2. **pyodbc导入失败**
   - 在Windows上安装pyodbc: `pip install pyodbc`
   - 非Windows环境会自动切换到模拟模式

3. **端口占用**
   - 修改配置中的PORT设置
   - 或使用环境变量: `SERVER6_PORT=8010`

### 调试模式

```bash
# 启用调试模式
DEBUG=true python start_server6.py

# 或设置日志级别
LOG_LEVEL=DEBUG python start_server6.py
```

## 🔄 与Server5集成

Server6设计为Server5的MDB数据访问代理：

1. **Server5调用**: 通过HTTP API调用Server6
2. **数据转换**: Server6处理MDB特定的数据格式
3. **错误处理**: Server6提供统一的错误响应格式
4. **性能优化**: 连接池和缓存减少延迟

## 📈 性能优化

1. **连接池**: 复用数据库连接
2. **批量操作**: 支持批量插入/更新
3. **异步处理**: FastAPI异步框架
4. **缓存策略**: 可配置的查询缓存
5. **监控告警**: 实时性能监控

## 🛠️ 开发指南

### 项目结构

```
server6/
├── app/
│   ├── core/           # 核心业务逻辑
│   ├── models/         # 数据模型
│   ├── routers/        # API路由
│   ├── utils/          # 工具函数
│   └── main.py         # 主应用
├── config/             # 配置文件
├── tests/              # 测试文件
├── logs/               # 日志文件
├── requirements.txt    # 依赖包
└── start_server6.py    # 启动脚本
```

### 添加新功能

1. 在 `app/models/` 中定义数据模型
2. 在 `app/routers/` 中添加API端点
3. 在 `app/core/` 中实现业务逻辑
4. 在 `tests/` 中添加测试用例

## 📞 技术支持

- **文档**: `/docs` API文档
- **健康检查**: `/health` 状态监控
- **日志**: `logs/server6.log` 详细日志
- **测试**: `pytest` 自动化测试

---

**版本**: 1.0.0  
**更新**: 2025/06/26  
**平台**: Windows 10+ (推荐) / Linux (模拟模式) 