#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试Server6的insert_record方法修复
验证category和item字段为0时的SQL生成
"""

import sys
from pathlib import Path
sys.path.append(str(Path(__file__).parent))

from app.core.mdb_client import MDBClient

def test_insert_sql_generation():
    """测试insert_record方法的SQL生成"""
    
    print("🧪 测试Server6的insert_record方法修复")
    print("=" * 50)
    
    # 初始化MDBClient
    client = MDBClient()
    
    # 测试数据1：category和item为字符串"0"
    test_data_1 = {
        '従業員ｺｰﾄﾞ': '215829',
        '日付': '2025/07/16',
        '機種': '',
        '号機': '',
        '工場製番': '',
        '工事番号': 'TEST001',
        'ﾕﾆｯﾄ番号': '',
        '区分': '0',  # 字符串"0"
        '項目': '0',  # 字符串"0"
        '時間': 0.1,
        '所属ｺｰﾄﾞ': '131'
    }
    
    print("📝 测试数据1 - category和item为字符串'0':")
    print(f"   区分: {test_data_1['区分']} (类型: {type(test_data_1['区分'])})")
    print(f"   項目: {test_data_1['項目']} (类型: {type(test_data_1['項目'])})")
    
    # 测试数据2：category和item为None
    test_data_2 = {
        '従業員ｺｰﾄﾞ': '215829',
        '日付': '2025/07/16',
        '機種': '',
        '号機': '',
        '工場製番': '',
        '工事番号': 'TEST001',
        'ﾕﾆｯﾄ番号': '',
        '区分': None,  # None值
        '項目': None,  # None值
        '時間': 0.1,
        '所属ｺｰﾄﾞ': '131'
    }
    
    print("\n📝 测试数据2 - category和item为None:")
    print(f"   区分: {test_data_2['区分']} (类型: {type(test_data_2['区分'])})")
    print(f"   項目: {test_data_2['項目']} (类型: {type(test_data_2['項目'])})")
    
    # 测试数据3：category和item为空字符串
    test_data_3 = {
        '従業員ｺｰﾄﾞ': '215829',
        '日付': '2025/07/16',
        '機種': '',
        '号機': '',
        '工場製番': '',
        '工事番号': 'TEST001',
        'ﾕﾆｯﾄ番号': '',
        '区分': '',  # 空字符串
        '項目': '',  # 空字符串
        '時間': 0.1,
        '所属ｺｰﾄﾞ': '131'
    }
    
    print("\n📝 测试数据3 - category和item为空字符串:")
    print(f"   区分: {test_data_3['区分']} (类型: {type(test_data_3['区分'])})")
    print(f"   項目: {test_data_3['項目']} (类型: {type(test_data_3['項目'])})")
    
    # 模拟insert_record方法中的SQL生成逻辑
    def generate_sql(record_data):
        """模拟insert_record方法中的SQL生成逻辑"""
        
        # 获取字段值
        employee_id = record_data.get('employee_id') or record_data.get('従業員ｺｰﾄﾞ')
        entry_date = record_data.get('entry_date') or record_data.get('日付')
        model = record_data.get('model') or record_data.get('機種', '')
        number = record_data.get('number') or record_data.get('号機', '')
        factory_number = record_data.get('factory_number') or record_data.get('工場製番', '')
        project_number = record_data.get('project_number') or record_data.get('工事番号', '')
        unit_number = record_data.get('unit_number') or record_data.get('ﾕﾆｯﾄ番号', '')
        category = record_data.get('category') or record_data.get('区分')
        item = record_data.get('item') or record_data.get('項目')
        duration = record_data.get('duration') or record_data.get('時間')
        department = record_data.get('department') or record_data.get('所属ｺｰﾄﾞ')
        
        # 生成SQL（修复后的版本）
        sql = f"""INSERT INTO [元作業時間]
              (従業員ｺｰﾄﾞ, 日付, 機種, 号機, 
               工場製番, 工事番号, ﾕﾆｯﾄ番号,
               区分, 項目, 時間, 所属ｺｰﾄﾞ)
             VALUES
              ('{employee_id}', 
               #{entry_date}#,
               {f"'{model}'" if model else 'NULL'},
               {f"'{number}'" if number else 'NULL'},
               {f"'{factory_number}'" if factory_number else 'NULL'},
               {f"'{project_number}'" if project_number else 'NULL'},
               {f"'{unit_number}'" if unit_number else 'NULL'},
               {f"'{category}'" if category is not None else 'NULL'},
               {f"'{item}'" if item is not None else 'NULL'},
               {duration},
               '{department}'
              )"""
        
        return sql
    
    # 测试SQL生成
    print("\n🔍 测试SQL生成:")
    
    print("\n1. 测试数据1的SQL:")
    sql1 = generate_sql(test_data_1)
    print(sql1)
    
    print("\n2. 测试数据2的SQL:")
    sql2 = generate_sql(test_data_2)
    print(sql2)
    
    print("\n3. 测试数据3的SQL:")
    sql3 = generate_sql(test_data_3)
    print(sql3)
    
    # 验证结果
    print("\n✅ 验证结果:")
    
    # 检查是否包含'None'字符串
    if "'None'" in sql1:
        print("❌ 测试数据1: SQL中包含'None'字符串")
    else:
        print("✅ 测试数据1: SQL中不包含'None'字符串")
    
    if "'None'" in sql2:
        print("❌ 测试数据2: SQL中包含'None'字符串")
    else:
        print("✅ 测试数据2: SQL中不包含'None'字符串")
    
    if "'None'" in sql3:
        print("❌ 测试数据3: SQL中包含'None'字符串")
    else:
        print("✅ 测试数据3: SQL中不包含'None'字符串")
    
    # 检查是否包含NULL
    if "NULL" in sql1:
        print("✅ 测试数据1: SQL中包含NULL")
    else:
        print("❌ 测试数据1: SQL中不包含NULL")
    
    if "NULL" in sql2:
        print("✅ 测试数据2: SQL中包含NULL")
    else:
        print("❌ 测试数据2: SQL中不包含NULL")
    
    if "NULL" in sql3:
        print("✅ 测试数据3: SQL中包含NULL")
    else:
        print("❌ 测试数据3: SQL中不包含NULL")
    
    print("\n🎯 修复总结:")
    print("- 修复前: category/item为None时，SQL中会出现'None'字符串")
    print("- 修复后: category/item为None时，SQL中会出现NULL")
    print("- 修复后: category/item为'0'时，SQL中会出现'0'")

if __name__ == "__main__":
    test_insert_sql_generation() 