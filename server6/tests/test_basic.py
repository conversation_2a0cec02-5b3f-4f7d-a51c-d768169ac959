# server6/tests/test_basic.py
# Server6基础功能测试

import pytest
import asyncio
from fastapi.testclient import TestClient
from app.main import app

client = TestClient(app)

def test_root():
    """测试根路径"""
    response = client.get("/")
    assert response.status_code == 200
    data = response.json()
    assert data["service"] == "MySuite Server6 - MDB Gateway"
    assert "version" in data

def test_health_check():
    """测试健康检查"""
    response = client.get("/health")
    assert response.status_code == 200
    data = response.json()
    assert "status" in data
    assert "mdb_available" in data

def test_status():
    """测试状态信息"""
    response = client.get("/status")
    assert response.status_code == 200
    data = response.json()
    assert data["service"] == "MySuite Server6 - MDB Gateway"
    assert "platform" in data
    assert "mdb" in data

def test_mdb_test():
    """测试MDB连接测试"""
    response = client.get("/mdb/test")
    assert response.status_code == 200
    data = response.json()
    assert "status" in data
    assert "data" in data

def test_mdb_query():
    """测试MDB查询"""
    query_data = {
        "table_name": "employees",
        "fields": ["id", "name"],
        "limit": 10
    }
    response = client.post("/mdb/query", json=query_data)
    assert response.status_code == 200
    data = response.json()
    assert "status" in data
    assert "data" in data

def test_mdb_insert():
    """测试MDB插入"""
    insert_data = {
        "table_name": "employees",
        "data": {
            "name": "测试用户",
            "department": "测试部门",
            "position": "测试职位"
        }
    }
    response = client.post("/mdb/insert", json=insert_data)
    assert response.status_code == 200
    data = response.json()
    assert "status" in data
    assert "data" in data

if __name__ == "__main__":
    pytest.main([__file__, "-v"]) 