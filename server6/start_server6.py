# server6/start_server6.py
# Server6 MDB网关启动脚本

import sys
import os
import logging
import platform
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

# 导入配置
from config.config import HOST, PORT, get_config_summary, validate_config

def main():
    """主函数"""
    print("=" * 60)
    print("MySuite Server6 - MDB网关启动")
    print("=" * 60)
    
    # 显示平台信息
    print(f"平台: {platform.system()} {platform.release()}")
    print(f"Python: {platform.python_version()}")
    print("")
    
    # 验证配置
    config_errors = validate_config()
    if config_errors:
        print("⚠️  配置验证警告:")
        for error in config_errors:
            print(f"   - {error}")
        print("")
    
    # 显示配置摘要
    config_summary = get_config_summary()
    print("📋 配置摘要:")
    for key, value in config_summary.items():
        print(f"   {key}: {value}")
    print("")
    
    # 显示访问信息
    print("🌐 访问地址:")
    print(f"   主页: http://{HOST}:{PORT}")
    print(f"   健康检查: http://{HOST}:{PORT}/health")
    print(f"   状态信息: http://{HOST}:{PORT}/status")
    print(f"   API文档: http://{HOST}:{PORT}/docs")
    print(f"   MDB测试: http://{HOST}:{PORT}/mdb/test")
    print("")
    
    print("🚀 启动Server6...")
    print("   按 Ctrl+C 停止服务")
    print("=" * 60)
    
    try:
        # 启动服务器
        import uvicorn
        from app.main import app
        
        uvicorn.run(
            app,
            host=HOST,
            port=PORT,
            log_level="info",
            access_log=True
        )
        
    except KeyboardInterrupt:
        print("\n👋 收到中断信号，正在关闭服务...")
    except Exception as e:
        print(f"\n❌ 启动失败: {e}")
        sys.exit(1)
    finally:
        print("🔌 Server6已关闭")

if __name__ == "__main__":
    main() 