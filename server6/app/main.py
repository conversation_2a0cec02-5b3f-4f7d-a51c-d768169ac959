# server6/app/main.py
# Server6 MDB网关主应用程序

import logging
from contextlib import asynccontextmanager
from fastapi import FastAPI
from fastapi.middleware.cors import CORSMiddleware

from config.config import (
    API_TITLE, API_DESCRIPTION, VERSION, DEBUG, ALLOWED_HOSTS,
    validate_config, get_config_summary
)
from .routers import mdb_router, health_router
from .utils.logger import setup_logging

# 设置日志
setup_logging()
logger = logging.getLogger(__name__)

@asynccontextmanager
async def lifespan(app: FastAPI):
    """应用生命周期管理"""
    # 启动时执行
    logger.info("=" * 60)
    logger.info("Server6 MDB网关启动中...")
    
    # 验证配置
    config_errors = validate_config()
    if config_errors:
        logger.warning("配置验证警告:")
        for error in config_errors:
            logger.warning(f"  - {error}")
    
    # 显示配置摘要
    config_summary = get_config_summary()
    logger.info("配置摘要:")
    for key, value in config_summary.items():
        logger.info(f"  {key}: {value}")
    
    logger.info("Server6 MDB网关启动完成")
    logger.info("=" * 60)
    
    yield
    
    # 关闭时执行
    logger.info("Server6 MDB网关正在关闭...")
    logger.info("Server6 MDB网关已关闭")

# 创建FastAPI应用
app = FastAPI(
    title=API_TITLE,
    description=API_DESCRIPTION,
    version=VERSION,
    debug=DEBUG,
    lifespan=lifespan
)

# 添加CORS中间件
app.add_middleware(
    CORSMiddleware,
    allow_origins=ALLOWED_HOSTS,
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# 注册路由
app.include_router(health_router)
app.include_router(mdb_router)

# 根路径重定向到健康检查
@app.get("/")
async def root():
    """根路径"""
    return {
        "service": "MySuite Server6 - MDB Gateway",
        "version": VERSION,
        "status": "running",
        "docs": "/docs",
        "health": "/health",
        "mdb_test": "/mdb/test"
    }

if __name__ == "__main__":
    import uvicorn
    from config.config import HOST, PORT
    
    logger.info(f"直接运行模式 - 启动在 {HOST}:{PORT}")
    uvicorn.run(app, host=HOST, port=PORT, log_level="info") 