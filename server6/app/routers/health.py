# server6/app/routers/health.py
# 健康检查和系统状态路由

import logging
import time
import platform
from datetime import datetime
from fastapi import APIRouter

from ..models.api_models import HealthStatus
from ..core.mdb_client import MDBClient

logger = logging.getLogger(__name__)
router = APIRouter(tags=["Health Check"])

# 服务启动时间
start_time = time.time()
mdb_client = MDBClient()

@router.get("/")
async def root():
    """根路径"""
    return {
        "service": "MySuite Server6 - MDB Gateway",
        "version": "1.0.0",
        "status": "running",
        "platform": platform.system(),
        "timestamp": datetime.now().isoformat()
    }

@router.get("/health")
async def health_check():
    """健康检查"""
    try:
        # 测试MDB连接
        mdb_test = mdb_client.test_connection()
        
        # 计算运行时间
        uptime = time.time() - start_time
        
        # 系统信息
        system_info = {
            "platform": platform.system(),
            "platform_release": platform.release(),
            "platform_version": platform.version(),
            "machine": platform.machine(),
            "processor": platform.processor(),
            "python_version": platform.python_version()
        }
        
        # 连接池状态（简化）
        connection_pool = {
            "active_connections": 0,
            "max_connections": 5,
            "available_connections": 5
        }
        
        health_status = HealthStatus(
            status="healthy" if mdb_test["success"] else "degraded",
            mdb_available=mdb_test["success"],
            connection_pool=connection_pool,
            system_info=system_info,
            uptime=uptime
        )
        
        return health_status.dict()
        
    except Exception as e:
        logger.error(f"健康检查失败: {e}")
        return {
            "status": "unhealthy",
            "error": str(e),
            "timestamp": datetime.now().isoformat()
        }

@router.get("/status")
async def detailed_status():
    """详细状态信息"""
    try:
        # MDB连接测试
        mdb_test = mdb_client.test_connection()
        
        # 获取连接信息
        conn_info = mdb_client.get_connection_info()
        
        return {
            "service": "MySuite Server6 - MDB Gateway",
            "version": "1.0.0",
            "status": "running",
            "uptime_seconds": time.time() - start_time,
            "platform": {
                "system": platform.system(),
                "release": platform.release(),
                "version": platform.version(),
                "machine": platform.machine(),
                "processor": platform.processor(),
                "python_version": platform.python_version()
            },
            "mdb": {
                "available": mdb_test["success"],
                "connection_test": mdb_test,
                "connection_info": conn_info.dict()
            },
            "endpoints": {
                "mdb_test": "/mdb/test",
                "mdb_query": "/mdb/query",
                "mdb_insert": "/mdb/insert",
                "mdb_update": "/mdb/update",
                "mdb_delete": "/mdb/delete",
                "health": "/health",
                "docs": "/docs"
            },
            "timestamp": datetime.now().isoformat()
        }
        
    except Exception as e:
        logger.error(f"状态检查失败: {e}")
        return {
            "status": "error",
            "error": str(e),
            "timestamp": datetime.now().isoformat()
        } 