# 2025/06/27+13：52 +MDB网关API路由重构
# server6/app/routers/mdb_api.py
# MDB数据库API路由 - 重构为面向 `元作業時間` 表的专用接口

import logging
from typing import List, Dict, Any
from fastapi import APIRouter, HTTPException, Depends, Body
from fastapi.responses import JSONResponse
from datetime import date

from ..models.mdb_models import (
    EntryRecordCreate, EntryRecordUpdate, QueryByEmployee, QueryByDate
)
from ..models.api_models import APIResponse, OperationResult
from ..core.mdb_client import MDBClient, COL_EMPLOYEE_ID, COL_DATE, COL_ID

logger = logging.getLogger(__name__)
router = APIRouter(prefix="/mdb", tags=["MDB Entries"])

# 全局MDB客户端实例
# Depends() 可以用于缓存或依赖注入，这里我们直接实例化
def get_mdb_client():
    return MDBClient()

@router.get("/test", summary="测试MDB连接")
async def test_connection(client: MDBClient = Depends(get_mdb_client)):
    """
    测试后端与MDB数据库文件的连接是否正常。
    这将通过 `win32com` 尝试启动 Access 应用并访问数据库。
    """
    try:
        result = client.test_connection()
        if result.get("success"):
            return JSONResponse(status_code=200, content=result)
        else:
            return JSONResponse(status_code=500, content=result)
    except Exception as e:
        logger.error(f"连接测试失败: {e}", exc_info=True)
        raise HTTPException(status_code=500, detail=f"内部服务器错误: {e}")

@router.post("/entries/query", summary="查询员工记录")
async def query_entries(
    params: QueryByEmployee, 
    client: MDBClient = Depends(get_mdb_client)
):
    """
    根据员工ID和日期范围查询 `元作業時間` 表中的记录。
    """
    try:
        # 构建安全的WHERE子句，并使用正确的日期格式 YYYY/MM/DD
        start_date_str = params.start_date.strftime('%Y/%m/%d')
        end_date_str = params.end_date.strftime('%Y/%m/%d')
        where_clause = (
            f"[{COL_EMPLOYEE_ID}] = '{params.employee_id}' AND "
            f"[{COL_DATE}] >= #{start_date_str}# AND "
            f"[{COL_DATE}] <= #{end_date_str}#"
        )
        
        records = client.fetch_records(where_clause)
        return APIResponse(
            status="success",
            message=f"查询成功，找到 {len(records)} 条记录",
            data=records
        )
    except Exception as e:
        logger.error(f"记录查询失败: {e}", exc_info=True)
        # 返回一个包含错误信息的响应，而不是抛出HTTPException
        return APIResponse(
            status="error",
            message=f"查询失败: {str(e)}",
            data=[]
        )

@router.post("/entries/query_by_date", summary="按日期范围查询所有记录")
async def query_entries_by_date(
    params: QueryByDate,
    client: MDBClient = Depends(get_mdb_client)
):
    """
    根据日期范围查询 `元作業時間` 表中的所有记录，不限制员工。
    """
    try:
        # 使用正确的日期格式 YYYY/MM/DD
        start_date_str = params.start_date.strftime('%Y/%m/%d')
        end_date_str = params.end_date.strftime('%Y/%m/%d')
        where_clause = (
            f"[{COL_DATE}] >= #{start_date_str}# AND "
            f"[{COL_DATE}] <= #{end_date_str}#"
        )
        
        records = client.fetch_records(where_clause)
        return APIResponse(
            status="success",
            message=f"按日期查询成功，找到 {len(records)} 条记录",
            data=records
        )
    except Exception as e:
        logger.error(f"按日期查询记录失败: {e}", exc_info=True)
        # 返回错误响应而不是抛出异常
        return APIResponse(
            status="error",
            message=f"查询失败: {str(e)}",
            data=[]
        )

@router.post("/entries/query_bulk_fast", summary="快速批量查询指定日期的所有记录")
async def query_entries_bulk_fast(
    params: QueryByDate,
    client: MDBClient = Depends(get_mdb_client)
):
    """
    快速查询指定日期范围的所有记录，不按员工分组。
    这是性能优化的版本，用于替代逐个员工查询。
    """
    try:
        # 使用正确的日期格式 YYYY/MM/DD
        start_date_str = params.start_date.strftime('%Y/%m/%d')
        end_date_str = params.end_date.strftime('%Y/%m/%d')
        where_clause = (
            f"[{COL_DATE}] >= #{start_date_str}# AND "
            f"[{COL_DATE}] <= #{end_date_str}#"
        )
        
        records = client.fetch_records(where_clause)
        return APIResponse(
            status="success",
            message=f"快速批量查询成功，找到 {len(records)} 条记录",
            data=records
        )
    except Exception as e:
        logger.error(f"快速批量查询失败: {e}", exc_info=True)
        return APIResponse(
            status="error",
            message=f"快速批量查询失败: {str(e)}",
            data=[]
        )

@router.get("/employees/distinct", summary="获取所有唯一的员工ID")
async def get_distinct_employees(client: MDBClient = Depends(get_mdb_client)):
    """
    从 `元作業時間` 表中查询并返回所有唯一的 `従業員ｺｰﾄﾞ` 列表。
    """
    try:
        employee_ids = client.fetch_distinct_employee_ids()
        return APIResponse(
            status="success",
            message=f"查询成功，找到 {len(employee_ids)} 个唯一的员工ID",
            data=employee_ids
        )
    except Exception as e:
        logger.error(f"查询唯一员工ID失败: {e}", exc_info=True)
        raise HTTPException(status_code=500, detail=f"查询失败: {e}")

@router.post("/entries/insert", summary="插入新记录", response_model=OperationResult)
async def insert_entry(
    entry_data: EntryRecordCreate,
    client: MDBClient = Depends(get_mdb_client)
):
    """
    向 `元作業時間` 表中插入一条新记录。
    成功后会返回新记录的自增 `ID`。
    """
    try:
        # 转换为字典并确保日期格式正确
        insert_data = entry_data.dict()
        
        # 确保日期格式正确 - 转换为YYYY/MM/DD格式
        if 'entry_date' in insert_data and isinstance(insert_data['entry_date'], date):
            insert_data['entry_date'] = insert_data['entry_date'].strftime('%Y/%m/%d')
        
        result = client.insert_record(insert_data)
        return result
    except Exception as e:
        logger.error(f"记录插入失败: {e}", exc_info=True)
        raise HTTPException(status_code=500, detail=f"插入失败: {e}")

@router.put("/entries/update/{external_id}", summary="更新指定记录", response_model=OperationResult)
async def update_entry(
    external_id: int,
    entry_data: EntryRecordUpdate,
    client: MDBClient = Depends(get_mdb_client)
):
    """
    根据MDB中的主键 `ID` (external_id) 更新一条记录。
    只需在请求体中提供需要修改的字段。
    """
    try:
        update_data = entry_data.dict(exclude_unset=True)
        if not update_data:
            raise HTTPException(status_code=400, detail="请求体中没有任何需要更新的字段")
        
        # 确保日期格式正确
        if 'entry_date' in update_data and isinstance(update_data['entry_date'], date):
            update_data['entry_date'] = update_data['entry_date'].strftime('%Y/%m/%d')
            
        result = client.update_record(external_id, update_data)
        return result
    except Exception as e:
        logger.error(f"记录更新失败 (ID: {external_id}): {e}", exc_info=True)
        raise HTTPException(status_code=500, detail=f"更新失败: {e}")

@router.delete("/entries/delete/{external_id}", summary="删除指定记录", response_model=OperationResult)
async def delete_entry(
    external_id: int,
    client: MDBClient = Depends(get_mdb_client)
):
    """
    根据MDB中的主键 `ID` (external_id) 删除一条记录。
    """
    try:
        result = client.delete_record(external_id)
        return result
    except Exception as e:
        logger.error(f"记录删除失败 (ID: {external_id}): {e}", exc_info=True)
        raise HTTPException(status_code=500, detail=f"删除失败: {e}")

# 20250708+11:30 + 新增一个专门用于快速获取ID列表的API，使用DAO以提高性能
@router.post("/entries/get_ids_in_range",
             summary="快速获取日期范围内的所有记录ID",
             response_model=APIResponse[List[int]])
async def get_entry_ids_in_range(query: QueryByDate, client: MDBClient = Depends(get_mdb_client)):
    """
    ## 2025/07/04 + 17：30 + f5优化: 新增高性能ID获取接口
    
    使用 `win32com` 和 `DAO` 引擎直接从MDB文件中快速提取指定日期范围内的所有记录的 **主键ID**。
    """
    try:
        start_date_str = query.start_date.strftime('%Y/%m/%d')
        end_date_str = query.end_date.strftime('%Y/%m/%d')
        
        ids = client.get_entry_ids_by_date_range_dao(start_date_str, end_date_str)
        
        return APIResponse(
            status="success",
            message=f"成功获取 {len(ids)} 个记录ID",
            data=ids
        )
    except Exception as e:
        logger.error(f"快速获取ID列表失败: {e}", exc_info=True)
        raise HTTPException(status_code=500, detail=f"内部服务器错误: {e}") 