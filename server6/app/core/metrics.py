# server6/app/core/metrics.py
# 简单的指标收集器

import time
import logging
from datetime import datetime
from typing import Dict, Any

logger = logging.getLogger(__name__)

class MetricsCollector:
    """简单的指标收集器"""
    
    def __init__(self):
        self.start_time = time.time()
        self.request_count = 0
        self.error_count = 0
        self.response_times = []
        
    def record_request(self, response_time: float = 0.0, error: bool = False):
        """记录请求指标"""
        self.request_count += 1
        if error:
            self.error_count += 1
        if response_time > 0:
            self.response_times.append(response_time)
    
    def get_metrics(self) -> Dict[str, Any]:
        """获取指标数据"""
        uptime = time.time() - self.start_time
        avg_response_time = sum(self.response_times) / len(self.response_times) if self.response_times else 0.0
        
        return {
            "uptime_seconds": uptime,
            "total_requests": self.request_count,
            "error_count": self.error_count,
            "success_rate": (self.request_count - self.error_count) / self.request_count if self.request_count > 0 else 1.0,
            "average_response_time": avg_response_time,
            "last_updated": datetime.now().isoformat()
        }
