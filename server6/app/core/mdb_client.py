# 2025/06/27+13：52 +MDB网关win32com核心重构
# server6/app/core/mdb_client.py
# MDB数据库客户端 - 完全基于win32com重构，确保稳定性和功能正确性

import logging
import platform
import time
from typing import List, Dict, Any, Optional
from datetime import datetime, date

# 确保在Windows环境下运行
if platform.system() == "Windows":
    import pythoncom
    import win32com.client

from config.config import MDB_FILE_PATH, IS_WINDOWS
from ..models.api_models import OperationResult

logger = logging.getLogger(__name__)

# MDB数据库中的日文字段名 (必须完全匹配)
# 这些是从 db_utils.py 和 xmlinit.py 中提取的
TABLE_NAME = "元作業時間"
COL_ID = "ID"
COL_EMPLOYEE_ID = "従業員ｺｰﾄﾞ"
COL_DATE = "日付"
COL_MODEL = "機種"
COL_NUMBER = "号機"
COL_FACTORY_NUMBER = "工場製番"
COL_PROJECT_NUMBER = "工事番号"
COL_UNIT_NUMBER = "ﾕﾆｯﾄ番号"
COL_CATEGORY = "区分"
COL_ITEM = "項目"
COL_TIME = "時間"
COL_DEPARTMENT = "所属ｺｰﾄﾞ"

# 所有需要查询的列
ALL_COLUMNS = [
    COL_ID, COL_EMPLOYEE_ID, COL_DATE, COL_MODEL, COL_NUMBER,
    COL_FACTORY_NUMBER, COL_PROJECT_NUMBER, COL_UNIT_NUMBER,
    COL_CATEGORY, COL_ITEM, COL_TIME, COL_DEPARTMENT
]

class MDBClient:
    """
    MDB数据库客户端 (win32com 实现)
    专门用于操作 `元作業時間` 表
    """
    def __init__(self):
        if not IS_WINDOWS:
            logger.warning("警告: MDBClient 正在非 Windows 环境下初始化。所有操作将失败。")
        self.db_path = MDB_FILE_PATH

    def _execute_in_com_thread(self, func, *args, **kwargs):
        """在独立的COM线程中安全地执行数据库操作"""
        if not IS_WINDOWS:
            raise RuntimeError("win32com 只能在 Windows 上运行。")

        pythoncom.CoInitialize()
        access = None
        db = None
        max_retries = 3
        retry_delay = 1  # 1秒延迟
        
        for attempt in range(max_retries):
            try:
                # 尝试创建Access应用实例
                try:
                    access = win32com.client.Dispatch("Access.Application")
                except Exception as e:
                    logger.error(f"创建Access应用实例失败: {e}", exc_info=True)
                    raise Exception(f"无法启动Access应用: {e}")

                # 尝试打开数据库
                try:
                    access.OpenCurrentDatabase(self.db_path)
                except Exception as e:
                    error_msg = str(e)
                    # 检查是否是"数据库已打开"错误
                    if "既にこのデータベースは開いています" in error_msg or "already open" in error_msg.lower():
                        if attempt < max_retries - 1:
                            logger.warning(f"数据库已打开，等待 {retry_delay} 秒后重试 (尝试 {attempt + 1}/{max_retries})")
                            # 清理当前尝试的资源
                            if access:
                                try:
                                    access.Quit()
                                except:
                                    pass
                            pythoncom.CoUninitialize()
                            # 等待后重试
                            time.sleep(retry_delay)
                            retry_delay *= 2  # 指数退避
                            pythoncom.CoInitialize()
                            continue
                        else:
                            logger.error(f"数据库持续被占用，无法打开: {self.db_path}")
                            raise Exception(f"数据库被占用，请稍后重试: {e}")
                    else:
                        logger.error(f"打开数据库文件失败: {self.db_path}, 错误: {e}", exc_info=True)
                        raise Exception(f"无法打开MDB文件: {e}")

                # 尝试获取数据库对象
                try:
                    db = access.CurrentDb()
                except Exception as e:
                    logger.error(f"获取数据库对象失败: {e}", exc_info=True)
                    raise Exception(f"无法访问数据库对象: {e}")

                # 执行用户函数
                try:
                    result = func(db, *args, **kwargs)
                    return result
                except Exception as e:
                    logger.error(f"执行数据库操作失败: {e}", exc_info=True)
                    raise Exception(f"数据库操作执行失败: {e}")

            except Exception as e:
                if attempt < max_retries - 1:
                    logger.warning(f"数据库操作失败，重试中 (尝试 {attempt + 1}/{max_retries}): {e}")
                    # 清理资源
                    if access:
                        try:
                            access.Quit()
                        except:
                            pass
                    pythoncom.CoUninitialize()
                    time.sleep(retry_delay)
                    retry_delay *= 2
                    pythoncom.CoInitialize()
                    continue
                else:
                    logger.error(f"win32com 操作最终失败: {e}", exc_info=True)
                    raise e
            finally:
                # 安全清理资源
                if access:
                    try:
                        # 检查Access应用是否仍然有效
                        if hasattr(access, 'CurrentDb'):
                            try:
                                # 尝试关闭当前数据库
                                access.CloseCurrentDatabase()
                            except Exception as close_db_error:
                                logger.warning(f"关闭当前数据库时出错 (可忽略): {close_db_error}")
                        
                        # 尝试退出Access应用
                        try:
                            access.Quit()
                        except Exception as quit_error:
                            logger.warning(f"退出Access应用时出错 (可忽略): {quit_error}")
                            
                    except Exception as e:
                        logger.warning(f"清理Access应用时出错 (可忽略): {e}")
                
                # 清理COM
                try:
                    pythoncom.CoUninitialize()
                except Exception as com_error:
                    logger.warning(f"清理COM时出错 (可忽略): {com_error}")

    def test_connection(self) -> Dict[str, Any]:
        """测试MDB文件是否可访问"""
        if not IS_WINDOWS:
            return {"success": False, "message": "非Windows环境，跳过测试"}
        
        def _test(db):
            try:
                # 简单查询表的数量来测试连接
                rs = db.TableDefs
                return {"success": True, "message": "连接成功", "table_count": rs.Count}
            except Exception as e:
                logger.error(f"测试连接时出错: {e}", exc_info=True)
                return {"success": False, "message": f"连接测试失败: {e}"}

        try:
            return self._execute_in_com_thread(_test)
        except Exception as e:
            return {"success": False, "message": f"连接测试异常: {e}"}

    def fetch_records(self, where_clause: str) -> List[Dict[str, Any]]:
        """根据条件查询记录"""
        def _fetch(db, where_clause):
            try:
                columns_str = ", ".join(ALL_COLUMNS)
                sql = f"SELECT {columns_str} FROM [{TABLE_NAME}] WHERE {where_clause}"
                logger.info(f"Executing fetch query: {sql}")
                
                rs = None
                records = []
                field_map_reverse = {
                    COL_ID: 'external_id', COL_EMPLOYEE_ID: 'employee_id', COL_DATE: 'entry_date',
                    COL_MODEL: 'model', COL_NUMBER: 'number', COL_FACTORY_NUMBER: 'factory_number',
                    COL_PROJECT_NUMBER: 'project_number', COL_UNIT_NUMBER: 'unit_number',
                    COL_CATEGORY: 'category', COL_ITEM: 'item', COL_TIME: 'duration',
                    COL_DEPARTMENT: 'department'
                }

                try:
                    # 尝试打开记录集
                    rs = db.OpenRecordset(sql)
                except Exception as e:
                    logger.error(f"打开记录集失败: {e}", exc_info=True)
                    return []

                try:
                    # 检查记录集是否为空
                    if rs.EOF and rs.BOF:
                        return []

                    # 移动到第一条记录
                    rs.MoveFirst()
                except Exception as e:
                    logger.error(f"移动到第一条记录失败: {e}", exc_info=True)
                    return []

                # 遍历记录
                while True:
                    try:
                        # 检查是否到达末尾
                        if rs.EOF:
                            break

                        # 处理单条记录
                        try:
                            rec = {}
                            for field_name_jp in ALL_COLUMNS:
                                py_name = field_map_reverse.get(field_name_jp, 'unknown_field')
                                try:
                                    value = rs.Fields(field_name_jp).Value
                                    if py_name == 'entry_date' and hasattr(value, 'strftime'):
                                        value = value.strftime("%Y/%m/%d")
                                    elif hasattr(value, 'isoformat'):
                                        value = value.isoformat()
                                    elif value is None:
                                        if py_name == 'duration': value = 0.0
                                        elif py_name in ['category', 'item']: value = 0
                                        else: value = ""
                                    rec[py_name] = value
                                except Exception as field_error:
                                    logger.warning(f"读取字段 '{field_name_jp}' 出错，使用默认值: {field_error}")
                                    if py_name in ['duration']: rec[py_name] = 0.0
                                    elif py_name in ['category', 'item']: rec[py_name] = 0
                                    else: rec[py_name] = ""
                            
                            # 添加时间戳字段 'ts'，使用当前时间
                            rec['ts'] = datetime.now().isoformat()
                            
                            records.append(rec)
                        except Exception as record_error:
                            logger.error(f"处理单条记录时出错，跳过: {record_error}", exc_info=False)
                        
                        # 移动到下一条记录
                        try:
                            rs.MoveNext()
                        except Exception as move_error:
                            logger.error(f"移动到下一条记录失败，停止遍历: {move_error}")
                            break

                    except Exception as loop_error:
                        logger.error(f"记录遍历循环出错: {loop_error}", exc_info=True)
                        break

                return records

            except Exception as e:
                logger.error(f"fetch_records 执行失败: {e}", exc_info=True)
                return []
            finally:
                # 确保记录集被关闭
                if rs:
                    try:
                        rs.Close()
                    except Exception as close_error:
                        logger.error(f"关闭记录集失败: {close_error}")
            
        return self._execute_in_com_thread(_fetch, where_clause)

    def fetch_distinct_employee_ids(self) -> List[str]:
        """查询所有唯一的员工ID"""
        def _fetch(db):
            sql = f"SELECT DISTINCT [{COL_EMPLOYEE_ID}] FROM [{TABLE_NAME}]"
            logger.info(f"Executing distinct employee ID query: {sql}")
            
            rs = db.OpenRecordset(sql)
            ids = []
            if rs.RecordCount > 0:
                rs.MoveFirst()
                while not rs.EOF:
                    ids.append(rs.Fields(COL_EMPLOYEE_ID).Value)
                    rs.MoveNext()
            rs.Close()
            return ids
            
        return self._execute_in_com_thread(_fetch)

    def insert_record(self, record_data: Dict[str, Any]) -> OperationResult:
        """
        插入一条新记录并返回其自增ID (`external_id`)
        """
        def _insert(db, record_data):
            # 支持英文字段名和日文字段名的映射
            field_map = {
                # 英文字段名 -> 日文列名
                'employee_id': COL_EMPLOYEE_ID, 'entry_date': COL_DATE, 'model': COL_MODEL,
                'number': COL_NUMBER, 'factory_number': COL_FACTORY_NUMBER, 
                'project_number': COL_PROJECT_NUMBER, 'unit_number': COL_UNIT_NUMBER,
                'category': COL_CATEGORY, 'item': COL_ITEM, 'duration': COL_TIME,
                'department': COL_DEPARTMENT,
                # 日文字段名 -> 日文列名 (直接映射)
                '従業員ｺｰﾄﾞ': COL_EMPLOYEE_ID, '日付': COL_DATE, '機種': COL_MODEL,
                '号機': COL_NUMBER, '工場製番': COL_FACTORY_NUMBER, 
                '工事番号': COL_PROJECT_NUMBER, 'ﾕﾆｯﾄ番号': COL_UNIT_NUMBER,
                '区分': COL_CATEGORY, '項目': COL_ITEM, '時間': COL_TIME,
                '所属ｺｰﾄﾞ': COL_DEPARTMENT
            }
            
            # 获取员工ID和日期，支持英文字段名和日文字段名
            employee_id = record_data.get('employee_id') or record_data.get('従業員ｺｰﾄﾞ')
            entry_date = record_data.get('entry_date') or record_data.get('日付')
            
            # 确保日期格式正确
            if isinstance(entry_date, date):
                entry_date_str = entry_date.strftime('%Y/%m/%d')
            elif isinstance(entry_date, str):
                # 如果是YYYY-MM-DD格式，转换为YYYY/MM/DD
                if '-' in entry_date:
                    entry_date_str = entry_date.replace('-', '/')
                else:
                    entry_date_str = entry_date
            else:
                entry_date_str = str(entry_date)
            
            # 获取其他字段值，支持英文字段名和日文字段名
            model = record_data.get('model') or record_data.get('機種', '')
            number = record_data.get('number') or record_data.get('号機', '')
            factory_number = record_data.get('factory_number') or record_data.get('工場製番', '')
            project_number = record_data.get('project_number') or record_data.get('工事番号', '')
            unit_number = record_data.get('unit_number') or record_data.get('ﾕﾆｯﾄ番号', '')
            category = record_data.get('category') or record_data.get('区分')
            item = record_data.get('item') or record_data.get('項目')
            duration = record_data.get('duration') or record_data.get('時間')
            department = record_data.get('department') or record_data.get('所属ｺｰﾄﾞ')
            
            # 准备SQL INSERT语句，字段名必须和MDB中的完全一致
            sql = f"""INSERT INTO [{TABLE_NAME}]
              ({COL_EMPLOYEE_ID}, {COL_DATE}, {COL_MODEL}, {COL_NUMBER}, 
               {COL_FACTORY_NUMBER}, {COL_PROJECT_NUMBER}, {COL_UNIT_NUMBER}, 
               {COL_CATEGORY}, {COL_ITEM}, {COL_TIME}, {COL_DEPARTMENT})
             VALUES
              ('{employee_id}', 
               #{entry_date_str}#,
               {f"'{model}'" if model else 'NULL'},
               {f"'{number}'" if number else 'NULL'},
               {f"'{factory_number}'" if factory_number else 'NULL'},
               {f"'{project_number}'" if project_number else 'NULL'},
               {f"'{unit_number}'" if unit_number else 'NULL'},
               {f"'{category}'" if category is not None else 'NULL'},
               {f"'{item}'" if item is not None else 'NULL'},
               {duration},
               '{department}'
              )"""
            logger.info(f"Executing insert query: {sql}")
            db.Execute(sql)
            
            # 关键步骤: 获取新插入记录的自增ID
            rs_id = db.OpenRecordset("SELECT @@IDENTITY AS NewID")
            new_id = rs_id.Fields("NewID").Value
            rs_id.Close()
            
            return OperationResult(
                success=True, 
                message="记录插入成功", 
                affected_rows=1, 
                inserted_id=new_id
            )

        return self._execute_in_com_thread(_insert, record_data)

    def update_record(self, external_id: int, record_data: Dict[str, Any]) -> OperationResult:
        """根据 external_id 更新记录"""
        def _update(db, external_id, record_data):
            set_clauses = []
            # 支持英文字段名和日文字段名的映射
            field_map = {
                # 英文字段名 -> 日文列名
                'employee_id': COL_EMPLOYEE_ID, 'entry_date': COL_DATE, 'model': COL_MODEL,
                'number': COL_NUMBER, 'factory_number': COL_FACTORY_NUMBER, 
                'project_number': COL_PROJECT_NUMBER, 'unit_number': COL_UNIT_NUMBER,
                'category': COL_CATEGORY, 'item': COL_ITEM, 'duration': COL_TIME,
                'department': COL_DEPARTMENT,
                # 日文字段名 -> 日文列名 (直接映射)
                '従業員ｺｰﾄﾞ': COL_EMPLOYEE_ID, '日付': COL_DATE, '機種': COL_MODEL,
                '号機': COL_NUMBER, '工場製番': COL_FACTORY_NUMBER, 
                '工事番号': COL_PROJECT_NUMBER, 'ﾕﾆｯﾄ番号': COL_UNIT_NUMBER,
                '区分': COL_CATEGORY, '項目': COL_ITEM, '時間': COL_TIME,
                '所属ｺｰﾄﾞ': COL_DEPARTMENT
            }
            
            for key, value in record_data.items():
                col_name = field_map.get(key)
                if col_name:
                    if isinstance(value, str):
                        set_clauses.append(f"{TABLE_NAME}.{col_name} = '{value}'")
                    elif value is None:
                        set_clauses.append(f"{TABLE_NAME}.{col_name} = NULL")
                    elif key in ['entry_date', '日付']: # 日期格式
                         set_clauses.append(f"{TABLE_NAME}.{col_name} = #{value}#")
                    else:
                        set_clauses.append(f"{TABLE_NAME}.{col_name} = {value}")
            
            if not set_clauses:
                return OperationResult(success=False, message="没有提供任何要更新的字段")

            # 使用Access VBA格式的UPDATE语句
            sql = f"UPDATE DISTINCTROW {TABLE_NAME} SET {', '.join(set_clauses)} WHERE ((({TABLE_NAME}.{COL_ID})={external_id}));"
            logger.info(f"Executing update query: {sql}")
            db.Execute(sql)
            
            # 在win32com中，db.Execute不直接返回影响的行数。
            # 如果需要，可以先SELECT检查记录是否存在。为简化，此处假定操作成功。
            return OperationResult(success=True, message="记录更新成功", affected_rows=1)

        return self._execute_in_com_thread(_update, external_id, record_data)

    def delete_record(self, external_id: int) -> OperationResult:
        """根据 external_id 删除记录"""
        def _delete(db, external_id):
            # 使用Access VBA格式的DELETE语句
            sql = f"DELETE DISTINCTROW {TABLE_NAME}.{COL_ID}, {TABLE_NAME}.{COL_EMPLOYEE_ID}, {TABLE_NAME}.{COL_DATE}, {TABLE_NAME}.{COL_MODEL}, {TABLE_NAME}.{COL_NUMBER}, {TABLE_NAME}.{COL_FACTORY_NUMBER}, {TABLE_NAME}.{COL_PROJECT_NUMBER}, {TABLE_NAME}.{COL_UNIT_NUMBER}, {TABLE_NAME}.{COL_CATEGORY}, {TABLE_NAME}.{COL_ITEM}, {TABLE_NAME}.{COL_TIME}, {TABLE_NAME}.{COL_DEPARTMENT} FROM {TABLE_NAME} WHERE ((({TABLE_NAME}.{COL_ID})={external_id}));"
            logger.info(f"Executing delete query: {sql}")
            db.Execute(sql)
            return OperationResult(success=True, message="记录删除成功", affected_rows=1)
            
        return self._execute_in_com_thread(_delete, external_id)

    def get_entry_ids_by_date_range_dao(self, start_date: str, end_date: str) -> List[int]:
        """
        ## 2025/07/04 + 17：30 + f5优化: 新增高性能ID获取方法
        
        使用 `win32com` 和 `DAO` 引擎直接从MDB文件中快速提取指定日期范围内的所有记录的 **主键ID**。
        这个方法比 `fetch_records` 快得多，因为它只查询一个字段，并且不涉及复杂的记录集遍历。
        
        Args:
            start_date (str): 开始日期 (格式: YYYY/MM/DD)
            end_date (str): 结束日期 (格式: YYYY/MM/DD)
            
        Returns:
            List[int]: 记录ID列表
        """
        def _fetch_ids_dao(db):
            try:
                sql = (
                    f"SELECT [{COL_ID}] FROM [{TABLE_NAME}] "
                    f"WHERE [{COL_DATE}] >= #{start_date}# AND [{COL_DATE}] <= #{end_date}#"
                )
                logger.info(f"Executing DAO ID fetch query: {sql}")

                # 使用DAO Recordset, 4 = dbOpenSnapshot (只读快照，性能较好)
                rs = db.OpenRecordset(sql, 4)
                
                if rs.EOF and rs.BOF:
                    return [] # 没有记录
                
                ids = []
                rs.MoveFirst()
                while not rs.EOF:
                    try:
                        # 直接访问字段值
                        ids.append(rs.Fields(0).Value)
                    except Exception as field_error:
                        logger.warning(f"读取ID字段出错，跳过: {field_error}")
                    rs.MoveNext()
                
                return ids
            except Exception as e:
                logger.error(f"使用DAO获取ID列表时出错: {e}", exc_info=True)
                raise Exception(f"DAO查询失败: {e}") # 重新抛出异常
            finally:
                if 'rs' in locals() and rs is not None:
                    try:
                        rs.Close()
                    except:
                        pass

        return self._execute_in_com_thread(_fetch_ids_dao)
        
    def _get_connection_string(self) -> str:
        """
        获取数据库连接字符串 (备用，当前未使用)
        """
        if platform.system() == "Windows":
            # ... existing code ...
            pass
        else:
            # ... existing code ...
            pass
        return "" 