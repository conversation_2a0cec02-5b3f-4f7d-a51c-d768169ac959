# server6/app/utils/logger.py
# 日志配置工具

import logging
import os
from pathlib import Path
from config.config import LOG_LEVEL, LOG_FORMAT, LOG_FILE

def setup_logging():
    """设置日志配置"""
    # 确保日志目录存在
    log_dir = Path(LOG_FILE).parent
    log_dir.mkdir(parents=True, exist_ok=True)
    
    # 配置日志格式
    logging.basicConfig(
        level=getattr(logging, LOG_LEVEL.upper()),
        format=LOG_FORMAT,
        handlers=[
            logging.FileHandler(LOG_FILE),
            logging.StreamHandler()
        ]
    )
    
    # 设置第三方库日志级别
    logging.getLogger("uvicorn").setLevel(logging.WARNING)
    logging.getLogger("fastapi").setLevel(logging.WARNING)
    
    logger = logging.getLogger(__name__)
    logger.info(f"日志系统已初始化 - 级别: {LOG_LEVEL}, 文件: {LOG_FILE}")
    
    return logger
