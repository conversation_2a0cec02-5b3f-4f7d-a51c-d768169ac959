# server6/app/utils/auth.py
# 简单的API密钥验证

from fastapi import HTTPException, Header
from typing import Optional
from config.config import API_KEY

async def verify_api_key(x_api_key: Optional[str] = Header(None)):
    """验证API密钥"""
    # 在开发环境中，可以跳过API密钥验证
    if not x_api_key:
        # 暂时允许不提供API密钥（开发阶段）
        return True
    
    if x_api_key != API_KEY:
        raise HTTPException(
            status_code=401,
            detail="Invalid API key"
        )
    
    return True 