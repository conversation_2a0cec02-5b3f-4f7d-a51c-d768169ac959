# 2025/06/27+13：52 +MDB网关API模型重构
# server6/app/models/mdb_models.py
# MDB数据库相关的Pydantic模型定义
# 重构为面向 `元作業時間` 表的专用模型

from typing import Optional, List
from pydantic import BaseModel, Field
from datetime import date

class EntryRecordBase(BaseModel):
    """
    元作業時間 表记录的基础模型，包含了所有业务字段。
    这些字段名将用于API的JSON payload，并在MDBClient中映射到日文列名。
    """
    employee_id: str = Field(..., alias="従業員ｺｰﾄﾞ")
    entry_date: date = Field(..., alias="日付")
    model: Optional[str] = Field(None, alias="機種")
    number: Optional[str] = Field(None, alias="号機")
    factory_number: Optional[str] = Field(None, alias="工場製番")
    project_number: Optional[str] = Field(None, alias="工事番号")
    unit_number: Optional[str] = Field(None, alias="ﾕﾆｯﾄ番号")
    category: int = Field(..., alias="区分")
    item: int = Field(..., alias="項目")
    duration: float = Field(..., alias="時間")
    department: str = Field(..., alias="所属ｺｰﾄﾞ")

class EntryRecordCreate(EntryRecordBase):
    """用于创建新记录的模型"""
    pass

class EntryRecordUpdate(BaseModel):
    """
    用于更新记录的模型。所有字段都是可选的，
    因为客户端可能只想更新一个或多个字段。
    """
    employee_id: Optional[str] = Field(None, alias="従業員ｺｰﾄﾞ")
    entry_date: Optional[date] = Field(None, alias="日付")
    model: Optional[str] = Field(None, alias="機種")
    number: Optional[str] = Field(None, alias="号機")
    factory_number: Optional[str] = Field(None, alias="工場製番")
    project_number: Optional[str] = Field(None, alias="工事番号")
    unit_number: Optional[str] = Field(None, alias="ﾕﾆｯﾄ番号")
    category: Optional[int] = Field(None, alias="区分")
    item: Optional[int] = Field(None, alias="項目")
    duration: Optional[float] = Field(None, alias="時間")
    department: Optional[str] = Field(None, alias="所属ｺｰﾄﾞ")

class EntryRecordInDB(EntryRecordBase):
    """代表数据库中完整记录的模型，包含了MDB的自增ID"""
    id: int = Field(..., description="MDB中的主键ID (external_id)")
    
    class Config:
        orm_mode = True
        # Pydantic v2 orm_mode is replaced by from_attributes
        # from_attributes = True

class QueryByEmployee(BaseModel):
    """按员工ID和日期范围查询的模型"""
    employee_id: str = Field(..., description="従業員ｺｰﾄﾞ")
    start_date: date = Field(..., description="开始日期")
    end_date: date = Field(..., description="结束日期")

class QueryByDate(BaseModel):
    start_date: date
    end_date: date 