# server6/app/models/api_models.py
# API响应模型定义

from typing import Optional, Dict, Any, List, Generic, TypeVar
from datetime import datetime
from pydantic import BaseModel, Field
from enum import Enum

T = TypeVar('T')

class ResponseStatus(str, Enum):
    """响应状态枚举"""
    SUCCESS = "success"
    ERROR = "error"
    WARNING = "warning"

class APIResponse(BaseModel, Generic[T]):
    """通用API响应模型"""
    status: ResponseStatus = Field(..., description="响应状态")
    message: str = Field(..., description="响应消息")
    data: Optional[T] = Field(None, description="响应数据")
    timestamp: datetime = Field(default_factory=datetime.now, description="响应时间")
    request_id: Optional[str] = Field(None, description="请求ID")
    
    class Config:
        json_encoders = {
            datetime: lambda v: v.isoformat()
        }

class ErrorResponse(BaseModel):
    """错误响应模型"""
    error_code: str = Field(..., description="错误代码")
    error_message: str = Field(..., description="错误消息")
    details: Optional[Dict[str, Any]] = Field(None, description="错误详情")
    timestamp: datetime = Field(default_factory=datetime.now, description="错误时间")

class HealthStatus(BaseModel):
    """健康检查状态"""
    status: str = Field(..., description="服务状态")
    mdb_available: bool = Field(..., description="MDB是否可用")
    connection_pool: Dict[str, Any] = Field(..., description="连接池状态")
    system_info: Dict[str, Any] = Field(..., description="系统信息")
    uptime: float = Field(..., description="运行时间(秒)")
    last_check: datetime = Field(default_factory=datetime.now, description="最后检查时间")

class PaginatedResponse(BaseModel, Generic[T]):
    """分页响应模型"""
    items: List[T] = Field(..., description="数据项")
    total: int = Field(..., description="总数")
    page: int = Field(..., description="当前页")
    size: int = Field(..., description="页大小")
    pages: int = Field(..., description="总页数")
    has_next: bool = Field(..., description="是否有下一页")
    has_prev: bool = Field(..., description="是否有上一页")

class OperationResult(BaseModel):
    """操作结果模型"""
    success: bool = Field(..., description="操作是否成功")
    affected_rows: int = Field(0, description="影响的行数")
    inserted_id: Optional[int] = Field(None, description="插入的ID")
    message: str = Field("", description="操作消息")
    execution_time: float = Field(0.0, description="执行时间(秒)")

class BatchOperationResult(BaseModel):
    """批量操作结果"""
    total_operations: int = Field(..., description="总操作数")
    successful_operations: int = Field(..., description="成功操作数")
    failed_operations: int = Field(..., description="失败操作数")
    results: List[OperationResult] = Field(..., description="详细结果")
    execution_time: float = Field(..., description="总执行时间(秒)")

class SyncResult(BaseModel):
    """同步结果模型"""
    table_name: str = Field(..., description="表名")
    total_records: int = Field(..., description="总记录数")
    new_records: int = Field(..., description="新记录数")
    updated_records: int = Field(..., description="更新记录数")
    sync_time: datetime = Field(default_factory=datetime.now, description="同步时间")
    execution_time: float = Field(..., description="执行时间(秒)")

class MetricsData(BaseModel):
    """指标数据模型"""
    total_requests: int = Field(0, description="总请求数")
    successful_requests: int = Field(0, description="成功请求数")
    failed_requests: int = Field(0, description="失败请求数")
    average_response_time: float = Field(0.0, description="平均响应时间")
    active_connections: int = Field(0, description="活跃连接数")
    last_reset: datetime = Field(default_factory=datetime.now, description="最后重置时间") 