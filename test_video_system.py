#!/usr/bin/env python3
# test_video_system.py
# 20250619.17:30 视频监控系统测试脚本

import requests
import json
import time
import sys

def test_service_health(port, service_name):
    """测试服务健康状态"""
    try:
        # 视频服务使用不同的健康检查路径
        if port == 8007:
            health_url = f"http://localhost:{port}/api/video/health"
        else:
            health_url = f"http://localhost:{port}/health"
            
        response = requests.get(health_url, timeout=5)
        if response.status_code == 200:
            print(f"✅ {service_name} 健康检查通过")
            return True
        else:
            print(f"❌ {service_name} 健康检查失败: HTTP {response.status_code}")
            return False
    except Exception as e:
        print(f"❌ {service_name} 连接失败: {e}")
        return False

def test_video_api():
    """测试视频API接口"""
    print("\n🎥 测试视频监控API...")
    
    # 测试服务信息
    try:
        response = requests.get("http://localhost:8007/api/video/info", timeout=5)
        if response.status_code == 200:
            info = response.json()
            print(f"✅ 视频服务信息获取成功")
            print(f"   服务名: {info['service_name']}")
            print(f"   版本: {info['version']}")
            print(f"   端口: {info['port']}")
            print(f"   视频配置: {info['video_config']['width']}x{info['video_config']['height']} @ {info['video_config']['fps']}FPS")
        else:
            print(f"❌ 获取视频服务信息失败: HTTP {response.status_code}")
    except Exception as e:
        print(f"❌ 视频服务信息请求失败: {e}")
    
    # 测试统计信息
    try:
        response = requests.get("http://localhost:8007/api/video/stats", timeout=5)
        if response.status_code == 200:
            stats = response.json()
            print(f"✅ 视频统计信息获取成功")
            print(f"   摄像头状态: {stats['data']['camera_status']}")
            print(f"   连接客户端: {stats['data']['connected_clients']}")
            print(f"   总帧数: {stats['data']['total_frames']}")
        else:
            print(f"❌ 获取视频统计信息失败: HTTP {response.status_code}")
    except Exception as e:
        print(f"❌ 视频统计信息请求失败: {e}")
    
    # 测试快照接口
    try:
        response = requests.get("http://localhost:8007/api/video/snapshot", timeout=10)
        if response.status_code == 200:
            snapshot = response.json()
            if snapshot.get("success"):
                print(f"✅ 快照获取成功")
                print(f"   格式: {snapshot['data']['format']}")
                print(f"   编码: {snapshot['data']['encoding']}")
                print(f"   尺寸: {snapshot['data']['width']}x{snapshot['data']['height']}")
            else:
                print(f"⚠️  快照获取失败: {snapshot.get('error', '未知错误')}")
        else:
            print(f"❌ 快照请求失败: HTTP {response.status_code}")
    except Exception as e:
        print(f"❌ 快照请求失败: {e}")

def test_websocket_connection():
    """测试WebSocket连接"""
    print("\n🔌 测试WebSocket连接...")
    
    try:
        import websockets
        import asyncio
        
        async def test_ws():
            uri = "ws://localhost:8007/ws/video"
            try:
                async with websockets.connect(uri) as websocket:
                    print("✅ WebSocket连接成功")
                    
                    # 等待欢迎消息
                    message = await asyncio.wait_for(websocket.recv(), timeout=5.0)
                    data = json.loads(message)
                    
                    if data.get("type") == "welcome":
                        print("✅ 收到欢迎消息")
                        print(f"   客户端ID: {data.get('client_id', 'N/A')}")
                        config = data.get('video_config', {})
                        print(f"   视频配置: {config.get('width', 'N/A')}x{config.get('height', 'N/A')}")
                    
                    # 发送ping消息
                    ping_msg = json.dumps({"type": "ping"})
                    await websocket.send(ping_msg)
                    
                    # 等待pong响应
                    response = await asyncio.wait_for(websocket.recv(), timeout=5.0)
                    pong_data = json.loads(response)
                    
                    if pong_data.get("type") == "pong":
                        print("✅ Ping/Pong测试成功")
                    
                    return True
                    
            except asyncio.TimeoutError:
                print("❌ WebSocket连接超时")
                return False
            except Exception as e:
                print(f"❌ WebSocket连接失败: {e}")
                return False
        
        # 运行异步测试
        result = asyncio.run(test_ws())
        return result
        
    except ImportError:
        print("⚠️  websockets模块未安装，跳过WebSocket测试")
        return True
    except Exception as e:
        print(f"❌ WebSocket测试失败: {e}")
        return False

def main():
    """主测试函数"""
    print("🚀 MySuite 视频监控系统测试")
    print("=" * 50)
    
    # 测试所有服务健康状态
    print("\n🏥 测试服务健康状态...")
    services = [
        (8003, "主服务"),
        (8005, "聊天微服务"),
        (8006, "认证微服务"),
        (8007, "视频监控微服务")
    ]
    
    all_healthy = True
    for port, name in services:
        if not test_service_health(port, name):
            all_healthy = False
    
    if not all_healthy:
        print("\n❌ 部分服务未正常运行，请检查服务状态")
        sys.exit(1)
    
    # 测试视频API
    test_video_api()
    
    # 测试WebSocket连接
    test_websocket_connection()
    
    print("\n🎉 测试完成！")
    print("\n📋 使用说明:")
    print("1. 启动客户端: python client/client_fixed.py")
    print("2. 使用员工ID '215829' 和密码 'test123' 登录")
    print("3. 点击第3个标签页 '视频监控'")
    print("4. 点击 '连接视频' 按钮开始视频监控")
    print("5. 点击 '获取快照' 获取当前画面")
    
    print("\n🔧 管理命令:")
    print("- 查看视频服务日志: tail -f logs/video_service.log")
    print("- 停止所有服务: ./start_microservices.sh stop")
    print("- 重启所有服务: ./start_microservices.sh restart")

if __name__ == "__main__":
    main() 