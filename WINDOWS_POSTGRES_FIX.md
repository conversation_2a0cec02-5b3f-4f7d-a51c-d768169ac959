# Windows 10 PostgreSQL 17.5 远程连接修复指南

**创建时间**: 25/06/25 17:35  
**目的**: 修复PostgreSQL 16升级到17后丢失的远程连接配置

## 🔧 需要在Windows 10电脑(************)上执行的操作

### 步骤1: 找到PostgreSQL 17配置文件

1. 打开文件资源管理器
2. 导航到: `C:\Program Files\PostgreSQL\17\data\`
3. 查找以下两个文件:
   - `pg_hba.conf` (认证配置)
   - `postgresql.conf` (主配置)

### 步骤2: 编辑 pg_hba.conf 文件

1. **右键 -> 以管理员身份打开记事本**
2. 在记事本中打开 `pg_hba.conf`
3. 在文件末尾添加以下行:

```conf
# 允许局域网连接 - 25/06/25 17:35 添加
host    all             all             ***********/24          md5
```

4. 保存文件

### 步骤3: 编辑 postgresql.conf 文件

1. 在记事本中打开 `postgresql.conf`
2. 找到 `#listen_addresses = 'localhost'` 这一行
3. 将其修改为:

```conf
listen_addresses = '*'          # 25/06/25 17:35 允许所有IP连接
```

4. 确认端口设置:

```conf
port = 5432                     # 25/06/25 17:35 确认端口
```

5. 保存文件

### 步骤4: 重启PostgreSQL服务

1. 按 `Win + R`，输入 `services.msc`
2. 找到 `PostgreSQL Database Server 17` 服务
3. 右键 -> 重新启动

### 步骤5: 检查Windows防火墙

1. 打开控制面板 -> Windows Defender 防火墙
2. 点击"允许应用或功能通过Windows Defender防火墙"
3. 确保PostgreSQL或端口5432被允许

## 🔍 验证修复

在Ubuntu电脑上运行:
```bash
python test_remote_postgres.py
```

如果显示连接成功，则修复完成。

## ⚠️ 替代配置 (如果上述方法不工作)

在pg_hba.conf中尝试更宽松的配置:
```conf
# 临时允许所有连接 - 测试用
host    all             all             0.0.0.0/0               md5
```

## 🚨 安全提醒

- 生产环境中应该限制具体的IP段而不是使用 0.0.0.0/0
- 确保使用强密码
- 定期更新PostgreSQL版本

---
*此配置修复了PostgreSQL 16到17升级后的远程连接问题* 