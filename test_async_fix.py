#!/usr/bin/env python3
"""
异步调度修复测试脚本
2025/07/08 + 17:15 + 测试_schedule_async_task方法的修复
"""

import sys
import ast
from pathlib import Path

def test_async_scheduling_fix():
    """测试异步调度修复"""
    program1_path = Path("client/program1.py")
    
    if not program1_path.exists():
        print("❌ client/program1.py 文件不存在")
        return False
    
    print("🧪 开始异步调度修复测试...")
    print("=" * 50)
    
    with open(program1_path, 'r', encoding='utf-8') as f:
        content = f.read()
    
    # 1. 检查新的异步调度方法
    print("🔍 检查异步调度方法...")
    
    # 检查关键的修复内容
    async_fixes = [
        "asyncio.new_event_loop()",
        "asyncio.set_event_loop(loop)",
        "loop.run_until_complete(coro)",
        "QtCore.QMetaObject.invokeMethod",
        "QtCore.Qt.ConnectionType.QueuedConnection",
        "QtCore.Q_ARG(str,",
        "finally:",
        "loop.close()"
    ]
    
    missing_fixes = []
    for fix in async_fixes:
        if fix in content:
            print(f"✅ 找到修复: {fix}")
        else:
            print(f"❌ 缺少修复: {fix}")
            missing_fixes.append(fix)
    
    if missing_fixes:
        print(f"❌ 缺少 {len(missing_fixes)} 个异步修复")
        return False
    
    # 2. 检查是否移除了有问题的代码
    print("\n🔍 检查是否移除了有问题的代码...")
    
    problematic_patterns = [
        "asyncio.get_event_loop()",
        "loop.is_running()",
        "asyncio.create_task(coro)",
        "asyncio.run(coro)"
    ]
    
    for pattern in problematic_patterns:
        if pattern in content:
            print(f"⚠️  仍然存在有问题的代码: {pattern}")
        else:
            print(f"✅ 已移除有问题的代码: {pattern}")
    
    # 3. 检查调用位置
    print("\n🔍 检查异步调度调用位置...")
    
    call_patterns = [
        "QtCore.QTimer.singleShot(100, lambda: self._schedule_async_task(self._load_timeprotab_data_for_table1(target_month)))",
        "QtCore.QTimer.singleShot(100, lambda: self._schedule_async_task(self._load_entries_data_for_table3(target_month)))",
        "QtCore.QTimer.singleShot(100, lambda: self._schedule_async_task(self._load_available_months_from_db()))",
        "QtCore.QTimer.singleShot(100, lambda: self._schedule_async_task(self._load_chart_data_from_db("
    ]
    
    for pattern in call_patterns:
        if pattern in content:
            print(f"✅ 找到正确的调用: {pattern[:60]}...")
        else:
            print(f"⚠️  缺少调用: {pattern[:60]}...")
    
    # 4. 检查方法签名
    print("\n🔍 检查方法签名...")
    
    if "def _schedule_async_task(self, coro):" in content:
        print("✅ _schedule_async_task 方法签名正确")
    else:
        print("❌ _schedule_async_task 方法签名错误")
        return False
    
    # 5. 检查错误处理
    print("\n🔍 检查错误处理...")
    
    error_handling = [
        "except Exception as e:",
        "❌ 异步任务执行失败:",
        "❌ 异步任务调度失败:"
    ]
    
    for handling in error_handling:
        if handling in content:
            print(f"✅ 找到错误处理: {handling}")
        else:
            print(f"⚠️  缺少错误处理: {handling}")
    
    print("\n" + "=" * 50)
    print("📋 异步调度修复测试结果:")
    
    if missing_fixes:
        print("❌ 异步调度修复不完整")
        return False
    else:
        print("✅ 异步调度修复完成！")
        
        print("\n💡 修复要点:")
        print("   1. ✅ 在新线程中创建独立的事件循环")
        print("   2. ✅ 使用 loop.run_until_complete() 运行协程")
        print("   3. ✅ 使用 QMetaObject.invokeMethod 线程安全地记录错误")
        print("   4. ✅ 正确关闭事件循环资源")
        print("   5. ✅ 移除了有问题的 asyncio.create_task() 调用")
        
        print("\n🚀 现在异步任务应该能够正常运行，不再出现:")
        print("   - RuntimeError: no running event loop")
        print("   - RuntimeWarning: coroutine was never awaited")
        
        return True

if __name__ == "__main__":
    success = test_async_scheduling_fix()
    sys.exit(0 if success else 1) 