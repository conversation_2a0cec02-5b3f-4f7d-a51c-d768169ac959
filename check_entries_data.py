#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
检查entries表中的实际数据
"""

import asyncio
import asyncpg
import json
from datetime import datetime

async def check_entries_data():
    """检查entries表中的实际数据"""
    
    # 数据库连接配置
    DATABASE_URL = "***************************************************/imdb"
    
    try:
        # 连接数据库
        conn = await asyncpg.connect(DATABASE_URL)
        
        print("🔍 检查entries表中的数据")
        print("=" * 50)
        
        # 查询最新的几条记录
        rows = await conn.fetch("""
            SELECT id, employee_id, category, item, source, external_id, created_at
            FROM entries 
            WHERE employee_id = '215829'
            ORDER BY created_at DESC 
            LIMIT 5
        """)
        
        if not rows:
            print("❌ 未找到employee_id='215829'的记录")
            return
        
        print(f"📊 找到 {len(rows)} 条记录:")
        print()
        
        for i, row in enumerate(rows, 1):
            print(f"记录 {i}:")
            print(f"  ID: {row['id']}")
            print(f"  Employee ID: {row['employee_id']}")
            print(f"  Category: {row['category']} (类型: {type(row['category'])})")
            print(f"  Item: {row['item']} (类型: {type(row['item'])})")
            print(f"  Source: {row['source']}")
            print(f"  External ID: {row['external_id']}")
            print(f"  Created At: {row['created_at']}")
            print()
        
        # 检查队列状态
        print("🔍 检查entries_push_queue表:")
        print("=" * 30)
        
        queue_rows = await conn.fetch("""
            SELECT queue_id, entry_id, operation_type, synced, created_at
            FROM entries_push_queue 
            WHERE entry_id IN (
                SELECT id FROM entries WHERE employee_id = '215829' ORDER BY created_at DESC LIMIT 5
            )
            ORDER BY created_at DESC
        """)
        
        if queue_rows:
            print(f"📊 找到 {len(queue_rows)} 条队列记录:")
            for row in queue_rows:
                print(f"  Queue ID: {row['queue_id']}, Entry ID: {row['entry_id']}, Operation: {row['operation_type']}, Synced: {row['synced']}")
        else:
            print("❌ 未找到队列记录")
        
        await conn.close()
        
    except Exception as e:
        print(f"❌ 检查失败: {e}")

if __name__ == "__main__":
    asyncio.run(check_entries_data()) 