# 🎥 USB摄像头黑白条纹问题解决方案

## 问题描述
USB Web摄像头接入后显示画面是乱码状的黑白条纹。

## ✅ 解决方案

### 1. 问题诊断
使用摄像头检测工具诊断问题：
```bash
python camera_device_test.py
```

### 2. 主要问题和修复

#### 问题1：摄像头索引错误
- **现象**: 系统中没有`/dev/video0`，只有`/dev/video1`和`/dev/video2`
- **解决**: 修改配置文件使用正确的摄像头索引

```python
# server4/app/config.py
CAMERA_INDEX: int = 1  # 使用video1设备
```

#### 问题2：摄像头初始化不稳定
- **现象**: 第一次初始化可能失败或产生错误格式
- **解决**: 增强摄像头初始化逻辑，支持多后端尝试和格式检测

#### 问题3：用户权限不足
- **现象**: 用户不在video组中，无法完全访问摄像头
- **解决**: 
```bash
sudo usermod -a -G video $USER
# 然后重新登录
```

### 3. 修复后的功能

#### ✅ 摄像头状态检测
```bash
curl http://localhost:8007/api/video/camera/test
```
返回：
```json
{
    "success": true,
    "data": {
        "camera_status": "connected",
        "camera_info": {
            "default_width": 640,
            "default_height": 480,
            "default_fps": 30.0,
            "best_resolution": [640, 480]
        },
        "total_frames": 97,
        "fps_actual": 16.5,
        "frame_available": true,
        "frame_size_kb": 47
    }
}
```

#### ✅ 摄像头重启功能
```bash
curl -X POST http://localhost:8007/api/video/camera/restart
```

#### ✅ 客户端重启按钮
在客户端"视频监控"Tab中添加了"重启摄像头"按钮。

## 🔧 技术改进

### 1. 增强的摄像头初始化
- 支持多种OpenCV后端 (V4L2, GStreamer, 默认)
- 自动检测最佳分辨率
- 稳定性测试（读取多帧验证）
- 详细的错误日志

### 2. 格式优化
- 设置MJPEG格式（如果支持）
- 优化JPEG编码参数
- 自动曝光和白平衡设置

### 3. 诊断工具
- `camera_device_test.py` - 完整的摄像头诊断工具
- 检测可用设备
- 测试每个设备的兼容性
- 权限检查
- V4L2工具集成

## 🎯 使用指南

### 客户端使用
1. 启动客户端：`python client/client_fixed.py`
2. 登录（员工ID: 215829，密码: test123）
3. 点击"视频监控"标签页
4. 如果画面异常，点击"重启摄像头"按钮

### 服务端管理
```bash
# 查看摄像头状态
curl http://localhost:8007/api/video/camera/test

# 重启摄像头
curl -X POST http://localhost:8007/api/video/camera/restart

# 获取快照
curl http://localhost:8007/api/video/snapshot

# 查看服务日志
tail -f logs/video_service.log
```

## 📊 测试结果

- ✅ 摄像头成功连接 (`/dev/video1`)
- ✅ 正常捕获彩色图像 (640x480, BGR格式)
- ✅ 稳定的帧率 (~16.5 FPS)
- ✅ 快照功能正常 (47KB JPEG)
- ✅ WebSocket实时传输
- ✅ 客户端显示正常

## 🚀 未来优化

1. **自动设备检测**: 启动时自动扫描可用摄像头
2. **热插拔支持**: 支持USB摄像头动态插拔
3. **多摄像头支持**: 同时支持多个摄像头设备
4. **格式自适应**: 根据摄像头能力自动选择最佳格式

---

**问题状态**: ✅ **已解决**  
**摄像头状态**: 🟢 **正常工作**  
**画面质量**: 🎥 **清晰彩色** 