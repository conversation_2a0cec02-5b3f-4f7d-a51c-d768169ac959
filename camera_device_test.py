#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
摄像头设备检测和测试工具
用于诊断USB摄像头连接和格式问题
"""

import cv2
import os
import sys
import subprocess
import time
from pathlib import Path

def print_banner():
    print("=" * 60)
    print("🎥 摄像头设备检测和测试工具")
    print("=" * 60)
    print()

def check_video_devices():
    """检查可用的视频设备"""
    print("📹 检查可用的视频设备...")
    
    video_devices = []
    for i in range(10):  # 检查 /dev/video0 到 /dev/video9
        device_path = f"/dev/video{i}"
        if os.path.exists(device_path):
            video_devices.append((i, device_path))
            print(f"✅ 发现设备: {device_path}")
    
    if not video_devices:
        print("❌ 未发现任何视频设备")
        return []
    
    return video_devices

def check_user_permissions():
    """检查用户权限"""
    print("\n🔐 检查用户权限...")
    
    # 检查当前用户组
    try:
        result = subprocess.run(['groups'], capture_output=True, text=True)
        groups = result.stdout.strip().split()
        
        if 'video' in groups:
            print("✅ 用户在video组中")
            return True
        else:
            print("⚠️  用户不在video组中")
            print("   建议运行: sudo usermod -a -G video $USER")
            print("   然后重新登录")
            return False
    except Exception as e:
        print(f"❌ 检查权限时出错: {e}")
        return False

def test_camera_with_opencv(device_index):
    """使用OpenCV测试摄像头"""
    print(f"\n🎥 测试摄像头 /dev/video{device_index} (OpenCV)...")
    
    # 尝试不同的后端
    backends = [
        (cv2.CAP_V4L2, "V4L2"),
        (cv2.CAP_GSTREAMER, "GStreamer"),
        (cv2.CAP_ANY, "默认")
    ]
    
    for backend, name in backends:
        print(f"   尝试后端: {name}")
        
        try:
            cap = cv2.VideoCapture(device_index, backend)
            
            if cap.isOpened():
                # 获取摄像头信息
                width = cap.get(cv2.CAP_PROP_FRAME_WIDTH)
                height = cap.get(cv2.CAP_PROP_FRAME_HEIGHT)
                fps = cap.get(cv2.CAP_PROP_FPS)
                fourcc = cap.get(cv2.CAP_PROP_FOURCC)
                
                print(f"   ✅ 摄像头打开成功")
                print(f"      默认分辨率: {int(width)}x{int(height)}")
                print(f"      默认FPS: {fps}")
                print(f"      FOURCC: {fourcc}")
                
                # 尝试读取几帧
                success_frames = 0
                for i in range(5):
                    ret, frame = cap.read()
                    if ret and frame is not None:
                        success_frames += 1
                        if i == 0:  # 分析第一帧
                            print(f"      帧格式: {frame.shape}, dtype: {frame.dtype}")
                            print(f"      帧大小: {frame.size} 像素")
                            
                            # 检查是否是有效的彩色图像
                            if len(frame.shape) == 3 and frame.shape[2] == 3:
                                print(f"      ✅ 检测到彩色图像 (BGR)")
                            elif len(frame.shape) == 2:
                                print(f"      ⚠️  检测到灰度图像")
                            else:
                                print(f"      ❌ 异常的图像格式")
                    else:
                        print(f"      ❌ 读取第{i+1}帧失败")
                    
                    time.sleep(0.1)
                
                print(f"      成功读取: {success_frames}/5 帧")
                
                cap.release()
                
                if success_frames >= 3:
                    print(f"   ✅ 摄像头测试通过 (后端: {name})")
                    return True, backend, name
                else:
                    print(f"   ⚠️  摄像头不稳定 (后端: {name})")
            else:
                print(f"   ❌ 无法打开摄像头 (后端: {name})")
                
        except Exception as e:
            print(f"   ❌ 测试出错 (后端: {name}): {e}")
        
        if cap:
            cap.release()
    
    return False, None, None

def test_with_v4l2():
    """使用v4l2-ctl测试摄像头"""
    print("\n🔧 使用v4l2-ctl检查设备...")
    
    try:
        # 检查是否安装了v4l2-ctl
        result = subprocess.run(['which', 'v4l2-ctl'], capture_output=True)
        if result.returncode != 0:
            print("⚠️  v4l2-ctl 未安装，跳过v4l2测试")
            print("   安装命令: sudo apt install v4l-utils")
            return
        
        # 列出所有设备
        result = subprocess.run(['v4l2-ctl', '--list-devices'], 
                              capture_output=True, text=True, timeout=10)
        if result.returncode == 0:
            print("📋 V4L2设备列表:")
            print(result.stdout)
        
        # 检查每个video设备的格式
        for i in range(5):
            device_path = f"/dev/video{i}"
            if os.path.exists(device_path):
                print(f"\n📋 检查设备 {device_path} 支持的格式:")
                result = subprocess.run(['v4l2-ctl', '-d', device_path, '--list-formats-ext'], 
                                      capture_output=True, text=True, timeout=5)
                if result.returncode == 0:
                    print(result.stdout[:500])  # 只显示前500字符
                else:
                    print(f"   无法获取格式信息: {result.stderr}")
    
    except Exception as e:
        print(f"❌ v4l2测试出错: {e}")

def recommend_fixes():
    """推荐修复方案"""
    print("\n" + "=" * 60)
    print("🔧 问题修复建议")
    print("=" * 60)
    print()
    
    print("如果遇到黑白条纹问题，尝试以下解决方案:")
    print()
    print("1. 权限问题:")
    print("   sudo usermod -a -G video $USER")
    print("   然后重新登录")
    print()
    print("2. 摄像头索引问题:")
    print("   修改 server4/app/config.py 中的 CAMERA_INDEX")
    print("   尝试不同的值: 0, 1, 2")
    print()
    print("3. 格式问题:")
    print("   在客户端点击 '重启摄像头' 按钮")
    print("   或调用API: curl -X POST http://localhost:8007/api/video/camera/restart")
    print()
    print("4. 依赖问题:")
    print("   sudo apt update")
    print("   sudo apt install v4l-utils")
    print("   sudo apt install libv4l-dev")
    print()
    print("5. USB摄像头重新插拔:")
    print("   断开USB连接，等待5秒，重新连接")
    print()

def main():
    """主函数"""
    print_banner()
    
    # 检查视频设备
    devices = check_video_devices()
    if not devices:
        recommend_fixes()
        return
    
    # 检查权限
    check_user_permissions()
    
    # 使用v4l2检查
    test_with_v4l2()
    
    # 测试每个设备
    working_devices = []
    for device_index, device_path in devices:
        success, backend, backend_name = test_camera_with_opencv(device_index)
        if success:
            working_devices.append((device_index, backend, backend_name))
    
    # 总结
    print("\n" + "=" * 60)
    print("📊 测试结果总结")
    print("=" * 60)
    
    if working_devices:
        print("✅ 工作正常的摄像头:")
        for device_index, backend, backend_name in working_devices:
            print(f"   /dev/video{device_index} (后端: {backend_name})")
        
        # 推荐配置
        best_device = working_devices[0][0]
        print(f"\n🎯 推荐配置:")
        print(f"   修改 server4/app/config.py:")
        print(f"   CAMERA_INDEX: int = {best_device}")
    else:
        print("❌ 没有找到工作正常的摄像头")
    
    recommend_fixes()

if __name__ == "__main__":
    main() 