# server3/start_auth_service.py
import uvicorn
import sys
from pathlib import Path

# Add the application directory to the Python path
current_dir = Path(__file__).parent
app_dir = current_dir / "app"
sys.path.insert(0, str(current_dir))

def main():
    """Main function to start the auth service."""
    port = 8006
    print("=" * 60)
    print("🚀 Starting MySuite Auth Microservice")
    print("=" * 60)
    print(f"📁 Working Directory: {current_dir}")
    print(f"🌐 Service Port: {port}")
    print(f"📝 API Documentation: http://localhost:{port}/docs")
    print(f"🔍 Health Check: http://localhost:{port}/health")
    print("=" * 60)
    
    try:
        uvicorn.run(
            "app.main:app",
            host="0.0.0.0",
            port=port,
            reload=True,
            log_level="info",
            access_log=True,
            reload_dirs=[str(app_dir)],
            workers=1
        )
    except KeyboardInterrupt:
        print("\n🛑 Auth service stopped by user")
    except Exception as e:
        print(f"❌ Error starting auth service: {e}")
        sys.exit(1)

if __name__ == "__main__":
    main() 