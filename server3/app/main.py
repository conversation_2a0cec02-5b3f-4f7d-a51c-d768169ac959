# server3/app/main.py
import uvicorn
from fastapi import Fast<PERSON><PERSON>, status
from fastapi.middleware.cors import CORSMiddleware
from contextlib import asynccontextmanager

from .database import init_db
# 2025/07/03 + 14:00 + 导入程序授权路由
from .routers import auth_login, hardware_registration, program_authorization

@asynccontextmanager
async def lifespan(app: FastAPI):
    """Handles application startup and shutdown events."""
    print("=== Auth Service Starting Up ===")
    await init_db()
    print("✅ Database for auth service initialized!")
    print("🚀 Auth Service startup complete.")
    yield
    print("=== Auth Service Shutting Down ===")

app = FastAPI(
    lifespan=lifespan,
    title="MySuite Auth API",
    description="Dedicated microservice for user authentication and hardware registration.",
    version="1.0.0"
)

# Allow all origins for simplicity, but you might want to restrict this in production.
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

@app.get("/health", status_code=status.HTTP_200_OK)
async def health_check():
    return {"status": "healthy", "service": "auth-service"}

# Include the authentication and registration routers
app.include_router(auth_login.router, tags=["Authentication"])
app.include_router(hardware_registration.router, tags=["Hardware Fingerprint"])
# 2025/07/03 + 14:00 + 注册程序授权路由
app.include_router(program_authorization.router, tags=["Program Authorization"])

if __name__ == "__main__":
    # This part is for direct execution, e.g., for debugging.
    # The actual service will be started by start_auth_service.py
    uvicorn.run(
        "app.main:app",
        host="127.0.0.1",
        port=8006, # A different port for the auth service
        reload=True,
        log_level="info"
    ) 