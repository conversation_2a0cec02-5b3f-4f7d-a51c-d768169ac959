# server3/app/models.py
from sqlalchemy import Column, String, DateTime, func, LargeBinary
from .database import Base

class User(Base):
    """
    标准用户表 - 用于第二个登录系统（专业JWT注册系统）
    username: 用户自定义用户名
    employee_id: 公司员工ID（可选，用于关联公司账户）
    """
    __tablename__ = "users"
    
    username = Column(String, primary_key=True, index=True, unique=True)
    employee_id = Column(String, nullable=True, index=True)  # 可选的员工ID关联
    encrypted_password = Column(LargeBinary, nullable=False)
    email = Column(String, nullable=True, index=True)
    full_name = Column(String, nullable=True)
    is_active = Column(String, default="true")
    created_at = Column(DateTime(timezone=True), server_default=func.now())

class HardwareFingerprint(Base):
    """
    Represents a registered hardware fingerprint for a user.
    employee_id is the fixed company employee ID.
    """
    __tablename__ = "hardware_fingerprints"
    
    employee_id = Column(String, primary_key=True, index=True)
    fingerprint = Column(String, primary_key=True, index=True)
    registration_date = Column(DateTime(timezone=True), server_default=func.now()) 