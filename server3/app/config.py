# server3/app/config.py

# PostgreSQL数据库配置，用于存储用户和硬件指纹数据
# 25/06/25 17：00 更改postgres 16到17，端口从5435改为5432
DB_HOST = "************"  # 数据库主机地址
DB_PORT = 5432            # 数据库端口 (25/06/25 17：00 从5435改为5432，postgres17.5标准端口)
DB_NAME = "auth"          # 数据库名称 (专门用于认证)
DB_USER = "postgres"      # 数据库用户名
DB_PASS = "pojiami0602"   # 数据库密码

# 构建数据库连接URL
DATABASE_URL = f"postgresql+asyncpg://{DB_USER}:{DB_PASS}@{DB_HOST}:{DB_PORT}/{DB_NAME}"

# JWT 密钥配置
# 强烈建议在生产环境中从环境变量加载此密钥
# 25/06/25 17：00 确保JWT密钥与客户端一致，用于认证token验证
JWT_SECRET_KEY = "your-very-secret-signing-key" # 与客户端保持一致
JWT_ALGORITHM = "HS256"

# 管理员删除密码 (用于硬件指纹管理)
# 25/06/25 17：00 硬件指纹管理功能，需要管理员权限删除绑定设备
ADMIN_DELETE_PASSWORD = "admin123" 