# server3/app/routers/hardware_registration.py
from fastapi import APIRouter, Depends, HTTPException, status
from pydantic import BaseModel
from typing import Optional
import hashlib
from datetime import datetime
import logging
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy.future import select

from ..database import get_db
from ..models import HardwareFingerprint, User
from ..config import ADMIN_DELETE_PASSWORD

logger = logging.getLogger(__name__)
router = APIRouter(prefix="/api/hardware", tags=["Hardware Fingerprint"])

class FingerprintRequest(BaseModel):
    employee_id: str
    password: str
    mac_address: str
    motherboard_uuid: str
    machine_id: str
    cpu_info: Optional[str] = ""
    disk_serial: Optional[str] = ""

class FingerprintDeleteRequest(BaseModel):
    employee_id: str
    admin_password: str

def generate_fingerprint(employee_id: str, mac: str, uuid: str) -> str:
    """
    Generates a hardware fingerprint hash.
    Compatible with client's simplified fingerprint where all fields use MAC address.
    """
    # 客户端使用极简版硬件指纹，所有字段都是MAC地址
    # 为了兼容，我们只使用MAC地址作为主要标识符
    # 格式: employee_id:mac_address:mac_address (因为客户端的motherboard_uuid也是mac_address)
    data = f"{employee_id.lower()}:{mac.lower()}:{mac.lower()}"
    return hashlib.sha256(data.encode('utf-8')).hexdigest()

@router.post("/register", status_code=status.HTTP_201_CREATED)
async def register_fingerprint(request: FingerprintRequest, db: AsyncSession = Depends(get_db)):
    """
    硬件指纹注册 - 独立验证流程：
    1. 验证员工ID和密码（通过加密文件 - 公司内部固定账户）
    2. 直接保存硬件指纹，不依赖用户表
    """
    # 1. 验证员工ID和密码（通过加密文件 - 第一个登录系统）
    from .auth_login import load_employee_mapping
    id_to_employee = load_employee_mapping()
    
    if not id_to_employee:
        raise HTTPException(status_code=500, detail="无法加载员工数据")
    
    if request.employee_id not in id_to_employee:
        raise HTTPException(status_code=status.HTTP_404_NOT_FOUND, detail="员工ID不存在于公司系统中")
    
    employee_info = id_to_employee[request.employee_id]
    if request.password != employee_info["password"]:
        raise HTTPException(status_code=status.HTTP_401_UNAUTHORIZED, detail="密码错误")

    # 2. 检查是否已注册硬件指纹
    fp_result = await db.execute(
        select(HardwareFingerprint).filter(HardwareFingerprint.employee_id == request.employee_id)
    )
    if fp_result.scalars().first():
        raise HTTPException(
            status_code=status.HTTP_409_CONFLICT,
            detail="该员工ID已注册硬件指纹",
        )

    # 3. 生成并保存硬件指纹
    fingerprint_hash = generate_fingerprint(request.employee_id, request.mac_address, request.motherboard_uuid)
    new_fingerprint = HardwareFingerprint(employee_id=request.employee_id, fingerprint=fingerprint_hash)
    db.add(new_fingerprint)
    
    await db.commit()
    logger.info(f"员工ID {request.employee_id} 硬件指纹注册成功")

    return {
        "status": "success", 
        "message": "硬件指纹注册成功",
        "employee_id": request.employee_id,
        "registration_time": datetime.now().isoformat()
    }

@router.post("/verify")
async def verify_fingerprint(request: FingerprintRequest, db: AsyncSession = Depends(get_db)):
    """
    验证硬件指纹 - 独立验证流程：
    1. 验证员工ID和密码（通过加密文件）
    2. 验证硬件指纹
    """
    # 1. 验证员工ID和密码（通过加密文件 - 第一个登录系统）
    from .auth_login import load_employee_mapping
    id_to_employee = load_employee_mapping()
    
    if not id_to_employee:
        raise HTTPException(status_code=500, detail="无法加载员工数据")
    
    if request.employee_id not in id_to_employee:
        raise HTTPException(status_code=status.HTTP_404_NOT_FOUND, detail="员工ID不存在")
    
    employee_info = id_to_employee[request.employee_id]
    if request.password != employee_info["password"]:
        raise HTTPException(status_code=status.HTTP_401_UNAUTHORIZED, detail="密码错误")
    
    # 2. 获取存储的硬件指纹
    fp_result = await db.execute(
        select(HardwareFingerprint).filter(HardwareFingerprint.employee_id == request.employee_id)
    )
    stored_fingerprint = fp_result.scalars().first()
    
    if not stored_fingerprint:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="该员工ID未注册硬件指纹",
        )
        
    # 3. 生成当前硬件指纹并比较
    current_fingerprint_hash = generate_fingerprint(request.employee_id, request.mac_address, request.motherboard_uuid)
    
    if current_fingerprint_hash != stored_fingerprint.fingerprint:
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="硬件指纹验证失败",
        )
        
    return {"status": "success", "message": "硬件指纹验证成功"}

@router.post("/delete", status_code=status.HTTP_200_OK)
async def delete_fingerprint(request: FingerprintDeleteRequest, db: AsyncSession = Depends(get_db)):
    """删除硬件指纹注册（管理员操作）"""
    # 1. 检查管理员密码
    if request.admin_password != ADMIN_DELETE_PASSWORD:
        raise HTTPException(status_code=status.HTTP_403_FORBIDDEN, detail="管理员密码错误")
        
    # 2. 查找并删除硬件指纹
    fp_result = await db.execute(
        select(HardwareFingerprint).filter(HardwareFingerprint.employee_id == request.employee_id)
    )
    fingerprint_to_delete = fp_result.scalars().first()
    
    if not fingerprint_to_delete:
        raise HTTPException(status_code=status.HTTP_404_NOT_FOUND, detail="未找到该用户的硬件指纹")
        
    await db.delete(fingerprint_to_delete)
    await db.commit()
    
    return {
        "status": "success", 
        "message": "硬件指纹删除成功",
        "employee_id": request.employee_id,
        "deletion_time": datetime.now().isoformat()
    }