# 2025/07/03 + 14:00 + 服务器端程序启动权限验证路由
from fastapi import APIRouter, HTTPException, Depends, status
from pydantic import BaseModel
from typing import Dict, List, Optional
import logging
from datetime import datetime

from ..auth.jwt_auth import verify_employee_token
from .auth_login import load_employee_mapping

logger = logging.getLogger(__name__)
router = APIRouter(prefix="/api/program", tags=["Program Authorization"])

class ProgramLaunchRequest(BaseModel):
    program_name: str  # "program1" 或 "program2"
    employee_id: str
    additional_params: Optional[Dict] = None

class ProgramLaunchResponse(BaseModel):
    success: bool
    message: str
    permission_granted: bool
    employee_permission: str
    program_name: str

# 2025/07/03 + 14:00 + 程序权限映射配置
PROGRAM_PERMISSIONS = {
    "program1": ["h9", "h8"],  # 员工操作界面：h9和h8权限可访问
    "program2": ["h9", "h7"],  # PLC编程工具：h9和h7权限可访问
}

# 2025/07/03 + 14:00 + 权限等级描述
PERMISSION_DESCRIPTIONS = {
    "h9": "最高权限 - 可启动所有程序",
    "h8": "高级权限 - 可启动员工操作界面",
    "h7": "专业权限 - 可启动PLC编程工具",
    "normal": "普通权限 - 仅基础功能"
}

@router.post("/authorize", response_model=ProgramLaunchResponse)
async def authorize_program_launch(
    request: ProgramLaunchRequest,
    token_data: dict = Depends(verify_employee_token)
):
    """
    2025/07/03 + 14:00 + 验证员工是否有权限启动指定程序
    这是服务器端的权限验证，确保安全性
    """
    try:
        # 从token中获取员工信息
        token_employee_id = token_data.get("employee_id")
        token_permission = token_data.get("permission", "normal")
        
        # 验证请求中的员工ID与token中的是否匹配
        if request.employee_id != token_employee_id:
            logger.warning(f"员工ID不匹配: 请求={request.employee_id}, Token={token_employee_id}")
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail="员工ID与认证信息不匹配"
            )
        
        # 验证程序名称
        if request.program_name not in PROGRAM_PERMISSIONS:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail=f"未知的程序名称: {request.program_name}"
            )
        
        # 检查权限
        required_permissions = PROGRAM_PERMISSIONS[request.program_name]
        permission_granted = token_permission in required_permissions
        
        # 记录授权尝试
        logger.info(f"程序启动授权请求: 员工={request.employee_id}, 程序={request.program_name}, "
                   f"权限={token_permission}, 结果={'允许' if permission_granted else '拒绝'}")
        
        # 双重验证：从员工数据文件再次确认权限
        employee_mapping = load_employee_mapping()
        if request.employee_id in employee_mapping:
            file_permission = employee_mapping[request.employee_id].get("permission", "normal")
            if file_permission != token_permission:
                logger.error(f"权限不一致: Token权限={token_permission}, 文件权限={file_permission}")
                raise HTTPException(
                    status_code=status.HTTP_403_FORBIDDEN,
                    detail="权限信息不一致，请重新登录"
                )
        
        if permission_granted:
            message = f"授权成功：{PERMISSION_DESCRIPTIONS.get(token_permission, token_permission)}"
        else:
            message = f"权限不足：当前权限({token_permission})无法启动{request.program_name}，需要权限：{required_permissions}"
        
        return ProgramLaunchResponse(
            success=permission_granted,
            message=message,
            permission_granted=permission_granted,
            employee_permission=token_permission,
            program_name=request.program_name
        )
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"程序授权验证失败: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"授权验证失败: {str(e)}"
        )

@router.get("/permissions/{employee_id}")
async def get_employee_program_permissions(
    employee_id: str,
    token_data: dict = Depends(verify_employee_token)
):
    """
    2025/07/03 + 14:00 + 获取员工的程序访问权限列表
    """
    try:
        # 验证请求的员工ID与token匹配
        token_employee_id = token_data.get("employee_id")
        if employee_id != token_employee_id:
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail="只能查询自己的权限信息"
            )
        
        employee_permission = token_data.get("permission", "normal")
        
        # 计算可访问的程序列表
        accessible_programs = []
        for program_name, required_perms in PROGRAM_PERMISSIONS.items():
            if employee_permission in required_perms:
                accessible_programs.append({
                    "program_name": program_name,
                    "description": _get_program_description(program_name),
                    "required_permissions": required_perms
                })
        
        return {
            "employee_id": employee_id,
            "permission": employee_permission,
            "permission_description": PERMISSION_DESCRIPTIONS.get(employee_permission, employee_permission),
            "accessible_programs": accessible_programs,
            "total_programs": len(accessible_programs)
        }
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"获取员工权限失败: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"获取权限信息失败: {str(e)}"
        )

@router.get("/available-programs")
async def get_available_programs():
    """
    2025/07/03 + 14:00 + 获取所有可用程序及其权限要求
    """
    programs = []
    for program_name, required_perms in PROGRAM_PERMISSIONS.items():
        programs.append({
            "program_name": program_name,
            "description": _get_program_description(program_name),
            "required_permissions": required_perms,
            "permission_descriptions": [PERMISSION_DESCRIPTIONS.get(p, p) for p in required_perms]
        })
    
    return {
        "programs": programs,
        "permission_levels": PERMISSION_DESCRIPTIONS
    }

def _get_program_description(program_name: str) -> str:
    """获取程序描述"""
    descriptions = {
        "program1": "员工操作界面 - 工时数据录入和管理",
        "program2": "PLC编程工具 - 设备编程和配置"
    }
    return descriptions.get(program_name, program_name) 