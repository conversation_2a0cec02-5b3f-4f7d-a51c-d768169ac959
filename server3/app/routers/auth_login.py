# 修改14 增加id 密码和跳转界面 - 新增登录验证路由
from fastapi import APIRouter, HTTPException, Depends, status, Request
from typing import Dict, List
from pydantic import BaseModel
import os
import json
import time
from cryptography.fernet import Fernet
from pathlib import Path
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy.future import select
import bcrypt

from ..auth.jwt_auth import verify_token, create_employee_token, create_access_token, get_current_user, verify_employee_token
from ..database import get_db
from ..models import User

router = APIRouter(tags=["auth"])

# 修改14 增加id 密码和跳转界面 - 加密配置
SCRIPT_DIR = Path(__file__).parent.parent
CONFIG_FOLDER = SCRIPT_DIR / "config"
ENCRYPTED_EMPLOYEE_DATA_FILE = CONFIG_FOLDER / "employees.encrypted"

# 修改14 增加id 密码和跳转界面 - 使用指定的密钥
KEY_FOR_DEMO = b'OtFpdMgbd5k7tzakP6b2g8K5gX_0J6E5IikukRnmZGc='

# 修改14 增加id 密码和跳转界面 - 登录请求模型
class LoginRequest(BaseModel):
    employee_id: str
    password: str

class LoginResponse(BaseModel):
    success: bool
    message: str
    employee_id: str = None
    employee_name: str = None
    access_token: str = None
    token_type: str = "bearer"

class UserCreate(BaseModel):
    username: str
    password: str
    email: str = None
    full_name: str = None
    employee_id: str = None  # 可选的员工ID关联

class UserLogin(BaseModel):
    username: str
    password: str

class UserResponse(BaseModel):
    username: str
    email: str = None
    full_name: str = None
    employee_id: str = None
    is_active: str
    created_at: str

class Token(BaseModel):
    access_token: str
    token_type: str

def decrypt_data(encrypted_data: bytes, key: bytes) -> dict:
    """修改14 增加id 密码和跳转界面 - 解密员工数据"""
    f = Fernet(key)
    decrypted_bytes = f.decrypt(encrypted_data)
    return json.loads(decrypted_bytes.decode('utf-8'))

def encrypt_data(data: dict, key: bytes) -> bytes:
    """修改14 增加id 密码和跳转界面 - 加密员工数据"""
    f = Fernet(key)
    json_data = json.dumps(data, ensure_ascii=False)
    return f.encrypt(json_data.encode('utf-8'))

def ensure_employee_data_exists():
    """修改14 增加id 密码和跳转界面 - 确保员工数据文件存在"""
    CONFIG_FOLDER.mkdir(parents=True, exist_ok=True)
    
    if not ENCRYPTED_EMPLOYEE_DATA_FILE.exists():
        # 2025/07/03 + 14:00 + 创建多权限等级的员工数据系统
        employee_data = {
            "mike": {
                "id": "215829",
                "password": "test123",
                "permission": "h9"  # 最高权限，可启动所有UI程序
            },
            "manager": {
                "id": "200001",
                "password": "manager123",
                "permission": "h8"  # 2025/07/03 + 14:00 + 高级权限，可启动员工操作界面
            },
            "engineer": {
                "id": "300001",
                "password": "engineer123", 
                "permission": "h7"  # 2025/07/03 + 14:00 + 专业权限，可启动PLC编程工具
            },
            "operator": {
                "id": "400001",
                "password": "operator123",
                "permission": "normal"  # 普通权限，仅基础功能
            },
            "test_user": {
                "id": "100001", 
                "password": "test456",
                "permission": "normal"  # 普通权限，仅基础功能
            }
        }
        
        encrypted_content = encrypt_data(employee_data, KEY_FOR_DEMO)
        with open(ENCRYPTED_EMPLOYEE_DATA_FILE, 'wb') as f:
            f.write(encrypted_content)
        print(f"创建默认员工数据文件: {ENCRYPTED_EMPLOYEE_DATA_FILE}")

def load_employee_mapping():
    """修改14 增加id 密码和跳转界面 - 加载员工映射数据"""
    ensure_employee_data_exists()
    
    try:
        with open(ENCRYPTED_EMPLOYEE_DATA_FILE, 'rb') as f:
            encrypted_content = f.read()
        
        employee_data = decrypt_data(encrypted_content, KEY_FOR_DEMO)
        
        # 2025/07/03 +13:40+ 构建ID到员工信息的映射，包含权限信息
        id_to_employee = {}
        for name, info in employee_data.items():
            employee_id = info["id"]
            id_to_employee[employee_id] = {
                "name": name,
                "password": info["password"],
                "permission": info.get("permission", "normal")  # 2025/07/03 +13:40+ 默认普通权限
            }
        
        return id_to_employee
    except Exception as e:
        print(f"加载员工数据失败: {e}")
        return {}

@router.post("/login", response_model=LoginResponse)
async def login(request: LoginRequest):
    """修改14 增加id 密码和跳转界面 - 员工登录验证"""
    try:
        # 加载员工数据
        id_to_employee = load_employee_mapping()
        
        if not id_to_employee:
            raise HTTPException(status_code=500, detail="无法加载员工数据")
        
        employee_id = request.employee_id.strip()
        password = request.password
        
        # 验证员工ID是否存在
        if employee_id not in id_to_employee:
            return LoginResponse(
                success=False,
                message="员工ID不存在"
            )
        
        # 验证密码
        employee_info = id_to_employee[employee_id]
        if password != employee_info["password"]:
            return LoginResponse(
                success=False,
                message="密码错误"
            )
        
        # 2025/07/03 +13:40+ 登录成功，在token中包含权限信息
        permission = employee_info.get("permission", "normal")
        access_token = create_employee_token(employee_id, employee_info["name"], permission)
        return LoginResponse(
            success=True,
            message="登录成功",
            employee_id=employee_id,
            employee_name=employee_info["name"],
            access_token=access_token,
            token_type="bearer"
        )
        
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"登录验证失败: {str(e)}")

@router.get("/employee/{employee_id}")
async def get_employee_info(employee_id: str):
    """修改14 增加id 密码和跳转界面 - 获取员工信息"""
    try:
        id_to_employee = load_employee_mapping()
        
        if employee_id not in id_to_employee:
            raise HTTPException(status_code=404, detail="员工不存在")
        
        employee_info = id_to_employee[employee_id]
        return {
            "employee_id": employee_id,
            "employee_name": employee_info["name"],
            "status": "active"
        }
        
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"获取员工信息失败: {str(e)}")

@router.get("/test-decrypt")
async def test_decrypt():
    """修改14 增加id 密码和跳转界面 - 测试解密功能"""
    try:
        id_to_employee = load_employee_mapping()
        return {
            "status": "success",
            "employee_count": len(id_to_employee),
            "employees": {eid: {"name": info["name"]} for eid, info in id_to_employee.items()}
        }
    except Exception as e:
        return {"status": "error", "message": str(e)}

def hash_password(password: str) -> bytes:
    """Hashes a password using bcrypt."""
    return bcrypt.hashpw(password.encode('utf-8'), bcrypt.gensalt())

def verify_password(plain_password: str, hashed_password: bytes) -> bool:
    """Verifies a password against a hash."""
    return bcrypt.checkpw(plain_password.encode('utf-8'), hashed_password)

@router.post("/register", status_code=status.HTTP_201_CREATED, response_model=UserResponse)
async def register_user(user: UserCreate, db: AsyncSession = Depends(get_db)):
    """
    标准用户注册端点
    任何人都可以注册，创建自己的用户账户
    """
    # 检查用户名是否已存在
    result = await db.execute(select(User).filter(User.username == user.username))
    if result.scalars().first():
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="用户名已存在",
        )
    
    # 如果提供了邮箱，检查邮箱是否已存在
    if user.email:
        email_result = await db.execute(select(User).filter(User.email == user.email))
        if email_result.scalars().first():
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="邮箱已被注册",
            )
    
    # 创建新用户
    hashed_pw = hash_password(user.password)
    new_user = User(
        username=user.username,
        encrypted_password=hashed_pw,
        email=user.email,
        full_name=user.full_name,
        employee_id=user.employee_id,
        is_active="true"
    )
    db.add(new_user)
    await db.commit()
    await db.refresh(new_user)
    
    return UserResponse(
        username=new_user.username,
        email=new_user.email,
        full_name=new_user.full_name,
        employee_id=new_user.employee_id,
        is_active=new_user.is_active,
        created_at=new_user.created_at.isoformat()
    )

@router.post("/loginlocal", response_model=Token)
async def login_for_access_token(form_data: UserLogin, db: AsyncSession = Depends(get_db)):
    """
    标准用户登录端点
    使用用户名和密码进行登录，返回JWT token
    """
    result = await db.execute(select(User).filter(User.username == form_data.username))
    db_user = result.scalars().first()

    if not db_user:
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="用户名不存在",
            headers={"WWW-Authenticate": "Bearer"},
        )
    
    if not verify_password(form_data.password, db_user.encrypted_password):
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="密码错误",
            headers={"WWW-Authenticate": "Bearer"},
        )
    
    if db_user.is_active != "true":
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="账户已被禁用",
            headers={"WWW-Authenticate": "Bearer"},
        )
    
    # 创建包含用户信息的token
    token_data = {
        "sub": db_user.username,
        "username": db_user.username,
        "employee_id": db_user.employee_id,
        "full_name": db_user.full_name,
        "email": db_user.email
    }
    access_token = create_access_token(data=token_data)
    return {"access_token": access_token, "token_type": "bearer"}

@router.get("/me", response_model=UserResponse)
async def get_current_user_info(current_user: dict = Depends(get_current_user)):
    """获取当前用户信息"""
    return UserResponse(
        username=current_user.get("username", ""),
        email=current_user.get("email", ""),
        full_name=current_user.get("full_name", ""),
        employee_id=current_user.get("employee_id", ""),
        is_active="true",
        created_at=""
    )

@router.get("/users", response_model=List[UserResponse])
async def list_users(db: AsyncSession = Depends(get_db), current_user: dict = Depends(get_current_user)):
    """获取用户列表（需要认证）"""
    result = await db.execute(select(User))
    users = result.scalars().all()
    
    return [
        UserResponse(
            username=user.username,
            email=user.email,
            full_name=user.full_name,
            employee_id=user.employee_id,
            is_active=user.is_active,
            created_at=user.created_at.isoformat()
        )
        for user in users
    ]

# 2025/06/26.11:50+分离ui操作 - 添加token验证端点
@router.get("/api/verify")
async def verify_token_endpoint(request: Request):
    """验证token是否有效，用于独立程序启动验证"""
    try:
        # 手动获取Authorization头
        authorization = request.headers.get("authorization")
        if not authorization:
            raise HTTPException(status_code=401, detail="缺少Authorization头")
        
        # 提取token
        if not authorization.startswith("Bearer "):
            raise HTTPException(status_code=401, detail="无效的Authorization格式")
        
        token = authorization.split(" ")[1]
        
        # 直接实现token验证逻辑，避免函数重名问题
        from jose import JWTError, jwt
        from ..config import JWT_SECRET_KEY, JWT_ALGORITHM
        
        try:
            payload = jwt.decode(token, JWT_SECRET_KEY, algorithms=[JWT_ALGORITHM])
            
            # 检查token是否过期
            exp = payload.get('exp')
            if exp and exp < time.time():
                raise HTTPException(status_code=401, detail="Token已过期")
            
            return {
                "status": "success",
                "message": "Token验证成功",
                "user": {
                    "employee_id": payload.get("employee_id"),
                    "employee_name": payload.get("employee_name"),
                    "user_type": payload.get("user_type")
                }
            }
        except JWTError:
            raise HTTPException(status_code=401, detail="Token无效")
            
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Token验证失败: {str(e)}") 