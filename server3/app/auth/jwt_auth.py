# server/app/auth/jwt_auth.py

import time
from typing import Optional, Dict, Any
from jose import J<PERSON><PERSON><PERSON><PERSON>, jwt
from fastapi import HTTPException, status, Depends
from fastapi.security import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, HTTPAuthorizationCredentials, OAuth2PasswordBearer
from datetime import datetime, timedelta
from passlib.context import Crypt<PERSON>ontext
import logging

from ..config import JWT_SECRET_KEY, JWT_ALGORITHM

logger = logging.getLogger(__name__)

# Password hashing context
pwd_context = CryptContext(schemes=["bcrypt"], deprecated="auto")

# 建议在生产环境中使用更复杂的密钥，并通过环境变量配置
SECRET_KEY = "your-very-secret-signing-key"
ALGORITHM = "HS256"
ACCESS_TOKEN_EXPIRE_SECONDS = 3600  # 1 小时过期

security = HTTPBearer()
oauth2_scheme = OAuth2PasswordBearer(tokenUrl="auth/login")

def verify_password(plain_password: str, hashed_password: bytes) -> bool:
    """Verify a plain password against its hash."""
    try:
        # Convert bytes to string if needed
        if isinstance(hashed_password, bytes):
            hashed_password = hashed_password.decode('utf-8')
        return pwd_context.verify(plain_password, hashed_password)
    except Exception as e:
        logger.error(f"Password verification error: {e}")
        return False

def get_password_hash(password: str) -> bytes:
    """Hash a password and return as bytes."""
    hashed = pwd_context.hash(password)
    return hashed.encode('utf-8')

def create_access_token(data: dict, expires_delta: Optional[timedelta] = None):
    """Create a JWT access token."""
    to_encode = data.copy()
    if expires_delta:
        expire = datetime.utcnow() + expires_delta
    else:
        expire = datetime.utcnow() + timedelta(hours=24)
    
    to_encode.update({"exp": expire})
    encoded_jwt = jwt.encode(to_encode, JWT_SECRET_KEY, algorithm=JWT_ALGORITHM)
    return encoded_jwt

def create_employee_token(employee_id: str, employee_name: str, permission: str = "normal", expires_delta: Optional[int] = None):
    """20250618.19:20 实时信息交流 - 创建员工专用token"""
    # 2025/07/03 +13:40+ 在token中包含权限信息
    data = {
        "sub": f"employee_{employee_id}",
        "employee_id": employee_id,
        "employee_name": employee_name,
        "user_type": "employee",
        "permission": permission  # 2025/07/03 +13:40+ 权限信息
    }
    
    if expires_delta:
        expires_delta_obj = timedelta(seconds=expires_delta)
    else:
        expires_delta_obj = None
        
    return create_access_token(data, expires_delta_obj)

def verify_token(token: str) -> Optional[dict]:
    """Verify a JWT token and return payload."""
    try:
        payload = jwt.decode(token, JWT_SECRET_KEY, algorithms=[JWT_ALGORITHM])
        return payload
    except JWTError as e:
        logger.error(f"JWT verification error: {e}")
        return None

async def verify_token_legacy(
    credentials: HTTPAuthorizationCredentials = Depends(security)
):
    """原有的token验证函数，保持兼容性"""
    token = credentials.credentials
    try:
        payload = jwt.decode(token, JWT_SECRET_KEY, algorithms=[JWT_ALGORITHM])
        return payload.get("sub")
    except JWTError:
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="Invalid or expired token.",
        )

async def verify_employee_token(
    credentials: HTTPAuthorizationCredentials = Depends(security)
) -> Dict[str, Any]:
    """20250618.19:20 实时信息交流 - 验证员工token并返回完整payload"""
    token = credentials.credentials
    return verify_token(token)

async def get_current_user(token: str = Depends(oauth2_scheme)):
    """Decodes token to get the current user."""
    credentials_exception = HTTPException(
        status_code=status.HTTP_401_UNAUTHORIZED,
        detail="Could not validate credentials",
        headers={"WWW-Authenticate": "Bearer"},
    )
    try:
        payload = jwt.decode(token, JWT_SECRET_KEY, algorithms=[JWT_ALGORITHM])
        username: str = payload.get("sub")
        if username is None:
            raise credentials_exception
    except JWTError:
        raise credentials_exception
    # In a real app, you might fetch the user from the DB here
    return {"username": username}
