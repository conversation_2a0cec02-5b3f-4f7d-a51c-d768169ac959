请把，上面的所有mermaid可视图，在原来的基础上，加上中文说明，这样可以更加详细

我将为所有的Mermaid图表添加详细的中文说明，让它们更加清晰易懂。

## Server主服务(8003)函数调用关系图 - 中文详细版

## Server2聊天微服务(8005)函数调用关系图 - 中文详细版

## Server3认证微服务(8006)函数调用关系图 - 中文详细版

## Server4视频监控微服务(8007)函数调用关系图 - 中文详细版

## Server5数据同步微服务(8009)函数调用关系图 - 中文详细版

## Server6 MDB网关微服务(8009)函数调用关系图 - 中文详细版

## 📋 MySuite项目函数调用关系图总结

我已经为所有六个服务器创建了详细的中文版Mermaid函数调用关系图。每个图表都包含了以下增强特性：

### 🎨 图表增强特性

#### 1. **详细中文说明**

- 每个函数节点都添加了中文描述
- 使用表情符号增强视觉识别
- 详细的功能说明和调用频率标注

#### 2. **颜色分类系统**

- **蓝色** (入口点): 应用启动和生命周期管理
- **紫色** (路由): API路由和端点
- **绿色** (数据库): 数据库操作和连接
- **橙色** (WebSocket): 实时通信和连接管理
- **红色** (Redis): 缓存和任务队列
- **黄色** (配置): 配置管理和验证

#### 3. **功能模块分组**

每个图表都按功能模块进行了清晰的分组：

- 应用启动流程
- 路由模块
- 核心业务逻辑
- 数据库操作
- 外部服务调用
- 错误处理和清理

### 📊 各服务器图表特色

#### **Server主服务** - 功能标志和任务管理中心

- 突出显示WebSocket广播机制
- 详细的后台任务循环说明
- ODBC和Excel处理流程

#### **Server2聊天微服务** - 实时通信专家

- 完整的WebSocket连接生命周期
- 加密服务和文件共享流程
- Redis消息存储机制

#### **Server3认证微服务** - 安全认证核心

- 多重认证流程(JWT + 硬件指纹)
- 加密解密操作详细说明
- 权限验证和程序授权

#### **Server4视频监控微服务** - AI视频处理

- OpenCV视频捕获流程
- YOLO物体检测集成
- 实时视频流广播机制

#### **Server5数据同步微服务** - 复杂数据同步

- 六大功能模块(F1-F6)协作关系
- PostgreSQL触发器和Redis队列
- Server6网关调用和MongoDB日志

#### **Server6 MDB网关微服务** - Windows COM专家

- COM线程安全执行机制
- 字段映射和数据转换
- 错误重试和资源清理

### 🔍 图表使用建议

#### 1. **开发者学习**

- 新开发者可以通过图表快速理解系统架构
- 每个函数的中文说明帮助理解业务逻辑
- 颜色分类帮助识别不同类型的操作

#### 2. **系统维护**

- 通过调用关系快速定位问题源头
- 理解模块间依赖关系，避免破坏性修改
- 优化性能瓶颈点

#### 3. **架构设计**

- 参考现有的函数调用模式
- 理解微服务间的协作机制
- 设计新功能时保持架构一致性

### 🎯 技术亮点展示

这些详细的中文版函数调用关系图展现了MySuite项目的以下技术亮点：

1. **微服务架构的成熟度** - 每个服务职责明确，接口清晰
2. **异步编程的深度应用** - 全面使用asyncio和FastAPI
3. **实时通信的完整实现** - WebSocket + Redis的完美结合
4. **数据同步的复杂性** - Server5的六大模块协作机制
5. **跨平台兼容性** - Windows COM与Linux的兼容处理
6. **AI技术的集成** - YOLO物体检测与视频流的结合

这些图表不仅是技术文档，更是MySuite项目架构设计智慧的可视化展现，为后续的开发、维护和优化提供了宝贵的参考资料。

```mermaid
graph TD
    %% 应用启动流程
    A["🚀 main.py<br/>主应用入口"] --> B["⚙️ lifespan()<br/>应用生命周期管理"]
    B --> C["🗄️ init_db()<br/>数据库初始化"]
    B --> D["🔍 test_db_connection()<br/>数据库连接测试"]
    B --> E["📋 init_imdb_tables()<br/>IMDB表初始化"]
    B --> F["🔴 redis_service.initialize()<br/>Redis服务初始化"]
    B --> G["⏰ check_overdue_tasks_loop()<br/>逾期任务检查循环"]
    B --> H["📄 process_xml_loop()<br/>XML文件处理循环"]
    
    %% FastAPI应用创建
    A --> I["🌐 FastAPI app<br/>Web应用框架"]
    I --> J["🔗 include_router()<br/>路由模块注册"]
    
    %% 路由模块
    J --> K["🚩 feature_flags.router<br/>功能标志路由"]
    J --> L["📝 tasks.router<br/>任务管理路由"]
    J --> M["📊 excel_upload.router<br/>Excel上传路由"]
    J --> N["🔌 odbc_actions.router<br/>ODBC操作路由"]
    J --> O["📄 xml_actions.router<br/>XML操作路由"]
    J --> P["🔗 ws_router<br/>WebSocket路由"]
    J --> Q["➕ extra_actions.router<br/>额外功能路由"]
    
    %% 功能标志路由详细
    K --> K1["📋 get_all_flags()<br/>获取所有功能标志"]
    K --> K2["✏️ set_flag()<br/>设置功能标志"]
    K2 --> K3["📢 notify_all_clients()<br/>通知所有WebSocket客户端"]
    K1 --> K4["🗄️ AsyncSessionLocal()<br/>异步数据库会话"]
    K2 --> K4
    
    %% 任务管理路由详细
    L --> L1["📋 get_pending_tasks()<br/>获取待处理任务"]
    L1 --> L2["🗄️ AsyncSessionLocal()<br/>异步数据库会话"]
    
    %% WebSocket管理详细
    P --> P1["🔗 websocket_feature_flags()<br/>功能标志WebSocket端点"]
    P1 --> P2["🔐 verify_token_from_query()<br/>查询参数JWT验证"]
    P1 --> P3["🔌 connect()<br/>WebSocket连接建立"]
    P1 --> P4["❌ disconnect()<br/>WebSocket连接断开"]
    P3 --> P5["📊 active_connections<br/>活跃连接列表"]
    P4 --> P5
    
    %% 后台任务详细
    G --> G1["⏰ check_overdue_tasks_loop()<br/>逾期任务检查主循环<br/>每60秒执行一次"]
    G1 --> G2["🗄️ AsyncSessionLocal()<br/>数据库会话获取"]
    G1 --> G3["🔧 _internal_set_flag()<br/>内部标志设置"]
    G3 --> G4["📢 notify_all_clients()<br/>WebSocket广播通知"]
    
    H --> H1["📄 process_xml_loop()<br/>XML处理主循环<br/>每10秒执行一次"]
    H1 --> H2["🔍 odbc_client.check_connection_status()<br/>ODBC连接状态检查"]
    H1 --> H3["⚙️ process_single_xml()<br/>单个XML文件处理"]
    H3 --> H4["💾 odbc_client.execute_non_query()<br/>ODBC非查询操作执行"]
    
    %% 数据库操作详细
    C --> C1["🔄 engine.begin()<br/>数据库事务开始"]
    C1 --> C2["🏗️ Base.metadata.create_all()<br/>创建所有数据表"]
    D --> D1["🔌 engine.connect()<br/>数据库连接建立"]
    E --> E1["📋 create_tables()<br/>创建IMDB相关表"]
    
    %% Redis服务详细
    F --> F1["🔴 redis.Redis()<br/>Redis客户端创建"]
    F --> F2["🏓 redis.ping()<br/>Redis连接测试"]
    
    %% ODBC操作详细
    N --> N1["📊 get_connection_status()<br/>获取ODBC连接状态"]
    N --> N2["🔍 execute_query()<br/>执行ODBC查询"]
    N --> N3["💾 execute_non_query()<br/>执行ODBC非查询操作"]
    N1 --> N4["🔌 odbc_client<br/>ODBC客户端实例"]
    N2 --> N4
    N3 --> N4
    
    %% Excel处理详细
    M --> M1["📊 upload_excel()<br/>Excel文件上传处理"]
    
    %% 额外功能详细
    Q --> Q1["📖 read_data_xml()<br/>读取XML数据"]
    Q --> Q2["📈 upload_progress()<br/>上传进度处理"]
    Q --> Q3["➕ add_one()<br/>数据增量操作"]
    Q2 --> Q4["🌳 ET.parse()<br/>XML解析"]
    Q2 --> Q5["🗄️ AsyncSessionLocal()<br/>数据库会话"]
    
    %% 样式定义
    classDef entryPoint fill:#e1f5fe,stroke:#01579b,stroke-width:3px
    classDef router fill:#f3e5f5,stroke:#4a148c,stroke-width:2px
    classDef database fill:#e8f5e8,stroke:#1b5e20,stroke-width:2px
    classDef websocket fill:#fff3e0,stroke:#e65100,stroke-width:2px
    classDef background fill:#fce4ec,stroke:#880e4f,stroke-width:2px
    classDef redis fill:#ffebee,stroke:#c62828,stroke-width:2px
    
    class A,B entryPoint
    class K,L,M,N,O,P,Q router
    class C,D,E,K4,L2,G2,Q5,C1,C2,D1,E1 database
    class P1,P3,P4,P5,G4 websocket
    class G,H,G1,H1 background
    class F,F1,F2 redis
```



```mermaid
graph TD
    %% 应用启动流程
    A["🚀 main.py<br/>聊天微服务主入口"] --> B["⚙️ lifespan()<br/>应用生命周期管理"]
    B --> C["🗄️ database_service.initialize()<br/>数据库服务初始化"]
    B --> D["🔴 redis_service.initialize()<br/>Redis服务初始化"]
    B --> E["🔐 encryption_service.initialize()<br/>加密服务初始化"]
    B --> F["📁 file_service.initialize()<br/>文件服务初始化"]
    
    %% FastAPI应用创建
    A --> G["🌐 FastAPI app<br/>Web应用框架"]
    G --> H["🔗 include_router()<br/>路由模块注册"]
    
    %% 路由模块
    H --> I["💬 chat_websocket.router<br/>WebSocket聊天路由"]
    H --> J["📊 chat_management.router<br/>聊天管理路由"]
    H --> K["📁 file_sharing.router<br/>文件共享路由"]
    H --> L["🔒 private_chat.router<br/>私聊路由"]
    H --> M["🏠 room_management.router<br/>聊天室管理路由"]
    
    %% WebSocket聊天核心流程
    I --> I1["🔗 websocket_chat_endpoint()<br/>WebSocket聊天连接端点"]
    I1 --> I2["🔐 jwt_auth.verify_token()<br/>JWT令牌验证"]
    I1 --> I3["🔌 chat_manager.connect()<br/>聊天管理器连接"]
    I1 --> I4["📢 chat_manager.broadcast_message()<br/>消息广播"]
    I1 --> I5["💌 chat_manager.send_private_message()<br/>私聊消息发送"]
    
    %% 聊天管理器详细
    I3 --> I6["🔌 ChatConnectionManager.connect()<br/>连接管理器建立连接"]
    I4 --> I7["📢 ChatConnectionManager.broadcast_message()<br/>连接管理器广播消息"]
    I5 --> I8["💌 ChatConnectionManager.send_private_message()<br/>连接管理器私聊"]
    I6 --> I9["📋 active_connections.append()<br/>添加到活跃连接列表"]
    I7 --> I10["📤 websocket.send_text()<br/>WebSocket发送文本"]
    I7 --> I11["💾 redis_service.save_chat_message()<br/>Redis保存聊天消息"]
    
    %% Redis服务操作详细
    I11 --> R1["💾 RedisService.save_chat_message()<br/>Redis聊天消息保存服务"]
    R1 --> R2["⏰ redis_client.setex()<br/>设置带过期时间的键值"]
    R1 --> R3["📝 redis_client.lpush()<br/>列表左侧推入"]
    R1 --> R4["✂️ redis_client.ltrim()<br/>列表修剪保持长度"]
    
    D --> R5["🔴 RedisService.initialize()<br/>Redis服务初始化"]
    R5 --> R6["🔴 redis.Redis()<br/>Redis客户端创建"]
    R5 --> R7["🏓 redis_client.ping()<br/>Redis连接测试"]
    
    %% 数据库服务操作详细
    C --> D1["🗄️ DatabaseService.initialize()<br/>数据库服务初始化"]
    D1 --> D2["🔧 create_async_engine()<br/>创建异步数据库引擎"]
    D1 --> D3["🏭 async_sessionmaker()<br/>异步会话制造器"]
    D1 --> D4["🏗️ Base.metadata.create_all()<br/>创建所有数据表"]
    D1 --> D5["🏠 initialize_default_rooms()<br/>初始化默认聊天室"]
    
    %% 文件共享功能详细
    K --> K1["📤 upload_file()<br/>文件上传"]
    K --> K2["📥 download_file()<br/>文件下载"]
    K --> K3["📋 get_room_files()<br/>获取聊天室文件列表"]
    K1 --> K4["📁 file_service.upload_file()<br/>文件服务上传"]
    K2 --> K5["📁 file_service.get_file()<br/>文件服务获取"]
    K3 --> K6["📁 file_service.get_room_files()<br/>文件服务获取房间文件"]
    
    %% 私聊功能详细
    L --> L1["💌 send_private_message()<br/>发送私聊消息"]
    L --> L2["💬 get_conversations()<br/>获取对话列表"]
    L --> L3["📜 get_private_messages()<br/>获取私聊消息"]
    L1 --> L4["🔐 encryption_service.encrypt_message()<br/>消息加密服务"]
    L1 --> L5["🗂️ redis_service.set_hash()<br/>Redis哈希设置"]
    L1 --> L6["📝 redis_service.list_push()<br/>Redis列表推送"]
    
    %% 聊天室管理详细
    M --> M1["🏠 get_rooms()<br/>获取聊天室列表"]
    M --> M2["🏗️ create_room()<br/>创建聊天室"]
    M --> M3["🚪 join_room()<br/>加入聊天室"]
    M --> M4["🚶 leave_room()<br/>离开聊天室"]
    
    %% 加密服务详细
    E --> E1["🔐 EncryptionService.initialize()<br/>加密服务初始化"]
    E1 --> E2["🔑 PBKDF2HMAC()<br/>密钥派生函数"]
    E1 --> E3["🛡️ AESGCM()<br/>AES-GCM加密算法"]
    L4 --> E4["🔐 EncryptionService.encrypt_message()<br/>加密服务消息加密"]
    E4 --> E5["🛡️ aes_gcm.encrypt()<br/>AES-GCM加密执行"]
    
    %% 文件服务详细
    F --> F1["📁 FileService.initialize()<br/>文件服务初始化"]
    K4 --> F2["📁 FileService.upload_file()<br/>文件服务上传文件"]
    F2 --> F3["✅ file validation<br/>文件验证"]
    F2 --> F4["💾 file storage<br/>文件存储"]
    
    %% 聊天管理API详细
    J --> J1["📊 get_chat_status()<br/>获取聊天状态"]
    J --> J2["👥 get_online_users()<br/>获取在线用户"]
    J --> J3["📢 broadcast_system_message()<br/>广播系统消息"]
    J1 --> J4["📊 chat_manager.get_connection_info()<br/>聊天管理器连接信息"]
    J1 --> J5["📊 redis_service.get_service_stats()<br/>Redis服务统计"]
    
    %% 心跳和连接管理详细
    I6 --> I12["💓 start_heartbeat()<br/>启动心跳检测"]
    I12 --> I13["🔄 _heartbeat_loop()<br/>心跳循环"]
    I13 --> I14["🏓 websocket.send_text ping<br/>WebSocket发送ping"]
    
    %% 样式定义
    classDef entryPoint fill:#e1f5fe,stroke:#01579b,stroke-width:3px
    classDef router fill:#f3e5f5,stroke:#4a148c,stroke-width:2px
    classDef service fill:#e8f5e8,stroke:#1b5e20,stroke-width:2px
    classDef websocket fill:#fff3e0,stroke:#e65100,stroke-width:2px
    classDef database fill:#f1f8e9,stroke:#33691e,stroke-width:2px
    classDef redis fill:#fce4ec,stroke:#880e4f,stroke-width:2px
    classDef encryption fill:#f3e5f5,stroke:#4a148c,stroke-width:2px
    
    class A,B entryPoint
    class I,J,K,L,M router
    class C,D,E,F,D1,R5,E1,F1 service
    class I1,I3,I4,I6,I7,I10,I12,I13 websocket
    class D2,D3,D4,D5 database
    class R1,R2,R3,R4,R6,R7,I11,L5,L6 redis
    class E2,E3,E4,E5,L4 encryption
```



```mermaid
graph TD
    %% 应用启动流程
    A["🚀 main.py<br/>认证微服务主入口"] --> B["⚙️ lifespan()<br/>应用生命周期管理"]
    B --> C["🗄️ init_db()<br/>数据库初始化"]
    C --> D["🔧 create_async_engine()<br/>创建异步数据库引擎"]
    C --> E["🏗️ Base.metadata.create_all()<br/>创建所有数据表"]
    
    %% FastAPI应用创建
    A --> F["🌐 FastAPI app<br/>Web应用框架"]
    F --> G["🔗 include_router()<br/>路由模块注册"]
    
    %% 路由模块
    G --> H["🔐 auth_login.router<br/>用户登录认证路由"]
    G --> I["🖥️ hardware_registration.router<br/>硬件指纹注册路由"]
    G --> J["🛡️ program_authorization.router<br/>程序授权路由"]
    
    %% 员工登录流程详细
    H --> H1["👤 login()<br/>员工登录验证"]
    H1 --> H2["📂 load_employee_mapping()<br/>加载员工数据映射"]
    H2 --> H3["🔓 decrypt_employee_data()<br/>解密员工数据文件"]
    H3 --> H4["🔐 Fernet.decrypt()<br/>Fernet解密算法"]
    H1 --> H5["🎫 create_employee_token()<br/>创建员工专用JWT令牌"]
    H5 --> H6["🔏 jwt.encode()<br/>JWT编码生成"]
    
    %% 标准用户登录流程详细
    H --> H7["👥 loginlocal()<br/>标准用户登录"]
    H7 --> H8["🔍 select User<br/>查询用户表"]
    H7 --> H9["🔒 verify_password()<br/>密码验证"]
    H9 --> H10["🔐 bcrypt.checkpw()<br/>bcrypt密码检查"]
    H7 --> H11["🎫 create_access_token()<br/>创建访问令牌"]
    H11 --> H12["🔏 jwt.encode()<br/>JWT编码生成"]
    
    %% Token验证流程详细
    H --> H13["✅ verify_token_endpoint()<br/>令牌验证端点"]
    H13 --> H14["🔍 jwt.decode()<br/>JWT解码"]
    H14 --> H15["✅ payload validation<br/>载荷验证"]
    
    %% 硬件指纹注册流程详细
    I --> I1["📝 register_fingerprint()<br/>注册硬件指纹"]
    I1 --> I2["📂 load_employee_mapping()<br/>加载员工数据映射"]
    I1 --> I3["🔢 generate_fingerprint()<br/>生成硬件指纹"]
    I3 --> I4["🔐 hashlib.sha256()<br/>SHA256哈希算法"]
    I1 --> I5["💾 HardwareFingerprint.create()<br/>创建硬件指纹记录"]
    I5 --> I6["➕ db.add()<br/>添加到数据库"]
    I6 --> I7["💾 db.commit()<br/>提交数据库事务"]
    
    %% 硬件指纹验证流程详细
    I --> I8["🔍 verify_fingerprint()<br/>验证硬件指纹"]
    I8 --> I9["📂 load_employee_mapping()<br/>加载员工数据映射"]
    I8 --> I10["🔍 select HardwareFingerprint<br/>查询硬件指纹表"]
    I8 --> I11["🔢 generate_fingerprint()<br/>生成当前硬件指纹"]
    I8 --> I12["⚖️ fingerprint comparison<br/>指纹比较验证"]
    
    %% 程序授权流程详细
    J --> J1["🛡️ authorize_program_launch()<br/>授权程序启动"]
    J1 --> J2["🔍 verify_employee_token()<br/>验证员工令牌"]
    J2 --> J3["🔍 jwt.decode()<br/>JWT解码"]
    J1 --> J4["📂 load_employee_mapping()<br/>加载员工数据映射"]
    J1 --> J5["✅ permission validation<br/>权限验证"]
    J5 --> J6["📋 PROGRAM_PERMISSIONS check<br/>程序权限检查"]
    
    %% JWT认证核心详细
    H5 --> K1["🎫 create_employee_token()<br/>创建员工令牌"]
    H11 --> K2["🎫 create_access_token()<br/>创建访问令牌"]
    J2 --> K3["🔍 verify_token()<br/>验证令牌"]
    K1 --> K4["🔏 jwt.encode with employee data<br/>JWT编码员工数据"]
    K2 --> K5["🔏 jwt.encode with user data<br/>JWT编码用户数据"]
    K3 --> K6["🔍 jwt.decode and validate<br/>JWT解码和验证"]
    
    %% 数据库操作详细
    C --> D1["🏭 AsyncSession creation<br/>异步会话创建"]
    I5 --> D2["🗄️ HardwareFingerprint table<br/>硬件指纹表"]
    H7 --> D3["👥 User table<br/>用户表"]
    I8 --> D4["🔍 select operations<br/>查询操作"]
    
    %% 加密解密操作详细
    H2 --> E1["🔑 load_encryption_key()<br/>加载加密密钥"]
    H3 --> E2["🔐 Fernet initialization<br/>Fernet初始化"]
    E2 --> E3["🔓 decrypt operation<br/>解密操作"]
    I3 --> E4["🔐 SHA256 hashing<br/>SHA256哈希"]
    
    %% 权限管理详细
    J1 --> P1["✅ permission validation<br/>权限验证"]
    P1 --> P2["📋 PROGRAM_PERMISSIONS<br/>程序权限配置"]
    P1 --> P3["🎫 token permission check<br/>令牌权限检查"]
    P1 --> P4["📁 file permission check<br/>文件权限检查"]
    
    %% 样式定义
    classDef entryPoint fill:#e1f5fe,stroke:#01579b,stroke-width:3px
    classDef router fill:#f3e5f5,stroke:#4a148c,stroke-width:2px
    classDef auth fill:#e8f5e8,stroke:#1b5e20,stroke-width:2px
    classDef database fill:#f1f8e9,stroke:#33691e,stroke-width:2px
    classDef crypto fill:#fce4ec,stroke:#880e4f,stroke-width:2px
    classDef permission fill:#fff3e0,stroke:#e65100,stroke-width:2px
    classDef jwt fill:#f9fbe7,stroke:#827717,stroke-width:2px
    
    class A,B entryPoint
    class H,I,J router
    class H1,H7,H13,I1,I8,J1,K1,K2,K3 auth
    class C,D,E,D1,D2,D3,D4,I5,I6,I7 database
    class H3,H4,E1,E2,E3,E4,I3,I4 crypto
    class J5,J6,P1,P2,P3,P4 permission
    class H5,H6,H11,H12,H14,H15,J2,J3,K4,K5,K6 jwt
```



```mermaid
graph TD
    %% 应用启动流程
    A["🚀 main.py<br/>视频监控微服务主入口"] --> B["⚙️ lifespan()<br/>应用生命周期管理"]
    B --> C["📹 video_service.initialize()<br/>视频服务初始化"]
    B --> D["▶️ video_service.start_capture()<br/>启动视频捕获"]
    B --> E["📡 video_broadcast_task()<br/>视频广播任务"]
    B --> F["📊 stats_broadcast_task()<br/>统计信息广播任务"]
    
    %% FastAPI应用创建
    A --> G["🌐 FastAPI app<br/>Web应用框架"]
    G --> H["🔗 include_router()<br/>路由模块注册"]
    
    %% 路由模块
    H --> I["🌐 video_api.router<br/>视频API路由"]
    H --> J["🔗 video_websocket.router<br/>视频WebSocket路由"]
    
    %% 视频服务初始化详细
    C --> C1["📹 VideoService.initialize()<br/>视频服务初始化"]
    C1 --> C2["📷 cv2.VideoCapture()<br/>OpenCV视频捕获"]
    C1 --> C3["🔍 _detect_camera_capabilities()<br/>检测摄像头能力"]
    C1 --> C4["⚙️ camera.set properties<br/>设置摄像头属性"]
    C4 --> C5["📐 CAP_PROP_FRAME_WIDTH/HEIGHT<br/>设置帧宽度和高度"]
    C4 --> C6["🎬 CAP_PROP_FPS<br/>设置帧率"]
    C4 --> C7["📦 CAP_PROP_BUFFERSIZE<br/>设置缓冲区大小"]
    
    %% 视频捕获流程详细
    D --> D1["▶️ VideoService.start_capture()<br/>启动视频捕获服务"]
    D1 --> D2["🧵 threading.Thread()<br/>创建捕获线程"]
    D2 --> D3["🔄 _capture_loop()<br/>视频捕获主循环"]
    D3 --> D4["📷 camera.read()<br/>读取摄像头帧"]
    D3 --> D5["🤖 yolo_service.detect_objects()<br/>YOLO物体检测"]
    D3 --> D6["🔒 frame_lock.acquire()<br/>获取帧锁"]
    D3 --> D7["🖼️ latest_frame update<br/>更新最新帧"]
    
    %% YOLO检测流程详细
    D5 --> Y1["🤖 YoloService.detect_objects()<br/>YOLO物体检测服务"]
    Y1 --> Y2["🔮 model.predict()<br/>模型预测"]
    Y1 --> Y3["🎨 draw_detections()<br/>绘制检测框"]
    Y1 --> Y4["📊 detection statistics<br/>检测统计"]
    
    %% WebSocket连接管理详细
    J --> J1["🔗 video_websocket_endpoint()<br/>视频WebSocket端点"]
    J1 --> J2["🔌 VideoConnectionManager.connect()<br/>视频连接管理器连接"]
    J2 --> J3["✅ websocket.accept()<br/>WebSocket接受连接"]
    J2 --> J4["📋 active_connections.add()<br/>添加到活跃连接"]
    J2 --> J5["👤 video_service.add_client()<br/>视频服务添加客户端"]
    
    %% WebSocket消息处理详细
    J1 --> J6["📨 websocket.receive_text()<br/>接收WebSocket文本消息"]
    J6 --> J7["🔀 message type routing<br/>消息类型路由"]
    J7 --> J8["🏓 ping/pong handling<br/>心跳处理"]
    J7 --> J9["📊 stats request<br/>统计请求"]
    J7 --> J10["🎮 stream control<br/>流控制"]
    
    %% 视频流广播详细
    E --> E1["📡 video_broadcast_task()<br/>视频广播任务主循环"]
    E1 --> E2["🖼️ video_service.get_latest_frame_base64()<br/>获取最新帧Base64"]
    E2 --> E3["🖼️ VideoService.get_latest_frame_base64()<br/>视频服务获取最新帧"]
    E3 --> E4["🔄 cv2.imencode()<br/>OpenCV图像编码"]
    E4 --> E5["📝 base64.b64encode()<br/>Base64编码"]
    E1 --> E6["📡 connection_manager.broadcast_frame()<br/>连接管理器广播帧"]
    E6 --> E7["📤 websocket.send_text()<br/>WebSocket发送文本"]
    
    %% API端点详细
    I --> I1["📊 get_stats()<br/>获取统计信息"]
    I --> I2["📸 get_snapshot()<br/>获取快照"]
    I --> I3["▶️ start_camera()<br/>启动摄像头"]
    I --> I4["⏹️ stop_camera()<br/>停止摄像头"]
    I --> I5["🔄 restart_camera()<br/>重启摄像头"]
    I --> I6["🧪 test_camera()<br/>测试摄像头"]
    
    %% 摄像头控制详细
    I3 --> I7["▶️ video_service.start_capture()<br/>视频服务启动捕获"]
    I4 --> I8["⏹️ video_service.stop_capture()<br/>视频服务停止捕获"]
    I5 --> I9["🔄 video_service.restart_camera()<br/>视频服务重启摄像头"]
    I9 --> I10["🔄 stop_capture + initialize + start_capture<br/>停止+初始化+启动"]
    
    %% YOLO API控制详细
    I --> I11["🤖 initialize_yolo()<br/>初始化YOLO"]
    I --> I12["✅ enable_yolo()<br/>启用YOLO"]
    I --> I13["❌ disable_yolo()<br/>禁用YOLO"]
    I11 --> I14["🤖 yolo_service.initialize_model()<br/>YOLO服务初始化模型"]
    I14 --> I15["📦 YOLO model loading<br/>YOLO模型加载"]
    I15 --> I16["💻 model.to('cpu')<br/>模型转移到CPU"]
    
    %% 统计信息广播详细
    F --> F1["📊 stats_broadcast_task()<br/>统计广播任务主循环"]
    F1 --> F2["📊 video_service.get_stats()<br/>视频服务获取统计"]
    F1 --> F3["📊 connection_manager.send_stats()<br/>连接管理器发送统计"]
    F3 --> F4["📤 websocket.send_text stats<br/>WebSocket发送统计文本"]
    
    %% 连接管理详细
    J2 --> M1["🔌 VideoConnectionManager<br/>视频连接管理器"]
    M1 --> M2["📋 active_connections Dict<br/>活跃连接字典"]
    M1 --> M3["👤 client_info Dict<br/>客户端信息字典"]
    E6 --> M4["📡 broadcast_frame()<br/>广播帧"]
    M4 --> M5["🚀 asyncio.gather parallel send<br/>异步并行发送"]
    
    %% 资源清理详细
    B --> R1["🧹 cleanup on shutdown<br/>关闭时清理"]
    R1 --> R2["🧹 video_service.cleanup()<br/>视频服务清理"]
    R2 --> R3["⏹️ stop_capture()<br/>停止捕获"]
    R2 --> R4["📷 camera.release()<br/>释放摄像头"]
    R2 --> R5["🗑️ connected_clients.clear()<br/>清空连接客户端"]
    
    %% 样式定义
    classDef entryPoint fill:#e1f5fe,stroke:#01579b,stroke-width:3px
    classDef router fill:#f3e5f5,stroke:#4a148c,stroke-width:2px
    classDef video fill:#e8f5e8,stroke:#1b5e20,stroke-width:2px
    classDef websocket fill:#fff3e0,stroke:#e65100,stroke-width:2px
    classDef yolo fill:#fce4ec,stroke:#880e4f,stroke-width:2px
    classDef api fill:#f1f8e9,stroke:#33691e,stroke-width:2px
    classDef broadcast fill:#f9fbe7,stroke:#827717,stroke-width:2px
    
    class A,B entryPoint
    class I,J router
    class C,D,C1,C2,C3,D1,D2,D3,D4,E2,E3,E4,E5 video
    class J1,J2,J3,J6,E1,E6,E7,M1,M2,M3,M4,M5 websocket
    class D5,Y1,Y2,Y3,Y4,I11,I14,I15,I16 yolo
    class I1,I2,I3,I4,I5,I6,I7,I8,I9 api
    class E,F,E1,F1,F2,F3,F4 broadcast
```



```mermaid
graph TD
    %% 应用启动流程
    A["🚀 main.py<br/>数据同步微服务主入口"] --> B["⚙️ lifespan()<br/>应用生命周期管理"]
    B --> C["🚀 start_services()<br/>启动所有服务"]
    C --> D["👂 f1_listener.start()<br/>F1监听器服务启动"]
    C --> E["📤 f2_push_writer.start()<br/>F2推送回写服务启动"]
    C --> F["📥 f3_data_puller.start()<br/>F3数据拉取服务启动"]
    C --> G["⚙️ f4_operation_handler.start()<br/>F4操作处理服务启动"]
    C --> H["📊 f5_bulk_sync.start()<br/>F5批量同步服务启动"]
    C --> I["👤 f6_user_sync.start()<br/>F6用户同步服务启动"]
    
    %% F1监听器服务详细
    D --> D1["👂 ListenerService.start()<br/>监听器服务启动"]
    D1 --> D2["🗄️ imdb_client.connect()<br/>IMDB客户端连接"]
    D1 --> D3["🔴 redis_client.connect()<br/>Redis客户端连接"]
    D1 --> D4["🍃 mongo_client.connect()<br/>MongoDB客户端连接"]
    D1 --> D5["🔄 _start_listener_loop()<br/>启动监听器循环"]
    D5 --> D6["🔌 asyncpg.connect()<br/>异步PostgreSQL连接"]
    D6 --> D7["👂 conn.add_listener()<br/>添加数据库监听器"]
    D7 --> D8["📨 _handle_notification()<br/>处理通知消息"]
    
    %% F1通知处理详细
    D8 --> D9["📤 _handle_push_job()<br/>处理推送任务"]
    D8 --> D10["🔄 _handle_sync_trigger()<br/>处理同步触发器"]
    D9 --> D11["📋 redis_client.push_task()<br/>Redis推送任务"]
    D10 --> D12["📋 redis_client.push_task()<br/>Redis推送任务"]
    
    %% F2推送回写服务详细
    E --> E1["📤 PushWriterServiceFixed.start()<br/>推送回写服务启动"]
    E1 --> E2["🗄️ database connections<br/>数据库连接"]
    E1 --> E3["🔄 _worker_loop()<br/>工作循环"]
    E3 --> E4["📋 entries_push_queue query<br/>查询推送队列"]
    E3 --> E5["⚙️ _process_queue_item_in_transaction()<br/>事务中处理队列项"]
    E5 --> E6["➕ _handle_insert_operation()<br/>处理插入操作"]
    E5 --> E7["✏️ _handle_update_operation()<br/>处理更新操作"]
    E5 --> E8["🗑️ _handle_delete_operation()<br/>处理删除操作"]
    
    %% F2操作处理详细
    E6 --> E9["📤 server6_client.insert_entry()<br/>Server6客户端插入记录"]
    E7 --> E10["✏️ server6_client.update_entry()<br/>Server6客户端更新记录"]
    E8 --> E11["🗑️ server6_client.delete_entry()<br/>Server6客户端删除记录"]
    E6 --> E12["🎯 _trigger_f6_sync()<br/>触发F6同步"]
    E7 --> E12
    E8 --> E12
    E12 --> E13["📋 redis_client.push_task user_sync<br/>Redis推送用户同步任务"]
    
    %% F3数据拉取服务详细
    F --> F1["📥 DataPullerService.start()<br/>数据拉取服务启动"]
    F1 --> F2["🔄 _data_pull_loop()<br/>数据拉取循环"]
    F2 --> F3["🔄 run_full_sync_cycle()<br/>运行完整同步周期"]
    F3 --> F4["📥 pull_recent_data()<br/>拉取最新数据"]
    F3 --> F5["🗑️ DeletionSyncService.start()<br/>删除同步服务启动"]
    F3 --> F6["🗑️ f5.run_deletion_sync()<br/>运行删除同步"]
    F4 --> F7["🔍 server6_client.query_entries()<br/>Server6客户端查询记录"]
    F4 --> F8["🔄 _sync_to_db()<br/>同步到数据库"]
    
    %% F4操作处理服务详细
    G --> G1["⚙️ OperationHandlerService.start()<br/>操作处理服务启动"]
    G1 --> G2["➕ handle_insert_operation()<br/>处理插入操作"]
    G1 --> G3["✏️ handle_update_operation()<br/>处理更新操作"]
    G1 --> G4["🗑️ handle_delete_operation()<br/>处理删除操作"]
    G1 --> G5["📊 handle_batch_operations()<br/>处理批量操作"]
    G2 --> G6["💾 imdb_client.execute_command()<br/>IMDB客户端执行命令"]
    G3 --> G6
    G4 --> G6
    
    %% F5批量同步服务详细
    H --> H1["🗑️ DeletionSyncService.start()<br/>删除同步服务启动"]
    H1 --> H2["🗑️ run_deletion_sync()<br/>运行删除同步"]
    H2 --> H3["🔍 server6_client.query_entries()<br/>Server6客户端查询记录"]
    H2 --> H4["📊 imdb_client.fetch_all()<br/>IMDB客户端获取所有数据"]
    H2 --> H5["⚖️ compare and delete<br/>比较并删除"]
    
    %% F6用户同步服务详细
    I --> I1["👤 UserSyncService.start()<br/>用户同步服务启动"]
    I1 --> I2["🔄 _sync_worker_loop()<br/>同步工作循环"]
    I1 --> I3["⏰ _auto_sync_loop()<br/>自动同步循环"]
    I2 --> I4["📋 redis_client.pop_task()<br/>Redis弹出任务"]
    I4 --> I5["⚙️ process user sync task<br/>处理用户同步任务"]
    
    %% Redis任务队列详细
    D11 --> R1["🔴 Redis Task Queue<br/>Redis任务队列"]
    D12 --> R1
    E13 --> R1
    I4 --> R1
    R1 --> R2["📋 server5:push_jobs<br/>推送任务队列"]
    R1 --> R3["👤 server5:user_sync_jobs<br/>用户同步任务队列"]
    
    %% PostgreSQL触发器详细
    G6 --> T1["🗄️ PostgreSQL Trigger<br/>PostgreSQL触发器"]
    T1 --> T2["⚡ trg_entries_enqueue<br/>entries入队触发器"]
    T2 --> T3["📋 entries_push_queue INSERT<br/>推送队列插入"]
    T2 --> T4["📢 pg_notify push_job<br/>PostgreSQL通知推送任务"]
    T4 --> D8
    
    %% Server6网关调用详细
    E9 --> S1["🌐 Server6Client<br/>Server6客户端"]
    E10 --> S1
    E11 --> S1
    F7 --> S1
    H3 --> S1
    S1 --> S2["🌐 HTTP API calls<br/>HTTP API调用"]
    S2 --> S3["🗄️ MDB operations<br/>MDB数据库操作"]
    
    %% MongoDB日志详细
    D8 --> M1["🍃 MongoDBClient<br/>MongoDB客户端"]
    E5 --> M1
    F4 --> M1
    G2 --> M1
    H2 --> M1
    I5 --> M1
    M1 --> M2["📝 log_sync_operation()<br/>记录同步操作日志"]
    M1 --> M3["❌ log_error()<br/>记录错误日志"]
    M1 --> M4["👤 log_f6_sync_result()<br/>记录F6同步结果日志"]
    
    %% 样式定义
    classDef entryPoint fill:#e1f5fe,stroke:#01579b,stroke-width:3px
    classDef service fill:#f3e5f5,stroke:#4a148c,stroke-width:2px
    classDef database fill:#e8f5e8,stroke:#1b5e20,stroke-width:2px
    classDef queue fill:#fff3e0,stroke:#e65100,stroke-width:2px
    classDef trigger fill:#fce4ec,stroke:#880e4f,stroke-width:2px
    classDef gateway fill:#f1f8e9,stroke:#33691e,stroke-width:2px
    classDef mongo fill:#f9fbe7,stroke:#827717,stroke-width:2px
    
    class A,B,C entryPoint
    class D,E,F,G,H,I,D1,E1,F1,G1,H1,I1 service
    class D2,D3,D4,E2,F1,G1,H1,I1,M1,M2,M3,M4 database
    class R1,R2,R3,D11,D12,E13,I4 queue
    class T1,T2,T3,T4 trigger
    class S1,S2,S3 gateway
    class M1,M2,M3,M4 mongo
```



```mermaid
graph TD
    %% 应用启动流程
    A["🚀 main.py<br/>MDB网关微服务主入口"] --> B["⚙️ lifespan()<br/>应用生命周期管理"]
    B --> C["✅ validate_config()<br/>配置验证"]
    B --> D["📋 get_config_summary()<br/>获取配置摘要"]
    B --> E["📝 setup_logging()<br/>设置日志系统"]
    
    %% FastAPI应用创建
    A --> F["🌐 FastAPI app<br/>Web应用框架"]
    F --> G["🔗 include_router()<br/>路由模块注册"]
    
    %% 路由模块
    G --> H["💚 health_router<br/>健康检查路由"]
    G --> I["🗄️ mdb_router<br/>MDB数据库路由"]
    
    %% 健康检查路由详细
    H --> H1["💚 health_check()<br/>健康检查端点"]
    H --> H2["📊 status()<br/>状态信息端点"]
    H --> H3["🏠 root()<br/>根路径端点"]
    H1 --> H4["🧪 mdb_client.test_connection()<br/>MDB客户端连接测试"]
    H1 --> H5["💚 HealthStatus model<br/>健康状态模型"]
    H2 --> H6["📊 ConnectionInfo model<br/>连接信息模型"]
    
    %% MDB API路由详细
    I --> I1["➕ insert_entry()<br/>插入记录端点"]
    I --> I2["✏️ update_entry()<br/>更新记录端点"]
    I --> I3["🗑️ delete_entry()<br/>删除记录端点"]
    I --> I4["🔍 query_entries()<br/>查询记录端点"]
    I --> I5["👤 query_by_employee()<br/>按员工查询端点"]
    I --> I6["📅 query_by_date()<br/>按日期查询端点"]
    I --> I7["👥 get_distinct_employees()<br/>获取不重复员工端点"]
    I --> I8["🧪 test_mdb_connection()<br/>测试MDB连接端点"]
    
    %% MDB客户端核心详细
    I1 --> M1["➕ MDBClient.insert_record()<br/>MDB客户端插入记录"]
    I2 --> M2["✏️ MDBClient.update_record()<br/>MDB客户端更新记录"]
    I3 --> M3["🗑️ MDBClient.delete_record()<br/>MDB客户端删除记录"]
    I4 --> M4["🔍 MDBClient.query_records()<br/>MDB客户端查询记录"]
    I5 --> M4
    I6 --> M4
    I7 --> M5["👥 MDBClient.get_distinct_values()<br/>MDB客户端获取不重复值"]
    I8 --> M6["🧪 MDBClient.test_connection()<br/>MDB客户端测试连接"]
    
    %% win32com操作核心详细
    M1 --> W1["🧵 _execute_in_com_thread()<br/>COM线程安全执行"]
    M2 --> W1
    M3 --> W1
    M4 --> W1
    M5 --> W1
    M6 --> W1
    
    %% COM线程操作详细
    W1 --> W2["🔧 pythoncom.CoInitialize()<br/>COM初始化"]
    W1 --> W3["🖥️ win32com.client.Dispatch Access.Application<br/>创建Access应用实例"]
    W1 --> W4["📂 access.OpenDatabase()<br/>打开Access数据库"]
    W1 --> W5["📋 db.OpenRecordset()<br/>打开记录集"]
    W1 --> W6["⚙️ rs.AddNew/Edit/Delete<br/>记录集新增/编辑/删除"]
    W1 --> W7["💾 rs.Update()<br/>记录集更新"]
    W1 --> W8["🧹 pythoncom.CoUninitialize()<br/>COM清理"]
    
    %% 数据操作具体实现详细
    M1 --> D1["➕ _insert function<br/>插入函数"]
    D1 --> D2["🔄 field mapping<br/>字段映射"]
    D1 --> D3["✅ data validation<br/>数据验证"]
    D1 --> D4["💾 SQL INSERT execution<br/>SQL插入执行"]
    D1 --> D5["🆔 return inserted_id<br/>返回插入ID"]
    
    M2 --> D6["✏️ _update function<br/>更新函数"]
    D6 --> D7["🔄 field mapping<br/>字段映射"]
    D6 --> D8["🔍 WHERE clause construction<br/>WHERE子句构建"]
    D6 --> D9["💾 SQL UPDATE execution<br/>SQL更新执行"]
    
    M3 --> D10["🗑️ _delete function<br/>删除函数"]
    D10 --> D11["🔍 WHERE clause construction<br/>WHERE子句构建"]
    D10 --> D12["💾 SQL DELETE execution<br/>SQL删除执行"]
    
    M4 --> D13["🔍 _query function<br/>查询函数"]
    D13 --> D14["🔍 WHERE clause construction<br/>WHERE子句构建"]
    D13 --> D15["📊 ORDER BY clause<br/>ORDER BY子句"]
    D13 --> D16["📏 LIMIT clause<br/>LIMIT子句"]
    D13 --> D17["🔍 SQL SELECT execution<br/>SQL查询执行"]
    D13 --> D18["📋 result formatting<br/>结果格式化"]
    
    %% 字段映射处理详细
    D2 --> F1["📋 field_map dictionary<br/>字段映射字典"]
    D7 --> F1
    F1 --> F2["🔤 English to Japanese mapping<br/>英文到日文映射"]
    F1 --> F3["🔤 Japanese to Japanese mapping<br/>日文到日文映射"]
    
    %% 错误处理和重试详细
    W1 --> E1["🔄 max_retries loop<br/>最大重试循环"]
    E1 --> E2["⏰ retry_delay sleep<br/>重试延迟等待"]
    E1 --> E3["❌ exception handling<br/>异常处理"]
    E1 --> E4["🧹 resource cleanup<br/>资源清理"]
    
    %% 连接池管理详细
    B --> P1["🏊 ConnectionPool.init()<br/>连接池初始化"]
    H1 --> P2["📊 ConnectionPool.get_status()<br/>连接池状态获取"]
    P1 --> P3["🔢 max_connections=5<br/>最大连接数设置"]
    P2 --> P4["📊 active_connections count<br/>活跃连接数统计"]
    
    %% 配置验证详细
    C --> C1["✅ validate_config()<br/>配置验证"]
    C1 --> C2["📁 MDB_FILE_PATH check<br/>MDB文件路径检查"]
    C1 --> C3["🖥️ IS_WINDOWS check<br/>Windows系统检查"]
    C1 --> C4["🔧 platform compatibility<br/>平台兼容性检查"]
    
    D --> D19["📋 get_config_summary()<br/>获取配置摘要"]
    D19 --> D20["📊 configuration display<br/>配置信息显示"]
    
    %% 样式定义
    classDef entryPoint fill:#e1f5fe,stroke:#01579b,stroke-width:3px
    classDef router fill:#f3e5f5,stroke:#4a148c,stroke-width:2px
    classDef mdb fill:#e8f5e8,stroke:#1b5e20,stroke-width:2px
    classDef com fill:#fff3e0,stroke:#e65100,stroke-width:2px
    classDef data fill:#fce4ec,stroke:#880e4f,stroke-width:2px
    classDef config fill:#f1f8e9,stroke:#33691e,stroke-width:2px
    classDef health fill:#f9fbe7,stroke:#827717,stroke-width:2px
    
    class A,B entryPoint
    class H,I,H1,H2,H3,I1,I2,I3,I4,I5,I6,I7,I8 router
    class M1,M2,M3,M4,M5,M6,D1,D6,D10,D13 mdb
    class W1,W2,W3,W4,W5,W6,W7,W8 com
    class D2,D3,D4,D5,D7,D8,D9,D11,D12,D14,D15,D16,D17,D18,F1,F2,F3 data
    class C,D,C1,C2,C3,C4,D19,D20,P1,P2,P3,P4 config
    class H1,H4,H5,H6 health
```



```mermaid

```

