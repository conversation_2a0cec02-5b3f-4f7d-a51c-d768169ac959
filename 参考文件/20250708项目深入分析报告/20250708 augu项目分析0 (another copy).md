  
请分析这个项目的每个服务器，都进行下列的函数调用关系调查：  
  
分析要求：  
1. **核心函数识别**  
- 列出主要的入口函数  
- 识别关键的工具函数和辅助函数  
- 标注每个函数的作用和重要性  
  
2. **调用链路分析**  
- 从main函数/入口点开始，绘制调用流程  
- 标注函数间的调用方向和次数  
- 识别循环调用和递归关系  
  
3. **模块间依赖**  
- 分析各个模块/文件间的依赖关系  
- 标注import/include关系  
- 识别紧耦合和松耦合的部分  
  
请用以下格式输出：  
- 文字描述 + ASCII流程图  
- 或者提供Mermaid图表代码  
- 按重要性排序函数列表

//


我将对MySuite项目的每个服务器进行详细的函数调用关系分析。让我逐个服务器进行深入分析。

现在开始分析第一个服务器 - Server主服务的函数调用关系：

现在让我获取更多关于Server主服务的路由和核心函数信息：

现在让我获取更多关于WebSocket和后台任务的信息：

现在我有足够的信息来分析Server主服务的函数调用关系。让我创建详细的分析报告：

# Server主服务(8003)函数调用关系分析

## 1. 核心函数识别

### 🚀 主要入口函数 (按重要性排序)

| 函数名 | 重要性 | 作用 | 调用频率 |
|--------|--------|------|----------|
| `main.py:lifespan` | ⭐⭐⭐⭐⭐ | 应用生命周期管理，初始化所有服务 | 启动时1次 |
| `main.py:root` | ⭐⭐⭐⭐ | 根路径API，返回服务状态 | 高频 |
| `feature_flags.py:get_all_flags` | ⭐⭐⭐⭐ | 获取所有功能标志 | 高频 |
| `feature_flags.py:set_flag` | ⭐⭐⭐⭐ | 设置功能标志并通知客户端 | 中频 |
| `websocket/flags_ws.py:websocket_feature_flags` | ⭐⭐⭐⭐ | WebSocket连接处理 | 持续连接 |

### 🔧 关键工具函数

| 函数名 | 重要性 | 作用 | 调用频率 |
|--------|--------|------|----------|
| `database.py:init_db` | ⭐⭐⭐⭐⭐ | 数据库初始化 | 启动时1次 |
| `websocket/manager.py:notify_all_clients` | ⭐⭐⭐⭐ | WebSocket广播通知 | 高频 |
| `task_checker.py:check_overdue_tasks_loop` | ⭐⭐⭐ | 后台任务检查循环 | 每60秒 |
| `xml_processor.py:process_xml_loop` | ⭐⭐⭐ | XML文件处理循环 | 每10秒 |
| `odbc_client.check_connection_status` | ⭐⭐⭐ | ODBC连接状态检查 | 中频 |

### 🛠️ 辅助函数

| 函数名 | 重要性 | 作用 | 调用频率 |
|--------|--------|------|----------|
| `websocket/manager.py:connect` | ⭐⭐⭐ | WebSocket连接管理 | 按需 |
| `websocket/manager.py:disconnect` | ⭐⭐⭐ | WebSocket断开管理 | 按需 |
| `task_checker.py:_internal_set_flag` | ⭐⭐ | 内部标志设置 | 低频 |
| `xml_processor.py:process_single_xml` | ⭐⭐ | 单个XML文件处理 | 按需 |

## 2. 调用链路分析

### 🔄 主要调用流程

#### 应用启动流程
```
main.py
├── lifespan() [启动]
│   ├── redis_service.initialize() → redis.Redis() → redis.ping()
│   ├── init_db() → engine.begin() → Base.metadata.create_all()
│   ├── test_db_connection() → engine.connect()
│   ├── init_imdb_tables() → create_tables()
│   ├── asyncio.create_task(check_overdue_tasks_loop())
│   └── asyncio.create_task(process_xml_loop())
├── FastAPI(lifespan=lifespan)
└── include_router() × 8个路由模块
```

#### 功能标志处理流程
```
feature_flags.py:set_flag()
├── check_db_connection()
├── AsyncSessionLocal() → session.get() → session.commit()
└── notify_all_clients() → ws.send_json() [广播到所有WebSocket客户端]
```

#### WebSocket连接流程
```
websocket_feature_flags()
├── verify_token_from_query() [JWT验证]
├── connect() → active_connections.append()
├── ws.receive_text() [循环监听]
└── disconnect() → active_connections.remove()
```

#### 后台任务流程
```
check_overdue_tasks_loop() [每60秒]
├── AsyncSessionLocal() → select(Task)
├── 检查逾期任务数量
├── _internal_set_flag() → session.commit()
└── notify_all_clients() [WebSocket通知]

process_xml_loop() [每10秒]
├── odbc_client.check_connection_status()
├── os.listdir(XML_FOLDER)
├── process_single_xml()
│   ├── ET.parse()
│   └── odbc_client.execute_non_query()
└── tree.write() [保存处理结果]
```

### 🔁 循环调用和递归关系

1. **后台任务循环**:
   - `check_overdue_tasks_loop()`: 无限循环，每60秒执行一次
   - `process_xml_loop()`: 无限循环，每10秒执行一次

2. **WebSocket连接循环**:
   - `websocket_feature_flags()`: 保持连接，持续监听客户端消息

3. **数据库会话循环**:
   - 各API函数中的`AsyncSessionLocal()`使用模式

## 3. 模块间依赖关系

### 📦 Import依赖图

```
main.py
├── .database (init_db, AsyncSessionLocal, odbc_client)
├── .databases.postgresql.client (test_db_connection, close_db, init_imdb_tables)
├── .core.services.redis_service (redis_service)
├── .routers (feature_flags, tasks, excel_upload, odbc_actions, xml_actions, client_version, counter, extra_actions)
├── .websocket.flags_ws (router as ws_router)
├── .modules.task_checker (check_overdue_tasks_loop)
└── .modules.xml_processor (process_xml_loop)

feature_flags.py
├── ..database (AsyncSessionLocal)
├── ..models (FeatureFlag)
├── ..auth.jwt_auth (verify_token)
└── ..websocket.manager (notify_all_clients)

websocket/flags_ws.py
├── .manager (connect, disconnect)
└── ..auth.jwt_auth (verify_token_from_query)

task_checker.py
├── ..database (AsyncSessionLocal)
├── ..models (Task, FeatureFlag)
└── ..websocket.manager (notify_all_clients)

xml_processor.py
├── ..database (odbc_client)
└── xml.etree.ElementTree
```

### 🔗 耦合度分析

#### 紧耦合部分 (需要关注)
1. **WebSocket管理器**: `feature_flags.py` → `websocket/manager.py`
2. **数据库会话**: 多个模块都依赖 `AsyncSessionLocal`
3. **后台任务**: `task_checker.py` → `websocket/manager.py`

#### 松耦合部分 (设计良好)
1. **路由模块**: 各路由模块相互独立
2. **服务层**: Redis、ODBC等服务封装良好
3. **配置管理**: 通过环境变量和配置文件管理

## 4. ASCII流程图

```
应用启动流程:
┌─────────────┐    ┌──────────────┐    ┌─────────────┐
│   main.py   │───▶│   lifespan   │───▶│  init_db    │
└─────────────┘    └──────────────┘    └─────────────┘
                           │                    │
                           ▼                    ▼
                   ┌──────────────┐    ┌─────────────┐
                   │ redis_service│    │PostgreSQL  │
                   │ .initialize  │    │ tables      │
                   └──────────────┘    └─────────────┘
                           │
                           ▼
                   ┌──────────────┐
                   │ 后台任务启动  │
                   │ - task_checker│
                   │ - xml_processor│
                   └──────────────┘

API请求流程:
┌─────────────┐    ┌──────────────┐    ┌─────────────┐
│ HTTP Request│───▶│ FastAPI      │───▶│ Router      │
└─────────────┘    │ Middleware   │    │ Handler     │
                   └──────────────┘    └─────────────┘
                                              │
                                              ▼
                                      ┌─────────────┐
                                      │ Database    │
                                      │ Operation   │
                                      └─────────────┘
                                              │
                                              ▼
                                      ┌─────────────┐
                                      │ WebSocket   │
                                      │ Broadcast   │
                                      └─────────────┘

WebSocket通信流程:
┌─────────────┐    ┌──────────────┐    ┌─────────────┐
│ Client WS   │◄──▶│ Connection   │───▶│ Manager     │
│ Connection  │    │ Handler      │    │ (global)    │
└─────────────┘    └──────────────┘    └─────────────┘
                           │                    │
                           ▼                    ▼
                   ┌──────────────┐    ┌─────────────┐
                   │ JWT Token    │    │ Active      │
                   │ Verification │    │ Connections │
                   └──────────────┘    │ List        │
                                      └─────────────┘
```

## 5. 重要性排序的函数列表

### 🏆 核心级函数 (系统关键)
1. `main.py:lifespan` - 应用生命周期管理
2. `database.py:init_db` - 数据库初始化
3. `feature_flags.py:set_flag` - 功能标志设置
4. `websocket/manager.py:notify_all_clients` - WebSocket广播

### 🥇 重要级函数 (业务核心)
5. `feature_flags.py:get_all_flags` - 功能标志获取
6. `websocket/flags_ws.py:websocket_feature_flags` - WebSocket连接处理
7. `tasks.py:get_pending_tasks` - 任务管理
8. `main.py:root` - 服务状态API

### 🥈 支撑级函数 (后台服务)
9. `task_checker.py:check_overdue_tasks_loop` - 后台任务检查
10. `xml_processor.py:process_xml_loop` - XML处理循环
11. `redis_service.initialize` - Redis服务初始化
12. `odbc_client.check_connection_status` - ODBC状态检查

### 🥉 辅助级函数 (工具支持)
13. `websocket/manager.py:connect/disconnect` - 连接管理
14. `task_checker.py:_internal_set_flag` - 内部标志设置
15. `xml_processor.py:process_single_xml` - 单文件处理
16. `extra_actions.py:upload_progress` - 进度上传

这个分析展现了Server主服务作为系统核心的复杂性，它承担了功能标志管理、任务调度、WebSocket通信、数据库操作等多重职责，是整个MySuite系统的控制中心。

现在分析Server2聊天微服务的函数调用关系：

现在让我获取更多关于Server2的核心服务和数据库操作的信息：

现在让我创建Server2聊天微服务的函数调用关系图：

# Server2聊天微服务(8005)函数调用关系分析

## 1. 核心函数识别

### 🚀 主要入口函数 (按重要性排序)

| 函数名 | 重要性 | 作用 | 调用频率 |
|--------|--------|------|----------|
| `main.py:lifespan` | ⭐⭐⭐⭐⭐ | 应用生命周期管理，初始化所有服务 | 启动时1次 |
| `chat_websocket.py:websocket_chat_endpoint` | ⭐⭐⭐⭐⭐ | WebSocket聊天连接处理 | 持续连接 |
| `ChatConnectionManager.broadcast_message` | ⭐⭐⭐⭐⭐ | 消息广播核心功能 | 高频 |
| `redis_service.save_chat_message` | ⭐⭐⭐⭐ | 聊天消息持久化 | 高频 |
| `ChatConnectionManager.connect` | ⭐⭐⭐⭐ | WebSocket连接管理 | 中频 |

### 🔧 关键工具函数

| 函数名 | 重要性 | 作用 | 调用频率 |
|--------|--------|------|----------|
| `redis_service.initialize` | ⭐⭐⭐⭐⭐ | Redis服务初始化 | 启动时1次 |
| `database_service.initialize` | ⭐⭐⭐⭐ | 数据库服务初始化 | 启动时1次 |
| `encryption_service.encrypt_message` | ⭐⭐⭐⭐ | 消息加密处理 | 中频 |
| `file_service.upload_file` | ⭐⭐⭐ | 文件上传处理 | 低频 |
| `jwt_auth.verify_token` | ⭐⭐⭐⭐ | JWT令牌验证 | 高频 |

### 🛠️ 辅助函数

| 函数名 | 重要性 | 作用 | 调用频率 |
|--------|--------|------|----------|
| `ChatConnectionManager._heartbeat_loop` | ⭐⭐⭐ | WebSocket心跳检测 | 持续循环 |
| `redis_service.get_service_stats` | ⭐⭐ | 服务统计信息获取 | 低频 |
| `database_service.initialize_default_rooms` | ⭐⭐ | 默认聊天室初始化 | 启动时1次 |
| `encryption_service.decrypt_message` | ⭐⭐ | 消息解密处理 | 中频 |

## 2. 调用链路分析

### 🔄 主要调用流程

#### 应用启动流程
```
main.py
├── lifespan() [启动]
│   ├── database_service.initialize()
│   │   ├── create_async_engine()
│   │   ├── async_sessionmaker()
│   │   ├── Base.metadata.create_all()
│   │   └── initialize_default_rooms()
│   ├── redis_service.initialize()
│   │   ├── redis.Redis()
│   │   └── redis_client.ping()
│   ├── encryption_service.initialize()
│   │   ├── PBKDF2HMAC()
│   │   └── AESGCM()
│   └── file_service.initialize()
├── FastAPI(lifespan=lifespan)
└── include_router() × 5个路由模块
```

#### WebSocket聊天流程
```
websocket_chat_endpoint()
├── jwt_auth.verify_token() [JWT验证]
├── chat_manager.connect()
│   ├── websocket.accept()
│   ├── active_connections[employee_id] = websocket
│   └── start_heartbeat() [心跳检测]
├── websocket.receive_text() [循环监听]
├── chat_manager.broadcast_message()
│   ├── redis_service.save_chat_message()
│   │   ├── redis_client.setex()
│   │   ├── redis_client.lpush()
│   │   └── redis_client.ltrim()
│   └── websocket.send_text() [广播到所有客户端]
└── chat_manager.disconnect() [断开时清理]
```

#### 私聊消息流程
```
private_chat.py:send_private_message()
├── get_current_user() [用户验证]
├── encryption_service.encrypt_message() [可选加密]
│   ├── os.urandom(12) [生成nonce]
│   └── aes_gcm.encrypt()
├── redis_service.set_hash() [消息存储]
├── redis_service.list_push() [用户消息列表]
├── redis_service.increment() [未读计数]
└── _push_private_message_to_user() [实时推送]
```

#### 文件共享流程
```
file_sharing.py:upload_file()
├── get_current_user() [用户验证]
├── file.read() [读取文件内容]
├── file_service.upload_file()
│   ├── file validation [文件验证]
│   ├── file storage [文件存储]
│   └── metadata save [元数据保存]
└── broadcast_file_message() [文件消息广播]
```

### 🔁 循环调用和递归关系

1. **WebSocket连接循环**:
   - `websocket_chat_endpoint()`: 保持连接，持续监听客户端消息
   - `_heartbeat_loop()`: 每30秒发送心跳检测

2. **Redis操作循环**:
   - 消息历史维护: `ltrim()` 保持固定数量的历史记录
   - 用户状态更新: 定期更新在线用户状态

3. **数据库会话管理**:
   - `async_sessionmaker()` 创建会话
   - 自动会话清理和连接池管理

## 3. 模块间依赖关系

### 📦 Import依赖图

```
main.py
├── .routers (chat_websocket, chat_management, file_sharing, private_chat, room_management)
├── .auth.jwt_auth (JWT认证)
├── .core.services (redis_service, database_service, encryption_service, file_service)
└── .config (settings)

chat_websocket.py
├── ..auth.jwt_auth (verify_token)
├── ..core.services.redis_service (save_chat_message, get_service_stats)
└── ChatConnectionManager (全局连接管理器)

private_chat.py
├── ..auth.jwt_auth (get_current_user)
├── ..core.services.redis_service (set_hash, list_push, increment)
├── ..core.services.encryption_service (encrypt_message, decrypt_message)
└── ..core.services.database_service (get_session)

file_sharing.py
├── ..auth.jwt_auth (get_current_user)
├── ..core.services.file_service (upload_file, get_file, get_room_files)
└── ..core.services.redis_service (统计和缓存)

room_management.py
├── ..auth.jwt_auth (get_current_user)
├── ..core.services.database_service (聊天室数据操作)
└── ..core.services.redis_service (缓存管理)
```

### 🔗 耦合度分析

#### 紧耦合部分 (需要关注)
1. **WebSocket管理器**: 所有聊天功能都依赖 `ChatConnectionManager`
2. **Redis服务**: 消息存储、用户状态、缓存都依赖Redis
3. **JWT认证**: 所有API都需要用户认证

#### 松耦合部分 (设计良好)
1. **服务层封装**: 各个服务(Redis、Database、Encryption、File)独立封装
2. **路由模块**: 各路由模块功能独立
3. **配置管理**: 统一的配置管理

## 4. ASCII流程图

```
应用启动流程:
┌─────────────┐    ┌──────────────┐    ┌─────────────┐
│   main.py   │───▶│   lifespan   │───▶│ Services    │
└─────────────┘    └──────────────┘    │ Initialize  │
                           │            └─────────────┘
                           ▼                    │
                   ┌──────────────┐            ▼
                   │ FastAPI App  │    ┌─────────────┐
                   │ + Routers    │    │ Database    │
                   └──────────────┘    │ Redis       │
                                      │ Encryption  │
                                      │ File Service│
                                      └─────────────┘

WebSocket聊天流程:
┌─────────────┐    ┌──────────────┐    ┌─────────────┐
│ Client WS   │───▶│ JWT Verify   │───▶│ Connection  │
│ Connect     │    │              │    │ Manager     │
└─────────────┘    └──────────────┘    └─────────────┘
                           │                    │
                           ▼                    ▼
                   ┌──────────────┐    ┌─────────────┐
                   │ Message      │    │ Redis       │
                   │ Broadcast    │◄──▶│ Storage     │
                   └──────────────┘    └─────────────┘
                           │
                           ▼
                   ┌──────────────┐
                   │ All Clients  │
                   │ Notification │
                   └──────────────┘

私聊消息流程:
┌─────────────┐    ┌──────────────┐    ┌─────────────┐
│ Private     │───▶│ Encryption   │───▶│ Redis       │
│ Message API │    │ (Optional)   │    │ Storage     │
└─────────────┘    └──────────────┘    └─────────────┘
                           │                    │
                           ▼                    ▼
                   ┌──────────────┐    ┌─────────────┐
                   │ Message      │    │ Recipient   │
                   │ Metadata     │    │ Push        │
                   └──────────────┘    └─────────────┘

文件共享流程:
┌─────────────┐    ┌──────────────┐    ┌─────────────┐
│ File Upload │───▶│ Validation   │───▶│ File        │
│ API         │    │ & Security   │    │ Storage     │
└─────────────┘    └──────────────┘    └─────────────┘
                           │                    │
                           ▼                    ▼
                   ┌──────────────┐    ┌─────────────┐
                   │ Metadata     │    │ Chat        │
                   │ Save         │    │ Broadcast   │
                   └──────────────┘    └─────────────┘
```

## 5. 重要性排序的函数列表

### 🏆 核心级函数 (系统关键)
1. `main.py:lifespan` - 应用生命周期管理
2. `chat_websocket.py:websocket_chat_endpoint` - WebSocket聊天端点
3. `ChatConnectionManager.broadcast_message` - 消息广播核心
4. `redis_service.initialize` - Redis服务初始化

### 🥇 重要级函数 (业务核心)
5. `ChatConnectionManager.connect` - WebSocket连接管理
6. `redis_service.save_chat_message` - 消息持久化
7. `jwt_auth.verify_token` - 用户认证
8. `database_service.initialize` - 数据库初始化

### 🥈 支撑级函数 (功能扩展)
9. `private_chat.py:send_private_message` - 私聊功能
10. `file_sharing.py:upload_file` - 文件共享
11. `encryption_service.encrypt_message` - 消息加密
12. `room_management.py:create_room` - 聊天室管理

### 🥉 辅助级函数 (工具支持)
13. `ChatConnectionManager._heartbeat_loop` - 心跳检测
14. `redis_service.get_service_stats` - 统计信息
15. `database_service.initialize_default_rooms` - 默认数据初始化
16. `chat_management.py:health_check` - 健康检查

## 6. 特色功能分析

### 💬 实时通信特色
- **WebSocket连接池**: 支持500+并发连接
- **心跳检测机制**: 30秒间隔的连接健康检查
- **消息广播优化**: 异步广播，失败连接自动清理

### 🔒 安全特色
- **端到端加密**: AES-GCM加密算法
- **JWT认证**: 统一的用户身份验证
- **文件安全**: 文件类型验证和大小限制

### 📊 性能特色
- **Redis缓存**: 消息历史和用户状态缓存
- **异步处理**: 全异步架构，高并发支持
- **连接管理**: 智能连接清理和资源回收

Server2聊天微服务展现了一个现代化的实时通信系统架构，具备完整的聊天、文件共享、私聊、聊天室管理功能，通过WebSocket提供低延迟的实时通信体验。

现在分析Server3认证微服务的函数调用关系：

现在让我获取更多关于Server3的JWT处理和程序授权的信息：

现在让我创建Server3认证微服务的函数调用关系图：

# Server3认证微服务(8006)函数调用关系分析

## 1. 核心函数识别

### 🚀 主要入口函数 (按重要性排序)

| 函数名 | 重要性 | 作用 | 调用频率 |
|--------|--------|------|----------|
| `main.py:lifespan` | ⭐⭐⭐⭐⭐ | 应用生命周期管理，数据库初始化 | 启动时1次 |
| `auth_login.py:login` | ⭐⭐⭐⭐⭐ | 员工登录验证核心功能 | 高频 |
| `hardware_registration.py:verify_fingerprint` | ⭐⭐⭐⭐⭐ | 硬件指纹验证 | 高频 |
| `program_authorization.py:authorize_program_launch` | ⭐⭐⭐⭐ | 程序启动授权验证 | 中频 |
| `jwt_auth.py:verify_token` | ⭐⭐⭐⭐⭐ | JWT令牌验证 | 高频 |

### 🔧 关键工具函数

| 函数名 | 重要性 | 作用 | 调用频率 |
|--------|--------|------|----------|
| `auth_login.py:load_employee_mapping` | ⭐⭐⭐⭐⭐ | 加载员工数据映射 | 高频 |
| `jwt_auth.py:create_employee_token` | ⭐⭐⭐⭐ | 创建员工专用JWT令牌 | 高频 |
| `hardware_registration.py:generate_fingerprint` | ⭐⭐⭐⭐ | 生成硬件指纹哈希 | 中频 |
| `auth_login.py:decrypt_employee_data` | ⭐⭐⭐⭐ | 解密员工数据文件 | 中频 |
| `database.py:init_db` | ⭐⭐⭐⭐ | 数据库初始化 | 启动时1次 |

### 🛠️ 辅助函数

| 函数名 | 重要性 | 作用 | 调用频率 |
|--------|--------|------|----------|
| `auth_login.py:verify_password` | ⭐⭐⭐ | 密码验证(bcrypt) | 中频 |
| `hardware_registration.py:register_fingerprint` | ⭐⭐⭐ | 硬件指纹注册 | 低频 |
| `program_authorization.py:get_employee_program_permissions` | ⭐⭐ | 获取员工程序权限 | 低频 |
| `auth_login.py:loginlocal` | ⭐⭐ | 标准用户登录 | 低频 |

## 2. 调用链路分析

### 🔄 主要调用流程

#### 应用启动流程
```
main.py
├── lifespan() [启动]
│   └── init_db()
│       ├── create_async_engine()
│       ├── async_sessionmaker()
│       └── Base.metadata.create_all()
├── FastAPI(lifespan=lifespan)
└── include_router() × 3个路由模块
```

#### 员工登录验证流程
```
auth_login.py:login()
├── load_employee_mapping()
│   ├── load_encryption_key()
│   ├── Fernet() [初始化加密器]
│   └── decrypt_employee_data()
│       └── Fernet.decrypt()
├── employee_id validation [员工ID验证]
├── password validation [密码验证]
└── create_employee_token()
    ├── jwt payload creation [包含权限信息]
    └── jwt.encode() [生成JWT令牌]
```

#### 硬件指纹验证流程
```
hardware_registration.py:verify_fingerprint()
├── load_employee_mapping() [员工数据验证]
├── select(HardwareFingerprint) [查询存储的指纹]
├── generate_fingerprint()
│   ├── data formatting [employee_id:mac:mac]
│   └── hashlib.sha256() [SHA256哈希]
└── fingerprint comparison [指纹比较验证]
```

#### 程序授权验证流程
```
program_authorization.py:authorize_program_launch()
├── verify_employee_token()
│   └── jwt.decode() [JWT令牌解析]
├── employee_id matching [员工ID匹配验证]
├── load_employee_mapping() [双重权限验证]
├── PROGRAM_PERMISSIONS check [程序权限检查]
└── permission validation [最终权限验证]
```

#### JWT令牌处理流程
```
JWT Token Lifecycle:
创建阶段:
├── create_employee_token()
│   ├── payload construction [employee_id, name, permission]
│   └── jwt.encode(payload, SECRET_KEY, ALGORITHM)

验证阶段:
├── verify_token()
│   ├── jwt.decode(token, SECRET_KEY, ALGORITHM)
│   ├── expiration check [过期时间检查]
│   └── payload extraction [载荷提取]
```

### 🔁 循环调用和递归关系

1. **员工数据加载循环**:
   - `load_employee_mapping()`: 每次认证时都会调用，但有缓存机制

2. **数据库会话管理**:
   - `AsyncSession`: 自动会话创建和清理
   - 连接池管理和重用

3. **JWT令牌生命周期**:
   - 创建 → 验证 → 过期 → 重新创建的循环

## 3. 模块间依赖关系

### 📦 Import依赖图

```
main.py
├── .database (init_db)
├── .routers (auth_login, hardware_registration, program_authorization)
└── FastAPI middleware

auth_login.py
├── ..auth.jwt_auth (verify_token, create_employee_token, create_access_token)
├── ..database (get_db)
├── ..models (User)
├── cryptography.fernet (Fernet)
├── bcrypt (password hashing)
└── jose.jwt (JWT operations)

hardware_registration.py
├── ..database (get_db)
├── ..models (HardwareFingerprint)
├── .auth_login (load_employee_mapping)
├── hashlib (SHA256)
└── sqlalchemy (database operations)

program_authorization.py
├── ..auth.jwt_auth (verify_employee_token)
├── .auth_login (load_employee_mapping)
└── permission constants (PROGRAM_PERMISSIONS)

jwt_auth.py
├── jose.jwt (JWT operations)
├── passlib.context (password hashing)
├── ..config (JWT_SECRET_KEY, JWT_ALGORITHM)
└── fastapi.security (HTTPBearer)
```

### 🔗 耦合度分析

#### 紧耦合部分 (需要关注)
1. **员工数据依赖**: 多个模块都依赖 `load_employee_mapping()`
2. **JWT认证**: 所有认证功能都依赖JWT处理
3. **数据库会话**: 硬件指纹和用户管理都依赖数据库

#### 松耦合部分 (设计良好)
1. **路由模块**: 三个路由模块功能独立
2. **加密算法**: 硬件指纹和数据加密使用不同算法
3. **认证方式**: 支持多种认证方式(员工登录、标准用户登录)

## 4. ASCII流程图

```
应用启动流程:
┌─────────────┐    ┌──────────────┐    ┌─────────────┐
│   main.py   │───▶│   lifespan   │───▶│   init_db   │
└─────────────┘    └──────────────┘    └─────────────┘
                           │                    │
                           ▼                    ▼
                   ┌──────────────┐    ┌─────────────┐
                   │ FastAPI App  │    │ Database    │
                   │ + 3 Routers  │    │ Tables      │
                   └──────────────┘    └─────────────┘

员工登录流程:
┌─────────────┐    ┌──────────────┐    ┌─────────────┐
│ Login API   │───▶│ Employee     │───▶│ Encrypted   │
│ Request     │    │ Data Load    │    │ File Decrypt│
└─────────────┘    └──────────────┘    └─────────────┘
                           │                    │
                           ▼                    ▼
                   ┌──────────────┐    ┌─────────────┐
                   │ Password     │    │ JWT Token   │
                   │ Validation   │───▶│ Creation    │
                   └──────────────┘    └─────────────┘

硬件指纹验证流程:
┌─────────────┐    ┌──────────────┐    ┌─────────────┐
│ Fingerprint │───▶│ Employee     │───▶│ Database    │
│ Request     │    │ Validation   │    │ Query       │
└─────────────┘    └──────────────┘    └─────────────┘
                           │                    │
                           ▼                    ▼
                   ┌──────────────┐    ┌─────────────┐
                   │ Generate     │    │ Fingerprint │
                   │ Current FP   │───▶│ Comparison  │
                   └──────────────┘    └─────────────┘

程序授权流程:
┌─────────────┐    ┌──────────────┐    ┌─────────────┐
│ Program     │───▶│ JWT Token    │───▶│ Permission  │
│ Launch Req  │    │ Verification │    │ Extraction  │
└─────────────┘    └──────────────┘    └─────────────┘
                           │                    │
                           ▼                    ▼
                   ┌──────────────┐    ┌─────────────┐
                   │ Employee ID  │    │ Program     │
                   │ Matching     │───▶│ Permission  │
                   └──────────────┘    │ Check       │
                                      └─────────────┘
```

## 5. 重要性排序的函数列表

### 🏆 核心级函数 (系统关键)
1. `main.py:lifespan` - 应用生命周期管理
2. `auth_login.py:login` - 员工登录验证
3. `jwt_auth.py:verify_token` - JWT令牌验证
4. `hardware_registration.py:verify_fingerprint` - 硬件指纹验证

### 🥇 重要级函数 (业务核心)
5. `auth_login.py:load_employee_mapping` - 员工数据加载
6. `jwt_auth.py:create_employee_token` - JWT令牌创建
7. `program_authorization.py:authorize_program_launch` - 程序授权
8. `database.py:init_db` - 数据库初始化

### 🥈 支撑级函数 (功能支持)
9. `hardware_registration.py:generate_fingerprint` - 硬件指纹生成
10. `auth_login.py:decrypt_employee_data` - 数据解密
11. `hardware_registration.py:register_fingerprint` - 硬件指纹注册
12. `auth_login.py:verify_password` - 密码验证

### 🥉 辅助级函数 (工具支持)
13. `auth_login.py:loginlocal` - 标准用户登录
14. `program_authorization.py:get_employee_program_permissions` - 权限查询
15. `auth_login.py:verify_token_endpoint` - Token验证端点
16. `jwt_auth.py:get_current_user` - 当前用户获取

## 6. 安全特色分析

### 🔐 多重认证机制
1. **员工ID + 密码**: 基础身份验证
2. **硬件指纹**: 设备绑定验证
3. **JWT令牌**: 会话状态管理
4. **程序授权**: 应用级权限控制

### 🛡️ 加密安全特色
1. **数据加密**: Fernet对称加密存储员工数据
2. **密码哈希**: bcrypt安全密码存储
3. **硬件指纹**: SHA256哈希生成唯一标识
4. **JWT签名**: HMAC-SHA256签名验证

### 📊 权限管理特色
1. **分级权限**: normal/admin/super权限体系
2. **程序授权**: 不同程序需要不同权限级别
3. **双重验证**: Token权限与文件权限交叉验证
4. **权限审计**: 完整的权限操作日志

### 🔄 会话管理特色
1. **Token过期**: 自动过期机制防止长期有效
2. **设备绑定**: 硬件指纹确保设备唯一性
3. **权限缓存**: 避免频繁文件读取
4. **状态同步**: 多服务间权限状态同步

Server3认证微服务展现了一个企业级的安全认证系统，通过多重验证机制、加密存储、权限管理等手段，为整个MySuite系统提供了坚实的安全基础。其设计既考虑了安全性，又兼顾了易用性和可扩展性。

现在分析Server4视频监控微服务的函数调用关系：

现在让我获取更多关于Server4的WebSocket和API路由的信息：

现在让我创建Server4视频监控微服务的函数调用关系图：

# Server4视频监控微服务(8007)函数调用关系分析

## 1. 核心函数识别

### 🚀 主要入口函数 (按重要性排序)

| 函数名 | 重要性 | 作用 | 调用频率 |
|--------|--------|------|----------|
| `main.py:lifespan` | ⭐⭐⭐⭐⭐ | 应用生命周期管理，初始化视频服务 | 启动时1次 |
| `video_service.initialize` | ⭐⭐⭐⭐⭐ | 视频服务初始化，摄像头配置 | 启动时1次 |
| `video_websocket.py:video_websocket_endpoint` | ⭐⭐⭐⭐⭐ | WebSocket视频流连接处理 | 持续连接 |
| `video_service._capture_loop` | ⭐⭐⭐⭐⭐ | 视频捕获核心循环 | 持续运行 |
| `video_broadcast_task` | ⭐⭐⭐⭐⭐ | 视频流广播任务 | 持续运行 |

### 🔧 关键工具函数

| 函数名 | 重要性 | 作用 | 调用频率 |
|--------|--------|------|----------|
| `video_service.get_latest_frame_base64` | ⭐⭐⭐⭐ | 获取最新帧的Base64编码 | 高频 |
| `VideoConnectionManager.broadcast_frame` | ⭐⭐⭐⭐ | 视频帧广播到所有客户端 | 高频 |
| `yolo_service.detect_objects` | ⭐⭐⭐⭐ | YOLO物体检测处理 | 中频 |
| `video_service.start_capture` | ⭐⭐⭐ | 启动视频捕获线程 | 低频 |
| `VideoConnectionManager.connect` | ⭐⭐⭐ | WebSocket连接管理 | 中频 |

### 🛠️ 辅助函数

| 函数名 | 重要性 | 作用 | 调用频率 |
|--------|--------|------|----------|
| `video_api.get_stats` | ⭐⭐⭐ | 获取视频服务统计信息 | 中频 |
| `video_api.get_snapshot` | ⭐⭐ | 获取视频快照 | 低频 |
| `yolo_service.initialize_model` | ⭐⭐ | 初始化YOLO模型 | 启动时1次 |
| `video_service.restart_camera` | ⭐⭐ | 重启摄像头 | 低频 |

## 2. 调用链路分析

### 🔄 主要调用流程

#### 应用启动流程
```
main.py
├── lifespan() [启动]
│   ├── video_service.initialize()
│   │   ├── cv2.VideoCapture(CAMERA_INDEX)
│   │   ├── _detect_camera_capabilities()
│   │   ├── camera.set(CAP_PROP_FRAME_WIDTH/HEIGHT)
│   │   ├── camera.set(CAP_PROP_FPS)
│   │   └── camera.set(CAP_PROP_BUFFERSIZE)
│   ├── video_service.start_capture()
│   │   ├── threading.Thread(target=_capture_loop)
│   │   └── thread.start()
│   ├── asyncio.create_task(video_broadcast_task())
│   └── asyncio.create_task(stats_broadcast_task())
├── FastAPI(lifespan=lifespan)
└── include_router() × 2个路由模块
```

#### 视频捕获核心流程
```
video_service._capture_loop() [独立线程]
├── camera.read() [读取帧]
├── yolo_service.detect_objects() [可选AI检测]
│   ├── model.predict(frame)
│   ├── draw_detections()
│   └── update statistics
├── frame_lock.acquire() [线程安全]
├── latest_frame = processed_frame [更新最新帧]
├── stats update [统计信息更新]
└── time.sleep(frame_time) [帧率控制]
```

#### WebSocket视频流传输流程
```
video_websocket_endpoint()
├── VideoConnectionManager.connect()
│   ├── websocket.accept()
│   ├── active_connections[client_id] = websocket
│   └── video_service.add_client(client_id)
├── welcome message send [发送欢迎消息]
├── websocket.receive_text() [循环监听]
│   ├── ping/pong handling [心跳处理]
│   ├── stats request [统计请求]
│   └── stream control [流控制]
└── disconnect cleanup [断开清理]

video_broadcast_task() [并行运行]
├── video_service.get_latest_frame_base64()
│   ├── frame_lock.acquire()
│   ├── cv2.imencode('.jpg', frame)
│   └── base64.b64encode(buffer)
├── connection_manager.broadcast_frame()
│   ├── asyncio.gather(*tasks) [并行发送]
│   └── websocket.send_text() [发送到所有客户端]
└── asyncio.sleep(1.0 / VIDEO_FPS) [帧率控制]
```

#### YOLO物体检测流程
```
yolo_service.detect_objects()
├── detection_lock.acquire() [线程安全]
├── model.predict(frame) [YOLO推理]
├── results processing [结果处理]
├── draw_detections() [绘制检测框]
├── statistics update [统计更新]
└── return (processed_frame, detections)
```

### 🔁 循环调用和递归关系

1. **视频捕获循环**:
   - `_capture_loop()`: 无限循环，按帧率持续捕获
   - 帧率控制: `time.sleep(1.0 / VIDEO_FPS)`

2. **视频广播循环**:
   - `video_broadcast_task()`: 无限循环，持续广播最新帧
   - 广播频率: `asyncio.sleep(1.0 / VIDEO_FPS)`

3. **统计信息广播循环**:
   - `stats_broadcast_task()`: 定期广播统计信息

4. **WebSocket连接循环**:
   - `video_websocket_endpoint()`: 保持连接，持续监听客户端消息

## 3. 模块间依赖关系

### 📦 Import依赖图

```
main.py
├── .core.services.video_service (video_service)
├── .routers (video_api, video_websocket)
├── .routers.video_websocket (video_broadcast_task, stats_broadcast_task)
└── .config (settings)

video_service.py
├── cv2 (OpenCV)
├── threading (多线程)
├── base64 (编码)
├── .yolo_service (yolo_service)
└── ..config (settings)

video_websocket.py
├── ..core.services.video_service (video_service)
├── asyncio (异步处理)
├── json (消息格式)
├── uuid (客户端ID)
└── fastapi.WebSocket

video_api.py
├── ..core.services.video_service (video_service)
├── ..core.services.yolo_service (yolo_service)
└── fastapi (HTTP API)

yolo_service.py
├── ultralytics.YOLO (YOLO模型)
├── numpy (数组处理)
├── cv2 (图像处理)
└── threading (线程安全)
```

### 🔗 耦合度分析

#### 紧耦合部分 (需要关注)
1. **视频服务核心**: 所有功能都依赖 `video_service`
2. **WebSocket管理**: 广播任务与连接管理器紧密耦合
3. **OpenCV依赖**: 视频处理完全依赖OpenCV

#### 松耦合部分 (设计良好)
1. **YOLO模块**: 可选的AI检测功能，独立封装
2. **API路由**: HTTP API与WebSocket功能分离
3. **配置管理**: 统一的配置管理

## 4. ASCII流程图

```
应用启动流程:
┌─────────────┐    ┌──────────────┐    ┌─────────────┐
│   main.py   │───▶│   lifespan   │───▶│ Video       │
└─────────────┘    └──────────────┘    │ Service     │
                           │            │ Initialize  │
                           ▼            └─────────────┘
                   ┌──────────────┐            │
                   │ Background   │            ▼
                   │ Tasks Start  │    ┌─────────────┐
                   └──────────────┘    │ Camera      │
                                      │ Setup       │
                                      └─────────────┘

视频捕获和传输流程:
┌─────────────┐    ┌──────────────┐    ┌─────────────┐
│ Camera      │───▶│ Capture      │───▶│ YOLO        │
│ Read Frame  │    │ Thread       │    │ Detection   │
└─────────────┘    └──────────────┘    │ (Optional)  │
                           │            └─────────────┘
                           ▼                    │
                   ┌──────────────┐            ▼
                   │ Frame        │    ┌─────────────┐
                   │ Buffer       │◄───│ Processed   │
                   └──────────────┘    │ Frame       │
                           │            └─────────────┘
                           ▼
                   ┌──────────────┐
                   │ Base64       │
                   │ Encoding     │
                   └──────────────┘
                           │
                           ▼
                   ┌──────────────┐
                   │ WebSocket    │
                   │ Broadcast    │
                   └──────────────┘

WebSocket连接管理流程:
┌─────────────┐    ┌──────────────┐    ┌─────────────┐
│ Client      │───▶│ WebSocket    │───▶│ Connection  │
│ Connect     │    │ Endpoint     │    │ Manager     │
└─────────────┘    └──────────────┘    └─────────────┘
                           │                    │
                           ▼                    ▼
                   ┌──────────────┐    ┌─────────────┐
                   │ Message      │    │ Active      │
                   │ Loop         │    │ Connections │
                   └──────────────┘    │ Dict        │
                           │            └─────────────┘
                           ▼                    │
                   ┌──────────────┐            ▼
                   │ Ping/Pong    │    ┌─────────────┐
                   │ Stats/Stream │    │ Parallel    │
                   │ Control      │    │ Broadcast   │
                   └──────────────┘    └─────────────┘

YOLO检测流程:
┌─────────────┐    ┌──────────────┐    ┌─────────────┐
│ Input       │───▶│ YOLO Model   │───▶│ Detection   │
│ Frame       │    │ Inference    │    │ Results     │
└─────────────┘    └──────────────┘    └─────────────┘
                           │                    │
                           ▼                    ▼
                   ┌──────────────┐    ┌─────────────┐
                   │ GPU/CPU      │    │ Bounding    │
                   │ Processing   │    │ Boxes       │
                   └──────────────┘    └─────────────┘
                                              │
                                              ▼
                                      ┌─────────────┐
                                      │ Annotated   │
                                      │ Frame       │
                                      └─────────────┘
```

## 5. 重要性排序的函数列表

### 🏆 核心级函数 (系统关键)
1. `main.py:lifespan` - 应用生命周期管理
2. `video_service.initialize` - 视频服务初始化
3. `video_service._capture_loop` - 视频捕获核心循环
4. `video_broadcast_task` - 视频流广播任务

### 🥇 重要级函数 (业务核心)
5. `video_websocket_endpoint` - WebSocket连接处理
6. `video_service.get_latest_frame_base64` - 帧编码处理
7. `VideoConnectionManager.broadcast_frame` - 帧广播
8. `video_service.start_capture` - 捕获启动

### 🥈 支撑级函数 (功能扩展)
9. `yolo_service.detect_objects` - AI物体检测
10. `VideoConnectionManager.connect` - 连接管理
11. `video_api.get_stats` - 统计信息API
12. `stats_broadcast_task` - 统计信息广播

### 🥉 辅助级函数 (工具支持)
13. `video_api.get_snapshot` - 视频快照
14. `video_service.restart_camera` - 摄像头重启
15. `yolo_service.initialize_model` - YOLO模型初始化
16. `video_api.test_camera` - 摄像头测试

## 6. 技术特色分析

### 📹 视频处理特色
- **实时捕获**: 30FPS高帧率视频捕获
- **多线程架构**: 捕获线程与主线程分离
- **帧缓冲优化**: 单帧缓冲减少延迟
- **质量控制**: 可配置的JPEG质量和编码参数

### 🌐 WebSocket通信特色
- **并行广播**: 使用`asyncio.gather`并行发送到多客户端
- **连接管理**: 智能连接状态管理和异常处理
- **心跳检测**: ping/pong机制保持连接活跃
- **消息路由**: 支持多种消息类型处理

### 🤖 AI集成特色
- **YOLO v8**: 集成最新的YOLO物体检测模型
- **CPU优化**: 自动降级到CPU推理避免GPU内存问题
- **实时检测**: 与视频流无缝集成的实时物体检测
- **可选启用**: 检测功能可动态开启/关闭

### ⚡ 性能优化特色
- **异步架构**: 全异步处理，高并发支持
- **资源管理**: 智能的摄像头资源管理和清理
- **错误恢复**: 自动错误恢复和重连机制
- **统计监控**: 完整的性能统计和监控

Server4视频监控微服务展现了一个现代化的实时视频处理系统，通过OpenCV、WebSocket、YOLO等技术的深度集成，提供了高性能的视频监控和AI检测能力，是MySuite系统中技术含量最高的微服务之一。

现在分析Server5数据同步微服务的函数调用关系：

现在让我获取更多关于Server5的F6用户同步和Redis任务队列的信息：

现在让我创建Server5数据同步微服务的函数调用关系图：

# Server5数据同步微服务(8009)函数调用关系分析

## 1. 核心函数识别

### 🚀 主要入口函数 (按重要性排序)

| 函数名 | 重要性 | 作用 | 调用频率 |
|--------|--------|------|----------|
| `main.py:lifespan` | ⭐⭐⭐⭐⭐ | 应用生命周期管理，启动所有F1-F6服务 | 启动时1次 |
| `f1_listener.py:_handle_notification` | ⭐⭐⭐⭐⭐ | PostgreSQL NOTIFY事件处理核心 | 高频 |
| `f2_push_writer.py:_worker_loop` | ⭐⭐⭐⭐⭐ | MDB推送回写工作循环 | 持续运行 |
| `f4_operation_handler.py:handle_batch_operations` | ⭐⭐⭐⭐ | 客户端操作批处理 | 高频 |
| `f3_data_puller.py:run_full_sync_cycle` | ⭐⭐⭐⭐ | 完整数据同步周期 | 定时执行 |

### 🔧 关键工具函数

| 函数名 | 重要性 | 作用 | 调用频率 |
|--------|--------|------|----------|
| `redis_client.push_task` | ⭐⭐⭐⭐⭐ | Redis任务队列推送 | 高频 |
| `server6_client.insert_entry` | ⭐⭐⭐⭐ | MDB数据插入 | 中频 |
| `f2_push_writer.py:_trigger_f6_sync` | ⭐⭐⭐⭐ | 触发F6用户同步 | 中频 |
| `f5_bulk_sync.py:run_deletion_sync` | ⭐⭐⭐ | 批量删除同步 | 定时执行 |
| `mongodb_client.log_sync_operation` | ⭐⭐⭐ | 同步操作日志记录 | 高频 |

### 🛠️ 辅助函数

| 函数名 | 重要性 | 作用 | 调用频率 |
|--------|--------|------|----------|
| `f6_user_sync.py:_sync_worker_loop` | ⭐⭐⭐ | 用户同步工作循环 | 持续运行 |
| `f3_data_puller.py:_sync_to_db` | ⭐⭐ | 数据库同步处理 | 中频 |
| `redis_client.pop_task` | ⭐⭐ | Redis任务队列弹出 | 中频 |
| `mongodb_client.log_error` | ⭐⭐ | 错误日志记录 | 低频 |

## 2. 调用链路分析

### 🔄 主要调用流程

#### 应用启动流程
```
main.py
├── lifespan() [启动]
│   └── start_services()
│       ├── f1_listener.start()
│       │   ├── imdb_client.connect()
│       │   ├── redis_client.connect()
│       │   ├── mongo_client.connect()
│       │   └── _start_listener_loop()
│       │       ├── asyncpg.connect()
│       │       ├── conn.add_listener('push_job')
│       │       ├── conn.add_listener('sync_trigger')
│       │       └── _handle_notification()
│       ├── f2_push_writer.start() → _worker_loop()
│       ├── f3_data_puller.start() → _data_pull_loop()
│       ├── f4_operation_handler.start()
│       ├── f5_bulk_sync.start()
│       └── f6_user_sync.start() → _sync_worker_loop()
```

#### 数据同步核心流程 (用户操作触发)
```
客户端UI操作
├── f4_operation_handler.handle_insert_operation()
│   ├── imdb_client.execute_command() [INSERT到entries表]
│   └── source='user' [标记为用户操作]
├── PostgreSQL触发器 trg_entries_enqueue()
│   ├── entries_push_queue INSERT [队列项创建]
│   └── pg_notify('push_job', queue_id) [通知F1]
├── f1_listener._handle_notification()
│   ├── _handle_push_job(queue_id)
│   └── redis_client.push_task('server5:push_jobs', task_data)
├── f2_push_writer._worker_loop()
│   ├── entries_push_queue query [获取待处理任务]
│   ├── _process_queue_item_in_transaction()
│   │   ├── _handle_insert_operation()
│   │   │   ├── server6_client.insert_entry() [写入MDB]
│   │   │   └── external_id回写到PostgreSQL
│   │   └── _trigger_f6_sync() [触发用户同步]
│   └── redis_client.push_task('server5:user_sync_jobs')
└── f6_user_sync._sync_worker_loop()
    ├── redis_client.pop_task('server5:user_sync_jobs')
    └── 用户数据一致性检查和校正
```

#### MDB到PostgreSQL数据拉取流程
```
f3_data_puller.run_full_sync_cycle() [定时执行]
├── pull_recent_data() [F3阶段: UPSERT]
│   ├── server6_client.query_entries() [从MDB拉取数据]
│   └── _sync_to_db() [同步到PostgreSQL]
├── DeletionSyncService.start() [F5阶段: DELETE]
├── f5.run_deletion_sync()
│   ├── server6_client.query_entries() [MDB数据]
│   ├── imdb_client.fetch_all() [PostgreSQL数据]
│   └── 比较差异并删除PostgreSQL中多余记录
└── f5.stop() [清理F5服务]
```

#### Redis任务队列处理流程
```
Redis任务队列系统
├── 队列类型:
│   ├── server5:push_jobs [F2推送任务]
│   └── server5:user_sync_jobs [F6用户同步任务]
├── 推送操作:
│   ├── redis_client.push_task() [任务入队]
│   └── json.dumps(task_data) [序列化]
├── 弹出操作:
│   ├── redis_client.pop_task() [阻塞弹出]
│   └── json.loads(task_json) [反序列化]
└── 队列管理:
    ├── get_queue_length() [队列长度]
    └── clear_queue() [清空队列]
```

### 🔁 循环调用和递归关系

1. **F1监听器循环**:
   - `_start_listener_loop()`: 无限循环监听PostgreSQL NOTIFY事件

2. **F2工作线程循环**:
   - `_worker_loop()`: 持续处理entries_push_queue中的任务

3. **F3数据拉取循环**:
   - `_data_pull_loop()`: 定时执行完整同步周期

4. **F6用户同步循环**:
   - `_sync_worker_loop()`: 持续处理用户同步任务队列
   - `_auto_sync_loop()`: 定时自动同步

## 3. 模块间依赖关系

### 📦 Import依赖图

```
main.py
├── .services (f1_listener, f2_push_writer, f3_data_puller, f4_operation_handler, f5_bulk_sync, f6_user_sync)
└── FastAPI application

f1_listener.py
├── ..database (IMDBClient, RedisClient, MongoDBClient)
├── asyncpg (PostgreSQL异步连接)
└── datetime, logging

f2_push_writer.py
├── ..database (IMDBClient, RedisClient, MongoDBClient)
├── ..utils.server6_client (Server6Client)
└── asyncio, threading

f3_data_puller.py
├── ..database (IMDBClient, RedisClient)
├── ..utils.server6_client (Server6Client)
├── .f5_bulk_sync (DeletionSyncService)
└── ..utils.server6_field_mapper

f4_operation_handler.py
├── ..database (IMDBClient, RedisClient, MongoDBClient)
└── decimal, datetime

f5_bulk_sync.py
├── ..database (IMDBClient, RedisClient, MongoDBClient)
├── ..utils.server6_client (Server6Client)
└── datetime, timedelta

f6_user_sync.py
├── ..database (IMDBClient, RedisClient, MongoDBClient)
├── ..utils.server6_client (Server6Client)
├── ..utils.mdb_constants
└── datetime, timedelta
```

### 🔗 耦合度分析

#### 紧耦合部分 (需要关注)
1. **Server6依赖**: F2、F3、F5都依赖Server6Client进行MDB操作
2. **Redis任务队列**: F1、F2、F6通过Redis队列紧密协作
3. **PostgreSQL触发器**: F1与数据库触发器紧密耦合

#### 松耦合部分 (设计良好)
1. **服务独立性**: F1-F6各服务功能独立，可单独启停
2. **数据库客户端**: 统一的数据库客户端封装
3. **配置管理**: 统一的配置管理系统

## 4. ASCII流程图

```
数据同步完整流程:
┌─────────────┐    ┌──────────────┐    ┌─────────────┐
│ Client UI   │───▶│ F4 Operation │───▶│ PostgreSQL  │
│ Operation   │    │ Handler      │    │ entries     │
└─────────────┘    └──────────────┘    └─────────────┘
                           │                    │
                           ▼                    ▼
                   ┌──────────────┐    ┌─────────────┐
                   │ source=user  │    │ Trigger     │
                   │ Marking      │    │ Function    │
                   └──────────────┘    └─────────────┘
                                              │
                                              ▼
                                      ┌─────────────┐
                                      │ pg_notify   │
                                      │ push_job    │
                                      └─────────────┘
                                              │
                                              ▼
┌─────────────┐    ┌──────────────┐    ┌─────────────┐
│ F1 Listener │◄───│ PostgreSQL   │    │ Redis Task  │
│ Service     │    │ NOTIFY       │───▶│ Queue       │
└─────────────┘    └──────────────┘    └─────────────┘
        │                                      │
        ▼                                      ▼
┌─────────────┐    ┌──────────────┐    ┌─────────────┐
│ F2 Push     │◄───│ Queue        │    │ F6 User     │
│ Writer      │    │ Processing   │───▶│ Sync        │
└─────────────┘    └──────────────┘    └─────────────┘
        │
        ▼
┌─────────────┐    ┌──────────────┐    ┌─────────────┐
│ Server6     │───▶│ MDB Access   │───▶│ external_id │
│ Gateway     │    │ Database     │    │ Mapping     │
└─────────────┘    └──────────────┘    └─────────────┘

MDB到PostgreSQL拉取流程:
┌─────────────┐    ┌──────────────┐    ┌─────────────┐
│ F3 Data     │───▶│ Server6      │───▶│ MDB Query   │
│ Puller      │    │ Gateway      │    │ Recent Data │
└─────────────┘    └──────────────┘    └─────────────┘
        │                                      │
        ▼                                      ▼
┌─────────────┐    ┌──────────────┐    ┌─────────────┐
│ PostgreSQL  │◄───│ Data         │    │ F5 Deletion │
│ UPSERT      │    │ Comparison   │───▶│ Sync        │
└─────────────┘    └──────────────┘    └─────────────┘

六大服务协作关系:
┌─────────────┐    ┌──────────────┐    ┌─────────────┐
│ F1: Listen  │───▶│ F2: Push     │───▶│ F6: User    │
│ PostgreSQL  │    │ to MDB       │    │ Sync        │
└─────────────┘    └──────────────┘    └─────────────┘
        ▲                                      │
        │                                      ▼
┌─────────────┐    ┌──────────────┐    ┌─────────────┐
│ F4: Client  │    │ F3: Pull     │◄───│ Redis Task  │
│ Operations  │    │ from MDB     │    │ Queue       │
└─────────────┘    └──────────────┘    └─────────────┘
                           │
                           ▼
                   ┌──────────────┐
                   │ F5: Bulk     │
                   │ Deletion     │
                   └──────────────┘
```

## 5. 重要性排序的函数列表

### 🏆 核心级函数 (系统关键)
1. `main.py:lifespan` - 应用生命周期管理
2. `f1_listener.py:_handle_notification` - PostgreSQL事件处理核心
3. `f2_push_writer.py:_worker_loop` - MDB推送回写循环
4. `redis_client.push_task` - Redis任务队列推送

### 🥇 重要级函数 (业务核心)
5. `f4_operation_handler.py:handle_batch_operations` - 客户端操作处理
6. `f3_data_puller.py:run_full_sync_cycle` - 完整同步周期
7. `server6_client.insert_entry` - MDB数据插入
8. `f2_push_writer.py:_trigger_f6_sync` - F6同步触发

### 🥈 支撑级函数 (功能支持)
9. `f5_bulk_sync.py:run_deletion_sync` - 批量删除同步
10. `f6_user_sync.py:_sync_worker_loop` - 用户同步循环
11. `mongodb_client.log_sync_operation` - 同步日志记录
12. `f1_listener.py:_handle_push_job` - 推送任务处理

### 🥉 辅助级函数 (工具支持)
13. `f3_data_puller.py:_sync_to_db` - 数据库同步
14. `redis_client.pop_task` - 任务队列弹出
15. `mongodb_client.log_error` - 错误日志记录
16. `f6_user_sync.py:_auto_sync_loop` - 自动同步循环

## 6. 数据同步特色分析

### 🔄 六大功能模块协作
1. **F1监听器**: PostgreSQL NOTIFY事件监听和分区管理
2. **F2推送回写**: 异步MDB写入和ID映射回写
3. **F3拉取同步**: MDB数据拉取和差异检测
4. **F4操作处理**: 客户端操作处理和触发器调用
5. **F5批量同步**: 全量数据同步和删除处理
6. **F6用户同步**: 专属用户数据同步和覆盖校正

### 📡 事件驱动架构
- **PostgreSQL触发器**: 自动触发数据同步流程
- **NOTIFY/LISTEN**: 实时事件通知机制
- **Redis任务队列**: 异步任务处理和解耦
- **MongoDB日志**: 完整的操作审计和错误追踪

### 🔒 数据一致性保证
- **事务处理**: 数据库事务保证原子性
- **ID映射**: external_id确保数据关联
- **重试机制**: 失败任务自动重试
- **双向同步**: MDB-PostgreSQL双向数据同步

### ⚡ 性能优化特色
- **异步处理**: 全异步架构，高并发支持
- **批量操作**: 批量数据处理提高效率
- **分区表**: PostgreSQL分区表优化查询性能
- **连接池**: 数据库连接池管理

Server5数据同步微服务展现了一个复杂的企业级数据同步系统，通过六大功能模块的精密协作，实现了MDB-PostgreSQL之间的可靠双向数据同步，是MySuite系统中最复杂和最关键的微服务。

现在分析最后一个服务器 - Server6 MDB网关微服务的函数调用关系：

现在让我获取更多关于Server6的主应用和路由的信息：

现在让我创建Server6 MDB网关微服务的函数调用关系图：

# Server6 MDB网关微服务(8009)函数调用关系分析

## 1. 核心函数识别

### 🚀 主要入口函数 (按重要性排序)

| 函数名 | 重要性 | 作用 | 调用频率 |
|--------|--------|------|----------|
| `main.py:lifespan` | ⭐⭐⭐⭐⭐ | 应用生命周期管理，配置验证 | 启动时1次 |
| `mdb_api.py:insert_entry` | ⭐⭐⭐⭐⭐ | MDB记录插入API端点 | 高频 |
| `mdb_api.py:query_entries` | ⭐⭐⭐⭐⭐ | MDB记录查询API端点 | 高频 |
| `MDBClient._execute_in_com_thread` | ⭐⭐⭐⭐⭐ | COM线程安全执行核心 | 高频 |
| `mdb_api.py:update_entry` | ⭐⭐⭐⭐ | MDB记录更新API端点 | 中频 |

### 🔧 关键工具函数

| 函数名 | 重要性 | 作用 | 调用频率 |
|--------|--------|------|----------|
| `MDBClient.insert_record` | ⭐⭐⭐⭐⭐ | MDB记录插入核心逻辑 | 高频 |
| `MDBClient.query_records` | ⭐⭐⭐⭐⭐ | MDB记录查询核心逻辑 | 高频 |
| `MDBClient.test_connection` | ⭐⭐⭐⭐ | MDB连接测试 | 中频 |
| `validate_config` | ⭐⭐⭐⭐ | 配置验证 | 启动时1次 |
| `health_check` | ⭐⭐⭐ | 健康检查端点 | 中频 |

### 🛠️ 辅助函数

| 函数名 | 重要性 | 作用 | 调用频率 |
|--------|--------|------|----------|
| `MDBClient.update_record` | ⭐⭐⭐ | MDB记录更新逻辑 | 中频 |
| `MDBClient.delete_record` | ⭐⭐ | MDB记录删除逻辑 | 低频 |
| `get_config_summary` | ⭐⭐ | 配置摘要显示 | 启动时1次 |
| `ConnectionPool.get_status` | ⭐⭐ | 连接池状态获取 | 低频 |

## 2. 调用链路分析

### 🔄 主要调用流程

#### 应用启动流程
```
main.py
├── lifespan() [启动]
│   ├── validate_config()
│   │   ├── MDB_FILE_PATH check [MDB文件路径验证]
│   │   ├── IS_WINDOWS check [Windows平台验证]
│   │   └── platform compatibility [平台兼容性检查]
│   ├── get_config_summary() [配置摘要显示]
│   └── setup_logging() [日志系统初始化]
├── FastAPI(lifespan=lifespan)
├── add_middleware(CORSMiddleware) [CORS中间件]
└── include_router() × 2个路由模块
```

#### MDB记录插入流程
```
mdb_api.py:insert_entry()
├── EntryRecordCreate model validation [Pydantic模型验证]
├── get_mdb_client() [获取MDB客户端实例]
├── MDBClient.insert_record()
│   └── _execute_in_com_thread(_insert)
│       ├── pythoncom.CoInitialize() [COM初始化]
│       ├── win32com.client.Dispatch("Access.Application")
│       ├── access.OpenDatabase(MDB_FILE_PATH)
│       ├── field mapping [字段名映射]
│       │   ├── English → Japanese mapping
│       │   └── Japanese → Japanese mapping
│       ├── data validation [数据验证]
│       ├── db.OpenRecordset(TABLE_NAME)
│       ├── rs.AddNew() [添加新记录]
│       ├── field assignment [字段赋值]
│       ├── rs.Update() [提交更新]
│       ├── get inserted_id [获取自增ID]
│       └── pythoncom.CoUninitialize() [COM清理]
└── OperationResult response [返回操作结果]
```

#### MDB记录查询流程
```
mdb_api.py:query_entries()
├── query parameters validation [查询参数验证]
├── get_mdb_client() [获取MDB客户端实例]
├── MDBClient.query_records()
│   └── _execute_in_com_thread(_query)
│       ├── pythoncom.CoInitialize() [COM初始化]
│       ├── win32com.client.Dispatch("Access.Application")
│       ├── access.OpenDatabase(MDB_FILE_PATH)
│       ├── WHERE clause construction [WHERE子句构建]
│       ├── ORDER BY clause [排序子句]
│       ├── LIMIT clause [限制子句]
│       ├── db.OpenRecordset(SQL) [执行查询]
│       ├── result iteration [结果遍历]
│       ├── result formatting [结果格式化]
│       └── pythoncom.CoUninitialize() [COM清理]
└── APIResponse with data [返回查询结果]
```

#### 健康检查流程
```
health.py:health_check()
├── mdb_client.test_connection()
│   └── _execute_in_com_thread(_test)
│       ├── pythoncom.CoInitialize()
│       ├── win32com.client.Dispatch("Access.Application")
│       ├── access.OpenDatabase(MDB_FILE_PATH)
│       ├── db.TableDefs.Count [测试表数量]
│       └── pythoncom.CoUninitialize()
├── uptime calculation [运行时间计算]
├── system_info collection [系统信息收集]
├── connection_pool status [连接池状态]
└── HealthStatus model response [健康状态响应]
```

### 🔁 循环调用和递归关系

1. **COM线程重试循环**:
   - `_execute_in_com_thread()`: 最多3次重试，每次间隔1秒

2. **错误恢复机制**:
   - COM初始化失败时的重试逻辑
   - 数据库连接失败时的重试机制

3. **资源清理循环**:
   - 每次COM操作后的资源清理
   - 异常情况下的强制清理

## 3. 模块间依赖关系

### 📦 Import依赖图

```
main.py
├── .routers (health, mdb_api)
├── .utils.logger (setup_logging)
├── config.config (validate_config, get_config_summary)
└── fastapi (FastAPI, CORSMiddleware)

mdb_api.py
├── ..models.mdb_models (EntryRecordCreate, EntryRecordUpdate, QueryByEmployee, QueryByDate)
├── ..models.api_models (APIResponse, OperationResult)
├── ..core.mdb_client (MDBClient, COL_*)
└── fastapi (APIRouter, HTTPException, Depends)

mdb_client.py
├── win32com.client (Windows COM)
├── pythoncom (COM线程管理)
├── config.config (MDB_FILE_PATH, IS_WINDOWS)
├── ..models.api_models (OperationResult)
└── platform (系统检测)

health.py
├── ..models.api_models (HealthStatus)
├── ..core.mdb_client (MDBClient)
├── platform (系统信息)
└── time (运行时间)
```

### 🔗 耦合度分析

#### 紧耦合部分 (需要关注)
1. **Windows平台依赖**: 完全依赖Windows和win32com
2. **MDB文件依赖**: 所有操作都依赖特定的MDB文件
3. **COM线程管理**: 所有数据库操作都需要COM线程

#### 松耦合部分 (设计良好)
1. **API路由**: HTTP API与MDB客户端分离
2. **模型定义**: Pydantic模型独立定义
3. **配置管理**: 统一的配置管理系统

## 4. ASCII流程图

```
应用启动流程:
┌─────────────┐    ┌──────────────┐    ┌─────────────┐
│   main.py   │───▶│   lifespan   │───▶│ Config      │
└─────────────┘    └──────────────┘    │ Validation  │
                           │            └─────────────┘
                           ▼                    │
                   ┌──────────────┐            ▼
                   │ FastAPI App  │    ┌─────────────┐
                   │ + Routers    │    │ Logging     │
                   └──────────────┘    │ Setup       │
                                      └─────────────┘

MDB操作流程:
┌─────────────┐    ┌──────────────┐    ┌─────────────┐
│ HTTP API    │───▶│ Pydantic     │───▶│ MDBClient   │
│ Request     │    │ Validation   │    │ Method      │
└─────────────┘    └──────────────┘    └─────────────┘
                           │                    │
                           ▼                    ▼
                   ┌──────────────┐    ┌─────────────┐
                   │ Field        │    │ COM Thread  │
                   │ Mapping      │───▶│ Execution   │
                   └──────────────┘    └─────────────┘
                                              │
                                              ▼
                                      ┌─────────────┐
                                      │ win32com    │
                                      │ Access DB   │
                                      └─────────────┘

COM线程操作流程:
┌─────────────┐    ┌──────────────┐    ┌─────────────┐
│ COM         │───▶│ Access       │───▶│ Database    │
│ Initialize  │    │ Application  │    │ Open        │
└─────────────┘    └──────────────┘    └─────────────┘
        │                                      │
        ▼                                      ▼
┌─────────────┐    ┌──────────────┐    ┌─────────────┐
│ Retry       │◄───│ Error        │    │ Recordset   │
│ Logic       │    │ Handling     │◄───│ Operations  │
└─────────────┘    └──────────────┘    └─────────────┘
        │                                      │
        ▼                                      ▼
┌─────────────┐    ┌──────────────┐    ┌─────────────┐
│ Resource    │    │ Result       │    │ Data        │
│ Cleanup     │◄───│ Processing   │◄───│ Retrieval   │
└─────────────┘    └──────────────┘    └─────────────┘

字段映射流程:
┌─────────────┐    ┌──────────────┐    ┌─────────────┐
│ English     │───▶│ Field Map    │───▶│ Japanese    │
│ Field Names │    │ Dictionary   │    │ Column Names│
└─────────────┘    └──────────────┘    └─────────────┘
        │                                      │
        ▼                                      ▼
┌─────────────┐    ┌──────────────┐    ┌─────────────┐
│ employee_id │───▶│ 従業員ｺｰﾄﾞ   │    │ MDB Table   │
│ entry_date  │───▶│ 日付         │───▶│ 元作業時間   │
│ duration    │───▶│ 時間         │    │             │
└─────────────┘    └──────────────┘    └─────────────┘
```

## 5. 重要性排序的函数列表

### 🏆 核心级函数 (系统关键)
1. `main.py:lifespan` - 应用生命周期管理
2. `MDBClient._execute_in_com_thread` - COM线程安全执行
3. `mdb_api.py:insert_entry` - MDB记录插入API
4. `mdb_api.py:query_entries` - MDB记录查询API

### 🥇 重要级函数 (业务核心)
5. `MDBClient.insert_record` - 记录插入核心逻辑
6. `MDBClient.query_records` - 记录查询核心逻辑
7. `mdb_api.py:update_entry` - MDB记录更新API
8. `validate_config` - 配置验证

### 🥈 支撑级函数 (功能支持)
9. `MDBClient.test_connection` - 连接测试
10. `health.py:health_check` - 健康检查
11. `MDBClient.update_record` - 记录更新逻辑
12. `mdb_api.py:get_distinct_employees` - 员工列表获取

### 🥉 辅助级函数 (工具支持)
13. `MDBClient.delete_record` - 记录删除逻辑
14. `get_config_summary` - 配置摘要
15. `ConnectionPool.get_status` - 连接池状态
16. `health.py:status` - 详细状态信息

## 6. 技术特色分析

### 🖥️ Windows平台特化
- **win32com集成**: 深度集成Windows COM技术
- **Access原生支持**: 直接操作Access数据库文件
- **平台检测**: 智能的平台兼容性检测
- **COM线程安全**: 完善的COM线程管理机制

### 🔄 数据转换特色
- **字段映射**: 英文-日文字段名双向映射
- **数据验证**: Pydantic模型严格验证
- **格式转换**: 日期、数值等格式自动转换
- **编码处理**: 多语言字符编码支持

### 🛡️ 错误处理特色
- **重试机制**: 3次重试，指数退避
- **资源清理**: 完善的COM资源清理
- **异常捕获**: 多层次异常处理
- **降级模式**: 非Windows环境的模拟模式

### 📡 API设计特色
- **RESTful接口**: 标准的REST API设计
- **类型安全**: 完整的类型注解和验证
- **文档自动生成**: FastAPI自动API文档
- **健康检查**: 完善的服务监控端点

### ⚡ 性能优化特色
- **连接复用**: 智能的数据库连接管理
- **批量操作**: 支持批量数据处理
- **异步架构**: FastAPI异步处理能力
- **缓存机制**: 配置和状态信息缓存

Server6 MDB网关微服务展现了一个专业的数据库网关设计，通过Windows COM技术与Access数据库的深度集成，为MySuite系统提供了可靠的MDB数据访问能力。其设计既考虑了Windows平台的特殊性，又保持了良好的API抽象和错误处理机制。
