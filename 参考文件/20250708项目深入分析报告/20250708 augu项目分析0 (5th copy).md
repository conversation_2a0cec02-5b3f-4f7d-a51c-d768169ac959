

  
把server5 server6 彻底剖析，重点是同步和拉取机制，的详细区别 。  
比如拉取是，把数据，从哪里到哪里的拉取，拉取的时候做了什么操作？  
而同步又做了什么操作？  
  
并且start_ubuntu_remote_quiet.py启动以后，到底具体会做什么？有什么逻辑机制和函数交互关系。




我将深入剖析Server5和Server6的同步与拉取机制，以及启动脚本的详细逻辑。让我先获取相关的核心代码。

现在让我获取Server6的核心实现和启动脚本的详细信息：

现在让我获取启动脚本的详细信息：

现在让我创建详细的分析和流程图。

# Server5 & Server6 数据同步与拉取机制深度剖析

## 📋 核心概念区分

### 🔄 同步 vs 拉取的本质区别

#### **同步 (Sync)** - 推送模式
- **方向**: PostgreSQL → MDB
- **触发**: 事件驱动 (PostgreSQL NOTIFY)
- **实时性**: 实时响应
- **数据流**: 用户操作 → entries表 → 触发器 → F1监听 → F2推送 → MDB

#### **拉取 (Pull)** - 拉取模式  
- **方向**: MDB → PostgreSQL
- **触发**: 定时任务
- **实时性**: 定期执行 (5分钟间隔)
- **数据流**: MDB → Server6网关 → F3拉取 → PostgreSQL

## 🏗️ Server5 六大服务架构详解

### 📊 F1-F6服务职责分工

| 服务 | 名称 | 核心职责 | 数据方向 | 触发方式 |
|------|------|----------|----------|----------|
| **F1** | Listener监听器 | PostgreSQL NOTIFY监听 | - | 事件驱动 |
| **F2** | 推送回写引擎 | PostgreSQL → MDB同步 | PG → MDB | Redis队列 |
| **F3** | 拉取同步器 | MDB → PostgreSQL拉取 | MDB → PG | 定时任务 |
| **F4** | 操作处理器 | 客户端操作处理 | 客户端 → PG | HTTP请求 |
| **F5** | 批量同步器 | 数据一致性检查 | 双向比较 | F3调用 |
| **F6** | 专属ID同步器 | 用户数据校正 | 双向校正 | Redis队列 |

## 🔄 数据同步机制详细分析

### 1. 推送同步流程 (PostgreSQL → MDB)

### 2. 拉取同步流程 (MDB → PostgreSQL)

## 🔧 Server6 MDB网关核心机制

### 3. Server6 COM线程安全机制

## 🚀 启动脚本详细分析

### 4. start_ubuntu_remote_quiet.py启动逻辑

## 📊 同步与拉取机制深度对比

### 🔄 核心区别总结

| 维度 | 推送同步 (Sync) | 拉取同步 (Pull) |
|------|----------------|----------------|
| **数据方向** | PostgreSQL → MDB | MDB → PostgreSQL |
| **触发方式** | 事件驱动 (PostgreSQL NOTIFY) | 定时任务 (5分钟间隔) |
| **实时性** | 实时响应 (毫秒级) | 定期执行 (分钟级) |
| **数据完整性** | 增量同步 | 全量比较 |
| **主要服务** | F1监听器 + F2推送回写 | F3拉取器 + F5批量同步 |
| **Redis队列** | server5:push_jobs | 不使用队列 |
| **操作类型** | INSERT/UPDATE/DELETE | UPSERT + DELETE |
| **ID映射** | 回写external_id | 基于external_id匹配 |

### 🎯 具体操作详解

#### **推送同步做了什么？**
1. **监听PostgreSQL变化**: F1监听器实时监听entries表的变化
2. **队列化处理**: 将变化事件推送到Redis队列
3. **异步处理**: F2推送回写引擎从队列中取出任务
4. **MDB写入**: 通过Server6网关写入MDB数据库
5. **ID回写**: 将MDB的自增ID回写到PostgreSQL的external_id字段
6. **用户同步**: 触发F6用户同步进行数据校正

#### **拉取同步做了什么？**
1. **定时触发**: 每5分钟自动执行一次完整同步周期
2. **活跃用户查询**: 从PostgreSQL获取最近活跃的用户列表
3. **MDB数据拉取**: 通过Server6网关查询MDB中的最新数据
4. **数据比较**: 将MDB数据与PostgreSQL数据进行比较
5. **UPSERT操作**: 执行插入或更新操作同步到PostgreSQL
6. **删除同步**: F5服务比较两边数据，删除PostgreSQL中多余的记录

### 🔧 Server6网关的作用

#### **在推送同步中**:
- 接收Server5的HTTP请求
- 执行MDB的INSERT/UPDATE/DELETE操作
- 返回操作结果和新记录的ID

#### **在拉取同步中**:
- 接收Server5的查询请求
- 执行MDB的SELECT操作
- 返回查询结果数据

#### **COM线程安全机制**:
- 每次操作都在独立的COM线程中执行
- 自动重试机制 (最多3次)
- 完善的资源清理和异常处理

## 🚀 启动脚本执行后的具体行为

### 📋 start_ubuntu_remote_quiet.py启动后会做什么？

#### **1. 初始化阶段**
- 配置简化日志系统 (终端只显示WARNING，文件记录详细DEBUG)
- 设置项目路径和导入配置
- 创建F1-F4服务实例

#### **2. 服务启动阶段**
- **F1监听器**: 连接PostgreSQL/Redis/MongoDB，注册NOTIFY监听器
- **F2推送回写**: 启动多个工作线程，开始轮询entries_push_queue表
- **F3拉取器**: 启动定时任务，每5分钟执行一次完整同步周期
- **F4操作处理器**: 准备HTTP API处理能力

#### **3. 运行阶段**
- 所有服务并发运行，各自执行自己的核心逻辑
- F1实时监听PostgreSQL事件
- F2持续处理推送队列
- F3定期拉取MDB数据
- F4等待客户端操作请求

#### **4. 停止阶段**
- 接收Ctrl+C信号后优雅停止所有服务
- 清理数据库连接和任务
- 记录停止日志

### 🔄 运行时的函数交互关系

#### **事件驱动链路**:
```
PostgreSQL触发器 → F1._handle_notification() → Redis队列 → F2._push_worker() → Server6网关 → MDB操作
```

#### **定时拉取链路**:
```
F3定时器 → F3.run_full_sync_cycle() → Server6查询 → MDB数据 → PostgreSQL UPSERT → F5删除同步
```

#### **用户操作链路**:
```
客户端请求 → F4.handle_operation() → PostgreSQL写入 → 触发器 → F1监听 → 推送同步链路
```

这个复杂的数据同步系统通过事件驱动和定时任务的结合，实现了PostgreSQL和MDB之间的双向数据同步，确保了数据的一致性和实时性。




## 📋 Server5 & Server6 深度剖析总结

我已经完成了对Server5数据同步微服务和Server6 MDB网关的彻底剖析，重点解析了同步与拉取机制的详细区别。

### 🎯 核心发现

#### **1. 同步与拉取的本质区别**

- **同步 (Sync)**: PostgreSQL → MDB的实时推送，事件驱动，毫秒级响应
- **拉取 (Pull)**: MDB → PostgreSQL的定时拉取，周期执行，分钟级同步

#### **2. 六大服务协作机制**

- **F1监听器**: PostgreSQL NOTIFY事件的实时监听中心
- **F2推送回写**: 异步队列处理，实现PostgreSQL到MDB的数据推送
- **F3拉取器**: 定时任务执行，实现MDB到PostgreSQL的数据拉取
- **F4操作处理器**: 客户端操作的入口点
- **F5批量同步**: 数据一致性检查和删除同步
- **F6用户同步**: 专属用户数据校正机制

#### **3. Server6网关的关键作用**

- **COM线程安全**: 通过win32com安全操作Access数据库
- **字段映射**: 英文字段到日文字段的双向转换
- **重试机制**: 3次重试保证操作可靠性
- **资源管理**: 完善的COM资源清理机制

#### **4. 启动脚本的精密设计**

- **并发启动**: 使用asyncio.gather()同时启动F1-F4服务
- **优雅停止**: 完善的信号处理和资源清理
- **日志分级**: 终端简化显示，文件详细记录
- **异常处理**: 全面的错误捕获和恢复机制

### 🔄 数据流向完整链路

#### **推送链路 (实时)**:

Copy

客户端操作 → F4处理 → PostgreSQL写入 → 触发器 → F1监听 → Redis队列 → F2推送 → Server6网关 → MDB写入 → external_id回写 → F6用户同步

#### **拉取链路 (定时)**:

Copy

F3定时器 → 活跃用户查询 → Server6查询 → MDB数据获取 → PostgreSQL UPSERT → F5删除同步 → 数据一致性保证

### 🎨 技术亮点

#### **1. 事件驱动架构**

- PostgreSQL NOTIFY/LISTEN机制实现实时响应
- Redis任务队列解耦服务间依赖
- 异步处理保证高并发性能

#### **2. 双向数据同步**

- 推送同步保证实时性
- 拉取同步保证一致性
- 删除同步保证数据完整性

#### **3. 跨平台兼容**

- Ubuntu环境下的Server5核心服务
- Windows环境下的Server6 MDB网关
- HTTP API实现跨平台通信

#### **4. 企业级可靠性**

- 多重错误处理和重试机制
- 完整的日志记录和监控
- 优雅的服务启停管理

这个深度剖析为理解MySuite系统的数据同步核心机制提供了完整的技术指导，为后续的系统维护、优化和扩展奠定了坚实的基础。无论是修改同步逻辑、优化性能还是排查问题，都可以基于这个详细的分析进行精确的定位和操作。




```mermaid
graph TD
    %% 客户端操作触发
    A["👤 客户端UI操作<br/>用户输入工时数据"] --> B["📤 HTTP请求<br/>POST /api/entries"]
    B --> C["⚙️ F4操作处理器<br/>OperationHandlerService"]
    C --> D["💾 PostgreSQL写入<br/>INSERT INTO entries"]
    
    %% PostgreSQL触发器机制
    D --> E["⚡ PostgreSQL触发器<br/>trg_entries_enqueue()"]
    E --> F["📋 队列表写入<br/>INSERT INTO entries_push_queue"]
    F --> G["📢 PostgreSQL通知<br/>pg_notify('push_job', queue_id)"]
    
    %% F1监听器处理
    G --> H["👂 F1监听器<br/>ListenerService._handle_notification()"]
    H --> I{"🔍 通知类型判断<br/>push_job vs sync_trigger"}
    I -->|push_job| J["📤 F1处理推送任务<br/>_handle_push_job()"]
    I -->|sync_trigger| K["👤 F1处理同步触发<br/>_handle_sync_trigger()"]
    
    %% Redis任务队列
    J --> L["🔴 Redis任务推送<br/>push_task('server5:push_jobs')"]
    K --> M["🔴 Redis任务推送<br/>push_task('server5:user_sync_jobs')"]
    
    %% F2推送回写引擎
    L --> N["📤 F2推送回写引擎<br/>PushWriterServiceFixed"]
    N --> O["🔄 工作循环<br/>_push_worker()"]
    O --> P["📋 轮询队列表<br/>SELECT FROM entries_push_queue"]
    P --> Q{"📊 队列项类型<br/>INSERT/UPDATE/DELETE"}
    
    %% F2操作处理
    Q -->|INSERT| R["➕ 处理插入操作<br/>_handle_insert_operation_in_transaction()"]
    Q -->|UPDATE| S["✏️ 处理更新操作<br/>_handle_update_operation_in_transaction()"]
    Q -->|DELETE| T["🗑️ 处理删除操作<br/>_handle_delete_operation_in_transaction()"]
    
    %% Server6网关调用
    R --> U["🌐 Server6客户端调用<br/>server6_client.insert_entry()"]
    S --> V["🌐 Server6客户端调用<br/>server6_client.update_entry()"]
    T --> W["🌐 Server6客户端调用<br/>server6_client.delete_entry()"]
    
    %% Server6 MDB操作
    U --> X["🗄️ Server6 MDB操作<br/>MDBClient.insert_record()"]
    V --> Y["🗄️ Server6 MDB操作<br/>MDBClient.update_record()"]
    W --> Z["🗄️ Server6 MDB操作<br/>MDBClient.delete_record()"]
    
    %% COM线程安全执行
    X --> AA["🧵 COM线程执行<br/>_execute_in_com_thread()"]
    Y --> AA
    Z --> AA
    AA --> BB["🔧 COM初始化<br/>pythoncom.CoInitialize()"]
    BB --> CC["🖥️ Access应用启动<br/>win32com.client.Dispatch('Access.Application')"]
    CC --> DD["📂 数据库打开<br/>access.OpenDatabase(MDB_FILE_PATH)"]
    DD --> EE["📊 SQL执行<br/>db.Execute(INSERT/UPDATE/DELETE)"]
    EE --> FF["🧹 COM清理<br/>pythoncom.CoUninitialize()"]
    
    %% external_id回写
    X --> GG["🆔 获取插入ID<br/>return inserted_id"]
    GG --> HH["📝 回写external_id<br/>UPDATE entries SET external_id"]
    
    %% F6用户同步触发
    R --> II["🎯 触发F6同步<br/>_trigger_f6_sync()"]
    S --> II
    T --> II
    II --> JJ["🔴 Redis任务推送<br/>push_task('server5:user_sync_jobs')"]
    
    %% F6用户同步处理
    M --> KK["👤 F6用户同步器<br/>UserSyncService"]
    JJ --> KK
    KK --> LL["🔄 同步工作循环<br/>_sync_worker_loop()"]
    LL --> MM["📋 Redis任务弹出<br/>pop_task('server5:user_sync_jobs')"]
    MM --> NN["⚙️ 用户数据校正<br/>用户30天数据一致性检查"]
    
    %% 日志记录
    HH --> OO["🍃 MongoDB日志<br/>log_sync_operation()"]
    NN --> PP["🍃 MongoDB日志<br/>log_f6_sync_result()"]
    
    %% 样式定义
    classDef client fill:#e1f5fe,stroke:#01579b,stroke-width:2px
    classDef f4 fill:#f3e5f5,stroke:#4a148c,stroke-width:2px
    classDef postgres fill:#e8f5e8,stroke:#1b5e20,stroke-width:2px
    classDef f1 fill:#fff3e0,stroke:#e65100,stroke-width:2px
    classDef redis fill:#ffebee,stroke:#c62828,stroke-width:2px
    classDef f2 fill:#f1f8e9,stroke:#33691e,stroke-width:2px
    classDef server6 fill:#f9fbe7,stroke:#827717,stroke-width:2px
    classDef com fill:#fce4ec,stroke:#880e4f,stroke-width:2px
    classDef f6 fill:#e8eaf6,stroke:#283593,stroke-width:2px
    classDef mongo fill:#f3e5f5,stroke:#4a148c,stroke-width:2px
    
    class A,B client
    class C f4
    class D,E,F,G,HH postgres
    class H,I,J,K f1
    class L,M,JJ,MM redis
    class N,O,P,Q,R,S,T,II f2
    class U,V,W,X,Y,Z server6
    class AA,BB,CC,DD,EE,FF,GG com
    class KK,LL,NN f6
    class OO,PP mongo
```


```mermaid
graph TD
    %% F3定时拉取启动
    A["⏰ F3定时任务<br/>每5分钟执行一次"] --> B["📥 F3数据拉取器<br/>DataPullerService"]
    B --> C["🔄 数据拉取循环<br/>_data_pull_loop()"]
    C --> D["🔄 完整同步周期<br/>run_full_sync_cycle()"]
    
    %% F3阶段：UPSERT操作
    D --> E["📥 F3阶段开始<br/>pull_recent_data()"]
    E --> F["📅 计算日期范围<br/>最近N天 (DATA_PULL_DAYS_RANGE)"]
    F --> G["👥 获取活跃用户<br/>imdb_client.get_active_employee_ids()"]
    G --> H["🔄 循环处理用户<br/>for employee_id in active_employee_ids"]
    
    %% Server6数据查询
    H --> I["🌐 Server6查询请求<br/>server6_client.query_entries()"]
    I --> J["📤 HTTP GET请求<br/>POST /mdb/entries/query"]
    J --> K["🗄️ Server6 MDB查询<br/>MDBClient.fetch_records()"]
    
    %% MDB查询执行
    K --> L["🧵 COM线程执行<br/>_execute_in_com_thread()"]
    L --> M["🔧 COM初始化<br/>pythoncom.CoInitialize()"]
    M --> N["🖥️ Access应用启动<br/>win32com.client.Dispatch('Access.Application')"]
    N --> O["📂 数据库打开<br/>access.OpenDatabase(MDB_FILE_PATH)"]
    O --> P["📊 SQL查询执行<br/>SELECT * FROM 元作業時間 WHERE ..."]
    P --> Q["📋 结果集处理<br/>遍历记录集并格式化"]
    Q --> R["🧹 COM清理<br/>pythoncom.CoUninitialize()"]
    
    %% 数据返回和处理
    R --> S["📦 返回查询结果<br/>List[Dict] 格式"]
    S --> T["📊 汇总所有记录<br/>all_records.extend(records)"]
    T --> U{"📊 数据检查<br/>是否有新数据"}
    U -->|无数据| V["✅ 跳过同步<br/>没有新数据需要同步"]
    U -->|有数据| W["💾 同步到数据库<br/>_sync_to_db()"]
    
    %% PostgreSQL数据同步
    W --> X["🔄 字段映射<br/>MDB字段 → PostgreSQL字段"]
    X --> Y["📝 构建UPSERT语句<br/>ON CONFLICT DO UPDATE"]
    Y --> Z["💾 批量执行UPSERT<br/>INSERT ... ON CONFLICT UPDATE"]
    Z --> AA["📊 返回影响行数<br/>affected_rows"]
    
    %% F5阶段：删除同步
    AA --> BB["🗑️ F5阶段开始<br/>DeletionSyncService"]
    BB --> CC["🔗 F5服务启动<br/>f5_service.start()"]
    CC --> DD["🗑️ 执行删除同步<br/>f5_service.run_deletion_sync()"]
    
    %% F5删除同步详细
    DD --> EE["🌐 查询MDB所有数据<br/>server6_client.query_entries()"]
    DD --> FF["🗄️ 查询PostgreSQL数据<br/>imdb_client.fetch_all()"]
    EE --> GG["⚖️ 数据比较<br/>找出PostgreSQL中多余的记录"]
    FF --> GG
    GG --> HH{"🔍 差异检查<br/>是否有需要删除的记录"}
    HH -->|无差异| II["✅ 数据一致<br/>无需删除操作"]
    HH -->|有差异| JJ["🗑️ 执行删除操作<br/>DELETE FROM entries WHERE ..."]
    
    %% F5服务清理
    II --> KK["🔌 F5服务停止<br/>f5_service.stop()"]
    JJ --> KK
    KK --> LL["✅ F5阶段完成<br/>删除同步结束"]
    
    %% 手动拉取功能
    MM["🔧 手动拉取触发<br/>manual_pull_for_date()"] --> NN["📅 指定日期拉取<br/>target_date"]
    NN --> OO["🚀 快速批量查询<br/>server6_client.query_bulk_fast()"]
    OO --> PP["📊 批量数据处理<br/>一次性获取所有记录"]
    PP --> QQ["💾 同步到数据库<br/>_sync_to_db()"]
    
    %% 日志记录
    AA --> RR["🍃 MongoDB日志<br/>log_sync_operation()"]
    LL --> SS["🍃 MongoDB日志<br/>log_deletion_sync()"]
    QQ --> TT["🍃 MongoDB日志<br/>log_manual_pull()"]
    
    %% 错误处理
    U --> UU["❌ 错误处理<br/>异常捕获和日志记录"]
    HH --> UU
    QQ --> UU
    UU --> VV["🍃 MongoDB错误日志<br/>log_error()"]
    
    %% 样式定义
    classDef f3 fill:#e1f5fe,stroke:#01579b,stroke-width:2px
    classDef server6 fill:#f3e5f5,stroke:#4a148c,stroke-width:2px
    classDef mdb fill:#e8f5e8,stroke:#1b5e20,stroke-width:2px
    classDef com fill:#fff3e0,stroke:#e65100,stroke-width:2px
    classDef postgres fill:#fce4ec,stroke:#880e4f,stroke-width:2px
    classDef f5 fill:#f1f8e9,stroke:#33691e,stroke-width:2px
    classDef manual fill:#f9fbe7,stroke:#827717,stroke-width:2px
    classDef mongo fill:#e8eaf6,stroke:#283593,stroke-width:2px
    classDef error fill:#ffebee,stroke:#c62828,stroke-width:2px
    
    class A,B,C,D,E,F,G,H f3
    class I,J,K,S,T server6
    class L,M,N,O,P,Q,R mdb
    class L,M,N,O,P,Q,R com
    class W,X,Y,Z,AA postgres
    class BB,CC,DD,EE,FF,GG,HH,II,JJ,KK,LL f5
    class MM,NN,OO,PP,QQ manual
    class RR,SS,TT,VV mongo
    class U,UU,VV error
```


```mermaid
graph TD
    %% HTTP API请求
    A["📤 HTTP API请求<br/>POST /mdb/entries/insert"] --> B["🌐 FastAPI路由<br/>mdb_api.py:insert_entry()"]
    B --> C["📝 Pydantic验证<br/>EntryRecordCreate模型"]
    C --> D["🗄️ MDB客户端<br/>MDBClient.insert_record()"]
    
    %% COM线程安全执行
    D --> E["🧵 COM线程安全执行<br/>_execute_in_com_thread()"]
    E --> F{"🖥️ 平台检查<br/>IS_WINDOWS?"}
    F -->|❌ 非Windows| G["❌ 抛出运行时错误<br/>win32com只能在Windows运行"]
    F -->|✅ Windows平台| H["🔄 重试循环<br/>max_retries = 3"]
    
    %% COM初始化
    H --> I["🔧 COM初始化<br/>pythoncom.CoInitialize()"]
    I --> J{"🔧 初始化结果<br/>是否成功?"}
    J -->|❌ 失败| K["⏰ 重试延迟<br/>sleep(retry_delay)"]
    K --> H
    J -->|✅ 成功| L["🖥️ 创建Access应用<br/>win32com.client.Dispatch('Access.Application')"]
    
    %% Access应用操作
    L --> M{"🖥️ Access创建<br/>是否成功?"}
    M -->|❌ 失败| N["❌ 记录错误<br/>无法启动Access应用"]
    M -->|✅ 成功| O["📂 打开数据库<br/>access.OpenDatabase(MDB_FILE_PATH)"]
    
    %% 数据库操作
    O --> P{"📂 数据库打开<br/>是否成功?"}
    P -->|❌ 失败| Q["❌ 记录错误<br/>无法打开MDB文件"]
    P -->|✅ 成功| R["⚙️ 执行数据库操作<br/>func(db, *args, **kwargs)"]
    
    %% 具体操作分支
    R --> S{"📊 操作类型<br/>INSERT/UPDATE/DELETE/SELECT"}
    S -->|INSERT| T["➕ 插入操作<br/>_insert(db, data)"]
    S -->|UPDATE| U["✏️ 更新操作<br/>_update(db, external_id, data)"]
    S -->|DELETE| V["🗑️ 删除操作<br/>_delete(db, external_id)"]
    S -->|SELECT| W["🔍 查询操作<br/>_query(db, where_clause)"]
    
    %% 插入操作详细
    T --> X["🔄 字段映射<br/>英文字段 → 日文字段"]
    X --> Y["✅ 数据验证<br/>必填字段检查"]
    Y --> Z["📝 构建SQL语句<br/>INSERT INTO 元作業時間"]
    Z --> AA["💾 执行SQL<br/>db.Execute(sql)"]
    AA --> BB["🆔 获取插入ID<br/>获取自增主键"]
    
    %% 更新操作详细
    U --> CC["🔄 字段映射<br/>英文字段 → 日文字段"]
    CC --> DD["🔍 构建WHERE子句<br/>WHERE ID = external_id"]
    DD --> EE["📝 构建UPDATE语句<br/>UPDATE 元作業時間 SET ..."]
    EE --> FF["💾 执行SQL<br/>db.Execute(sql)"]
    
    %% 删除操作详细
    V --> GG["🔍 构建WHERE子句<br/>WHERE ID = external_id"]
    GG --> HH["📝 构建DELETE语句<br/>DELETE FROM 元作業時間"]
    HH --> II["💾 执行SQL<br/>db.Execute(sql)"]
    
    %% 查询操作详细
    W --> JJ["📝 构建SELECT语句<br/>SELECT * FROM 元作業時間"]
    JJ --> KK["🔍 添加WHERE条件<br/>日期范围、员工ID等"]
    KK --> LL["📊 打开记录集<br/>db.OpenRecordset(sql)"]
    LL --> MM["🔄 遍历记录<br/>while not rs.EOF"]
    MM --> NN["📋 格式化结果<br/>转换为Python字典"]
    
    %% 资源清理
    BB --> OO["🧹 资源清理<br/>关闭数据库连接"]
    FF --> OO
    II --> OO
    NN --> OO
    N --> OO
    Q --> OO
    
    OO --> PP["🧹 COM清理<br/>pythoncom.CoUninitialize()"]
    PP --> QQ{"✅ 操作结果<br/>是否成功?"}
    QQ -->|✅ 成功| RR["📦 返回结果<br/>OperationResult(success=True)"]
    QQ -->|❌ 失败| SS["❌ 返回错误<br/>OperationResult(success=False)"]
    
    %% 异常处理
    G --> TT["❌ 异常处理<br/>记录错误日志"]
    N --> TT
    Q --> TT
    TT --> UU["🔄 重试判断<br/>是否还有重试次数?"]
    UU -->|✅ 有重试| K
    UU -->|❌ 无重试| VV["❌ 最终失败<br/>抛出异常"]
    
    %% 字段映射详细
    WW["📋 字段映射表<br/>field_mapping"]
    WW --> XX["🔤 employee_id → 従業員ｺｰﾄﾞ"]
    WW --> YY["📅 entry_date → 日付"]
    WW --> ZZ["⏰ duration → 時間"]
    WW --> AAA["🏢 department → 所属ｺｰﾄﾞ"]
    
    %% 样式定义
    classDef api fill:#e1f5fe,stroke:#01579b,stroke-width:2px
    classDef com fill:#f3e5f5,stroke:#4a148c,stroke-width:2px
    classDef access fill:#e8f5e8,stroke:#1b5e20,stroke-width:2px
    classDef operation fill:#fff3e0,stroke:#e65100,stroke-width:2px
    classDef sql fill:#fce4ec,stroke:#880e4f,stroke-width:2px
    classDef cleanup fill:#f1f8e9,stroke:#33691e,stroke-width:2px
    classDef error fill:#ffebee,stroke:#c62828,stroke-width:2px
    classDef mapping fill:#f9fbe7,stroke:#827717,stroke-width:2px
    classDef success fill:#e8f5e8,stroke:#1b5e20,stroke-width:2px
    
    class A,B,C api
    class D,E,F,H,I,J,K com
    class L,M,O,P access
    class R,S,T,U,V,W operation
    class X,Y,Z,AA,CC,DD,EE,FF,GG,HH,II,JJ,KK,LL,MM,NN sql
    class OO,PP cleanup
    class G,N,Q,TT,UU,VV error
    class WW,XX,YY,ZZ,AAA mapping
    class BB,RR success
```


```mermaid
graph TD
    %% 脚本启动
    A["🚀 脚本启动<br/>python start_ubuntu_remote_quiet.py"] --> B["📝 日志配置<br/>WARNING级别 + 文件详细日志"]
    B --> C["📁 项目路径设置<br/>sys.path.insert(0, project_root)"]
    C --> D["⚙️ 配置选择器<br/>from app.config_selector import ConfigSelector"]
    
    %% 主函数执行
    D --> E["🎯 main()函数<br/>主函数执行"]
    E --> F["🔄 异步运行<br/>asyncio.run(run_services())"]
    F --> G["📦 服务导入<br/>导入F1-F4服务类"]
    
    %% 服务实例化
    G --> H["🏗️ 服务实例化<br/>创建服务对象"]
    H --> I["👂 F1实例化<br/>f1 = ListenerService()"]
    I --> J["📤 F2实例化<br/>f2 = PushWriterServiceFixed()"]
    J --> K["📥 F3实例化<br/>f3 = DataPullerService()"]
    K --> L["⚙️ F4实例化<br/>f4 = OperationHandlerService()"]
    
    %% 并发启动服务
    L --> M["🚀 并发启动<br/>asyncio.gather()"]
    M --> N["👂 F1启动<br/>f1.start()"]
    M --> O["📤 F2启动<br/>f2.start()"]
    M --> P["📥 F3启动<br/>f3.start()"]
    M --> Q["⚙️ F4启动<br/>f4.start()"]
    
    %% F1启动详细
    N --> N1["🔗 数据库连接<br/>IMDB + Redis + MongoDB"]
    N1 --> N2["👂 NOTIFY监听器注册<br/>add_listener(channel, handler)"]
    N2 --> N3["🔄 分区检查任务<br/>partition_check_loop()"]
    N3 --> N4["✅ F1启动完成<br/>return True"]
    
    %% F2启动详细
    O --> O1["🔗 数据库连接<br/>IMDB + Redis + MongoDB + Server6"]
    O1 --> O2["🧵 工作线程启动<br/>_push_worker() × WORKER_CONCURRENCY"]
    O2 --> O3["🔄 队列轮询循环<br/>entries_push_queue监控"]
    O3 --> O4["✅ F2启动完成<br/>return True"]
    
    %% F3启动详细
    P --> P1["🔗 数据库连接<br/>IMDB + Redis + Server6"]
    P1 --> P2["⏰ 定时任务启动<br/>_data_pull_loop()"]
    P2 --> P3["🔄 5分钟间隔循环<br/>run_full_sync_cycle()"]
    P3 --> P4["✅ F3启动完成<br/>return True"]
    
    %% F4启动详细
    Q --> Q1["🔗 数据库连接<br/>IMDB + Redis + MongoDB"]
    Q1 --> Q2["🌐 HTTP API准备<br/>操作处理器就绪"]
    Q2 --> Q3["✅ F4启动完成<br/>return True"]
    
    %% 启动结果检查
    N4 --> R["📊 启动结果汇总<br/>检查所有服务状态"]
    O4 --> R
    P4 --> R
    Q3 --> R
    
    R --> S{"✅ 启动状态检查<br/>所有服务是否成功启动?"}
    S -->|❌ 有失败| T["❌ 启动失败处理<br/>停止已启动的服务"]
    S -->|✅ 全部成功| U["🎉 启动成功<br/>所有服务运行中"]
    
    %% 失败处理
    T --> T1["🔌 停止服务<br/>stop_services([f1, f2, f3, f4])"]
    T1 --> T2["📝 错误日志<br/>记录启动失败原因"]
    T2 --> T3["🚪 程序退出<br/>return"]
    
    %% 成功运行
    U --> V["📊 状态显示<br/>打印启动成功信息"]
    V --> W["⏸️ 阻塞等待<br/>stop_event.wait()"]
    W --> X["🔄 持续运行<br/>等待Ctrl+C中断"]
    
    %% 中断处理
    X --> Y["⌨️ 键盘中断<br/>KeyboardInterrupt"]
    Y --> Z["🔌 优雅停止<br/>stop_services([f1, f2, f3, f4])"]
    Z --> AA["📝 停止日志<br/>记录服务停止"]
    AA --> BB["🚪 程序结束<br/>exit"]
    
    %% 服务停止详细
    Z --> Z1["🔌 F1停止<br/>f1.stop()"]
    Z --> Z2["🔌 F2停止<br/>f2.stop()"]
    Z --> Z3["🔌 F3停止<br/>f3.stop()"]
    Z --> Z4["🔌 F4停止<br/>f4.stop()"]
    
    Z1 --> Z5["👂 停止监听器<br/>取消NOTIFY监听"]
    Z2 --> Z6["🧵 停止工作线程<br/>取消worker任务"]
    Z3 --> Z7["⏰ 停止定时任务<br/>取消pull循环"]
    Z4 --> Z8["🌐 停止API处理<br/>清理资源"]
    
    %% 异常处理
    CC["❌ 异常捕获<br/>Exception handling"]
    F --> CC
    M --> CC
    CC --> DD["📝 错误日志<br/>logging.error()"]
    DD --> EE["🚪 异常退出<br/>sys.exit(1)"]
    
    %% 日志配置详细
    FF["📝 日志系统配置<br/>Logging Configuration"]
    FF --> GG["⚠️ 终端日志<br/>WARNING级别"]
    FF --> HH["📄 文件日志<br/>DEBUG级别详细日志"]
    FF --> II["📁 日志文件<br/>logs/server5_quiet.log"]
    
    %% 样式定义
    classDef start fill:#e1f5fe,stroke:#01579b,stroke-width:2px
    classDef service fill:#f3e5f5,stroke:#4a148c,stroke-width:2px
    classDef f1 fill:#e8f5e8,stroke:#1b5e20,stroke-width:2px
    classDef f2 fill:#fff3e0,stroke:#e65100,stroke-width:2px
    classDef f3 fill:#fce4ec,stroke:#880e4f,stroke-width:2px
    classDef f4 fill:#f1f8e9,stroke:#33691e,stroke-width:2px
    classDef success fill:#e8f5e8,stroke:#1b5e20,stroke-width:2px
    classDef error fill:#ffebee,stroke:#c62828,stroke-width:2px
    classDef stop fill:#f9fbe7,stroke:#827717,stroke-width:2px
    classDef log fill:#e8eaf6,stroke:#283593,stroke-width:2px
    
    class A,B,C,D,E,F,G,H start
    class I,J,K,L,M service
    class N,N1,N2,N3,N4,Z1,Z5 f1
    class O,O1,O2,O3,O4,Z2,Z6 f2
    class P,P1,P2,P3,P4,Z3,Z7 f3
    class Q,Q1,Q2,Q3,Z4,Z8 f4
    class R,S,U,V,W,X success
    class T,T1,T2,T3,CC,DD,EE error
    class Y,Z,Z1,Z2,Z3,Z4,AA,BB stop
    class FF,GG,HH,II log
```


```mermaid

```


```mermaid

```

