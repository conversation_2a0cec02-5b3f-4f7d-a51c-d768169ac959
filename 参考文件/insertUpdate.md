Option Compare Database

Sub queryset(input1, input2, input3, input4, input5, input6, input7, input8, input9, input10, input11, input12, input13, input14)
Dim mydb As Database
Dim myquery As QueryDef, s0 As Recordset
Set mydb = DBEngine.Workspaces(0).Databases(0)
Set myquery = mydb.CreateQueryDef()
On Error GoTo ErrorHandler

PARA1 = input1
PARA2 = input2
PARA3 = input3
PARA4 = input4
PARA5 = input5
para6 = input6
para7 = input7
para8 = input8
para9 = input9
para10 = input10
para11 = input11
para12 = input12
para13 = input13
para14 = input14

myquery.Name = "workquery"

Select Case PARA1
Case "code_M_所属ｺｰﾄﾞ"
    myquery.SQL = "INSERT INTO 作業用A ( ID, 従業員ｺｰﾄﾞ, 日付, 機種, 号機, 工場製番, 工事番号, ﾕﾆｯﾄ番号, 区分, 項目, 時間, 所属ｺｰﾄﾞ ) SELECT 作業時間.ID, 作業時間.従業員ｺｰﾄﾞ, 作業時間.日付, 作業時間.機種, 作業時間.号機, 作業時間.工場製番, 作業時間.工事番号, 作業時間.ﾕﾆｯﾄ番号, 作業時間.区分, 作業時間.項目, 作業時間.時間, 作業時間.所属ｺｰﾄﾞ FROM 作業時間 WHERE (((作業時間.所属ｺｰﾄﾞ) Like '" & PARA2 & "*'));"
Case "code_M_従業員コード"
    myquery.SQL = "INSERT INTO 作業用A ( ID, 従業員ｺｰﾄﾞ, 日付, 機種, 号機, 工場製番, 工事番号, ﾕﾆｯﾄ番号, 区分, 項目, 時間, 所属ｺｰﾄﾞ ) SELECT 作業時間.ID, 作業時間.従業員ｺｰﾄﾞ, 作業時間.日付, 作業時間.機種, 作業時間.号機, 作業時間.工場製番, 作業時間.工事番号, 作業時間.ﾕﾆｯﾄ番号, 作業時間.区分, 作業時間.項目, 作業時間.時間, 作業時間.所属ｺｰﾄﾞ FROM 作業時間 WHERE (((作業時間.従業員ｺｰﾄﾞ) Like '" & PARA2 & "*'));"
Case "code_M_日付"
    myquery.SQL = "INSERT INTO 作業用A ( ID, 従業員ｺｰﾄﾞ, 日付, 機種, 号機, 工場製番, 工事番号, ﾕﾆｯﾄ番号, 区分, 項目, 時間, 所属ｺｰﾄﾞ ) SELECT 作業時間.ID, 作業時間.従業員ｺｰﾄﾞ, 作業時間.日付, 作業時間.機種, 作業時間.号機, 作業時間.工場製番, 作業時間.工事番号, 作業時間.ﾕﾆｯﾄ番号, 作業時間.区分, 作業時間.項目, 作業時間.時間, 作業時間.所属ｺｰﾄﾞ FROM 作業時間 WHERE (((作業時間.日付)>=#" & PARA2 & "# And (作業時間.日付)<=#" & PARA3 & "#));"
Case "code_M_機種"
    myquery.SQL = "INSERT INTO 作業用A ( ID, 従業員ｺｰﾄﾞ, 日付, 機種, 号機, 工場製番, 工事番号, ﾕﾆｯﾄ番号, 区分, 項目, 時間, 所属ｺｰﾄﾞ ) SELECT 作業時間.ID, 作業時間.従業員ｺｰﾄﾞ, 作業時間.日付, 作業時間.機種, 作業時間.号機, 作業時間.工場製番, 作業時間.工事番号, 作業時間.ﾕﾆｯﾄ番号, 作業時間.区分, 作業時間.項目, 作業時間.時間, 作業時間.所属ｺｰﾄﾞ FROM 作業時間 WHERE (((作業時間.機種) Like '" & PARA2 & "*'));"
Case "code_M_号機"
    myquery.SQL = "INSERT INTO 作業用A ( ID, 従業員ｺｰﾄﾞ, 日付, 機種, 号機, 工場製番, 工事番号, ﾕﾆｯﾄ番号, 区分, 項目, 時間, 所属ｺｰﾄﾞ ) SELECT 作業時間.ID, 作業時間.従業員ｺｰﾄﾞ, 作業時間.日付, 作業時間.機種, 作業時間.号機, 作業時間.工場製番, 作業時間.工事番号, 作業時間.ﾕﾆｯﾄ番号, 作業時間.区分, 作業時間.項目, 作業時間.時間, 作業時間.所属ｺｰﾄﾞ FROM 作業時間 WHERE (((作業時間.号機) Like '" & PARA2 & "*'));"
Case "code_M_工場製番"
    myquery.SQL = "INSERT INTO 作業用A ( ID, 従業員ｺｰﾄﾞ, 日付, 機種, 号機, 工場製番, 工事番号, ﾕﾆｯﾄ番号, 区分, 項目, 時間, 所属ｺｰﾄﾞ ) SELECT 作業時間.ID, 作業時間.従業員ｺｰﾄﾞ, 作業時間.日付, 作業時間.機種, 作業時間.号機, 作業時間.工場製番, 作業時間.工事番号, 作業時間.ﾕﾆｯﾄ番号, 作業時間.区分, 作業時間.項目, 作業時間.時間, 作業時間.所属ｺｰﾄﾞ FROM 作業時間 WHERE (((作業時間.工場製番) Like '" & PARA2 & "*'));"
Case "code_M_工事番号"
    myquery.SQL = "INSERT INTO 作業用A ( ID, 従業員ｺｰﾄﾞ, 日付, 機種, 号機, 工場製番, 工事番号, ﾕﾆｯﾄ番号, 区分, 項目, 時間, 所属ｺｰﾄﾞ ) SELECT 作業時間.ID, 作業時間.従業員ｺｰﾄﾞ, 作業時間.日付, 作業時間.機種, 作業時間.号機, 作業時間.工場製番, 作業時間.工事番号, 作業時間.ﾕﾆｯﾄ番号, 作業時間.区分, 作業時間.項目, 作業時間.時間, 作業時間.所属ｺｰﾄﾞ FROM 作業時間 WHERE (((作業時間.工事番号) Like '" & PARA2 & "*'));"
Case "code_M_区分"
    myquery.SQL = "INSERT INTO 作業用A ( ID, 従業員ｺｰﾄﾞ, 日付, 機種, 号機, 工場製番, 工事番号, ﾕﾆｯﾄ番号, 区分, 項目, 時間, 所属ｺｰﾄﾞ ) SELECT 作業時間.ID, 作業時間.従業員ｺｰﾄﾞ, 作業時間.日付, 作業時間.機種, 作業時間.号機, 作業時間.工場製番, 作業時間.工事番号, 作業時間.ﾕﾆｯﾄ番号, 作業時間.区分, 作業時間.項目, 作業時間.時間, 作業時間.所属ｺｰﾄﾞ FROM 作業時間 WHERE (((作業時間.区分) Like '" & PARA2 & "*'));"

Case "code_A_所属ｺｰﾄﾞ"
    myquery.SQL = "INSERT INTO 作業用B ( ID, 従業員ｺｰﾄﾞ, 日付, 機種, 号機, 工場製番, 工事番号, ﾕﾆｯﾄ番号, 区分, 項目, 時間, 所属ｺｰﾄﾞ ) SELECT 作業用A.ID, 作業用A.従業員ｺｰﾄﾞ, 作業用A.日付, 作業用A.機種, 作業用A.号機, 作業用A.工場製番, 作業用A.工事番号, 作業用A.ﾕﾆｯﾄ番号, 作業用A.区分, 作業用A.項目, 作業用A.時間, 作業用A.所属ｺｰﾄﾞ FROM 作業用A WHERE (((作業用A.所属ｺｰﾄﾞ) Like '" & PARA2 & "*'));"
Case "code_A_従業員コード"
    myquery.SQL = "INSERT INTO 作業用B ( ID, 従業員ｺｰﾄﾞ, 日付, 機種, 号機, 工場製番, 工事番号, ﾕﾆｯﾄ番号, 区分, 項目, 時間, 所属ｺｰﾄﾞ ) SELECT 作業用A.ID, 作業用A.従業員ｺｰﾄﾞ, 作業用A.日付, 作業用A.機種, 作業用A.号機, 作業用A.工場製番, 作業用A.工事番号, 作業用A.ﾕﾆｯﾄ番号, 作業用A.区分, 作業用A.項目, 作業用A.時間, 作業用A.所属ｺｰﾄﾞ FROM 作業用A WHERE (((作業用A.従業員ｺｰﾄﾞ) Like '" & PARA2 & "*'));"
Case "code_A_日付"
    myquery.SQL = "INSERT INTO 作業用B ( ID, 従業員ｺｰﾄﾞ, 日付, 機種, 号機, 工場製番, 工事番号, ﾕﾆｯﾄ番号, 区分, 項目, 時間, 所属ｺｰﾄﾞ ) SELECT 作業用A.ID, 作業用A.従業員ｺｰﾄﾞ, 作業用A.日付, 作業用A.機種, 作業用A.号機, 作業用A.工場製番, 作業用A.工事番号, 作業用A.ﾕﾆｯﾄ番号, 作業用A.区分, 作業用A.項目, 作業用A.時間, 作業用A.所属ｺｰﾄﾞ FROM 作業用A WHERE (((作業用A.日付)>=#" & PARA2 & "# And (作業用A.日付)<=#" & PARA3 & "#));"
Case "code_A_機種"
    myquery.SQL = "INSERT INTO 作業用B ( ID, 従業員ｺｰﾄﾞ, 日付, 機種, 号機, 工場製番, 工事番号, ﾕﾆｯﾄ番号, 区分, 項目, 時間, 所属ｺｰﾄﾞ ) SELECT 作業用A.ID, 作業用A.従業員ｺｰﾄﾞ, 作業用A.日付, 作業用A.機種, 作業用A.号機, 作業用A.工場製番, 作業用A.工事番号, 作業用A.ﾕﾆｯﾄ番号, 作業用A.区分, 作業用A.項目, 作業用A.時間, 作業用A.所属ｺｰﾄﾞ FROM 作業用A WHERE (((作業用A.機種) Like '" & PARA2 & "*'));"
Case "code_A_号機"
    myquery.SQL = "INSERT INTO 作業用B ( ID, 従業員ｺｰﾄﾞ, 日付, 機種, 号機, 工場製番, 工事番号, ﾕﾆｯﾄ番号, 区分, 項目, 時間, 所属ｺｰﾄﾞ ) SELECT 作業用A.ID, 作業用A.従業員ｺｰﾄﾞ, 作業用A.日付, 作業用A.機種, 作業用A.号機, 作業用A.工場製番, 作業用A.工事番号, 作業用A.ﾕﾆｯﾄ番号, 作業用A.区分, 作業用A.項目, 作業用A.時間, 作業用A.所属ｺｰﾄﾞ FROM 作業用A WHERE (((作業用A.号機) Like '" & PARA2 & "*'));"
Case "code_A_工場製番"
    myquery.SQL = "INSERT INTO 作業用B ( ID, 従業員ｺｰﾄﾞ, 日付, 機種, 号機, 工場製番, 工事番号, ﾕﾆｯﾄ番号, 区分, 項目, 時間, 所属ｺｰﾄﾞ ) SELECT 作業用A.ID, 作業用A.従業員ｺｰﾄﾞ, 作業用A.日付, 作業用A.機種, 作業用A.号機, 作業用A.工場製番, 作業用A.工事番号, 作業用A.ﾕﾆｯﾄ番号, 作業用A.区分, 作業用A.項目, 作業用A.時間, 作業用A.所属ｺｰﾄﾞ FROM 作業用A WHERE (((作業用A.工場製番) Like '" & PARA2 & "*'));"
Case "code_A_工事番号"
    myquery.SQL = "INSERT INTO 作業用B ( ID, 従業員ｺｰﾄﾞ, 日付, 機種, 号機, 工場製番, 工事番号, ﾕﾆｯﾄ番号, 区分, 項目, 時間, 所属ｺｰﾄﾞ ) SELECT 作業用A.ID, 作業用A.従業員ｺｰﾄﾞ, 作業用A.日付, 作業用A.機種, 作業用A.号機, 作業用A.工場製番, 作業用A.工事番号, 作業用A.ﾕﾆｯﾄ番号, 作業用A.区分, 作業用A.項目, 作業用A.時間, 作業用A.所属ｺｰﾄﾞ FROM 作業用A WHERE (((作業用A.工事番号) Like '" & PARA2 & "*'));"
Case "code_A_区分"
    myquery.SQL = "INSERT INTO 作業用B ( ID, 従業員ｺｰﾄﾞ, 日付, 機種, 号機, 工場製番, 工事番号, ﾕﾆｯﾄ番号, 区分, 項目, 時間, 所属ｺｰﾄﾞ ) SELECT 作業用A.ID, 作業用A.従業員ｺｰﾄﾞ, 作業用A.日付, 作業用A.機種, 作業用A.号機, 作業用A.工場製番, 作業用A.工事番号, 作業用A.ﾕﾆｯﾄ番号, 作業用A.区分, 作業用A.項目, 作業用A.時間, 作業用A.所属ｺｰﾄﾞ FROM 作業用A WHERE (((作業用A.区分) Like '" & PARA2 & "*'));"

Case "code_B_所属ｺｰﾄﾞ"
    myquery.SQL = "INSERT INTO 作業用A ( ID, 従業員ｺｰﾄﾞ, 日付, 機種, 号機, 工場製番, 工事番号, ﾕﾆｯﾄ番号, 区分, 項目, 時間, 所属ｺｰﾄﾞ ) SELECT 作業用B.ID, 作業用B.従業員ｺｰﾄﾞ, 作業用B.日付, 作業用B.機種, 作業用B.号機, 作業用B.工場製番, 作業用B.工事番号, 作業用B.ﾕﾆｯﾄ番号, 作業用B.区分, 作業用B.項目, 作業用B.時間, 作業用B.所属ｺｰﾄﾞ FROM 作業用B WHERE (((作業用B.所属ｺｰﾄﾞ) Like '" & PARA2 & "*'));"
Case "code_B_従業員コード"
    myquery.SQL = "INSERT INTO 作業用A ( ID, 従業員ｺｰﾄﾞ, 日付, 機種, 号機, 工場製番, 工事番号, ﾕﾆｯﾄ番号, 区分, 項目, 時間, 所属ｺｰﾄﾞ ) SELECT 作業用B.ID, 作業用B.従業員ｺｰﾄﾞ, 作業用B.日付, 作業用B.機種, 作業用B.号機, 作業用B.工場製番, 作業用B.工事番号, 作業用B.ﾕﾆｯﾄ番号, 作業用B.区分, 作業用B.項目, 作業用B.時間, 作業用B.所属ｺｰﾄﾞ FROM 作業用B WHERE (((作業用B.従業員ｺｰﾄﾞ) Like '" & PARA2 & "*'));"
Case "code_B_日付"
    myquery.SQL = "INSERT INTO 作業用A ( ID, 従業員ｺｰﾄﾞ, 日付, 機種, 号機, 工場製番, 工事番号, ﾕﾆｯﾄ番号, 区分, 項目, 時間, 所属ｺｰﾄﾞ ) SELECT 作業用B.ID, 作業用B.従業員ｺｰﾄﾞ, 作業用B.日付, 作業用B.機種, 作業用B.号機, 作業用B.工場製番, 作業用B.工事番号, 作業用B.ﾕﾆｯﾄ番号, 作業用B.区分, 作業用B.項目, 作業用B.時間, 作業用B.所属ｺｰﾄﾞ FROM 作業用B WHERE (((作業用B.日付)>=#" & PARA2 & "# And (作業用B.日付)<=#" & PARA3 & "#));"
Case "code_B_機種"
    myquery.SQL = "INSERT INTO 作業用A ( ID, 従業員ｺｰﾄﾞ, 日付, 機種, 号機, 工場製番, 工事番号, ﾕﾆｯﾄ番号, 区分, 項目, 時間, 所属ｺｰﾄﾞ ) SELECT 作業用B.ID, 作業用B.従業員ｺｰﾄﾞ, 作業用B.日付, 作業用B.機種, 作業用B.号機, 作業用B.工場製番, 作業用B.工事番号, 作業用B.ﾕﾆｯﾄ番号, 作業用B.区分, 作業用B.項目, 作業用B.時間, 作業用B.所属ｺｰﾄﾞ FROM 作業用B WHERE (((作業用B.機種) Like '" & PARA2 & "*'));"
Case "code_B_号機"
    myquery.SQL = "INSERT INTO 作業用A ( ID, 従業員ｺｰﾄﾞ, 日付, 機種, 号機, 工場製番, 工事番号, ﾕﾆｯﾄ番号, 区分, 項目, 時間, 所属ｺｰﾄﾞ ) SELECT 作業用B.ID, 作業用B.従業員ｺｰﾄﾞ, 作業用B.日付, 作業用B.機種, 作業用B.号機, 作業用B.工場製番, 作業用B.工事番号, 作業用B.ﾕﾆｯﾄ番号, 作業用B.区分, 作業用B.項目, 作業用B.時間, 作業用B.所属ｺｰﾄﾞ FROM 作業用B WHERE (((作業用B.号機) Like '" & PARA2 & "*'));"
Case "code_B_工場製番"
    myquery.SQL = "INSERT INTO 作業用A ( ID, 従業員ｺｰﾄﾞ, 日付, 機種, 号機, 工場製番, 工事番号, ﾕﾆｯﾄ番号, 区分, 項目, 時間, 所属ｺｰﾄﾞ ) SELECT 作業用B.ID, 作業用B.従業員ｺｰﾄﾞ, 作業用B.日付, 作業用B.機種, 作業用B.号機, 作業用B.工場製番, 作業用B.工事番号, 作業用B.ﾕﾆｯﾄ番号, 作業用B.区分, 作業用B.項目, 作業用B.時間, 作業用B.所属ｺｰﾄﾞ FROM 作業用B WHERE (((作業用B.工場製番) Like '" & PARA2 & "*'));"
Case "code_B_工事番号"
    myquery.SQL = "INSERT INTO 作業用A ( ID, 従業員ｺｰﾄﾞ, 日付, 機種, 号機, 工場製番, 工事番号, ﾕﾆｯﾄ番号, 区分, 項目, 時間, 所属ｺｰﾄﾞ ) SELECT 作業用B.ID, 作業用B.従業員ｺｰﾄﾞ, 作業用B.日付, 作業用B.機種, 作業用B.号機, 作業用B.工場製番, 作業用B.工事番号, 作業用B.ﾕﾆｯﾄ番号, 作業用B.区分, 作業用B.項目, 作業用B.時間, 作業用B.所属ｺｰﾄﾞ FROM 作業用B WHERE (((作業用B.工事番号) Like '" & PARA2 & "*'));"
Case "code_B_区分"
    myquery.SQL = "INSERT INTO 作業用A ( ID, 従業員ｺｰﾄﾞ, 日付, 機種, 号機, 工場製番, 工事番号, ﾕﾆｯﾄ番号, 区分, 項目, 時間, 所属ｺｰﾄﾞ ) SELECT 作業用B.ID, 作業用B.従業員ｺｰﾄﾞ, 作業用B.日付, 作業用B.機種, 作業用B.号機, 作業用B.工場製番, 作業用B.工事番号, 作業用B.ﾕﾆｯﾄ番号, 作業用B.区分, 作業用B.項目, 作業用B.時間, 作業用B.所属ｺｰﾄﾞ FROM 作業用B WHERE (((作業用B.区分) Like '" & PARA2 & "*'));"
Case "hiduke"
    myquery.SQL = "INSERT INTO 作業用A ( ID, 従業員ｺｰﾄﾞ, 日付, 機種, 号機, 工場製番, 工事番号, ﾕﾆｯﾄ番号, 区分, 項目, 時間, 所属ｺｰﾄﾞ ) SELECT 作業時間.ID, 作業時間.従業員ｺｰﾄﾞ, 作業時間.日付, 作業時間.機種, 作業時間.号機, 作業時間.工場製番, 作業時間.工事番号, 作業時間.ﾕﾆｯﾄ番号, 作業時間.区分, 作業時間.項目, 作業時間.時間, 作業時間.所属ｺｰﾄﾞ FROM 作業時間 WHERE (((作業時間.日付)>=#" & PARA2 & "# And (作業時間.日付)<=#" & PARA3 & "#));"



Case "jin_sakujyo1"
    myquery.SQL = "INSERT INTO 人員削除 ( 従業員ｺｰﾄﾞ, 名前, ﾌﾘｶﾞﾅ, 所属, 所属ｺｰﾄﾞ, 電話番号, 住所, 備考 ) SELECT DISTINCTROW 人員登録.従業員ｺｰﾄﾞ, 人員登録.名前, 人員登録.ﾌﾘｶﾞﾅ, 人員登録.所属, 人員登録.所属ｺｰﾄﾞ, 人員登録.電話番号, 人員登録.住所, 人員登録.備考 FROM 人員登録 WHERE (((人員登録.ID)=" & PARA2 & "));"
Case "jin_sakujyo2"
    myquery.SQL = "DELETE DISTINCTROW 人員登録.ID, 人員登録.従業員ｺｰﾄﾞ, 人員登録.名前, 人員登録.ﾌﾘｶﾞﾅ, 人員登録.所属, 人員登録.所属ｺｰﾄﾞ, 人員登録.電話番号, 人員登録.住所, 人員登録.備考 FROM 人員登録 WHERE (((人員登録.ID)=" & PARA2 & "));"
Case "jin_teisei1"
    myquery.SQL = "INSERT INTO 人員訂正 ( ID, 従業員ｺｰﾄﾞ, 名前, ﾌﾘｶﾞﾅ, 所属, 所属ｺｰﾄﾞ, 電話番号, 住所, 備考 ) SELECT DISTINCTROW 人員登録.ID, 人員登録.従業員ｺｰﾄﾞ, 人員登録.名前, 人員登録.ﾌﾘｶﾞﾅ, 人員登録.所属, 人員登録.所属ｺｰﾄﾞ, 人員登録.電話番号, 人員登録.住所, 人員登録.備考 FROM 人員登録 WHERE (((人員登録.ID)=" & PARA2 & "));"
Case "jin_teisei2"
    myquery.SQL = "UPDATE DISTINCTROW 人員登録 SET 人員登録.従業員ｺｰﾄﾞ = " & PARA3 & ", 人員登録.名前 = " & PARA4 & ", 人員登録.ﾌﾘｶﾞﾅ = " & PARA5 & ", 人員登録.所属 =" & para6 & ", 人員登録.所属ｺｰﾄﾞ =" & para7 & ", 人員登録.電話番号 = " & para8 & ", 人員登録.住所 = " & para9 & ", 人員登録.備考 = " & para10 & " WHERE (((人員登録.ID)=" & PARA2 & "));"
Case "jin_touroku_人員２"
    myquery.SQL = "INSERT INTO 人員２ ( 従業員ｺｰﾄﾞ, 名前, ﾌﾘｶﾞﾅ, 所属, 所属ｺｰﾄﾞ, 電話番号, 住所, 備考 ) SELECT DISTINCTROW 人員登録.従業員ｺｰﾄﾞ, 人員登録.名前, 人員登録.ﾌﾘｶﾞﾅ, 人員登録.所属, 人員登録.所属ｺｰﾄﾞ, 人員登録.電話番号, 人員登録.住所, 人員登録.備考 FROM 人員登録 WHERE (((人員登録.従業員ｺｰﾄﾞ)='" & PARA2 & "') AND ((人員登録.所属ｺｰﾄﾞ)='" & PARA3 & "'));"
Case "jin_touroku_人員登録"
    myquery.SQL = "INSERT INTO 人員登録 ( 従業員ｺｰﾄﾞ, 名前, ﾌﾘｶﾞﾅ, 所属, 所属ｺｰﾄﾞ, 電話番号, 住所, 備考 ) SELECT DISTINCTROW 人員.従業員ｺｰﾄﾞ, 人員.名前, 人員.ﾌﾘｶﾞﾅ, 人員.所属, 人員.所属ｺｰﾄﾞ, 人員.電話番号, 人員.住所, 人員.備考 FROM 人員;"



Case "sakujyo2_元作業時間"
    myquery.SQL = "DELETE DISTINCTROW 元作業時間.ID, 元作業時間.従業員ｺｰﾄﾞ, 元作業時間.日付, 元作業時間.機種, 元作業時間.号機, 元作業時間.工場製番, 元作業時間.工事番号, 元作業時間.ﾕﾆｯﾄ番号, 元作業時間.区分, 元作業時間.項目, 元作業時間.時間, 元作業時間.所属ｺｰﾄﾞ FROM 元作業時間 WHERE (((元作業時間.ID)=" & PARA2 & "));"
Case "sakujyo2_作業用A"
    myquery.SQL = "DELETE DISTINCTROW 作業用A.ID, 作業用A.従業員ｺｰﾄﾞ, 作業用A.日付, 作業用A.機種, 作業用A.号機, 作業用A.工場製番, 作業用A.工事番号, 作業用A.ﾕﾆｯﾄ番号, 作業用A.区分, 作業用A.項目, 作業用A.時間, 作業用A.所属ｺｰﾄﾞ FROM 作業用A WHERE (((作業用A.ID)=" & PARA2 & "));"
Case "sakujyo2_作業用B"
    myquery.SQL = "DELETE DISTINCTROW 作業用B.ID, 作業用B.従業員ｺｰﾄﾞ, 作業用B.日付, 作業用B.機種, 作業用B.号機, 作業用B.工場製番, 作業用B.工事番号, 作業用B.ﾕﾆｯﾄ番号, 作業用B.区分, 作業用B.項目, 作業用B.時間, 作業用B.所属ｺｰﾄﾞ FROM 作業用B WHERE (((作業用B.ID)=" & PARA2 & "));"
Case "sakujyo2_作業時間"
    myquery.SQL = "DELETE DISTINCTROW 作業時間.ID, 作業時間.従業員ｺｰﾄﾞ, 作業時間.日付, 作業時間.機種, 作業時間.号機, 作業時間.工場製番, 作業時間.工事番号, 作業時間.ﾕﾆｯﾄ番号, 作業時間.区分, 作業時間.項目, 作業時間.時間, 作業時間.所属ｺｰﾄﾞ FROM 作業時間 WHERE (((作業時間.ID)=" & PARA2 & "));"

'**************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************
Case "sentaku"
    myquery.SQL = "INSERT INTO 作業時間 ( ID, 従業員ｺｰﾄﾞ, 日付, 機種, 号機, 工場製番, 工事番号, ﾕﾆｯﾄ番号, 区分, 項目, 時間, 所属ｺｰﾄﾞ ) SELECT DISTINCTROW 元作業時間.ID, 元作業時間.従業員ｺｰﾄﾞ, 元作業時間.日付, 元作業時間.機種, 元作業時間.号機, 元作業時間.工場製番, 元作業時間.工事番号, 元作業時間.ﾕﾆｯﾄ番号, 元作業時間.区分, 元作業時間.項目, 元作業時間.時間, 元作業時間.所属ｺｰﾄﾞ FROM 元作業時間 WHERE (((元作業時間.日付)>=#" & PARA4 & "# And (元作業時間.日付)<=#" & PARA5 & "#) AND ((元作業時間.所属ｺｰﾄﾞ)>='" & PARA2 & "' And (元作業時間.所属ｺｰﾄﾞ)<='" & PARA3 & "'));"
'   myquery.SQL = "INSERT INTO 作業時間 ( ID, 従業員ｺｰﾄﾞ, 日付, 機種, 号機, 工場製番, 工事番号, ﾕﾆｯﾄ番号, 区分, 項目, 時間, 所属ｺｰﾄﾞ ) SELECT DISTINCTROW 元作業時間.ID, 元作業時間.従業員ｺｰﾄﾞ, 元作業時間.日付, 元作業時間.機種, 元作業時間.号機, 元作業時間.工場製番, 元作業時間.工事番号, 元作業時間.ﾕﾆｯﾄ番号, 元作業時間.区分, 元作業時間.項目, 元作業時間.時間, 元作業時間.所属ｺｰﾄﾞ FROM 人員登録 INNER JOIN 元作業時間 ON 人員登録.従業員ｺｰﾄﾞ = 元作業時間.従業員ｺｰﾄﾞ WHERE (((人員登録.所属コード)>='" & PARA2 & "' And (人員登録.所属コード)<='" & PARA3 & "'));"

Case "teisei"
    myquery.SQL = "INSERT INTO 訂正 ( 従業員ｺｰﾄﾞ, 日付, 機種, 号機, 工場製番, 工事番号, ﾕﾆｯﾄ番号, 区分, 項目, 時間, 所属ｺｰﾄﾞ ) SELECT DISTINCTROW 元作業時間.従業員ｺｰﾄﾞ, 元作業時間.日付, 元作業時間.機種, 元作業時間.号機, 元作業時間.工場製番, 元作業時間.工事番号, 元作業時間.ﾕﾆｯﾄ番号, 元作業時間.区分, 元作業時間.項目, 元作業時間.時間, 元作業時間.所属ｺｰﾄﾞ FROM 元作業時間 WHERE (((元作業時間.従業員ｺｰﾄﾞ)=" & PARA2 & ") AND ((元作業時間.日付)=" & PARA3 & ") AND ((元作業時間.機種)=" & PARA4 & ") AND ((元作業時間.号機)=" & PARA5 & ") AND ((元作業時間.区分)=" & para6 & ") AND ((元作業時間.項目)=" & para7 & "));"


Case "teisei3_元作業時間"
    myquery.SQL = "UPDATE DISTINCTROW 元作業時間 SET 元作業時間.従業員ｺｰﾄﾞ = " & PARA3 & ", 元作業時間.日付 = " & PARA4 & ", 元作業時間.機種 =" & PARA5 & ", 元作業時間.号機 = " & para6 & ", 元作業時間.工場製番 =" & para7 & ", 元作業時間.工事番号 =" & para8 & ", 元作業時間.ﾕﾆｯﾄ番号 =" & para9 & ", 元作業時間.区分 =" & para10 & ", 元作業時間.項目 =" & para11 & ", 元作業時間.時間 = " & para12 & ", 元作業時間.所属ｺｰﾄﾞ =" & para13 & " WHERE (((元作業時間.ID)=" & PARA2 & "));"
Case "teisei3_作業用A"
    myquery.SQL = "UPDATE DISTINCTROW 作業用A SET 作業用A.従業員ｺｰﾄﾞ = " & PARA3 & ", 作業用A.日付 = " & PARA4 & ", 作業用A.機種 =" & PARA5 & ", 作業用A.号機 = " & para6 & ", 作業用A.工場製番 =" & para7 & ", 作業用A.工事番号 =" & para8 & ", 作業用A.ﾕﾆｯﾄ番号 =" & para9 & ", 作業用A.区分 =" & para10 & ", 作業用A.項目 =" & para11 & ", 作業用A.時間 = " & para12 & ", 作業用A.所属ｺｰﾄﾞ =" & para13 & " WHERE (((作業用A.ID)=" & PARA2 & "));"
Case "teisei3_作業用B"
    myquery.SQL = "UPDATE DISTINCTROW 作業用B SET 作業用B.従業員ｺｰﾄﾞ = " & PARA3 & ", 作業用B.日付 = " & PARA4 & ", 作業用B.機種 =" & PARA5 & ", 作業用B.号機 = " & para6 & ", 作業用B.工場製番 =" & para7 & ", 作業用B.工事番号 =" & para8 & ", 作業用B.ﾕﾆｯﾄ番号 =" & para9 & ", 作業用B.区分 =" & para10 & ", 作業用B.項目 =" & para11 & ", 作業用B.時間 = " & para12 & ", 作業用B.所属ｺｰﾄﾞ =" & para13 & " WHERE (((作業用B.ID)=" & PARA2 & "));"
Case "teisei3_作業時間"
    myquery.SQL = "UPDATE DISTINCTROW 作業時間 SET 作業時間.従業員ｺｰﾄﾞ = " & PARA3 & ", 作業時間.日付 = " & PARA4 & ", 作業時間.機種 =" & PARA5 & ", 作業時間.号機 = " & para6 & ", 作業時間.工場製番 =" & para7 & ", 作業時間.工事番号 =" & para8 & ", 作業時間.ﾕﾆｯﾄ番号 =" & para9 & ", 作業時間.区分 =" & para10 & ", 作業時間.項目 =" & para11 & ", 作業時間.時間 = " & para12 & ", 作業時間.所属ｺｰﾄﾞ =" & para13 & " WHERE (((作業時間.ID)=" & PARA2 & "));"


Case "touroku"
    myquery.SQL = "INSERT INTO 元作業時間 ( 従業員ｺｰﾄﾞ, 日付, 機種, 号機, 工場製番, 工事番号, ﾕﾆｯﾄ番号, 区分, 項目, 時間, 所属ｺｰﾄﾞ ) SELECT DISTINCTROW 登録.従業員ｺｰﾄﾞ, 登録.日付, 登録.機種, 登録.号機, 登録.工場製番, 登録.工事番号, 登録.ﾕﾆｯﾄ番号, 登録.区分, 登録.項目, 登録.時間, 登録.所属ｺｰﾄﾞ FROM 登録;"
Case "touroku_S"
    myquery.SQL = "INSERT INTO 作業時間 ( ID, 従業員ｺｰﾄﾞ, 日付, 機種, 号機, 工場製番, 工事番号, ﾕﾆｯﾄ番号, 区分, 項目, 時間, 所属ｺｰﾄﾞ ) SELECT DISTINCTROW 登録.ID, 登録.従業員ｺｰﾄﾞ, 登録.日付, 登録.機種, 登録.号機, 登録.工場製番, 登録.工事番号, 登録.ﾕﾆｯﾄ番号, 登録.区分, 登録.項目, 登録.時間, 登録.所属ｺｰﾄﾞ FROM 登録;"
Case "touroku_A"
    myquery.SQL = "INSERT INTO 作業用A ( ID, 従業員ｺｰﾄﾞ, 日付, 機種, 号機, 工場製番, 工事番号, ﾕﾆｯﾄ番号, 区分, 項目, 時間, 所属ｺｰﾄﾞ ) SELECT DISTINCTROW 登録.ID, 登録.従業員ｺｰﾄﾞ, 登録.日付, 登録.機種, 登録.号機, 登録.工場製番, 登録.工事番号, 登録.ﾕﾆｯﾄ番号, 登録.区分, 登録.項目, 登録.時間, 登録.所属ｺｰﾄﾞ FROM 登録;"
Case "touroku_B"
    myquery.SQL = "INSERT INTO 作業用B ( ID, 従業員ｺｰﾄﾞ, 日付, 機種, 号機, 工場製番, 工事番号, ﾕﾆｯﾄ番号, 区分, 項目, 時間, 所属ｺｰﾄﾞ ) SELECT DISTINCTROW 登録.ID, 登録.従業員ｺｰﾄﾞ, 登録.日付, 登録.機種, 登録.号機, 登録.工場製番, 登録.工事番号, 登録.ﾕﾆｯﾄ番号, 登録.区分, 登録.項目, 登録.時間, 登録.所属ｺｰﾄﾞ FROM 登録;"


Case "TEST_PRO"
    myquery.SQL = "INSERT INTO testx ( 従業員ｺｰﾄﾞ, 日付, 機種, 号機, 工場製番, 工事番号, ﾕﾆｯﾄ番号, 区分, 項目, 時間 ) SELECT 作業時間.従業員ｺｰﾄﾞ, 作業時間.日付, 作業時間.機種, 作業時間.号機, 作業時間.工場製番, 作業時間.工事番号, 作業時間.ﾕﾆｯﾄ番号, 作業時間.区分, 作業時間.項目, 作業時間.時間 FROM 作業時間 WHERE (((作業時間.従業員ｺｰﾄﾞ) Like  '" & PARA2 & "*'));"
Case "SHOZOKU_GET"
    myquery.SQL = "INSERT INTO 従業員所属 ( 従業員ｺｰﾄﾞ, 所属ｺｰﾄﾞ ) SELECT DISTINCTROW 人員登録.従業員ｺｰﾄﾞ, 人員登録.所属ｺｰﾄﾞ FROM 人員登録 WHERE (((人員登録.従業員ｺｰﾄﾞ)='" & PARA2 & "'));"
Case "INS_ID_GET_TBL"
    myquery.SQL = "INSERT INTO ID_GET_TBL ( IDの最大, 従業員ｺｰﾄﾞ, 日付, 区分, 時間 ) SELECT DISTINCTROW Max(元作業時間.ID) AS IDの最大, 元作業時間.従業員ｺｰﾄﾞ, 元作業時間.日付, 元作業時間.区分, 元作業時間.時間 FROM 元作業時間 GROUP BY 元作業時間.従業員ｺｰﾄﾞ, 元作業時間.日付, 元作業時間.区分, 元作業時間.時間 HAVING (((元作業時間.従業員ｺｰﾄﾞ)='" & PARA2 & "') AND ((元作業時間.日付)=#" & PARA3 & "#) AND ((元作業時間.区分)='" & PARA4 & "') AND ((元作業時間.時間)=" & PARA5 & "));"
Case "NAME_GET"
    myquery.SQL = "INSERT INTO NAME_GET_TBL ( 従業員ｺｰﾄﾞ, 所属ｺｰﾄﾞ, 名前 ) SELECT DISTINCTROW 人員登録.従業員ｺｰﾄﾞ, 人員登録.所属ｺｰﾄﾞ, 人員登録.名前 FROM 人員登録 WHERE (((人員登録.従業員ｺｰﾄﾞ)='" & PARA2 & "'));"

End Select

mydb.QueryDefs.Append myquery

DoCmd.SetWarnings False
DoCmd.OpenQuery "workquery"
mydb.QueryDefs.Delete "workquery"
DoCmd.SetWarnings True

Exit Sub
ErrorHandler:
  Set s0 = mydb.OpenRecordset("ｴﾗｰﾛｸﾞ", DB_OPEN_TABLE)
  s0.AddNew
  s0![日付] = Date
  s0![エラーコード] = Err
  s0![エラーメッセージ] = Error(Err)
  s0.UPDATE
  s0.Close