<!DOCTYPE html>
<html lang="ja">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>高精度CNC加工機製造業向け 次世代統合管理システム導入提案書</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/mermaid@10/dist/mermaid.min.js"></script>
    <style>
        @import url('https://fonts.googleapis.com/css2?family=Noto+Sans+JP:wght@400;500;700&display=swap');
        body {
            font-family: 'Noto Sans JP', sans-serif;
            background-color: #F0F2F5;
            color: #333;
            display: flex;
            flex-direction: column;
            align-items: center;
            padding: 20px;
        }
        .a3-page {
            width: 1587px;
            height: 1122px;
            background-color: #FFFFFF;
            box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
            margin-bottom: 30px;
            padding: 50px;
            box-sizing: border-box;
            display: flex;
            flex-direction: column;
            overflow: hidden;
            border: 1px solid #E0E0E0;
        }
        .header-bg {
            background: linear-gradient(135deg, #1A237E 0%, #0D47A1 100%);
            color: white;
            padding: 20px 40px;
            text-align: center;
            border-radius: 8px;
            margin-bottom: 20px;
            width: 1587px;
        }
        .footer-bg {
            background-color: #37474F;
            color: #CFD8DC;
            padding: 15px 40px;
            text-align: center;
            border-radius: 8px;
            width: 1587px;
        }
        .page-header h2 {
            font-size: 2.8rem;
            font-weight: 700;
            color: #1A237E;
            border-bottom: 5px solid #1976D2;
            padding-bottom: 15px;
            display: inline-block;
            margin-bottom: 25px;
        }
        .executive-summary {
            background: linear-gradient(135deg, #0D47A1, #1976D2);
            color: white;
            padding: 20px;
            border-radius: 12px;
            box-shadow: 0 4px 12px rgba(0,0,0,0.15);
        }
        .executive-summary h4 {
            font-weight: 700;
            font-size: 1.5rem;
            margin-bottom: 0.75rem;
            border-bottom: 2px solid rgba(255,255,255,0.3);
            padding-bottom: 0.5rem;
        }
        .executive-summary p {
            font-size: 1.1rem;
            line-height: 1.6;
        }
        .executive-summary strong {
            font-size: 1.5rem;
            color: #BBDEFB;
        }
        .card {
            background-color: #FFFFFF;
            border-radius: 12px;
            border: 1px solid #E0E0E0;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
            padding: 25px;
            height: 100%;
            display: flex;
            flex-direction: column;
        }
        .card h3 {
            font-size: 1.8rem;
            font-weight: 700;
            margin-bottom: 15px;
            color: #0D47A1;
            text-align: center;
        }
        .flowchart-step {
            background-color: #FBE9E7;
            border: 2px solid #FF8A65;
            color: #BF360C;
            padding: 1rem;
            border-radius: 8px;
            text-align: center;
            font-weight: 600;
            font-size: 1.1rem;
            box-shadow: 0 2px 4px rgba(0,0,0,0.05);
        }
        .flowchart-arrow {
            color: #FF7043;
            font-size: 2rem;
            font-weight: bold;
            margin: 0.5rem 0;
        }
        .benefit-grid {
            display: grid;
            grid-template-columns: repeat(2, 1fr);
            gap: 15px;
        }
        .benefit-item {
            padding: 15px;
            border-radius: 8px;
            border-width: 2px;
            border-style: solid;
        }
        .benefit-item h5 {
            font-weight: 700;
            font-size: 1.25rem;
            margin-bottom: 5px;
        }
        .benefit-item p {
            font-size: 1rem;
            line-height: 1.5;
        }
        .comparison-table th, .comparison-table td {
            border: 1px solid #CFD8DC;
            padding: 12px;
            text-align: left;
            vertical-align: top;
            font-size: 1rem;
        }
        .comparison-table th {
            background-color: #ECEFF1;
            font-weight: 700;
        }
        .accent-red { color: #D32F2F; font-weight: 600; }
        .accent-green { color: #388E3C; font-weight: 600; }
        .stat-value {
            font-size: 3rem;
            font-weight: 700;
            line-height: 1;
        }
        .stat-label {
            font-size: 1.1rem;
            color: #546E7A;
            margin-top: 5px;
        }
        .tech-skill-card {
            background: #ECEFF1;
            border-radius: 10px;
            padding: 15px;
        }
        .tech-skill-card h4 {
            color: #37474F;
            font-weight: 700;
            font-size: 1.25rem;
            margin-bottom: 10px;
        }
        .tech-skill-card ul {
            list-style-type: none;
            padding-left: 0;
        }
        .tech-skill-card li {
            background: #CFD8DC;
            margin-bottom: 6px;
            padding: 8px 12px;
            border-radius: 6px;
            font-size: 0.9rem;
        }
        .mermaid { text-align: center; }
    </style>
</head>
<body class="antialiased">

    <header class="header-bg">
        <h1 class="text-4xl font-bold mb-2">高精度CNC加工機製造業向け 次世代統合管理システム</h1>
        <p class="text-lg opacity-95">TimePro・ODBC自動連携 × AI監視 × リアルタイム協業で実現する生産性革命</p>
    </header>

    <!-- PAGE 1: EXECUTIVE SUMMARY & CURRENT PROBLEMS -->
    <div class="a3-page">
        <div class="page-header text-center">
            <h2>1. 経営層向け要約 と 現状の課題</h2>
        </div>
        
        <div class="executive-summary mb-6">
            <div class="grid grid-cols-4 gap-6 text-center">
                <div>
                    <h4>🎉 目的 (うれしさ)</h4>
                    <p>TimePro・旧DBの<strong class="text-yellow-300">手動作業を完全自動化</strong>し、現場の非効率を撲滅。CNC加工の生産性を劇的に向上させる。</p>
                </div>
                <div>
                    <h4>📊 効果 (定量的)</h4>
                    <p>年間<strong class="text-yellow-300">約24,000時間</strong>の工数削減、および手作業ミスによる損失<strong class="text-yellow-300">約3,600万円</strong>を回避。</p>
                </div>
                <div>
                    <h4>💰 費用 (工数)</h4>
                    <p>内製開発により<strong class="text-yellow-300">約200人日</strong>で実現。外部パッケージ導入・改修に比べ<strong class="text-yellow-300">1/20以下</strong>の圧倒的低コスト。</p>
                </div>
                <div>
                    <h4>⏰ 納期 (LT)</h4>
                    <p>開発期間<strong class="text-yellow-300">2～3週間</strong>でコア機能を提供。迅速に効果を実感し、現場の士気を向上させる。</p>
                </div>
            </div>
        </div>
        
        <div class="grid grid-cols-2 gap-8 flex-grow">
            <div class="card">
                <h3>現状の課題：現場に潜む「見えないコスト」の正体</h3>
                <p class="text-center text-gray-600 mb-6">TimeProと旧ODBCデータベースの「手動連携」が、日々の生産性を静かに蝕んでいます。</p>
                <div class="space-y-4 text-lg">
                    <div class="flex items-start p-4 bg-red-50 border-l-4 border-red-500 rounded-r-lg">
                        <span class="text-3xl mr-4">⏰</span>
                        <div><strong class="text-red-700">膨大な手入力工数：</strong>作業員1人あたり毎日10分の入力作業が、年間では約24,000時間もの膨大な工数ロスに。</div>
                    </div>
                    <div class="flex items-start p-4 bg-red-50 border-l-4 border-red-500 rounded-r-lg">
                        <span class="text-3xl mr-4">💣</span>
                        <div><strong class="text-red-700">高額なエラーコスト：</strong>手動同期による入力ミス、データ不整合が、再加工や納期遅延を誘発。月平均300万円の損失が発生。</div>
                    </div>
                    <div class="flex items-start p-4 bg-yellow-50 border-l-4 border-yellow-600 rounded-r-lg">
                        <span class="text-3xl mr-4">🐢</span>
                        <div><strong class="text-yellow-800">リアルタイム性の欠如：</strong>加工進捗の把握にタイムラグが生じ、迅速な経営判断や品質管理の妨げとなっている。</div>
                    </div>
                     <div class="flex items-start p-4 bg-yellow-50 border-l-4 border-yellow-600 rounded-r-lg">
                        <span class="text-3xl mr-4">🤔</span>
                        <div><strong class="text-yellow-800">モチベーションの低下：</strong>単純で非生産的な繰り返し作業が、現場作業員の貴重な時間と意欲を削いでいる。</div>
                    </div>
                </div>
            </div>

            <div class="card">
                <h3>非効率な手作業の悪循環（現状の操作イメージ）</h3>
                <p class="text-center text-gray-600 mb-6">この複雑で時間のかかるプロセスを、全作業員が毎日繰り返しています。</p>
                <div class="space-y-2 flex flex-col items-center flex-grow justify-center">
                    <div class="flowchart-step w-3/4">PC起動後、共有フォルダからAccessファイルを探して開く</div>
                    <div class="flowchart-arrow self-center">▼</div>
                    <div class="flowchart-step w-3/4">旧システムの起動を待つ（数十秒～数分）</div>
                    <div class="flowchart-arrow self-center">▼</div>
                    <div class="flowchart-step w-3/4">複数のメニューを何度もクリックし、目的の入力画面に到達</div>
                    <div class="flowchart-arrow self-center">▼</div>
                    <div class="flowchart-step w-3/4">TimeProのデータを目視で確認しながら、手作業で転記・入力</div>
                     <div class="flowchart-arrow self-center">▼</div>
                    <div class="flowchart-step w-3/4">入力ミスがないか自己チェック後、保存。完了まで平均10分。</div>
                </div>
            </div>
        </div>
    </div>

    <!-- PAGE 2: SOLUTION & BENEFITS -->
    <div class="a3-page">
        <div class="page-header text-center">
            <h2>2. 統合システムによる解決策と導入効果</h2>
        </div>
        
        <div class="grid grid-cols-5 gap-8 flex-grow">
            <!-- Left 2 columns -->
            <div class="col-span-2 flex flex-col gap-8">
                <div class="card">
                    <h3>4つの核心機能による相乗効果</h3>
                    <p class="text-center text-gray-600 mb-4">4つの機能を統合し、現場の生産性を飛躍的に向上させます。</p>
                    <div class="benefit-grid flex-grow">
                        <div class="benefit-item border-blue-500 bg-blue-50">
                            <h5 class="text-blue-800">🔄 TimePro・ODBC自動化</h5>
                            <p>工時データの手動入力を撤廃。PostgreSQLをハブとした完全自動同期で、工数を<strong>95%以上削減</strong>します。</p>
                        </div>
                        <div class="benefit-item border-green-500 bg-green-50">
                            <h5 class="text-green-800">💬 リアルタイム協業</h5>
                            <p>現場と管理部門が即座に情報共有。問題発生時の対応時間を<strong>70%短縮</strong>し、機会損失を防ぎます。</p>
                        </div>
                        <div class="benefit-item border-purple-500 bg-purple-50">
                            <h5 class="text-purple-800">🤖 AI画像監視</h5>
                            <p>YOLO技術で24時間、品質・安全を自動監視。検査工数を<strong>80%削減</strong>し、不良品流出を未然に防ぎます。</p>
                        </div>
                        <div class="benefit-item border-orange-500 bg-orange-50">
                            <h5 class="text-orange-800">⚙️ PLC統合制御</h5>
                            <p>各設備のPLC情報を集約し、一元管理。設備稼働率を<strong>15%向上</strong>させ、生産計画の精度を高めます。</p>
                        </div>
                    </div>
                </div>
                 <div class="card">
                    <h3>圧倒的なコストパフォーマンス</h3>
                    <p class="text-center text-gray-600 mb-4">本提案の内製開発は、外部パッケージ製品の導入・改修に比べ、コストと期間を大幅に圧縮できます。</p>
                     <div class="flex-grow" style="height: 250px;">
                        <canvas id="costBenefitChart"></canvas>
                    </div>
                </div>
            </div>

            <!-- Right 3 columns -->
            <div class="col-span-3 flex flex-col">
                <div class="card">
                     <h3>定量的効果：利益に直結する改善インパクト</h3>
                    <div class="grid grid-cols-3 gap-4 text-center my-6 py-6 bg-gray-50 rounded-lg">
                        <div>
                            <p class="stat-value text-blue-600">24,000</p>
                            <p class="stat-label">時間/年の工数削減</p>
                        </div>
                        <div>
                            <p class="stat-value text-green-600">4,000</p>
                            <p class="stat-label">万円/年のコスト削減効果</p>
                        </div>
                        <div>
                            <p class="stat-value text-purple-600">30%</p>
                            <p class="stat-label">総合生産性向上(目標)</p>
                        </div>
                    </div>
                    
                    <h3 class="mt-4">導入効果比較：現状 vs 統合システム</h3>
                    <div class="overflow-x-auto flex-grow">
                        <table class="w-full comparison-table">
                            <thead>
                                <tr>
                                    <th class="w-1/4">機能分野</th>
                                    <th class="w-2/5">現状の課題</th>
                                    <th class="w-2/5">統合システム導入後の姿</th>
                                    <th class="w-1/5">定量的効果</th>
                                </tr>
                            </thead>
                            <tbody>
                                <tr>
                                    <td><strong>工時管理</strong></td>
                                    <td class="accent-red">手動入力10分/日/人<br>データ不整合が頻発</td>
                                    <td class="accent-green">完全自動同期<br>リアルタイムで正確なデータ</td>
                                    <td><strong>工数95%削減</strong></td>
                                </tr>
                                <tr>
                                    <td><strong>情報共有</strong></td>
                                    <td class="accent-red">電話・メールに依存<br>対応に遅れが発生</td>
                                    <td class="accent-green">リアルタイムチャットで即時連携<br>全関係者が同時に状況把握</td>
                                    <td><strong>対応時間70%短縮</strong></td>
                                </tr>
                                <tr>
                                    <td><strong>品質・安全管理</strong></td>
                                    <td class="accent-red">人的な目視検査のみ<br>24時間監視は不可能</td>
                                    <td class="accent-green">AIによる24時間自動監視<br>異常を即時検知・通知</td>
                                    <td><strong>不良品90%減</strong></td>
                                </tr>
                                <tr>
                                    <td><strong>設備管理</strong></td>
                                    <td class="accent-red">設備ごとに個別操作<br>稼働状況が不透明</td>
                                    <td class="accent-green">全設備の情報を統合管理<br>稼働率を可視化・最適化</td>
                                    <td><strong>稼働率15%向上</strong></td>
                                </tr>
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <!-- PAGE 3: TECHNICAL OVERVIEW -->
    <div class="a3-page">
        <div class="page-header text-center">
            <h2>3. 技術概要：なぜ専門的なシステム開発が必要か</h2>
        </div>
        <div class="grid grid-cols-2 gap-8 flex-grow">
            <div class="card">
                <h3>システムアーキテクチャ概要</h3>
                <p class="text-center text-gray-600 mb-4">安定的かつ拡張性の高い「マイクロサービスアーキテクチャ」を採用。将来の機能追加にも柔軟に対応可能です。</p>
                <div class="mermaid flex-grow" style="font-size: 10px;">
graph TB
    subgraph "クライアント操作層"
        Launcher[統合ランチャー<br/>🔧 データベース接続管理<br/>📊 TimePro同期監視<br/>🚀 サービス状態総覧<br/>👤 ユーザーログイン制御]
        Program1[従業員データ操作画面<br/>📈 TimePro分散データ表示<br/>🔍 リアルタイムデータ差異監視<br/>⚡ 自動同期コンソール<br/>💬 リアルタイム協業チャット<br/>📹 映像監視パネル]
        Program2[PLC工業プログラミングツール<br/>🏭 機器制御プログラミング<br/>📐 ラダー図エディタ<br/>⚙️ 工業ロジック制御<br/>🗺️ システムアーキテクチャ可視化]
    end
    
    subgraph "ゲートウェイ・負荷分散層"
        Nginx[Nginxインテリジェントゲートウェイ<br/>🔄 負荷分散スケジューリング<br/>🔒 SSL安全ターミナル<br/>📁 静的リソースサービス<br/>🔀 リバースプロキシルーティング]
    end
    
    subgraph "マイクロサービス群"
        Server1[データ同期・ビジネスサービス<br/>⭐ PostgreSQL分散エンジン<br/>🔗 TimeProデータプラー<br/>🧠 分散レベルデータ比較<br/>🔧 トランザクション同期エンジン]
        Server2[リアルタイム通信サービス<br/>🌐 WebSocketチャット<br/>📁 ファイル共有システム<br/>🔐 メッセージ暗号化伝送]
        Server3[認証認可サービス<br/>🎫 JWTトークン管理<br/>✅ ユーザー身元認証<br/>🛡️ アクセス権限制御]
        Server4[インテリジェント映像監視サービス<br/>🎥 リアルタイム映像ストリーミング<br/>🤖 YOLO AI目標検出<br/>📷 複数カメラ管理]
    end
    
    subgraph "PostgreSQLデータセンター"
        PostgreSQL[(PostgreSQLメインデータベース<br/>🏢 データ統合センター<br/>📊 分散テーブル管理<br/>⚡ 高性能クエリエンジン<br/>🔄 リアルタイムデータ同期)]
        subgraph "分散テーブル群"
            TimePro_Partitions[TimePro分散テーブル群]
            ODBC_Partitions[ODBCマッピング分散テーブル]
            Business_Partitions[ビジネスデータ分散テーブル]
        end
    end
    
    subgraph "キャッシュ・補助ストレージ層"
        Redis[(Redis高速キャッシュ)]
        MongoDB[(MongoDBドキュメントデータベース)]
        FileSystem[ファイルシステムストレージ]
    end
    
    subgraph "外部データソースシステム"
        TimePro_Source[TimePro勤務システム]
        Legacy_MDB[旧式MDBデータベース]
        Camera[インテリジェントカメラシステム]
        Sensors[工業センサーネットワーク]
        PLC[PLCインダストリアルコントローラ]
    end
    
    %% クライアント接続
    Launcher -.->|HTTPS/WSS| Nginx
    Program1 -.->|HTTPS/WSS| Nginx
    Program2 -.->|HTTPS/WSS| Nginx
    
    %% ゲートウェイルーティング配信
    Nginx --> Server1 & Server2 & Server3 & Server4
    
    %% マイクロサービス間通信
    Server1 & Server2 & Server4 <-.->|認証| Server3
    Server1 <-.->|状態同期| Server2
    
    %% データセンター接続
    Server1 & Server2 & Server3 & Server4 --> PostgreSQL
    
    %% PostgreSQL内部分散関係
    PostgreSQL --> TimePro_Partitions & ODBC_Partitions & Business_Partitions
    
    %% 外部データソース接続
    TimePro_Partitions <-.->|FDW| TimePro_Source
    ODBC_Partitions <-.->|FDW| Legacy_MDB
    
    %% 補助ストレージ接続
    Server1 --> Redis & MongoDB
    Server2 --> FileSystem
    Server4 --> FileSystem
    
    %% 外部機器接続
    Server1 <--> Sensors & PLC
    Server4 <--> Camera

    %% スタイル定義
    classDef default fill:#fff,stroke:#333,stroke-width:2px,font-size:12px;
    classDef clientStyle fill:#e1f5fe,stroke:#01579b,stroke-width:2px;
    classDef serviceStyle fill:#f3e5f5,stroke:#4a148c,stroke-width:2px;
    classDef dbStyle fill:#e8f5e8,stroke:#1b5e20,stroke-width:3px;
    classDef partitionStyle fill:#fff3e0,stroke:#e65100,stroke-width:2px;
    classDef sourceStyle fill:#fce4ec,stroke:#880e4f,stroke-width:2px;
    classDef gatewayStyle fill:#e3f2fd,stroke:#0d47a1,stroke-width:2px;
    
    class Launcher,Program1,Program2 clientStyle;
    class Server1,Server2,Server3,Server4 serviceStyle;
    class PostgreSQL dbStyle;
    class TimePro_Partitions,ODBC_Partitions,Business_Partitions partitionStyle;
    class Redis,MongoDB,FileSystem,Legacy_MDB,Camera,Sensors,PLC,TimePro_Source sourceStyle;
    class Nginx gatewayStyle;
                </div>
            </div>
            <div class="card">
                <h3>なぜ「フルスタック開発」が必要なのか？</h3>
                <p class="text-center text-gray-600 mb-4">CNC加工現場のシステムは、単一のプログラミング言語知識だけでは構築不可能です。下記のような多様な専門技術の組み合わせが不可欠です。</p>
                <div class="grid grid-cols-3 gap-4 flex-grow">
                    <div class="tech-skill-card">
                        <h4>🏗️ システム基盤技術</h4>
                        <ul>
                            <li>マイクロサービス設計</li>
                            <li>データベース設計(SQL/NoSQL)</li>
                            <li>API設計</li>
                            <li>認証・認可 (セキュリティ)</li>
                            <li>クラウド/コンテナ技術</li>
                            <li>高並行性設計</li>
                        </ul>
                    </div>
                    <div class="tech-skill-card">
                        <h4>🏭 工場特化技術</h4>
                        <ul>
                            <li>PLCラダー/プロトコル通信</li>
                            <li>リアルタイム制御</li>
                            <li>IIoTデータ収集</li>
                            <li>AI/画像処理アルゴリズム</li>
                            <li>工作機械API連携</li>
                            <li>エッジコンピューティング</li>
                        </ul>
                    </div>
                    <div class="tech-skill-card">
                        <h4>🖥️ アプリ開発技術</h4>
                        <ul>
                            <li>UI/フロントエンド開発</li>
                            <li>サーバーサイド開発</li>
                            <li>リアルタイム通信(WebSocket)</li>
                            <li>要求分析・要件定義</li>
                            <li>自動テスト/CI/CD</li>
                            <li>プロジェクト管理</li>
                        </ul>
                    </div>
                </div>
                <p class="mt-6 text-center text-lg font-semibold text-gray-700 bg-gray-100 p-4 rounded-lg">これら<strong class="text-blue-700">30以上</strong>の専門技術を統合する<strong class="text-blue-700">「研究開発能力」+「企画・設計能力」+「フルスタック実装能力」</strong>が、真に価値あるシステムを実現します。</p>
            </div>
        </div>
    </div>

    <!-- PAGE 4: FUTURE OUTLOOK -->
    <div class="a3-page">
        <div class="page-header text-center">
            <h2>4. 導入による将来展望と戦略的価値</h2>
        </div>
        <div class="flex flex-col h-full">
            <div class="card flex-grow">
                <h3 class="text-center">単なる効率化を超えた、企業の未来への戦略的投資</h3>
                <p class="text-center text-gray-600 mb-8 text-lg">この統合システムは、目先のコスト削減だけでなく、会社の持続的な成長を支える強固な基盤となります。</p>
                <div class="grid md:grid-cols-3 gap-8 flex-grow">
                    <div class="p-6 bg-blue-50 rounded-lg text-center flex flex-col items-center justify-center border-t-4 border-blue-500">
                        <span class="text-6xl mb-4">📊</span>
                        <h4 class="text-2xl font-bold mt-2 mb-2 text-blue-800">データ駆動型経営への進化</h4>
                        <p class="text-base text-gray-700">正確でリアルタイムな現場データを基に、機械設計・要素開発から生産最適化・コスト計算まで、全ての意思決定が迅速かつ的確になります。製造業DXの核となる経営基盤を構築します。</p>
                    </div>
                    <div class="p-6 bg-green-50 rounded-lg text-center flex flex-col items-center justify-center border-t-4 border-green-500">
                        <span class="text-6xl mb-4">💡</span>
                        <h4 class="text-2xl font-bold mt-2 mb-2 text-green-800">社内技術力の飛躍的向上</h4>
                        <p class="text-base text-gray-700">フルスタック内製開発を通じて、高精度制御、AI、システム設計といった最先端技術が社内に蓄積されます。将来の市場変化や技術革新に、迅速かつ柔軟に対応できる自社だけの強みを確立します。</p>
                    </div>
                    <div class="p-6 bg-purple-50 rounded-lg text-center flex flex-col items-center justify-center border-t-4 border-purple-500">
                        <span class="text-6xl mb-4">🚀</span>
                        <h4 class="text-2xl font-bold mt-2 mb-2 text-purple-800">次世代製造業への変革</h4>
                        <p class="text-base text-gray-700">熟練技能のデジタル化、AIによる自律的な生産管理など、Industry 4.0の実現を加速させます。本システムは、他社の追随を許さない、次世代の「スマートファクトリー」へ向けた第一歩です。</p>
                    </div>
                </div>
            </div>

            <div class="text-center mt-8">
                <h3 class="text-3xl font-bold text-gray-800">結論：今こそ、未来への競争優位を確立する時です。</h3>
                <p class="text-xl text-gray-600 mt-2">この統合システム開発は、<strong class="accent-green">低リスク・低コスト・短期間</strong>で、<strong class="accent-green">最大の効果</strong>を生み出す、最も現実的かつ戦略的な選択です。</p>
            </div>
        </div>
    </div>
    
    <footer class="footer-bg">
        <p class="text-sm">この提案書は、ご提示いただいた分析資料に基づき、貴社の更なる発展のために作成されました。</p>
        <p>&copy; <span id="currentYear"></span> [貴社名]. All rights reserved.</p>
    </footer>

    <script>
        mermaid.initialize({ startOnLoad: true });
        document.getElementById('currentYear').textContent = new Date().getFullYear();

        const costBenefitCtx = document.getElementById('costBenefitChart')?.getContext('2d');
        if (costBenefitCtx) {
            new Chart(costBenefitCtx, {
                type: 'bar',
                data: {
                    labels: ['外部パッケージ購入・改修', '本提案 (内製開発)'],
                    datasets: [{
                        label: '想定費用 (万円)',
                        data: [4000, 200],
                        backgroundColor: ['rgba(255, 159, 64, 0.6)', 'rgba(75, 192, 192, 0.6)'],
                        borderColor: ['rgba(255, 159, 64, 1)', 'rgba(75, 192, 192, 1)'],
                        borderWidth: 1
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    indexAxis: 'y',
                    scales: {
                        x: {
                            beginAtZero: true,
                            title: { display: true, text: '想定費用 (万円)', font: { size: 14 } }
                        }
                    },
                    plugins: {
                        legend: { display: false },
                        title: { display: true, text: '開発アプローチ別 コスト比較', font: { size: 18 } }
                    }
                }
            });
        }
    </script>
</body>
</html>
