
************:8009)
✅ Server6 HTTP: 成功 - {'status': 'degraded', 'mdb_available': False, 'connection_pool': {'active_connections': 0, 'max_connections': 5, 'available_connections': 5}, 'system_info': {'platform': 'Windows', 'platform_release': '10', 'platform_version': '10.0.19045', 'machine': 'AMD64', 'processor': 'Intel64 Family 6 Model 85 Stepping 4, GenuineIntel', 'python_version': '3.12.11'}, 'uptime': 392.657751083374, 'last_check': '2025-06-27T09:58:08.495919'}
✅ PostgreSQL连接: 成功 (************:5432)
✅ Redis连接: 成功 (localhost:6379)

🌐 访问地址:
  - 主页: http://localhost:8009
  - 状态: http://localhost:8009/status
  - 健康检查: http://localhost:8009/health
  - API文档: http://localhost:8009/docs

⚠️ 按 Ctrl+C 可安全停止服务


25-06-27 10:09:07,986 - app.database.mongodb_client - DEBUG - 📋 MongoDB索引创建完成
2025-06-27 10:09:07,986 - app.database.mongodb_client - INFO - ✅ MongoDB连接成功
2025-06-27 10:09:08,060 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, '/home/<USER>/Documents/testF/2025/ser25/server0613/MySuiteDatebase2402newuigood-yolo5-3/server5/logs/server5.log')}
2025-06-27 10:09:08,208 - app.database.postgresql_client - INFO - ✅ PostgreSQL连接池创建成功
2025-06-27 10:09:08,208 - app.services.f1_listener - INFO - ✅ f1监听器服务启动成功
2025-06-27 10:09:08,209 - app.main - INFO - f1_listener 启动成功
2025-06-27 10:09:08,209 - app.main - INFO - 启动 f2_push_writer...
2025-06-27 10:09:08,209 - app.database.postgresql_client - INFO - 📡 开始监听频道: push_job
2025-06-27 10:09:08,210 - pymongo.topology - DEBUG - {"message": "Starting topology monitoring", "topologyId": {"$oid": "685def34d0a7d263c9c7c70b"}}
2025-06-27 10:09:08,210 - pymongo.topology - DEBUG - {"message": "Topology description changed", "topologyId": {"$oid": "685def34d0a7d263c9c7c70b"}, "previousDescription": "<TopologyDescription id: 685def34d0a7d263c9c7c70b, topology_type: Unknown, servers: []>", "newDescription": "<TopologyDescription id: 685def34d0a7d263c9c7c70b, topology_type: Unknown, servers: [<ServerDescription ('************', 27017) server_type: Unknown, rtt: None>]>"}
2025-06-27 10:09:08,210 - pymongo.topology - DEBUG - {"message": "Starting server monitoring", "topologyId": {"$oid": "685def34d0a7d263c9c7c70b"}, "serverHost": "************", "serverPort": 27017}
2025-06-27 10:09:08,211 - pymongo.connection - DEBUG - {"message": "Connection pool created", "clientId": {"$oid": "685def34d0a7d263c9c7c70b"}, "serverHost": "************", "serverPort": 27017}
2025-06-27 10:09:08,211 - app.database.postgresql_client - INFO - 📡 开始监听频道: partition_check
2025-06-27 10:09:08,212 - pymongo.serverSelection - DEBUG - {"message": "Server selection started", "selector": "Primary()", "operation": "ismaster", "topologyDescription": "<TopologyDescription id: 685def34d0a7d263c9c7c70b, topology_type: Unknown, servers: [<ServerDescription ('************', 27017) server_type: Unknown, rtt: None>]>", "clientId": {"$oid": "685def34d0a7d263c9c7c70b"}}
2025-06-27 10:09:08,212 - pymongo.serverSelection - DEBUG - {"message": "Waiting for suitable server to become available", "selector": "Primary()", "operation": "ismaster", "topologyDescription": "<TopologyDescription id: 685def34d0a7d263c9c7c70b, topology_type: Unknown, servers: [<ServerDescription ('************', 27017) server_type: Unknown, rtt: None>]>", "clientId": {"$oid": "685def34d0a7d263c9c7c70b"}, "remainingTimeMS": 4999}
2025-06-27 10:09:08,212 - pymongo.topology - DEBUG - {"message": "Server heartbeat started", "topologyId": {"$oid": "685def34d0a7d263c9c7c70b"}, "driverConnectionId": 1, "serverHost": "************", "serverPort": 27017, "awaited": false}
2025-06-27 10:09:08,212 - app.database.redis_client - INFO - Redis连接成功
2025-06-27 10:09:08,213 - app.database.postgresql_client - INFO - 📡 开始监听频道: sync_trigger
2025-06-27 10:09:08,213 - app.database.postgresql_client - ERROR - 监听通知时出错: 'Connection' object has no attribute 'connection'
2025-06-27 10:09:08,215 - pymongo.topology - DEBUG - {"message": "Server heartbeat succeeded", "topologyId": {"$oid": "685def34d0a7d263c9c7c70b"}, "driverConnectionId": 1, "serverConnectionId": 428, "serverHost": "************", "serverPort": 27017, "awaited": false, "durationMS": 2.1418669493868947, "reply": "{\"helloOk\": true, \"ismaster\": true, \"topologyVersion\": {\"processId\": {\"$oid\": \"685b97d01d8e2f809d089f93\"}}, \"maxBsonObjectSize\": 16777216, \"maxMessageSizeBytes\": 48000000, \"maxWriteBatchSize\": 100000, \"localTime\": {\"$date\": \"2025-06-27T01:09:08.252Z\"}, \"logicalSessionTimeoutMinutes\": 30, \"connectionId\": 428, \"maxWireVersion\": 25, \"ok\": 1.0}"}
2025-06-27 10:09:08,215 - pymongo.connection - DEBUG - {"message": "Connection pool ready", "clientId": {"$oid": "685def34d0a7d263c9c7c70b"}, "serverHost": "************", "serverPort": 27017}
2025-06-27 10:09:08,215 - pymongo.topology - DEBUG - {"message": "Topology description changed", "topologyId": {"$oid": "685def34d0a7d263c9c7c70b"}, "previousDescription": "<TopologyDescription id: 685def34d0a7d263c9c7c70b, topology_type: Unknown, servers: [<ServerDescription ('************', 27017) server_type: Unknown, rtt: None>]>", "newDescription": "<TopologyDescription id: 685def34d0a7d263c9c7c70b, topology_type: Single, servers: [<ServerDescription ('************', 27017) server_type: Standalone, rtt: 0.0021418669493868947>]>"}
2025-06-27 10:09:08,215 - pymongo.serverSelection - DEBUG - {"message": "Server selection succeeded", "selector": "Primary()", "operation": "ismaster", "topologyDescription": "<TopologyDescription id: 685def34d0a7d263c9c7c70b, topology_type: Single, servers: [<ServerDescription ('************', 27017) server_type: Standalone, rtt: 0.0021418669493868947>]>", "clientId": {"$oid": "685def34d0a7d263c9c7c70b"}, "serverHost": "************", "serverPort": 27017}
2025-06-27 10:09:08,215 - pymongo.topology - DEBUG - {"message": "Server heartbeat started", "topologyId": {"$oid": "685def34d0a7d263c9c7c70b"}, "driverConnectionId": 1, "serverConnectionId": 428, "serverHost": "************", "serverPort": 27017, "awaited": true}
2025-06-27 10:09:08,215 - pymongo.connection - DEBUG - {"message": "Connection checkout started", "clientId": {"$oid": "685def34d0a7d263c9c7c70b"}, "serverHost": "************", "serverPort": 27017}
2025-06-27 10:09:08,216 - pymongo.connection - DEBUG - {"message": "Connection created", "clientId": {"$oid": "685def34d0a7d263c9c7c70b"}, "serverHost": "************", "serverPort": 27017, "driverConnectionId": 1}
2025-06-27 10:09:08,217 - app.utils.server6_client - INFO - ✅ Server6连接成功: degraded
2025-06-27 10:09:08,218 - pymongo.connection - DEBUG - {"message": "Connection ready", "clientId": {"$oid": "685def34d0a7d263c9c7c70b"}, "serverHost": "************", "serverPort": 27017, "driverConnectionId": 1, "durationMS": 0.0017927639419212937}
2025-06-27 10:09:08,218 - pymongo.connection - DEBUG - {"message": "Connection checked out", "clientId": {"$oid": "685def34d0a7d263c9c7c70b"}, "serverHost": "************", "serverPort": 27017, "driverConnectionId": 1, "durationMS": 0.002755615976639092}
2025-06-27 10:09:08,218 - pymongo.command - DEBUG - {"message": "Command started", "clientId": {"$oid": "685def34d0a7d263c9c7c70b"}, "command": "{\"ismaster\": 1, \"lsid\": {\"id\": {\"$binary\": {\"base64\": \"LW3zdunEQnOi6MOxU/x9FA==\", \"subType\": \"04\"}}}, \"$db\": \"admin\"}", "commandName": "ismaster", "databaseName": "admin", "requestId": 783368690, "operationId": 783368690, "driverConnectionId": 1, "serverConnectionId": 430, "serverHost": "************", "serverPort": 27017}
2025-06-27 10:09:08,219 - pymongo.command - DEBUG - {"message": "Command succeeded", "clientId": {"$oid": "685def34d0a7d263c9c7c70b"}, "durationMS": 0.752, "reply": "{\"ismaster\": true, \"topologyVersion\": {\"processId\": {\"$oid\": \"685b97d01d8e2f809d089f93\"}}, \"maxBsonObjectSize\": 16777216, \"maxMessageSizeBytes\": 48000000, \"maxWriteBatchSize\": 100000, \"localTime\": {\"$date\": \"2025-06-27T01:09:08.256Z\"}, \"logicalSessionTimeoutMinutes\": 30, \"connectionId\": 430, \"maxWireVersion\": 25, \"ok\": 1.0}", "commandName": "ismaster", "databaseName": "admin", "requestId": 783368690, "operationId": 783368690, "driverConnectionId": 1, "serverConnectionId": 430, "serverHost": "************", "serverPort": 27017}
2025-06-27 10:09:08,219 - pymongo.connection - DEBUG - {"message": "Connection checked in", "clientId": {"$oid": "685def34d0a7d263c9c7c70b"}, "serverHost": "************", "serverPort": 27017, "driverConnectionId": 1}
2025-06-27 10:09:08,220 - pymongo.serverSelection - DEBUG - {"message": "Server selection started", "selector": "<function writable_server_selector at 0x75a7f90a36a0>", "operation": "createIndexes", "topologyDescription": "<TopologyDescription id: 685def34d0a7d263c9c7c70b, topology_type: Single, servers: [<ServerDescription ('************', 27017) server_type: Standalone, rtt: 0.0021418669493868947>]>", "clientId": {"$oid": "685def34d0a7d263c9c7c70b"}}
2025-06-27 10:09:08,220 - pymongo.serverSelection - DEBUG - {"message": "Server selection succeeded", "selector": "<function writable_server_selector at 0x75a7f90a36a0>", "operation": "createIndexes", "topologyDescription": "<TopologyDescription id: 685def34d0a7d263c9c7c70b, topology_type: Single, servers: [<ServerDescription ('************', 27017) server_type: Standalone, rtt: 0.0021418669493868947>]>", "clientId": {"$oid": "685def34d0a7d263c9c7c70b"}, "serverHost": "************", "serverPort": 27017}
2025-06-27 10:09:08,220 - pymongo.connection - DEBUG - {"message": "Connection checkout started", "clientId": {"$oid": "685def34d0a7d263c9c7c70b"}, "serverHost": "************", "serverPort": 27017}
2025-06-27 10:09:08,220 - pymongo.connection - DEBUG - {"message": "Connection checked out", "clientId": {"$oid": "685def34d0a7d263c9c7c70b"}, "serverHost": "************", "serverPort": 27017, "driverConnectionId": 1, "durationMS": 8.79139406606555e-05}
2025-06-27 10:09:08,220 - pymongo.command - DEBUG - {"message": "Command started", "clientId": {"$oid": "685def34d0a7d263c9c7c70b"}, "command": "{\"createIndexes\": \"sync_logs\", \"indexes\": [{\"name\": \"timestamp_-1_operation_type_1_employee_id_1\", \"key\": {\"timestamp\": -1, \"operation_type\": 1, \"employee_id\": 1}}], \"lsid\": {\"id\": {\"$binary\": {\"base64\": \"LW3zdunEQnOi6MOxU/x9FA==\", \"subType\": \"04\"}}}, \"$db\": \"server5_logs\"}", "commandName": "createIndexes", "databaseName": "server5_logs", "requestId": 1102520059, "operationId": 1102520059, "driverConnectionId": 1, "serverConnectionId": 430, "serverHost": "************", "serverPort": 27017}
2025-06-27 10:09:08,221 - pymongo.command - DEBUG - {"message": "Command succeeded", "clientId": {"$oid": "685def34d0a7d263c9c7c70b"}, "durationMS": 0.868, "reply": "{\"numIndexesBefore\": 2, \"numIndexesAfter\": 2, \"note\": \"all indexes already exist\", \"ok\": 1.0}", "commandName": "createIndexes", "databaseName": "server5_logs", "requestId": 1102520059, "operationId": 1102520059, "driverConnectionId": 1, "serverConnectionId": 430, "serverHost": "************", "serverPort": 27017}
2025-06-27 10:09:08,221 - pymongo.connection - DEBUG - {"message": "Connection checked in", "clientId": {"$oid": "685def34d0a7d263c9c7c70b"}, "serverHost": "************", "serverPort": 27017, "driverConnectionId": 1}
2025-06-27 10:09:08,221 - pymongo.serverSelection - DEBUG - {"message": "Server selection started", "selector": "<function writable_server_selector at 0x75a7f90a36a0>", "operation": "createIndexes", "topologyDescription": "<TopologyDescription id: 685def34d0a7d263c9c7c70b, topology_type: Single, servers: [<ServerDescription ('************', 27017) server_type: Standalone, rtt: 0.0021418669493868947>]>", "clientId": {"$oid": "685def34d0a7d263c9c7c70b"}}
2025-06-27 10:09:08,221 - pymongo.serverSelection - DEBUG - {"message": "Server selection succeeded", "selector": "<function writable_server_selector at 0x75a7f90a36a0>", "operation": "createIndexes", "topologyDescription": "<TopologyDescription id: 685def34d0a7d263c9c7c70b, topology_type: Single, servers: [<ServerDescription ('************', 27017) server_type: Standalone, rtt: 0.0021418669493868947>]>", "clientId": {"$oid": "685def34d0a7d263c9c7c70b"}, "serverHost": "************", "serverPort": 27017}
2025-06-27 10:09:08,221 - pymongo.connection - DEBUG - {"message": "Connection checkout started", "clientId": {"$oid": "685def34d0a7d263c9c7c70b"}, "serverHost": "************", "serverPort": 27017}
2025-06-27 10:09:08,221 - pymongo.connection - DEBUG - {"message": "Connection checked out", "clientId": {"$oid": "685def34d0a7d263c9c7c70b"}, "serverHost": "************", "serverPort": 27017, "driverConnectionId": 1, "durationMS": 8.418294601142406e-05}
2025-06-27 10:09:08,222 - pymongo.command - DEBUG - {"message": "Command started", "clientId": {"$oid": "685def34d0a7d263c9c7c70b"}, "command": "{\"createIndexes\": \"error_logs\", \"indexes\": [{\"name\": \"timestamp_-1_error_type_1\", \"key\": {\"timestamp\": -1, \"error_type\": 1}}], \"lsid\": {\"id\": {\"$binary\": {\"base64\": \"LW3zdunEQnOi6MOxU/x9FA==\", \"subType\": \"04\"}}}, \"$db\": \"server5_logs\"}", "commandName": "createIndexes", "databaseName": "server5_logs", "requestId": 2044897763, "operationId": 2044897763, "driverConnectionId": 1, "serverConnectionId": 430, "serverHost": "************", "serverPort": 27017}
2025-06-27 10:09:08,222 - pymongo.command - DEBUG - {"message": "Command succeeded", "clientId": {"$oid": "685def34d0a7d263c9c7c70b"}, "durationMS": 0.744, "reply": "{\"numIndexesBefore\": 2, \"numIndexesAfter\": 2, \"note\": \"all indexes already exist\", \"ok\": 1.0}", "commandName": "createIndexes", "databaseName": "server5_logs", "requestId": 2044897763, "operationId": 2044897763, "driverConnectionId": 1, "serverConnectionId": 430, "serverHost": "************", "serverPort": 27017}
2025-06-27 10:09:08,222 - pymongo.connection - DEBUG - {"message": "Connection checked in", "clientId": {"$oid": "685def34d0a7d263c9c7c70b"}, "serverHost": "************", "serverPort": 27017, "driverConnectionId": 1}
2025-06-27 10:09:08,223 - pymongo.serverSelection - DEBUG - {"message": "Server selection started", "selector": "<function writable_server_selector at 0x75a7f90a36a0>", "operation": "createIndexes", "topologyDescription": "<TopologyDescription id: 685def34d0a7d263c9c7c70b, topology_type: Single, servers: [<ServerDescription ('************', 27017) server_type: Standalone, rtt: 0.0021418669493868947>]>", "clientId": {"$oid": "685def34d0a7d263c9c7c70b"}}
2025-06-27 10:09:08,223 - pymongo.serverSelection - DEBUG - {"message": "Server selection succeeded", "selector": "<function writable_server_selector at 0x75a7f90a36a0>", "operation": "createIndexes", "topologyDescription": "<TopologyDescription id: 685def34d0a7d263c9c7c70b, topology_type: Single, servers: [<ServerDescription ('************', 27017) server_type: Standalone, rtt: 0.0021418669493868947>]>", "clientId": {"$oid": "685def34d0a7d263c9c7c70b"}, "serverHost": "************", "serverPort": 27017}
2025-06-27 10:09:08,223 - pymongo.connection - DEBUG - {"message": "Connection checkout started", "clientId": {"$oid": "685def34d0a7d263c9c7c70b"}, "serverHost": "************", "serverPort": 27017}
2025-06-27 10:09:08,223 - pymongo.connection - DEBUG - {"message": "Connection checked out", "clientId": {"$oid": "685def34d0a7d263c9c7c70b"}, "serverHost": "************", "serverPort": 27017, "driverConnectionId": 1, "durationMS": 8.020491804927588e-05}
2025-06-27 10:09:08,223 - pymongo.command - DEBUG - {"message": "Command started", "clientId": {"$oid": "685def34d0a7d263c9c7c70b"}, "command": "{\"createIndexes\": \"performance_metrics\", \"indexes\": [{\"name\": \"timestamp_-1_metric_type_1\", \"key\": {\"timestamp\": -1, \"metric_type\": 1}}], \"lsid\": {\"id\": {\"$binary\": {\"base64\": \"LW3zdunEQnOi6MOxU/x9FA==\", \"subType\": \"04\"}}}, \"$db\": \"server5_logs\"}", "commandName": "createIndexes", "databaseName": "server5_logs", "requestId": 1967513926, "operationId": 1967513926, "driverConnectionId": 1, "serverConnectionId": 430, "serverHost": "************", "serverPort": 27017}
2025-06-27 10:09:08,224 - pymongo.command - DEBUG - {"message": "Command succeeded", "clientId": {"$oid": "685def34d0a7d263c9c7c70b"}, "durationMS": 0.723, "reply": "{\"numIndexesBefore\": 2, \"numIndexesAfter\": 2, \"note\": \"all indexes already exist\", \"ok\": 1.0}", "commandName": "createIndexes", "databaseName": "server5_logs", "requestId": 1967513926, "operationId": 1967513926, "driverConnectionId": 1, "serverConnectionId": 430, "serverHost": "************", "serverPort": 27017}
2025-06-27 10:09:08,224 - pymongo.connection - DEBUG - {"message": "Connection checked in", "clientId": {"$oid": "685def34d0a7d263c9c7c70b"}, "serverHost": "************", "serverPort": 27017, "driverConnectionId": 1}
2025-06-27 10:09:08,224 - app.database.mongodb_client - DEBUG - 📋 MongoDB索引创建完成
2025-06-27 10:09:08,224 - app.database.mongodb_client - INFO - ✅ MongoDB连接成功
2025-06-27 10:09:08,226 - app.database.postgresql_client - ERROR - 命令执行失败: 
                CREATE TABLE IF NOT EXISTS entries_202506 
                PARTITION OF entries
   ... 错误: パーティション"entries_202506"はパーティション"entries_2025_06"と重複があります
2025-06-27 10:09:08,227 - app.database.postgresql_client - ERROR - ❌ 分区创建失败 202506: パーティション"entries_202506"はパーティション"entries_2025_06"と重複があります
2025-06-27 10:09:08,227 - app.services.f1_listener - ERROR - ❌ 创建分区失败 202506: パーティション"entries_202506"はパーティション"entries_2025_06"と重複があります
2025-06-27 10:09:08,228 - pymongo.serverSelection - DEBUG - {"message": "Server selection started", "selector": "<function writable_server_selector at 0x75a7f90a36a0>", "operation": "insert", "topologyDescription": "<TopologyDescription id: 685def33d0a7d263c9c7c70a, topology_type: Single, servers: [<ServerDescription ('************', 27017) server_type: Standalone, rtt: 0.0018384819850325584>]>", "clientId": {"$oid": "685def33d0a7d263c9c7c70a"}}
2025-06-27 10:09:08,228 - pymongo.serverSelection - DEBUG - {"message": "Server selection succeeded", "selector": "<function writable_server_selector at 0x75a7f90a36a0>", "operation": "insert", "topologyDescription": "<TopologyDescription id: 685def33d0a7d263c9c7c70a, topology_type: Single, servers: [<ServerDescription ('************', 27017) server_type: Standalone, rtt: 0.0018384819850325584>]>", "clientId": {"$oid": "685def33d0a7d263c9c7c70a"}, "serverHost": "************", "serverPort": 27017}
2025-06-27 10:09:08,228 - pymongo.connection - DEBUG - {"message": "Connection checkout started", "clientId": {"$oid": "685def33d0a7d263c9c7c70a"}, "serverHost": "************", "serverPort": 27017}
2025-06-27 10:09:08,228 - pymongo.connection - DEBUG - {"message": "Connection checked out", "clientId": {"$oid": "685def33d0a7d263c9c7c70a"}, "serverHost": "************", "serverPort": 27017, "driverConnectionId": 1, "durationMS": 8.380296640098095e-05}
2025-06-27 10:09:08,228 - pymongo.command - DEBUG - {"message": "Command started", "clientId": {"$oid": "685def33d0a7d263c9c7c70a"}, "command": "{\"insert\": \"error_logs\", \"ordered\": true, \"lsid\": {\"id\": {\"$binary\": {\"base64\": \"NgisYBkSRT2JsBAw71iCrQ==\", \"subType\": \"04\"}}}, \"$db\": \"server5_logs\", \"documents\": [{\"timestamp\": {\"$date\": \"2025-06-27T01:09:08.228Z\"}, \"error_type\": \"partition_creation_failed\", \"error_message\": \"\\u30d1\\u30fc\\u30c6\\u30a3\\u30b7\\u30e7\\u30f3\\\"entries_202506\\\"\\u306f\\u30d1\\u30fc\\u30c6\\u30a3\\u30b7\\u30e7\\u30f3\\\"entries_2025_06\\\"\\u3068\\u91cd\\u8907\\u304c\\u3042\\u308a\\u307e\\u3059\", \"context\": {\"month_code\": \"202506\"}, \"severity\": \"error\", \"function_name\": \"_create_partition_if_needed\", \"_id\": {\"$oid\": \"685def34d0a7d263c9c7c70c\"}}]}", "commandName": "insert", "databaseName": "server5_logs", "requestId": 1365180540, "operationId": 1365180540, "driverConnectionId": 1, "serverConnectionId": 427, "serverHost": "************", "serverPort": 27017}
2025-06-27 10:09:08,229 - pymongo.command - DEBUG - {"message": "Command succeeded", "clientId": {"$oid": "685def33d0a7d263c9c7c70a"}, "durationMS": 1.012, "reply": "{\"n\": 1, \"ok\": 1.0}", "commandName": "insert", "databaseName": "server5_logs", "requestId": 1365180540, "operationId": 1365180540, "driverConnectionId": 1, "serverConnectionId": 427, "serverHost": "************", "serverPort": 27017}
2025-06-27 10:09:08,229 - pymongo.connection - DEBUG - {"message": "Connection checked in", "clientId": {"$oid": "685def33d0a7d263c9c7c70a"}, "serverHost": "************", "serverPort": 27017, "driverConnectionId": 1}
2025-06-27 10:09:08,229 - app.database.mongodb_client - DEBUG - 🚨 错误日志记录成功: partition_creation_failed
2025-06-27 10:09:08,230 - app.database.postgresql_client - DEBUG - 命令执行成功: CREATE TABLE
2025-06-27 10:09:08,231 - app.database.postgresql_client - INFO - ✅ 分区创建成功: entries_202507
2025-06-27 10:09:08,231 - pymongo.serverSelection - DEBUG - {"message": "Server selection started", "selector": "<function writable_server_selector at 0x75a7f90a36a0>", "operation": "insert", "topologyDescription": "<TopologyDescription id: 685def33d0a7d263c9c7c70a, topology_type: Single, servers: [<ServerDescription ('************', 27017) server_type: Standalone, rtt: 0.0018384819850325584>]>", "clientId": {"$oid": "685def33d0a7d263c9c7c70a"}}
2025-06-27 10:09:08,232 - pymongo.serverSelection - DEBUG - {"message": "Server selection succeeded", "selector": "<function writable_server_selector at 0x75a7f90a36a0>", "operation": "insert", "topologyDescription": "<TopologyDescription id: 685def33d0a7d263c9c7c70a, topology_type: Single, servers: [<ServerDescription ('************', 27017) server_type: Standalone, rtt: 0.0018384819850325584>]>", "clientId": {"$oid": "685def33d0a7d263c9c7c70a"}, "serverHost": "************", "serverPort": 27017}
2025-06-27 10:09:08,232 - pymongo.connection - DEBUG - {"message": "Connection checkout started", "clientId": {"$oid": "685def33d0a7d263c9c7c70a"}, "serverHost": "************", "serverPort": 27017}
2025-06-27 10:09:08,232 - pymongo.connection - DEBUG - {"message": "Connection checked out", "clientId": {"$oid": "685def33d0a7d263c9c7c70a"}, "serverHost": "************", "serverPort": 27017, "driverConnectionId": 1, "durationMS": 7.497600745409727e-05}
2025-06-27 10:09:08,232 - pymongo.command - DEBUG - {"message": "Command started", "clientId": {"$oid": "685def33d0a7d263c9c7c70a"}, "command": "{\"insert\": \"sync_logs\", \"ordered\": true, \"lsid\": {\"id\": {\"$binary\": {\"base64\": \"NgisYBkSRT2JsBAw71iCrQ==\", \"subType\": \"04\"}}}, \"$db\": \"server5_logs\", \"documents\": [{\"timestamp\": {\"$date\": \"2025-06-27T01:09:08.231Z\"}, \"operation_type\": \"partition_create\", \"status\": \"success\", \"metadata\": {\"month_code\": \"202507\"}, \"_id\": {\"$oid\": \"685def34d0a7d263c9c7c70d\"}}]}", "commandName": "insert", "databaseName": "server5_logs", "requestId": 1540383426, "operationId": 1540383426, "driverConnectionId": 1, "serverConnectionId": 427, "serverHost": "************", "serverPort": 27017}
2025-06-27 10:09:08,233 - pymongo.command - DEBUG - {"message": "Command succeeded", "clientId": {"$oid": "685def33d0a7d263c9c7c70a"}, "durationMS": 0.9039999999999999, "reply": "{\"n\": 1, \"ok\": 1.0}", "commandName": "insert", "databaseName": "server5_logs", "requestId": 1540383426, "operationId": 1540383426, "driverConnectionId": 1, "serverConnectionId": 427, "serverHost": "************", "serverPort": 27017}
2025-06-27 10:09:08,233 - pymongo.connection - DEBUG - {"message": "Connection checked in", "clientId": {"$oid": "685def33d0a7d263c9c7c70a"}, "serverHost": "************", "serverPort": 27017, "driverConnectionId": 1}
2025-06-27 10:09:08,233 - app.database.mongodb_client - DEBUG - 📝 同步日志记录成功: partition_create
2025-06-27 10:09:08,233 - app.services.f1_listener - DEBUG - 📅 月度分区检查完成
2025-06-27 10:09:08,411 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, '/home/<USER>/Documents/testF/2025/ser25/server0613/MySuiteDatebase2402newuigood-yolo5-3/server5/logs/server5.log')}
2025-06-27 10:09:08,440 - app.database.postgresql_client - INFO - ✅ PostgreSQL连接池创建成功
2025-06-27 10:09:08,440 - app.services.f2_push_writer - INFO - ✅ f2推送回写服务启动成功，工作线程数: 4
2025-06-27 10:09:08,440 - app.services.f2_push_writer - INFO - 🚀 推送工作线程启动: worker_1
2025-06-27 10:09:08,440 - app.services.f2_push_writer - INFO - 🚀 推送工作线程启动: worker_2
2025-06-27 10:09:08,440 - app.services.f2_push_writer - INFO - 🚀 推送工作线程启动: worker_3
2025-06-27 10:09:08,440 - app.services.f2_push_writer - INFO - 🚀 推送工作线程启动: worker_4
2025-06-27 10:09:08,442 - app.main - INFO - f2_push_writer 启动成功
2025-06-27 10:09:08,442 - app.main - INFO - 启动 f3_data_puller...
2025-06-27 10:09:08,443 - pymongo.topology - DEBUG - {"message": "Starting topology monitoring", "topologyId": {"$oid": "685def34d0a7d263c9c7c70e"}}
2025-06-27 10:09:08,443 - pymongo.topology - DEBUG - {"message": "Topology description changed", "topologyId": {"$oid": "685def34d0a7d263c9c7c70e"}, "previousDescription": "<TopologyDescription id: 685def34d0a7d263c9c7c70e, topology_type: Unknown, servers: []>", "newDescription": "<TopologyDescription id: 685def34d0a7d263c9c7c70e, topology_type: Unknown, servers: [<ServerDescription ('************', 27017) server_type: Unknown, rtt: None>]>"}
2025-06-27 10:09:08,443 - pymongo.topology - DEBUG - {"message": "Starting server monitoring", "topologyId": {"$oid": "685def34d0a7d263c9c7c70e"}, "serverHost": "************", "serverPort": 27017}
2025-06-27 10:09:08,443 - pymongo.connection - DEBUG - {"message": "Connection pool created", "clientId": {"$oid": "685def34d0a7d263c9c7c70e"}, "serverHost": "************", "serverPort": 27017}
2025-06-27 10:09:08,444 - pymongo.serverSelection - DEBUG - {"message": "Server selection started", "selector": "Primary()", "operation": "ismaster", "topologyDescription": "<TopologyDescription id: 685def34d0a7d263c9c7c70e, topology_type: Unknown, servers: [<ServerDescription ('************', 27017) server_type: Unknown, rtt: None>]>", "clientId": {"$oid": "685def34d0a7d263c9c7c70e"}}
2025-06-27 10:09:08,444 - pymongo.serverSelection - DEBUG - {"message": "Waiting for suitable server to become available", "selector": "Primary()", "operation": "ismaster", "topologyDescription": "<TopologyDescription id: 685def34d0a7d263c9c7c70e, topology_type: Unknown, servers: [<ServerDescription ('************', 27017) server_type: Unknown, rtt: None>]>", "clientId": {"$oid": "685def34d0a7d263c9c7c70e"}, "remainingTimeMS": 4999}
2025-06-27 10:09:08,444 - app.database.redis_client - INFO - Redis连接成功
2025-06-27 10:09:08,445 - pymongo.topology - DEBUG - {"message": "Server heartbeat started", "topologyId": {"$oid": "685def34d0a7d263c9c7c70e"}, "driverConnectionId": 1, "serverHost": "************", "serverPort": 27017, "awaited": false}
2025-06-27 10:09:08,447 - pymongo.topology - DEBUG - {"message": "Server heartbeat succeeded", "topologyId": {"$oid": "685def34d0a7d263c9c7c70e"}, "driverConnectionId": 1, "serverConnectionId": 431, "serverHost": "************", "serverPort": 27017, "awaited": false, "durationMS": 1.786396955139935, "reply": "{\"helloOk\": true, \"ismaster\": true, \"topologyVersion\": {\"processId\": {\"$oid\": \"685b97d01d8e2f809d089f93\"}}, \"maxBsonObjectSize\": 16777216, \"maxMessageSizeBytes\": 48000000, \"maxWriteBatchSize\": 100000, \"localTime\": {\"$date\": \"2025-06-27T01:09:08.484Z\"}, \"logicalSessionTimeoutMinutes\": 30, \"connectionId\": 431, \"maxWireVersion\": 25, \"ok\": 1.0}"}
2025-06-27 10:09:08,447 - pymongo.connection - DEBUG - {"message": "Connection pool ready", "clientId": {"$oid": "685def34d0a7d263c9c7c70e"}, "serverHost": "************", "serverPort": 27017}
2025-06-27 10:09:08,447 - pymongo.topology - DEBUG - {"message": "Topology description changed", "topologyId": {"$oid": "685def34d0a7d263c9c7c70e"}, "previousDescription": "<TopologyDescription id: 685def34d0a7d263c9c7c70e, topology_type: Unknown, servers: [<ServerDescription ('************', 27017) server_type: Unknown, rtt: None>]>", "newDescription": "<TopologyDescription id: 685def34d0a7d263c9c7c70e, topology_type: Single, servers: [<ServerDescription ('************', 27017) server_type: Standalone, rtt: 0.001786396955139935>]>"}
2025-06-27 10:09:08,447 - pymongo.serverSelection - DEBUG - {"message": "Server selection succeeded", "selector": "Primary()", "operation": "ismaster", "topologyDescription": "<TopologyDescription id: 685def34d0a7d263c9c7c70e, topology_type: Single, servers: [<ServerDescription ('************', 27017) server_type: Standalone, rtt: 0.001786396955139935>]>", "clientId": {"$oid": "685def34d0a7d263c9c7c70e"}, "serverHost": "************", "serverPort": 27017}
2025-06-27 10:09:08,447 - pymongo.topology - DEBUG - {"message": "Server heartbeat started", "topologyId": {"$oid": "685def34d0a7d263c9c7c70e"}, "driverConnectionId": 1, "serverConnectionId": 431, "serverHost": "************", "serverPort": 27017, "awaited": true}
2025-06-27 10:09:08,447 - pymongo.connection - DEBUG - {"message": "Connection checkout started", "clientId": {"$oid": "685def34d0a7d263c9c7c70e"}, "serverHost": "************", "serverPort": 27017}
2025-06-27 10:09:08,447 - pymongo.connection - DEBUG - {"message": "Connection created", "clientId": {"$oid": "685def34d0a7d263c9c7c70e"}, "serverHost": "************", "serverPort": 27017, "driverConnectionId": 1}
2025-06-27 10:09:08,448 - app.utils.server6_client - INFO - ✅ Server6连接成功: degraded
2025-06-27 10:09:08,450 - pymongo.connection - DEBUG - {"message": "Connection ready", "clientId": {"$oid": "685def34d0a7d263c9c7c70e"}, "serverHost": "************", "serverPort": 27017, "driverConnectionId": 1, "durationMS": 0.0014732879353687167}
2025-06-27 10:09:08,450 - pymongo.connection - DEBUG - {"message": "Connection checked out", "clientId": {"$oid": "685def34d0a7d263c9c7c70e"}, "serverHost": "************", "serverPort": 27017, "driverConnectionId": 1, "durationMS": 0.0026600159471854568}
2025-06-27 10:09:08,450 - pymongo.command - DEBUG - {"message": "Command started", "clientId": {"$oid": "685def34d0a7d263c9c7c70e"}, "command": "{\"ismaster\": 1, \"lsid\": {\"id\": {\"$binary\": {\"base64\": \"3693e4GATUqghxuW5frNIA==\", \"subType\": \"04\"}}}, \"$db\": \"admin\"}", "commandName": "ismaster", "databaseName": "admin", "requestId": 294702567, "operationId": 294702567, "driverConnectionId": 1, "serverConnectionId": 433, "serverHost": "************", "serverPort": 27017}
2025-06-27 10:09:08,451 - pymongo.command - DEBUG - {"message": "Command succeeded", "clientId": {"$oid": "685def34d0a7d263c9c7c70e"}, "durationMS": 0.7010000000000001, "reply": "{\"ismaster\": true, \"topologyVersion\": {\"processId\": {\"$oid\": \"685b97d01d8e2f809d089f93\"}}, \"maxBsonObjectSize\": 16777216, \"maxMessageSizeBytes\": 48000000, \"maxWriteBatchSize\": 100000, \"localTime\": {\"$date\": \"2025-06-27T01:09:08.488Z\"}, \"logicalSessionTimeoutMinutes\": 30, \"connectionId\": 433, \"maxWireVersion\": 25, \"ok\": 1.0}", "commandName": "ismaster", "databaseName": "admin", "requestId": 294702567, "operationId": 294702567, "driverConnectionId": 1, "serverConnectionId": 433, "serverHost": "************", "serverPort": 27017}
2025-06-27 10:09:08,451 - pymongo.connection - DEBUG - {"message": "Connection checked in", "clientId": {"$oid": "685def34d0a7d263c9c7c70e"}, "serverHost": "************", "serverPort": 27017, "driverConnectionId": 1}
2025-06-27 10:09:08,451 - pymongo.serverSelection - DEBUG - {"message": "Server selection started", "selector": "<function writable_server_selector at 0x75a7f90a36a0>", "operation": "createIndexes", "topologyDescription": "<TopologyDescription id: 685def34d0a7d263c9c7c70e, topology_type: Single, servers: [<ServerDescription ('************', 27017) server_type: Standalone, rtt: 0.001786396955139935>]>", "clientId": {"$oid": "685def34d0a7d263c9c7c70e"}}
2025-06-27 10:09:08,451 - pymongo.serverSelection - DEBUG - {"message": "Server selection succeeded", "selector": "<function writable_server_selector at 0x75a7f90a36a0>", "operation": "createIndexes", "topologyDescription": "<TopologyDescription id: 685def34d0a7d263c9c7c70e, topology_type: Single, servers: [<ServerDescription ('************', 27017) server_type: Standalone, rtt: 0.001786396955139935>]>", "clientId": {"$oid": "685def34d0a7d263c9c7c70e"}, "serverHost": "************", "serverPort": 27017}
2025-06-27 10:09:08,451 - pymongo.connection - DEBUG - {"message": "Connection checkout started", "clientId": {"$oid": "685def34d0a7d263c9c7c70e"}, "serverHost": "************", "serverPort": 27017}
2025-06-27 10:09:08,451 - pymongo.connection - DEBUG - {"message": "Connection checked out", "clientId": {"$oid": "685def34d0a7d263c9c7c70e"}, "serverHost": "************", "serverPort": 27017, "driverConnectionId": 1, "durationMS": 7.27709848433733e-05}
2025-06-27 10:09:08,452 - pymongo.command - DEBUG - {"message": "Command started", "clientId": {"$oid": "685def34d0a7d263c9c7c70e"}, "command": "{\"createIndexes\": \"sync_logs\", \"indexes\": [{\"name\": \"timestamp_-1_operation_type_1_employee_id_1\", \"key\": {\"timestamp\": -1, \"operation_type\": 1, \"employee_id\": 1}}], \"lsid\": {\"id\": {\"$binary\": {\"base64\": \"3693e4GATUqghxuW5frNIA==\", \"subType\": \"04\"}}}, \"$db\": \"server5_logs\"}", "commandName": "createIndexes", "databaseName": "server5_logs", "requestId": 1726956429, "operationId": 1726956429, "driverConnectionId": 1, "serverConnectionId": 433, "serverHost": "************", "serverPort": 27017}
2025-06-27 10:09:08,452 - pymongo.command - DEBUG - {"message": "Command succeeded", "clientId": {"$oid": "685def34d0a7d263c9c7c70e"}, "durationMS": 0.803, "reply": "{\"numIndexesBefore\": 2, \"numIndexesAfter\": 2, \"note\": \"all indexes already exist\", \"ok\": 1.0}", "commandName": "createIndexes", "databaseName": "server5_logs", "requestId": 1726956429, "operationId": 1726956429, "driverConnectionId": 1, "serverConnectionId": 433, "serverHost": "************", "serverPort": 27017}
2025-06-27 10:09:08,452 - pymongo.connection - DEBUG - {"message": "Connection checked in", "clientId": {"$oid": "685def34d0a7d263c9c7c70e"}, "serverHost": "************", "serverPort": 27017, "driverConnectionId": 1}
2025-06-27 10:09:08,453 - pymongo.serverSelection - DEBUG - {"message": "Server selection started", "selector": "<function writable_server_selector at 0x75a7f90a36a0>", "operation": "createIndexes", "topologyDescription": "<TopologyDescription id: 685def34d0a7d263c9c7c70e, topology_type: Single, servers: [<ServerDescription ('************', 27017) server_type: Standalone, rtt: 0.001786396955139935>]>", "clientId": {"$oid": "685def34d0a7d263c9c7c70e"}}
2025-06-27 10:09:08,453 - pymongo.serverSelection - DEBUG - {"message": "Server selection succeeded", "selector": "<function writable_server_selector at 0x75a7f90a36a0>", "operation": "createIndexes", "topologyDescription": "<TopologyDescription id: 685def34d0a7d263c9c7c70e, topology_type: Single, servers: [<ServerDescription ('************', 27017) server_type: Standalone, rtt: 0.001786396955139935>]>", "clientId": {"$oid": "685def34d0a7d263c9c7c70e"}, "serverHost": "************", "serverPort": 27017}
2025-06-27 10:09:08,453 - pymongo.connection - DEBUG - {"message": "Connection checkout started", "clientId": {"$oid": "685def34d0a7d263c9c7c70e"}, "serverHost": "************", "serverPort": 27017}
2025-06-27 10:09:08,453 - pymongo.connection - DEBUG - {"message": "Connection checked out", "clientId": {"$oid": "685def34d0a7d263c9c7c70e"}, "serverHost": "************", "serverPort": 27017, "driverConnectionId": 1, "durationMS": 7.143698167055845e-05}
2025-06-27 10:09:08,453 - pymongo.command - DEBUG - {"message": "Command started", "clientId": {"$oid": "685def34d0a7d263c9c7c70e"}, "command": "{\"createIndexes\": \"error_logs\", \"indexes\": [{\"name\": \"timestamp_-1_error_type_1\", \"key\": {\"timestamp\": -1, \"error_type\": 1}}], \"lsid\": {\"id\": {\"$binary\": {\"base64\": \"3693e4GATUqghxuW5frNIA==\", \"subType\": \"04\"}}}, \"$db\": \"server5_logs\"}", "commandName": "createIndexes", "databaseName": "server5_logs", "requestId": 336465782, "operationId": 336465782, "driverConnectionId": 1, "serverConnectionId": 433, "serverHost": "************", "serverPort": 27017}
2025-06-27 10:09:08,454 - pymongo.command - DEBUG - {"message": "Command succeeded", "clientId": {"$oid": "685def34d0a7d263c9c7c70e"}, "durationMS": 0.6920000000000001, "reply": "{\"numIndexesBefore\": 2, \"numIndexesAfter\": 2, \"note\": \"all indexes already exist\", \"ok\": 1.0}", "commandName": "createIndexes", "databaseName": "server5_logs", "requestId": 336465782, "operationId": 336465782, "driverConnectionId": 1, "serverConnectionId": 433, "serverHost": "************", "serverPort": 27017}
2025-06-27 10:09:08,454 - pymongo.connection - DEBUG - {"message": "Connection checked in", "clientId": {"$oid": "685def34d0a7d263c9c7c70e"}, "serverHost": "************", "serverPort": 27017, "driverConnectionId": 1}
2025-06-27 10:09:08,454 - pymongo.serverSelection - DEBUG - {"message": "Server selection started", "selector": "<function writable_server_selector at 0x75a7f90a36a0>", "operation": "createIndexes", "topologyDescription": "<TopologyDescription id: 685def34d0a7d263c9c7c70e, topology_type: Single, servers: [<ServerDescription ('************', 27017) server_type: Standalone, rtt: 0.001786396955139935>]>", "clientId": {"$oid": "685def34d0a7d263c9c7c70e"}}
2025-06-27 10:09:08,454 - pymongo.serverSelection - DEBUG - {"message": "Server selection succeeded", "selector": "<function writable_server_selector at 0x75a7f90a36a0>", "operation": "createIndexes", "topologyDescription": "<TopologyDescription id: 685def34d0a7d263c9c7c70e, topology_type: Single, servers: [<ServerDescription ('************', 27017) server_type: Standalone, rtt: 0.001786396955139935>]>", "clientId": {"$oid": "685def34d0a7d263c9c7c70e"}, "serverHost": "************", "serverPort": 27017}
2025-06-27 10:09:08,454 - pymongo.connection - DEBUG - {"message": "Connection checkout started", "clientId": {"$oid": "685def34d0a7d263c9c7c70e"}, "serverHost": "************", "serverPort": 27017}
2025-06-27 10:09:08,454 - pymongo.connection - DEBUG - {"message": "Connection checked out", "clientId": {"$oid": "685def34d0a7d263c9c7c70e"}, "serverHost": "************", "serverPort": 27017, "driverConnectionId": 1, "durationMS": 7.080100476741791e-05}
2025-06-27 10:09:08,454 - pymongo.command - DEBUG - {"message": "Command started", "clientId": {"$oid": "685def34d0a7d263c9c7c70e"}, "command": "{\"createIndexes\": \"performance_metrics\", \"indexes\": [{\"name\": \"timestamp_-1_metric_type_1\", \"key\": {\"timestamp\": -1, \"metric_type\": 1}}], \"lsid\": {\"id\": {\"$binary\": {\"base64\": \"3693e4GATUqghxuW5frNIA==\", \"subType\": \"04\"}}}, \"$db\": \"server5_logs\"}", "commandName": "createIndexes", "databaseName": "server5_logs", "requestId": 861021530, "operationId": 861021530, "driverConnectionId": 1, "serverConnectionId": 433, "serverHost": "************", "serverPort": 27017}
2025-06-27 10:09:08,455 - pymongo.command - DEBUG - {"message": "Command succeeded", "clientId": {"$oid": "685def34d0a7d263c9c7c70e"}, "durationMS": 0.7649999999999999, "reply": "{\"numIndexesBefore\": 2, \"numIndexesAfter\": 2, \"note\": \"all indexes already exist\", \"ok\": 1.0}", "commandName": "createIndexes", "databaseName": "server5_logs", "requestId": 861021530, "operationId": 861021530, "driverConnectionId": 1, "serverConnectionId": 433, "serverHost": "************", "serverPort": 27017}
2025-06-27 10:09:08,455 - pymongo.connection - DEBUG - {"message": "Connection checked in", "clientId": {"$oid": "685def34d0a7d263c9c7c70e"}, "serverHost": "************", "serverPort": 27017, "driverConnectionId": 1}
2025-06-27 10:09:08,456 - app.database.mongodb_client - DEBUG - 📋 MongoDB索引创建完成
2025-06-27 10:09:08,456 - app.database.mongodb_client - INFO - ✅ MongoDB连接成功
2025-06-27 10:09:08,671 - app.database.postgresql_client - INFO - ✅ PostgreSQL连接池创建成功
2025-06-27 10:09:08,671 - app.services.f3_data_puller - ERROR - ❌ f3数据拉取服务启动失败: 'RedisClient' object has no attribute 'get_last_pull_time'
2025-06-27 10:09:08,672 - pymongo.serverSelection - DEBUG - {"message": "Server selection started", "selector": "<function writable_server_selector at 0x75a7f90a36a0>", "operation": "insert", "topologyDescription": "<TopologyDescription id: 685def34d0a7d263c9c7c70e, topology_type: Single, servers: [<ServerDescription ('************', 27017) server_type: Standalone, rtt: 0.001786396955139935>]>", "clientId": {"$oid": "685def34d0a7d263c9c7c70e"}}
2025-06-27 10:09:08,672 - pymongo.serverSelection - DEBUG - {"message": "Server selection succeeded", "selector": "<function writable_server_selector at 0x75a7f90a36a0>", "operation": "insert", "topologyDescription": "<TopologyDescription id: 685def34d0a7d263c9c7c70e, topology_type: Single, servers: [<ServerDescription ('************', 27017) server_type: Standalone, rtt: 0.001786396955139935>]>", "clientId": {"$oid": "685def34d0a7d263c9c7c70e"}, "serverHost": "************", "serverPort": 27017}
2025-06-27 10:09:08,672 - pymongo.connection - DEBUG - {"message": "Connection checkout started", "clientId": {"$oid": "685def34d0a7d263c9c7c70e"}, "serverHost": "************", "serverPort": 27017}
2025-06-27 10:09:08,672 - pymongo.connection - DEBUG - {"message": "Connection checked out", "clientId": {"$oid": "685def34d0a7d263c9c7c70e"}, "serverHost": "************", "serverPort": 27017, "driverConnectionId": 1, "durationMS": 7.926893886178732e-05}
2025-06-27 10:09:08,672 - pymongo.command - DEBUG - {"message": "Command started", "clientId": {"$oid": "685def34d0a7d263c9c7c70e"}, "command": "{\"insert\": \"error_logs\", \"ordered\": true, \"lsid\": {\"id\": {\"$binary\": {\"base64\": \"3693e4GATUqghxuW5frNIA==\", \"subType\": \"04\"}}}, \"$db\": \"server5_logs\", \"documents\": [{\"timestamp\": {\"$date\": \"2025-06-27T01:09:08.671Z\"}, \"error_type\": \"service_start_failed\", \"error_message\": \"'RedisClient' object has no attribute 'get_last_pull_time'\", \"severity\": \"error\", \"function_name\": \"f3_data_puller\", \"_id\": {\"$oid\": \"685def34d0a7d263c9c7c70f\"}}]}", "commandName": "insert", "databaseName": "server5_logs", "requestId": 278722862, "operationId": 278722862, "driverConnectionId": 1, "serverConnectionId": 433, "serverHost": "************", "serverPort": 27017}
2025-06-27 10:09:08,673 - pymongo.command - DEBUG - {"message": "Command succeeded", "clientId": {"$oid": "685def34d0a7d263c9c7c70e"}, "durationMS": 1.159, "reply": "{\"n\": 1, \"ok\": 1.0}", "commandName": "insert", "databaseName": "server5_logs", "requestId": 278722862, "operationId": 278722862, "driverConnectionId": 1, "serverConnectionId": 433, "serverHost": "************", "serverPort": 27017}
2025-06-27 10:09:08,673 - pymongo.connection - DEBUG - {"message": "Connection checked in", "clientId": {"$oid": "685def34d0a7d263c9c7c70e"}, "serverHost": "************", "serverPort": 27017, "driverConnectionId": 1}
2025-06-27 10:09:08,674 - app.database.mongodb_client - DEBUG - 🚨 错误日志记录成功: service_start_failed
2025-06-27 10:09:08,674 - app.main - ERROR - f3_data_puller 启动失败
2025-06-27 10:09:08,674 - app.main - INFO - 启动 f4_operation_handler...
2025-06-27 10:09:08,675 - pymongo.topology - DEBUG - {"message": "Starting topology monitoring", "topologyId": {"$oid": "685def34d0a7d263c9c7c710"}}
2025-06-27 10:09:08,675 - pymongo.topology - DEBUG - {"message": "Topology description changed", "topologyId": {"$oid": "685def34d0a7d263c9c7c710"}, "previousDescription": "<TopologyDescription id: 685def34d0a7d263c9c7c710, topology_type: Unknown, servers: []>", "newDescription": "<TopologyDescription id: 685def34d0a7d263c9c7c710, topology_type: Unknown, servers: [<ServerDescription ('************', 27017) server_type: Unknown, rtt: None>]>"}
2025-06-27 10:09:08,675 - pymongo.topology - DEBUG - {"message": "Starting server monitoring", "topologyId": {"$oid": "685def34d0a7d263c9c7c710"}, "serverHost": "************", "serverPort": 27017}
2025-06-27 10:09:08,677 - pymongo.connection - DEBUG - {"message": "Connection pool created", "clientId": {"$oid": "685def34d0a7d263c9c7c710"}, "serverHost": "************", "serverPort": 27017}
2025-06-27 10:09:08,677 - app.database.redis_client - INFO - Redis连接成功
2025-06-27 10:09:08,677 - pymongo.serverSelection - DEBUG - {"message": "Server selection started", "selector": "Primary()", "operation": "ismaster", "topologyDescription": "<TopologyDescription id: 685def34d0a7d263c9c7c710, topology_type: Unknown, servers: [<ServerDescription ('************', 27017) server_type: Unknown, rtt: None>]>", "clientId": {"$oid": "685def34d0a7d263c9c7c710"}}
2025-06-27 10:09:08,677 - pymongo.serverSelection - DEBUG - {"message": "Waiting for suitable server to become available", "selector": "Primary()", "operation": "ismaster", "topologyDescription": "<TopologyDescription id: 685def34d0a7d263c9c7c710, topology_type: Unknown, servers: [<ServerDescription ('************', 27017) server_type: Unknown, rtt: None>]>", "clientId": {"$oid": "685def34d0a7d263c9c7c710"}, "remainingTimeMS": 4999}
2025-06-27 10:09:08,678 - pymongo.topology - DEBUG - {"message": "Server heartbeat started", "topologyId": {"$oid": "685def34d0a7d263c9c7c710"}, "driverConnectionId": 1, "serverHost": "************", "serverPort": 27017, "awaited": false}
2025-06-27 10:09:08,680 - pymongo.topology - DEBUG - {"message": "Server heartbeat succeeded", "topologyId": {"$oid": "685def34d0a7d263c9c7c710"}, "driverConnectionId": 1, "serverConnectionId": 434, "serverHost": "************", "serverPort": 27017, "awaited": false, "durationMS": 1.9460259936749935, "reply": "{\"helloOk\": true, \"ismaster\": true, \"topologyVersion\": {\"processId\": {\"$oid\": \"685b97d01d8e2f809d089f93\"}}, \"maxBsonObjectSize\": 16777216, \"maxMessageSizeBytes\": 48000000, \"maxWriteBatchSize\": 100000, \"localTime\": {\"$date\": \"2025-06-27T01:09:08.717Z\"}, \"logicalSessionTimeoutMinutes\": 30, \"connectionId\": 434, \"maxWireVersion\": 25, \"ok\": 1.0}"}
2025-06-27 10:09:08,680 - pymongo.connection - DEBUG - {"message": "Connection pool ready", "clientId": {"$oid": "685def34d0a7d263c9c7c710"}, "serverHost": "************", "serverPort": 27017}
2025-06-27 10:09:08,680 - pymongo.topology - DEBUG - {"message": "Topology description changed", "topologyId": {"$oid": "685def34d0a7d263c9c7c710"}, "previousDescription": "<TopologyDescription id: 685def34d0a7d263c9c7c710, topology_type: Unknown, servers: [<ServerDescription ('************', 27017) server_type: Unknown, rtt: None>]>", "newDescription": "<TopologyDescription id: 685def34d0a7d263c9c7c710, topology_type: Single, servers: [<ServerDescription ('************', 27017) server_type: Standalone, rtt: 0.0019460259936749935>]>"}
2025-06-27 10:09:08,680 - pymongo.serverSelection - DEBUG - {"message": "Server selection succeeded", "selector": "Primary()", "operation": "ismaster", "topologyDescription": "<TopologyDescription id: 685def34d0a7d263c9c7c710, topology_type: Single, servers: [<ServerDescription ('************', 27017) server_type: Standalone, rtt: 0.0019460259936749935>]>", "clientId": {"$oid": "685def34d0a7d263c9c7c710"}, "serverHost": "************", "serverPort": 27017}
2025-06-27 10:09:08,680 - pymongo.topology - DEBUG - {"message": "Server heartbeat started", "topologyId": {"$oid": "685def34d0a7d263c9c7c710"}, "driverConnectionId": 1, "serverConnectionId": 434, "serverHost": "************", "serverPort": 27017, "awaited": true}
2025-06-27 10:09:08,681 - pymongo.connection - DEBUG - {"message": "Connection checkout started", "clientId": {"$oid": "685def34d0a7d263c9c7c710"}, "serverHost": "************", "serverPort": 27017}
2025-06-27 10:09:08,681 - pymongo.connection - DEBUG - {"message": "Connection created", "clientId": {"$oid": "685def34d0a7d263c9c7c710"}, "serverHost": "************", "serverPort": 27017, "driverConnectionId": 1}
2025-06-27 10:09:08,683 - pymongo.connection - DEBUG - {"message": "Connection ready", "clientId": {"$oid": "685def34d0a7d263c9c7c710"}, "serverHost": "************", "serverPort": 27017, "driverConnectionId": 1, "durationMS": 0.0018982780165970325}
2025-06-27 10:09:08,683 - pymongo.connection - DEBUG - {"message": "Connection checked out", "clientId": {"$oid": "685def34d0a7d263c9c7c710"}, "serverHost": "************", "serverPort": 27017, "driverConnectionId": 1, "durationMS": 0.002802897011861205}
2025-06-27 10:09:08,683 - pymongo.command - DEBUG - {"message": "Command started", "clientId": {"$oid": "685def34d0a7d263c9c7c710"}, "command": "{\"ismaster\": 1, \"lsid\": {\"id\": {\"$binary\": {\"base64\": \"odUpl1W0S06EH+Qsg0Y/sQ==\", \"subType\": \"04\"}}}, \"$db\": \"admin\"}", "commandName": "ismaster", "databaseName": "admin", "requestId": 1801979802, "operationId": 1801979802, "driverConnectionId": 1, "serverConnectionId": 436, "serverHost": "************", "serverPort": 27017}
2025-06-27 10:09:08,684 - pymongo.command - DEBUG - {"message": "Command succeeded", "clientId": {"$oid": "685def34d0a7d263c9c7c710"}, "durationMS": 0.6859999999999999, "reply": "{\"ismaster\": true, \"topologyVersion\": {\"processId\": {\"$oid\": \"685b97d01d8e2f809d089f93\"}}, \"maxBsonObjectSize\": 16777216, \"maxMessageSizeBytes\": 48000000, \"maxWriteBatchSize\": 100000, \"localTime\": {\"$date\": \"2025-06-27T01:09:08.722Z\"}, \"logicalSessionTimeoutMinutes\": 30, \"connectionId\": 436, \"maxWireVersion\": 25, \"ok\": 1.0}", "commandName": "ismaster", "databaseName": "admin", "requestId": 1801979802, "operationId": 1801979802, "driverConnectionId": 1, "serverConnectionId": 436, "serverHost": "************", "serverPort": 27017}
2025-06-27 10:09:08,684 - pymongo.connection - DEBUG - {"message": "Connection checked in", "clientId": {"$oid": "685def34d0a7d263c9c7c710"}, "serverHost": "************", "serverPort": 27017, "driverConnectionId": 1}
2025-06-27 10:09:08,685 - pymongo.serverSelection - DEBUG - {"message": "Server selection started", "selector": "<function writable_server_selector at 0x75a7f90a36a0>", "operation": "createIndexes", "topologyDescription": "<TopologyDescription id: 685def34d0a7d263c9c7c710, topology_type: Single, servers: [<ServerDescription ('************', 27017) server_type: Standalone, rtt: 0.0019460259936749935>]>", "clientId": {"$oid": "685def34d0a7d263c9c7c710"}}
2025-06-27 10:09:08,685 - pymongo.serverSelection - DEBUG - {"message": "Server selection succeeded", "selector": "<function writable_server_selector at 0x75a7f90a36a0>", "operation": "createIndexes", "topologyDescription": "<TopologyDescription id: 685def34d0a7d263c9c7c710, topology_type: Single, servers: [<ServerDescription ('************', 27017) server_type: Standalone, rtt: 0.0019460259936749935>]>", "clientId": {"$oid": "685def34d0a7d263c9c7c710"}, "serverHost": "************", "serverPort": 27017}
2025-06-27 10:09:08,685 - pymongo.connection - DEBUG - {"message": "Connection checkout started", "clientId": {"$oid": "685def34d0a7d263c9c7c710"}, "serverHost": "************", "serverPort": 27017}
2025-06-27 10:09:08,685 - pymongo.connection - DEBUG - {"message": "Connection checked out", "clientId": {"$oid": "685def34d0a7d263c9c7c710"}, "serverHost": "************", "serverPort": 27017, "driverConnectionId": 1, "durationMS": 7.138005457818508e-05}
2025-06-27 10:09:08,685 - pymongo.command - DEBUG - {"message": "Command started", "clientId": {"$oid": "685def34d0a7d263c9c7c710"}, "command": "{\"createIndexes\": \"sync_logs\", \"indexes\": [{\"name\": \"timestamp_-1_operation_type_1_employee_id_1\", \"key\": {\"timestamp\": -1, \"operation_type\": 1, \"employee_id\": 1}}], \"lsid\": {\"id\": {\"$binary\": {\"base64\": \"odUpl1W0S06EH+Qsg0Y/sQ==\", \"subType\": \"04\"}}}, \"$db\": \"server5_logs\"}", "commandName": "createIndexes", "databaseName": "server5_logs", "requestId": 1315634022, "operationId": 1315634022, "driverConnectionId": 1, "serverConnectionId": 436, "serverHost": "************", "serverPort": 27017}
2025-06-27 10:09:08,686 - pymongo.command - DEBUG - {"message": "Command succeeded", "clientId": {"$oid": "685def34d0a7d263c9c7c710"}, "durationMS": 0.7739999999999999, "reply": "{\"numIndexesBefore\": 2, \"numIndexesAfter\": 2, \"note\": \"all indexes already exist\", \"ok\": 1.0}", "commandName": "createIndexes", "databaseName": "server5_logs", "requestId": 1315634022, "operationId": 1315634022, "driverConnectionId": 1, "serverConnectionId": 436, "serverHost": "************", "serverPort": 27017}
2025-06-27 10:09:08,686 - pymongo.connection - DEBUG - {"message": "Connection checked in", "clientId": {"$oid": "685def34d0a7d263c9c7c710"}, "serverHost": "************", "serverPort": 27017, "driverConnectionId": 1}
2025-06-27 10:09:08,686 - pymongo.serverSelection - DEBUG - {"message": "Server selection started", "selector": "<function writable_server_selector at 0x75a7f90a36a0>", "operation": "createIndexes", "topologyDescription": "<TopologyDescription id: 685def34d0a7d263c9c7c710, topology_type: Single, servers: [<ServerDescription ('************', 27017) server_type: Standalone, rtt: 0.0019460259936749935>]>", "clientId": {"$oid": "685def34d0a7d263c9c7c710"}}
2025-06-27 10:09:08,686 - pymongo.serverSelection - DEBUG - {"message": "Server selection succeeded", "selector": "<function writable_server_selector at 0x75a7f90a36a0>", "operation": "createIndexes", "topologyDescription": "<TopologyDescription id: 685def34d0a7d263c9c7c710, topology_type: Single, servers: [<ServerDescription ('************', 27017) server_type: Standalone, rtt: 0.0019460259936749935>]>", "clientId": {"$oid": "685def34d0a7d263c9c7c710"}, "serverHost": "************", "serverPort": 27017}
2025-06-27 10:09:08,686 - pymongo.connection - DEBUG - {"message": "Connection checkout started", "clientId": {"$oid": "685def34d0a7d263c9c7c710"}, "serverHost": "************", "serverPort": 27017}
2025-06-27 10:09:08,686 - pymongo.connection - DEBUG - {"message": "Connection checked out", "clientId": {"$oid": "685def34d0a7d263c9c7c710"}, "serverHost": "************", "serverPort": 27017, "driverConnectionId": 1, "durationMS": 7.679197005927563e-05}
2025-06-27 10:09:08,686 - pymongo.command - DEBUG - {"message": "Command started", "clientId": {"$oid": "685def34d0a7d263c9c7c710"}, "command": "{\"createIndexes\": \"error_logs\", \"indexes\": [{\"name\": \"timestamp_-1_error_type_1\", \"key\": {\"timestamp\": -1, \"error_type\": 1}}], \"lsid\": {\"id\": {\"$binary\": {\"base64\": \"odUpl1W0S06EH+Qsg0Y/sQ==\", \"subType\": \"04\"}}}, \"$db\": \"server5_logs\"}", "commandName": "createIndexes", "databaseName": "server5_logs", "requestId": 635723058, "operationId": 635723058, "driverConnectionId": 1, "serverConnectionId": 436, "serverHost": "************", "serverPort": 27017}
2025-06-27 10:09:08,687 - pymongo.command - DEBUG - {"message": "Command succeeded", "clientId": {"$oid": "685def34d0a7d263c9c7c710"}, "durationMS": 0.758, "reply": "{\"numIndexesBefore\": 2, \"numIndexesAfter\": 2, \"note\": \"all indexes already exist\", \"ok\": 1.0}", "commandName": "createIndexes", "databaseName": "server5_logs", "requestId": 635723058, "operationId": 635723058, "driverConnectionId": 1, "serverConnectionId": 436, "serverHost": "************", "serverPort": 27017}
2025-06-27 10:09:08,687 - pymongo.connection - DEBUG - {"message": "Connection checked in", "clientId": {"$oid": "685def34d0a7d263c9c7c710"}, "serverHost": "************", "serverPort": 27017, "driverConnectionId": 1}
2025-06-27 10:09:08,687 - pymongo.serverSelection - DEBUG - {"message": "Server selection started", "selector": "<function writable_server_selector at 0x75a7f90a36a0>", "operation": "createIndexes", "topologyDescription": "<TopologyDescription id: 685def34d0a7d263c9c7c710, topology_type: Single, servers: [<ServerDescription ('************', 27017) server_type: Standalone, rtt: 0.0019460259936749935>]>", "clientId": {"$oid": "685def34d0a7d263c9c7c710"}}
2025-06-27 10:09:08,688 - pymongo.serverSelection - DEBUG - {"message": "Server selection succeeded", "selector": "<function writable_server_selector at 0x75a7f90a36a0>", "operation": "createIndexes", "topologyDescription": "<TopologyDescription id: 685def34d0a7d263c9c7c710, topology_type: Single, servers: [<ServerDescription ('************', 27017) server_type: Standalone, rtt: 0.0019460259936749935>]>", "clientId": {"$oid": "685def34d0a7d263c9c7c710"}, "serverHost": "************", "serverPort": 27017}
2025-06-27 10:09:08,688 - pymongo.connection - DEBUG - {"message": "Connection checkout started", "clientId": {"$oid": "685def34d0a7d263c9c7c710"}, "serverHost": "************", "serverPort": 27017}
2025-06-27 10:09:08,688 - pymongo.connection - DEBUG - {"message": "Connection checked out", "clientId": {"$oid": "685def34d0a7d263c9c7c710"}, "serverHost": "************", "serverPort": 27017, "driverConnectionId": 1, "durationMS": 7.933599408715963e-05}
2025-06-27 10:09:08,688 - pymongo.command - DEBUG - {"message": "Command started", "clientId": {"$oid": "685def34d0a7d263c9c7c710"}, "command": "{\"createIndexes\": \"performance_metrics\", \"indexes\": [{\"name\": \"timestamp_-1_metric_type_1\", \"key\": {\"timestamp\": -1, \"metric_type\": 1}}], \"lsid\": {\"id\": {\"$binary\": {\"base64\": \"odUpl1W0S06EH+Qsg0Y/sQ==\", \"subType\": \"04\"}}}, \"$db\": \"server5_logs\"}", "commandName": "createIndexes", "databaseName": "server5_logs", "requestId": 1369133069, "operationId": 1369133069, "driverConnectionId": 1, "serverConnectionId": 436, "serverHost": "************", "serverPort": 27017}
2025-06-27 10:09:08,689 - pymongo.command - DEBUG - {"message": "Command succeeded", "clientId": {"$oid": "685def34d0a7d263c9c7c710"}, "durationMS": 0.753, "reply": "{\"numIndexesBefore\": 2, \"numIndexesAfter\": 2, \"note\": \"all indexes already exist\", \"ok\": 1.0}", "commandName": "createIndexes", "databaseName": "server5_logs", "requestId": 1369133069, "operationId": 1369133069, "driverConnectionId": 1, "serverConnectionId": 436, "serverHost": "************", "serverPort": 27017}
2025-06-27 10:09:08,689 - pymongo.connection - DEBUG - {"message": "Connection checked in", "clientId": {"$oid": "685def34d0a7d263c9c7c710"}, "serverHost": "************", "serverPort": 27017, "driverConnectionId": 1}
2025-06-27 10:09:08,689 - app.database.mongodb_client - DEBUG - 📋 MongoDB索引创建完成
2025-06-27 10:09:08,689 - app.database.mongodb_client - INFO - ✅ MongoDB连接成功
2025-06-27 10:09:08,762 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, '/home/<USER>/Documents/testF/2025/ser25/server0613/MySuiteDatebase2402newuigood-yolo5-3/server5/logs/server5.log')}
2025-06-27 10:09:08,890 - app.database.postgresql_client - INFO - ✅ PostgreSQL连接池创建成功
2025-06-27 10:09:08,890 - app.services.f4_operation_handler - INFO - ✅ f4操作处理服务启动成功
2025-06-27 10:09:08,891 - app.main - INFO - f4_operation_handler 启动成功
2025-06-27 10:09:08,891 - app.main - INFO - 启动 f5_bulk_sync...
2025-06-27 10:09:08,892 - pymongo.topology - DEBUG - {"message": "Starting topology monitoring", "topologyId": {"$oid": "685def34d0a7d263c9c7c711"}}
2025-06-27 10:09:08,892 - pymongo.topology - DEBUG - {"message": "Topology description changed", "topologyId": {"$oid": "685def34d0a7d263c9c7c711"}, "previousDescription": "<TopologyDescription id: 685def34d0a7d263c9c7c711, topology_type: Unknown, servers: []>", "newDescription": "<TopologyDescription id: 685def34d0a7d263c9c7c711, topology_type: Unknown, servers: [<ServerDescription ('************', 27017) server_type: Unknown, rtt: None>]>"}
2025-06-27 10:09:08,892 - pymongo.topology - DEBUG - {"message": "Starting server monitoring", "topologyId": {"$oid": "685def34d0a7d263c9c7c711"}, "serverHost": "************", "serverPort": 27017}
2025-06-27 10:09:08,893 - pymongo.connection - DEBUG - {"message": "Connection pool created", "clientId": {"$oid": "685def34d0a7d263c9c7c711"}, "serverHost": "************", "serverPort": 27017}
2025-06-27 10:09:08,893 - pymongo.serverSelection - DEBUG - {"message": "Server selection started", "selector": "Primary()", "operation": "ismaster", "topologyDescription": "<TopologyDescription id: 685def34d0a7d263c9c7c711, topology_type: Unknown, servers: [<ServerDescription ('************', 27017) server_type: Unknown, rtt: None>]>", "clientId": {"$oid": "685def34d0a7d263c9c7c711"}}
2025-06-27 10:09:08,894 - pymongo.serverSelection - DEBUG - {"message": "Waiting for suitable server to become available", "selector": "Primary()", "operation": "ismaster", "topologyDescription": "<TopologyDescription id: 685def34d0a7d263c9c7c711, topology_type: Unknown, servers: [<ServerDescription ('************', 27017) server_type: Unknown, rtt: None>]>", "clientId": {"$oid": "685def34d0a7d263c9c7c711"}, "remainingTimeMS": 4999}
2025-06-27 10:09:08,894 - app.database.redis_client - INFO - Redis连接成功
2025-06-27 10:09:08,894 - pymongo.topology - DEBUG - {"message": "Server heartbeat started", "topologyId": {"$oid": "685def34d0a7d263c9c7c711"}, "driverConnectionId": 1, "serverHost": "************", "serverPort": 27017, "awaited": false}
2025-06-27 10:09:08,896 - pymongo.topology - DEBUG - {"message": "Server heartbeat succeeded", "topologyId": {"$oid": "685def34d0a7d263c9c7c711"}, "driverConnectionId": 1, "serverConnectionId": 437, "serverHost": "************", "serverPort": 27017, "awaited": false, "durationMS": 1.9973490852862597, "reply": "{\"helloOk\": true, \"ismaster\": true, \"topologyVersion\": {\"processId\": {\"$oid\": \"685b97d01d8e2f809d089f93\"}}, \"maxBsonObjectSize\": 16777216, \"maxMessageSizeBytes\": 48000000, \"maxWriteBatchSize\": 100000, \"localTime\": {\"$date\": \"2025-06-27T01:09:08.933Z\"}, \"logicalSessionTimeoutMinutes\": 30, \"connectionId\": 437, \"maxWireVersion\": 25, \"ok\": 1.0}"}
2025-06-27 10:09:08,897 - pymongo.connection - DEBUG - {"message": "Connection pool ready", "clientId": {"$oid": "685def34d0a7d263c9c7c711"}, "serverHost": "************", "serverPort": 27017}
2025-06-27 10:09:08,897 - pymongo.topology - DEBUG - {"message": "Topology description changed", "topologyId": {"$oid": "685def34d0a7d263c9c7c711"}, "previousDescription": "<TopologyDescription id: 685def34d0a7d263c9c7c711, topology_type: Unknown, servers: [<ServerDescription ('************', 27017) server_type: Unknown, rtt: None>]>", "newDescription": "<TopologyDescription id: 685def34d0a7d263c9c7c711, topology_type: Single, servers: [<ServerDescription ('************', 27017) server_type: Standalone, rtt: 0.0019973490852862597>]>"}
2025-06-27 10:09:08,897 - pymongo.serverSelection - DEBUG - {"message": "Server selection succeeded", "selector": "Primary()", "operation": "ismaster", "topologyDescription": "<TopologyDescription id: 685def34d0a7d263c9c7c711, topology_type: Single, servers: [<ServerDescription ('************', 27017) server_type: Standalone, rtt: 0.0019973490852862597>]>", "clientId": {"$oid": "685def34d0a7d263c9c7c711"}, "serverHost": "************", "serverPort": 27017}
2025-06-27 10:09:08,897 - pymongo.topology - DEBUG - {"message": "Server heartbeat started", "topologyId": {"$oid": "685def34d0a7d263c9c7c711"}, "driverConnectionId": 1, "serverConnectionId": 437, "serverHost": "************", "serverPort": 27017, "awaited": true}
2025-06-27 10:09:08,897 - pymongo.connection - DEBUG - {"message": "Connection checkout started", "clientId": {"$oid": "685def34d0a7d263c9c7c711"}, "serverHost": "************", "serverPort": 27017}
2025-06-27 10:09:08,897 - pymongo.connection - DEBUG - {"message": "Connection created", "clientId": {"$oid": "685def34d0a7d263c9c7c711"}, "serverHost": "************", "serverPort": 27017, "driverConnectionId": 1}
2025-06-27 10:09:08,898 - app.utils.server6_client - INFO - ✅ Server6连接成功: degraded
2025-06-27 10:09:08,900 - pymongo.connection - DEBUG - {"message": "Connection ready", "clientId": {"$oid": "685def34d0a7d263c9c7c711"}, "serverHost": "************", "serverPort": 27017, "driverConnectionId": 1, "durationMS": 0.0015656809555366635}
2025-06-27 10:09:08,900 - pymongo.connection - DEBUG - {"message": "Connection checked out", "clientId": {"$oid": "685def34d0a7d263c9c7c711"}, "serverHost": "************", "serverPort": 27017, "driverConnectionId": 1, "durationMS": 0.002641635946929455}
2025-06-27 10:09:08,900 - pymongo.command - DEBUG - {"message": "Command started", "clientId": {"$oid": "685def34d0a7d263c9c7c711"}, "command": "{\"ismaster\": 1, \"lsid\": {\"id\": {\"$binary\": {\"base64\": \"pZ9uJy7jQyKvVolzQRlNBA==\", \"subType\": \"04\"}}}, \"$db\": \"admin\"}", "commandName": "ismaster", "databaseName": "admin", "requestId": 1656478042, "operationId": 1656478042, "driverConnectionId": 1, "serverConnectionId": 439, "serverHost": "************", "serverPort": 27017}
2025-06-27 10:09:08,901 - pymongo.command - DEBUG - {"message": "Command succeeded", "clientId": {"$oid": "685def34d0a7d263c9c7c711"}, "durationMS": 0.716, "reply": "{\"ismaster\": true, \"topologyVersion\": {\"processId\": {\"$oid\": \"685b97d01d8e2f809d089f93\"}}, \"maxBsonObjectSize\": 16777216, \"maxMessageSizeBytes\": 48000000, \"maxWriteBatchSize\": 100000, \"localTime\": {\"$date\": \"2025-06-27T01:09:08.938Z\"}, \"logicalSessionTimeoutMinutes\": 30, \"connectionId\": 439, \"maxWireVersion\": 25, \"ok\": 1.0}", "commandName": "ismaster", "databaseName": "admin", "requestId": 1656478042, "operationId": 1656478042, "driverConnectionId": 1, "serverConnectionId": 439, "serverHost": "************", "serverPort": 27017}
2025-06-27 10:09:08,901 - pymongo.connection - DEBUG - {"message": "Connection checked in", "clientId": {"$oid": "685def34d0a7d263c9c7c711"}, "serverHost": "************", "serverPort": 27017, "driverConnectionId": 1}
2025-06-27 10:09:08,901 - pymongo.serverSelection - DEBUG - {"message": "Server selection started", "selector": "<function writable_server_selector at 0x75a7f90a36a0>", "operation": "createIndexes", "topologyDescription": "<TopologyDescription id: 685def34d0a7d263c9c7c711, topology_type: Single, servers: [<ServerDescription ('************', 27017) server_type: Standalone, rtt: 0.0019973490852862597>]>", "clientId": {"$oid": "685def34d0a7d263c9c7c711"}}
2025-06-27 10:09:08,901 - pymongo.serverSelection - DEBUG - {"message": "Server selection succeeded", "selector": "<function writable_server_selector at 0x75a7f90a36a0>", "operation": "createIndexes", "topologyDescription": "<TopologyDescription id: 685def34d0a7d263c9c7c711, topology_type: Single, servers: [<ServerDescription ('************', 27017) server_type: Standalone, rtt: 0.0019973490852862597>]>", "clientId": {"$oid": "685def34d0a7d263c9c7c711"}, "serverHost": "************", "serverPort": 27017}
2025-06-27 10:09:08,901 - pymongo.connection - DEBUG - {"message": "Connection checkout started", "clientId": {"$oid": "685def34d0a7d263c9c7c711"}, "serverHost": "************", "serverPort": 27017}
2025-06-27 10:09:08,901 - pymongo.connection - DEBUG - {"message": "Connection checked out", "clientId": {"$oid": "685def34d0a7d263c9c7c711"}, "serverHost": "************", "serverPort": 27017, "driverConnectionId": 1, "durationMS": 7.176899816840887e-05}
2025-06-27 10:09:08,901 - pymongo.command - DEBUG - {"message": "Command started", "clientId": {"$oid": "685def34d0a7d263c9c7c711"}, "command": "{\"createIndexes\": \"sync_logs\", \"indexes\": [{\"name\": \"timestamp_-1_operation_type_1_employee_id_1\", \"key\": {\"timestamp\": -1, \"operation_type\": 1, \"employee_id\": 1}}], \"lsid\": {\"id\": {\"$binary\": {\"base64\": \"pZ9uJy7jQyKvVolzQRlNBA==\", \"subType\": \"04\"}}}, \"$db\": \"server5_logs\"}", "commandName": "createIndexes", "databaseName": "server5_logs", "requestId": 1131176229, "operationId": 1131176229, "driverConnectionId": 1, "serverConnectionId": 439, "serverHost": "************", "serverPort": 27017}
2025-06-27 10:09:08,902 - pymongo.command - DEBUG - {"message": "Command succeeded", "clientId": {"$oid": "685def34d0a7d263c9c7c711"}, "durationMS": 0.8059999999999999, "reply": "{\"numIndexesBefore\": 2, \"numIndexesAfter\": 2, \"note\": \"all indexes already exist\", \"ok\": 1.0}", "commandName": "createIndexes", "databaseName": "server5_logs", "requestId": 1131176229, "operationId": 1131176229, "driverConnectionId": 1, "serverConnectionId": 439, "serverHost": "************", "serverPort": 27017}
2025-06-27 10:09:08,902 - pymongo.connection - DEBUG - {"message": "Connection checked in", "clientId": {"$oid": "685def34d0a7d263c9c7c711"}, "serverHost": "************", "serverPort": 27017, "driverConnectionId": 1}
2025-06-27 10:09:08,903 - pymongo.serverSelection - DEBUG - {"message": "Server selection started", "selector": "<function writable_server_selector at 0x75a7f90a36a0>", "operation": "createIndexes", "topologyDescription": "<TopologyDescription id: 685def34d0a7d263c9c7c711, topology_type: Single, servers: [<ServerDescription ('************', 27017) server_type: Standalone, rtt: 0.0019973490852862597>]>", "clientId": {"$oid": "685def34d0a7d263c9c7c711"}}
2025-06-27 10:09:08,903 - pymongo.serverSelection - DEBUG - {"message": "Server selection succeeded", "selector": "<function writable_server_selector at 0x75a7f90a36a0>", "operation": "createIndexes", "topologyDescription": "<TopologyDescription id: 685def34d0a7d263c9c7c711, topology_type: Single, servers: [<ServerDescription ('************', 27017) server_type: Standalone, rtt: 0.0019973490852862597>]>", "clientId": {"$oid": "685def34d0a7d263c9c7c711"}, "serverHost": "************", "serverPort": 27017}
2025-06-27 10:09:08,903 - pymongo.connection - DEBUG - {"message": "Connection checkout started", "clientId": {"$oid": "685def34d0a7d263c9c7c711"}, "serverHost": "************", "serverPort": 27017}
2025-06-27 10:09:08,903 - pymongo.connection - DEBUG - {"message": "Connection checked out", "clientId": {"$oid": "685def34d0a7d263c9c7c711"}, "serverHost": "************", "serverPort": 27017, "driverConnectionId": 1, "durationMS": 7.659394759684801e-05}
2025-06-27 10:09:08,903 - pymongo.command - DEBUG - {"message": "Command started", "clientId": {"$oid": "685def34d0a7d263c9c7c711"}, "command": "{\"createIndexes\": \"error_logs\", \"indexes\": [{\"name\": \"timestamp_-1_error_type_1\", \"key\": {\"timestamp\": -1, \"error_type\": 1}}], \"lsid\": {\"id\": {\"$binary\": {\"base64\": \"pZ9uJy7jQyKvVolzQRlNBA==\", \"subType\": \"04\"}}}, \"$db\": \"server5_logs\"}", "commandName": "createIndexes", "databaseName": "server5_logs", "requestId": 1653377373, "operationId": 1653377373, "driverConnectionId": 1, "serverConnectionId": 439, "serverHost": "************", "serverPort": 27017}
2025-06-27 10:09:08,904 - pymongo.command - DEBUG - {"message": "Command succeeded", "clientId": {"$oid": "685def34d0a7d263c9c7c711"}, "durationMS": 0.716, "reply": "{\"numIndexesBefore\": 2, \"numIndexesAfter\": 2, \"note\": \"all indexes already exist\", \"ok\": 1.0}", "commandName": "createIndexes", "databaseName": "server5_logs", "requestId": 1653377373, "operationId": 1653377373, "driverConnectionId": 1, "serverConnectionId": 439, "serverHost": "************", "serverPort": 27017}
2025-06-27 10:09:08,904 - pymongo.connection - DEBUG - {"message": "Connection checked in", "clientId": {"$oid": "685def34d0a7d263c9c7c711"}, "serverHost": "************", "serverPort": 27017, "driverConnectionId": 1}
2025-06-27 10:09:08,904 - pymongo.serverSelection - DEBUG - {"message": "Server selection started", "selector": "<function writable_server_selector at 0x75a7f90a36a0>", "operation": "createIndexes", "topologyDescription": "<TopologyDescription id: 685def34d0a7d263c9c7c711, topology_type: Single, servers: [<ServerDescription ('************', 27017) server_type: Standalone, rtt: 0.0019973490852862597>]>", "clientId": {"$oid": "685def34d0a7d263c9c7c711"}}
2025-06-27 10:09:08,904 - pymongo.serverSelection - DEBUG - {"message": "Server selection succeeded", "selector": "<function writable_server_selector at 0x75a7f90a36a0>", "operation": "createIndexes", "topologyDescription": "<TopologyDescription id: 685def34d0a7d263c9c7c711, topology_type: Single, servers: [<ServerDescription ('************', 27017) server_type: Standalone, rtt: 0.0019973490852862597>]>", "clientId": {"$oid": "685def34d0a7d263c9c7c711"}, "serverHost": "************", "serverPort": 27017}
2025-06-27 10:09:08,904 - pymongo.connection - DEBUG - {"message": "Connection checkout started", "clientId": {"$oid": "685def34d0a7d263c9c7c711"}, "serverHost": "************", "serverPort": 27017}
2025-06-27 10:09:08,904 - pymongo.connection - DEBUG - {"message": "Connection checked out", "clientId": {"$oid": "685def34d0a7d263c9c7c711"}, "serverHost": "************", "serverPort": 27017, "driverConnectionId": 1, "durationMS": 6.984604988247156e-05}
2025-06-27 10:09:08,904 - pymongo.command - DEBUG - {"message": "Command started", "clientId": {"$oid": "685def34d0a7d263c9c7c711"}, "command": "{\"createIndexes\": \"performance_metrics\", \"indexes\": [{\"name\": \"timestamp_-1_metric_type_1\", \"key\": {\"timestamp\": -1, \"metric_type\": 1}}], \"lsid\": {\"id\": {\"$binary\": {\"base64\": \"pZ9uJy7jQyKvVolzQRlNBA==\", \"subType\": \"04\"}}}, \"$db\": \"server5_logs\"}", "commandName": "createIndexes", "databaseName": "server5_logs", "requestId": 859484421, "operationId": 859484421, "driverConnectionId": 1, "serverConnectionId": 439, "serverHost": "************", "serverPort": 27017}
2025-06-27 10:09:08,905 - pymongo.command - DEBUG - {"message": "Command succeeded", "clientId": {"$oid": "685def34d0a7d263c9c7c711"}, "durationMS": 0.675, "reply": "{\"numIndexesBefore\": 2, \"numIndexesAfter\": 2, \"note\": \"all indexes already exist\", \"ok\": 1.0}", "commandName": "createIndexes", "databaseName": "server5_logs", "requestId": 859484421, "operationId": 859484421, "driverConnectionId": 1, "serverConnectionId": 439, "serverHost": "************", "serverPort": 27017}
2025-06-27 10:09:08,905 - pymongo.connection - DEBUG - {"message": "Connection checked in", "clientId": {"$oid": "685def34d0a7d263c9c7c711"}, "serverHost": "************", "serverPort": 27017, "driverConnectionId": 1}
2025-06-27 10:09:08,905 - app.database.mongodb_client - DEBUG - 📋 MongoDB索引创建完成
2025-06-27 10:09:08,905 - app.database.mongodb_client - INFO - ✅ MongoDB连接成功
2025-06-27 10:09:09,104 - app.database.postgresql_client - INFO - ✅ PostgreSQL连接池创建成功
2025-06-27 10:09:09,104 - app.services.f5_bulk_sync - INFO - ✅ f5批量同步服务启动成功
2025-06-27 10:09:09,104 - app.services.f5_bulk_sync - INFO - 🚀 批量同步循环启动
2025-06-27 10:09:09,104 - app.services.f5_bulk_sync - INFO - 📊 开始执行批量同步
2025-06-27 10:09:09,104 - app.services.f5_bulk_sync - INFO - 🔍 一致性检查循环启动
2025-06-27 10:09:09,104 - app.services.f5_bulk_sync - INFO - 🔍 开始执行一致性检查
2025-06-27 10:09:09,104 - app.main - INFO - f5_bulk_sync 启动成功
2025-06-27 10:09:09,104 - app.main - INFO - 启动 f6_user_sync...
2025-06-27 10:09:09,105 - pymongo.topology - DEBUG - {"message": "Starting topology monitoring", "topologyId": {"$oid": "685def35d0a7d263c9c7c712"}}
2025-06-27 10:09:09,106 - pymongo.topology - DEBUG - {"message": "Topology description changed", "topologyId": {"$oid": "685def35d0a7d263c9c7c712"}, "previousDescription": "<TopologyDescription id: 685def35d0a7d263c9c7c712, topology_type: Unknown, servers: []>", "newDescription": "<TopologyDescription id: 685def35d0a7d263c9c7c712, topology_type: Unknown, servers: [<ServerDescription ('************', 27017) server_type: Unknown, rtt: None>]>"}
2025-06-27 10:09:09,106 - pymongo.topology - DEBUG - {"message": "Starting server monitoring", "topologyId": {"$oid": "685def35d0a7d263c9c7c712"}, "serverHost": "************", "serverPort": 27017}
2025-06-27 10:09:09,106 - pymongo.connection - DEBUG - {"message": "Connection pool created", "clientId": {"$oid": "685def35d0a7d263c9c7c712"}, "serverHost": "************", "serverPort": 27017}
2025-06-27 10:09:09,107 - pymongo.serverSelection - DEBUG - {"message": "Server selection started", "selector": "Primary()", "operation": "ismaster", "topologyDescription": "<TopologyDescription id: 685def35d0a7d263c9c7c712, topology_type: Unknown, servers: [<ServerDescription ('************', 27017) server_type: Unknown, rtt: None>]>", "clientId": {"$oid": "685def35d0a7d263c9c7c712"}}
2025-06-27 10:09:09,107 - pymongo.serverSelection - DEBUG - {"message": "Waiting for suitable server to become available", "selector": "Primary()", "operation": "ismaster", "topologyDescription": "<TopologyDescription id: 685def35d0a7d263c9c7c712, topology_type: Unknown, servers: [<ServerDescription ('************', 27017) server_type: Unknown, rtt: None>]>", "clientId": {"$oid": "685def35d0a7d263c9c7c712"}, "remainingTimeMS": 4999}
2025-06-27 10:09:09,107 - app.database.redis_client - INFO - Redis连接成功
2025-06-27 10:09:09,107 - pymongo.topology - DEBUG - {"message": "Server heartbeat started", "topologyId": {"$oid": "685def35d0a7d263c9c7c712"}, "driverConnectionId": 1, "serverHost": "************", "serverPort": 27017, "awaited": false}
2025-06-27 10:09:09,109 - pymongo.topology - DEBUG - {"message": "Server heartbeat succeeded", "topologyId": {"$oid": "685def35d0a7d263c9c7c712"}, "driverConnectionId": 1, "serverConnectionId": 440, "serverHost": "************", "serverPort": 27017, "awaited": false, "durationMS": 1.9125130493193865, "reply": "{\"helloOk\": true, \"ismaster\": true, \"topologyVersion\": {\"processId\": {\"$oid\": \"685b97d01d8e2f809d089f93\"}}, \"maxBsonObjectSize\": 16777216, \"maxMessageSizeBytes\": 48000000, \"maxWriteBatchSize\": 100000, \"localTime\": {\"$date\": \"2025-06-27T01:09:09.147Z\"}, \"logicalSessionTimeoutMinutes\": 30, \"connectionId\": 440, \"maxWireVersion\": 25, \"ok\": 1.0}"}
2025-06-27 10:09:09,110 - pymongo.connection - DEBUG - {"message": "Connection pool ready", "clientId": {"$oid": "685def35d0a7d263c9c7c712"}, "serverHost": "************", "serverPort": 27017}
2025-06-27 10:09:09,110 - pymongo.topology - DEBUG - {"message": "Topology description changed", "topologyId": {"$oid": "685def35d0a7d263c9c7c712"}, "previousDescription": "<TopologyDescription id: 685def35d0a7d263c9c7c712, topology_type: Unknown, servers: [<ServerDescription ('************', 27017) server_type: Unknown, rtt: None>]>", "newDescription": "<TopologyDescription id: 685def35d0a7d263c9c7c712, topology_type: Single, servers: [<ServerDescription ('************', 27017) server_type: Standalone, rtt: 0.0019125130493193865>]>"}
2025-06-27 10:09:09,110 - pymongo.serverSelection - DEBUG - {"message": "Server selection succeeded", "selector": "Primary()", "operation": "ismaster", "topologyDescription": "<TopologyDescription id: 685def35d0a7d263c9c7c712, topology_type: Single, servers: [<ServerDescription ('************', 27017) server_type: Standalone, rtt: 0.0019125130493193865>]>", "clientId": {"$oid": "685def35d0a7d263c9c7c712"}, "serverHost": "************", "serverPort": 27017}
2025-06-27 10:09:09,110 - pymongo.topology - DEBUG - {"message": "Server heartbeat started", "topologyId": {"$oid": "685def35d0a7d263c9c7c712"}, "driverConnectionId": 1, "serverConnectionId": 440, "serverHost": "************", "serverPort": 27017, "awaited": true}
2025-06-27 10:09:09,110 - pymongo.connection - DEBUG - {"message": "Connection checkout started", "clientId": {"$oid": "685def35d0a7d263c9c7c712"}, "serverHost": "************", "serverPort": 27017}
2025-06-27 10:09:09,110 - pymongo.connection - DEBUG - {"message": "Connection created", "clientId": {"$oid": "685def35d0a7d263c9c7c712"}, "serverHost": "************", "serverPort": 27017, "driverConnectionId": 1}
2025-06-27 10:09:09,112 - app.utils.server6_client - INFO - ✅ Server6连接成功: degraded
2025-06-27 10:09:09,112 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, '/home/<USER>/Documents/testF/2025/ser25/server0613/MySuiteDatebase2402newuigood-yolo5-3/server5/logs/server5.log')}
2025-06-27 10:09:09,113 - app.database.postgresql_client - ERROR - 查询执行失败: 
                SELECT * FROM entries 
                WHERE updated_at > NOW() - INTERVAL '1 day'
... 错误: 列"updated_at"は存在しません
2025-06-27 10:09:09,113 - app.database.postgresql_client - ERROR - 查询执行失败: 
                SELECT * FROM entries 
                WHERE external_id IS NOT NULL 
             ... 错误: 列"updated_at"は存在しません
2025-06-27 10:09:09,113 - pymongo.connection - DEBUG - {"message": "Connection ready", "clientId": {"$oid": "685def35d0a7d263c9c7c712"}, "serverHost": "************", "serverPort": 27017, "driverConnectionId": 1, "durationMS": 0.0025064849760383368}
2025-06-27 10:09:09,114 - pymongo.connection - DEBUG - {"message": "Connection checked out", "clientId": {"$oid": "685def35d0a7d263c9c7c712"}, "serverHost": "************", "serverPort": 27017, "driverConnectionId": 1, "durationMS": 0.003569438005797565}
2025-06-27 10:09:09,114 - pymongo.command - DEBUG - {"message": "Command started", "clientId": {"$oid": "685def35d0a7d263c9c7c712"}, "command": "{\"ismaster\": 1, \"lsid\": {\"id\": {\"$binary\": {\"base64\": \"oR+S8tbvS6m0zwUEd1X9cg==\", \"subType\": \"04\"}}}, \"$db\": \"admin\"}", "commandName": "ismaster", "databaseName": "admin", "requestId": 1973594324, "operationId": 1973594324, "driverConnectionId": 1, "serverConnectionId": 442, "serverHost": "************", "serverPort": 27017}
2025-06-27 10:09:09,114 - app.services.f5_bulk_sync - ERROR - ❌ 数据一致性检查失败: 列"updated_at"は存在しません
2025-06-27 10:09:09,114 - app.services.f5_bulk_sync - ERROR - ❌ 获取同步数据失败: 列"updated_at"は存在しません
2025-06-27 10:09:09,114 - app.services.f5_bulk_sync - DEBUG - 📝 没有需要批量同步的数据
2025-06-27 10:09:09,114 - app.services.f5_bulk_sync - INFO - ✅ 一致性检查完成: {'error': '列"updated_at"は存在しません'}
2025-06-27 10:09:09,115 - pymongo.command - DEBUG - {"message": "Command succeeded", "clientId": {"$oid": "685def35d0a7d263c9c7c712"}, "durationMS": 0.735, "reply": "{\"ismaster\": true, \"topologyVersion\": {\"processId\": {\"$oid\": \"685b97d01d8e2f809d089f93\"}}, \"maxBsonObjectSize\": 16777216, \"maxMessageSizeBytes\": 48000000, \"maxWriteBatchSize\": 100000, \"localTime\": {\"$date\": \"2025-06-27T01:09:09.152Z\"}, \"logicalSessionTimeoutMinutes\": 30, \"connectionId\": 442, \"maxWireVersion\": 25, \"ok\": 1.0}", "commandName": "ismaster", "databaseName": "admin", "requestId": 1973594324, "operationId": 1973594324, "driverConnectionId": 1, "serverConnectionId": 442, "serverHost": "************", "serverPort": 27017}
2025-06-27 10:09:09,115 - pymongo.connection - DEBUG - {"message": "Connection checked in", "clientId": {"$oid": "685def35d0a7d263c9c7c712"}, "serverHost": "************", "serverPort": 27017, "driverConnectionId": 1}
2025-06-27 10:09:09,115 - pymongo.serverSelection - DEBUG - {"message": "Server selection started", "selector": "<function writable_server_selector at 0x75a7f90a36a0>", "operation": "createIndexes", "topologyDescription": "<TopologyDescription id: 685def35d0a7d263c9c7c712, topology_type: Single, servers: [<ServerDescription ('************', 27017) server_type: Standalone, rtt: 0.0019125130493193865>]>", "clientId": {"$oid": "685def35d0a7d263c9c7c712"}}
2025-06-27 10:09:09,115 - pymongo.serverSelection - DEBUG - {"message": "Server selection succeeded", "selector": "<function writable_server_selector at 0x75a7f90a36a0>", "operation": "createIndexes", "topologyDescription": "<TopologyDescription id: 685def35d0a7d263c9c7c712, topology_type: Single, servers: [<ServerDescription ('************', 27017) server_type: Standalone, rtt: 0.0019125130493193865>]>", "clientId": {"$oid": "685def35d0a7d263c9c7c712"}, "serverHost": "************", "serverPort": 27017}
2025-06-27 10:09:09,115 - pymongo.connection - DEBUG - {"message": "Connection checkout started", "clientId": {"$oid": "685def35d0a7d263c9c7c712"}, "serverHost": "************", "serverPort": 27017}
2025-06-27 10:09:09,115 - pymongo.connection - DEBUG - {"message": "Connection checked out", "clientId": {"$oid": "685def35d0a7d263c9c7c712"}, "serverHost": "************", "serverPort": 27017, "driverConnectionId": 1, "durationMS": 8.17989930510521e-05}
2025-06-27 10:09:09,116 - pymongo.command - DEBUG - {"message": "Command started", "clientId": {"$oid": "685def35d0a7d263c9c7c712"}, "command": "{\"createIndexes\": \"sync_logs\", \"indexes\": [{\"name\": \"timestamp_-1_operation_type_1_employee_id_1\", \"key\": {\"timestamp\": -1, \"operation_type\": 1, \"employee_id\": 1}}], \"lsid\": {\"id\": {\"$binary\": {\"base64\": \"oR+S8tbvS6m0zwUEd1X9cg==\", \"subType\": \"04\"}}}, \"$db\": \"server5_logs\"}", "commandName": "createIndexes", "databaseName": "server5_logs", "requestId": 149798315, "operationId": 149798315, "driverConnectionId": 1, "serverConnectionId": 442, "serverHost": "************", "serverPort": 27017}
2025-06-27 10:09:09,116 - pymongo.command - DEBUG - {"message": "Command succeeded", "clientId": {"$oid": "685def35d0a7d263c9c7c712"}, "durationMS": 0.848, "reply": "{\"numIndexesBefore\": 2, \"numIndexesAfter\": 2, \"note\": \"all indexes already exist\", \"ok\": 1.0}", "commandName": "createIndexes", "databaseName": "server5_logs", "requestId": 149798315, "operationId": 149798315, "driverConnectionId": 1, "serverConnectionId": 442, "serverHost": "************", "serverPort": 27017}
2025-06-27 10:09:09,117 - pymongo.connection - DEBUG - {"message": "Connection checked in", "clientId": {"$oid": "685def35d0a7d263c9c7c712"}, "serverHost": "************", "serverPort": 27017, "driverConnectionId": 1}
2025-06-27 10:09:09,117 - pymongo.serverSelection - DEBUG - {"message": "Server selection started", "selector": "<function writable_server_selector at 0x75a7f90a36a0>", "operation": "createIndexes", "topologyDescription": "<TopologyDescription id: 685def35d0a7d263c9c7c712, topology_type: Single, servers: [<ServerDescription ('************', 27017) server_type: Standalone, rtt: 0.0019125130493193865>]>", "clientId": {"$oid": "685def35d0a7d263c9c7c712"}}
2025-06-27 10:09:09,117 - pymongo.serverSelection - DEBUG - {"message": "Server selection succeeded", "selector": "<function writable_server_selector at 0x75a7f90a36a0>", "operation": "createIndexes", "topologyDescription": "<TopologyDescription id: 685def35d0a7d263c9c7c712, topology_type: Single, servers: [<ServerDescription ('************', 27017) server_type: Standalone, rtt: 0.0019125130493193865>]>", "clientId": {"$oid": "685def35d0a7d263c9c7c712"}, "serverHost": "************", "serverPort": 27017}
2025-06-27 10:09:09,117 - pymongo.connection - DEBUG - {"message": "Connection checkout started", "clientId": {"$oid": "685def35d0a7d263c9c7c712"}, "serverHost": "************", "serverPort": 27017}
2025-06-27 10:09:09,117 - pymongo.connection - DEBUG - {"message": "Connection checked out", "clientId": {"$oid": "685def35d0a7d263c9c7c712"}, "serverHost": "************", "serverPort": 27017, "driverConnectionId": 1, "durationMS": 7.715902756899595e-05}
2025-06-27 10:09:09,117 - pymongo.command - DEBUG - {"message": "Command started", "clientId": {"$oid": "685def35d0a7d263c9c7c712"}, "command": "{\"createIndexes\": \"error_logs\", \"indexes\": [{\"name\": \"timestamp_-1_error_type_1\", \"key\": {\"timestamp\": -1, \"error_type\": 1}}], \"lsid\": {\"id\": {\"$binary\": {\"base64\": \"oR+S8tbvS6m0zwUEd1X9cg==\", \"subType\": \"04\"}}}, \"$db\": \"server5_logs\"}", "commandName": "createIndexes", "databaseName": "server5_logs", "requestId": 2038664370, "operationId": 2038664370, "driverConnectionId": 1, "serverConnectionId": 442, "serverHost": "************", "serverPort": 27017}
2025-06-27 10:09:09,118 - pymongo.command - DEBUG - {"message": "Command succeeded", "clientId": {"$oid": "685def35d0a7d263c9c7c712"}, "durationMS": 0.759, "reply": "{\"numIndexesBefore\": 2, \"numIndexesAfter\": 2, \"note\": \"all indexes already exist\", \"ok\": 1.0}", "commandName": "createIndexes", "databaseName": "server5_logs", "requestId": 2038664370, "operationId": 2038664370, "driverConnectionId": 1, "serverConnectionId": 442, "serverHost": "************", "serverPort": 27017}
2025-06-27 10:09:09,118 - pymongo.connection - DEBUG - {"message": "Connection checked in", "clientId": {"$oid": "685def35d0a7d263c9c7c712"}, "serverHost": "************", "serverPort": 27017, "driverConnectionId": 1}
2025-06-27 10:09:09,118 - pymongo.serverSelection - DEBUG - {"message": "Server selection started", "selector": "<function writable_server_selector at 0x75a7f90a36a0>", "operation": "createIndexes", "topologyDescription": "<TopologyDescription id: 685def35d0a7d263c9c7c712, topology_type: Single, servers: [<ServerDescription ('************', 27017) server_type: Standalone, rtt: 0.0019125130493193865>]>", "clientId": {"$oid": "685def35d0a7d263c9c7c712"}}
2025-06-27 10:09:09,118 - pymongo.serverSelection - DEBUG - {"message": "Server selection succeeded", "selector": "<function writable_server_selector at 0x75a7f90a36a0>", "operation": "createIndexes", "topologyDescription": "<TopologyDescription id: 685def35d0a7d263c9c7c712, topology_type: Single, servers: [<ServerDescription ('************', 27017) server_type: Standalone, rtt: 0.0019125130493193865>]>", "clientId": {"$oid": "685def35d0a7d263c9c7c712"}, "serverHost": "************", "serverPort": 27017}
2025-06-27 10:09:09,118 - pymongo.connection - DEBUG - {"message": "Connection checkout started", "clientId": {"$oid": "685def35d0a7d263c9c7c712"}, "serverHost": "************", "serverPort": 27017}
2025-06-27 10:09:09,119 - pymongo.connection - DEBUG - {"message": "Connection checked out", "clientId": {"$oid": "685def35d0a7d263c9c7c712"}, "serverHost": "************", "serverPort": 27017, "driverConnectionId": 1, "durationMS": 8.31129727885127e-05}
2025-06-27 10:09:09,119 - pymongo.command - DEBUG - {"message": "Command started", "clientId": {"$oid": "685def35d0a7d263c9c7c712"}, "command": "{\"createIndexes\": \"performance_metrics\", \"indexes\": [{\"name\": \"timestamp_-1_metric_type_1\", \"key\": {\"timestamp\": -1, \"metric_type\": 1}}], \"lsid\": {\"id\": {\"$binary\": {\"base64\": \"oR+S8tbvS6m0zwUEd1X9cg==\", \"subType\": \"04\"}}}, \"$db\": \"server5_logs\"}", "commandName": "createIndexes", "databaseName": "server5_logs", "requestId": 1129566413, "operationId": 1129566413, "driverConnectionId": 1, "serverConnectionId": 442, "serverHost": "************", "serverPort": 27017}
2025-06-27 10:09:09,119 - pymongo.command - DEBUG - {"message": "Command succeeded", "clientId": {"$oid": "685def35d0a7d263c9c7c712"}, "durationMS": 0.8099999999999999, "reply": "{\"numIndexesBefore\": 2, \"numIndexesAfter\": 2, \"note\": \"all indexes already exist\", \"ok\": 1.0}", "commandName": "createIndexes", "databaseName": "server5_logs", "requestId": 1129566413, "operationId": 1129566413, "driverConnectionId": 1, "serverConnectionId": 442, "serverHost": "************", "serverPort": 27017}
2025-06-27 10:09:09,120 - pymongo.connection - DEBUG - {"message": "Connection checked in", "clientId": {"$oid": "685def35d0a7d263c9c7c712"}, "serverHost": "************", "serverPort": 27017, "driverConnectionId": 1}
2025-06-27 10:09:09,120 - app.database.mongodb_client - DEBUG - 📋 MongoDB索引创建完成
2025-06-27 10:09:09,120 - app.database.mongodb_client - INFO - ✅ MongoDB连接成功
2025-06-27 10:09:09,322 - app.database.postgresql_client - INFO - ✅ PostgreSQL连接池创建成功
2025-06-27 10:09:09,322 - app.services.f6_user_sync - INFO - ✅ f6专属ID同步服务启动成功
2025-06-27 10:09:09,322 - app.services.f6_user_sync - INFO - 🚀 用户同步工作线程启动
2025-06-27 10:09:09,322 - app.services.f6_user_sync - INFO - 🔄 启动自动同步循环，间隔: 600秒
2025-06-27 10:09:09,323 - app.main - INFO - f6_user_sync 启动成功
2025-06-27 10:09:09,323 - app.main - INFO - 服务启动完成，已启动: ['f1_listener', 'f2_push_writer', 'f4_operation_handler', 'f5_bulk_sync', 'f6_user_sync']
INFO:     Application startup complete.
2025-06-27 10:09:09,329 - app.database.postgresql_client - ERROR - 查询执行失败: 
                SELECT DISTINCT employee_id 
                FROM entries 
                WHERE up... 错误: 列"updated_at"は存在しません
2025-06-27 10:09:09,330 - app.services.f6_user_sync - ERROR - ❌ 自动同步检查失败: 列"updated_at"は存在しません
2025-06-27 10:09:09,463 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, '/home/<USER>/Documents/testF/2025/ser25/server0613/MySuiteDatebase2402newuigood-yolo5-3/server5/logs/server5.log')}
2025-06-27 10:09:09,813 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, '/home