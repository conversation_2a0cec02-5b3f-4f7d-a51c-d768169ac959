


```sql
/*───────────────────────────────────────────────
  1. 父表 public.entries   （分区键：entry_date）
───────────────────────────────────────────────*/
CREATE TABLE IF NOT EXISTS public.entries (
    entry_date      DATE         NOT NULL,
    id              BIGSERIAL    NOT NULL,
    external_id     INTEGER,
    ts              TIMESTAMPTZ  NOT NULL DEFAULT NOW(),
    employee_id     TEXT NOT NULL,
    model           TEXT,
    number          TEXT,
    factory_number  TEXT,
    project_number  TEXT,
    unit_number     TEXT,
    category        INT,
    item            INT,
    duration        NUMERIC,
    department      TEXT,
    PRIMARY KEY (entry_date, id),
    UNIQUE (entry_date, external_id)         -- 复合唯一键
) PARTITION BY RANGE (entry_date);

/* 2. 现有两个月分区（示例）*/
CREATE TABLE IF NOT EXISTS public.entries_2025_05
  PARTITION OF public.entries
  FOR VALUES FROM ('2025-05-01') TO ('2025-06-01');

CREATE TABLE IF NOT EXISTS public.entries_2025_06
  PARTITION OF public.entries
  FOR VALUES FROM ('2025-06-01') TO ('2025-07-01');

/* 3. 函数：create_next_month_partition */
CREATE OR REPLACE FUNCTION create_next_month_partition() RETURNS void AS $$
DECLARE
    start_next  DATE := date_trunc('month', CURRENT_DATE) + INTERVAL '1 month';
    start_after DATE := start_next + INTERVAL '1 month';
    part_name   TEXT := format('entries_%s', to_char(start_next, 'YYYY_MM'));
BEGIN
    EXECUTE format(
        'CREATE TABLE IF NOT EXISTS public.%I PARTITION OF public.entries
           FOR VALUES FROM (%L) TO (%L);',
        part_name, start_next::date, start_after::date
    );
END;
$$ LANGUAGE plpgsql;

/* 4. staging_entries */
CREATE TABLE IF NOT EXISTS public.staging_entries (
    external_id     INTEGER PRIMARY KEY,
    entry_date      DATE         NOT NULL,
    ts              TIMESTAMPTZ  NOT NULL,
    employee_id     TEXT         NOT NULL,
    model           TEXT,
    number          TEXT,
    factory_number  TEXT,
    project_number  TEXT,
    unit_number     TEXT,
    category        INT,
    item            INT,
    duration        NUMERIC,
    department      TEXT
);

/* 5. entries_push_queue */
CREATE TABLE IF NOT EXISTS public.entries_push_queue (
    queue_id    BIGSERIAL PRIMARY KEY,
    entry_id    BIGINT     NOT NULL,
    external_id INTEGER,
    operation   VARCHAR(8) NOT NULL CHECK (operation IN ('INSERT','UPDATE','DELETE')),
    synced      BOOLEAN    NOT NULL DEFAULT FALSE,
    created_ts  TIMESTAMPTZ NOT NULL DEFAULT NOW()
);
CREATE INDEX IF NOT EXISTS idx_push_unsynced ON public.entries_push_queue (synced);

/* 6. AFTER 触发器：排队 + NOTIFY(push_job) */
CREATE OR REPLACE FUNCTION trg_entries_enqueue() RETURNS TRIGGER AS $$
DECLARE q BIGINT;
BEGIN
    IF TG_OP='INSERT' THEN
        INSERT INTO entries_push_queue(entry_id, operation)
        VALUES (NEW.id,'INSERT') RETURNING queue_id INTO q;
    ELSIF TG_OP='UPDATE' THEN
        INSERT INTO entries_push_queue(entry_id, external_id, operation)
        VALUES (NEW.id, NEW.external_id,'UPDATE') RETURNING queue_id INTO q;
    ELSE
        INSERT INTO entries_push_queue(entry_id, external_id, operation)
        VALUES (OLD.id, OLD.external_id,'DELETE') RETURNING queue_id INTO q;
    END IF;
    PERFORM pg_notify('push_job', q::text);
    RETURN NULL;
END;
$$ LANGUAGE plpgsql;

DROP TRIGGER IF EXISTS trg_entries_enqueue ON public.entries;
CREATE TRIGGER trg_entries_enqueue
AFTER INSERT OR UPDATE OR DELETE ON public.entries
FOR EACH ROW EXECUTE FUNCTION trg_entries_enqueue();
```