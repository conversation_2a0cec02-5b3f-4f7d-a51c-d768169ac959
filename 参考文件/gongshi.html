<!DOCTYPE html>
<html lang="ja">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>高精度CNC加工機製造業向け 統合管理システム導入提案書</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <style>
        body {
            font-family: 'Noto Sans JP', 'Hiragino Kaku Gothic ProN', 'メイリオ', Meiryo, sans-serif;
            background-color: #E0E0E0; /* Lighter Gray for background outside A3 pages */
            color: #333333; /* Dark Gray - Text */
            display: flex;
            flex-direction: column;
            align-items: center; /* Center pages horizontally */
            padding: 20px;
        }
        .a3-page {
            width: 1587px; /* A3 Landscape at ~128 DPI (420mm x 128/25.4) */
            height: 1122px; /* A3 Landscape at ~128 DPI (297mm x 128/25.4) */
            background-color: #FFFFFF; /* White background for the page */
            box-shadow: 0 0 15px rgba(0, 0, 0, 0.2);
            margin-bottom: 30px; /* Space between pages */
            padding: 20px 35px 50px; /* Reduced padding further */
            box-sizing: border-box; /* Include padding in width/height */
            display: flex;
            flex-direction: column;
            justify-content: flex-start; /* Start from top, allow natural spacing */
            overflow: hidden; /* Ensure content stays within page bounds */
        }

        .header-bg {
            background: linear-gradient(135deg, #2196F3 0%, #4CAF50 100%);
            color: white;
            padding: 20px 40px;
            text-align: center;
            width: 100%; /* Full width */
            box-sizing: border-box;
            margin-bottom: 20px;
            box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
        }
        .footer-bg {
            background-color: #333333;
            color: #CCCCCC;
            padding: 15px 40px;
            text-align: center;
            width: 100%; /* Full width */
            box-sizing: border-box;
            margin-top: 20px;
        }

        .section-title {
            color: #333333;
            border-bottom: 4px solid #4CAF50; /* Subtle Green Accent */
            padding-bottom: 0.5rem;
            display: inline-block;
            margin-bottom: 1.5rem;
            font-size: 2.75rem; /* Larger title for A3 */
            text-align: center;
            width: 100%;
        }
        .card {
            background-color: white;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.08);
            padding: 0.75rem; /* Further reduced padding */
            margin-bottom: 0.5rem; /* Minimal margin */
            transition: transform 0.2s ease-out, box-shadow 0.2s ease-out;
            flex-grow: 1; /* Allows cards to fill space */
            display: flex;
            flex-direction: column;
        }
        .card:hover {
            transform: translateY(-2px);
            box-shadow: 0 6px 12px rgba(0, 0, 0, 0.1);
        }
        .stat-value {
            color: #4CAF50;
            font-weight: 700;
            font-size: 2rem; /* Adjusted size for A3 */
        }
        .stat-label {
            color: #555555;
            font-size: 0.9rem;
            margin-top: 0.25rem;
        }
        .accent-text-proposalC {
            color: #4CAF50;
            font-weight: 600;
        }
        .accent-text-challenge {
            color: #FFC107;
            font-weight: 600;
        }
        .accent-text-costly {
            color: #E53E3E;
            font-weight: 600;
        }

        .flowchart-step {
            background-color: #E8F5E9;
            border: 2px solid #4CAF50;
            color: #333333;
            padding: 0.4rem 0.6rem;
            border-radius: 6px;
            text-align: center;
            font-weight: 600;
            font-size: 0.8rem;
        }
        .flowchart-arrow {
            color: #4CAF50;
            font-size: 1.5rem;
            line-height: 1;
            font-weight: bold;
            margin: 0.15rem 0;
        }
        .comparison-table th, .comparison-table td {
            border: 1px solid #E0E0E0;
            padding: 0.8rem;
            text-align: left;
            vertical-align: top;
            font-size: 1rem; /* Adjusted font size for table */
        }
        .comparison-table th {
            background-color: #EEEEEE;
            font-weight: 600;
        }
        .chart-container {
            position: relative;
            width: 100%;
            max-width: 450px; /* Adjusted max-width for A3 column */
            margin-left: auto;
            margin-right: auto;
            height: 280px; /* Adjusted height for A3 column */
            max-height: 320px;
        }
        .list-item-custom {
            background-color: #fcfcfc; /* Lighter background for list items */
            padding: 8px;
            border-left: 4px solid #2196F3; /* Blue accent for list */
            margin-bottom: 6px;
            box-shadow: 0 1px 2px rgba(0,0,0,0.05);
            font-size: 1.1rem;
        }
        .list-item-custom h4 {
            font-size: 1rem;
        }
        .tooltip .tooltiptext {
            width: 200px; /* Adjusted tooltip width */
            margin-left: -100px;
            font-size: 0.8rem;
        }

        /* Specific layout for A3 pages */
        .a3-grid-2-col {
            display: grid;
            grid-template-columns: repeat(2, 1fr);
            gap: 10px;
            flex-grow: 1;
        }
        .a3-section-content {
            flex-grow: 1;
            display: flex;
            flex-direction: column;
            justify-content: flex-start;
        }
        .a3-section-content > .card {
            flex-grow: 0; /* Cards within A3 sections should not grow to fill all space unless needed */
            margin-bottom: 0; /* Remove default card margin, manage spacing with gap */
        }
        .a3-page-header {
            text-align: center;
            margin-bottom: 10px;
        }
        .a3-page-header h2 {
            font-size: 2.2rem;
            font-weight: bold;
            color: #333333;
            border-bottom: 3px solid #4CAF50;
            padding-bottom: 5px;
            display: inline-block;
            margin-bottom: 10px;
        }

        /* Styles copied from tech_commonality_analysis_jp for integration */
        .tech-column {
            display: flex;
            flex-direction: column;
            gap: 8px; /* Adjusted gap */
        }
        .tech-item {
            background-color: #F8F8F8;
            border: 1px solid #E0E0E0;
            border-radius: 6px;
            padding: 0.6rem; /* Adjusted padding */
            font-size: 0.8rem; /* Adjusted font size */
            text-align: left;
        }
        .tech-item strong {
            color: #555555;
        }
        .tech-group {
            margin-bottom: 12px; /* Adjusted margin */
            padding: 8px; /* Adjusted padding */
            border: 1px dashed #CCCCCC;
            border-radius: 8px;
            background-color: #FAFAFA;
        }
        .tech-group h4 {
            font-weight: 600;
            color: #4CAF50;
            margin-bottom: 6px; /* Adjusted margin */
            text-align: center;
            font-size: 0.95rem; /* Adjusted font size */
        }
        .commonality-link {
            display: flex;
            align-items: center;
            justify-content: center;
            margin: 8px 0; /* Adjusted margin */
            position: relative;
            z-index: 10;
        }
        .commonality-text {
            background-color: #2196F3; /* Blue accent */
            color: white;
            padding: 6px 12px; /* Adjusted padding */
            border-radius: 20px;
            font-weight: 600;
            font-size: 0.85rem; /* Adjusted font size */
            white-space: nowrap;
            box-shadow: 0 2px 5px rgba(0,0,0,0.2);
            position: relative;
            z-index: 20;
        }
        .commonality-line {
            position: absolute;
            height: 2px;
            background-color: #4CAF50; /* Green accent */
            z-index: 1;
        }
        .arrow-right {
            width: 0;
            height: 0;
            border-top: 5px solid transparent; /* Adjusted arrow size */
            border-bottom: 5px solid transparent;
            border-left: 8px solid #4CAF50;
            position: absolute;
            right: -8px;
            top: 50%;
            transform: translateY(-50%);
        }
        .arrow-left {
            width: 0;
            height: 0;
            border-top: 5px solid transparent; /* Adjusted arrow size */
            border-bottom: 5px solid transparent;
            border-right: 8px solid #4CAF50;
            position: absolute;
            left: -8px;
            top: 50%;
            transform: translateY(-50%);
        }
    </style>
</head>
<body class="antialiased">

    <header class="header-bg">
        <h1 class="text-3xl lg:text-4xl font-bold mb-2">高精度CNC加工機製造業向け 次世代統合管理システム</h1>
        <p class="text-md lg:text-lg opacity-90">TimePro・ODBC自動連携 × AI監視 × リアルタイム協業で実現する生産性革命</p>
    </header>

    <div class="a3-page">
        <div class="a3-page-header">
            <h2>1. 現状課題と統合システム導入提案の概要</h2>
        </div>
        
        <!-- 経営層向け要約 -->
        <div class="card bg-gradient-to-r from-blue-600 to-green-600 text-white p-3 mb-2">
            <h3 class="text-lg font-bold mb-2 text-center">🎯 経営層向け要約</h3>
            <div class="grid grid-cols-4 gap-2 text-center">
                <div class="bg-white bg-opacity-20 rounded-lg p-2">
                    <h4 class="font-bold text-sm mb-1">🎉 目的</h4>
                    <p class="text-xs">TimePro・ODBC自動化</p>
                </div>
                <div class="bg-white bg-opacity-20 rounded-lg p-2">
                    <h4 class="font-bold text-sm mb-1">📊 効果</h4>
                    <p class="text-xs">年間<strong>20,000工数削減</strong><br/><strong>4,000万円</strong>回避</p>
                </div>
                <div class="bg-white bg-opacity-20 rounded-lg p-2">
                    <h4 class="font-bold text-sm mb-1">💰 費用</h4>
                    <p class="text-xs"><strong>約200人日</strong><br/>外注比1/20</p>
                </div>
                <div class="bg-white bg-opacity-20 rounded-lg p-2">
                    <h4 class="font-bold text-sm mb-1">⏰ 納期</h4>
                    <p class="text-xs"><strong>2-3週間</strong><br/>即座に効果実感</p>
                </div>
            </div>
        </div>
        
        <div class="a3-grid-2-col">
            <div class="a3-section-content">
                <div class="card">
                    <h3 class="text-lg font-bold mb-3 text-center">現状の課題：CNC加工現場の隠れたコスト</h3>
                    <p class="text-center text-gray-700 mb-3 text-sm">TimePro・ODBCデータベースの手動操作が現場の生産性を大幅に低下</p>
                    <div class="grid grid-cols-1 gap-3 text-gray-700 flex-grow">
                        <div id="tab-legacy" class="tab-content active">
                            <h3 class="text-base font-semibold mb-2 text-center text-gray-800">TimePro・旧ODBCシステムの現場課題</h3>
                            <div class="grid md:grid-cols-2 gap-2 text-gray-700">
                                <div class="p-2 bg-red-50 rounded-lg border border-red-200">
                                    <h4 class="font-semibold text-red-700 mb-1 text-sm">⏰ 工時データ入力の非効率</h4>
                                    <p class="text-xs">1人当たり毎日10分の手動入力。600人×年間で<strong>20,000時間</strong>の工数ロス。</p>
                                </div>
                                <div class="p-2 bg-red-50 rounded-lg border border-red-200">
                                    <h4 class="font-semibold text-red-700 mb-1 text-sm">🔄 データ整合性の問題</h4>
                                    <p class="text-xs">TimePro↔ODBC間の手動同期でデータ不整合が発生。</p>
                                </div>
                                <div class="p-2 bg-red-50 rounded-lg border border-red-200">
                                    <h4 class="font-semibold text-red-700 mb-1 text-sm">💰 高いエラーコスト</h4>
                                    <p class="text-xs">手作業ミスによる再加工、納期遅延。<strong>月平均300万円</strong>の損失。</p>
                                </div>
                                <div class="p-2 bg-red-50 rounded-lg border border-red-200">
                                    <h4 class="font-semibold text-red-700 mb-1 text-sm">📊 リアルタイム性の欠如</h4>
                                    <p class="text-xs">加工進捗の把握が遅延し、効率的な生産管理が困難。</p>
                                </div>
                            </div>
                        </div>
                        <div id="tab-timepro" class="tab-content">
                            <h3 class="text-xl font-semibold mb-4 text-center text-gray-800">TimePro-XG 導入検討時の課題</h3>
                            <div class="grid md:grid-cols-2 gap-4 text-gray-700">
                                 <div class="p-4 bg-yellow-50 rounded-lg border border-yellow-300">
                                    <h4 class="font-semibold text-yellow-700 mb-1">🛠️ UI・機能カスタマイズ</h4>
                                    <p>パッケージ製品のため、UIの抜本的改善や独自機能追加は困難かつ<span class="accent-text-costly">高コスト</span>。</p>
                                </div>
                                <div class="p-4 bg-yellow-50 rounded-lg border border-yellow-300">
                                    <h4 class="font-semibold text-yellow-700 mb-1">🔗 システム連携</h4>
                                    <p>旧システムとの「深い」リアルタイム連携はCSVが主。根本解決に至らず。</p>
                                </div>
                                <div class="p-4 bg-yellow-50 rounded-lg border border-yellow-300">
                                    <h4 class="font-semibold text-yellow-700 mb-1">💰 導入・開発コスト</h4>
                                    <p>初期費用に加え、カスタマイズには<span class="accent-text-costly">数百万円～</span>、開発期間は<span class="accent-text-costly">半年以上</span>。</p>
                                </div>
                                <div class="p-4 bg-yellow-50 rounded-lg border border-yellow-300">
                                    <h4 class="font-semibold text-yellow-700 mb-1">🕰️ 技術的制約</h4>
                                    <p>一部機能は旧技術基盤 (IE11等) に依存の可能性があり、柔軟性に欠ける。</p>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <div class="a3-section-content">
                <div class="card">
                    <h3 class="text-xl font-bold mb-4 text-center"> 既存システム 操作流程</h3>
            
                                     
                                     
                    <div class="flex-grow flex flex-col justify-between">
                        <div class="mt-auto">
                            <h4 class="text-lg font-semibold mb-2 text-center text-gray-800">老旧系统操作的イメージ💡操作效率极低</h4>
                            <div class="space-y-1 flex flex-col items-center">
                                <div class="flowchart-step w-full sm:w-3/4">开机寻找，access，打开</div>
                                <div class="flowchart-arrow self-center">↓</div>
                                <div class="flowchart-step w-full sm:w-3/4">✨ 等待启动 ✨</div>
                                <div class="flowchart-arrow self-center">↓</div>
                                <div class="flex w-full sm:w-3/4 justify-around space-x-1">
                                     <div class="flowchart-step flex-1 text-xs">🔗 点击工时系统</div>
                                     <div class="flowchart-step flex-1 text-xs">🔗 点击检索</div>
                                     <div class="flowchart-step flex-1 text-xs">🔗 输入日期，点击</div>
                                    
                                    
                                </div>
                                <div class="flowchart-arrow self-center">↓</div>
                                <div class="flex w-full sm:w-3/4 justify-around space-x-1">
                                    <div class="flowchart-step flex-1 text-xs">等待结果，时间久，烦躁</div>
                                    <div class="flowchart-step flex-1 text-xs">🔗 点击id</div>
                                    <div class="flowchart-step flex-1 text-xs">输入id，点击ok</div>
                                    <div class="flowchart-step flex-1 text-xs">进入操作界面</div>
                                    
                                </div>
                                <div class="flowchart-arrow self-center">↓</div>
                                <div class="flex w-full sm:w-3/4 justify-around space-x-1">
                                    <div class="flowchart-step flex-1 text-xs">点击登录</div>                                    
                                    <div class="flowchart-step flex-1 text-xs">输入id，点击ok</div>
                                    <div class="flowchart-step flex-1 text-xs">进入输入界面</div>

                                    
                                </div>
                                <div class="flowchart-arrow self-center">↓</div>
                                <div class="flex w-full sm:w-3/4 justify-around space-x-1">
                                    <div class="flowchart-step flex-1 text-xs">输入1</div>                                    
                                    <div class="flowchart-step flex-1 text-xs">输入2</div>
                                    <div class="flowchart-step flex-1 text-xs">输入其他。。。。</div>
                                </div>
                                <div class="flowchart-arrow self-center">↓</div>
                                <div class="flex w-full sm:w-3/4 justify-around space-x-1">                                    
                                    <div class="flowchart-step flex-1 text-xs">其他。。。。</div>                                                                       
                                </div>
                            </div>
                            
                        </div>
                    </div>
                </div>
                <div class="card">
                    <h3 class="text-xl font-bold mb-3 text-center">統合システムの解決策</h3>
                    <div class="flex flex-col justify-center h-full">
                        <div class="space-y-3 text-center">
                            <div class="flowchart-step bg-green-100 border-green-300">
                                <strong>🚀 完全自動化</strong><br/>
                                PostgreSQL分散DB による TimePro ↔ ODBC 自動同期
                            </div>
                            <div class="flowchart-arrow self-center">↓</div>
                            <div class="flowchart-step bg-blue-100 border-blue-300">
                                <strong>⚡ 即効性</strong><br/>
                                手動入力<strong class="text-green-700">95%削減</strong> + データエラー<strong class="text-green-700">99%削減</strong>
                            </div>
                            <div class="flowchart-arrow self-center">↓</div>
                            <div class="flowchart-step bg-purple-100 border-purple-300">
                                <strong>💰 投資回収</strong><br/>
                                開発期間<strong class="text-blue-700">2-3週間</strong> + 年間<strong class="text-blue-700">4,000万円</strong>効果
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="a3-page">
        <div class="a3-page-header">
            <h2>2. なぜフルスタック開発が必要なのか？</h2>
        </div>
        
        <!-- 技術的専門性の説明 -->
        <div class="card bg-gradient-to-r from-purple-600 to-indigo-600 text-white p-3 mb-3">
            <h3 class="text-lg font-bold mb-2 text-center">⚙️ 単純なC言語プログラミングとの決定的違い</h3>
            <p class="text-center text-sm mb-3 font-semibold">CNC加工機向け統合システムには高度な専門技術群が必須</p>
            <div class="grid grid-cols-3 gap-3 text-sm">
                <div class="bg-white bg-opacity-20 rounded-lg p-2">
                    <h4 class="font-bold mb-1 text-sm">🏗️ システム基盤</h4>
                    <ul class="space-y-0 text-xs">
                        <li>• マイクロサービス設計</li>
                        <li>• PostgreSQL分散DB</li>
                        <li>• リアルタイム通信</li>
                        <li>• 認証・セキュリティ</li>
                    </ul>
                </div>
                <div class="bg-white bg-opacity-20 rounded-lg p-2">
                    <h4 class="font-bold mb-1 text-sm">🏭 工場特化</h4>
                    <ul class="space-y-0 text-xs">
                        <li>• TimePro・ODBC連携</li>
                        <li>• PLC制御・産業通信</li>
                        <li>• AI画像処理・YOLO</li>
                        <li>• エッジコンピューティング</li>
                    </ul>
                </div>
                <div class="bg-white bg-opacity-20 rounded-lg p-2">
                    <h4 class="font-bold mb-1 text-sm">💼 開発・運用</h4>
                    <ul class="space-y-0 text-xs">
                        <li>• UI/UX設計</li>
                        <li>• DevOps・CI/CD</li>
                        <li>• システム監視</li>
                        <li>• プロジェクト管理</li>
                    </ul>
                </div>
            </div>
            <p class="text-center text-xs mt-2 opacity-90 font-bold bg-white bg-opacity-20 rounded-lg p-2">
                これら<span class="text-yellow-300 text-sm">30+の専門技術</span>を組み合わせた総合的なアプローチ
            </p>
        </div>
        
        <div class="a3-grid-2-col">
            <div class="a3-section-content">
                <div class="card">
                    <h3 class="text-2xl font-bold mb-6 text-center">🔍 従来開発 vs フルスタック開発の違い</h3>
                    <div class="space-y-4">
                        <div class="bg-red-50 border border-red-200 rounded-lg p-4">
                            <h4 class="font-bold text-red-700 mb-3 text-lg">❌ 従来のC言語中心開発の限界</h4>
                            <ul class="space-y-2 text-sm">
                                <li class="flex items-start"><span class="text-red-500 mr-2">•</span><span><strong>単一機能実装</strong>：組込み制御のみ、システム統合なし</span></li>
                                <li class="flex items-start"><span class="text-red-500 mr-2">•</span><span><strong>データ孤立</strong>：各装置個別管理、全体最適化不可</span></li>
                                <li class="flex items-start"><span class="text-red-500 mr-2">•</span><span><strong>保守性低</strong>：ハードコード、変更困難</span></li>
                                <li class="flex items-start"><span class="text-red-500 mr-2">•</span><span><strong>拡張性なし</strong>：新機能追加でゼロから再開発</span></li>
                                <li class="flex items-start"><span class="text-red-500 mr-2">•</span><span><strong>ユーザビリティ不良</strong>：操作性・視認性を軽視</span></li>
                            </ul>
                        </div>
                        
                        <div class="bg-green-50 border border-green-200 rounded-lg p-4">
                            <h4 class="font-bold text-green-700 mb-3 text-lg">✅ フルスタック統合開発の優位性</h4>
                            <ul class="space-y-2 text-sm">
                                <li class="flex items-start"><span class="text-green-500 mr-2">•</span><span><strong>システム全体設計</strong>：エンドツーエンドの最適化</span></li>
                                <li class="flex items-start"><span class="text-green-500 mr-2">•</span><span><strong>データ統合活用</strong>：全工程データの一元管理・分析</span></li>
                                <li class="flex items-start"><span class="text-green-500 mr-2">•</span><span><strong>高保守性</strong>：モジュール設計、コード再利用</span></li>
                                <li class="flex items-start"><span class="text-green-500 mr-2">•</span><span><strong>柔軟拡張</strong>：新技術・新機能の迅速対応</span></li>
                                <li class="flex items-start"><span class="text-green-500 mr-2">•</span><span><strong>優れたUX</strong>：直感操作・情報可視化</span></li>
                            </ul>
                        </div>
                    </div>
                </div>
            </div>
            
            <div class="a3-section-content">
                <div class="card">
                    <h3 class="text-2xl font-bold mb-6 text-center">💡 なぜ社内技術蓄積が重要か？</h3>
                    <div class="space-y-4">
                        <div class="bg-blue-50 border border-blue-200 rounded-lg p-4">
                            <h4 class="font-bold text-blue-700 mb-3 text-lg">🧠 技術ノウハウ蓄積効果</h4>
                            <ul class="space-y-2 text-sm">
                                <li class="flex items-start"><span class="text-blue-500 mr-2">•</span><span><strong>CNC特化技術習得</strong>：業界特有の技術課題解決能力</span></li>
                                <li class="flex items-start"><span class="text-blue-500 mr-2">•</span><span><strong>設計パターン蓄積</strong>：製造業向けアーキテクチャ知識</span></li>
                                <li class="flex items-start"><span class="text-blue-500 mr-2">•</span><span><strong>運用ノウハウ</strong>：現場特有のトラブル対応経験</span></li>
                                <li class="flex items-start"><span class="text-blue-500 mr-2">•</span><span><strong>継続改善能力</strong>：PDCA回した進化的開発</span></li>
                            </ul>
                        </div>
                        
                        <div class="bg-purple-50 border border-purple-200 rounded-lg p-4">
                            <h4 class="font-bold text-purple-700 mb-3 text-lg">🚀 将来技術対応力</h4>
                            <ul class="space-y-2 text-sm">
                                <li class="flex items-start"><span class="text-purple-500 mr-2">•</span><span><strong>新技術即座導入</strong>：AI・IoT新技術の迅速活用</span></li>
                                <li class="flex items-start"><span class="text-purple-500 mr-2">•</span><span><strong>業界変化対応</strong>：Industry 4.0・スマートファクトリー</span></li>
                                <li class="flex items-start"><span class="text-purple-500 mr-2">•</span><span><strong>競合優位維持</strong>：独自技術による差別化継続</span></li>
                                <li class="flex items-start"><span class="text-purple-500 mr-2">•</span><span><strong>事業拡大基盤</strong>：技術力による新規事業展開</span></li>
                            </ul>
                        </div>
                        
                        <div class="bg-amber-50 border border-amber-200 rounded-lg p-4">
                            <h4 class="font-bold text-amber-700 mb-3 text-lg">💼 経営戦略価値</h4>
                            <ul class="space-y-2 text-sm">
                                <li class="flex items-start"><span class="text-amber-500 mr-2">•</span><span><strong>内製開発力</strong>：外部依存からの脱却</span></li>
                                <li class="flex items-start"><span class="text-amber-500 mr-2">•</span><span><strong>技術資産形成</strong>：知的財産としての価値蓄積</span></li>
                                <li class="flex items-start"><span class="text-amber-500 mr-2">•</span><span><strong>人材価値向上</strong>：エンジニアの市場価値向上</span></li>
                                <li class="flex items-start"><span class="text-amber-500 mr-2">•</span><span><strong>企業ブランド</strong>：技術先進企業としての認知</span></li>
                            </ul>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="bg-gradient-to-r from-indigo-600 to-purple-600 text-white p-3 mt-3 rounded-lg shadow-lg">
            <h3 class="text-lg font-bold text-center mb-2">🎯 フルスタック開発の結論</h3>
            <p class="text-center text-sm mb-2">
                単純なC言語では<strong class="text-red-300">実現不可能</strong>な統合システムを、
                <strong class="text-yellow-300">30+の先端技術</strong>で構築。
                <strong class="text-green-300">CNC製造業界での圧倒的優位性</strong>を確立。
            </p>
            <div class="text-center">
                <p class="text-sm font-semibold">フルスタック開発 = <span class="text-yellow-300">未来投資</span> + <span class="text-green-300">技術優位</span> + <span class="text-blue-300">競争力</span></p>
            </div>
        </div>
    </div>

    <div class="a3-page">
        <div class="a3-page-header">
            <h2>3. 統合システムの効果分析</h2>
        </div>
        <div class="a3-grid-2-col">
            <div class="a3-section-content">
                <div class="card">
                    <h3 class="text-2xl font-bold mb-6 text-center">統合システム：4つの核心機能による相乗効果</h3>
                    <p class="text-center text-gray-700 mb-6 text-base font-semibold">TimePro自動化 × リアルタイム協業 × AI監視 × PLC制御の統合で現場力を最大化</p>
                    <div class="flex-grow flex flex-col justify-between">
                        
                        <div class="mt-auto">
                            <h4 class="text-xl font-semibold mb-4 text-center text-gray-800">4つの核心機能統合システム</h4>
                            <div class="grid grid-cols-2 gap-4 text-sm">
                                <div class="p-4 bg-blue-50 rounded-lg border border-blue-300 text-center">
                                    <h5 class="font-bold text-blue-700 mb-2 text-base">🔄 TimePro・ODBC自動化</h5>
                                    <p>PostgreSQL分散DB経由でTimePro↔ODBC間の完全自動同期。手動入力工数を<strong>95%削減</strong></p>
                                </div>
                                <div class="p-4 bg-green-50 rounded-lg border border-green-300 text-center">
                                    <h5 class="font-bold text-green-700 mb-2 text-base">💬 リアルタイム協業</h5>
                                    <p>WebSocket技術による現場-管理間の即座情報共有。緊急対応時間を<strong>70%短縮</strong></p>
                                </div>
                                <div class="p-4 bg-purple-50 rounded-lg border border-purple-300 text-center">
                                    <h5 class="font-bold text-purple-700 mb-2 text-base">🤖 AI画像監視</h5>
                                    <p>YOLO技術による24時間自動品質・安全監視。検査工数<strong>80%削減</strong>、不良品<strong>90%削減</strong></p>
                                </div>
                                <div class="p-4 bg-orange-50 rounded-lg border border-orange-300 text-center">
                                    <h5 class="font-bold text-orange-700 mb-2 text-base">⚙️ PLC統合制御</h5>
                                    <p>現場PLCと管理システムの直接連携。設備稼働率<strong>15%向上</strong>、段取り時間<strong>50%短縮</strong></p>
                                </div>
                            </div>
                            <p class="text-sm text-center text-gray-600 mt-4 font-semibold">
                                4機能の相乗効果により、従来システムでは実現不可能な<strong class="text-green-600">総合生産性30%向上</strong>を達成
                            </p>
                        </div>
                    </div>
                </div> 
                <div class="card">
                    <h3 class="text-2xl font-bold mb-6 text-center">🚀 CNC製造現場が劇的に変わる！統合システムの驚きの効果</h3>
                    <p class="text-center text-gray-700 mb-6 text-base font-semibold">単なる工時入力自動化を遥かに超える、現場全体のデジタル変革</p>
                    
                    <!-- 視覚的インパクトのあるビフォー・アフター -->
                    <div class="grid grid-cols-2 gap-6 mb-6">
                        <div class="bg-red-50 border border-red-200 rounded-lg p-4">
                            <h4 class="text-lg font-bold text-red-700 mb-3 text-center">😰 導入前の現場</h4>
                            <div class="space-y-2 text-sm">
                                <div class="flex items-center text-sm"><span class="text-red-500 mr-2">❌</span><span>毎日10分×600人の手動工時入力</span></div>
                                <div class="flex items-center text-sm"><span class="text-red-500 mr-2">❌</span><span>TimePro↔ODBC手動同期でエラー頻発</span></div>
                                <div class="flex items-center text-sm"><span class="text-red-500 mr-2">❌</span><span>緊急時の電話・メール対応で混乱</span></div>
                                <div class="flex items-center text-sm"><span class="text-red-500 mr-2">❌</span><span>目視検査で不良品見逃し</span></div>
                                <div class="flex items-center text-sm"><span class="text-red-500 mr-2">❌</span><span>個別設備操作で段取り時間長</span></div>
                                <div class="flex items-center text-sm"><span class="text-red-500 mr-2">❌</span><span>データ分析は月末の手作業集計</span></div>
                            </div>
                        </div>
                        
                        <div class="bg-green-50 border border-green-200 rounded-lg p-4">
                            <h4 class="text-lg font-bold text-green-700 mb-3 text-center">😊 導入後の現場</h4>
                            <div class="space-y-2 text-sm">
                                <div class="flex items-center text-sm"><span class="text-green-500 mr-2">✅</span><span>PostgreSQL自動同期で工時入力ゼロ</span></div>
                                <div class="flex items-center text-sm"><span class="text-green-500 mr-2">✅</span><span>リアルタイムデータ整合性100%保証</span></div>
                                <div class="flex items-center text-sm"><span class="text-green-500 mr-2">✅</span><span>WebSocketチャットで即座情報共有</span></div>
                                <div class="flex items-center text-sm"><span class="text-green-500 mr-2">✅</span><span>AI画像監視で24時間品質保証</span></div>
                                <div class="flex items-center text-sm"><span class="text-green-500 mr-2">✅</span><span>PLC統合制御で設備連携自動化</span></div>
                                <div class="flex items-center text-sm"><span class="text-green-500 mr-2">✅</span><span>リアルタイムダッシュボードで即座分析</span></div>
                            </div>
                        </div>
                    </div>

                    <!-- 劇的な効果を数値で -->
                    <div class="bg-gradient-to-r from-yellow-400 to-orange-500 text-white rounded-lg p-4 mb-4">
                        <h4 class="text-lg font-bold text-center mb-3">💰 経営陣が喜ぶ確実な投資回収</h4>
                        <div class="grid grid-cols-3 gap-4 text-center">
                            <div>
                                <p class="text-2xl font-bold"><span class="animate-pulse">年間4,000万円</span></p>
                                <p class="text-sm">直接コスト削減</p>
                                <p class="text-xs">工数削減+エラー削減+効率向上</p>
                            </div>
                            <div>
                                <p class="text-2xl font-bold"><span class="animate-pulse">200人日</span></p>
                                <p class="text-sm">開発工数のみ</p>
                                <p class="text-xs">外注の1/20のコスト</p>
                            </div>
                            <div>
                                <p class="text-2xl font-bold"><span class="animate-pulse">2000%</span></p>
                                <p class="text-sm">投資回収率</p>
                                <p class="text-xs">1年で20倍のリターン</p>
                            </div>
                        </div>
                    </div>

                    <!-- 現場責任者が実感する変化 -->
                    <div class="bg-blue-50 border border-blue-200 rounded-lg p-3">
                        <h4 class="text-base font-bold text-blue-700 text-center mb-2">👥 現場責任者が実感する具体的変化</h4>
                        <div class="grid grid-cols-2 gap-3 text-xs">
                            <div>
                                <h5 class="font-bold text-blue-600 mb-1">🕐 時間短縮効果</h5>
                                <ul class="space-y-1">
                                    <li>• 朝礼：30分 → 5分</li>
                                    <li>• 段取り：60分 → 30分</li>
                                    <li>• 検査：20分 → 2分</li>
                                    <li>• 報告：2時間 → 0分</li>
                                </ul>
                            </div>
                            <div>
                                <h5 class="font-bold text-blue-600 mb-1">📊 品質向上効果</h5>
                                <ul class="space-y-1">
                                    <li>• 不良品率：2% → 0.2%</li>
                                    <li>• データエラー：20% → 0.1%</li>
                                    <li>• 設備稼働率：75% → 90%</li>
                                    <li>• 緊急対応：2時間 → 15分</li>
                                </ul>
                            </div>
                        </div>
                    </div>
                </div>               
             
            </div>

            <div class="a3-section-content">
                <div class="card">
                    <h3 class="text-xl font-bold mb-4 text-center">⚡ 4つの革新機能：現場が実感する劇的変化</h3>
                    <p class="text-center text-gray-700 mb-4 text-sm font-semibold">従業員・現場責任者・経営陣、全員が納得する確実な改善効果</p>
                    
                    <!-- 機能別カード形式で視覚的に -->
                    <div class="space-y-3 flex-grow">
                        <!-- TimePro・ODBC -->
                        <div class="bg-gradient-to-r from-blue-100 to-blue-200 rounded-lg p-3 border border-blue-300">
                            <div class="flex items-center mb-2">
                                <span class="text-xl mr-2">🔄</span>
                                <h4 class="font-bold text-blue-800 text-base">TimePro・ODBC完全自動化</h4>
                            </div>
                            <div class="grid grid-cols-3 gap-1 text-sm">
                                <div class="bg-red-50 p-2 rounded text-center">
                                    <p class="font-bold text-red-700">導入前</p>
                                    <p>毎日10分/人の手動入力</p>
                                    <p>エラー率20%</p>
                                </div>
                                <div class="bg-green-50 p-2 rounded text-center">
                                    <p class="font-bold text-green-700">導入後</p>
                                    <p>PostgreSQL自動同期</p>
                                    <p>エラー率0.1%</p>
                                </div>
                                <div class="bg-yellow-50 p-2 rounded text-center font-bold">
                                    <p class="text-yellow-700">効果</p>
                                    <p class="text-lg text-green-600">年間20,000時間削減</p>
                                </div>
                            </div>
                        </div>

                        <!-- リアルタイム協業 -->
                        <div class="bg-gradient-to-r from-green-100 to-green-200 rounded-lg p-4 border border-green-300">
                            <div class="flex items-center mb-2">
                                <span class="text-2xl mr-3">💬</span>
                                <h4 class="font-bold text-green-800">リアルタイム協業プラットフォーム</h4>
                            </div>
                            <div class="grid grid-cols-3 gap-2 text-sm">
                                <div class="bg-red-50 p-2 rounded text-center">
                                    <p class="font-bold text-red-700">導入前</p>
                                    <p>電話・メール依存</p>
                                    <p>緊急対応2時間</p>
                                </div>
                                <div class="bg-green-50 p-2 rounded text-center">
                                    <p class="font-bold text-green-700">導入後</p>
                                    <p>WebSocket即時通信</p>
                                    <p>緊急対応15分</p>
                                </div>
                                <div class="bg-yellow-50 p-2 rounded text-center font-bold">
                                    <p class="text-yellow-700">効果</p>
                                    <p class="text-lg text-green-600">対応時間88%短縮</p>
                                </div>
                            </div>
                        </div>

                        <!-- AI画像監視 -->
                        <div class="bg-gradient-to-r from-purple-100 to-purple-200 rounded-lg p-4 border border-purple-300">
                            <div class="flex items-center mb-2">
                                <span class="text-2xl mr-3">🤖</span>
                                <h4 class="font-bold text-purple-800">AI画像監視・品質保証システム</h4>
                            </div>
                            <div class="grid grid-cols-3 gap-2 text-sm">
                                <div class="bg-red-50 p-2 rounded text-center">
                                    <p class="font-bold text-red-700">導入前</p>
                                    <p>人的目視検査</p>
                                    <p>不良品率2%</p>
                                </div>
                                <div class="bg-green-50 p-2 rounded text-center">
                                    <p class="font-bold text-green-700">導入後</p>
                                    <p>YOLO AI 24時間監視</p>
                                    <p>不良品率0.2%</p>
                                </div>
                                <div class="bg-yellow-50 p-2 rounded text-center font-bold">
                                    <p class="text-yellow-700">効果</p>
                                    <p class="text-lg text-green-600">不良品90%削減</p>
                                </div>
                            </div>
                        </div>

                        <!-- PLC統合制御 -->
                        <div class="bg-gradient-to-r from-orange-100 to-orange-200 rounded-lg p-4 border border-orange-300">
                            <div class="flex items-center mb-2">
                                <span class="text-2xl mr-3">⚙️</span>
                                <h4 class="font-bold text-orange-800">PLC統合制御・設備最適化</h4>
                            </div>
                            <div class="grid grid-cols-3 gap-2 text-sm">
                                <div class="bg-red-50 p-2 rounded text-center">
                                    <p class="font-bold text-red-700">導入前</p>
                                    <p>個別設備操作</p>
                                    <p>稼働率75%</p>
                                </div>
                                <div class="bg-green-50 p-2 rounded text-center">
                                    <p class="font-bold text-green-700">導入後</p>
                                    <p>統合制御システム</p>
                                    <p>稼働率90%</p>
                                </div>
                                <div class="bg-yellow-50 p-2 rounded text-center font-bold">
                                    <p class="text-yellow-700">効果</p>
                                    <p class="text-lg text-green-600">稼働率20%向上</p>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <div class="mt-4 bg-gradient-to-r from-green-500 to-blue-500 text-white rounded-lg p-3 text-center">
                        <p class="font-bold">🎯 相乗効果：<span class="text-yellow-300">4機能統合により総合生産性30%向上</span></p>
                        <p class="text-sm">個別導入では絶対に実現不可能な包括的変革を短期間・低コストで達成</p>
                    </div>
                </div>
                <div class="card">
                    <h3 class="text-xl font-bold mb-4 text-center">💎 隠れた価値：工時入力以外の驚くべき効果</h3>
                    <p class="text-center text-gray-700 mb-4 text-sm font-semibold">経営陣が見落としがちな、統合システムの真の戦略的価値</p>
                    
                    <div class="grid grid-cols-2 gap-4 mb-6">
                        <div class="bg-gradient-to-br from-indigo-100 to-indigo-200 rounded-lg p-4 border border-indigo-300">
                            <h4 class="font-bold text-indigo-800 mb-3 text-center">🧠 知的資産・技術力蓄積</h4>
                            <ul class="text-sm space-y-2">
                                <li class="flex items-start"><span class="text-indigo-600 mr-2">•</span><span><strong>社内フルスタック技術力</strong>：将来の技術変化に自社で対応可能</span></li>
                                <li class="flex items-start"><span class="text-indigo-600 mr-2">•</span><span><strong>設計ノウハウ蓄積</strong>：CNC特化システム設計の専門知識獲得</span></li>
                                <li class="flex items-start"><span class="text-indigo-600 mr-2">•</span><span><strong>競合優位技術</strong>：同業他社では真似できない技術基盤</span></li>
                                <li class="flex items-start"><span class="text-indigo-600 mr-2">•</span><span><strong>人材価値向上</strong>：最新技術に精通したエンジニア育成</span></li>
                            </ul>
                        </div>
                        
                        <div class="bg-gradient-to-br from-teal-100 to-teal-200 rounded-lg p-4 border border-teal-300">
                            <h4 class="font-bold text-teal-800 mb-3 text-center">🚀 将来拡張・事業機会</h4>
                            <ul class="text-sm space-y-2">
                                <li class="flex items-start"><span class="text-teal-600 mr-2">•</span><span><strong>顧客向けシステム提供</strong>：技術を他社に販売する新事業</span></li>
                                <li class="flex items-start"><span class="text-teal-600 mr-2">•</span><span><strong>IoT・Industry 4.0対応</strong>：次世代製造業への確実な準備</span></li>
                                <li class="flex items-start"><span class="text-teal-600 mr-2">•</span><span><strong>AI技術の横展開</strong>：品質管理以外への応用拡大</span></li>
                                <li class="flex items-start"><span class="text-teal-600 mr-2">•</span><span><strong>データ活用ビジネス</strong>：蓄積データから新たな価値創出</span></li>
                            </ul>
                        </div>
                        
                        <div class="bg-gradient-to-br from-rose-100 to-rose-200 rounded-lg p-4 border border-rose-300">
                            <h4 class="font-bold text-rose-800 mb-3 text-center">🛡️ リスク軽減・安定性</h4>
                            <ul class="text-sm space-y-2">
                                <li class="flex items-start"><span class="text-rose-600 mr-2">•</span><span><strong>ベンダーロックイン回避</strong>：外部依存からの完全脱却</span></li>
                                <li class="flex items-start"><span class="text-rose-600 mr-2">•</span><span><strong>セキュリティ完全制御</strong>：社内情報の外部流出防止</span></li>
                                <li class="flex items-start"><span class="text-rose-600 mr-2">•</span><span><strong>カスタマイズ自由度</strong>：ビジネス変化への即座対応</span></li>
                                <li class="flex items-start"><span class="text-rose-600 mr-2">•</span><span><strong>コスト予測可能性</strong>：予期せぬライセンス費用ゼロ</span></li>
                            </ul>
                        </div>
                        
                        <div class="bg-gradient-to-br from-amber-100 to-amber-200 rounded-lg p-4 border border-amber-300">
                            <h4 class="font-bold text-amber-800 mb-3 text-center">📈 組織・経営効果</h4>
                            <ul class="text-sm space-y-2">
                                <li class="flex items-start"><span class="text-amber-600 mr-2">•</span><span><strong>意思決定の高速化</strong>：リアルタイムデータで即座判断</span></li>
                                <li class="flex items-start"><span class="text-amber-600 mr-2">•</span><span><strong>従業員満足度向上</strong>：単純作業削減で創造的業務へ</span></li>
                                <li class="flex items-start"><span class="text-amber-600 mr-2">•</span><span><strong>顧客信頼度向上</strong>：品質安定・納期厳守の確実性</span></li>
                                <li class="flex items-start"><span class="text-amber-600 mr-2">•</span><span><strong>企業ブランド価値</strong>：技術先進企業としての市場認知</span></li>
                            </ul>
                        </div>
                    </div>
                    
                    <div class="bg-gradient-to-r from-purple-600 to-pink-600 text-white rounded-lg p-4 text-center">
                        <h4 class="font-bold text-lg mb-2">💡 これらの価値は工時入力節約効果を遥かに上回る戦略的投資効果</h4>
                        <p class="text-sm">年間4,000万円の直接効果に加え、将来の事業機会・技術蓄積・競争優位確立により、<strong class="text-yellow-300">長期的に数億円規模の企業価値向上</strong>を実現</p>
                    </div>
                </div>
                
                                <div class="card">
                    <h3 class="text-xl font-bold mb-4 text-center">⚖️ 開発アプローチ比較：なぜ統合システムが最適か</h3>                    
                    <p class="text-center text-gray-700 mb-4 text-sm font-semibold">3つの選択肢の決定的違い：経営判断のための明確な指標</p>
                    
                    <!-- 視覚的な比較チャート -->
                    <div class="bg-gray-50 rounded-lg p-3 mb-4">
                        <div class="grid grid-cols-4 gap-1 text-center text-xs font-bold mb-2">
                            <div class="text-gray-600">評価項目</div>
                            <div class="text-red-600">TimePro-XG購入</div>
                            <div class="text-purple-600">完全自社開発</div>
                            <div class="text-green-600">統合システム提案</div>
                        </div>
                        
                        <!-- コスト比較バー -->
                        <div class="mb-2">
                            <div class="text-sm font-semibold mb-1">💰 初期コスト</div>
                            <div class="grid grid-cols-4 gap-1 items-center">
                                <div class="text-xs text-gray-600">想定費用</div>
                                <div class="bg-red-500 h-5 rounded flex items-center justify-center text-white text-xs">1,000万円</div>
                                <div class="bg-purple-500 h-6 rounded flex items-center justify-center text-white text-xs">1,500万円</div>
                                <div class="bg-green-500 h-2 rounded flex items-center justify-center text-white text-xs">50万円</div>
                            </div>
                        </div>
                        
                        <!-- 期間比較バー -->
                        <div class="mb-2">
                            <div class="text-sm font-semibold mb-1">⏱️ 開発期間</div>
                            <div class="grid grid-cols-4 gap-1 items-center">
                                <div class="text-xs text-gray-600">必要期間</div>
                                <div class="bg-red-400 h-5 rounded flex items-center justify-center text-white text-xs">6ヶ月以上</div>
                                <div class="bg-purple-400 h-6 rounded flex items-center justify-center text-white text-xs">12ヶ月以上</div>
                                <div class="bg-green-400 h-2 rounded flex items-center justify-center text-white text-xs">2-3週間</div>
                            </div>
                        </div>
                        
                        <!-- リスク比較バー -->
                        <div>
                            <div class="text-sm font-semibold mb-1">⚠️ 失敗リスク</div>
                            <div class="grid grid-cols-4 gap-1 items-center">
                                <div class="text-xs text-gray-600">リスク度</div>
                                <div class="bg-yellow-500 h-4 rounded flex items-center justify-center text-white text-xs">中リスク</div>
                                <div class="bg-red-600 h-5 rounded flex items-center justify-center text-white text-xs">高リスク</div>
                                <div class="bg-green-600 h-2 rounded flex items-center justify-center text-white text-xs">低リスク</div>
                            </div>
                        </div>
                    </div>
                    
                    <!-- 決定要因の強調 -->
                    <div class="bg-green-50 border border-green-200 rounded-lg p-4 mb-4">
                        <h4 class="font-bold text-green-800 text-center mb-2">🎯 統合システム提案が圧倒的に有利な理由</h4>
                        <div class="grid grid-cols-3 gap-4 text-sm">
                            <div class="text-center">
                                <span class="text-2xl">⚡</span>
                                <p class="font-bold text-green-700">超高速実装</p>
                                <p>既存技術活用で2-3週間</p>
                            </div>
                            <div class="text-center">
                                <span class="text-2xl">💎</span>
                                <p class="font-bold text-green-700">超低コスト</p>
                                <p>外注の1/20の費用対効果</p>
                            </div>
                            <div class="text-center">
                                <span class="text-2xl">🛡️</span>
                                <p class="font-bold text-green-700">超低リスク</p>
                                <p>実証済み技術の横展開</p>
                            </div>
                        </div>
                    </div>

                    <div class="overflow-x-auto flex-grow">
                        <table class="w-full comparison-table text-sm">
                            <thead>
                                <tr>
                                    <th class="w-1/4">評価軸</th>
                                    <th class="w-1/4 accent-text-costly">TimePro-XG購入<br>+フルカスタマイズ</th>
                                    <th class="w-1/4 text-purple-600 font-semibold">一般的完全自社開発<br>(伴随技术突破的开发構築)</th>
                                    <th class="w-1/4 accent-text-proposalC">工时数据提案<br>(已有技术的横向開発)</th>
                                </tr>
                            </thead>
                            <tbody>
                                <tr>
                                    <td>初期コスト</td>
                                    <td class="accent-text-costly">高～非常に高い</td>
                                    <td class="accent-text-costly">非常に高い</td>
                                    <td class="accent-text-proposalC">低い</td>
                                </tr>
                                <tr>
                                    <td>開発期間</td>
                                    <td class="accent-text-costly">長い</td>
                                    <td class="accent-text-costly">非常に長い</td>
                                    <td class="accent-text-proposalC">非常に短い</td>
                                </tr>
                                <tr>
                                    <td>UI/UX柔軟性</td>
                                    <td class="accent-text-costly">低い</td>
                                    <td class="accent-text-proposalC">非常に高い</td>
                                    <td class="accent-text-proposalC">高い</td>
                                </tr>
                                <tr>
                                    <td>既存システム連携</td>
                                    <td class="accent-text-costly">限定的</td>
                                    <td class="accent-text-proposalC">非常に高い</td>
                                    <td class="accent-text-proposalC">非常に高い</td>
                                </tr>
                                 <tr>
                                    <td>リスク</td>
                                    <td class="accent-text-costly">ベンダー依存、高額ロックイン</td>
                                    <td class="text-purple-600">一般的プロジェクト頓挫、理论技术瓶颈</td>
                                    <td class="accent-text-proposalC">横向技术活用(既存活用)</td>
                                </tr>
                                <tr>
                                    <td>社内技術力蓄積</td>
                                    <td class="accent-text-costly">限定的</td>
                                    <td class="非常に高い">高い</td>
                                    <td class="accent-text-proposalC">高い</td>
                                </tr>
                            </tbody>
                        </table>
                    </div>
                    <p class="mt-4 text-center text-gray-600 text-sm">工时数据提案は、<strong class="accent-text-proposalC">低リスク・低コスト・短期間</strong>で、<strong class="accent-text-proposalC">最大の効果</strong>を生み出す現実的な選択肢です。</p>
                </div>

            </div>
        </div>
        
        <!-- 第三页の強力な結論 -->
        <div class="bg-gradient-to-r from-green-600 to-blue-600 text-white p-3 mt-3 rounded-lg shadow-lg">
            <h3 class="text-lg font-bold text-center mb-2">🎯 今すぐ行動すべき理由</h3>
            <div class="grid grid-cols-3 gap-3 text-center">
                <div class="bg-white bg-opacity-20 rounded-lg p-2">
                    <span class="text-xl">💰</span>
                    <h4 class="font-bold text-sm mb-1">経済的妥当性</h4>
                    <p class="text-xs">年間4,000万円削減<br/>⇒ <strong class="text-yellow-300">投資回収率2,000%</strong></p>
                </div>
                <div class="bg-white bg-opacity-20 rounded-lg p-2">
                    <span class="text-xl">⚡</span>
                    <h4 class="font-bold text-sm mb-1">実現可能性</h4>
                    <p class="text-xs">2-3週間で効果実感<br/>⇒ <strong class="text-yellow-300">失敗リスクほぼゼロ</strong></p>
                </div>
                <div class="bg-white bg-opacity-20 rounded-lg p-2">
                    <span class="text-xl">🚀</span>
                    <h4 class="font-bold text-sm mb-1">戦略的価値</h4>
                    <p class="text-xs">競合優位技術確立<br/>⇒ <strong class="text-yellow-300">永続的競争力獲得</strong></p>
                </div>
            </div>
            <div class="text-center mt-2">
                <p class="text-sm font-bold"><span class="text-yellow-300">CNC製造業界でのポジション確立</span>のための戦略的投資</p>
            </div>
        </div>
    </div>

    <div class="a3-page">
        <div class="a3-page-header">
            <h2>4. 技術概要：なぜ専門的なシステム開発が必要か</h2>
        </div>
        
        <div class="a3-grid-2-col gap-8">
            <div class="a3-section-content">
                <div class="card">
                    <h3 class="text-2xl font-bold mb-6 text-center">システムアーキテクチャ概要</h3>
                    <p class="text-center text-gray-700 mb-4 text-base">安定的かつ拡張性の高い「マイクロサービスアーキテクチャ」を採用。将来の機能追加にも柔軟に対応可能です。</p>
                    
                    <div class="bg-gray-50 rounded-lg p-4 mb-4">
                        <h4 class="font-bold text-gray-700 mb-3 text-center">🏗️ 4層アーキテクチャ構成</h4>
                        <div class="grid grid-cols-1 gap-3 text-sm">
                            <div class="bg-blue-100 p-3 rounded border border-blue-300">
                                <h5 class="font-bold text-blue-800 mb-1">📱 クライアント操作層</h5>
                                <p class="text-xs">統合ランチャー・従業員操作画面・PLC制御ツール</p>
                            </div>
                            <div class="bg-green-100 p-3 rounded border border-green-300">
                                <h5 class="font-bold text-green-800 mb-1">🔄 マイクロサービス群</h5>
                                <p class="text-xs">データ同期・リアルタイム通信・認証・AI監視の4サービス</p>
                            </div>
                            <div class="bg-purple-100 p-3 rounded border border-purple-300">
                                <h5 class="font-bold text-purple-800 mb-1">🗄️ データベース層</h5>
                                <p class="text-xs">PostgreSQL分散DB・Redis高速キャッシュ・MongoDB</p>
                            </div>
                            <div class="bg-orange-100 p-3 rounded border border-orange-300">
                                <h5 class="font-bold text-orange-800 mb-1">🏭 外部システム連携</h5>
                                <p class="text-xs">TimePro・旧MDB・PLC・センサー・AIカメラ</p>
                            </div>
                        </div>
                    </div>
                    
                    <div class="bg-gradient-to-r from-indigo-100 to-indigo-200 p-4 rounded-lg">
                        <h4 class="font-bold text-indigo-800 mb-2 text-center">🚀 アーキテクチャの優位性</h4>
                        <ul class="text-sm space-y-1">
                            <li class="flex items-start"><span class="text-indigo-600 mr-2">•</span><span><strong>高可用性</strong>：サービス分離によりシステム全体の安定性確保</span></li>
                            <li class="flex items-start"><span class="text-indigo-600 mr-2">•</span><span><strong>拡張性</strong>：新機能追加時に既存サービスに影響なし</span></li>
                            <li class="flex items-start"><span class="text-indigo-600 mr-2">•</span><span><strong>保守性</strong>：モジュール化により障害対応・機能改良が容易</span></li>
                            <li class="flex items-start"><span class="text-indigo-600 mr-2">•</span><span><strong>性能</strong>：分散処理によりリアルタイム応答を実現</span></li>
                        </ul>
                    </div>
                </div>
            </div>
            
            <div class="a3-section-content">
                <div class="card">
                    <h3 class="text-2xl font-bold mb-6 text-center">なぜ「フルスタック開発」が必要なのか？</h3>
                    <p class="text-center text-gray-700 mb-4 text-base">CNC加工現場のシステムは、単一のプログラミング言語知識だけでは構築不可能です。</p>
                    
                    <div class="grid grid-cols-1 gap-4 mb-4">
                        <div class="bg-blue-50 border border-blue-200 rounded-lg p-4">
                            <h4 class="font-bold text-blue-800 mb-3">🏗️ システム基盤技術</h4>
                            <div class="grid grid-cols-2 gap-2 text-sm">
                                <ul class="space-y-1">
                                    <li>• マイクロサービス設計</li>
                                    <li>• データベース設計(SQL/NoSQL)</li>
                                    <li>• API設計・RESTful設計</li>
                                </ul>
                                <ul class="space-y-1">
                                    <li>• 認証・認可システム</li>
                                    <li>• 高並行性・分散処理</li>
                                    <li>• セキュリティ・暗号化</li>
                                </ul>
                            </div>
                        </div>
                        
                        <div class="bg-green-50 border border-green-200 rounded-lg p-4">
                            <h4 class="font-bold text-green-800 mb-3">🏭 工場特化技術</h4>
                            <div class="grid grid-cols-2 gap-2 text-sm">
                                <ul class="space-y-1">
                                    <li>• PLCプロトコル通信</li>
                                    <li>• リアルタイム制御</li>
                                    <li>• IIoTデータ収集</li>
                                </ul>
                                <ul class="space-y-1">
                                    <li>• AI・画像処理アルゴリズム</li>
                                    <li>• 工作機械API連携</li>
                                    <li>• エッジコンピューティング</li>
                                </ul>
                            </div>
                        </div>
                        
                        <div class="bg-purple-50 border border-purple-200 rounded-lg p-4">
                            <h4 class="font-bold text-purple-800 mb-3">💻 アプリ開発技術</h4>
                            <div class="grid grid-cols-2 gap-2 text-sm">
                                <ul class="space-y-1">
                                    <li>• UI/フロントエンド開発</li>
                                    <li>• サーバーサイド開発</li>
                                    <li>• リアルタイム通信</li>
                                </ul>
                                <ul class="space-y-1">
                                    <li>• 要求分析・要件定義</li>
                                    <li>• 自動テスト・CI/CD</li>
                                    <li>• プロジェクト管理</li>
                                </ul>
                            </div>
                        </div>
                    </div>
                    
                    <div class="bg-gradient-to-r from-amber-100 to-amber-200 p-4 rounded-lg">
                        <p class="text-center text-lg font-bold text-amber-800 mb-2">🎯 技術統合の価値</p>
                        <p class="text-center text-base text-amber-700">
                            これら<strong class="text-red-600">30以上</strong>の専門技術を統合する
                            <strong class="text-blue-600">「研究開発能力」+「企画・設計能力」+「実装能力」</strong>が、
                            真に価値あるシステムを実現します。
                        </p>
                    </div>
                </div>
                
                <div class="card">
                    <h3 class="text-xl font-bold mb-4 text-center">🔍 従来開発 vs フルスタック開発</h3>
                    <div class="space-y-4">
                        <div class="bg-red-50 border border-red-200 rounded-lg p-4">
                            <h4 class="font-bold text-red-700 mb-2">❌ 従来のC言語中心開発の限界</h4>
                            <ul class="space-y-1 text-sm">
                                <li>• <strong>単一機能実装</strong>：組込み制御のみ、システム統合なし</li>
                                <li>• <strong>データ孤立</strong>：各装置個別管理、全体最適化不可</li>
                                <li>• <strong>保守性低</strong>：ハードコード、変更困難</li>
                                <li>• <strong>拡張性なし</strong>：新機能追加でゼロから再開発</li>
                            </ul>
                        </div>
                        
                        <div class="bg-green-50 border border-green-200 rounded-lg p-4">
                            <h4 class="font-bold text-green-700 mb-2">✅ フルスタック統合開発の優位性</h4>
                            <ul class="space-y-1 text-sm">
                                <li>• <strong>システム全体設計</strong>：エンドツーエンドの最適化</li>
                                <li>• <strong>データ統合活用</strong>：全工程データの一元管理・分析</li>
                                <li>• <strong>高保守性</strong>：モジュール設計、コード再利用</li>
                                <li>• <strong>柔軟拡張</strong>：新技術・新機能の迅速対応</li>
                            </ul>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="a3-page">
        <div class="a3-page-header">
            <h2>5. 統合システムUI実装と技術アーキテクチャ</h2>
        </div>
        <div class="flex flex-col h-full">
            <div class="card flex-grow">
                <h3 class="text-xl font-bold mb-4 text-center">CNC製造業特化統合インターフェース</h3>
                <p class="text-center text-gray-700 mb-4 text-sm">TimePro・ODBC・AI監視・PLC制御を一元管理する直感的操作画面</p>
                
                <div class="grid grid-cols-2 gap-6 mb-4">
                    <div class="p-4 bg-blue-50 rounded-lg border border-blue-300">
                        <h4 class="font-bold text-blue-700 mb-2">🔄 TimePro・ODBC統合ダッシュボード</h4>
                        <ul class="text-sm space-y-1">
                            <li>• PostgreSQL分散DB経由リアルタイム同期状態表示</li>
                            <li>• 工時データ差異検出・自動校正履歴</li>
                            <li>• エラー統計・処理効率可視化</li>
                        </ul>
                    </div>
                    <div class="p-4 bg-green-50 rounded-lg border border-green-300">
                        <h4 class="font-bold text-green-700 mb-2">💬 現場協業コミュニケーション</h4>
                        <ul class="text-sm space-y-1">
                            <li>• WebSocket即時チャット・ファイル共有</li>
                            <li>• 加工進捗・品質情報リアルタイム配信</li>
                            <li>• 緊急時アラート・対応履歴管理</li>
                        </ul>
                    </div>
                    <div class="p-4 bg-purple-50 rounded-lg border border-purple-300">
                        <h4 class="font-bold text-purple-700 mb-2">🤖 AI画像監視制御</h4>
                        <ul class="text-sm space-y-1">
                            <li>• YOLO技術多視点品質・安全監視</li>
                            <li>• 異常検出即時通知・自動記録</li>
                            <li>• 検査結果統計・品質傾向分析</li>
                        </ul>
                    </div>
                    <div class="p-4 bg-orange-50 rounded-lg border border-orange-300">
                        <h4 class="font-bold text-orange-700 mb-2">⚙️ PLC統合制御センター</h4>
                        <ul class="text-sm space-y-1">
                            <li>• 複数CNC設備統一操作インターフェース</li>
                            <li>• 設備稼働状況・メンテナンス管理</li>
                            <li>• 生産スケジュール・効率最適化</li>
                        </ul>
                    </div>
                </div>

                <div class="bg-gradient-to-r from-gray-100 to-gray-200 p-4 rounded-lg">
                    <h4 class="font-bold text-gray-700 mb-2 text-center">🏗️ 技術アーキテクチャの価値</h4>
                    <p class="text-sm text-gray-600 text-center">
                        マイクロサービス・分散DB・リアルタイム通信・AI・セキュリティ設計など<strong class="text-blue-600">30+の高度技術</strong>を統合し、
                        CNC製造業の複雑な要求に対応する<strong class="text-green-600">エンタープライズ級システム</strong>を構築
                    </p>
                </div>
            </div>
        </div>
    </div>
    

    <div class="a3-page">
        <div class="a3-page-header">
            <h2>6. 導入効果と将来展望</h2>
        </div>
        <div class="flex flex-col h-full">
            <div class="card flex-grow">
                <h3 class="text-xl font-bold mb-4 text-center">CNC製造業の競争力強化：短期・長期効果</h3>
                <p class="text-center text-gray-700 mb-6 text-sm">統合システムが実現する、現場の即効性から企業戦略まで段階的な価値創出</p>
                <div class="grid md:grid-cols-3 gap-4 flex-grow">
                    <div class="p-3 bg-blue-50 rounded-lg text-center flex flex-col justify-between">
                        <span class="text-4xl">📊</span>
                        <h4 class="text-lg font-semibold mt-2 mb-1 text-blue-700">データ駆動型経営基盤</h4>
                        <p class="text-sm text-gray-600">TimePro・ODBC統合により、機械設計・要素開発・制御設計・生産最適化・コスト計算・技術改良の迅速で正確な意思決定を実現。製造業DXの基盤を構築。</p>
                    </div>
                    <div class="p-3 bg-green-50 rounded-lg text-center flex flex-col justify-between">
                        <span class="text-4xl">💡</span>
                        <h4 class="text-lg font-semibold mt-2 mb-1 text-green-700">社内技術力の飛躍的強化</h4>
                        <p class="text-sm text-gray-600">フルスタック内製開発により、高精度制御技術・加工技術・AI技術・システム設計力を蓄積。将来の変化に柔軟かつ迅速に対応できる技術基盤を確立。</p>
                    </div>
                    <div class="p-3 bg-yellow-50 rounded-lg text-center flex flex-col justify-between">
                        <span class="text-4xl">🚀</span>
                        <h4 class="text-lg font-semibold mt-2 mb-1 text-yellow-700">次世代製造業への転換</h4>
                        <p class="text-sm text-gray-600">AI・IoT・画像技術・制御理論・各種アルゴリズムの先端技術活用により、熟練技能のデジタル化と高精度生産管理を実現。Industry 4.0の先駆者となる。</p>
                    </div>
                </div>
            </div>

            <div class="card bg-gradient-to-r from-green-500 to-blue-600 text-white p-6 text-center mt-auto">
                <h3 class="text-2xl font-bold mb-3">戦略的投資判断：今こそ始める競争優位確立</h3>
                <p class="text-md mb-4">統合システムは単なる効率化ツールではなく、<strong>CNC加工機製造業界での圧倒的な競争優位</strong>を確立するための戦略的投資です。短期間で効果を実感しながら、長期的な企業価値向上を実現しましょう。</p>
                <div class="grid grid-cols-3 gap-4 text-sm">
                    <div class="bg-white bg-opacity-20 rounded p-2">
                        <strong>即効性</strong><br/>2-3週間で効果実感
                    </div>
                    <div class="bg-white bg-opacity-20 rounded p-2">
                        <strong>確実性</strong><br/>既存技術活用でリスク最小
                    </div>
                    <div class="bg-white bg-opacity-20 rounded p-2">
                        <strong>拡張性</strong><br/>将来ニーズに柔軟対応
                    </div>
                </div>
            </div>
        </div>
    </div>




    
    <footer class="footer-bg">
        <p class="text-xs">このインタラクティブ分析は「高精度CNC加工機製造業向け統合管理システム導入提案書」および関連技術調査に基づき作成されました。</p>
        <p class="text-xs">&copy; <span id="currentYear"></span> [貴社名]. All rights reserved.</p>
    </footer>

    <script>
        document.getElementById('currentYear').textContent = new Date().getFullYear();

        // Chart.js: Cost Benefit Chart
        const costBenefitCtx = document.getElementById('costBenefitChart')?.getContext('2d');
        if (costBenefitCtx) {
            new Chart(costBenefitCtx, {
                type: 'bar',
                data: {
                    labels: ['TimePro・ODBC手動入力', '統合システム自動化'],
                    datasets: [{
                        label: '年間工数コスト (万円)',
                        data: [4000, 200], /* 手動入力の年間コスト vs 統合システム維持コスト */
                        backgroundColor: [
                            'rgba(255, 193, 7, 0.6)', /* Subtle Amber/Yellow for TimePro-XG */
                            'rgba(76, 175, 80, 0.6)'  /* Subtle Green for Proposal C */
                        ],
                        borderColor: [
                            'rgba(255, 193, 7, 1)',
                            'rgba(76, 175, 80, 1)'
                        ],
                        borderWidth: 1
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    indexAxis: 'y', /* Horizontal bar chart for better label readability */
                    scales: {
                        x: {
                            beginAtZero: true,
                            title: {
                                display: true,
                                text: '年間工数コスト比較 (万円)',
                                font: { size: 20 }
                            },
                            ticks: { font: { size: 18 } }
                        },
                        y: {
                           ticks: { font: { size: 12 } }
                        }
                    },
                    plugins: {
                        legend: {
                            display: false /* Hiding legend as labels on Y-axis are clear */
                        },
                        tooltip: {
                            callbacks: {
                                title: function(tooltipItems) {
                                    /* Keep default title (which is the Y-axis label) */
                                    return tooltipItems[0].label;
                                },
                                label: function(context) {
                                    let label = context.dataset.label || '';
                                    if (label) {
                                        label += ': ';
                                    }
                                    if (context.parsed.x !== null) {
                                        label += context.parsed.x + ' 万円';
                                    }
                                    return label;
                                }
                            }
                        }
                    }
                }
            });
        }
       // Chart.js: Cost Benefit Chart
       const costBenefitCtx2 = document.getElementById('costBenefitChart2')?.getContext('2d');
        if (costBenefitCtx2) {
            new Chart(costBenefitCtx2, {
                type: 'bar',
                data: {
                    labels: ['TimePro-XG フルカスタマイズ', '工时数据提案 (社内開発)'],
                    datasets: [{
                        label: '实现功能所需要的想定費用 (万円)',
                        data: [1000, 50], /* TimePro-XG (400-1000万の中間), Proposal C (2週間開発の人件費目安) */
                        backgroundColor: [
                            'rgba(255, 193, 7, 0.6)', /* Subtle Amber/Yellow for TimePro-XG */
                            'rgba(76, 175, 80, 0.6)'  /* Subtle Green for Proposal C */
                        ],
                        borderColor: [
                            'rgba(255, 193, 7, 1)',
                            'rgba(76, 175, 80, 1)'
                        ],
                        borderWidth: 1,
                        barPercentage: 0.9 // 棒の幅をデフォルトの半分にする (0.9 / 2 = 0.45)
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    indexAxis: 'y', /* Horizontal bar chart for better label readability */
                    scales: {
                        x: {
                            beginAtZero: true,
                            max: 5000,
                            title: {
                                display: true,
                                text: '实现功能所需要的想定費用 (万円)',
                                font: { size: 20 }
                            },
                            ticks: { font: { size: 18 } }
                        },
                        y: {
                           ticks: { font: { size: 12 } }
                        }
                    },
                    plugins: {
                        legend: {
                            display: false /* Hiding legend as labels on Y-axis are clear */
                        },
                        tooltip: {
                            callbacks: {
                                title: function(tooltipItems) {
                                    /* Keep default title (which is the Y-axis label) */
                                    return tooltipItems[0].label;
                                },
                                label: function(context) {
                                    let label = context.dataset.label || '';
                                    if (label) {
                                        label += ': ';
                                    }
                                    if (context.parsed.x !== null) {
                                        label += context.parsed.x + ' 万円';
                                    }
                                    return label;
                                }
                            }
                        }
                    }
                }
            });
        }        
    </script>
</body>
</html>
