


5f187874-23f7-4ee3-9251-837e453c7c31


265e4260-f984-4971-b0dc-b67b1163ee21


# MySuite 客户端功能与交互流程分析




## 客户端架构概览
MySuite客户端由两个主要组件构成：
1. **Launcher.py** - 启动器，负责登录、注册和程序启动控制
2. **program1.py** - 员工操作界面，提供主要业务功能
这两个组件通过命令行参数传递登录凭证进行交互，形成完整的客户端体系。
## 一、Launcher.py 功能分析
### 1. 硬件指纹注册功能
**UI元素与流程**:
1. 输入员工ID (
    
     `id_input`
    
    )
2. 输入密码 (
    
     `password_input`
    
    )
3. 勾选"记住密码"复选框 (
    
     `remember_password_checkbox`
    
    ) [可选]
4. 点击"注册"按钮 (
    
     `register_btn`
    
    )
5. 确认注册对话框 (点击"是")
6. 等待硬件信息收集进度对话框
7. 注册成功提示框
account_treeDiagram      
```mermaid
sequenceDiagram
    participant UI as Launcher界面
    participant Client as 客户端处理
    participant Server as Server3认证服务

    UI->>Client: 点击"注册"按钮
    Client->>Client: 验证输入(员工ID/密码)
    Client->>UI: 显示确认对话框
    UI->>Client: 确认注册
    Client->>UI: 显示进度对话框
    Client->>Client: 收集硬件信息(hardware_fingerprint.collect_all)
    Client->>Server: POST /auth/api/hardware/register
    Note right of Server: 验证员工ID和密码\\n检查是否已注册\\n生成硬件指纹\\n保存到数据库
    Server->>Client: 返回注册结果
    Client->>UI: 关闭进度对话框
    Client->>UI: 显示注册结果

    alt 注册成功
        Client->>Client: 保存密码(如果勾选)
        Client->>UI: 清空密码框
    end
```
**后端交互**:
- **服务器**: Server3 (认证微服务)
- **API端点**: `/auth/api/hardware/register`
- **处理函数**: `register_fingerprint()` in 
    
    `hardware_registration.py`
    
- **数据库操作**: 将硬件指纹保存到
    
     `HardwareFingerprint`
    
    表
### 2. 登录功能
**UI元素与流程**:
1. 输入员工ID (
    
     `id_input`
    
    )
2. 输入密码 (
    
     `password_input`
    
    )
3. 勾选"记住密码"复选框 (
    
     `remember_password_checkbox`
    
    ) [可选]
4. 点击"登录确认"按钮 (
    
     `login_confirm_btn`
    
    )
5. 等待验证
6. 登录成功后启用程序启动按钮
account_treeDiagram      
```mermaid 
sequenceDiagram
    participant UI as Launcher界面
    participant Client as 客户端处理
    participant Auth as Server3认证服务
    
    UI->>Client: 点击"登录确认"按钮
    Client->>Client: 验证输入(员工ID/密码)
    Client->>Auth: POST /auth/api/hardware/verify
    Note right of Auth: 验证硬件指纹
    Auth->>Client: 返回硬件验证结果
    
    alt 硬件验证成功
        Client->>Auth: POST /auth/login
        Note right of Auth: 验证员工ID和密码
        Auth->>Client: 返回登录结果(含token)
        
        alt 登录成功
            Client->>Client: 保存密码(如果勾选)
            Client->>Client: 保存最近使用的员工ID
            Client->>Client: 清空输入框
            Client->>Client: 保存登录状态
            Client->>UI: 启用程序启动按钮
        else 登录失败
            Client->>UI: 显示错误消息
            Client->>UI: 清空密码框
        end
    else 硬件验证失败
        Client->>UI: 显示硬件验证错误
        Client->>UI: 清空密码框
    end
```

**后端交互**:
- **服务器**: Server3 (认证微服务)
- **API端点1**: `/auth/api/hardware/verify`
- **处理函数1**: `verify_fingerprint()` in 
    
    `hardware_registration.py`
    
- **API端点2**: `/auth/login`
- **处理函数2**: `login()` in 
    
    `auth_login.py`
    
- **返回数据**: JWT token, 员工姓名
### 3. 删除硬件指纹注册
**UI元素与流程**:
1. 输入员工ID (
    
     `id_input`
    
    )
2. 点击"注册删除"按钮 (
    
     `delete_registration_btn`
    
    )
3. 确认删除对话框 (点击"是")
4. 输入管理员密码对话框
5. 等待删除操作
6. 删除结果提示框
account_treeDiagram      
```mermaid
sequenceDiagram
    participant UI as Launcher界面
    participant Client as 客户端处理
    participant Server as Server3认证服务
    
    UI->>Client: 点击"注册删除"按钮
    Client->>Client: 验证员工ID输入
    Client->>UI: 显示确认对话框
    UI->>Client: 确认删除
    Client->>UI: 显示管理员密码对话框
    UI->>Client: 输入管理员密码
    Client->>Server: POST /auth/api/hardware/delete
    Note right of Server: 验证管理员密码<br/>查找硬件指纹<br/>删除指纹记录
    Server->>Client: 返回删除结果
    Client->>UI: 显示删除结果
    alt 删除成功
        Client->>Client: 删除保存的密码
        Client->>UI: 清空输入框
    end
```
**后端交互**:
- **服务器**: Server3 (认证微服务)
- **API端点**: `/auth/api/hardware/delete`
- **处理函数**: 未在代码片段中显示，但推测为`delete_fingerprint()`
- **数据库操作**: 从
    
     `HardwareFingerprint`
    
    表删除记录
### 4. 启动员工操作界面
**UI元素与流程**:
1. 成功登录后
2. 点击"启动员工操作界面"按钮 (
    
     `program_launch_btn`
    
    )
3. 启动独立程序
    
     `program1.py`
    
account_treeDiagram      
```mermaid
sequenceDiagram
    participant UI as Launcher界面
    participant Client as 客户端处理
    participant OS as 操作系统
    participant Program1 as program1.py
    
    UI->>Client: 点击"启动员工操作界面"按钮
    Client->>Client: 验证登录状态
    Client->>Client: 获取program1.py路径
    Client->>OS: 启动子进程(subprocess.Popen)
    Note right of OS: 传递参数:<br/>- 登录token<br/>- 员工ID<br/>- 员工姓名
    OS->>Program1: 启动program1.py
    Client->>Client: 记录运行中的程序
    Client->>UI: 更新按钮状态和程序状态
```
**后端交互**:
- 无直接后端交互，使用操作系统的进程管理
- 通过命令行参数传递登录凭证到
    
     `program1.py`
    
### 5. 启动PLC编程工具
**UI元素与流程**:
1. 成功登录后
2. 点击"启动PLC编程工具"按钮 (
    
     `program2_launch_btn`
    
    )
3. 启动独立程序
    
     `program2.py`
    
account_treeDiagram      
```mermaid
sequenceDiagram
    participant UI as Launcher界面
    participant Client as 客户端处理
    participant OS as 操作系统
    participant Program2 as program2.py
    
    UI->>Client: 点击"启动PLC编程工具"按钮
    Client->>Client: 验证登录状态和token有效性
    Client->>Client: 获取program2.py路径
    Client->>OS: 启动子进程(subprocess.Popen)
    Note right of OS: 传递参数:<br/>- 登录token<br/>- 员工ID<br/>- 员工姓名
    OS->>Program2: 启动program2.py
    Client->>Client: 记录运行中的程序
    Client->>UI: 更新按钮状态和程序状态
```
**后端交互**:
- 无直接后端交互，使用操作系统的进程管理
- 通过命令行参数传递登录凭证到
    
     `program2.py`
    
## 二、program1.py 功能分析
### 1. 员工操作界面初始化
**UI元素与流程**:
1. 从命令行参数获取登录凭证
2. 初始化界面
3. 加载员工信息
4. 连接到服务器
account_treeDiagram      
```mermaid
sequenceDiagram
    participant OS as 操作系统
    participant Program1 as program1.py
    participant UI as 员工界面
    participant Server as 服务器API
    
    OS->>Program1: 启动(传递token/ID/姓名)
    Program1->>Program1: 解析命令行参数
    Program1->>Program1: 初始化UI组件
    Program1->>Program1: 设置HTTP客户端
    Program1->>Server: 验证token有效性
    Server->>Program1: 返回验证结果
    Program1->>UI: 显示员工信息
    Program1->>UI: 初始化功能标签页
```
**后端交互**:
- **服务器**: Server3 (认证微服务)
- **API端点**: `/auth/verify_token`
- **处理函数**: 未在代码片段中显示，但推测为`verify_token()`
### 2. 工时数据操作功能
**UI元素与流程**:
1. 输入工时数据(日期、时间、部门等)
2. 点击"データ送信"按钮(数据发送)
3. 等待服务器响应
4. 显示操作结果
account_treeDiagram      
```mermaid
sequenceDiagram
    participant UI as 员工界面
    participant Client as program1.py
    participant Server5 as 数据同步微服务
    participant DB as PostgreSQL
    participant MDB as Access MDB
    
    UI->>Client: 点击"データ送信"按钮
    Client->>Client: 收集输入数据
    Client->>Server5: POST /api/sync/entries
    Note right of Server5: f4_operation_handler<br/>处理客户端操作
    Server5->>DB: 写入entries分区表
    Note right of DB: 触发器通知f1监听器
    DB-->>Server5: 数据变更通知(f1_listener)
    Server5->>MDB: 推送数据到MDB(f2_push_writer)
    Server5->>Server5: 触发用户同步(f6_user_sync)
    Server5->>Client: 返回操作结果
    Client->>UI: 显示操作结果
```
**后端交互**:
- **服务器**: Server5 (数据同步微服务)
- **API端点**: `/api/sync/entries`
- **处理函数**: `f4_operation_handler`中的创建操作
- **数据流**: PostgreSQL → f1监听器 → f2推送 → MDB → f6同步
### 3. 工时数据修改功能
**UI元素与流程**:
1. 在表格中选择工时记录
2. 点击"変更"按钮(修改)
3. 在弹出对话框中修改数据
4. 确认修改
5. 等待服务器响应
6. 显示操作结果
account_treeDiagram      
```mermaid
sequenceDiagram
    participant UI as 员工界面
    participant Client as program1.py
    participant Server5 as 数据同步微服务
    participant DB as PostgreSQL
    participant MDB as Access MDB
    
    UI->>Client: 点击"変更"按钮
    Client->>UI: 显示修改对话框
    UI->>Client: 输入修改数据
    Client->>Client: 验证输入
    Client->>Server5: PUT /api/sync/entries/{id}
    Note right of Server5: f4_operation_handler<br/>处理更新操作
    Server5->>DB: 更新entries分区表
    Note right of DB: 触发器通知f1监听器
    DB-->>Server5: 数据变更通知(f1_listener)
    Server5->>MDB: 更新MDB数据(f2_push_writer)
    Server5->>Client: 返回操作结果
    Client->>UI: 更新表格显示
    Client->>UI: 显示操作结果
```
**后端交互**:
- **服务器**: Server5 (数据同步微服务)
- **API端点**: `/api/sync/entries/{id}`
- **处理函数**: `f4_operation_handler`中的更新操作
- **数据流**: PostgreSQL → f1监听器 → f2推送 → MDB
### 4. 工时数据删除功能
**UI元素与流程**:
1. 在表格中选择工时记录
2. 点击"削除"按钮(删除)
3. 确认删除对话框
4. 等待服务器响应
5. 显示操作结果
account_treeDiagram      
```mermaid
sequenceDiagram
    participant UI as 员工界面
    participant Client as program1.py
    participant Server5 as 数据同步微服务
    participant DB as PostgreSQL
    participant MDB as Access MDB
    
    UI->>Client: 点击"削除"按钮
    Client->>UI: 显示确认对话框
    UI->>Client: 确认删除
    Client->>Server5: DELETE /api/sync/entries/{id}
    Note right of Server5: f4_operation_handler<br/>处理删除操作
    Server5->>DB: 标记entries记录为删除
    Note right of DB: 触发器通知f1监听器
    DB-->>Server5: 数据变更通知(f1_listener)
    Server5->>MDB: 从MDB删除数据(f2_push_writer)
    Server5->>Client: 返回操作结果
    Client->>UI: 从表格移除记录
    Client->>UI: 显示操作结果
```
**后端交互**:
- **服务器**: Server5 (数据同步微服务)
- **API端点**: `/api/sync/entries/{id}`
- **处理函数**: `f4_operation_handler`中的删除操作
- **数据流**: PostgreSQL → f1监听器 → f2推送 → MDB
### 5. 数据刷新功能
**UI元素与流程**:
1. 点击"リフレッシュ"按钮(刷新)
2. 等待服务器响应
3. 更新表格数据
account_treeDiagram      
```mermaid
sequenceDiagram
    participant UI as 员工界面
    participant Client as program1.py
    participant Server5 as 数据同步微服务
    
    UI->>Client: 点击"リフレッシュ"按钮
    Client->>Server5: GET /api/sync/entries?employee_id={id}
    Note right of Server5: 查询员工的entries数据
    Server5->>Client: 返回entries数据
    Client->>UI: 更新表格显示
```
**后端交互**:
- **服务器**: Server5 (数据同步微服务)
- **API端点**: `/api/sync/entries`
- **处理函数**: 未在代码片段中显示，但推测为`get_entries()`
### 6. 视频监控功能
**UI元素与流程**:
1. 切换到"视频监控"标签页
2. 点击"连接视频"按钮
3. 等待视频流连接
4. 显示实时视频画面
5. 点击"获取快照"保存画面
account_treeDiagram      
```mermaid
sequenceDiagram
    participant UI as 员工界面
    participant Client as program1.py
    participant Server4 as 视频监控微服务
    
    UI->>Client: 点击"连接视频"按钮
    Client->>Server4: GET /api/video/stream
    Note right of Server4: 启动视频流
    Server4-->>Client: 返回视频流(MJPEG)
    Client->>UI: 显示视频画面
    Client->>UI: 更新FPS和状态
    
    UI->>Client: 点击"获取快照"按钮
    Client->>Server4: GET /api/video/snapshot
    Server4->>Client: 返回快照图像
    Client->>Client: 保存图像到文件
    Client->>UI: 显示保存结果
```
**后端交互**:
- **服务器**: Server4 (视频监控微服务)
- **API端点1**: `/api/video/stream`
- **API端点2**: `/api/video/snapshot`
- **处理函数**: 未在代码片段中显示，但推测为`get_video_stream()`和`get_snapshot()`
### 7. 聊天功能
**UI元素与流程**:
1. 切换到"聊天"标签页
2. 输入用户名和消息
3. 点击"发送"按钮
4. 消息显示在聊天窗口
account_treeDiagram      
```mermaid
sequenceDiagram
    participant UI as 员工界面
    participant Client as program1.py
    participant Server2 as 聊天微服务
    
    Note over Client,Server2: WebSocket连接建立
    
    UI->>Client: 输入消息并点击"发送"
    Client->>Server2: WebSocket发送消息
    Server2->>Server2: 广播消息给所有连接
    Server2-->>Client: WebSocket接收消息
    Client->>UI: 显示消息在聊天窗口
    
    UI->>Client: 点击"发送文件"
    Client->>UI: 打开文件选择对话框
    UI->>Client: 选择文件
    Client->>Server2: 上传文件
    Server2->>Server2: 存储文件
    Server2-->>Client: 返回文件链接
    Client->>UI: 在聊天窗口显示文件链接
```
**后端交互**:
- **服务器**: Server2 (聊天微服务)
- **WebSocket端点**: `/ws/chat`
- **文件上传端点**: `/api/chat/upload`
- **处理函数**: 未在代码片段中显示，但推测为WebSocket连接处理函数
## 三、功能扩展指南
### 添加新功能的一般流程
1. **确定功能需求**
    - 明确功能目标和用户交互流程
    - 确定需要的UI元素和后端API
2. **前端实现**
    - 在适当的客户端文件中添加UI元素
    - 实现事件处理函数
    - 添加与后端的通信代码
3. **后端实现**
    - 在适当的微服务中添加API端点
    - 实现业务逻辑
    - 添加数据库操作
4. **集成测试**
    - 测试前端UI交互
    - 测试前后端通信
    - 测试数据流和业务逻辑
### 示例：添加"导出报表"功能
**前端实现**:
Copy
    # 添加报表标签页
    report_tab = QtWidgets.QWidget()
    self.tab_widget.addTab(report_tab, "レポート")
    # 添加报表控件
    layout = QtWidgets.QVBoxLayout(report_tab)
    # 日期选择
    date_layout = QtWidgets.QHBoxLayout()
    date_layout.addWidget(QtWidgets.QLabel("期間:"))
    self.start_date = QtWidgets.QDateEdit()
    self.start_date.setCalendarPopup(True)
    self.start_date.setDate(QtCore.QDate.currentDate().addDays(-30))
    date_layout.addWidget(self.start_date)
    date_layout.addWidget(QtWidgets.QLabel("〜"))
    self.end_date = QtWidgets.QDateEdit()
    self.end_date.setCalendarPopup(True)
    self.end_date.setDate(QtCore.QDate.currentDate())
    date_layout.addWidget(self.end_date)
    date_layout.addStretch()
    layout.addLayout(date_layout)
    # 导出按钮
    button_layout = QtWidgets.QHBoxLayout()
    self.export_excel_btn = QtWidgets.QPushButton("Excelエクスポート")
    self.export_excel_btn.clicked.connect(self.on_export_excel)
    button_layout.addWidget(self.export_excel_btn)
    self.export_pdf_btn = QtWidgets.QPushButton("PDFエクスポート")
    self.export_pdf_btn.clicked.connect(self.on_export_pdf)
    button_layout.addWidget(self.export_pdf_btn)
    button_layout.addStretch()
    layout.addLayout(button_layout)
    # 状态显示
    self.report_status = QtWidgets.QLabel("レポート準備完了")
    layout.addWidget(self.report_status)
    layout.addStretch()
def on_export_excel(self):
    """导出Excel报表"""
    start_date = self.start_date.date().toString("yyyy-MM-dd")
    end_date = self.end_date.date().toString("yyyy-MM-dd")
    # 显示保存对话框
    file_path, _ = QtWidgets.QFileDialog.getSaveFileName(
        self, "保存Excel", "", "Excel Files (*.xlsx)"
    )
    if not file_path:
        return
    # 请求报表数据
    self.report_status.setText("レポート生成中...")
    request_id = f"report_excel_{QtCore.QDateTime.currentSecsSinceEpoch()}"
    self.async_http_client.get_async(
        f"/api/reports/excel?employee_id={self.employee_id}&start_date={start_date}&end_date={end_date}",
        request_id,
        stream=True
    )
def setup_report_ui(self):
**后端实现**:
Copy
            Entries.employee_id == employee_id,
            Entries.entry_date >= start_date,
            Entries.entry_date <= end_date
        )
        result = await db.execute(query)
        entries = result.scalars().all()
        # 创建DataFrame
        data = []
        for entry in entries:
            data.append({
                "日付": entry.entry_date,
                "時間": entry.duration,
                "所属": entry.department,
                "機種": entry.project_code,
                "区分": entry.status,
                "項目": entry.description
            })
        df = pd.DataFrame(data)
        # 创建Excel
        output = io.BytesIO()
        with pd.ExcelWriter(output, engine='xlsxwriter') as writer:
            df.to_excel(writer, sheet_name='工時データ', index=False)
            # 添加格式
            workbook = writer.book
            worksheet = writer.sheets['工時データ']
            # 添加标题格式
            header_format = workbook.add_format({
                'bold': True,
                'text_wrap': True,
                'valign': 'top',
                'bg_color': '#D7E4BC',
                'border': 1
            })
    try:
        # 查询数据
        query = select(Entries).where(
    employee_id: str,
    start_date: str,
    end_date: str,
    db: AsyncSession = Depends(get_db)
):
    """生成Excel报表"""
# 在Server5中添加reports.py
from fastapi import APIRouter, Depends, HTTPException, status
from fastapi.responses import StreamingResponse
import pandas as pd
import io
from datetime import datetime
router = APIRouter(prefix="/api/reports", tags=["Reports"])
@router.get("/excel")
async def generate_excel_report(
## 四、总结
MySuite客户端提供了丰富的功能，包括用户认证、硬件指纹管理、工时数据操作、视频监控和聊天等。每个功能都有清晰的UI交互流程和后端API调用。
系统采用微服务架构，不同功能由不同的服务提供支持：
- **认证与安全**: Server3
- **数据同步**: Server5
- **视频监控**: Server4
- **聊天通信**: Server2
客户端采用模块化设计，将启动器(Launcher.py)和业务功能(program1.py)分离，通过命令行参数和JWT token进行安全通信。这种设计使得系统具有良好的可扩展性和可维护性。
在进行功能扩展或修改时，开发者需要同时考虑前端UI交互和后端API实现，确保它们协同工作以提供完整的用户体验。
