# xmlinit.py
import os, time, calendar
import xml.etree.ElementTree as ET
from datetime import datetime, timedelta

import status
from process_deals import process_all_deals
from db_utils import db_path  # 假设在 db_utils.py 中定义了 db_path
import win32com.client, pythoncom

XML_FOLDER = r"D:\actest25\xml25"

def ensure_folder(folder):
    if not os.path.exists(folder):
        os.makedirs(folder)

def fetch_records_with_id(employee_id, start_date, end_date):
    pythoncom.CoInitialize()
    recs = []
    try:
        access = win32com.client.Dispatch("Access.Application")
        access.OpenCurrentDatabase(db_path)
        db = access.CurrentDb()
        sql = f"""
        SELECT ID, 従業員ｺｰﾄﾞ, 日付, 機種, 号機, 工場製番, 工事番号,
               ﾕﾆｯﾄ番号, 区分, 項目, 時間, 所属ｺｰﾄﾞ
          FROM 元作業時間
         WHERE 従業員ｺｰﾄﾞ = '{employee_id}'
           AND 日付 >= #{start_date}#
           AND 日付 <= #{end_date}#
         ORDER BY 日付, 時間
        """
        rs = db.OpenRecordset(sql)
        while not rs.EOF:
            recs.append({
                'id':             rs.Fields('ID').Value,
                'employee_id':    rs.Fields('従業員ｺｰﾄﾞ').Value,
                'date':           rs.Fields('日付').Value.strftime("%Y/%m/%d"),
                'model':          rs.Fields('機種').Value or "",
                'number':         rs.Fields('号機').Value or "",
                'factory_number': rs.Fields('工場製番').Value or "",
                'project_number': rs.Fields('工事番号').Value or "",
                'unit_number':    rs.Fields('ﾕﾆｯﾄ番号').Value or "",
                'category':       rs.Fields('区分').Value or "",
                'item':           rs.Fields('項目').Value or "",
                'time':           rs.Fields('時間').Value or 0.0,
                'department':     rs.Fields('所属ｺｰﾄﾞ').Value or ""
            })
            rs.MoveNext()
        return recs
    except Exception as e:
        print(f"[XMLINIT] 查询失败: {e}")
        return []
    finally:
        try:
            access.CloseCurrentDatabase()
            access.Quit()
        except:
            pass
        pythoncom.CoUninitialize()

def init_employee_xml(employee_id):
    ensure_folder(XML_FOLDER)
    deal_fn = os.path.join(XML_FOLDER, f"deal{employee_id}.xml")

    # 如果有待处理条目，触发处理并等待
    if os.path.exists(deal_fn):
        tree = ET.parse(deal_fn)
        if tree.getroot().findall('entry'):
            print("[XMLINIT] 有未处理条目，开始触发 process_all_deals() …")
            process_all_deals()
            # 等待 xmlfinish==True，最长 3 分钟
            start = time.time()
            while not status.xmlfinish:
                if time.time() - start > 180:
                    raise TimeoutError("等待后台处理超时（3 分钟）")
                time.sleep(1)
            print("[XMLINIT] 后台处理完成，继续同步主 XML")

    # 计算上月 + 本月日期范围
    today = datetime.now()
    y, m = today.year, today.month
    first_cur = datetime(y, m, 1)
    last_cur  = datetime(y, m, calendar.monthrange(y, m)[1])
    prev_last = first_cur - timedelta(days=1)
    first_prev = prev_last.replace(day=1)

    start = first_prev.strftime("%Y/%m/%d")
    end   = last_cur.strftime("%Y/%m/%d")

    # 拉取数据库记录
    records = fetch_records_with_id(employee_id, start, end)

    # 覆盖写主 XML
    main_fn = os.path.join(XML_FOLDER, f"{employee_id}.xml")
    root = ET.Element('entries')
    tree = ET.ElementTree(root)

    for rec in records:
        entry = ET.SubElement(root, 'entry')
        entry.set('id', str(rec['id']))
        entry.set('ts', datetime.now().isoformat())
        for key in ['employee_id','date','model','number',
                    'factory_number','project_number','unit_number',
                    'category','item','time','department']:
            c = ET.SubElement(entry, key)
            c.text = str(rec.get(key, ""))
    tree.write(main_fn, encoding='utf-8', xml_declaration=True)
    print(f"[XMLINIT] 已生成/更新 {main_fn}（共 {len(records)} 条）")

