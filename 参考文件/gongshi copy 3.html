.header-bg {
    background: linear-gradient(135deg, #2196F3 0%, #4CAF50 100%);
    color: white;
    padding: 20px 40px;
    text-align: center;
    width: 100%; /* Full width */
    box-sizing: border-box;
    margin-bottom: 20px;
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
}
.footer-bg {
    background-color: #333333;
    color: #CCCCCC;
    padding: 15px 40px;
    text-align: center;
    width: 100%; /* Full width */
    box-sizing: border-box;
    margin-top: 20px;
}

.section-title {
    color: #333333;
    border-bottom: 4px solid #4CAF50; /* Subtle Green Accent */
    padding-bottom: 0.5rem;
    display: inline-block;
    margin-bottom: 1.5rem;
    font-size: 2.25rem; /* Larger title for A3 */
    text-align: center;
    width: 100%;
}
.card {
    background-color: white;
    border-radius: 12px;
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.08);
    padding: 1.25rem; /* Adjusted padding for A3 */
    margin-bottom: 1.5rem; /* Adjusted margin */
    transition: transform 0.2s ease-out, box-shadow 0.2s ease-out;
    flex-grow: 1; /* Allows cards to fill space */
    display: flex;
    flex-direction: column;
}
.card:hover {
    transform: translateY(-2px);
    box-shadow: 0 6px 12px rgba(0, 0, 0, 0.1);
}
.stat-value {
    color: #4CAF50;
    font-weight: 700;
    font-size: 2rem; /* Adjusted size for A3 */
}
.stat-label {
    color: #555555;
    font-size: 0.9rem;
    margin-top: 0.25rem;
}
.accent-text-proposalC {
    color: #4CAF50;
    font-weight: 600;
}
.accent-text-challenge {
    color: #FFC107;
    font-weight: 600;
}
.accent-text-costly {
    color: #E53E3E;
    font-weight: 600;
}

.flowchart-step {
    background-color: #E8F5E9;
    border: 2px solid #4CAF50;
    color: #333333;
    padding: 0.6rem 0.8rem;
    border-radius: 8px;
    text-align: center;
    font-weight: 600;
    font-size: 0.9rem;
}
.flowchart-arrow {
    color: #4CAF50;
    font-size: 1.5rem;
    line-height: 1;
    font-weight: bold;
    margin: 0.15rem 0;
}
.comparison-table th, .comparison-table td {
    border: 1px solid #E0E0E0;
    padding: 0.6rem;
    text-align: left;
    vertical-align: top;
    font-size: 0.85rem; /* Adjusted font size for table */
}
.comparison-table th {
    background-color: #EEEEEE;
    font-weight: 600;
}
.chart-container {
    position: relative;
    width: 100%;
    max-width: 450px; /* Adjusted max-width for A3 column */
    margin-left: auto;
    margin-right: auto;
    height: 280px; /* Adjusted height for A3 column */
    max-height: 320px;
}
.list-item-custom {
    background-color: #fcfcfc; /* Lighter background for list items */
    padding: 8px;
    border-left: 4px solid #2196F3; /* Blue accent for list */
    margin-bottom: 6px;
    box-shadow: 0 1px 2px rgba(0,0,0,0.05);
    font-size: 0.9rem;
}
.list-item-custom h4 {
    font-size: 1rem;
}
.tooltip .tooltiptext {
    width: 200px; /* Adjusted tooltip width */
    margin-left: -100px;
    font-size: 0.8rem;
}

/* Specific layout for A3 pages */
.a3-grid-2-col {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: 20px;
    flex-grow: 1;
}
.a3-section-content {
    flex-grow: 1;
    display: flex;
    flex-direction: column;
    justify-content: flex-start;
}
.a3-section-content > .card {
    flex-grow: 0; /* Cards within A3 sections should not grow to fill all space unless needed */
    margin-bottom: 0; /* Remove default card margin, manage spacing with gap */
}
.a3-page-header {
    text-align: center;
    margin-bottom: 20px;
}
.a3-page-header h2 {
    font-size: 2.5rem;
    font-weight: bold;
    color: #333333;
    border-bottom: 4px solid #4CAF50;
    padding-bottom: 10px;
    display: inline-block;
}

/* Styles copied from tech_commonality_analysis_jp for integration */
.tech-column {
    display: flex;
    flex-direction: column;
    gap: 8px; /* Adjusted gap */
}
.tech-item {
    background-color: #F8F8F8;
    border: 1px solid #E0E0E0;
    border-radius: 6px;
    padding: 0.6rem; /* Adjusted padding */
    font-size: 0.8rem; /* Adjusted font size */
    text-align: left;
}
.tech-item strong {
    color: #555555;
}
.tech-group {
    margin-bottom: 12px; /* Adjusted margin */
    padding: 8px; /* Adjusted padding */
    border: 1px dashed #CCCCCC;
    border-radius: 8px;
    background-color: #FAFAFA;
}
.tech-group h4 {
    font-weight: 600;
    color: #4CAF50;
    margin-bottom: 6px; /* Adjusted margin */
    text-align: center;
    font-size: 0.95rem; /* Adjusted font size */
}
.commonality-link {
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 8px 0; /* Adjusted margin */
    position: relative;
    z-index: 10;
}
.commonality-text {
    background-color: #2196F3; /* Blue accent */
    color: white;
    padding: 6px 12px; /* Adjusted padding */
    border-radius: 20px;
    font-weight: 600;
    font-size: 0.85rem; /* Adjusted font size */
    white-space: nowrap;
    box-shadow: 0 2px 5px rgba(0,0,0,0.2);
    position: relative;
    z-index: 20;
}
.commonality-line {
    position: absolute;
    height: 2px;
    background-color: #4CAF50; /* Green accent */
    z-index: 1;
}
.arrow-right {
    width: 0;
    height: 0;
    border-top: 5px solid transparent; /* Adjusted arrow size */
    border-bottom: 5px solid transparent;
    border-left: 8px solid #4CAF50;
    position: absolute;
    right: -8px;
    top: 50%;
    transform: translateY(-50%);
}
.arrow-left {
    width: 0;
    height: 0;
    border-top: 5px solid transparent; /* Adjusted arrow size */
    border-bottom: 5px solid transparent;
    border-right: 8px solid #4CAF50;
    position: absolute;
    left: -8px;
    top: 50%;
    transform: translateY(-50%);
}
</style>
