# -*- coding: utf-8 -*-
import os
import threading
import calendar
from datetime import datetime, timedelta
import tkinter as tk
from tkinter import ttk, messagebox, simpledialog
from tkcalendar import Calendar
import xml.etree.ElementTree as ET
import status  # Assuming status is a module you have

import re  # 修改点①：新增，用于正则解析"当 8:12"等带前缀的时间字符串

# Import functions from other modules that WorkTimeApp uses
from xmlinit import init_employee_xml
from timepro import fetch_timepro_data
from html_to_xml_parser3 import parse_html_to_xml
from db_utils import delete_work_time_by_id # Added import

# Global definitions for UI elements if needed by multiple methods/classes
# Define labels here if not already globally defined and submit_data needs it.
global_form_labels = {
    'employee_id': "従業員ID (必須):",
    'model': "機種 :", 'number': "号機 :",
    'factory_number': "工場製番 :", 'project_number': "工事番号 :",
    'unit_number': "ユニット番号 :", 'category': "区分 (必須):", 'item': "項目 (必須):",
    'time': "時間 (時) (必須):", 'department': "部門 (必須):"
}

# 表格列定义
cols = ('DB_ID', '日付','機種','号機','工場製番','工事番号','ﾕﾆｯﾄ番号','区分','項目','時間') # Added DB_ID

# --- Helper Functions moved here ---
def ensure_folder(folder):
    """确保指定文件夹存在，如果不存在则创建。"""
    if not os.path.exists(folder):
        os.makedirs(folder)



def append_to_deal_xml(data, xml_folder):
    ensure_folder(xml_folder)
    emp_id = data['employee_id']
    fn = os.path.join(xml_folder, f"deal{emp_id}.xml")
    if os.path.exists(fn):
        tree = ET.parse(fn)
        root = tree.getroot()
    else:
        root = ET.Element('entries')
        tree = ET.ElementTree(root)
    entry = ET.SubElement(root, 'entry')
    entry.set('ts', datetime.now().isoformat())
    xml_field_map = {
        'employee_id': 'employee_id', 'date': 'date', 'model': 'model', 'number': 'number',
        'factory_number': 'factory_number', 'project_number': 'project_number',
        'unit_number': 'unit_number', 'category': 'category', 'item': 'item',
        'time': 'time', 'department': 'department'
    }
    for k_ui, k_xml in xml_field_map.items():
        child = ET.SubElement(entry, k_xml)
        child.text = str(data.get(k_ui, ""))
    entry.set('processed', 'false')
    tree.write(fn, encoding='utf-8', xml_declaration=True)




def read_records_from_xml(employee_id, xml_folder):
    fn = os.path.join(xml_folder, f"{employee_id}.xml")
    records = []
    if not os.path.exists(fn):
        print(f"[UI] XMLファイルが見つかりません: {fn}")
        return []
    try:
        tree = ET.parse(fn)
        root = tree.getroot()
        xml_to_japanese_map = {
            'employee_id':    '従業員ｺｰﾄﾞ',
            'date':           '日付',
            'model':          '機種',
            'number':         '号機',
            'factory_number': '工場製番',
            'project_number': '工事番号',
            'unit_number':    'ﾕﾆｯﾄ番号',
            'category':       '区分',
            'item':           '項目',
            'time':           '時間',
            'department':     '所属ｺｰﾄﾞ'
        }
        for entry in root.findall('entry'):
            rec = {}
            rec['DB_ID'] = entry.get('id') # Extract 'id' attribute

            for xml_key, japanese_key in xml_to_japanese_map.items():
                element = entry.find(xml_key)
                value = element.text if element is not None else ""
                if value is None or (isinstance(value, str) and value.strip().lower() == 'none'):
                    value = ''
                if xml_key == 'time':
                    try:
                        rec[japanese_key] = float(value) if value else 0.0
                    except ValueError:
                        rec[japanese_key] = 0.0
                else:
                    rec[japanese_key] = value
            records.append(rec)
        return records
    except ET.ParseError as e:
        print(f"[UI] XMLファイルの解析エラー {fn}: {e}")
        return []
    except Exception as e:
        print(f"[UI] XML読込中に予期せぬエラーが発生しました {fn}: {e}")
        return []

def query_daily_work_hours(employee_id, target_date_str, xml_folder):
    all_records = read_records_from_xml(employee_id, xml_folder)
    daily_records = [rec for rec in all_records if rec.get('日付') == target_date_str]
    daily_records.sort(key=lambda x: x.get('時間', 0.0))
    return daily_records

def query_monthly_work_hours(employee_id, start_date_str, end_date_str, xml_folder):
    all_records = read_records_from_xml(employee_id, xml_folder)
    try:
        start_dt = datetime.strptime(start_date_str, "%Y/%m/%d")
        end_dt = datetime.strptime(end_date_str, "%Y/%m/%d")
    except ValueError as e:
        print(f"[UI] 月次検索の日付形式が無効です: {e}")
        return []
    monthly_records = []
    for rec in all_records:
        record_date_str = rec.get('日付')
        if record_date_str:
            try:
                record_dt = datetime.strptime(record_date_str, "%Y/%m/%d")
                if start_dt <= record_dt <= end_dt:
                    monthly_records.append(rec)
            except ValueError:
                print(f"[UI] 不正な日付形式の記録をスキップします: {record_date_str}")
                continue
    monthly_records.sort(key=lambda x: (x.get('日付', ''), x.get('時間', 0.0)))
    return monthly_records



# 修改点1：添加比较函数 compare_time_for_date
def compare_time_for_date(date_str, timepro_xml_path, employee_xml_path):
    t1 = 0.0
    try:
        tree = ET.parse(timepro_xml_path); root = tree.getroot()
        for rec in root.findall('record'):
            fld = rec.find("./field[@name='日付']")
            if fld is not None and fld.text and fld.text.strip() == date_str:
                sched = rec.find("./field[@name='所定時間']"); early = rec.find("./field[@name='早出残業']")
                st = sched.text.strip() if sched is not None and sched.text else ""
                et = early.text.strip() if early is not None and early.text else ""
                # 解析所定時間（分钟>0向上取整）
                try:
                    h, m = map(int, st.split(':'))
                    sched_h = h + (1 if m > 0 else 0)
                except:
                    sched_h = 0.0
                # 解析早出残業
                try:
                    h, m = map(int, et.split(':'))
                    early_h = h + m/60.0
                except:
                    early_h = 0.0
                t1 = sched_h + early_h
                break
    except:
        t1 = 0.0

    t2 = 0.0
    try:
        tree2 = ET.parse(employee_xml_path); root2 = tree2.getroot()
        for entry in root2.findall('entry'):
            d = entry.find('date'); tm = entry.find('time')
            if d is not None and d.text and d.text.strip() == date_str:
                try:
                    t2 += float(tm.text)
                except:
                    pass
    except:
        t2 = 0.0

    return abs(t1 - t2) < 1e-6



class WorkTimeApp:
    def __init__(self, root, employee_id, employee_name="未知姓名", timepro_password=None, xml_folder=None, timepro_output_dir=None, 
                 fetch_timepro_data_func=None, parse_html_to_xml_func=None):
        self.root = root
        self.root.title(f"勤務時間入力システム - {employee_name} ({employee_id})")
        self.root.wm_attributes("-alpha", 0.95)
        self.today_dt = datetime.now()
        self.current_date_dt = self.today_dt
        self.entries = {}
        self.status_animation_after_id = None
        self.show_current_month_table = True
        self.show_current_calendar_month = True

        self.employee_id = employee_id
        self.employee_name = employee_name
        self.timepro_password = timepro_password
        self.xml_folder = xml_folder
        self.timepro_output_dir = timepro_output_dir
        self.labels = global_form_labels # Use the global labels for the form

        # Store passed-in functions
        self.fetch_timepro_data = fetch_timepro_data_func
        self.parse_html_to_xml = parse_html_to_xml_func
        
        if not self.xml_folder:
            raise ValueError("XML_FOLDER must be provided to WorkTimeApp")
        if not self.timepro_output_dir:
            raise ValueError("TIMEPRO_OUTPUT_DIR must be provided to WorkTimeApp")
                # 修改点2：预先保存两个 XML 文件的路径，方便后面调用
        self.timepro_xml_path = os.path.join(self.timepro_output_dir, "timepro_report.xml")
        self.local_xml_path   = os.path.join(self.xml_folder, f"{self.employee_id}.xml")
       

        if not self.fetch_timepro_data:
            raise ValueError("fetch_timepro_data function must be provided")
        if not self.parse_html_to_xml:
            raise ValueError("parse_html_to_xml function must be provided")
        # --- UI Setup ---
        self.root.columnconfigure(0, weight=1)
        self.root.columnconfigure(1, weight=1)
        main = ttk.Frame(root, padding=10)
        main.grid(row=0, column=0, sticky="nsew")
        main.columnconfigure(1, weight=1)

        r = 0
        ttk.Label(main, text=self.labels['employee_id']).grid(row=r, column=0, sticky=tk.W, pady=2, padx=5)
        self.entries['employee_id'] = ttk.Entry(main, width=40)
        self.entries['employee_id'].grid(row=r, column=1, columnspan=3, sticky="ew", pady=2, padx=5)
        self.entries['employee_id'].insert(0, self.employee_id)
        self.entries['employee_id'].config(state='readonly')
        r += 1

        ttk.Label(main, text="日付 (YYYY/MM/DD):").grid(row=r, column=0, sticky=tk.W, pady=2, padx=5)
        df = ttk.Frame(main); df.grid(row=r, column=1, columnspan=3, sticky="ew")
        self.entries['date'] = ttk.Entry(df, width=15)
        self.entries['date'].pack(side=tk.LEFT, padx=(0,5))
        self.entries['date'].bind("<KeyRelease>", self._on_date_change)
        self.entries['date'].bind("<FocusOut>", self._on_date_change)
        ttk.Button(df, text="前日", command=lambda: self._change_date(-1)).pack(side=tk.LEFT, padx=2)
        ttk.Button(df, text="翌日", command=lambda: self._change_date(1)).pack(side=tk.LEFT, padx=2)
        ttk.Button(df, text="カレンダー", command=self._open_calendar).pack(side=tk.LEFT, padx=2)
        self.day_of_week = ttk.Label(df, text="", width=10)
        self.day_of_week.pack(side=tk.LEFT, padx=(5,0))
        r += 1

        for key in ['model','number','factory_number','project_number','unit_number','category','item','time','department']:
            ttk.Label(main, text=self.labels[key]).grid(row=r, column=0, sticky=tk.W, pady=2, padx=5)
            ent = ttk.Entry(main, width=40)
            ent.grid(row=r, column=1, columnspan=3, sticky="ew", pady=2, padx=5)
            self.entries[key] = ent
            r += 1

        bf = ttk.Frame(main); bf.grid(row=r, column=0, columnspan=4, pady=10, sticky="ew")
        bf.columnconfigure(0, weight=1); bf.columnconfigure(1, weight=1)
        ttk.Button(bf, text="データ送信", command=self.submit_data).grid(row=0, column=0, padx=5, sticky="ew")
        ttk.Button(bf, text="フォームクリア", command=self.clear_form).grid(row=0, column=1, padx=5, sticky="ew")
        
        qf = ttk.Frame(bf); qf.grid(row=1, column=0, columnspan=2, pady=(5,0), sticky="ew")
        qf.columnconfigure(0, weight=1); qf.columnconfigure(1, weight=1); qf.columnconfigure(2, weight=1)
        
        for i,(txt,cmd) in enumerate([
            ("当日勤務時間表示", self._show_daily_hours_gui),
            ("当月勤務時間表示", self._show_current_month_hours_gui),
            ("前月勤務時間表示", self._show_prev_month_hours_gui),
        ]):
            btn = ttk.Button(qf, text=txt, command=cmd)
            btn.grid(row=0, column=i, padx=5, sticky="ew")
        
        self.sync_xml_btn = ttk.Button(qf, text="XMLデータ同期", command=self._synchronize_xml_gui)
        self.sync_xml_btn.grid(row=1, column=0, columnspan=3, padx=5, pady=(5,0), sticky="ew")

        self.delete_db_entry_btn = ttk.Button(qf, text="選択行をDBから削除", command=self._delete_selected_from_db)
        self.delete_db_entry_btn.grid(row=2, column=0, columnspan=3, padx=5, pady=(5,0), sticky="ew")

        self.fetch_timepro_btn = ttk.Button(qf, text="TimePro読込", command=self._fetch_timepro_report_gui)
        self.fetch_timepro_btn.grid(row=3, column=0, columnspan=3, padx=5, pady=(5,0), sticky="ew")

        self.convert_html_to_xml_btn = ttk.Button(qf, text="TimePro HTML→XML変換", command=self._convert_timepro_html_to_xml_gui)
        self.convert_html_to_xml_btn.grid(row=4, column=0, columnspan=3, padx=5, pady=(5,0), sticky="ew")

        self._update_date_and_weekday()

        cf = ttk.Frame(root, padding=5); cf.grid(row=0, column=1, sticky="nsew")
        cf.columnconfigure(0, weight=1); cf.columnconfigure(1, weight=1)
        cf.rowconfigure(1, weight=1); cf.rowconfigure(2, weight=1)

        self.status_frame = ttk.Frame(cf); self.status_frame.grid(row=0, column=0, columnspan=3, sticky="ew", pady=(0,5))
        self.status_frame.columnconfigure(0, weight=1)
        self.main_status_lbl = ttk.Label(self.status_frame, text="", anchor='w', font=("Arial", 9, "bold"))
        self.main_status_lbl.pack(side=tk.TOP, fill="x", expand=True)
        self.main_progress_bar = ttk.Progressbar(self.status_frame, mode='indeterminate')
        self.main_progress_bar.pack(side=tk.TOP, fill="x", expand=True)

        self.calendar_display_frame = ttk.Frame(cf)
        self.calendar_display_frame.grid(row=1, column=0, rowspan=2, padx=5, pady=5, sticky="nsew")
        self.calendar_display_frame.columnconfigure(0, weight=1); self.calendar_display_frame.rowconfigure(1, weight=1)
        self.month_display_lbl = ttk.Label(self.calendar_display_frame, text="", font=("Arial", 10, "bold"))
        self.month_display_lbl.grid(row=0, column=0, sticky="ew", pady=(0, 5))

        prev_last = self.today_dt.replace(day=1) - timedelta(days=1)
        self.cal_prev = Calendar(self.calendar_display_frame, selectmode='none', year=prev_last.year, month=prev_last.month, day=1, showweeknumbers=False, weekendbackground='gray', disabledbackground='gray', background='white')
        self.cal_current = Calendar(self.calendar_display_frame, selectmode='none', year=self.today_dt.year, month=self.today_dt.month, day=1, showweeknumbers=False, weekendbackground='gray', disabledbackground='gray', background='white')

        self.style = ttk.Style()
        self.style.configure("PrevMonth.Treeview", background="light gray")
        self.style.configure("CurrentMonth.Treeview", background="white")
        self.style.map("PrevMonth.Treeview", background=[('selected', 'gray')])
        self.style.map("CurrentMonth.Treeview", background=[('selected', 'blue')])

        self.tree_prev_all = ttk.Treeview(cf, columns=cols, show='headings', height=8, style="PrevMonth.Treeview")
        self.vs1 = ttk.Scrollbar(cf, orient="vertical", command=self.tree_prev_all.yview); self.tree_prev_all.configure(yscrollcommand=self.vs1.set)

        self.tree_cur_all = ttk.Treeview(cf, columns=cols, show='headings', height=8, style="CurrentMonth.Treeview")
        self.vs2 = ttk.Scrollbar(cf, orient="vertical", command=self.tree_cur_all.yview); self.tree_cur_all.configure(yscrollcommand=self.vs2.set)
        
        col_display_names = {'DB_ID': 'ID'}
        default_col_width = 80
        # Adjusted default widths, especially for DB_ID, 日付, 時間
        self.column_widths_main_ui = {
            'DB_ID': 40, '日付': 90, '機種':70, '号機':70, '工場製番':80, 
            '工事番号':80, 'ﾕﾆｯﾄ番号':80, '区分':60, '項目':100, '時間':50
        }

        for c in cols:
            text = col_display_names.get(c, c)
            width = self.column_widths_main_ui.get(c, default_col_width)
            stretch_val = tk.NO if c == 'DB_ID' else tk.YES
            
            for tree_widget in [self.tree_prev_all, self.tree_cur_all]:
                tree_widget.heading(c, text=text)
                tree_widget.column(c, width=width, anchor='center', stretch=stretch_val)
        
        self.tree_cur_all.grid(row=1, column=1, padx=5, pady=5, sticky="nsew", rowspan=2)
        self.vs2.grid(row=1, column=2, sticky="ns", pady=5, rowspan=2)

        for cal_widget in (self.cal_prev, self.cal_current):
            cal_widget.tag_config('filled', background='light green')
            cal_widget.tag_config('empty', background='orange', bordercolor='red', borderwidth=10) # Note: border settings might not work on all themes/OS for tkcalendar tags

        self.toggle_table_btn = ttk.Button(cf, text="前月勤務時間表示", command=self._toggle_month_table_display)
        self.toggle_table_btn.grid(row=3, column=1, columnspan=2, pady=5, sticky="ew")
        self.toggle_calendar_btn = ttk.Button(cf, text="", command=self._toggle_calendar_month_display)
        self.toggle_calendar_btn.grid(row=3, column=0, pady=5, sticky="ew")
        self._update_calendar_month_display()
        threading.Thread(target=self._load_calendar_data, daemon=True).start()

        # --- Chart Display Area Setup ---
        # Configure root's rows for the main content and the chart
        self.root.rowconfigure(0, weight=1) # Main content row
        self.root.rowconfigure(1, weight=1) # Chart row, allow to expand

        self.chart_display_frame = ttk.Frame(self.root, padding=5)
        self.chart_display_frame.grid(row=1, column=0, columnspan=2, sticky="nsew", pady=10)

        self.chart_month_label = ttk.Label(self.chart_display_frame, text="勤務時間グラフ (読込中...)", font=("Arial", 10, "bold"))
        self.chart_month_label.pack(side=tk.TOP, pady=2)

        self.time_chart_canvas = tk.Canvas(self.chart_display_frame, bg="white", height=250) # Initial height
        self.time_chart_canvas.pack(side=tk.TOP, fill=tk.BOTH, expand=True)
        
        # --- Chart Control Buttons ---
        chart_controls_frame = ttk.Frame(self.chart_display_frame)
        chart_controls_frame.pack(side=tk.TOP, fill=tk.X, pady=(5,0))

        self.prev_month_chart_btn = ttk.Button(chart_controls_frame, text="前月グラフ表示", command=self._show_previous_month_chart_data)
        self.prev_month_chart_btn.pack(side=tk.LEFT, padx=5, expand=True, fill=tk.X)

        self.curr_month_chart_btn = ttk.Button(chart_controls_frame, text="当月グラフ表示", command=self._show_current_month_chart_data)
        self.curr_month_chart_btn.pack(side=tk.LEFT, padx=5, expand=True, fill=tk.X)


        # --- End Chart Control Buttons ---

        # ▼▼▼ 追加: 固定月(2025/05, 2025/06) 表示ボタン ▼▼▼
        self.may_chart_btn = ttk.Button(chart_controls_frame, text="2025/05グラフ表示",
                                        command=lambda: self._load_month_chart("202505"))
        self.may_chart_btn.pack(side=tk.LEFT, padx=5, expand=True, fill=tk.X)

        self.june_chart_btn = ttk.Button(chart_controls_frame, text="2025/06グラフ表示",
                                         command=lambda: self._load_month_chart("202506"))
        self.june_chart_btn.pack(side=tk.LEFT, padx=5, expand=True, fill=tk.X)
        # ▲▲▲ 追加ここまで ▲▲▲


        self.chart_raw_data = None 
        self.time_chart_canvas.bind("<Configure>", self._on_chart_canvas_resize)
        
        # Initialize chart display year and month
        self.chart_display_year = self.today_dt.year
        self.chart_display_month = self.today_dt.month

        threading.Thread(target=self._attempt_initial_chart_load, daemon=True).start()

    # --- 追加メソッド: 指定月ロード ---
    def _load_month_chart(self, month_str):
        html_filename = f"{month_str}_body_content.html"
        xml_filename  = f"{month_str}_timepro_report.xml"
        html_input_path = os.path.join(self.timepro_output_dir, html_filename)
        xml_output_path = os.path.join(self.timepro_output_dir, xml_filename)

        if not os.path.exists(html_input_path):
            messagebox.showerror("エラー", f"{html_filename} が見つかりません。")
            return

        self.main_status_lbl.config(text=f"{month_str} のデータ変換中...")
        self.main_progress_bar.start()

        threading.Thread(target=self._convert_and_load_chart_data,
                         args=(html_input_path, xml_output_path, month_str),
                         daemon=True).start()

    # --- 追加メソッド: 変換後ロード処理 ---
    def _convert_and_load_chart_data(self, html_path, xml_path, month_str):
        try:
            self.parse_html_to_xml(html_path, xml_path)
            if not os.path.exists(xml_path):
                raise FileNotFoundError("XML変換後のファイルが見つかりません。")

            year, month = int(month_str[:4]), int(month_str[4:])
            self.root.after(0, lambda: self._load_data_and_trigger_chart_redraw(year, month))
            self.root.after(0, lambda: self.main_status_lbl.config(text=f"{month_str} のグラフ表示完了"))
        except Exception as e:
            self.root.after(0, lambda: messagebox.showerror("変換エラー", str(e)))
            self.root.after(0, lambda: self.main_status_lbl.config(text="変換エラー"))
        finally:
            self.root.after(0, self.main_progress_bar.stop)





    def _get_jp_weekday(self, dt): return ["月曜日","火曜日","水曜日","木曜日","金曜日","土曜日","日曜日"][dt.weekday()]
    def _update_date_and_weekday(self):
        s = self.current_date_dt.strftime("%Y/%m/%d")
        self.entries['date'].delete(0, tk.END); self.entries['date'].insert(0, s)
        self.day_of_week.config(text=self._get_jp_weekday(self.current_date_dt))
    def _on_date_change(self, e=None):
        try: self.current_date_dt = datetime.strptime(self.entries['date'].get(), "%Y/%m/%d"); self._update_date_and_weekday()
        except: self.day_of_week.config(text="無効な日付")
    def _change_date(self, delta): self.current_date_dt += timedelta(days=delta); self._update_date_and_weekday()
    def _open_calendar(self):
        top = tk.Toplevel(self.root); top.grab_set(); top.title("日付選択")
        y,m,d = self.current_date_dt.year, self.current_date_dt.month, self.current_date_dt.day
        cal = Calendar(top, selectmode='day', year=y, month=m, day=d, date_pattern='yyyy/MM/dd', locale='ja_JP'); cal.pack(padx=20, pady=20)
        def sel(): 
            try: 
                dt_date = cal.selection_get()
                self.current_date_dt = datetime(dt_date.year, dt_date.month, dt_date.day)
                self._update_date_and_weekday()
            finally: 
                top.destroy()
        ttk.Button(top, text="確定", command=sel).pack(pady=10)
        threading.Thread(target=self._toggle_calendar_month_display, daemon=True).start() # Original might be direct call
        self.show_current_calendar_month = not self.show_current_calendar_month
        self._update_calendar_month_display()
        threading.Thread(target=self._load_calendar_data, daemon=True).start() # Reload data for calendars
    def clear_form(self):
        self.current_date_dt = datetime.now(); self._update_date_and_weekday()
        for k,v in self.entries.items():
            if k not in ['date', 'employee_id']: v.delete(0, tk.END)
        self.entries['model'].focus_set()


    def submit_data(self):
        data = {}
        eid = self.employee_id
        if not eid: messagebox.showerror("内部エラー","従業員IDが設定されていません。プログラムを再起動してください。"); return
        data['employee_id'] = eid
        data['date'] = self.current_date_dt.strftime("%Y/%m/%d")
        required = {'category','item','time','department'}
        for k in ['model','number','factory_number','project_number','unit_number','category','item','time','department']:
            v = self.entries[k].get().strip()
            if k in required and not v: messagebox.showerror("入力エラー",f"「{self.labels.get(k,k)}」は必須です"); return
            data[k] = v
        try: t = float(data['time']); assert t > 0; data['time'] = t
        except: messagebox.showerror("入力エラー","時間は有効な正数を入力してください"); return
        if messagebox.askyesno("確認","送信しますか？"): 
            threading.Thread(target=self._thread_write, args=(data,), daemon=True).start()


    def _thread_write(self, data):
        append_to_deal_xml(data, self.xml_folder) 
        def done(): 
            self.main_status_lbl.config(text="データはローカルキャッシュに保存されました...")
            if hasattr(status, 'xmlfinish'): # Check if status.xmlfinish exists
                 status.xmlfinish = False
            self.main_progress_bar.stop()
        self.root.after(0, done)


    def _start_anim(self, lbl, base="検索中"): 
        def anim(c=0): 
            lbl.config(text=base + "."*(c%4))
            self.status_animation_after_id = self.root.after(300, anim, c+1)
        anim()
    def _stop_anim(self): 
        if self.status_animation_after_id: 
            self.root.after_cancel(self.status_animation_after_id)
            self.status_animation_after_id = None
    def _prepare_query(self, eid, suffix):
        if not hasattr(self, 'qry_win') or not self.qry_win.winfo_exists(): self._create_query_win()
        else: 
            self.qry_win.deiconify()
            self.qry_win.lift()
            for i in self.tree.get_children(): self.tree.delete(i)
            self.total_lbl.config(text="総勤務時間: ... 時間")
        self.qry_win.title(f"{eid} - {suffix} 検索中...")
        self.status_lbl.pack(pady=10)
        self._start_anim(self.status_lbl, base="検索中")
    def _show_daily_hours_gui(self):
        eid = self.employee_id 
        if not eid: messagebox.showerror("入力エラー","従業員IDが設定されていません。ログイン後、再試行してください。"); return
        dts = self.current_date_dt.strftime("%Y/%m/%d")
        self._prepare_query(eid, dts)
        threading.Thread(target=self._fetch_async, args=(query_daily_work_hours, (eid, dts, self.xml_folder), eid, dts, "当日"), daemon=True).start()
    def _show_current_month_hours_gui(self):
        eid = self.employee_id
        if not eid: messagebox.showerror("入力エラー","従業員IDが設定されていません。ログイン後、再試行してください。"); return
        y,m = self.current_date_dt.year, self.current_date_dt.month
        first = datetime(y,m,1)
        last = datetime(y,m,calendar.monthrange(y,m)[1])
        s,e = first.strftime("%Y/%m/%d"), last.strftime("%Y/%m/%d")
        disp = f"{y}年{m:02d}月"
        self._prepare_query(eid, disp)
        threading.Thread(target=self._fetch_async, args=(query_monthly_work_hours, (eid,s,e, self.xml_folder), eid, disp, "当月"), daemon=True).start()
    def _show_prev_month_hours_gui(self):
        eid = self.employee_id
        if not eid: messagebox.showerror("入力エラー","従業員IDが設定されていません。ログイン後、再試行してください。"); return
        first_cur = self.current_date_dt.replace(day=1)
        last_prev = first_cur - timedelta(days=1)
        first_prev = last_prev.replace(day=1)
        s,e = first_prev.strftime("%Y/%m/%d"), last_prev.strftime("%Y/%m/%d")
        disp = f"{first_prev.year}年{first_prev.month:02d}月"
        self._prepare_query(eid, disp)
        threading.Thread(target=self._fetch_async, args=(query_monthly_work_hours, (eid,s,e, self.xml_folder), eid, disp, "前月"), daemon=True).start()
    def _create_query_win(self):
        self.qry_win = tk.Toplevel(self.root)
        self.qry_win.geometry("1000x500")
        self.qry_win.protocol("WM_DELETE_WINDOW", self._on_query_close)
        self.status_lbl = ttk.Label(self.qry_win, text="初期化中...", font=("Arial",12))
        self.status_lbl.pack(pady=10)
        tf = ttk.Frame(self.qry_win)
        tf.pack(fill="both", expand=True, padx=10, pady=(0,5))
        self.tree = ttk.Treeview(tf, columns=cols, show='headings')
        names = {'従業員ｺｰﾄﾞ':'従業員ID','日付':'日付','機種':'機種','号機':'号機','区分':'区分','項目':'項目','時間':'時間(H)','所属ｺｰﾄﾞ':'部門','工場製番':'工場製番','工事番号':'工事番号','ﾕﾆｯﾄ番号':'ユニット番号', 'DB_ID':'DB ID'}
        widths = {'従業員ｺｰﾄﾞ':80,'日付':100,'機種':80,'号機':80,'区分':70,'項目':120,'時間':60,'所属ｺｰﾄﾞ':70,'工場製番':100,'工事番号':100,'ﾕﾆｯﾄ番号':80, 'DB_ID':50}
        for c in cols: 
            self.tree.heading(c, text=names.get(c,c), command=lambda _c=c: self._sort_col(self.tree, _c, False))
            self.tree.column(c, width=widths.get(c,80), anchor='center')
        vs = ttk.Scrollbar(tf, orient="vertical", command=self.tree.yview)
        hs = ttk.Scrollbar(tf, orient="horizontal", command=self.tree.xview)
        hs.pack(side=tk.BOTTOM, fill="x")
        self.tree.pack(side=tk.LEFT, fill="both", expand=True)
        vs.pack(side=tk.RIGHT, fill="y")
        self.tree.configure(yscrollcommand=vs.set, xscrollcommand=hs.set)
        self.total_lbl = ttk.Label(self.qry_win, text="総勤務時間: ... 時間", font=("Arial",12,"bold"))
        self.total_lbl.pack(pady=10, side=tk.BOTTOM)
    def _on_query_close(self): 
        self._stop_anim() 
        if hasattr(self, 'qry_win') and self.qry_win: 
            self.qry_win.withdraw()
    def _fetch_async(self, func, args, eid, disp, ptype): 
        recs = func(*args)
        self.root.after(0, lambda: self._update_tree(recs, eid, disp, ptype))
    def _update_tree(self, recs, eid, disp, ptype):
        self._stop_anim()
        if not hasattr(self, 'qry_win') or not self.qry_win.winfo_exists(): self._create_query_win()
        self.qry_win.title(f"{eid} - {disp} 勤務記録")
        for i in self.tree.get_children(): self.tree.delete(i)
        if recs is None: 
            self.status_lbl.config(text=f"{ptype} の検索エラー")
            self.total_lbl.config(text="総勤務時間: N/A")
            return
        if not recs: 
            self.status_lbl.config(text=f"{eid} は {disp} に記録がありません")
            self.total_lbl.config(text="総勤務時間: 0.00 時間")
            return
        self.status_lbl.config(text="")
        total = 0.0
        for r in recs:
            vals = [r.get(c, '') if not (isinstance(r.get(c,''), str) and r.get(c,'').strip().lower()=='none') else '' for c in cols]
            self.tree.insert('', tk.END, values=vals)
            total += float(r.get('時間', 0) or 0)
        self.total_lbl.config(text=f"{ptype}総勤務時間: {total:.2f} 時間")
    def _sort_col(self, tv, col, rev):
        l = [(tv.set(k,col),k) for k in tv.get_children('')]
        try: l.sort(key=lambda t: float(t[0]) if col == '時間' else t[0], reverse=rev)
        except ValueError: l.sort(key=lambda t: t[0], reverse=rev)
        for idx, (_,k) in enumerate(l): tv.move(k,'',idx)
        tv.heading(col, command=lambda: self._sort_col(tv,col, not rev))
    def _load_calendar_data(self):
        self.root.after(0, self.main_progress_bar.start)
        self.root.after(0, lambda: self.main_status_lbl.config(text="データ読込中..."))
        eid = self.employee_id
        if not eid: 
            self.root.after(0, self.main_progress_bar.stop)
            self.root.after(0, lambda: self.main_status_lbl.config(text="従業員ID未設定..."))
            return
        
        y,m = self.today_dt.year, self.today_dt.month
        first_cur = datetime(y,m,1)
        last_cur = datetime(y,m,calendar.monthrange(y,m)[1])
        prev_last = first_cur - timedelta(days=1)
        first_prev = prev_last.replace(day=1)
        
        recs = query_monthly_work_hours(eid, first_prev.strftime("%Y/%m/%d"), last_cur.strftime("%Y/%m/%d"), self.xml_folder) or []
        
        records_prev = [r for r in recs if r.get('日付','').startswith(first_prev.strftime("%Y/%m"))]
        records_cur = [r for r in recs if r.get('日付','').startswith(first_cur.strftime("%Y/%m"))]
        
        filled_prev = {int(r['日付'].split('/')[-1]) for r in records_prev if r.get('日付')}
        filled_cur = {int(r['日付'].split('/')[-1]) for r in records_cur if r.get('日付')}
        
        self.root.after(0, lambda: self._update_calendar(self.cal_prev, filled_prev, prev_last.year, prev_last.month))
        self.root.after(0, lambda: self._update_calendar(self.cal_current, filled_cur, y,m))
        
        records_cur.sort(key=lambda x: (x.get('日付',''), x.get('時間',0.0)), reverse=True)
        records_prev.sort(key=lambda x: (x.get('日付',''), x.get('時間',0.0)), reverse=True)
        
        self.root.after(0, lambda: self._update_table(self.tree_prev_all, records_prev))
        self.root.after(0, lambda: self._update_table(self.tree_cur_all, records_cur))
        self.root.after(0, self.main_progress_bar.stop)
        self.root.after(0, lambda: self.main_status_lbl.config(text="データ読込完了。"))
    def _update_calendar(self, cal, filled_days, year, month):
        today = self.today_dt
        for tag in ['filled','empty','today_highlight']:
            for ev_id in cal.get_calevents(tag=tag):
                 cal.calevent_remove(ev_id)
        
        for d in range(1, calendar.monthrange(year,month)[1]+1):
            dt = datetime(year,month,d)
            if dt.weekday() >= 5: continue # Skip weekends
            if dt.date() <= today.date(): 
                cal.calevent_create(dt, '', 'filled' if d in filled_days else 'empty')
    def _update_table(self, tree, records): 
        for i in tree.get_children(): 
            tree.delete(i)
        for r in records: 
            vals = [r.get(c,'') if not (isinstance(r.get(c,''),str) and r.get(c,'').strip().lower()=='none') else '' for c in cols]
            tree.insert('',tk.END,values=vals)
    def _toggle_month_table_display(self):
        if self.show_current_month_table:
            self.tree_cur_all.grid_forget()
            self.vs2.grid_forget()
            self.tree_prev_all.grid(row=1,column=1,padx=5,pady=5,sticky="nsew",rowspan=2)
            self.vs1.grid(row=1,column=2,sticky="ns",pady=5,rowspan=2)
            self.toggle_table_btn.config(text="当月勤務時間表示")
            self.show_current_month_table = False
        else:
            self.tree_prev_all.grid_forget()
            self.vs1.grid_forget()
            self.tree_cur_all.grid(row=1,column=1,padx=5,pady=5,sticky="nsew",rowspan=2)
            self.vs2.grid(row=1,column=2,sticky="ns",pady=5,rowspan=2)
            self.toggle_table_btn.config(text="前月勤務時間表示")
            self.show_current_month_table = True
        threading.Thread(target=self._load_calendar_data, daemon=True).start()
    def _update_calendar_month_display(self):
        if self.show_current_calendar_month:
            self.cal_prev.grid_forget()
            self.cal_current.grid(row=1,column=0,sticky="nsew",padx=5,pady=5)
            self.month_display_lbl.config(text=self.today_dt.strftime("当月 %Y年%m月"))
            self.toggle_calendar_btn.config(text="前月カレンダー表示")
        else:
            self.cal_current.grid_forget()
            self.cal_prev.grid(row=1,column=0,sticky="nsew",padx=5,pady=5)
            prev_month = self.today_dt.replace(day=1)-timedelta(days=1)
            self.month_display_lbl.config(text=prev_month.strftime("前月 %Y年%m月"))
            self.toggle_calendar_btn.config(text="当月カレンダー表示")
    def _toggle_calendar_month_display(self): 
        self.show_current_calendar_month = not self.show_current_calendar_month
        self._update_calendar_month_display()
        threading.Thread(target=self._load_calendar_data, daemon=True).start()
    def _fetch_timepro_report_gui(self):
        if not self.employee_id or not self.timepro_password: 
            messagebox.showerror("エラー", "従業員IDまたはTimeProパスワードが設定されていません。")
            return
        if not self.timepro_output_dir: 
            messagebox.showerror("エラー", "TimePro出力ディレクトリが設定されていません。")
            return
        if not self.fetch_timepro_data:
             messagebox.showerror("エラー", "TimeProデータ読込機能が初期化されていません。")
             return

        self.fetch_timepro_btn.config(state=tk.DISABLED)
        self.main_status_lbl.config(text=f"TimeProからデータ読込中 ({self.employee_id})...")
        self.main_progress_bar.start()
        ensure_folder(self.timepro_output_dir)
        threading.Thread(target=self._fetch_timepro_task, args=(self.employee_id, self.timepro_password, self.timepro_output_dir), daemon=True).start()
    def _fetch_timepro_task(self, emp_id, emp_password, output_dir):
        try:
            print(f"[UI] Starting TimePro data fetch for employee {emp_id}...")
            success, message = self.fetch_timepro_data(emp_id, emp_password, output_dir) # Use stored function
            status_msg = f"TimeProデータ読込成功: {message}" if success else f"TimeProデータ読込失敗: {message}"
            self.root.after(0, lambda: self.main_status_lbl.config(text=status_msg))
            print(f"[UI] TimePro fetch for {emp_id} {'successful' if success else 'failed'}: {message}")
        except Exception as e: 
            self.root.after(0, lambda: self.main_status_lbl.config(text=f"TimePro読込中に予期せぬエラー発生: {e}"))
            print(f"[UI Critical Error] Fetching TimePro data: {e}")
        finally: 
            self.root.after(0, self.main_progress_bar.stop)
            self.root.after(0, lambda: self.fetch_timepro_btn.config(state=tk.NORMAL))
            print(f"[UI] TimePro fetch task for {emp_id} finished.")
    def _convert_timepro_html_to_xml_gui(self):
        if not self.timepro_output_dir: 
            messagebox.showerror("エラー", "TimePro出力ディレクトリが設定されていません。")
            return
        if not self.parse_html_to_xml:
            messagebox.showerror("エラー", "HTML→XML変換機能が初期化されていません。")
            return
            
        html_input_path = os.path.join(self.timepro_output_dir, "body_content.html")
        xml_output_path = os.path.join(self.timepro_output_dir, "timepro_report.xml")
        
        if not os.path.exists(html_input_path): 
            # Ensure the f-string for messagebox is correctly escaped
            msg = f'HTMLファイルが見つかりません: {html_input_path}\\nまず「TimePro読込」機能でこのファイルを生成してください。'
            messagebox.showerror("エラー", msg, parent=self.root)
            return
            
        self.convert_html_to_xml_btn.config(state=tk.DISABLED)
        self.main_status_lbl.config(text=f"変換中: {os.path.basename(html_input_path)}...")
        self.main_progress_bar.start()
        threading.Thread(target=self._convert_html_to_xml_task, args=(html_input_path, xml_output_path), daemon=True).start()
    def _convert_html_to_xml_task(self, html_path, xml_path):
        try:
            print(f"[UI] Converting: {html_path} -> {xml_path}")
            self.parse_html_to_xml(html_path, xml_path) # Use stored function
            status_msg = f"HTML→XML変換成功: {os.path.basename(xml_path)}" if os.path.exists(xml_path) else "HTML→XML変換失敗。コンソールログを確認してください。"
            self.root.after(0, lambda: self.main_status_lbl.config(text=status_msg))
            print(f"[UI] Conversion {'successful' if os.path.exists(xml_path) else 'failed'}: {xml_path}")
            if os.path.exists(xml_path):
                self.root.after(0, lambda: self._load_and_draw_chart_data_from_path(xml_path))
        except Exception as e: 
            self.root.after(0, lambda: self.main_status_lbl.config(text=f"HTML→XML変換エラー: {e}"))
            print(f"[UI Critical Error] Converting HTML to XML: {e}")
        finally: 
            self.root.after(0, self.main_progress_bar.stop)
            self.root.after(0, lambda: self.convert_html_to_xml_btn.config(state=tk.NORMAL))
            print(f"[UI] Conversion task for {html_path} finished.")
    def _synchronize_xml_gui(self):
        eid = self.employee_id
        if not eid: 
            self.main_status_lbl.config(text="エラー：従業員ID未設定...")
            return
        self.sync_xml_btn.config(state=tk.DISABLED)
        self.main_status_lbl.config(text=f"XMLデータ同期中 ({eid})...")
        self.main_progress_bar.start()
        # Pass self.xml_folder to the task if init_employee_xml requires it
        threading.Thread(target=self._sync_xml_task, args=(eid, self.xml_folder), daemon=True).start()
    def _sync_xml_task(self, employee_id, current_xml_folder): # Added current_xml_folder
        try:
            print(f"[UI] Syncing XML for {employee_id} in folder {current_xml_folder}...")
            # Assuming init_employee_xml is globally available or imported in this file.
            # If init_employee_xml needs the folder, it should be passed or configured within that module
            # For now, assuming init_employee_xml knows its target folder or xml_folder needs to be passed.
            # If init_employee_xml is defined in xmlinit.py and uses a global XML_FOLDER from there,
            # ensure that global is correctly set or pass current_xml_folder to it.
            # For this refactor, let's assume init_employee_xml needs the employee_id and finds the folder.
            # However, if xmlinit.init_employee_xml expects the folder path:
            # init_employee_xml(employee_id, xml_folder=current_xml_folder)
            init_employee_xml(employee_id) # Original call, assuming it handles its path
            
            self.root.after(0, lambda: self.main_status_lbl.config(text="XML同期成功！更新中..."))
            self.root.after(0, self._load_calendar_data) # This uses self.xml_folder
            print(f"[UI] XML sync for {employee_id} complete.")
        except TimeoutError: 
            self.root.after(0, lambda: self.main_status_lbl.config(text="同期タイムアウト（3分）。"))
        except Exception as e: 
            self.root.after(0, lambda: self.main_status_lbl.config(text=f"同期エラー: {e}"))
            print(f"[UI Error] XML sync for {employee_id}: {e}")
        finally: 
            self.root.after(0, self.main_progress_bar.stop)
            self.root.after(0, lambda: self.sync_xml_btn.config(state=tk.NORMAL))
            print(f"[UI] XML sync task for {employee_id} finished.")

    def _delete_selected_from_db(self):
        active_tree = None
        # Determine which tree is active based on the toggle state for table display
        if self.show_current_month_table: # This flag indicates self.tree_cur_all is visible
            active_tree = self.tree_cur_all
        else: # self.tree_prev_all is visible
            active_tree = self.tree_prev_all

        selected_items = active_tree.selection()
        if not selected_items:
            messagebox.showwarning("選択なし", "削除する行を選択してください。", parent=self.root)
            return
        
        if len(selected_items) > 1:
            messagebox.showwarning("複数選択", "一度に削除できるのは1行のみです。", parent=self.root)
            return
            
        selected_item = selected_items[0]
        item_values = active_tree.item(selected_item, 'values')

        try:
            db_id_index = cols.index('DB_ID') # cols is global
            entry_to_delete_id = item_values[db_id_index]
        except (ValueError, IndexError):
            messagebox.showerror("エラー", "選択された行からDB IDを取得できませんでした。\n列定義を確認してください。", parent=self.root)
            return

        if not entry_to_delete_id or str(entry_to_delete_id).strip() == '':
            messagebox.showerror("エラー", "選択された行のDB IDが無効です。", parent=self.root)
            return

        if messagebox.askyesno("確認", f"ID '{entry_to_delete_id}' の記録をデータベースから削除しますか？\nこの操作は取り消せません。", parent=self.root):
            self.main_status_lbl.config(text=f"ID {entry_to_delete_id} をデータベースから削除中...")
            self.main_progress_bar.start()
            self.delete_db_entry_btn.config(state=tk.DISABLED)

            threading.Thread(target=self._perform_db_delete_task, args=(str(entry_to_delete_id),), daemon=True).start()

    def _perform_db_delete_task(self, entry_id_str):
        try:
            success, message = delete_work_time_by_id(entry_id_str) # Imported from db_utils
            
            if success:
                self.root.after(0, lambda: self.main_status_lbl.config(text=f"ID {entry_id_str} 削除成功。XML同期を開始します..."))
                # Trigger XML sync by calling its GUI initiating method
                self.root.after(100, self._synchronize_xml_gui) 
            else:
                self.root.after(0, lambda: messagebox.showerror("削除失敗", f"データベースからの削除に失敗しました:\n{message}", parent=self.root))
                self.root.after(0, lambda: self.main_status_lbl.config(text=f"ID {entry_id_str} 削除失敗。"))
        
        except Exception as e:
            self.root.after(0, lambda: messagebox.showerror("削除エラー", f"削除処理中に予期せぬエラーが発生しました:\n{e}", parent=self.root))
            self.root.after(0, lambda: self.main_status_lbl.config(text="削除処理中にエラー。"))
        finally:
            self.root.after(0, self.main_progress_bar.stop)
            self.root.after(0, lambda: self.delete_db_entry_btn.config(state=tk.NORMAL))


    # --- Chart Drawing Methods ---
    def _attempt_initial_chart_load(self):
        if not self.timepro_output_dir:
            print("[UI Chart DEBUG] TimePro output directory not set. Cannot load chart.")
            # Ensure chart_display_year/month are set before calling redraw
            self.chart_display_year = self.current_date_dt.year
            self.chart_display_month = self.current_date_dt.month
            self.chart_raw_data = None # Explicitly None for error
            self.root.after(0, lambda: self.chart_month_label.config(text=f"{self.chart_display_year}年{self.chart_display_month:02d}月 勤務時間グラフ (設定エラー)"))
            self.root.after(0, self._redraw_chart_on_canvas)
            return

        # Default to loading current system month
        now = datetime.now()
        print(f"[UI Chart DEBUG] Attempting initial chart load for system current month: {now.year}/{now.month:02d}")
        self._load_data_and_trigger_chart_redraw(now.year, now.month)

    def _show_previous_month_chart_data(self):
        now = datetime.now()
        first_day_current_month = now.replace(day=1)
        last_day_prev_month = first_day_current_month - timedelta(days=1)
        self._load_data_and_trigger_chart_redraw(last_day_prev_month.year, last_day_prev_month.month)

    def _show_current_month_chart_data(self):
        now = datetime.now()
        self._load_data_and_trigger_chart_redraw(now.year, now.month)

    def _load_data_and_trigger_chart_redraw(self, target_year, target_month):
        self.chart_display_year = target_year
        self.chart_display_month = target_month

                # ▼▼▼ 変更: XMLパスをターゲット月ベースに ▼▼▼
        xml_report_path = os.path.join(
            self.timepro_output_dir,
            f"{target_year}{target_month:02d}_timepro_report.xml"
        )
        self.timepro_xml_path = xml_report_path  # compare_time_for_date 用
        # ▲▲▲ 変更ここまで ▲▲▲
        self.main_status_lbl.config(text=f"グラフデータ読込中 ({target_year}/{target_month:02d})...")
        self.main_progress_bar.start()
        self.chart_month_label.config(text=f"{target_year}年{target_month:02d}月 勤務時間グラフ (読込中...)")

        # 修正点: ここで xml_report_path が誤って上書きされていました。
        # 上でターゲット月ベースのパスが正しく設定されているため、この行は不要です。
        # xml_report_path = os.path.join(self.timepro_output_dir, "timepro_report.xml") # この行は削除

        if not os.path.exists(xml_report_path):
            print(f"[UI Chart DEBUG] Chart XML file NOT FOUND at {xml_report_path} for target {target_year}/{target_month:02d}")
            self.chart_raw_data = None # Explicitly None as file is missing
            self.root.after(0, self.main_progress_bar.stop)
            self.root.after(0, lambda: self.main_status_lbl.config(text=f"グラフデータファイルなし ({xml_report_path})"))
            self.root.after(0, lambda: self.chart_month_label.config(text=f"{target_year}年{target_month:02d}月 勤務時間グラフ (データファイルなし)"))
            self.root.after(0, self._redraw_chart_on_canvas)
            return

        threading.Thread(target=self._process_xml_for_chart_for_target, args=(xml_report_path, target_year, target_month), daemon=True).start()

    def _process_xml_for_chart_for_target(self, xml_path, target_year, target_month): # Runs in a thread
        """
        Processes the XML for a specific target year and month.
        Updates self.chart_raw_data and schedules a redraw.
        """
        print(f"[UI Chart DEBUG] Thread: Processing XML {xml_path} for target: {target_year}/{target_month:02d}")
        parsed_records = self._parse_xml_records_for_specific_month(xml_path, target_year, target_month)
        
        self.chart_raw_data = parsed_records # Can be None (error), [] (no data for month), or list of records

        # self.chart_display_year and self.chart_display_month are already set by the caller

        def update_ui_after_processing():
            self.main_progress_bar.stop()
            status_message = f"グラフデータ読込完了 ({target_year}/{target_month:02d})"
            if self.chart_raw_data is None:
                status_message = f"グラフデータ読込エラー ({target_year}/{target_month:02d})"
            elif not self.chart_raw_data:
                status_message = f"グラフデータなし ({target_year}/{target_month:02d})"
            self.main_status_lbl.config(text=status_message)
            # chart_month_label is updated by _redraw_chart_on_canvas
            self._redraw_chart_on_canvas()

        self.root.after(0, update_ui_after_processing)


    def _parse_xml_records_for_specific_month(self, xml_path, target_year, target_month):
        """
        Parses the TimePro XML and filters records for the specified target_year and target_month.
        Returns a list of record dictionaries for the chart, or None if a parsing error occurs,
        or an empty list if no data is found for the target month.
        """
        records = []
        print(f"[UI Chart DEBUG] Parsing XML {xml_path} specifically for {target_year}/{target_month:02d}")
        
        try:
            if not os.path.exists(xml_path): # Should be caught by caller, but double check
                print(f"[UI Chart DEBUG] XML file not found (parse stage): {xml_path}")
                return None 
            
            tree = ET.parse(xml_path)
            root_xml = tree.getroot()
            
            for record_node in root_xml.findall('record'):
                date_str, clock_in_str, clock_out_str = "", "", ""
                for field_node in record_node.findall('field'):
                    name = field_node.get('name')
                    text = field_node.text if field_node.text else ""
                    if name == "日付": date_str = text
                    elif name == "出勤時刻": clock_in_str = text
                    elif name == "退勤時刻": clock_out_str = text
                
                if date_str:
                    try:
                        rec_date = datetime.strptime(date_str, "%Y/%m/%d")
                        if rec_date.year == target_year and rec_date.month == target_month:
                            # Debug print before parsing calls for this specific record
                            # print(f"[UI Chart PRE-PARSE] For Day {rec_date.day} - About to parse clock_in_str: '{clock_in_str}'")
                            clock_in_tuple = self._parse_time_string_for_chart(clock_in_str)
                            # print(f"[UI Chart PRE-PARSE] For Day {rec_date.day} - About to parse clock_out_str: '{clock_out_str}'")
                            clock_out_tuple = self._parse_time_string_for_chart(clock_out_str)
                            records.append({
                                'day': rec_date.day,
                                'clock_in': clock_in_tuple,
                                'clock_out': clock_out_tuple,
                            })
                    except ValueError:
                        # print(f"[UI Chart] Skipping record with invalid date format during filtering: {date_str}")
                        continue
            
            print(f"[UI Chart DEBUG] Filtered {len(records)} records for display in chart for {target_year}/{target_month:02d}.")
            return records

        except ET.ParseError as e:
            print(f"[UI Chart DEBUG] Error parsing XML {xml_path} for chart: {e}")
            return None 
        except Exception as e:
            print(f"[UI Chart DEBUG] Unexpected error processing XML {xml_path} for chart: {e}")
            return None


    def _load_and_draw_chart_data_from_path(self, xml_path):
        # This method is called after HTML->XML conversion.
        # It should now default to loading the current system month from the newly converted XML.
        now = datetime.now()
        print(f"[UI Chart DEBUG] Post-conversion: Triggering chart load for current month {now.year}/{now.month:02d} from {xml_path}")
        # The xml_path argument here is the one just converted.
        # We need to ensure _load_data_and_trigger_chart_redraw uses this specific path
        # if it's different from the default self.timepro_output_dir / "timepro_report.xml",
        # or ensure that timepro_report.xml IS the path.
        # For now, assuming xml_path IS "timepro_report.xml"
        self._load_data_and_trigger_chart_redraw(now.year, now.month)


    def _process_xml_for_chart(self, xml_path): # OBSOLETE - replaced by _process_xml_for_chart_for_target
        # This method will be removed or adapted.
        # For now, let's mark it clearly and ensure it's not called.
        raise NotImplementedError("_process_xml_for_chart is obsolete and should not be called. Use _process_xml_for_chart_for_target.")
        # target_year, target_month, parsed_records = self._parse_timepro_xml_for_chart(xml_path) # Old parsing call
        
        # self.chart_raw_data = parsed_records 

        # if target_year is not None and target_month is not None:
        #     self.chart_display_year = target_year
        #     self.chart_display_month = target_month
        # else:
        #     self.chart_display_year = self.current_date_dt.year
        #     self.chart_display_month = self.current_date_dt.month
            
        # self.root.after(0, self._redraw_chart_on_canvas)

    def _parse_time_string_for_chart(self, time_str):
        """
        将"出勤時刻"或"退勤時刻"字符串解析为 (小时, 分钟) 的整数元组。
        支持诸如 "8:12", "当 8:12", "9:05" 等格式。
        如果无法解析（为空串、"----" 或不含时间部分），返回 None。
        """
        if not time_str or time_str == "----":
            return None

        # 使用正则匹配 "小时:分钟" 部分
        m = re.search(r"(\d{1,2}):(\d{2})", time_str)
        if not m:
            return None

        hour = int(m.group(1))
        minute = int(m.group(2))
        return hour, minute


    def _on_chart_canvas_resize(self, event=None):
        # Schedule the redraw to ensure canvas dimensions are stable
        self.time_chart_canvas.after_idle(self._redraw_chart_on_canvas)

    def _time_to_y_coord(self, hour, minute, chart_area_y_start, chart_area_height,
                           axis_start_hour, axis_end_hour):
        time_in_hours = hour + minute / 60.0
        total_hours_in_axis = axis_end_hour - axis_start_hour
        if total_hours_in_axis <= 0: 
            # Fallback: bottom of chart area if axis is invalid
            return chart_area_y_start + chart_area_height 

        proportion = (time_in_hours - axis_start_hour) / total_hours_in_axis
        proportion = max(0.0, min(1.0, proportion)) # Clamp proportion to [0, 1]
            
        # Inverted: smaller time (e.g. 6am, proportion 0) should be at the bottom (larger Y)
        # larger time (e.g. 10pm, proportion 1) should be at the top (smaller Y)
        return (chart_area_y_start + chart_area_height) - (proportion * chart_area_height)

    def _redraw_chart_on_canvas(self):
        canvas = self.time_chart_canvas
        canvas.delete("all")

        # Define constants - consider moving to class level if widely used
        CHART_Y_AXIS_START_HOUR = 6
        CHART_Y_AXIS_END_HOUR = 22
        CHART_X_PADDING_LEFT = 50 
        CHART_X_PADDING_RIGHT = 20
        CHART_Y_PADDING_TOP = 20   
        CHART_Y_PADDING_BOTTOM = 30
        GRID_COLOR = "#e0e0e0"
        AXIS_LABEL_COLOR = "black"
        BAR_FILL_COLOR = "skyblue"
        BAR_OUTLINE_COLOR = "blue"

        c_width = canvas.winfo_width()
        c_height = canvas.winfo_height()

        current_display_year = getattr(self, 'chart_display_year', self.current_date_dt.year)
        current_display_month = getattr(self, 'chart_display_month', self.current_date_dt.month)
        print(f"[UI Chart DEBUG] Redrawing chart. Canvas: {c_width}x{c_height}. Target display: {current_display_year}/{current_display_month:02d}. Raw data type: {type(self.chart_raw_data)}")
        if self.chart_raw_data is not None:
            print(f"[UI Chart DEBUG] Raw data length: {len(self.chart_raw_data)}")

        if c_width <= 1 or c_height <= 1: # Canvas not ready
            self.chart_month_label.config(text=f"{current_display_year}年{current_display_month:02d}月 勤務時間グラフ (調整中)")
            return

        if self.chart_raw_data is None: # Error state (e.g. file not found, parse error)
            no_data_msg = "グラフデータ読込失敗またはファイル未存在"
            print(f"[UI Chart DEBUG] Chart raw data is None. Displaying: {no_data_msg}")
            canvas.create_text(c_width / 2, c_height / 2, text=no_data_msg,
                               font=("Arial", 12), fill="grey", anchor=tk.CENTER)
            self.chart_month_label.config(text=f"{current_display_year}年{current_display_month:02d}月 勤務時間グラフ - エラー")
            return
            
        data_for_chart = self.chart_raw_data 
        
        self.chart_month_label.config(text=f"{current_display_year}年{current_display_month:02d}月 勤務時間グラフ")

        if not data_for_chart: # Empty list: successfully parsed for month, but no entries
            print(f"[UI Chart DEBUG] Chart data for {current_display_year}/{current_display_month:02d} is empty. Displaying: 当月勤務記録なし")
            canvas.create_text(c_width / 2, c_height / 2, text="当月勤務記録なし",
                               font=("Arial", 12), fill="grey", anchor=tk.CENTER)
            return
            
        print(f"[UI Chart DEBUG] Drawing {len(data_for_chart)} records for {current_display_year}/{current_display_month:02d}.")
        # Data exists for current_display_year, current_display_month
        num_days_in_month = calendar.monthrange(current_display_year, current_display_month)[1]

        chart_area_x_start = CHART_X_PADDING_LEFT
        chart_area_y_start = CHART_Y_PADDING_TOP
        chart_draw_width = c_width - CHART_X_PADDING_LEFT - CHART_X_PADDING_RIGHT
        chart_draw_height = c_height - CHART_Y_PADDING_TOP - CHART_Y_PADDING_BOTTOM

        if chart_draw_width <= 0 or chart_draw_height <= 0: return # Not enough space

        # Draw Y-axis (Time labels and grid lines)
        hours_on_axis = CHART_Y_AXIS_END_HOUR - CHART_Y_AXIS_START_HOUR
        for i in range(hours_on_axis + 1):
            hour_val = CHART_Y_AXIS_START_HOUR + i
            # y = chart_area_y_start + (i / hours_on_axis) * chart_draw_height # Original (6am at top)
            # Inverted: 6am (i=0) at bottom, 22am (i=hours_on_axis) at top
            y = (chart_area_y_start + chart_draw_height) - (i / hours_on_axis) * chart_draw_height
            
            canvas.create_line(chart_area_x_start - 5, y, chart_area_x_start + chart_draw_width, y, fill=GRID_COLOR if i != 0 and i != hours_on_axis else AXIS_LABEL_COLOR)
            canvas.create_text(chart_area_x_start - 10, y, text=f"{hour_val:02d}:00", anchor=tk.E, fill=AXIS_LABEL_COLOR)

        # Draw X-axis (Day labels and grid lines)
        day_slot_width = chart_draw_width / num_days_in_month
        x_axis_y_pos = chart_area_y_start + chart_draw_height
        
        for day in range(1, num_days_in_month + 1):
            x_center = chart_area_x_start + (day - 0.5) * day_slot_width
            x_line_start = chart_area_x_start + (day - 1) * day_slot_width
            
            # Vertical grid line for each day
            canvas.create_line(x_line_start, chart_area_y_start, x_line_start, x_axis_y_pos, fill=GRID_COLOR)

            # Determine the date to check its weekday
            current_date_for_weekday_check = datetime(current_display_year, current_display_month, day)
            weekday = current_date_for_weekday_check.weekday() # Monday=0, ..., Sunday=6
            
            label_text = ""
            if weekday < 5: # Monday to Friday
                label_text = str(day)
            elif weekday == 5: # Saturday
                label_text = "土"
            elif weekday == 6: # Sunday
                label_text = "日"
            
            if label_text: # Only create text if there's something to display
                canvas.create_text(x_center, x_axis_y_pos + 10, text=label_text, anchor=tk.N, fill=AXIS_LABEL_COLOR)
        # Final vertical line at the end of the chart area
        canvas.create_line(chart_area_x_start + chart_draw_width, chart_area_y_start, chart_area_x_start + chart_draw_width, x_axis_y_pos, fill=GRID_COLOR)


        # Draw Data Bars
        for record in data_for_chart:
            day = record['day']
            clock_in = record['clock_in']
            clock_out = record['clock_out']
             # 修改点3：构造当前条目的完整日期字符串
            date_str = f"{self.chart_display_year}/{self.chart_display_month:02d}/{day:02d}"
            # 修改点4：调用比较函数，若不匹配则用红色高亮
            matched = compare_time_for_date(date_str, self.timepro_xml_path, self.local_xml_path)
            fill_col    = BAR_FILL_COLOR    if matched else "red"
            outline_col = BAR_OUTLINE_COLOR if matched else "red"

            print(f"[UI Chart DEBUG] Drawing bar for Day: {day}, In: {clock_in}, Out: {clock_out}") # ADDED

            if clock_in and clock_out:
                # Handle cases where clock_in might be > clock_out (e.g. overnight shift ending early morning)
                # For this chart, if start > end, maybe skip or draw a dot. Simple bar assumes start < end on same day axis.
                if (clock_in[0] + clock_in[1]/60.0) >= (clock_out[0] + clock_out[1]/60.0):
                    # Optionally draw a dot or skip, for now skip invalid range for a bar
                    # print(f"[UI Chart] Skipping day {day} due to clock_in >= clock_out: {clock_in} -> {clock_out}")
                    # Could draw clock_in as a point:
                    y1 = self._time_to_y_coord(clock_in[0], clock_in[1], chart_area_y_start, chart_draw_height, CHART_Y_AXIS_START_HOUR, CHART_Y_AXIS_END_HOUR)
                    bar_x_start = chart_area_x_start + (day - 1) * day_slot_width
                    bar_x_end = bar_x_start + day_slot_width
                    canvas.create_oval(bar_x_start + day_slot_width/2 - 2, y1 -2, bar_x_start + day_slot_width/2 + 2, y1 + 2, fill="red", outline="red")
                    continue


                y1 = self._time_to_y_coord(clock_in[0], clock_in[1], chart_area_y_start, chart_draw_height, CHART_Y_AXIS_START_HOUR, CHART_Y_AXIS_END_HOUR)
                y2 = self._time_to_y_coord(clock_out[0], clock_out[1], chart_area_y_start, chart_draw_height, CHART_Y_AXIS_START_HOUR, CHART_Y_AXIS_END_HOUR)

                bar_x_start = chart_area_x_start + (day - 1) * day_slot_width
                bar_x_end = bar_x_start + day_slot_width
                
                # Add a small padding within the day slot for the bar
                #canvas.create_rectangle(bar_x_start + 2, y1, bar_x_end - 2, y2, 
                #                        fill=BAR_FILL_COLOR, outline=BAR_OUTLINE_COLOR)
                
                canvas.create_rectangle(
                bar_x_start + 2, y1, bar_x_end - 2, y2,
                fill=fill_col, outline=outline_col)

# Global constant for chart, if _parse_time_string_for_chart needs it outside class context
# Though it's a method, so self.CHART_Y_AXIS_START_HOUR is fine. Let's assume it is.
# The method _parse_time_string_for_chart currently doesn't use CHART_Y_AXIS_START_HOUR,
# but it was mentioned in thought process. Let's add it at class level for consistency.
CHART_Y_AXIS_START_HOUR = 6 # Duplicates for clarity for the helper if it were global
# No, it's fine as method.

