
（触发器+notify）--Listener f1  --  推送回写 f2---（读取  entries_push_queue）--（写入/更新/删除）--32位Access数据。
entries_push_queue中的，`synced=TRUE`

但是（写入/更新/删除）的操作的执行稍有不同，具体就是，"写入"操作中多了个步骤：取 `external_id ` → UPDATE entries.external_id 
也就是 **任何插入** 都能在 "写 MDB 的同一事务" 拿到 `external_id` 并立即补到 PG。

具体流程是：

| **INSERT** | UI→entries (external_id =NULL) → 触发器入队+NOTIFY → worker 插 MDB → 取 `external_id ` → UPDATE entries.external_id  → `synced=TRUE` →   运行一次专属id同步函数 f6     |
| **UPDATE** | UI→entries (带 external_id ) → 入队 → worker `UPDATE MDB WHERE ID=external_id ` → `synced=TRUE` →   运行一次专属id同步函数 f6     |
| **DELETE** | UI `DELETE FROM entries WHERE external_id ` → 入队 → worker `DELETE MDB WHERE ID=external_id ` → `synced=TRUE`  →   运行一次专属id同步函数 f6     |


修改/说明 4：
关于插入操作涉及到的操作细节：

下面把「**Insert 场景**」进一步拆分：
假设，mdb，插入数据操作之后，返回专属id42，
 **external\_id = 42** 是 *插进 MDB 的那个瞬间* 拿到的；

## 0. 假设的前提记号

| 记号         | 值         | 说明                          |
| ---------- | --------- | --------------------------- |
| `id_pg`    | 123       | entries 的内部 ID（`BIGSERIAL`） |
| `external_id `   | *NULL*→42 | MDB 自增键                     |
| `queue_id` | 7         | entries\_push\_queue 的主键（例） |

---

## 1️⃣ UI 把新行写进 **entries**

```sql
-- 返回 id_pg = 123，external_id 默认为 NULL
```
> UI 不需要也拿不到 external_id——它只管给用户一个 "保存成功"。

### 触发器立即执行

```sql
INSERT INTO entries_push_queue(entry_id, operation)
VALUES (123, 'INSERT')          -- 生成 queue_id=7
;
NOTIFY push_job, '7';
```

---

## 2️⃣ Python Listener 收到事件 `payload='7'`

```python
async def callback(conn, pid, channel, payload):
    await process_queue(int(payload))   # queue_id = 7
```

---

## 3️⃣ `process_queue(7)` 取出待办 & 原始行

```sql
SELECT q.queue_id, q.operation,
       e.*                       -- rows for INSERT
FROM entries_push_queue q
JOIN entries e ON e.id = q.entry_id
WHERE q.queue_id = 7
FOR UPDATE;                      -- 锁这行，防止并发
```

得到整行（id\_pg = 123，external\_id  = NULL，其他字段…）。

---

## 4️⃣ Worker 把这行 **写进 MDB** 并立即拿回 `external_id =42`

```python
acc, db = _open_access()
# 拼 INSERT INTO 元作業時間 (...) VALUES (...)
db.Execute(sql)
new_id = db.OpenRecordset("SELECT @@IDENTITY AS NewID").Fields("NewID").Value
# new_id == 42
```

> `@@IDENTITY` 始终返回"刚刚插进去的那行"的自增键。
> **映射一手抓**：此刻代码仍然握着 `id_pg = 123`，同时拿到了 `external_id  = 42`。

---

## 5️⃣ 同一事务里 **回写 PG** ➜ entries & 队列表

```sql
UPDATE entries
   SET external_id = 42
 WHERE id = 123;

UPDATE entries_push_queue
   SET synced = TRUE
 WHERE queue_id = 7;
COMMIT;
```

> ✅ 现在 `entries(id=123)` 这行已经带 `external_id=42`。
> ✅ 队列表打✅，再也不会被 worker 处理第二次。

---

## 6️⃣ 立刻跑一次「增属id同步函数 f6 」

