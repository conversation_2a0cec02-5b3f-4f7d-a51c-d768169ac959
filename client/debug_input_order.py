#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
调试输入字段的顺序和数据收集过程
"""

def debug_input_order():
    """调试输入字段的顺序"""
    
    # 模拟输入字段定义顺序
    labels = {
        'employee_id': '従業員ID:', 'date': '日付:',
        'model': '機種:', 'number': '号機:', 'factory_number': '工場製番:',
        'project_number': '工事番号:', 'unit_number': 'ﾕﾆｯﾄ番号:',
        'category': '区分 (必須):', 'item': '項目 (必須):',
        'time': '時間 (必須):', 'department': '部門:'
    }
    
    print("输入字段定义顺序:")
    for i, (field, label) in enumerate(labels.items()):
        print(f"  {i+1}. {field}: {label}")
    
    print("\n" + "="*50)
    
    # 模拟用户输入的数据（假设用户按UI顺序输入）
    user_input = {
        'employee_id': '215829',
        'date': '2025/07/15',
        'model': 'test_model',  # 用户输入的第一个字段
        'number': 'test_number',  # 用户输入的第二个字段
        'factory_number': 'test_factory',  # 用户输入的第三个字段
        'project_number': 'test_project',  # 用户输入的第四个字段
        'unit_number': 'test_unit',  # 用户输入的第五个字段
        'category': '1',  # 用户输入的第六个字段
        'item': '2',  # 用户输入的第七个字段
        'time': '1.5',  # 用户输入的第八个字段
        'department': '131'  # 用户输入的第九个字段
    }
    
    print("用户输入数据:")
    for i, (field, value) in enumerate(user_input.items()):
        print(f"  {i+1}. {field}: {value}")
    
    print("\n" + "="*50)
    
    # 模拟数据收集过程
    data = {}
    for field, entry in user_input.items():
        data[field] = entry.strip()
    
    print("收集到的数据:")
    for i, (field, value) in enumerate(data.items()):
        print(f"  {i+1}. {field}: {value}")
    
    print("\n" + "="*50)
    
    # 模拟entry_data构建过程
    entry_data = {
        "entry_date": data.get('date', '').replace('/', '-'),
        "employee_id": data.get('employee_id', ''),
        "duration": float(data.get('time', '0')),
        "model": data.get('model', ''),
        "number": data.get('number', ''),
        "factory_number": data.get('factory_number', ''),
        "project_number": data.get('project_number', ''),
        "unit_number": data.get('unit_number', ''),
        "category": data.get('category', ''),
        "item": data.get('item', ''),
        "department": data.get('department', ''),
        "source": "user"
    }
    
    print("构建的entry_data:")
    for i, (field, value) in enumerate(entry_data.items()):
        print(f"  {i+1}. {field}: {value}")
    
    print("\n" + "="*50)
    
    # 检查是否有字段映射错误
    print("字段映射检查:")
    print(f"  category字段值: {entry_data['category']}")
    print(f"  item字段值: {entry_data['item']}")
    print(f"  model字段值: {entry_data['model']}")
    
    if entry_data['category'] == '5' or entry_data['item'] == '5':
        print("  ⚠️  发现字段值为'5'，可能存在映射错误")
    else:
        print("  ✅ 字段值正常")

if __name__ == "__main__":
    debug_input_order() 