from PyQt6.QtWidgets import QApplication, QTableView
from PyQt6.QtGui import QStandardItemModel, QStandardItem

app = QApplication([])

# 1. 创建模型
model = QStandardItemModel(3, 2) # 3行2列
model.setHorizontalHeaderLabels(["姓名", "年龄"])

# 2. 填充模型数据
data = [
    ("张三", "30"),
    ("李四", "25"),
    ("王五", "40")
]
for row, (name, age) in enumerate(data):
    item_name = QStandardItem(name)
    item_age = QStandardItem(age)
    if name == "王五": # 同样支持富文本
        item_name.setData("<b style='color:red;'>王五</b>", 0) # 使用setData设置显示文本
    model.setItem(row, 0, item_name)
    model.setItem(row, 1, item_age)

# 3. 创建视图
view = QTableView()
# 4. 将模型设置给视图
view.setModel(model)

# 自动调整列宽
view.resizeColumnsToContents()

view.show()
app.exec()