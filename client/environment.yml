name: mysuite_client_multiplatform
channels:
  - conda-forge
  - defaults
dependencies:
  - python=3.12

  # 修改10 -多平台: PyQt6 GUI框架（跨平台）
  - pyqt=6

  # 修改10 -多平台: 网络请求库（移除aiohttp，使用更稳定的库）
  - requests  # 稳定的HTTP客户端
  - websockets  # WebSocket客户端
  # 注意：移除了aiohttp，使用自定义的简化实现

  # 修改10 -多平台: 文件监控和处理（跨平台）
  - pandas  # 数据处理
  - openpyxl  # Excel文件处理
  - watchdog  # 文件监控（跨平台）

  # 修改10 -多平台: 串口通信（跨平台）
  - pyserial  # 串口通信库

  # 修改10 -多平台: 系统通知和托盘（跨平台）
  - pip
  - pip:
    - plyer  # 跨平台通知库
    - Pyro4>=4.80 # 用于远程对象通信
    - Pillow>=8.0.0 # 图片处理库
    - lxml>=4.9.0 # 用于解析XML
    - matplotlib>=3.8.0 # 用于绘图
  # 修改10 -多平台: JWT认证
  - python-jose[cryptography]  # JWT令牌处理

  # 修改10 -多平台: 配置管理
  - python-dotenv  # 环境变量管理

  # 修改10 -多平台: SSL证书处理
  - certifi  # SSL证书

  # 修改10 -多平台: 跨平台路径处理（Python 3.4+内置pathlib）
  # pathlib已经是Python标准库的一部分

  # 修改10 -多平台: 额外的跨平台依赖
  - urllib3  # HTTP库底层支持
  - charset-normalizer  # 字符编码检测 
  - flask           # HTTP 服务框架，用于 server.py
  - pyjwt           # JWT 编码/解码，用于 service 端的 import jwt