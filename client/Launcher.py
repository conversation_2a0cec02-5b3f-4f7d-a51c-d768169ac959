# client/client_multiplatform.py
# MySuite 客户端 - 跨平台版本
# 修改10 -多平台: 支持Windows和Linux平台

import sys
import json
import threading
import asyncio
import requests
import os
import platform  # 修改10 -多平台: 添加平台检测
from pathlib import Path  # 修改10 -多平台: 使用跨平台路径处理
from datetime import datetime, timedelta
from PyQt6 import QtWidgets, QtCore, QtGui
from websockets.client import connect
import urllib3
import uuid  # 250618.18：39加入注册 - 用于生成硬件指纹
import subprocess  # 250618.18：39加入注册 - 用于获取硬件信息
import hashlib  # 250618.18：39加入注册 - 用于哈希计算
import base64  # 250618.19：15 密码记住功能 - 用于密码编码
from cryptography.fernet import Fernet  # 250618.19：15 密码记住功能 - 用于密码加密
import mimetypes

# 忽略SSL警告（如开发环境自签名证书）
urllib3.disable_warnings(urllib3.exceptions.InsecureRequestWarning)

# 修改10 -多平台: 不直接导入http_client，而是使用内置实现
# from http_client import AsyncHTTPClient, SyncHTTPClient

# 250618.18：39加入注册 - 硬件指纹收集类
class HardwareFingerprint:
    """硬件指纹收集器"""
    
    def __init__(self):
        self.system = platform.system().lower()
    
    def get_mac_address(self) -> str:
        """获取MAC地址"""
        try:
            mac = ':'.join(['{:02x}'.format((uuid.getnode() >> elements) & 0xff) 
                           for elements in range(0,2*6,2)][::-1])
            return mac
        except:
            return "unknown"
    
    def get_motherboard_uuid(self) -> str:
        """获取主板UUID"""
        try:
            if self.system == "windows":
                result = subprocess.run(['wmic', 'csproduct', 'get', 'UUID'], 
                                      capture_output=True, text=True, timeout=10)
                lines = result.stdout.strip().split('\n')
                for line in lines:
                    line = line.strip()
                    if line and line != 'UUID':
                        return line
            else:  # Linux
                try:
                    with open('/sys/class/dmi/id/product_uuid', 'r') as f:
                        return f.read().strip()
                except:
                    # 备用方法
                    result = subprocess.run(['dmidecode', '-s', 'system-uuid'], 
                                          capture_output=True, text=True, timeout=10)
                    if result.returncode == 0:
                        return result.stdout.strip()
        except:
            pass
        return str(uuid.uuid4())  # 生成随机UUID作为备用
    
    def get_machine_id(self) -> str:
        """获取机器ID"""
        try:
            if self.system == "windows":
                result = subprocess.run(['wmic', 'computersystem', 'get', 'Name'], 
                                      capture_output=True, text=True, timeout=10)
                lines = result.stdout.strip().split('\n')
                for line in lines:
                    line = line.strip()
                    if line and line != 'Name':
                        return line
            else:  # Linux
                try:
                    with open('/etc/machine-id', 'r') as f:
                        return f.read().strip()
                except:
                    # 备用方法
                    try:
                        with open('/var/lib/dbus/machine-id', 'r') as f:
                            return f.read().strip()
                    except:
                        return platform.node()
        except:
            pass
        return platform.node()  # 使用主机名作为备用
    
    def get_cpu_info(self) -> str:
        """获取CPU信息"""
        try:
            if self.system == "windows":
                result = subprocess.run(['wmic', 'cpu', 'get', 'ProcessorId'], 
                                      capture_output=True, text=True, timeout=10)
                lines = result.stdout.strip().split('\n')
                for line in lines:
                    line = line.strip()
                    if line and line != 'ProcessorId':
                        return line
            
            else:  # Linux
                try:
                    with open('/proc/cpuinfo', 'r') as f:
                        for line in f:
                            if 'processor' in line.lower() and ':' in line:
                                return line.split(':')[1].strip()
                except:
                    pass
        except:
            pass
        return platform.processor()
    
    def get_disk_serial(self) -> str:
        """获取磁盘序列号"""
        try:
            if self.system == "windows":
                result = subprocess.run(['wmic', 'diskdrive', 'get', 'SerialNumber'], 
                                      capture_output=True, text=True, timeout=10)
                lines = result.stdout.strip().split('\n')
                for line in lines:
                    line = line.strip()
                    if line and line != 'SerialNumber':
                        return line
            else:  # Linux
                try:
                    result = subprocess.run(['lsblk', '-d', '-o', 'SERIAL'], 
                                          capture_output=True, text=True, timeout=10)
                    lines = result.stdout.strip().split('\n')
                    for line in lines[1:]:  # 跳过标题行
                        line = line.strip()
                        if line:
                            return line
                except:
                    pass
        except:
            pass
        return "unknown"
    
    def collect_all(self) -> dict:
        """收集所有硬件信息 - 250618.19：05 极简版硬件指纹"""
        # 250618.19：05 使用极简版本，只依赖MAC地址作为主要标识符
        mac_addr = self.get_mac_address()
        
        # 使用MAC地址作为所有字段的值，确保一致性
        hardware_info = {
            "mac_address": mac_addr,
            "motherboard_uuid": mac_addr,  # 使用MAC地址确保一致性
            "machine_id": mac_addr,        # 使用MAC地址确保一致性
            "cpu_info": "",
            "disk_serial": ""
        }
        
        # 250618.19：05 添加调试信息
        print(f"[DEBUG] 极简硬件指纹 (基于MAC地址):")
        print(f"  MAC地址: {mac_addr}")
        print(f"  所有字段都使用MAC地址以确保一致性")
        
        return hardware_info

# 修改10 -多平台: 平台相关配置
class PlatformConfig:
    """跨平台配置管理"""
    
    def __init__(self):
        self.system = platform.system().lower()
        
    def get_default_watch_folder(self):
        """获取默认监控文件夹路径"""
        if self.system == "windows":
            return Path("C:/MySuite/InputExcels")
        else:  # Linux和其他系统
            return Path.home() / "MySuite" / "InputExcels"
    
    def get_default_serial_port(self):
        """获取默认串口"""
        if self.system == "windows":
            return "COM3"
        else:  # Linux
            return "/dev/ttyUSB0"
    
    def get_serial_ports(self):
        """获取可用串口列表"""
        import serial.tools.list_ports
        ports = serial.tools.list_ports.comports()
        return [port.device for port in ports]
    
    def create_directories(self, path: Path):
        """创建目录（如果不存在）"""
        path.mkdir(parents=True, exist_ok=True)

# 修改10 -多平台: 简化的HTTP客户端实现，移除aiohttp依赖
class SimpleAsyncHTTPClient(QtCore.QObject):
    """简化的异步HTTP客户端，不依赖aiohttp"""
    
    request_finished = QtCore.pyqtSignal(str, dict, object)
    
    def __init__(self, base_url: str = "https://localhost"):
    #def __init__(self, base_url: str = SERVER_HTTP_BASE):
        super().__init__()
        self.base_url = base_url
        self.loop: asyncio.AbstractEventLoop = None
        
    def set_event_loop(self, loop: asyncio.AbstractEventLoop):
        """设置事件循环"""
        self.loop = loop
    
    def _make_sync_request(self, method: str, endpoint: str, request_id: str, **kwargs):
        """使用requests进行同步请求，然后发送信号"""
        try:
            import requests
            session = requests.Session()
            session.trust_env = False
            session.verify = False
            
            url = f"{self.base_url}{endpoint}"
            
            if method.upper() == 'GET':
                response = session.get(url, timeout=10, **kwargs)
            elif method.upper() == 'POST':
                response = session.post(url, timeout=10, **kwargs)
            else:
                raise ValueError(f"Unsupported method: {method}")
            
            try:
                data = response.json()
            except:
                data = response.text
            
            result = {
                'status_code': response.status_code,
                'data': data,
                'headers': dict(response.headers),
                'ok': response.status_code < 400
            }
            
            # 在主线程中发射信号
            QtCore.QMetaObject.invokeMethod(
                self, "_emit_result", 
                QtCore.Qt.ConnectionType.QueuedConnection,
                QtCore.Q_ARG(str, request_id),
                QtCore.Q_ARG(dict, result),
                QtCore.Q_ARG(object, None)
            )
            
        except Exception as e:
            QtCore.QMetaObject.invokeMethod(
                self, "_emit_result",
                QtCore.Qt.ConnectionType.QueuedConnection,
                QtCore.Q_ARG(str, request_id),
                QtCore.Q_ARG(dict, {}),
                QtCore.Q_ARG(object, e)
            )
    
    @QtCore.pyqtSlot(str, dict, object)
    def _emit_result(self, request_id: str, result: dict, error: object):
        """在主线程中发射结果信号"""
        self.request_finished.emit(request_id, result, error)
    
    def post_async(self, endpoint: str, request_id: str = None, **kwargs) -> str:
        """异步POST请求"""
        import time
        if request_id is None:
            request_id = f"post_{int(time.time() * 1000)}"
        
        # 在后台线程中执行请求
        thread = threading.Thread(
            target=self._make_sync_request,
            args=('POST', endpoint, request_id),
            kwargs=kwargs,
            daemon=True
        )
        thread.start()
        
        return request_id
    
    def get_async(self, endpoint: str, request_id: str = None, **kwargs) -> str:
        """异步GET请求"""
        import time
        if request_id is None:
            request_id = f"get_{int(time.time() * 1000)}"
        
        # 在后台线程中执行请求
        thread = threading.Thread(
            target=self._make_sync_request,
            args=('GET', endpoint, request_id),
            kwargs=kwargs,
            daemon=True
        )
        thread.start()
        
        return request_id
    
    async def close(self):
        """关闭客户端（空实现）"""
        pass

class SimpleSyncHTTPClient:
    """简化的同步HTTP客户端"""
    
    def __init__(self, base_url: str = "https://localhost"):
        self.base_url = base_url
        self.session = self._create_session()
    
    def _create_session(self):
        """创建配置好的requests session"""
        import requests
        session = requests.Session()
        session.trust_env = False
        session.verify = False
        return session
    
    def post(self, endpoint: str, timeout: int = 3, headers: dict = None, **kwargs) -> dict:
        """同步POST请求"""
        try:
            url = f"{self.base_url}{endpoint}"
            response = self.session.post(url, timeout=timeout, headers=headers, **kwargs)
            
            return {
                'status_code': response.status_code,
                'data': response.json() if response.headers.get('content-type', '').startswith('application/json') else response.text,
                'ok': response.ok,
                'headers': dict(response.headers)
            }
        except Exception as e:
            return {
                'status_code': 0,
                'data': None,
                'ok': False,
                'error': str(e)
            }
    
    def get(self, endpoint: str, timeout: int = 3, headers: dict = None, **kwargs) -> dict:
        """同步GET请求"""
        try:
            url = f"{self.base_url}{endpoint}"
            response = self.session.get(url, timeout=timeout, headers=headers, **kwargs)
            
            return {
                'status_code': response.status_code,
                'data': response.json() if response.headers.get('content-type', '').startswith('application/json') else response.text,
                'ok': response.ok,
                'headers': dict(response.headers)
            }
        except Exception as e:
            return {
                'status_code': 0,
                'data': None,
                'ok': False,
                'error': str(e)
            }
    
    def close(self):
        """关闭session"""
        if self.session:
            self.session.close()

# Dummy classes and functions to make the main script runnable
def load_local_flags():
    """修改10 -多平台: 使用跨平台路径加载配置"""
    config_path = Path.home() / ".mysuite" / "flags.json"
    try:
        if config_path.exists():
            with open(config_path, 'r', encoding='utf-8') as f:
                return json.load(f)
    except Exception as e:
        print(f"Loading local flags failed: {e}")
    return {}

def save_local_flags(flags):
    """修改10 -多平台: 使用跨平台路径保存配置"""
    config_path = Path.home() / ".mysuite" / "flags.json"
    try:
        config_path.parent.mkdir(parents=True, exist_ok=True)
        with open(config_path, 'w', encoding='utf-8') as f:
            json.dump(flags, f, indent=2, ensure_ascii=False)
        print(f"Saved local flags: {flags}")
    except Exception as e:
        print(f"Saving local flags failed: {e}")

# 250618.19：15 密码记住功能 - 密码管理类
class PasswordManager:
    """密码安全存储管理器"""
    
    def __init__(self):
        self.config_dir = Path.home() / ".mysuite"
        self.config_dir.mkdir(parents=True, exist_ok=True)
        self.key_file = self.config_dir / ".key"
        self.password_file = self.config_dir / "saved_passwords.encrypted"
        self._ensure_key()
    
    def _ensure_key(self):
        """确保加密密钥存在"""
        if not self.key_file.exists():
            # 生成新的加密密钥
            key = Fernet.generate_key()
            with open(self.key_file, 'wb') as f:
                f.write(key)
            # 设置文件权限（仅用户可读）
            try:
                self.key_file.chmod(0o600)
            except:
                pass  # Windows可能不支持chmod
    
    def _get_cipher(self):
        """获取加密器"""
        with open(self.key_file, 'rb') as f:
            key = f.read()
        return Fernet(key)
    
    def save_password(self, employee_id: str, password: str):
        """保存加密的密码"""
        try:
            cipher = self._get_cipher()
            
            # 加载现有密码数据
            passwords = {}
            if self.password_file.exists():
                try:
                    with open(self.password_file, 'rb') as f:
                        encrypted_data = f.read()
                    if encrypted_data:
                        decrypted_data = cipher.decrypt(encrypted_data)
                        passwords = json.loads(decrypted_data.decode())
                except:
                    passwords = {}  # 如果解密失败，重新开始
            
            # 保存新密码
            passwords[employee_id] = password
            
            # 加密并保存
            data_to_encrypt = json.dumps(passwords).encode()
            encrypted_data = cipher.encrypt(data_to_encrypt)
            
            with open(self.password_file, 'wb') as f:
                f.write(encrypted_data)
            
            # 设置文件权限
            try:
                self.password_file.chmod(0o600)
            except:
                pass
            
            print(f"Password saved for employee: {employee_id}")
            return True
        except Exception as e:
            print(f"Failed to save password: {e}")
            return False
    
    def load_password(self, employee_id: str) -> str:
        """加载解密的密码"""
        try:
            if not self.password_file.exists():
                return ""
            
            cipher = self._get_cipher()
            
            with open(self.password_file, 'rb') as f:
                encrypted_data = f.read()
            
            if not encrypted_data:
                return ""
            
            decrypted_data = cipher.decrypt(encrypted_data)
            passwords = json.loads(decrypted_data.decode())
            
            return passwords.get(employee_id, "")
        except Exception as e:
            print(f"Failed to load password: {e}")
            return ""
    
    def remove_password(self, employee_id: str):
        """删除保存的密码"""
        try:
            if not self.password_file.exists():
                return True
            
            cipher = self._get_cipher()
            
            with open(self.password_file, 'rb') as f:
                encrypted_data = f.read()
            
            if not encrypted_data:
                return True
            
            decrypted_data = cipher.decrypt(encrypted_data)
            passwords = json.loads(decrypted_data.decode())
            
            if employee_id in passwords:
                del passwords[employee_id]
                
                # 重新加密并保存
                data_to_encrypt = json.dumps(passwords).encode()
                encrypted_data = cipher.encrypt(data_to_encrypt)
                
                with open(self.password_file, 'wb') as f:
                    f.write(encrypted_data)
                
                print(f"Password removed for employee: {employee_id}")
            
            return True
        except Exception as e:
            print(f"Failed to remove password: {e}")
            return False

class _BaseModule:
    def __init__(self):
        self._is_running = False
    def start(self):
        self._is_running = True
        print(f"Started {self.__class__.__name__}")
    def stop(self):
        self._is_running = False
        print(f"Stopped {self.__class__.__name__}")
    def is_running(self):
        return self._is_running

class FloatingReminderModule(_BaseModule): 
    pass

class ExcelMonitorModule(_BaseModule):
    def __init__(self, watch_folder): 
        super().__init__()
        self.watch_folder = Path(watch_folder)  # 修改10 -多平台: 使用Path对象

class SerialListenerModule(_BaseModule):
    def __init__(self, port, baudrate): 
        super().__init__()
        self.port = port
        self.baudrate = baudrate

# 修改为Nginx监听的地址和端口
# 如果Nginx在同一台机器上，监听443端口，且域名是localhost，则如下
SERVER_HTTP_BASE = "https://localhost" # 默认HTTPS端口443可以省略
SERVER_WS_URL = "wss://localhost/ws/feature_flags?token=" # 默认WSS端口443可以省略
CLIENT_VERSION = "1.0.0"

class FeatureManager:
    def __init__(self):
        # 修改10 -多平台: 使用平台配置
        self.platform_config = PlatformConfig()
        
        self.modules = {
            "floating_reminder": FloatingReminderModule(),
            "excel_monitor": ExcelMonitorModule(
                watch_folder=self.platform_config.get_default_watch_folder()
            ),
            "serial_listener": SerialListenerModule(
                port=self.platform_config.get_default_serial_port(), 
                baudrate=9600
            ),
        }
        self.current_flags = {name: False for name in self.modules.keys()}

    def apply_flags(self, flags: dict):
        for name, enabled in flags.items():
            module = self.modules.get(name)
            if not module:
                continue
            
            is_running = module.is_running()
            if enabled and not is_running:
                module.start()
            elif not enabled and is_running:
                module.stop()
        
        # Also update our internal state record
        self.current_flags.update(flags)

    def stop_all(self):
        for module in self.modules.values():
            if module.is_running():
                module.stop()

class MainWindow(QtWidgets.QMainWindow):
    """主窗口 - MySuite 客户端主界面"""
    
    def __init__(self, client_app):
        super().__init__()
        self.client_app = client_app
        
        # 修改10 -多平台: 使用简化的HTTP客户端
        #self.async_http_client = SimpleAsyncHTTPClient()
       # self.sync_http_client = SimpleSyncHTTPClient()
        self.async_http_client = SimpleAsyncHTTPClient(base_url=SERVER_HTTP_BASE)
        self.sync_http_client  = SimpleSyncHTTPClient(base_url=SERVER_HTTP_BASE)
        
        # 250618.18：39加入注册 - 初始化硬件指纹收集器
        self.hardware_fingerprint = HardwareFingerprint()
        
        # 250618.19：15 密码记住功能 - 初始化密码管理器
        self.password_manager = PasswordManager()
        
        # 连接异步HTTP响应信号
        self.async_http_client.request_finished.connect(self.on_async_request_finished)
        
        self.setup_ui()
        # 监听窗口状态变化
        self.installEventFilter(self)        
        

    def eventFilter(self, obj, event):
        # 修改: 使用 QEvent.Type.WindowStateChange
        if event.type() == QtCore.QEvent.Type.WindowStateChange:
            if self.windowState() & QtCore.Qt.WindowState.Minimized:
                QtCore.QTimer.singleShot(0, self.hide)
                self.client_app.tray_icon.showMessage(
                    "MySuite Client",
                    "已最小化到托盘，双击图标可恢复",
                    QtWidgets.QSystemTrayIcon.MessageIcon.Information,
                    2000
                )
        return super().eventFilter(obj, event)

    def closeEvent(self, event):
        # 修改: 拦截关闭事件，隐藏到托盘而非退出
        event.ignore()
        self.hide()
        self.client_app.tray_icon.showMessage(
            "MySuite Client",
            "已隐藏到托盘，双击图标可恢复",
            QtWidgets.QSystemTrayIcon.MessageIcon.Information,
            2000
        )   
    def setup_ui(self):
        self.setWindowTitle("MySuite クライアント - クロスプラットフォーム版")  # 修改10 -多平台: 更新标题
        self.setGeometry(100, 100, 600, 400)
        
        # 主体布局
        central_widget = QtWidgets.QWidget()
        self.setCentralWidget(central_widget)
        layout = QtWidgets.QVBoxLayout(central_widget)
        
        # 修改10 -多平台: 添加平台信息显示
        platform_info = QtWidgets.QLabel(f"現在のプラットフォーム：{platform.system()} {platform.release()}")
        platform_info.setStyleSheet("color: blue; font-weight: bold;")
        layout.addWidget(platform_info)
        
        # 状态分组
        status_group = QtWidgets.QGroupBox("サーバー状態")
        status_layout = QtWidgets.QVBoxLayout(status_group)
        
        self.server_status_label = QtWidgets.QLabel("サーバー：確認中...")
        self.db_status_label = QtWidgets.QLabel("データベース：確認中...")
        self.websocket_status_label = QtWidgets.QLabel("WebSocket：未接続")
        
        status_layout.addWidget(self.server_status_label)
        status_layout.addWidget(self.db_status_label)
        status_layout.addWidget(self.websocket_status_label)
        layout.addWidget(status_group)
        
        # 功能开关分组
        flags_group = QtWidgets.QGroupBox("機能スイッチ")
        flags_layout = QtWidgets.QVBoxLayout(flags_group)
        
        self.flags_table = QtWidgets.QTableWidget(0, 3)
        self.flags_table.setHorizontalHeaderLabels(["機能名", "状態", "操作"])
        flags_layout.addWidget(self.flags_table)
        layout.addWidget(flags_group)
        
        # 修改14 增加id 密码和跳转界面 - 添加登录分组
        login_group = QtWidgets.QGroupBox("ユーザーログイン")
        login_layout = QtWidgets.QVBoxLayout(login_group)
        
        # 登录控件行1：ID输入
        login_row1 = QtWidgets.QHBoxLayout()
        login_row1.addWidget(QtWidgets.QLabel("従業員ID："))
        self.id_input = QtWidgets.QLineEdit()  # 修改14 增加id 密码和跳转界面 - 文本框3
        self.id_input.setPlaceholderText("従業員IDを入力してください")
        self.id_input.setFixedWidth(150)
        # 250618.19：15 密码记住功能 - 监听员工ID变化以自动加载密码
        self.id_input.textChanged.connect(self.on_employee_id_changed)
        login_row1.addWidget(self.id_input)
        login_row1.addStretch()
        login_layout.addLayout(login_row1)
        
        # 登录控件行2：密码输入
        login_row2 = QtWidgets.QHBoxLayout()
        login_row2.addWidget(QtWidgets.QLabel("パスワード："))
        self.password_input = QtWidgets.QLineEdit()  # 修改14 增加id 密码和跳转界面 - 文本框4
        self.password_input.setPlaceholderText("パスワードを入力してください")
        self.password_input.setEchoMode(QtWidgets.QLineEdit.EchoMode.Password)  # 密码模式
        self.password_input.setFixedWidth(150)
        login_row2.addWidget(self.password_input)
        login_row2.addStretch()
        login_layout.addLayout(login_row2)
        
        # 250618.19：15 密码记住功能 - 添加记住密码复选框行
        remember_row = QtWidgets.QHBoxLayout()
        self.remember_password_checkbox = QtWidgets.QCheckBox("パスワードを記憶")
        self.remember_password_checkbox.setToolTip("選択するとパスワードが安全に保存され、次回起動時に自動入力されます")
        remember_row.addWidget(self.remember_password_checkbox)
        remember_row.addStretch()
        login_layout.addLayout(remember_row)
        
        # 登录控件行3：按钮
        login_buttons = QtWidgets.QHBoxLayout()
        self.login_confirm_btn = QtWidgets.QPushButton("ログイン確認")  # 修改14 增加id 密码和跳转界面 - 按键3
        self.login_confirm_btn.clicked.connect(self.on_login_confirm)
        self.register_btn = QtWidgets.QPushButton("登録")  # 250618.18：39加入注册 - 注册按钮
        self.register_btn.clicked.connect(self.on_register_clicked)
        self.register_btn.setStyleSheet("background-color: #28a745; color: white; font-weight: bold;")
        self.login_cancel_btn = QtWidgets.QPushButton("キャンセル")  # 修改14 增加id 密码和跳转界面 - 按键4
        self.login_cancel_btn.clicked.connect(self.on_login_cancel)
        
        login_buttons.addWidget(self.login_confirm_btn)
        login_buttons.addWidget(self.register_btn)  # 250618.18：39加入注册 - 添加注册按钮
        
        # 250618.18：53 硬件指纹删除功能 - 添加删除按钮
        self.delete_registration_btn = QtWidgets.QPushButton("登録削除")
        self.delete_registration_btn.clicked.connect(self.on_delete_registration_clicked)
        self.delete_registration_btn.setStyleSheet("background-color: #dc3545; color: white; font-weight: bold;")
        login_buttons.addWidget(self.delete_registration_btn)
        
        login_buttons.addWidget(self.login_cancel_btn)
        login_buttons.addStretch()
        login_layout.addLayout(login_buttons)
        
        layout.addWidget(login_group)
        
        # 2025/06/26.11:50+分离ui操作 - 添加程序启动控制面板
        program_group = QtWidgets.QGroupBox("プログラム起動制御")
        program_layout = QtWidgets.QVBoxLayout(program_group)
        
        # 说明标签
        program_info_label = QtWidgets.QLabel("ログイン成功後、以下の独立プログラムを起動できます：")
        program_info_label.setStyleSheet("color: #666; font-style: italic;")
        program_layout.addWidget(program_info_label)
        
        # 2025/07/03 + 15:40 + 程序启动按钮（初始隐藏，根据权限显示）
        program_buttons = QtWidgets.QHBoxLayout()
        self.program_launch_btn = QtWidgets.QPushButton("従業員操作画面")
        self.program_launch_btn.setEnabled(False)  # 初始状态禁用
        self.program_launch_btn.setVisible(False)  # 2025/07/03 + 15:40 + 初始隐藏
        self.program_launch_btn.clicked.connect(self.on_launch_program1)
        self.program_launch_btn.setStyleSheet("background-color: #007bff; color: white; font-weight: bold; padding: 8px;")
        program_buttons.addWidget(self.program_launch_btn)
        
        # 添加PLC编程工具按钮
        self.program2_launch_btn = QtWidgets.QPushButton("PLC編集ツール")
        self.program2_launch_btn.setEnabled(False)  # 初始状态禁用
        self.program2_launch_btn.setVisible(False)  # 2025/07/03 + 15:40 + 初始隐藏
        self.program2_launch_btn.clicked.connect(self.on_launch_program2)
        self.program2_launch_btn.setStyleSheet("background-color: #28a745; color: white; font-weight: bold; padding: 8px;")
        program_buttons.addWidget(self.program2_launch_btn)
        
        program_buttons.addStretch()
        program_layout.addLayout(program_buttons)
        
        # 程序状态显示
        self.program_status_label = QtWidgets.QLabel("プログラム機能を有効にするには、まずログインしてください")
        self.program_status_label.setStyleSheet("color: #888; font-size: 12px;")
        program_layout.addWidget(self.program_status_label)
        
        layout.addWidget(program_group)
        
        # 计数器分组
        counter_group = QtWidgets.QGroupBox("カウンターテスト")
        counter_layout = QtWidgets.QVBoxLayout(counter_group)  # 改为垂直布局以容纳两行
        
        # 第一行: btn1和文本框1
        row1_layout = QtWidgets.QHBoxLayout()
        self.btn1 = QtWidgets.QPushButton("btn1 (+1)")
        self.btn1.clicked.connect(self.on_btn1_clicked)
        row1_layout.addWidget(self.btn1)
        
        row1_layout.addWidget(QtWidgets.QLabel("カウント値1："))
        self.text_box1 = QtWidgets.QLineEdit()
        self.text_box1.setReadOnly(True)
        self.text_box1.setText("0")  # 初始值
        self.text_box1.setFixedWidth(80)
        row1_layout.addWidget(self.text_box1)
        
        counter_layout.addLayout(row1_layout)
        
        # 第二行: btn2和文本框2
        row2_layout = QtWidgets.QHBoxLayout()
        self.btn2 = QtWidgets.QPushButton("btn2 (+2)")
        self.btn2.clicked.connect(self.on_btn2_clicked)  # 新增btn2点击处理
        row2_layout.addWidget(self.btn2)
        
        row2_layout.addWidget(QtWidgets.QLabel("カウント値2："))
        self.text_box2 = QtWidgets.QLineEdit()  # 新增文本框2
        self.text_box2.setReadOnly(True)
        self.text_box2.setText("0")  # 初始值
        self.text_box2.setFixedWidth(80)
        row2_layout.addWidget(self.text_box2)
        
        counter_layout.addLayout(row2_layout)
        
        # 第三行: 控制按钮
        controls_layout = QtWidgets.QHBoxLayout()
        self.refresh_counter_btn = QtWidgets.QPushButton("更新")
        self.refresh_counter_btn.clicked.connect(self.load_current_counter)
        controls_layout.addWidget(self.refresh_counter_btn)
        
        self.reset_btn = QtWidgets.QPushButton("リセット")
        self.reset_btn.clicked.connect(self.on_reset_counter)
        controls_layout.addWidget(self.reset_btn)
        
        controls_layout.addStretch()  # 添加弹性空间
        counter_layout.addLayout(controls_layout)
        
        layout.addWidget(counter_group)
        
        # 按钮区
        button_layout = QtWidgets.QHBoxLayout()
        
        self.refresh_btn = QtWidgets.QPushButton("状態更新")
        self.refresh_btn.clicked.connect(self.refresh_status)
        
        self.xml_btn = QtWidgets.QPushButton("XMLデータインポート")
        self.xml_btn.clicked.connect(self.open_xml_dialog)
        
        # 更改12 odbc按键处理，@client_fixed.py @/server - 添加ODBC连接控制按键
        self.odbc_connect_btn = QtWidgets.QPushButton("ODBC接続")
        self.odbc_connect_btn.clicked.connect(self.connect_odbc)
        
        self.odbc_btn = QtWidgets.QPushButton("ODBCデータクエリ")
        self.odbc_btn.clicked.connect(self.query_odbc_data)
        self.odbc_btn.setEnabled(False)  # 更改12 odbc按键处理，@client_fixed.py @/server - 初始状态禁用
        
        self.minimize_btn = QtWidgets.QPushButton("トレイに最小化")
        self.minimize_btn.clicked.connect(self.hide)
        
        button_layout.addWidget(self.refresh_btn)
        button_layout.addWidget(self.xml_btn)
        button_layout.addWidget(self.odbc_connect_btn)  # 更改12 odbc按键处理，@client_fixed.py @/server
        button_layout.addWidget(self.odbc_btn)
        button_layout.addWidget(self.minimize_btn)
        layout.addLayout(button_layout)
        
        # 日志区
        log_group = QtWidgets.QGroupBox("运行日志")
        log_layout = QtWidgets.QVBoxLayout(log_group)
        
        self.log_text = QtWidgets.QTextEdit()
        self.log_text.setMaximumHeight(150)
        self.log_text.setReadOnly(True)
        log_layout.addWidget(self.log_text)
        layout.addWidget(log_group)
        
        # 启动时自动刷新
        QtCore.QTimer.singleShot(1000, self.refresh_status)
        # 更改12 odbc按键处理，@client_fixed.py @/server - 启动时检查ODBC状态
        QtCore.QTimer.singleShot(2000, self.check_odbc_status)
        # 250618.19：15 密码记住功能 - 启动时尝试加载最近使用的员工ID和密码
        QtCore.QTimer.singleShot(500, self.load_last_used_credentials)
        
        # 2025/07/03 + 14:00 + 初始化程序启动相关属性（移除客户端权限存储）
        self.current_employee_id = None
        self.current_employee_name = None
        self.current_login_token = None
        self.running_programs = {}  # 跟踪正在运行的程序
    
    def log_message(self, message):
        """写入运行日志"""
        self.log_text.append(f"[{QtCore.QDateTime.currentDateTime().toString()}] {message}")
        self.log_text.verticalScrollBar().setValue(
            self.log_text.verticalScrollBar().maximum()
        )
    
    @QtCore.pyqtSlot(str, dict, object)
    def on_async_request_finished(self, request_id: str, result: dict, error: object):
        """处理异步HTTP请求完成的响应"""
        if error:
            self.log_message(f"请求失败 [{request_id}]: {error}")
            return
        
        # 根据request_id分发到不同的处理函数
        if request_id.startswith('btn1_'):
            self._handle_btn1_response(result)
        elif request_id.startswith('btn2_'):
            self._handle_btn2_response(result)  # 新增btn2响应处理
        elif request_id.startswith('reset_'):
            self._handle_reset_response(result)
        elif request_id.startswith('refresh_'):
            self._handle_refresh_response(result)

        else:
            self.log_message(f"未知请求类型: {request_id}")
    
    def _handle_btn1_response(self, result: dict):
        """处理btn1异步响应"""
        if result.get('ok'):
            data = result.get('data', {})
            n_value = data.get('n', 0)
            message = data.get('message', '')
            self.text_box1.setText(str(n_value))
            self.log_message(f"btn1成功: {message}")
        else:
            self.log_message(f"btn1失败: {result.get('status_code')} {result.get('data')}")
    
    def _handle_btn2_response(self, result: dict):
        """处理btn2异步响应"""
        if result.get('ok'):
            data = result.get('data', {})
            n_value = data.get('n', 0)
            message = data.get('message', '')
            self.text_box2.setText(str(n_value))  # 显示在文本框2中
            self.log_message(f"btn2成功: {message}")
        else:
            self.log_message(f"btn2失败: {result.get('status_code')} {result.get('data')}")
    
    def _handle_reset_response(self, result: dict):
        """处理重置异步响应"""
        if result.get('ok'):
            data = result.get('data', {})
            n_value = data.get('n', 0)
            # 重置时同时更新两个文本框
            self.text_box1.setText(str(n_value))
            self.text_box2.setText(str(n_value))
            self.log_message("计数器重置成功")
        else:
            self.log_message(f"重置失败: {result.get('status_code')} {result.get('data')}")
    
    def _handle_refresh_response(self, result: dict):
        """处理刷新异步响应"""
        if result.get('ok'):
            data = result.get('data', {})
            n_value = data.get('n', 0)
            # 刷新时同时更新两个文本框（因为它们显示的是同一个计数器）
            self.text_box1.setText(str(n_value))
            self.text_box2.setText(str(n_value))
            self.log_message(f"当前计数器值: {n_value}")
        else:
            self.log_message(f"刷新失败: {result.get('status_code')} {result.get('data')}")



    def refresh_status(self):
        """刷新服务器和数据库状态 - 使用连接池"""
        self.log_message("正在检查服务器状态...")
        try:
            # 使用同步客户端进行快速状态检查
            resp = self.sync_http_client.get("/", timeout=3)
            if resp.get('ok'):
                self.server_status_label.setText("服务器: 已连接")
                self.log_message("服务器已连接")
                
                # 检查数据库状态
                db_resp = self.sync_http_client.get("/api/db/status", timeout=3)
                if db_resp.get('ok'):
                    db_data = db_resp.get('data', {})
                    if db_data.get('connected'):
                        self.db_status_label.setText("数据库: 已连接")
                        self.log_message("数据库已连接")
                    else:
                        self.db_status_label.setText("数据库: 未连接")
                        self.log_message("数据库未连接")
                else:
                    self.db_status_label.setText("数据库: 状态异常")
            else:
                self.server_status_label.setText(f"服务器: 错误 {resp.get('status_code', 'Unknown')}")
                self.log_message(f"服务器连接异常: {resp.get('status_code', 'Unknown')}")
        except Exception as e:
            self.server_status_label.setText("服务器: 未连接")
            self.log_message(f"服务器连接失败: {e}")
    
    def update_websocket_status(self, connected):
        """更新WebSocket连接状态"""
        if connected:
            self.websocket_status_label.setText("WebSocket: 已连接")
            self.log_message("WebSocket 已连接")
        else:
            self.websocket_status_label.setText("WebSocket: 未连接")
            self.log_message("WebSocket 未连接")
    
    def update_flags_display(self, flags):
        """更新功能开关显示"""
        self.flags_table.setRowCount(len(flags))
        for i, (name, enabled) in enumerate(flags.items()):
            self.flags_table.setItem(i, 0, QtWidgets.QTableWidgetItem(name))
            self.flags_table.setItem(i, 1, QtWidgets.QTableWidgetItem("已开启" if enabled else "已关闭"))
            
            toggle_btn = QtWidgets.QPushButton("切换")
            toggle_btn.clicked.connect(lambda checked, n=name: self.toggle_flag(n))
            self.flags_table.setCellWidget(i, 2, toggle_btn)
    
    def toggle_flag(self, flag_name):
        """切换功能开关"""
        self.log_message(f"切换功能开关: {flag_name}")
        # TODO: 实现切换功能
    
    def open_xml_dialog(self):
        """打开XML数据导入对话框"""
        dlg = InputDialog()
        dlg.exec()
    
    def connect_odbc(self):
        """更改12 odbc按键处理，@client_fixed.py @/server - 手动连接ODBC"""
        self.log_message("尝试连接ODBC...")
        try:
            # 先检查ODBC状态
            status_resp = self.sync_http_client.get("/api/odbc/status", timeout=5)
            if not status_resp.get('ok'):
                self.log_message("无法获取ODBC状态")
                return
            
            status_data = status_resp.get('data', {}).get('data', {})
            if not status_data.get('platform_supported', False):
                msg = f"当前平台不支持ODBC功能\n平台: {status_data.get('platform', 'Unknown')}\npywin32可用: {status_data.get('pywin32_available', False)}"
                self.log_message(msg)
                QtWidgets.QMessageBox.warning(self, "ODBC连接", msg)
                return
            
            if status_data.get('is_connected', False):
                self.log_message("ODBC已经连接")
                self.update_odbc_ui_state(True)
                return
            
            # 尝试连接
            token = self.client_app.get_jwt_token()
            headers = {"Authorization": f"Bearer {token}"}
            
            resp = self.sync_http_client.post("/api/odbc/connect", timeout=10, headers=headers)
            if resp.get('ok'):
                data = resp.get('data', {})
                if data.get('status') == 'success':
                    self.log_message("ODBC连接成功")
                    self.update_odbc_ui_state(True)
                    QtWidgets.QMessageBox.information(self, "ODBC连接", "ODBC连接成功！")
                else:
                    error_msg = data.get('message', '连接失败')
                    self.log_message(f"ODBC连接失败: {error_msg}")
                    QtWidgets.QMessageBox.warning(self, "ODBC连接", f"连接失败: {error_msg}")
            else:
                error_msg = f"HTTP错误: {resp.get('status_code')} {resp.get('data')}"
                self.log_message(f"ODBC连接失败: {error_msg}")
                QtWidgets.QMessageBox.warning(self, "ODBC连接", f"连接失败: {error_msg}")
                
        except Exception as e:
            error_msg = f"连接异常: {e}"
            self.log_message(error_msg)
            QtWidgets.QMessageBox.critical(self, "ODBC连接", error_msg)
    
    def update_odbc_ui_state(self, connected: bool):
        """更改12 odbc按键处理，@client_fixed.py @/server - 更新ODBC相关UI状态"""
        if connected:
            self.odbc_connect_btn.setText("断开 ODBC")
            self.odbc_connect_btn.clicked.disconnect()
            self.odbc_connect_btn.clicked.connect(self.disconnect_odbc)
            self.odbc_btn.setEnabled(True)
        else:
            self.odbc_connect_btn.setText("连接 ODBC")
            self.odbc_connect_btn.clicked.disconnect()
            self.odbc_connect_btn.clicked.connect(self.connect_odbc)
            self.odbc_btn.setEnabled(False)
    
    def disconnect_odbc(self):
        """更改12 odbc按键处理，@client_fixed.py @/server - 断开ODBC连接"""
        self.log_message("断开ODBC连接...")
        try:
            token = self.client_app.get_jwt_token()
            headers = {"Authorization": f"Bearer {token}"}
            
            resp = self.sync_http_client.post("/api/odbc/disconnect", timeout=5, headers=headers)
            if resp.get('ok'):
                data = resp.get('data', {})
                if data.get('status') == 'success':
                    self.log_message("ODBC连接已断开")
                    self.update_odbc_ui_state(False)
                    QtWidgets.QMessageBox.information(self, "ODBC连接", "ODBC连接已断开")
                else:
                    self.log_message(f"断开ODBC失败: {data.get('message', '未知错误')}")
            else:
                self.log_message(f"断开ODBC失败: HTTP {resp.get('status_code')}")
        except Exception as e:
            self.log_message(f"断开ODBC异常: {e}")

    def query_odbc_data(self):
        """查询ODBC数据 - 使用连接池
        更改12 odbc按键处理，@client_fixed.py @/server - 移除自动连接逻辑"""
        self.log_message("查询ODBC数据...")
        try:
            # 准备认证头
            token = self.client_app.get_jwt_token()
            headers = {"Authorization": f"Bearer {token}"}
            
            # 使用同步HTTP客户端
            resp = self.sync_http_client.get("/api/odbc/read", timeout=5, headers=headers)
            if resp.get('ok'):
                data = resp.get('data')
                text = "\n".join(str(row) for row in data) if isinstance(data, list) else str(data)
                self.log_message("ODBC查询成功")
            else:
                # 更改12 odbc按键处理，@client_fixed.py @/server - 更好的错误处理
                if resp.get('status_code') == 400:
                    text = "请先点击'连接ODBC'按钮建立连接"
                elif resp.get('status_code') == 501:
                    text = "当前平台不支持ODBC功能"
                else:
                    text = f"错误: {resp.get('status_code')} {resp.get('data')}"
                self.log_message(f"ODBC查询失败: {resp.get('status_code')}")
        except Exception as e:
            text = f"异常: {e}"
            self.log_message(f"ODBC异常: {e}")
        
        dlg = QtWidgets.QMessageBox()
        dlg.setWindowTitle("ODBC 数据查询")
        dlg.setText(text)
        dlg.exec()
    
    def on_btn1_clicked(self):
        """btn1点击处理 - 异步调用服务器btn1func"""
        self.log_message("点击 btn1，向服务器发送异步请求...")
        request_id = f"btn1_{int(QtCore.QDateTime.currentSecsSinceEpoch())}"
        self.async_http_client.post_async("/api/counter/btn1", request_id)
    
    def on_btn2_clicked(self):
        """btn2点击处理 - 异步调用服务器btn2func"""
        self.log_message("点击 btn2，向服务器发送异步请求...")
        request_id = f"btn2_{int(QtCore.QDateTime.currentSecsSinceEpoch())}"
        self.async_http_client.post_async("/api/counter/btn2", request_id)
    
    def on_reset_counter(self):
        """重置计数器 - 异步"""
        self.log_message("重置计数器...")
        request_id = f"reset_{int(QtCore.QDateTime.currentSecsSinceEpoch())}"
        self.async_http_client.post_async("/api/counter/reset", request_id)
    
    def load_current_counter(self):
        """手动刷新当前计数器值 - 异步"""
        self.log_message("刷新计数器值...")
        request_id = f"refresh_{int(QtCore.QDateTime.currentSecsSinceEpoch())}"
        self.async_http_client.get_async("/api/counter/current", request_id)
    
    def check_odbc_status(self):
        """更改12 odbc按键处理，@client_fixed.py @/server - 检查ODBC状态并更新UI"""
        try:
            resp = self.sync_http_client.get("/api/odbc/status", timeout=3)
            if resp.get('ok'):
                status_data = resp.get('data', {}).get('data', {})
                is_connected = status_data.get('is_connected', False)
                platform_supported = status_data.get('platform_supported', False)
                
                if not platform_supported:
                    self.odbc_connect_btn.setEnabled(False)
                    self.odbc_connect_btn.setText("ODBC不支持")
                    self.log_message(f"当前平台不支持ODBC: {status_data.get('platform', 'Unknown')}")
                else:
                    self.odbc_connect_btn.setEnabled(True)
                    self.update_odbc_ui_state(is_connected)
                    if is_connected:
                        self.log_message("检测到ODBC已连接")
                    else:
                        self.log_message("ODBC未连接，点击'连接ODBC'按钮可建立连接")
            else:
                self.log_message("无法获取ODBC状态")
        except Exception as e:
            self.log_message(f"检查ODBC状态异常: {e}")
    
    def load_last_used_credentials(self):
        """250618.19：15 密码记住功能 - 加载最近使用的凭据"""
        try:
            # 尝试从配置文件加载最近使用的员工ID
            config_path = Path.home() / ".mysuite" / "last_user.json"
            if config_path.exists():
                with open(config_path, 'r', encoding='utf-8') as f:
                    config = json.load(f)
                    last_employee_id = config.get('last_employee_id', '')
                    if last_employee_id:
                        self.id_input.setText(last_employee_id)
                        self.log_message(f"已自动填入最近使用的员工ID: {last_employee_id}")
                        # 触发ID变化事件以加载密码
                        self.on_employee_id_changed()
        except Exception as e:
            self.log_message(f"加载最近使用凭据失败: {e}")
    
    def save_last_used_employee_id(self, employee_id: str):
        """250618.19：15 密码记住功能 - 保存最近使用的员工ID"""
        try:
            config_path = Path.home() / ".mysuite" / "last_user.json"
            config_path.parent.mkdir(parents=True, exist_ok=True)
            
            config = {'last_employee_id': employee_id}
            with open(config_path, 'w', encoding='utf-8') as f:
                json.dump(config, f, indent=2, ensure_ascii=False)
        except Exception as e:
            self.log_message(f"保存最近使用员工ID失败: {e}")
    
    def on_login_confirm(self):
        """修改14 增加id 密码和跳转界面 - 确认登录按钮处理"""
        employee_id = self.id_input.text().strip()
        password = self.password_input.text()
        
        if not employee_id:
            QtWidgets.QMessageBox.warning(self, "登录", "请输入员工ID")
            return
        
        if not password:
            QtWidgets.QMessageBox.warning(self, "登录", "请输入密码")
            return
        
        self.log_message(f"尝试登录，员工ID: {employee_id}")
        
        try:
            # 250618.18：39加入注册 - 先进行硬件指纹验证
            self.log_message("正在进行硬件指纹验证...")
            hardware_info = self.hardware_fingerprint.collect_all()
            
            hardware_verify_data = {
                "employee_id": employee_id,
                "password": password,
                **hardware_info
            }
            
            # 发送硬件指纹验证请求
            hardware_resp = self.sync_http_client.post(
                "/auth/api/hardware/verify",
                timeout=10,
                json=hardware_verify_data,
                headers={"Content-Type": "application/json"}
            )
            
            # 检查硬件指纹验证结果
            if not hardware_resp.get('ok'):
                if hardware_resp.get('status_code') == 404:
                    error_msg = "该员工ID未注册硬件指纹，请先点击'注册'按钮进行注册"
                elif hardware_resp.get('status_code') == 401:
                    error_msg = "硬件指纹验证失败，请确认您使用的是注册时的设备"
                else:
                    error_msg = f"硬件指纹验证失败: {hardware_resp.get('data', '未知错误')}"
                
                self.log_message(f"硬件指纹验证失败: {error_msg}")
                QtWidgets.QMessageBox.warning(self, "验证失败", error_msg)
                self.password_input.clear()
                return
            
            self.log_message("硬件指纹验证成功，继续原有登录流程...")
            
            # 发送登录请求到认证服务
            login_data = {
                "employee_id": employee_id,
                "password": password
            }
            
            resp = self.sync_http_client.post(
                "/auth/login", 
                timeout=5, 
                json=login_data,
                headers={"Content-Type": "application/json"}
            )
            
            if resp.get('ok'):
                data = resp.get('data', {})
                if data.get('success'):
                    # 登录成功
                    employee_name = data.get('employee_name', '')
                    self.log_message(f"登录成功，欢迎 {employee_name}")
                    
                    # 250618.19：15 密码记住功能 - 处理密码保存
                    if self.remember_password_checkbox.isChecked():
                        if self.password_manager.save_password(employee_id, password):
                            self.log_message(f"已保存员工ID '{employee_id}' 的密码")
                        else:
                            self.log_message("密码保存失败")
                    
                    # 250618.19：15 密码记住功能 - 保存最近使用的员工ID
                    self.save_last_used_employee_id(employee_id)
                    
                    # 清空输入框
                    self.id_input.clear()
                    self.password_input.clear()
                    # 250618.19：15 密码记住功能 - 重置复选框
                    self.remember_password_checkbox.setChecked(False)
                    
                    # 打开新的UI界面B并隐藏当前界面
                    self.open_employee_interface(employee_id, employee_name)
                    
                else:
                    # 登录失败
                    error_msg = data.get('message', '登录失败')
                    self.log_message(f"登录失败: {error_msg}")
                    QtWidgets.QMessageBox.warning(self, "登录失败", error_msg)
                    # 清空密码框
                    self.password_input.clear()
            else:
                error_msg = f"服务器错误: {resp.get('status_code')} {resp.get('data')}"
                self.log_message(f"登录请求失败: {error_msg}")
                QtWidgets.QMessageBox.critical(self, "登录错误", f"请求失败: {error_msg}")
                
        except Exception as e:
            error_msg = f"登录异常: {e}"
            self.log_message(error_msg)
            QtWidgets.QMessageBox.critical(self, "登录异常", error_msg)
    
    def on_login_cancel(self):
        """修改14 增加id 密码和跳转界面 - 取消登录按钮处理"""
        # 清空输入框
        self.id_input.clear()
        self.password_input.clear()
        # 250618.19：15 密码记住功能 - 重置记住密码复选框
        self.remember_password_checkbox.setChecked(False)
        
        # 最小化到托盘（与最小化按钮功能相同）
        self.log_message("取消登录，最小化到托盘")
        self.hide()
    
    def on_employee_id_changed(self):
        """250618.19：15 密码记住功能 - 员工ID变化时自动加载保存的密码"""
        employee_id = self.id_input.text().strip()
        if employee_id:
            # 尝试加载保存的密码
            saved_password = self.password_manager.load_password(employee_id)
            if saved_password:
                self.password_input.setText(saved_password)
                self.remember_password_checkbox.setChecked(True)
                self.log_message(f"已自动填入员工ID '{employee_id}' 的保存密码")
            else:
                # 清空密码框和复选框
                self.password_input.clear()
                self.remember_password_checkbox.setChecked(False)
        else:
            # 清空密码框和复选框
            self.password_input.clear()
            self.remember_password_checkbox.setChecked(False)
    
    def on_register_clicked(self):
        """250618.18：39加入注册 - 注册按钮处理"""
        employee_id = self.id_input.text().strip()
        password = self.password_input.text()
        
        if not employee_id:
            QtWidgets.QMessageBox.warning(self, "注册", "请输入员工ID")
            return
        
        if not password:
            QtWidgets.QMessageBox.warning(self, "注册", "请输入密码")
            return
        
        # 显示注册确认对话框
        reply = QtWidgets.QMessageBox.question(
            self, "硬件指纹注册", 
            f"确定要为员工ID '{employee_id}' 注册硬件指纹吗？\n\n"
            "注册后，此设备将与该员工ID绑定。\n"
            "如果更换设备，需要重新注册。",
            QtWidgets.QMessageBox.StandardButton.Yes | QtWidgets.QMessageBox.StandardButton.No,
            QtWidgets.QMessageBox.StandardButton.No
        )
        
        if reply != QtWidgets.QMessageBox.StandardButton.Yes:
            return
        
        self.log_message(f"开始为员工ID '{employee_id}' 注册硬件指纹...")
        
        # 显示进度对话框
        progress_dialog = QtWidgets.QProgressDialog("正在收集硬件信息...", "取消", 0, 0, self)
        progress_dialog.setWindowModality(QtCore.Qt.WindowModality.WindowModal)
        progress_dialog.setAutoClose(False)
        progress_dialog.setAutoReset(False)
        progress_dialog.show()
        
        # 在后台线程中收集硬件信息
        def collect_and_register():
            try:
                # 收集硬件信息
                QtCore.QMetaObject.invokeMethod(
                    progress_dialog, "setLabelText",
                    QtCore.Qt.ConnectionType.QueuedConnection,
                    QtCore.Q_ARG(str, "正在收集硬件信息...")
                )
                
                hardware_info = self.hardware_fingerprint.collect_all()
                
                QtCore.QMetaObject.invokeMethod(
                    progress_dialog, "setLabelText",
                    QtCore.Qt.ConnectionType.QueuedConnection,
                    QtCore.Q_ARG(str, "正在发送注册请求...")
                )
                
                # 发送注册请求
                registration_data = {
                    "employee_id": employee_id,
                    "password": password,
                    **hardware_info
                }
                
                resp = self.sync_http_client.post(
                    "/auth/api/hardware/register",
                    timeout=10,
                    json=registration_data,
                    headers={"Content-Type": "application/json"}
                )
                
                # 在主线程中处理结果
                QtCore.QMetaObject.invokeMethod(
                    self, "_handle_register_response",
                    QtCore.Qt.ConnectionType.QueuedConnection,
                    QtCore.Q_ARG(dict, resp),
                    QtCore.Q_ARG(object, progress_dialog)
                )
                
            except Exception as e:
                QtCore.QMetaObject.invokeMethod(
                    self, "_handle_register_error",
                    QtCore.Qt.ConnectionType.QueuedConnection,
                    QtCore.Q_ARG(str, str(e)),
                    QtCore.Q_ARG(object, progress_dialog)
                )
        
        # 启动后台线程
        thread = threading.Thread(target=collect_and_register, daemon=True)
        thread.start()
    
    @QtCore.pyqtSlot(dict, object)
    def _handle_register_response(self, resp: dict, progress_dialog):
        """250618.18：39加入注册 - 处理注册响应"""
        progress_dialog.close()
        
        if resp.get('ok'):
            data = resp.get('data', {})
            if data.get('status') == 'success':
                self.log_message(f"硬件指纹注册成功: {data.get('message', '')}")
                QtWidgets.QMessageBox.information(
                    self, "注册成功", 
                    f"硬件指纹注册成功！\n\n"
                    f"员工ID: {data.get('employee_id', '')}\n"
                    f"注册时间: {data.get('registration_time', '')}\n\n"
                    "现在可以使用此设备进行登录。"
                )
                
                # 250618.19：15 密码记住功能 - 注册成功后也可以保存密码
                employee_id = self.id_input.text().strip()
                password = self.password_input.text()
                if self.remember_password_checkbox.isChecked() and employee_id and password:
                    if self.password_manager.save_password(employee_id, password):
                        self.log_message(f"已保存员工ID '{employee_id}' 的密码")
                    else:
                        self.log_message("密码保存失败")
                
                # 清空密码框，保留ID
                self.password_input.clear()
                # 250618.19：15 密码记住功能 - 重置复选框
                self.remember_password_checkbox.setChecked(False)
            else:
                error_msg = data.get('message', '注册失败')
                self.log_message(f"注册失败: {error_msg}")
                QtWidgets.QMessageBox.warning(self, "注册失败", error_msg)
        else:
            error_msg = f"服务器错误: {resp.get('status_code')} {resp.get('data')}"
            self.log_message(f"注册请求失败: {error_msg}")
            QtWidgets.QMessageBox.critical(self, "注册错误", f"请求失败: {error_msg}")
    
    @QtCore.pyqtSlot(str, object)
    def _handle_register_error(self, error_msg: str, progress_dialog):
        """250618.18：39加入注册 - 处理注册错误"""
        progress_dialog.close()
        self.log_message(f"注册异常: {error_msg}")
        QtWidgets.QMessageBox.critical(self, "注册异常", f"注册过程中发生错误:\n{error_msg}")
    
    def on_delete_registration_clicked(self):
        """250618.18：53 硬件指纹删除功能 - 删除注册按钮处理"""
        employee_id = self.id_input.text().strip()
        
        if not employee_id:
            QtWidgets.QMessageBox.warning(self, "删除注册", "请输入员工ID")
            return
        
        # 显示确认对话框
        reply = QtWidgets.QMessageBox.question(
            self, "确认删除", 
            f"确定要删除员工ID '{employee_id}' 的硬件指纹注册吗？\n\n"
            "此操作需要管理员密码确认。\n"
            "删除后该员工需要重新注册才能登录。",
            QtWidgets.QMessageBox.StandardButton.Yes | QtWidgets.QMessageBox.StandardButton.No,
            QtWidgets.QMessageBox.StandardButton.No
        )
        
        if reply != QtWidgets.QMessageBox.StandardButton.Yes:
            return
        
        # 弹出密码输入对话框
        password_dialog = AdminPasswordDialog(self)
        if password_dialog.exec() == QtWidgets.QDialog.DialogCode.Accepted:
            admin_password = password_dialog.get_password()
            self._perform_delete_registration(employee_id, admin_password)
    
    def _perform_delete_registration(self, employee_id: str, admin_password: str):
        """250618.18：53 硬件指纹删除功能 - 执行删除操作"""
        self.log_message(f"正在删除员工ID '{employee_id}' 的硬件指纹注册...")
        
        try:
            delete_data = {
                "employee_id": employee_id,
                "admin_password": admin_password
            }
            
            resp = self.sync_http_client.post(
                "/auth/api/hardware/delete",
                timeout=10,
                json=delete_data,
                headers={"Content-Type": "application/json"}
            )
            
            if resp.get('ok'):
                data = resp.get('data', {})
                if data.get('status') == 'success':
                    self.log_message(f"硬件指纹删除成功: {data.get('message', '')}")
                    QtWidgets.QMessageBox.information(
                        self, "删除成功", 
                        f"员工ID '{employee_id}' 的硬件指纹注册已成功删除！\n\n"
                        f"删除时间: {data.get('deletion_time', '')}\n\n"
                        "该员工现在可以重新注册硬件指纹。"
                    )
                    
                    # 250618.19：15 密码记住功能 - 删除注册时同时删除保存的密码
                    if self.password_manager.remove_password(employee_id):
                        self.log_message(f"已删除员工ID '{employee_id}' 的保存密码")
                    
                    # 清空输入框
                    self.id_input.clear()
                    self.password_input.clear()
                    # 250618.19：15 密码记住功能 - 重置复选框
                    self.remember_password_checkbox.setChecked(False)
                else:
                    error_msg = data.get('message', '删除失败')
                    self.log_message(f"删除失败: {error_msg}")
                    QtWidgets.QMessageBox.warning(self, "删除失败", error_msg)
            else:
                if resp.get('status_code') == 403:
                    error_msg = "管理员密码错误"
                elif resp.get('status_code') == 404:
                    error_msg = "该员工ID未注册硬件指纹"
                else:
                    error_msg = f"服务器错误: {resp.get('status_code')} {resp.get('data')}"
                
                self.log_message(f"删除请求失败: {error_msg}")
                QtWidgets.QMessageBox.critical(self, "删除错误", f"删除失败: {error_msg}")
                
        except Exception as e:
            error_msg = f"删除异常: {e}"
            self.log_message(error_msg)
            QtWidgets.QMessageBox.critical(self, "删除异常", error_msg)
    
    def on_launch_program1(self):
        """2025/07/03 + 14:00 + 启动员工操作界面程序（增强版服务器权限验证）"""
        if not self.current_login_token:
            QtWidgets.QMessageBox.warning(self, "启动失败", "请先登录后再启动程序")
            return
        
        # 2025/07/03 + 14:00 + 服务器端权限验证
        if not self._request_program_authorization("program1"):
            return  # 权限验证失败，已在函数内显示错误信息
        
        # 验证token是否仍然有效
        if not self.verify_current_token():
            QtWidgets.QMessageBox.warning(self, "登录过期", "登录状态已过期，请重新登录")
            self.reset_login_state()
            return
        
        try:
            self.log_message("启动员工操作界面程序...")
            
            # 获取program1.py的路径
            current_dir = Path(__file__).parent
            program1_path = current_dir / "program1.py"
            
            if not program1_path.exists():
                QtWidgets.QMessageBox.critical(self, "启动失败", f"未找到程序文件: {program1_path}")
                return
            
            # 构造启动参数 - 确保传递有效的JWT token
            args = [
                sys.executable,  # Python解释器
                str(program1_path),
                self.current_login_token,  # 这是从Server3获取的有效JWT token
                self.current_employee_id,
                self.current_employee_name
            ]
            
            # 启动独立程序
            process = subprocess.Popen(args, 
                                     cwd=str(current_dir),
                                     creationflags=subprocess.CREATE_NEW_CONSOLE if platform.system() == "Windows" else 0)
            
            # 记录正在运行的程序
            self.running_programs["program1"] = process
            
            self.log_message(f"员工操作界面程序已启动 (PID: {process.pid})")
            self.program_status_label.setText(f"员工操作界面已启动 (PID: {process.pid})")
            
            # 更新按钮状态
            self.program_launch_btn.setText("重新启动员工操作界面")
            
        except Exception as e:
            error_msg = f"启动程序失败: {e}"
            self.log_message(error_msg)
            QtWidgets.QMessageBox.critical(self, "启动失败", error_msg)
    
    def on_launch_program2(self):
        """2025/07/03 + 14:00 + 启动PLC编程工具程序（增强版服务器权限验证）"""
        if not self.current_login_token:
            QtWidgets.QMessageBox.warning(self, "启动失败", "请先登录后再启动程序")
            return
        
        # 2025/07/03 + 14:00 + 服务器端权限验证
        if not self._request_program_authorization("program2"):
            return  # 权限验证失败，已在函数内显示错误信息
        
        # 验证token是否仍然有效
        if not self.verify_current_token():
            QtWidgets.QMessageBox.warning(self, "登录过期", "登录状态已过期，请重新登录")
            self.reset_login_state()
            return
        
        try:
            self.log_message("启动PLC编程工具程序...")
            
            # 获取program2.py的路径
            current_dir = Path(__file__).parent
            program2_path = current_dir / "program2.py"
            
            if not program2_path.exists():
                QtWidgets.QMessageBox.critical(self, "启动失败", f"未找到程序文件: {program2_path}")
                return
            
            # 构造启动参数
            args = [
                sys.executable,  # Python解释器
                str(program2_path),
                self.current_login_token,
                self.current_employee_id,
                self.current_employee_name
            ]
            
            # 启动独立程序
            process = subprocess.Popen(args, 
                                     cwd=str(current_dir),
                                     creationflags=subprocess.CREATE_NEW_CONSOLE if platform.system() == "Windows" else 0)
            
            # 记录正在运行的程序
            self.running_programs["program2"] = process
            
            self.log_message(f"PLC编程工具程序已启动 (PID: {process.pid})")
            self.program_status_label.setText(f"PLC编程工具已启动 (PID: {process.pid})")
            
            # 更新按钮状态
            self.program2_launch_btn.setText("重新启动PLC编程工具")
            
        except Exception as e:
            error_msg = f"启动PLC编程工具失败: {e}"
            self.log_message(error_msg)
            QtWidgets.QMessageBox.critical(self, "启动失败", error_msg)
    
    def verify_current_token(self) -> bool:
        """2025/06/26.11:50+分离ui操作 - 验证当前token是否有效"""
        if not self.current_login_token:
            return False
        
        try:
            # 2025/06/26.11:50+分离ui操作 - 使用正确的认证服务地址进行token验证
            import requests
            headers = {"Authorization": f"Bearer {self.current_login_token}"}
            auth_service_url = "http://localhost:8006"
            
            session = requests.Session()
            session.trust_env = False
            
            resp = session.get(
                f"{auth_service_url}/api/verify", 
                headers=headers, 
                timeout=5, 
                verify=False
            )
            
            if resp.status_code == 200:
                self.log_message("Token验证成功")
                return True
            else:
                self.log_message(f"Token验证失败: {resp.status_code}")
                return False
                
        except Exception as e:
            self.log_message(f"Token验证异常: {e}")
            return False
    
    def reset_login_state(self):
        """2025/07/03 + 14:00 + 重置登录状态（移除客户端权限管理）"""
        self.current_employee_id = None
        self.current_employee_name = None
        self.current_login_token = None
        
        # 2025/07/03 + 15:40 + 隐藏程序启动按钮
        self._hide_all_program_buttons("ログインしてプログラム機能を有効にしてください")
        
        # 清空登录框
        self.id_input.clear()
        self.password_input.clear()
        self.remember_password_checkbox.setChecked(False)
        
        self.log_message("登录状态已重置")
    
    def _get_employee_jwt_token(self, employee_id: str, employee_name: str) -> dict:
        """2025/07/03 + 14:00 + 从认证服务获取员工JWT token（简化版本）"""
        try:
            import requests
            
            # 使用存储的密码进行认证
            password_manager = PasswordManager()
            password = password_manager.load_password(employee_id)
            
            if not password:
                self.log_message("未找到存储的密码，无法自动获取token")
                return None
            
            # 向认证服务请求token
            login_data = {
                "employee_id": employee_id,
                "password": password
            }
            
            session = requests.Session()
            session.trust_env = False
            
            response = session.post(
                "http://localhost:8006/login",
                json=login_data,
                timeout=5,
                verify=False
            )
            
            if response.status_code == 200:
                result = response.json()
                if result.get("success"):
                    token = result.get("access_token")
                    if token:
                        self.log_message("成功获取员工JWT token")
                        return {'token': token}
                    else:
                        self.log_message("认证服务响应中缺少access_token")
                else:
                    self.log_message(f"认证失败: {result.get('message', '未知错误')}")
            else:
                self.log_message(f"认证请求失败: HTTP {response.status_code}")
                
            return None
            
        except Exception as e:
            self.log_message(f"获取JWT token异常: {e}")
            return None
    
    def _get_server_permissions(self, employee_id: str) -> dict:
        """2025/07/03 + 14:00 + 从服务器获取员工权限信息"""
        try:
            import requests
            
            if not self.current_login_token:
                return None
                
            headers = {"Authorization": f"Bearer {self.current_login_token}"}
            
            session = requests.Session()
            session.trust_env = False
            
            response = session.get(
                f"http://localhost:8006/api/program/permissions/{employee_id}",
                headers=headers,
                timeout=5,
                verify=False
            )
            
            if response.status_code == 200:
                return response.json()
            else:
                self.log_message(f"获取权限信息失败: HTTP {response.status_code}")
                return None
                
        except Exception as e:
            self.log_message(f"获取服务器权限异常: {e}")
            return None
    
    def _update_program_buttons_based_on_permissions(self, permission_info: dict):
        """2025/07/03 + 16:10 + 根据服务器权限信息显示/隐藏程序按钮"""
        try:
            accessible_programs = permission_info.get('accessible_programs', [])
            permission = permission_info.get('permission', 'unknown')
            permission_desc = permission_info.get('permission_description', permission)
            
            # 2025/07/03 + 16:10 + 检查program1权限，有权限则显示并启用，无权限则隐藏
            program1_accessible = any(p.get('program_name') == 'program1' for p in accessible_programs)
            if program1_accessible:
                self.program_launch_btn.setVisible(True)
                self.program_launch_btn.setEnabled(True)
                self.program_launch_btn.setText("従業員操作画面")
            else:
                self.program_launch_btn.setVisible(False)
            
            # 2025/07/03 + 16:10 + 检查program2权限，有权限则显示并启用，无权限则隐藏
            program2_accessible = any(p.get('program_name') == 'program2' for p in accessible_programs)
            if program2_accessible:
                self.program2_launch_btn.setVisible(True)
                self.program2_launch_btn.setEnabled(True)
                self.program2_launch_btn.setText("PLC編集ツール")
            else:
                self.program2_launch_btn.setVisible(False)
            
            # 更新状态标签
            total_programs = len(accessible_programs)
            if total_programs > 0:
                program_names = []
                for p in accessible_programs:
                    if p.get('program_name') == 'program1':
                        program_names.append('従業員操作画面')
                    elif p.get('program_name') == 'program2':
                        program_names.append('PLC編集ツール')
                
                self.program_status_label.setText(
                    f"ようこそ！{permission_desc} - 利用可能: {', '.join(program_names)}"
                )
            else:
                self.program_status_label.setText(f"{permission_desc} - プログラム機能なし")
                
        except Exception as e:
            self.log_message(f"2025/07/03 + 16:10 + 更新程序按钮状态失败: {e}")
            self._hide_all_program_buttons(f"2025/07/03 + 16:10 + 状态更新失败: {e}")
    
    def _hide_all_program_buttons(self, reason: str):
        """2025/07/03 + 16:10 + 隐藏所有程序启动按钮"""
        self.program_launch_btn.setVisible(False)
        self.program2_launch_btn.setVisible(False)
        self.program_status_label.setText(f"2025/07/03 + 16:10 + プログラム機能無効: {reason}")
    
    def _disable_all_program_buttons(self, reason: str):
        """2025/07/03 + 16:10 + 兼容性函数，调用隐藏函数"""
        self._hide_all_program_buttons(reason)
    
    def _request_program_authorization(self, program_name: str) -> bool:
        """2025/07/03 + 14:00 + 向服务器请求程序启动授权"""
        try:
            import requests
            
            if not self.current_login_token or not self.current_employee_id:
                return False
                
            headers = {
                "Authorization": f"Bearer {self.current_login_token}",
                "Content-Type": "application/json"
            }
            
            request_data = {
                "program_name": program_name,
                "employee_id": self.current_employee_id
            }
            
            session = requests.Session()
            session.trust_env = False
            
            response = session.post(
                "http://localhost:8006/api/program/authorize",
                headers=headers,
                json=request_data,
                timeout=5,
                verify=False
            )
            
            if response.status_code == 200:
                result = response.json()
                if result.get("permission_granted"):
                    self.log_message(f"程序 {program_name} 授权成功: {result.get('message', '')}")
                    return True
                else:
                    self.log_message(f"程序 {program_name} 权限不足: {result.get('message', '')}")
                    QtWidgets.QMessageBox.warning(
                        self, "权限不足", 
                        f"无法启动 {program_name}:\n{result.get('message', '权限不足')}"
                    )
                    return False
            else:
                self.log_message(f"程序授权请求失败: HTTP {response.status_code}")
                return False
                
        except Exception as e:
            self.log_message(f"程序授权请求异常: {e}")
            return False
    
    def open_employee_interface(self, employee_id: str, employee_name: str):
        """修改14 增加id 密码和跳转界面 - 打开员工操作界面B"""
        self.log_message(f"打开员工界面: {employee_name} ({employee_id})")
        
        # 2025/06/26.11:50+分离ui操作 - 保存登录状态用于启动独立程序
        self.current_employee_id = employee_id
        self.current_employee_name = employee_name
        
        # 2025/07/03 + 14:00 + 从认证服务获取JWT token（移除客户端权限解析）
        try:
            token_result = self._get_employee_jwt_token(employee_id, employee_name)
            if not token_result or not token_result.get('token'):
                QtWidgets.QMessageBox.warning(self, "登录失败", "无法获取有效的登录token")
                return
            
            self.current_login_token = token_result['token']
            self.log_message(f"成功获取JWT token: {self.current_login_token[:50]}...")
            # 2025/07/03 + 14:00 + 不再在客户端解析权限，改为从服务器获取
            
        except Exception as e:
            self.log_message(f"获取JWT token失败: {e}")
            QtWidgets.QMessageBox.warning(self, "登录失败", f"获取登录token失败: {e}")
            return
        
        # 2025/07/03 + 14:00 + 改为从服务器获取权限信息并设置UI状态
        try:
            permission_info = self._get_server_permissions(employee_id)
            if permission_info:
                self._update_program_buttons_based_on_permissions(permission_info)
                self.log_message(f"登录成功！权限: {permission_info.get('permission', 'unknown')} - 已从服务器获取权限配置")
            else:
                # 2025/07/03 + 15:40 + 服务器权限获取失败，隐藏所有程序按钮
                self._hide_all_program_buttons("サーバーから権限情報を取得できません")
                self.log_message("登录成功！但无法获取权限信息，所有程序功能已禁用")
        except Exception as e:
            self._hide_all_program_buttons(f"権限検証失敗: {e}")
            self.log_message(f"权限验证异常: {e}")
        
        # 2025/06/26.11:50+分离ui操作 - 保持Launcher界面可见，不再隐藏
        # self.hide()  # 注释掉隐藏界面的代码



# 2025/06/26.11:50+分离ui操作 - EmployeeInterfaceWindow已移至program1.py

# 250618.18：53 硬件指纹删除功能 - 管理员密码输入对话框
class AdminPasswordDialog(QtWidgets.QDialog):
    def __init__(self, parent=None):
        super().__init__(parent)
        self.setWindowTitle("管理员密码验证")
        self.setModal(True)
        self.resize(300, 150)
        
        layout = QtWidgets.QVBoxLayout(self)
        
        # 说明标签
        info_label = QtWidgets.QLabel("请输入管理员密码以确认删除操作：")
        info_label.setStyleSheet("font-weight: bold; color: #dc3545; margin: 10px;")
        layout.addWidget(info_label)
        
        # 密码输入框
        self.password_input = QtWidgets.QLineEdit()
        self.password_input.setEchoMode(QtWidgets.QLineEdit.EchoMode.Password)
        self.password_input.setPlaceholderText("请输入管理员密码")
        layout.addWidget(self.password_input)
        
        # 按钮布局
        button_layout = QtWidgets.QHBoxLayout()
        
        self.ok_button = QtWidgets.QPushButton("确认")
        self.ok_button.clicked.connect(self.accept)
        self.ok_button.setStyleSheet("background-color: #dc3545; color: white; font-weight: bold;")
        
        self.cancel_button = QtWidgets.QPushButton("取消")
        self.cancel_button.clicked.connect(self.reject)
        
        button_layout.addWidget(self.ok_button)
        button_layout.addWidget(self.cancel_button)
        layout.addLayout(button_layout)
        
        # 设置焦点到密码输入框
        self.password_input.setFocus()
        
        # 回车键确认
        self.password_input.returnPressed.connect(self.accept)
    
    def get_password(self):
        """获取输入的密码"""
        return self.password_input.text()

class InputDialog(QtWidgets.QDialog):
    def __init__(self, parent=None):
        super().__init__(parent)
        self.setWindowTitle("导入 XML 数据")
        self.resize(400, 300)

        layout = QtWidgets.QVBoxLayout(self)

        self.client_id_edit = QtWidgets.QLineEdit(self)
        self.client_id_edit.setPlaceholderText("client_id (如 1, 2-1)")
        layout.addWidget(QtWidgets.QLabel("Client ID:", self))
        layout.addWidget(self.client_id_edit)

        self.key_edit = QtWidgets.QLineEdit(self)
        self.key_edit.setPlaceholderText("字段名 (如 username)")
        layout.addWidget(QtWidgets.QLabel("字段名:", self))
        layout.addWidget(self.key_edit)

        self.value_edit = QtWidgets.QLineEdit(self)
        self.value_edit.setPlaceholderText("字段值 (如 alice)")
        layout.addWidget(QtWidgets.QLabel("字段值:", self))
        layout.addWidget(self.value_edit)

        self.send_btn = QtWidgets.QPushButton("发送 XML", self)
        self.send_btn.clicked.connect(self.on_send)
        layout.addWidget(self.send_btn)

        self.result_text = QtWidgets.QTextEdit(self)
        self.result_text.setReadOnly(True)
        layout.addWidget(self.result_text)

    def on_send(self):
        client_id = self.client_id_edit.text().strip()
        key = self.key_edit.text().strip()
        value = self.value_edit.text().strip()
        if not client_id or not key:
            self.result_text.setPlainText("Client ID 和字段名不能为空")
            return
        data = {key: value}
        try:
            session = requests.Session()
            session.trust_env = False
            
            token = self.get_jwt_token()
            headers = {"Authorization": f"Bearer {token}", "Content-Type": "application/json"}
            payload = {"client_id": client_id, "data": data}
            resp = session.post(
                f"{SERVER_HTTP_BASE}/api/xml/write/",
                headers=headers,
                json=payload,
                timeout=5,
                verify=False
            )
            if resp.status_code == 200:
                self.result_text.setPlainText(f"发送成功: {resp.json()}")
            else:
                self.result_text.setPlainText(f"发送失败: {resp.status_code} {resp.text}")
        except Exception as e:
            self.result_text.setPlainText(f"发送错误: {e}")

    def get_jwt_token(self) -> str:
        SECRET_KEY = "your-very-secret-signing-key"
        ALGORITHM = "HS256"
        import time
        from jose import jwt
        to_encode = {"sub": "client_user", "exp": time.time() + 3600}
        return jwt.encode(to_encode, SECRET_KEY, algorithm=ALGORITHM)

class ClientApp(QtWidgets.QApplication):
    # Signal to apply flags from a non-GUI thread
    apply_flags_signal = QtCore.pyqtSignal(dict)
    websocket_status_signal = QtCore.pyqtSignal(bool)

    def __init__(self, argv):
        super().__init__(argv)
        # 修改: 隐藏最后窗口关闭时退出
        self.setQuitOnLastWindowClosed(False)
        
        # 修改10 -多平台: 跨平台托盘图标处理
        if not QtWidgets.QSystemTrayIcon.isSystemTrayAvailable():
            QtWidgets.QMessageBox.critical(None, "错误", "当前系统不支持托盘图标")
            sys.exit(1)
        
        # 修改10 -多平台: 跨平台图标路径处理
        self.setup_icon_path()

        # 主窗口
        self.main_window = MainWindow(self)
        
        # 托盘
        self.tray_icon = QtWidgets.QSystemTrayIcon(QtGui.QIcon(str(self.icon_path)), self)
        # 修改: 托盘图标双击还原窗口
        self.tray_icon.activated.connect(self.on_tray_activated)
        menu = QtWidgets.QMenu()
        show_action = menu.addAction("显示主窗口")
        show_action.triggered.connect(self.show_main_window)
        menu.addSeparator()
        xml_action = menu.addAction("导入 XML 数据")
        xml_action.triggered.connect(self.open_input_dialog)
        # 更改12 odbc按键处理，@client_fixed.py @/server - 添加ODBC连接控制到托盘菜单
        odbc_connect_action = menu.addAction("连接 ODBC")
        odbc_connect_action.triggered.connect(self.main_window.connect_odbc)
        odbc_action = menu.addAction("查询 ODBC 数据")
        odbc_action.triggered.connect(self.on_click_odbc)
        menu.addSeparator()
        exit_action = menu.addAction("退出程序")
        exit_action.triggered.connect(self.on_exit)
        self.tray_icon.setContextMenu(menu)
        self.tray_icon.show()
        self.tray_icon.showMessage(
            "MySuite Client",
            "客户端已启动，点击托盘图标可显示主窗口",
            QtWidgets.QSystemTrayIcon.MessageIcon.Information,
            3000
        )

        self.feature_manager = FeatureManager()

        # 信号连接
        self.apply_flags_signal.connect(self._apply_flags_slot)
        self.websocket_status_signal.connect(self.main_window.update_websocket_status)

        # 修改10 -多平台: 简化异步处理，移除复杂的事件循环
        self.loop = asyncio.new_event_loop()
        t = threading.Thread(target=self.loop.run_forever, daemon=True)
        t.start()
        
        # 设置HTTP客户端的事件循环
        self.main_window.async_http_client.set_event_loop(self.loop)

        # 加载本地缓存并初始化
        local_flags = load_local_flags()
        self.apply_flags_signal.emit(local_flags)
        asyncio.run_coroutine_threadsafe(self.fetch_and_listen_flags(), self.loop)
        
        # 显示主窗口
        self.show_main_window()

    def setup_icon_path(self):
        """修改10 -多平台: 设置跨平台图标路径"""
        # 获取当前脚本所在目录
        basedir = Path(__file__).parent
        
        # 创建跨平台的资源目录
        resources_dir = basedir / "resources" / "icons"
        resources_dir.mkdir(parents=True, exist_ok=True)
        
        self.icon_path = resources_dir / "app.png"  # 使用PNG格式更跨平台
        
        # 如果图标不存在，创建一个默认图标
        if not self.icon_path.exists():
            self.create_default_icon()

    def create_default_icon(self):
        """修改10 -多平台: 创建默认图标"""
        try:
            pixmap = QtGui.QPixmap(32, 32)
            pixmap.fill(QtGui.QColor(0, 120, 215))  # 蓝色背景
            
            # 添加简单的文字标识
            painter = QtGui.QPainter(pixmap)
            painter.setPen(QtGui.QColor(255, 255, 255))
            painter.setFont(QtGui.QFont("Arial", 12, QtGui.QFont.Weight.Bold))
            painter.drawText(pixmap.rect(), QtCore.Qt.AlignmentFlag.AlignCenter, "MS")
            painter.end()
            
            pixmap.save(str(self.icon_path), "PNG")
            print(f"Created default icon at: {self.icon_path}")
        except Exception as e:
            print(f"Failed to create default icon: {e}")
            # 使用系统默认图标作为fallback
            self.icon_path = None

    def show_main_window(self):
        """显示主窗口"""
        self.main_window.show()
        self.main_window.raise_()
        self.main_window.activateWindow()

    async def fetch_and_listen_flags(self):
        """获取并监听功能开关"""
        try:
            # 使用忽略代理的session
            session = requests.Session()
            session.trust_env = False
            
            resp = session.get(f"{SERVER_HTTP_BASE}/api/feature_flags/", timeout=5, verify=False)
            if resp.ok:
                flags = resp.json()
                self.apply_flags_signal.emit(flags)
                print(f"Successfully fetched initial flags: {flags}")
            else:
                print(f"Failed to fetch flags: {resp.status_code}")
        except Exception as e:
            print(f"Error fetching initial flags: {e}")

        # WebSocket 连接
        token = self.get_jwt_token()
        ws_url = SERVER_WS_URL + token
        while True:
            try:
                import ssl
                ssl_context = ssl.create_default_context()
                ssl_context.check_hostname = False
                ssl_context.verify_mode = ssl.CERT_NONE
                
                async with connect(ws_url, ssl=ssl_context) as ws:
                    print("WebSocket connection established.")
                    self.websocket_status_signal.emit(True)
                    while True:
                        msg = await ws.recv()
                        data = json.loads(msg)
                        self.apply_flags_signal.emit(data)
            except Exception as e:
                print(f"WebSocket connection error: {e}. Retrying in 5s.")
                self.websocket_status_signal.emit(False)
                await asyncio.sleep(5)

    @QtCore.pyqtSlot(dict)
    def _apply_flags_slot(self, flags: dict):
        print(f"Applying flags from Qt slot: {flags}")
        
        # Handle different flag formats
        if 'name' in flags and 'enabled' in flags:  # Format from websocket
            flag_update = {flags['name']: flags['enabled']}
        else:  # Format from initial HTTP GET
            flag_update = flags

        self.feature_manager.apply_flags(flag_update)
        
        # Update main window display
        self.main_window.update_flags_display(flag_update)
        
        # Update local cache
        current_cached_flags = load_local_flags()
        current_cached_flags.update(flag_update)
        save_local_flags(current_cached_flags)

    def open_input_dialog(self):
        dlg = InputDialog()
        dlg.exec()

    def on_click_odbc(self):
        try:
            session = requests.Session()
            session.trust_env = False
            token = self.get_jwt_token()
            headers = {"Authorization": f"Bearer {token}"}
            resp = session.get(f"{SERVER_HTTP_BASE}/api/odbc/read/", headers=headers, timeout=5, verify=False)
            if resp.ok:
                data = resp.json()
                text = "\n".join(str(row) for row in data) if isinstance(data, list) else str(data)
            else:
                text = f"错误: {resp.status_code} {resp.text}"
        except Exception as e:
            text = f"异常: {e}"
        dlg = QtWidgets.QMessageBox()
        dlg.setWindowTitle("ODBC 数据查询")
        dlg.setText(text)
        dlg.exec()

    def on_exit(self):
        print("Exit requested. Stopping all modules and event loop.")
        self.feature_manager.stop_all()
        
        # 清理HTTP客户端
        self.main_window.sync_http_client.close()
        # 异步客户端需要在事件循环中关闭
        asyncio.run_coroutine_threadsafe(
            self.main_window.async_http_client.close(), 
            self.loop
        )
        
        self.loop.call_soon_threadsafe(self.loop.stop)
        QtCore.QCoreApplication.exit()

    def get_jwt_token(self) -> str:
        SECRET_KEY = "your-very-secret-signing-key"
        ALGORITHM = "HS256"
        import time
        from jose import jwt
        to_encode = {"sub": "client_user", "exp": time.time() + 3600}
        return jwt.encode(to_encode, SECRET_KEY, algorithm=ALGORITHM)
    
    def on_tray_activated(self, reason):
        # 修改: 响应托盘图标激活事件
        if reason == QtWidgets.QSystemTrayIcon.ActivationReason.Trigger:
            self.show_main_window()



# 修改10 -多平台: 跨平台启动脚本
if __name__ == "__main__":
    # 修改10 -多平台: 跨平台路径处理
    current_dir = Path(__file__).parent
    modules_dir = current_dir / "modules"
    utils_dir = current_dir / "utils"
    
    # 只在目录存在时才添加到路径
    if modules_dir.exists():
        sys.path.append(str(modules_dir))
    if utils_dir.exists():
        sys.path.append(str(utils_dir))

    # 修改10 -多平台: 添加平台检测和启动信息
    print(f"Starting MySuite Client on {platform.system()} {platform.release()}")
    print(f"Python version: {sys.version}")
    print(f"Working directory: {current_dir}")
    
    app = ClientApp(sys.argv)
    sys.exit(app.exec()) 