#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试更新操作的字段映射修复
"""

def test_update_field_mapping():
    """测试更新操作的字段映射"""
    
    print("更新操作字段映射测试")
    print("="*60)
    
    # 模拟输入表单数据
    form_data = {
        'employee_id': '215829',
        'date': '2025/07/15',
        'model': 'test_model',
        'number': 'test_number',
        'factory_number': 'test_factory',
        'project_number': 'test_project',
        'unit_number': 'test_unit',
        'category': '1',
        'item': '2',
        'time': '1.5',
        'department': '131'
    }
    
    print("1. 输入表单数据:")
    for field, value in form_data.items():
        print(f"   {field}: {value}")
    
    print("\n" + "="*60)
    
    # 模拟修复前的错误映射（旧版本）
    def old_mapping(form_data):
        return {
            "entry_date": form_data.get('date', '').replace('/', '-'),
            "employee_id": form_data.get('employee_id', ''),
            "duration": float(form_data.get('time', '0')),
            "project_code": form_data.get('model', ''),  # ❌ 错误映射
            "status": form_data.get('category', ''),     # ❌ 错误映射
            "description": form_data.get('item', ''),    # ❌ 错误映射
            "department": form_data.get('department', ''),
            "notes": f"号机:{form_data.get('number', '')} 工场製番:{form_data.get('factory_number', '')} 工事番号:{form_data.get('project_number', '')} ユニット番号:{form_data.get('unit_number', '')}",
            "source": "user"
        }
    
    # 模拟修复后的正确映射（新版本）
    def new_mapping(form_data):
        return {
            "entry_date": form_data.get('date', '').replace('/', '-'),
            "employee_id": form_data.get('employee_id', ''),
            "duration": float(form_data.get('time', '0')),
            "model": form_data.get('model', ''),              # ✅ 正确映射
            "number": form_data.get('number', ''),            # ✅ 正确映射
            "factory_number": form_data.get('factory_number', ''),  # ✅ 正确映射
            "project_number": form_data.get('project_number', ''),  # ✅ 正确映射
            "unit_number": form_data.get('unit_number', ''),  # ✅ 正确映射
            "category": form_data.get('category', ''),        # ✅ 正确映射
            "item": form_data.get('item', ''),                # ✅ 正确映射
            "department": form_data.get('department', ''),
            "source": "user"
        }
    
    print("2. 修复前的错误映射（旧版本）:")
    old_data = old_mapping(form_data)
    for field, value in old_data.items():
        print(f"   {field}: {value}")
    
    print("\n" + "="*60)
    
    print("3. 修复后的正确映射（新版本）:")
    new_data = new_mapping(form_data)
    for field, value in new_data.items():
        print(f"   {field}: {value}")
    
    print("\n" + "="*60)
    
    print("4. 字段映射对比:")
    print("   修复前的问题:")
    print("   - model -> project_code (错误)")
    print("   - category -> status (错误)")
    print("   - item -> description (错误)")
    print("   - number, factory_number, project_number, unit_number -> notes (错误)")
    
    print("\n   修复后的正确映射:")
    print("   - model -> model (正确)")
    print("   - number -> number (正确)")
    print("   - factory_number -> factory_number (正确)")
    print("   - project_number -> project_number (正确)")
    print("   - unit_number -> unit_number (正确)")
    print("   - category -> category (正确)")
    print("   - item -> item (正确)")
    
    print("\n" + "="*60)
    
    print("5. 关键字段值对比:")
    print(f"   修复前 - category字段值: {old_data.get('status', 'N/A')}")
    print(f"   修复后 - category字段值: {new_data.get('category', 'N/A')}")
    print(f"   修复前 - item字段值: {old_data.get('description', 'N/A')}")
    print(f"   修复后 - item字段值: {new_data.get('item', 'N/A')}")
    print(f"   修复前 - model字段值: {old_data.get('project_code', 'N/A')}")
    print(f"   修复后 - model字段值: {new_data.get('model', 'N/A')}")
    
    print("\n" + "="*60)
    
    print("6. 测试结果:")
    if new_data.get('category') == '1' and new_data.get('item') == '2':
        print("   ✅ 修复成功：category和item字段映射正确")
    else:
        print("   ❌ 修复失败：字段映射仍有问题")
    
    if new_data.get('model') == 'test_model':
        print("   ✅ 修复成功：model字段映射正确")
    else:
        print("   ❌ 修复失败：model字段映射有问题")

if __name__ == "__main__":
    test_update_field_mapping() 