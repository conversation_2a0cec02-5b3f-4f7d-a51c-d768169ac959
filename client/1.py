"""
bubble_radar_ui_fanuc.py
-------------------------------------------------
▶ 役割：UI_FANUC_Dev （サードパーティFanuc API + WPFフロントエンド、自己学習型）
▶ 評価軸：55（32 システムソフトウェア + 23 ファクトリーオートメーション）

実行方法：
    pip install matplotlib numpy
    python bubble_radar_ui_fanuc.py
"""

import numpy as np
import matplotlib.pyplot as plt
import matplotlib.font_manager

# ---------- フォント設定 ----------
# 使用可能な日本語フォントを確認し、優先順位を設定
# 一般的なUbuntuでの日本語フォント候補： "Noto Sans CJK JP", "TakaoPGothic", "IPAPGothic"
# Windowsの場合："Yu Gothic", "MS Gothic"など
# macOSの場合："Hiragino Sans"など
plt.rcParams["font.sans-serif"] = ["Noto Sans CJK JP", "TakaoPGothic", "IPAPGothic", "IPAexGothic", "Yu Gothic", "MS Gothic", "Hiragino Sans", "sans-serif"]
plt.rcParams["axes.unicode_minus"] = False # マイナス記号の文字化け対策

# ---------- 評価項目 ----------
sys32_jp = [
    "アルゴリズム/データ構造","コーディング規約","バージョン管理","デバッグ/トラブルシューティング",
    "単体テスト","結合テスト","自動テストフレームワーク",
    "要求分析","UML/システムモデリング","モジュール分割",
    "マイクロサービスアーキテクチャ","API設計","メッセージキュー",
    "データベース設計","SQL最適化","NoSQL","キャッシュ技術",
    "DevOps/CI-CD","コンテナ/K8s","クラウドプラットフォーム",
    "認証/認可","セキュアコーディング","ペネトレーションテスト",
    "パフォーマンスチューニング","ログ/可観測性","高並行性設計","分散一貫性",
    "エラー回復/ロールバック","IaC/構成管理",
    "ドキュメント作成","コミュニケーション/コラボレーション","プロジェクト管理"
]

factory23_jp = [
    "数学/制御理論","機械設計/プロセス","組込みハードウェア","リアルタイム制御",
    "ロボットインテグレーション","PLCラダー図","PLC配線図解析","産業用プロトコル通信",
    "IIoT/データ収集","サーバーサイド開発","工場認証/セキュリティ","AI/画像処理アルゴリズム",
    "UI/フロントエンド開発","codeプログラミング","工作機械API連携",
    "CNC加工補正","機械安全","産業用ロボット安全",
    "リアルタイムイーサネット","エッジコンピューティング","現場スケジューリング","生産ラインシミュレーション","OEE向上"
]

# 重複を避けてラベルを結合
labels = sys32_jp + [x for x in factory23_jp if x not in sys32_jp]
N = len(labels)
angles = np.linspace(0, 2*np.pi, N, endpoint=False).tolist() + [0] # 閉じるために最初に戻る

# ---------- 役割評価 ----------
# リストを指定された長さにパディングする関数
def pad(lst, length, fill=0):
    return lst + [fill]*(length-len(lst))

# 役割説明に基づく評価：UI/フロントエンド開発 = 4、Fanuc API = 3、高級言語 = 3。その他システム関連スキルは低め。
prof_ui = pad(
    # 32 システム項目
    [2,2,2,2,1,1,0,1,0,1,0,1,0,1,1,1,1,1,0,0,
     0,0,0,0,0,1,0,0,0,0,1,1] +
    # 23 ファクトリーオートメーション項目
    # 注意：factory23_jp の対応するインデックスにスコアを配置
    # "UI / フロントエンド開発" は factory23_jp の12番目 (0-indexed)
    # "高級言語プログラミング" は factory23_jp の13番目
    # "工作機械 API 集成" (Fanuc API に相当) は factory23_jp の14番目
    [1,1,0,0,0,3,2,0,0,1,0,0,3,3,3,1,1,0,0,0,0,0,0], # FA項目スコア
    N) # N は結合後のラベル総数

# 難易度評価：殆どの高度なスキル＆FAスキルは「難しい」(4)。自己学習した項目は(3)。
diff_ui = pad(
    # 32 システム項目
    [4,4,4,4,3,3,3,4,4,4,4,4,4,4,3,3,3,4,4,4,
     4,4,4,4,4,4,4,4,4,4,3,3] +
    # 23 ファクトリーオートメーション項目
    [4,4,4,4,4,4,4,4,4,3,4,4,3,3,3,4,4,4,4,4,4,4,4], # FA項目難易度
    N)

# ---------- 描画 ----------
def draw_single_role(prof, diff, title, color="tab:purple"):
    fig, ax = plt.subplots(figsize=(13,13), subplot_kw=dict(polar=True))
    ax.set_thetagrids(np.degrees(angles[:-1]), labels, fontsize=7) # フォントサイズ調整
    ax.set_ylim(0,5)
    ax.set_title(title, fontsize=18, pad=35)

    prof_closed = prof + prof[:1] # 閉じるために最初の値を追加
    ax.plot(angles, prof_closed, lw=1.4, color=color, label="UI_FANUC_Dev (スキル)") # ラベルをより具体的に
    ax.fill(angles, prof_closed, alpha=0.10, color=color)

    for ang, r, d in zip(angles[:-1], prof, diff):
        # バブルのサイズを難易度(d)に応じて調整 (dが大きいほど大きなバブル)
        # dの値が0-4の範囲を想定しているため、d**2 は 0-16。これに係数をかけてサイズを調整
        ax.scatter(ang, r, s=30*(d**1.8), color=color, # サイズのスケールを調整
                   alpha=0.7, edgecolors="k", linewidths=0.25)

    # 凡例の位置を調整
    ax.legend(loc="upper right", bbox_to_anchor=(1.3, 1.15))
    plt.tight_layout() # レイアウトを自動調整
    plt.show()

if __name__ == "__main__":
    # N（ラベル総数）が prof_ui と diff_ui の要素数と一致することを確認
    if len(prof_ui) != N or len(diff_ui) != N:
        print(f"警告：ラベル数({N})と評価/難易度データ数({len(prof_ui)}, {len(diff_ui)})が一致しません。")
        print(f"labelsの長さ: {len(labels)}")
        print(f"prof_uiの長さ: {len(prof_ui)}")
        print(f"diff_uiの長さ: {len(diff_ui)}")
        # 必要に応じて pad 関数の呼び出しで N を使うように修正
        # 例: prof_ui = pad(..., N)

    draw_single_role(prof_ui, diff_ui,
                     title="55次元スキルバブルレーダー：UI_FANUC_Dev")