# 密码记住功能说明

## 功能概述
在注册按钮旁边新增了"记住密码"复选框，选中后系统会安全地保存用户密码，下次启动客户端时自动填入。

## 功能特点

### 1. 安全加密存储
- 使用 Fernet 对称加密算法（基于 AES 128）
- 每个用户的加密密钥单独生成并安全存储
- 密码文件具有严格的文件权限控制（仅用户可读写）

### 2. 自动化体验
- **启动时自动加载**: 客户端启动时自动填入最近使用的员工ID和对应密码
- **输入时智能提示**: 输入员工ID时自动检查是否有保存的密码并填入
- **登录后自动保存**: 成功登录后根据复选框状态决定是否保存密码

### 3. 完整的生命周期管理
- **注册时保存**: 硬件指纹注册成功后可同时保存密码
- **登录时保存**: 登录成功后可保存密码
- **删除时清理**: 删除硬件指纹注册时同时清理保存的密码

## 存储位置
```
~/.mysuite/
├── .key                    # 加密密钥（权限 600）
├── saved_passwords.encrypted  # 加密的密码文件（权限 600）
└── last_user.json         # 最近使用的员工ID
```

## 使用流程

### 首次使用
1. 输入员工ID和密码
2. 勾选"记住密码"复选框
3. 点击"注册"或"确认登录"
4. 系统提示密码已保存

### 后续使用
1. 启动客户端，自动填入最近使用的员工ID和密码
2. 直接点击"确认登录"即可

### 更换密码
1. 输入新密码
2. 勾选"记住密码"
3. 登录成功后新密码自动覆盖旧密码

### 清除保存的密码
1. 点击"删除注册"按钮
2. 输入管理员密码确认
3. 系统同时清除硬件指纹和保存的密码

## 安全考虑

### 加密强度
- 使用工业级 AES 加密算法
- 每个用户独立的加密密钥
- 密钥和密码文件分离存储

### 文件权限
- 密钥文件权限设置为 600（仅用户可读写）
- 密码文件权限设置为 600（仅用户可读写）
- 配置目录创建在用户主目录下

### 错误处理
- 加密/解密失败时安全降级
- 文件损坏时自动重新初始化
- 权限不足时给出明确提示

## 技术实现

### 核心类
```python
class PasswordManager:
    def save_password(employee_id: str, password: str) -> bool
    def load_password(employee_id: str) -> str  
    def remove_password(employee_id: str) -> bool
```

### 依赖库
- `cryptography`: 提供 Fernet 加密功能
- `pathlib`: 跨平台路径处理
- `json`: 配置文件处理

### 集成点
- 主窗口初始化时创建 PasswordManager 实例
- 员工ID输入框变化时触发密码加载
- 登录/注册成功后根据复选框状态保存密码
- 删除注册时同时清理密码

## 故障排除

### 常见问题
1. **密码无法保存**: 检查是否安装了 cryptography 库
2. **密码无法加载**: 检查 ~/.mysuite 目录权限
3. **加密失败**: 删除 ~/.mysuite/.key 文件重新初始化

### 依赖安装
```bash
pip install cryptography
```

### 重置功能
如需完全重置密码记住功能，删除以下目录：
```bash
rm -rf ~/.mysuite
``` 