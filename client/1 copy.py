"""
bubble_radar_55_three_roles.py (日本語版)
-------------------------------------------------
► 55次元スキルバブルレーダー
    1. 完全自動化工場フルスタック開発者 (変更なし)
    2. 標準的Webプログラマー (日本のIT企業で一般的なWeb開発者)
    3. 中小製造業PLCエンジニア (日本の製造現場のPLC技術者)

半径 = 習熟度 (0–5)      気泡の面積 ∝ 難易度 (1–5)

依存関係： pip install matplotlib numpy
実行方法： python bubble_radar_55_three_roles.py
"""

import numpy as np
import matplotlib.pyplot as plt
import matplotlib.font_manager

# ---------- フォント設定 ----------
plt.rcParams["font.sans-serif"] = ["Noto Sans CJK JP", "TakaoPGothic", "IPAPGothic", "IPAexGothic", "Yu Gothic", "MS Gothic", "Hiragino Sans", "sans-serif"]
plt.rcParams["axes.unicode_minus"] = False # マイナス記号の文字化け対策

# ---------- 評価項目 (日本語) ----------
sys32_jp = [
    "アルゴリズム/データ構造","コーディング規約","バージョン管理","デバッグ/トラブルシューティング",
    "単体テスト","結合テスト","自動テストフレームワーク",
    "要求分析","UML/システムモデリング","モジュール分割",
    "マイクロサービスアーキテクチャ","API設計","メッセージキュー",
    "データベース設計","SQL最適化","NoSQL","キャッシュ技術",
    "DevOps/CI-CD","コンテナ/K8s","クラウドプラットフォーム",
    "認証/認可","セキュアコーディング","ペネトレーションテスト",
    "パフォーマンスチューニング","ログ/可観測性","高並行性設計","分散一貫性",
    "エラー回復/ロールバック","IaC/構成管理",
    "ドキュメント作成","コミュニケーション/コラボレーション","プロジェクト管理"
]

factory23_jp = [
    "数学/制御理論","機械設計/プロセス","組込みハードウェア","リアルタイム制御",
    "ロボットインテグレーション","PLCラダー図","PLC配線図解析","産業用プロトコル通信",
    "IIoT/データ収集","サーバーサイド開発","工場認証/セキュリティ","AI/画像処理アルゴリズム",
    "UI/フロントエンド開発","高級言語プログラミング","工作機械API連携",
    "CNC加工補正","機械安全","産業用ロボット安全",
    "リアルタイムイーサネット","エッジコンピューティング","現場スケジューリング","生産ラインシミュレーション","OEE向上"
]



# 重複を避けてラベルを結合
labels_jp = sys32_jp + [x for x in factory23_jp if x not in sys32_jp]
N = len(labels_jp)
angles = np.linspace(0, 2*np.pi, N, endpoint=False).tolist() + [0] # 閉じるために最初に戻る

# ---------- ユーティリティ関数 ----------
def pad(lst, length, fill=0):
    """リストを指定された長さにパディングする"""
    return lst + [fill]*(length-len(lst))

def make_role(prof_scores, diff_scores, color_code):
    """役割データを作成する"""
    # prof_scores と diff_scores は labels_jp と同じ順序のスコアリストであると仮定
    return {"skills": dict(zip(labels_jp, zip(prof_scores, diff_scores))), "color": color_code}

# ---------- 役割データ (日本語名) ----------
roles = {}

# 役割の日本語名
role_ff_dev_jp = "完全自動化工場フルスタック開発者"
role_web_coder_jp = "標準的Webプログラマー (日本IT企業)"
role_plc_engineer_jp = "中小製造業PLCエンジニア (現場)"

# 1) 完全自動化工場フルスタック開発者 (以前の例を維持)
# スコアは sys32_jp の32項目 + factory23_jp の23項目の順
roles[role_ff_dev_jp] = make_role(
    pad([5,5,5,5,4,4,4,5,5,5,5,5,4,5,5,4,4,5,5,5,  # システム系 (32項目)
         5,5,4,5,5,5,5,5,5,5,4,4] +
        [5,5,5,5,5,5,4,5,5,5,5,5,4,5,5,4,5,5,5,5,5,5,5], N), # FA系 (23項目)
    pad([5,4,4,4,4,4,4,5,5,5,5,4,4,5,5,4,4,5,5,5,  # システム系 (32項目)
         5,5,5,5,5,5,5,5,5,4,3,4] +
        [5,5,5,5,5,4,4,4,4,5,5,5,4,5,5,4,5,5,5,5,5,5,5], N), # FA系 (23項目)
    "tab:orange"
)

# 2) 標準的Webプログラマー (日本IT企業) — 典型的なWeb/バックエンド開発（PLC知識なし）
# スコアは sys32_jp の32項目 + factory23_jp の23項目の順
# factory23_jp の項目: UI/フロントエンド開発(4), 高級言語プログラミング(4), サーバーサイド開発(3), 工場認証/セキュリティ(2), 工作機械API連携(1) がやや高め。他は低い。
# factory23_jp indices: サーバーサイド(9), 工場認証(10), UI/フロント(12), 高級言語(13), 工作機械API(14)
web_coder_factory_prof = [0]*len(factory23_jp) # 基本は0
web_coder_factory_prof[9] = 3 # サーバーサイド開発
web_coder_factory_prof[10] = 2 # 工場認証/セキュリティ
web_coder_factory_prof[12] = 4 # UI/フロントエンド開発
web_coder_factory_prof[13] = 4 # 高級言語プログラミング
web_coder_factory_prof[14] = 1 # 工作機械API連携
# 他のfactory項目は低いスコア（例：1や0）を設定
# 元データ: [1,1,0,0,0,0,0,0,0,3,2,0,4,4,1,0,0,0,0,0,0,0,0]
# 念のため元データに合わせておく
original_web_coder_factory_prof = [1,1,0,0,0,0,0,0,0,3,2,0,4,4,1,0,0,0,0,0,0,0,0]

roles[role_web_coder_jp] = make_role(
    pad([4,4,4,4,3,3,2,3,2,3,3,4,3,3,3,2,3,  # システム系 (32項目)
         3,3,3,3,2,2,3,3,3,2,2,2,3,3] +
        original_web_coder_factory_prof, N), # FA系 (23項目)
    pad([4,3,3,3,3,3,3,4,4,4,4,3,3,3,3,2,2,  # システム系 (32項目)
         4,3,3,3,4,4,3,3,4,4,4,3,3,2] +
        [4,4,4,4,4,4,4,4,4,3,3,4,3,3,3,4,4,4,4,4,4,4,4], N), # FA系 (23項目)
    "tab:blue"
)

# 3) 中小製造業PLCエンジニア (現場) — システム32項目はほぼ使用せず
# 専門：PLCラダー図(5), PLC配線図解析(5); 産業用プロトコル通信(3); 機械安全(2)
# factory23_jp indices: PLCラダー図(5), PLC配線図解析(6), 産業用プロトコル(7), 機械安全(16)
plc_sys_prof = [1]*len(sys32_jp)  # システム32項目はほぼ1
plc_factory_prof = [1]*len(factory23_jp) # 基本は1
plc_factory_prof[1] = 2 # 機械設計/プロセス
plc_factory_prof[3] = 2 # リアルタイム制御
plc_factory_prof[5] = 5 # PLCラダー図
plc_factory_prof[6] = 5 # PLC配線図解析
plc_factory_prof[7] = 3 # 産業用プロトコル通信
plc_factory_prof[8] = 2 # IIoT/データ収集
plc_factory_prof[14] = 3 # 工作機械API連携 (CNCなど)
plc_factory_prof[16] = 2 # 機械安全
plc_factory_prof[17] = 2 # 産業用ロボット安全
plc_factory_prof[18] = 2 # リアルタイムイーサネット
plc_factory_prof[19] = 2 # エッジコンピューティング
plc_factory_prof[20] = 2 # 現場スケジューリング
# 元データ: [1,2,1,2,1,5,5,3,2,1,1,0,0,1,3,0,2,2,2,2,2,2,1]
original_plc_factory_prof = [1,2,1,2,1,5,5,3,2,1,1,0,0,1,3,0,2,2,2,2,2,2,1]


plc_sys_diff  = [3]*len(sys32_jp)  # システム系難易度は中程度
# 元データ: [3,3,3,3,3,3,3,3,3,4,4,4,4,3,3,3,4,3,3,3,3,3,3]
original_plc_factory_diff = [3,3,3,3,3,3,3,3,3,4,4,4,4,3,3,3,4,3,3,3,3,3,3]

roles[role_plc_engineer_jp] = make_role(
    pad(plc_sys_prof + original_plc_factory_prof, N),
    pad(plc_sys_diff + original_plc_factory_diff, N),
    "tab:green"
)

# ---------- 描画関数 ----------
def add_role_to_plot(ax, role_name_jp):
    """指定された役割をレーダーチャートに追加する"""
    info   = roles[role_name_jp]
    # labels_jp の順序でスキルと難易度を取得
    prof_ordered   = [info["skills"].get(label, (0,1))[0] for label in labels_jp]
    diff_ordered   = [info["skills"].get(label, (0,1))[1] for label in labels_jp]
    color_code  = info["color"]

    prof_closed = prof_ordered + prof_ordered[:1] # 閉じるために最初の値を追加
    ax.plot(angles, prof_closed, lw=1.4, color=color_code, label=role_name_jp)
    ax.fill(angles, prof_closed, alpha=0.10, color=color_code)

    for ang, r_val, d_val in zip(angles[:-1], prof_ordered, diff_ordered):
        # バブルのサイズを難易度(d_val)に応じて調整
        ax.scatter(ang, r_val, s=30*(d_val**1.8), color=color_code,
                   alpha=0.7, edgecolors="k", linewidths=0.25)

def draw_radar_chart(role_names_list, chart_title):
    """複数の役割を含むレーダーチャートを描画する"""
    fig, ax = plt.subplots(figsize=(15,15), subplot_kw=dict(polar=True)) # サイズ調整
    ax.set_thetagrids(np.degrees(angles[:-1]), labels_jp, fontsize=7) # フォントサイズ調整
    ax.set_ylim(0,5.5) # Y軸の範囲を少し広げる
    ax.set_title(chart_title, fontsize=20, pad=40) # タイトルサイズとパディング調整

    for name_jp in role_names_list:
        add_role_to_plot(ax, name_jp)

    # 凡例の位置とフォントサイズを調整
    ax.legend(loc="upper right", bbox_to_anchor=(1.35, 1.15), fontsize=10)
    plt.tight_layout(pad=1.5) # レイアウト調整、padで余白確保
    plt.show()

# ---------- メイン処理 ----------
if __name__ == "__main__":
    roles_to_draw = [role_ff_dev_jp, role_web_coder_jp, role_plc_engineer_jp]
    
    # プロットタイトルを構成
    title_parts = [name.split(" (")[0] for name in roles_to_draw] # カッコ内を除いた短い名前を使用
    plot_title = f"55次元スキルバブルレーダー：\n{title_parts[0]} vs {title_parts[1]} vs {title_parts[2]}"
    
    draw_radar_chart(roles_to_draw, plot_title)