#!/usr/bin/env python3
# server4/start_video_service.py
# 20250619.17:00 视频监控微服务 - 启动脚本
"""
MySuite 视频监控微服务启动脚本
"""

import sys
import os
import logging
import uvicorn

# 添加当前目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from app.config import settings

def main():
    """主函数"""
    print(f"Starting {settings.SERVICE_NAME}...")
    print(f"Version: {settings.SERVICE_VERSION}")
    print(f"Host: {settings.SERVICE_HOST}")
    print(f"Port: {settings.SERVICE_PORT}")
    print(f"Camera Index: {settings.CAMERA_INDEX}")
    print(f"Video Resolution: {settings.VIDEO_WIDTH}x{settings.VIDEO_HEIGHT}")
    print(f"Video FPS: {settings.VIDEO_FPS}")
    print("-" * 50)
    
    try:
        uvicorn.run(
            "app.main:app",
            host=settings.SERVICE_HOST,
            port=settings.SERVICE_PORT,
            reload=False,
            log_level=settings.LOG_LEVEL.lower(),
            access_log=True
        )
    except KeyboardInterrupt:
        print("\nShutting down video service...")
    except Exception as e:
        print(f"Error starting video service: {e}")
        sys.exit(1)

if __name__ == "__main__":
    main() 