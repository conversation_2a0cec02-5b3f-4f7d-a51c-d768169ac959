# server4/app/config.py
# 20250619.17:00 视频监控微服务 - 配置文件
"""
MySuite 视频监控微服务配置
"""

# 兼容不同版本的pydantic
try:
    from pydantic_settings import BaseSettings
except ImportError:
    try:
        from pydantic import BaseSettings
    except ImportError:
        class BaseSettings:
            def __init__(self, **kwargs):
                for key, value in kwargs.items():
                    setattr(self, key, value)
            
            class Config:
                env_file = ".env"
                env_file_encoding = "utf-8"

from typing import Optional, List
import os
from pathlib import Path
from datetime import datetime

class Settings(BaseSettings):
    """视频监控微服务配置类"""
    
    # 服务器基本配置
    SERVICE_NAME: str = "MySuite Video Monitoring Microservice"
    SERVICE_VERSION: str = "1.0.0"
    SERVICE_PORT: int = 8007
    SERVICE_HOST: str = "0.0.0.0"
    
    # JWT配置（与其他服务保持一致）
    JWT_SECRET_KEY: str = "your-very-secret-signing-key"
    JWT_ALGORITHM: str = "HS256"
    JWT_ACCESS_TOKEN_EXPIRE_HOURS: int = 24
    
    # 视频配置
    CAMERA_INDEX: int = 1  # 默认摄像头索引（使用video1）
    VIDEO_WIDTH: int = 640
    VIDEO_HEIGHT: int = 480
    VIDEO_FPS: int = 30
    VIDEO_QUALITY: int = 80  # JPEG质量 (1-100)
    
    # WebSocket配置
    WS_MAX_CONNECTIONS: int = 10
    WS_HEARTBEAT_INTERVAL: int = 30
    WS_CONNECTION_TIMEOUT: int = 300
    
    # YOLO配置（未来使用）
    YOLO_ENABLED: bool = False
    YOLO_MODEL_PATH: str = "yolov8n.pt"
    YOLO_CONFIDENCE: float = 0.5
    YOLO_IOU_THRESHOLD: float = 0.5
    
    # 录像配置（可选）
    RECORDING_ENABLED: bool = False
    RECORDING_PATH: str = str(Path(__file__).parent.parent / "recordings")
    RECORDING_MAX_DURATION: int = 3600  # 1小时
    
    # 跨域配置
    CORS_ORIGINS: list = ["*"]
    
    # 日志配置
    LOG_LEVEL: str = "INFO"
    LOG_FORMAT: str = "%(asctime)s - %(name)s - %(levelname)s - %(message)s"
    
    class Config:
        env_file = ".env"
        env_file_encoding = "utf-8"
    
    def ensure_recording_directory(self):
        """确保录像目录存在"""
        if self.RECORDING_ENABLED:
            recording_path = Path(self.RECORDING_PATH)
            recording_path.mkdir(parents=True, exist_ok=True)
            return recording_path
        return None
    
    def get_current_timestamp(self):
        """获取当前时间戳"""
        return datetime.now().isoformat()

# 创建全局配置实例
settings = Settings()

# 配置验证函数
def validate_settings():
    """验证配置是否正确"""
    errors = []
    
    if not settings.JWT_SECRET_KEY or len(settings.JWT_SECRET_KEY) < 10:
        errors.append("JWT_SECRET_KEY should be set to a secure value")
    
    if settings.SERVICE_PORT < 1024 or settings.SERVICE_PORT > 65535:
        errors.append("SERVICE_PORT should be between 1024 and 65535")
    
    if settings.VIDEO_WIDTH <= 0 or settings.VIDEO_HEIGHT <= 0:
        errors.append("Video dimensions must be positive")
    
    if settings.VIDEO_FPS <= 0 or settings.VIDEO_FPS > 60:
        errors.append("Video FPS should be between 1 and 60")
    
    if settings.VIDEO_QUALITY < 1 or settings.VIDEO_QUALITY > 100:
        errors.append("Video quality should be between 1 and 100")
    
    if errors:
        raise ValueError(f"Configuration errors: {'; '.join(errors)}")
    
    return True

# 初始化配置
try:
    validate_settings()
    settings.ensure_recording_directory()
    print(f"Video Monitoring Service Configuration validated successfully")
    print(f"Service will run on port: {settings.SERVICE_PORT}")
    print(f"Camera index: {settings.CAMERA_INDEX}")
    print(f"Video resolution: {settings.VIDEO_WIDTH}x{settings.VIDEO_HEIGHT}")
except Exception as e:
    print(f"Configuration error: {e}")
    raise 