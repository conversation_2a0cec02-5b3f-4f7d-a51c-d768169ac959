# server4/app/main.py
# 20250619.17:00 视频监控微服务 - 主应用文件
"""
MySuite 视频监控微服务主应用
"""

import asyncio
import logging
import uvicorn
from contextlib import asynccontextmanager
from fastapi import FastAPI
from fastapi.middleware.cors import CORSMiddleware
from fastapi.responses import JSONResponse

from .config import settings
from .core.services.video_service import video_service
from .routers import video_api, video_websocket
from .routers.video_websocket import video_broadcast_task, stats_broadcast_task

# 配置日志
logging.basicConfig(
    level=getattr(logging, settings.LOG_LEVEL),
    format=settings.LOG_FORMAT
)
logger = logging.getLogger(__name__)

# 后台任务管理
background_tasks = set()

@asynccontextmanager
async def lifespan(app: FastAPI):
    """应用生命周期管理"""
    logger.info("Starting MySuite Video Monitoring Microservice...")
    
    try:
        # 初始化视频服务
        success = await video_service.initialize()
        if success:
            logger.info("Video service initialized successfully")
            video_service.start_capture()
            logger.info("Video capture started")
        else:
            logger.warning("Failed to initialize video service - camera may not be available")
        
        # 启动后台任务
        broadcast_task = asyncio.create_task(video_broadcast_task())
        stats_task = asyncio.create_task(stats_broadcast_task())
        
        background_tasks.add(broadcast_task)
        background_tasks.add(stats_task)
        
        logger.info("Background tasks started")
        logger.info(f"Video Monitoring Microservice started successfully on port {settings.SERVICE_PORT}")
        
        yield
        
    except Exception as e:
        logger.error(f"Error during startup: {e}")
        yield
    
    finally:
        logger.info("Shutting down MySuite Video Monitoring Microservice...")
        
        # 取消后台任务
        for task in background_tasks:
            task.cancel()
        
        # 等待任务完成
        if background_tasks:
            await asyncio.gather(*background_tasks, return_exceptions=True)
        
        # 清理视频服务
        await video_service.cleanup()
        
        logger.info("Video Monitoring Microservice shutdown completed")

# 创建FastAPI应用
app = FastAPI(
    title=settings.SERVICE_NAME,
    version=settings.SERVICE_VERSION,
    description="MySuite Video Monitoring Microservice - Real-time video streaming and future YOLO detection",
    lifespan=lifespan
)

# 添加CORS中间件
app.add_middleware(
    CORSMiddleware,
    allow_origins=settings.CORS_ORIGINS,
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# 注册路由
app.include_router(video_api.router, prefix="/api/video", tags=["video"])
app.include_router(video_websocket.router, tags=["websocket"])

# 根路径
@app.get("/")
async def root():
    """根路径信息"""
    return {
        "service": settings.SERVICE_NAME,
        "version": settings.SERVICE_VERSION,
        "status": "running",
        "endpoints": {
            "health": "/api/video/health",
            "info": "/api/video/info",
            "stats": "/api/video/stats",
            "snapshot": "/api/video/snapshot",
            "websocket": "/ws/video"
        }
    }

# 全局异常处理器
@app.exception_handler(Exception)
async def global_exception_handler(request, exc):
    """全局异常处理"""
    logger.error(f"Global exception: {exc}")
    return JSONResponse(
        status_code=500,
        content={
            "error": "Internal server error",
            "message": "An unexpected error occurred"
        }
    )

if __name__ == "__main__":
    uvicorn.run(
        "app.main:app",
        host=settings.SERVICE_HOST,
        port=settings.SERVICE_PORT,
        reload=False,
        log_level=settings.LOG_LEVEL.lower()
    ) 