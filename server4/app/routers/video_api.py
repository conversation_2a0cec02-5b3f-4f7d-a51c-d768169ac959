# server4/app/routers/video_api.py
# 20250619.17:00 视频监控微服务 - 视频API路由
"""
视频API路由
提供RESTful接口用于视频服务管理
"""

import logging
from typing import Dict, Any
from fastapi import APIRouter, HTTPException, status
from fastapi.responses import JSONResponse

from ..core.services.video_service import video_service
from ..core.services.yolo_service import yolo_service
from ..config import settings

logger = logging.getLogger(__name__)

router = APIRouter()

@router.get("/health")
async def health_check():
    """健康检查端点"""
    return {
        "status": "healthy",
        "service": settings.SERVICE_NAME,
        "version": settings.SERVICE_VERSION,
        "timestamp": settings.get_current_timestamp()
    }

@router.get("/info")
async def get_service_info():
    """获取服务信息"""
    return {
        "service_name": settings.SERVICE_NAME,
        "version": settings.SERVICE_VERSION,
        "port": settings.SERVICE_PORT,
        "video_config": {
            "width": settings.VIDEO_WIDTH,
            "height": settings.VIDEO_HEIGHT,
            "fps": settings.VIDEO_FPS,
            "quality": settings.VIDEO_QUALITY,
            "camera_index": settings.CAMERA_INDEX
        },
        "camera_info": video_service.get_stats().get("camera_info", {}),
        "endpoints": {
            "websocket": "/ws/video",
            "health": "/api/video/health",
            "info": "/api/video/info",
            "stats": "/api/video/stats",
            "snapshot": "/api/video/snapshot"
        }
    }

@router.get("/stats")
async def get_stats():
    """获取统计信息"""
    stats = video_service.get_stats()
    return {
        "success": True,
        "data": stats,
        "timestamp": settings.get_current_timestamp()
    }

@router.get("/snapshot")
async def get_snapshot():
    """获取当前视频快照"""
    try:
        frame_data = video_service.get_latest_frame_base64()
        
        if frame_data:
            return {
                "success": True,
                "data": {
                    "image": frame_data,
                    "format": "jpeg",
                    "encoding": "base64"
                },
                "timestamp": settings.get_current_timestamp()
            }
        else:
            return {
                "success": False,
                "error": "No frame available",
                "timestamp": settings.get_current_timestamp()
            }
    
    except Exception as e:
        logger.error(f"Error getting snapshot: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to get snapshot: {str(e)}"
        )

@router.post("/camera/start")
async def start_camera():
    """启动摄像头捕获"""
    try:
        video_service.start_capture()
        return {
            "success": True,
            "message": "Camera capture started",
            "timestamp": settings.get_current_timestamp()
        }
    except Exception as e:
        logger.error(f"Error starting camera: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to start camera: {str(e)}"
        )

@router.post("/camera/stop")
async def stop_camera():
    """停止摄像头捕获"""
    try:
        video_service.stop_capture()
        return {
            "success": True,
            "message": "Camera capture stopped",
            "timestamp": settings.get_current_timestamp()
        }
    except Exception as e:
        logger.error(f"Error stopping camera: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to stop camera: {str(e)}"
        )

@router.post("/camera/restart")
async def restart_camera():
    """重启摄像头（解决显示问题）"""
    try:
        logger.info("Restarting camera via API request")
        success = await video_service.restart_camera()
        
        if success:
            return {
                "success": True,
                "message": "Camera restarted successfully",
                "camera_info": video_service.get_stats().get("camera_info", {}),
                "timestamp": settings.get_current_timestamp()
            }
        else:
            return {
                "success": False,
                "error": "Failed to restart camera",
                "timestamp": settings.get_current_timestamp()
            }
    
    except Exception as e:
        logger.error(f"Error restarting camera: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to restart camera: {str(e)}"
        )

@router.get("/camera/test")
async def test_camera():
    """测试摄像头状态和获取诊断信息"""
    try:
        stats = video_service.get_stats()
        
        # 尝试获取一帧用于测试
        frame_data = video_service.get_latest_frame_base64()
        
        return {
            "success": True,
            "data": {
                "camera_status": stats.get("camera_status", "unknown"),
                "camera_info": stats.get("camera_info", {}),
                "total_frames": stats.get("total_frames", 0),
                "fps_actual": stats.get("fps_actual", 0),
                "connected_clients": stats.get("connected_clients", 0),
                "frame_available": frame_data is not None,
                "frame_size_kb": len(frame_data) // 1024 if frame_data else 0
            },
            "timestamp": settings.get_current_timestamp()
        }
    
    except Exception as e:
        logger.error(f"Error testing camera: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to test camera: {str(e)}"
        )

# YOLO AI检测相关端点
@router.post("/yolo/initialize")
async def initialize_yolo():
    """初始化YOLO模型"""
    try:
        success = await yolo_service.initialize_model()
        if success:
            return {
                "success": True,
                "message": "YOLO model initialized successfully",
                "model_info": yolo_service.get_stats().get("model_info", {}),
                "timestamp": settings.get_current_timestamp()
            }
        else:
            return {
                "success": False,
                "error": "Failed to initialize YOLO model",
                "timestamp": settings.get_current_timestamp()
            }
    except Exception as e:
        logger.error(f"Error initializing YOLO: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to initialize YOLO: {str(e)}"
        )

@router.post("/yolo/enable")
async def enable_yolo():
    """启用YOLO物体检测"""
    try:
        success = video_service.enable_yolo_detection()
        if success:
            return {
                "success": True,
                "message": "YOLO detection enabled",
                "timestamp": settings.get_current_timestamp()
            }
        else:
            return {
                "success": False,
                "error": "Failed to enable YOLO detection. Make sure model is initialized first.",
                "timestamp": settings.get_current_timestamp()
            }
    except Exception as e:
        logger.error(f"Error enabling YOLO: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to enable YOLO: {str(e)}"
        )

@router.post("/yolo/disable")
async def disable_yolo():
    """禁用YOLO物体检测"""
    try:
        video_service.disable_yolo_detection()
        return {
            "success": True,
            "message": "YOLO detection disabled",
            "timestamp": settings.get_current_timestamp()
        }
    except Exception as e:
        logger.error(f"Error disabling YOLO: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to disable YOLO: {str(e)}"
        )

@router.get("/yolo/status")
async def get_yolo_status():
    """获取YOLO检测状态和统计信息"""
    try:
        yolo_stats = yolo_service.get_stats()
        video_stats = video_service.get_stats()
        
        return {
            "success": True,
            "data": {
                "yolo_available": yolo_stats["yolo_available"],
                "model_loaded": yolo_stats["model_loaded"],
                "is_enabled": yolo_stats["is_enabled"],
                "is_loading": yolo_stats["is_loading"],
                "video_yolo_enabled": video_stats["yolo_enabled"],
                "total_detections": yolo_stats["total_detections"],
                "objects_detected": yolo_stats["objects_detected"],
                "last_detection_time": yolo_stats["last_detection_time"],
                "detection_classes": yolo_stats["detection_classes"],
                "last_detections": video_stats["last_detections"],
                "model_info": yolo_stats.get("model_info", {})
            },
            "timestamp": settings.get_current_timestamp()
        }
    except Exception as e:
        logger.error(f"Error getting YOLO status: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to get YOLO status: {str(e)}"
        ) 