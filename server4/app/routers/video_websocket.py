# server4/app/routers/video_websocket.py
# 20250619.17:00 视频监控微服务 - 视频WebSocket路由
"""
视频WebSocket路由
负责实时视频流传输
"""

import asyncio
import logging
import json
import uuid
from typing import Dict, Any
from fastapi import APIRouter, WebSocket, WebSocketDisconnect, Depends, HTTPException, status
from fastapi.security import HTTPBearer

from ..core.services.video_service import video_service
from ..config import settings

logger = logging.getLogger(__name__)

router = APIRouter()

# 连接管理器
class VideoConnectionManager:
    def __init__(self):
        self.active_connections: Dict[str, WebSocket] = {}
        self.client_info: Dict[str, Dict[str, Any]] = {}
    
    async def connect(self, websocket: WebSocket, client_id: str, user_info: Dict[str, Any] = None):
        """接受WebSocket连接"""
        await websocket.accept()
        self.active_connections[client_id] = websocket
        self.client_info[client_id] = {
            "connected_at": asyncio.get_event_loop().time(),
            "user_info": user_info or {},
            "frames_sent": 0
        }
        
        # 通知视频服务
        video_service.add_client(client_id)
        logger.info(f"Video WebSocket client {client_id} connected")
    
    def disconnect(self, client_id: str):
        """断开WebSocket连接"""
        if client_id in self.active_connections:
            del self.active_connections[client_id]
        if client_id in self.client_info:
            del self.client_info[client_id]
        
        # 通知视频服务
        video_service.remove_client(client_id)
        logger.info(f"Video WebSocket client {client_id} disconnected")
    
    async def send_frame_to_client(self, client_id: str, frame_data: str):
        """向特定客户端发送视频帧"""
        if client_id in self.active_connections:
            try:
                websocket = self.active_connections[client_id]
                message = {
                    "type": "video_frame",
                    "data": frame_data,
                    "timestamp": asyncio.get_event_loop().time()
                }
                await websocket.send_text(json.dumps(message))
                
                # 更新统计
                if client_id in self.client_info:
                    self.client_info[client_id]["frames_sent"] += 1
                    
            except Exception as e:
                logger.error(f"Error sending frame to client {client_id}: {e}")
                self.disconnect(client_id)
    
    async def broadcast_frame(self, frame_data: str):
        """向所有连接的客户端广播视频帧"""
        if not self.active_connections:
            return
        
        message = {
            "type": "video_frame",
            "data": frame_data,
            "timestamp": asyncio.get_event_loop().time()
        }
        message_str = json.dumps(message)
        
        # 并行发送给所有客户端
        tasks = []
        for client_id in list(self.active_connections.keys()):
            tasks.append(self._send_to_client(client_id, message_str))
        
        if tasks:
            await asyncio.gather(*tasks, return_exceptions=True)
    
    async def _send_to_client(self, client_id: str, message: str):
        """内部方法：向客户端发送消息"""
        try:
            if client_id in self.active_connections:
                websocket = self.active_connections[client_id]
                await websocket.send_text(message)
                
                # 更新统计
                if client_id in self.client_info:
                    self.client_info[client_id]["frames_sent"] += 1
                    
        except Exception as e:
            logger.error(f"Error sending message to client {client_id}: {e}")
            self.disconnect(client_id)
    
    async def send_stats(self):
        """向所有客户端发送统计信息"""
        if not self.active_connections:
            return
        
        stats = video_service.get_stats()
        stats["ws_connections"] = len(self.active_connections)
        
        message = {
            "type": "stats",
            "data": stats
        }
        message_str = json.dumps(message)
        
        for client_id in list(self.active_connections.keys()):
            try:
                websocket = self.active_connections[client_id]
                await websocket.send_text(message_str)
            except Exception as e:
                logger.error(f"Error sending stats to client {client_id}: {e}")
                self.disconnect(client_id)
    
    def get_connection_info(self) -> Dict[str, Any]:
        """获取连接信息"""
        return {
            "total_connections": len(self.active_connections),
            "clients": self.client_info.copy()
        }

# 全局连接管理器
connection_manager = VideoConnectionManager()

@router.websocket("/ws/video")
async def video_websocket_endpoint(websocket: WebSocket, token: str = None):
    """视频WebSocket端点"""
    client_id = str(uuid.uuid4())
    
    try:
        # 简单的token验证（可以扩展为完整的JWT验证）
        user_info = {"token": token} if token else {}
        
        # 接受连接
        await connection_manager.connect(websocket, client_id, user_info)
        
        # 发送欢迎消息
        welcome_message = {
            "type": "welcome",
            "client_id": client_id,
            "message": "Connected to video stream",
            "video_config": {
                "width": settings.VIDEO_WIDTH,
                "height": settings.VIDEO_HEIGHT,
                "fps": settings.VIDEO_FPS,
                "quality": settings.VIDEO_QUALITY
            }
        }
        await websocket.send_text(json.dumps(welcome_message))
        
        # 监听客户端消息
        while True:
            try:
                data = await websocket.receive_text()
                message = json.loads(data)
                
                message_type = message.get("type", "")
                
                if message_type == "ping":
                    # 心跳响应
                    pong_message = {
                        "type": "pong",
                        "timestamp": asyncio.get_event_loop().time()
                    }
                    await websocket.send_text(json.dumps(pong_message))
                
                elif message_type == "request_stats":
                    # 发送统计信息
                    stats = video_service.get_stats()
                    stats_message = {
                        "type": "stats",
                        "data": stats
                    }
                    await websocket.send_text(json.dumps(stats_message))
                
                elif message_type == "start_stream":
                    # 客户端请求开始视频流
                    logger.info(f"Client {client_id} requested video stream start")
                
                elif message_type == "stop_stream":
                    # 客户端请求停止视频流
                    logger.info(f"Client {client_id} requested video stream stop")
                
                else:
                    logger.warning(f"Unknown message type from client {client_id}: {message_type}")
                    
            except WebSocketDisconnect:
                break
            except json.JSONDecodeError:
                logger.error(f"Invalid JSON from client {client_id}")
                continue
            except Exception as e:
                logger.error(f"Error handling message from client {client_id}: {e}")
                break
    
    except Exception as e:
        logger.error(f"Error in video WebSocket endpoint: {e}")
    
    finally:
        connection_manager.disconnect(client_id)

# 视频流广播任务
async def video_broadcast_task():
    """视频广播任务"""
    logger.info("Starting video broadcast task")
    
    while True:
        try:
            if connection_manager.active_connections:
                # 获取最新帧
                frame_data = video_service.get_latest_frame_base64()
                
                if frame_data:
                    # 广播给所有客户端
                    await connection_manager.broadcast_frame(frame_data)
                
                # 控制广播频率
                await asyncio.sleep(1.0 / settings.VIDEO_FPS)
            else:
                # 没有客户端连接时，降低检查频率
                await asyncio.sleep(1.0)
                
        except Exception as e:
            logger.error(f"Error in video broadcast task: {e}")
            await asyncio.sleep(1.0)

# 统计信息广播任务
async def stats_broadcast_task():
    """统计信息广播任务（每5秒一次）"""
    while True:
        try:
            await asyncio.sleep(5.0)
            if connection_manager.active_connections:
                await connection_manager.send_stats()
        except Exception as e:
            logger.error(f"Error in stats broadcast task: {e}")
            await asyncio.sleep(5.0) 