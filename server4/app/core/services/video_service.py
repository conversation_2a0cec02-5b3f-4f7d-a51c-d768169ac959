# server4/app/core/services/video_service.py
# 20250619.17:00 视频监控微服务 - 视频服务核心模块
"""
视频服务核心模块
负责摄像头捕获、图像处理和视频流传输
"""

import cv2
import asyncio
import logging
import base64
import threading
import time
from typing import Optional, List, Dict, Any
from datetime import datetime
import json
import numpy as np

from ...config import settings
from .yolo_service import yolo_service

logger = logging.getLogger(__name__)

class VideoService:
    """视频服务类"""
    
    def __init__(self):
        self.camera = None
        self.is_running = False
        self.connected_clients = set()
        self.frame_lock = threading.Lock()
        self.latest_frame = None
        self.capture_thread = None
        
        # 统计信息
        self.stats = {
            "total_frames": 0,
            "fps_actual": 0,
            "connected_clients": 0,
            "last_frame_time": None,
            "camera_status": "disconnected",
            "camera_info": {}
        }
        
        # YOLO检测状态
        self.yolo_enabled = False
        self.last_detections = []
    
    def _detect_camera_capabilities(self, camera):
        """检测摄像头能力和最佳设置"""
        try:
            # 获取摄像头信息
            width = camera.get(cv2.CAP_PROP_FRAME_WIDTH)
            height = camera.get(cv2.CAP_PROP_FRAME_HEIGHT)
            fps = camera.get(cv2.CAP_PROP_FPS)
            fourcc = camera.get(cv2.CAP_PROP_FOURCC)
            
            camera_info = {
                "default_width": int(width),
                "default_height": int(height),
                "default_fps": fps,
                "fourcc": fourcc
            }
            
            logger.info(f"Camera default settings: {camera_info}")
            
            # 尝试设置常见的分辨率
            common_resolutions = [
                (640, 480),   # VGA
                (320, 240),   # QVGA
                (800, 600),   # SVGA
                (1280, 720),  # HD
                (1920, 1080)  # Full HD
            ]
            
            best_resolution = None
            for w, h in common_resolutions:
                camera.set(cv2.CAP_PROP_FRAME_WIDTH, w)
                camera.set(cv2.CAP_PROP_FRAME_HEIGHT, h)
                
                # 读取一帧测试
                ret, frame = camera.read()
                if ret and frame is not None:
                    actual_w, actual_h = frame.shape[1], frame.shape[0]
                    if actual_w == w and actual_h == h:
                        best_resolution = (w, h)
                        logger.info(f"Successfully set resolution: {w}x{h}")
                        break
                    else:
                        logger.warning(f"Requested {w}x{h}, got {actual_w}x{actual_h}")
            
            if best_resolution:
                camera_info["best_resolution"] = best_resolution
            else:
                # 使用默认分辨率
                camera.set(cv2.CAP_PROP_FRAME_WIDTH, width)
                camera.set(cv2.CAP_PROP_FRAME_HEIGHT, height)
                camera_info["best_resolution"] = (int(width), int(height))
            
            return camera_info
            
        except Exception as e:
            logger.error(f"Error detecting camera capabilities: {e}")
            return {"error": str(e)}
    
    async def initialize(self) -> bool:
        """初始化视频服务"""
        try:
            logger.info(f"Initializing video service with camera index: {settings.CAMERA_INDEX}")
            
            # 在线程中初始化摄像头以避免阻塞
            def init_camera():
                try:
                    # 尝试不同的后端
                    backends = [
                        cv2.CAP_V4L2,    # Linux V4L2
                        cv2.CAP_GSTREAMER, # GStreamer
                        cv2.CAP_ANY      # 默认
                    ]
                    
                    camera = None
                    for backend in backends:
                        try:
                            logger.info(f"Trying backend: {backend}")
                            camera = cv2.VideoCapture(settings.CAMERA_INDEX, backend)
                            
                            if camera.isOpened():
                                # 检测摄像头能力
                                camera_info = self._detect_camera_capabilities(camera)
                                self.stats["camera_info"] = camera_info
                                
                                # 设置缓冲区大小（减少延迟）
                                camera.set(cv2.CAP_PROP_BUFFERSIZE, 1)
                                
                                # 设置像素格式（如果支持）
                                try:
                                    # 尝试设置为MJPEG格式（通常质量更好）
                                    camera.set(cv2.CAP_PROP_FOURCC, cv2.VideoWriter_fourcc('M','J','P','G'))
                                except:
                                    logger.info("MJPEG format not supported, using default")
                                
                                # 设置目标分辨率和帧率
                                camera.set(cv2.CAP_PROP_FRAME_WIDTH, settings.VIDEO_WIDTH)
                                camera.set(cv2.CAP_PROP_FRAME_HEIGHT, settings.VIDEO_HEIGHT)
                                camera.set(cv2.CAP_PROP_FPS, settings.VIDEO_FPS)
                                
                                # 设置自动曝光和白平衡
                                try:
                                    camera.set(cv2.CAP_PROP_AUTO_EXPOSURE, 0.25)  # 自动曝光
                                    camera.set(cv2.CAP_PROP_AUTO_WB, 1)           # 自动白平衡
                                except:
                                    logger.info("Auto exposure/white balance not supported")
                                
                                # 测试读取多帧以稳定摄像头
                                for i in range(5):
                                    ret, frame = camera.read()
                                    if ret and frame is not None:
                                        # 检查帧是否有效
                                        if frame.size > 0 and len(frame.shape) == 3:
                                            logger.info(f"Frame {i+1}: {frame.shape}, dtype: {frame.dtype}")
                                            self.camera = camera
                                            self.stats["camera_status"] = "connected"
                                            logger.info(f"Camera initialized successfully with backend {backend}")
                                            return True
                                        else:
                                            logger.warning(f"Invalid frame {i+1}: shape={frame.shape if frame is not None else 'None'}")
                                    else:
                                        logger.warning(f"Failed to read frame {i+1}")
                                    time.sleep(0.1)  # 短暂延迟
                                
                                camera.release()
                                logger.error(f"Failed to get valid frames with backend {backend}")
                            else:
                                logger.error(f"Failed to open camera with backend {backend}")
                                if camera:
                                    camera.release()
                        
                        except Exception as e:
                            logger.error(f"Error with backend {backend}: {e}")
                            if camera:
                                camera.release()
                            continue
                    
                    logger.error("All camera backends failed")
                    return False
                    
                except Exception as e:
                    logger.error(f"Error in camera initialization: {e}")
                    return False
            
            # 在线程池中执行摄像头初始化
            loop = asyncio.get_event_loop()
            success = await loop.run_in_executor(None, init_camera)
            
            if success:
                logger.info("Video service initialized successfully")
                return True
            else:
                logger.error("Failed to initialize video service")
                return False
                
        except Exception as e:
            logger.error(f"Error initializing video service: {e}")
            return False
    
    def start_capture(self):
        """启动视频捕获线程"""
        if self.is_running:
            logger.warning("Video capture is already running")
            return
        
        if not self.camera or not self.camera.isOpened():
            logger.error("Camera not initialized")
            return
        
        self.is_running = True
        self.capture_thread = threading.Thread(target=self._capture_loop, daemon=True)
        self.capture_thread.start()
        logger.info("Video capture started")
    
    def stop_capture(self):
        """停止视频捕获"""
        self.is_running = False
        if self.capture_thread and self.capture_thread.is_alive():
            self.capture_thread.join(timeout=2)
        logger.info("Video capture stopped")
    
    def _capture_loop(self):
        """视频捕获循环（在独立线程中运行）"""
        frame_time = 1.0 / settings.VIDEO_FPS
        last_fps_time = time.time()
        frame_count = 0
        
        while self.is_running:
            try:
                if not self.camera or not self.camera.isOpened():
                    break
                
                ret, frame = self.camera.read()
                if not ret or frame is None:
                    logger.warning("Failed to read frame from camera")
                    time.sleep(0.1)
                    continue
                
                # 验证帧的有效性
                if frame.size == 0 or len(frame.shape) != 3:
                    logger.warning(f"Invalid frame: shape={frame.shape}, size={frame.size}")
                    time.sleep(0.1)
                    continue
                
                # 确保帧是RGB格式（OpenCV默认是BGR）
                if frame.shape[2] == 3:
                    # 转换BGR到RGB（如果需要显示正确颜色）
                    # 注意：对于JPEG编码，BGR格式通常是正确的
                    pass
                
                # 调整帧大小（如果需要）
                target_height, target_width = settings.VIDEO_HEIGHT, settings.VIDEO_WIDTH
                if frame.shape[1] != target_width or frame.shape[0] != target_height:
                    frame = cv2.resize(frame, (target_width, target_height), interpolation=cv2.INTER_LINEAR)
                
                # YOLO物体检测（如果启用）
                processed_frame = frame
                if self.yolo_enabled and yolo_service.is_enabled:
                    try:
                        processed_frame, detections = yolo_service.detect_objects(frame)
                        self.last_detections = detections
                    except Exception as e:
                        logger.error(f"YOLO detection error: {e}")
                        processed_frame = frame
                        self.last_detections = []
                
                # 更新最新帧
                with self.frame_lock:
                    self.latest_frame = processed_frame.copy()
                    self.stats["total_frames"] += 1
                    self.stats["last_frame_time"] = datetime.now().isoformat()
                
                # 计算实际FPS
                frame_count += 1
                current_time = time.time()
                if current_time - last_fps_time >= 1.0:
                    self.stats["fps_actual"] = round(frame_count / (current_time - last_fps_time), 1)
                    last_fps_time = current_time
                    frame_count = 0
                
                # 控制帧率
                time.sleep(max(0, frame_time - 0.001))  # 减少延迟
                
            except Exception as e:
                logger.error(f"Error in capture loop: {e}")
                time.sleep(0.1)
    
    def get_latest_frame_base64(self) -> Optional[str]:
        """获取最新帧的base64编码"""
        with self.frame_lock:
            if self.latest_frame is None:
                return None
            
            try:
                # 使用更高质量的JPEG编码参数
                encode_param = [
                    int(cv2.IMWRITE_JPEG_QUALITY), settings.VIDEO_QUALITY,
                    int(cv2.IMWRITE_JPEG_OPTIMIZE), 1,  # 优化编码
                    int(cv2.IMWRITE_JPEG_PROGRESSIVE), 1  # 渐进式JPEG
                ]
                
                ret, buffer = cv2.imencode('.jpg', self.latest_frame, encode_param)
                
                if ret and buffer is not None:
                    # 转换为base64
                    jpg_as_text = base64.b64encode(buffer).decode('utf-8')
                    return jpg_as_text
                else:
                    logger.error("Failed to encode frame to JPEG")
                    return None
                    
            except Exception as e:
                logger.error(f"Error encoding frame: {e}")
                return None
    
    def get_raw_frame(self) -> Optional[np.ndarray]:
        """获取原始帧数据（用于调试）"""
        with self.frame_lock:
            return self.latest_frame.copy() if self.latest_frame is not None else None
    
    def add_client(self, client_id: str):
        """添加连接的客户端"""
        self.connected_clients.add(client_id)
        self.stats["connected_clients"] = len(self.connected_clients)
        logger.info(f"Client {client_id} connected. Total clients: {self.stats['connected_clients']}")
    
    def remove_client(self, client_id: str):
        """移除断开连接的客户端"""
        self.connected_clients.discard(client_id)
        self.stats["connected_clients"] = len(self.connected_clients)
        logger.info(f"Client {client_id} disconnected. Total clients: {self.stats['connected_clients']}")
    
    def get_stats(self) -> Dict[str, Any]:
        """获取服务统计信息"""
        stats = self.stats.copy()
        stats["yolo_enabled"] = self.yolo_enabled
        stats["last_detections"] = self.last_detections
        return stats
    
    def enable_yolo_detection(self) -> bool:
        """启用YOLO物体检测"""
        if yolo_service.enable_detection():
            self.yolo_enabled = True
            logger.info("YOLO detection enabled for video service")
            return True
        else:
            logger.error("Failed to enable YOLO detection")
            return False
    
    def disable_yolo_detection(self):
        """禁用YOLO物体检测"""
        self.yolo_enabled = False
        self.last_detections = []
        yolo_service.disable_detection()
        logger.info("YOLO detection disabled for video service")
    
    async def restart_camera(self) -> bool:
        """重启摄像头"""
        logger.info("Restarting camera...")
        
        # 停止当前捕获
        self.stop_capture()
        
        # 释放摄像头
        if self.camera:
            self.camera.release()
            self.camera = None
        
        # 重新初始化
        success = await self.initialize()
        
        if success:
            self.start_capture()
            logger.info("Camera restarted successfully")
        else:
            logger.error("Failed to restart camera")
        
        return success
    
    async def cleanup(self):
        """清理资源"""
        logger.info("Cleaning up video service...")
        
        # 停止捕获
        self.stop_capture()
        
        # 释放摄像头
        if self.camera:
            self.camera.release()
            self.camera = None
        
        # 清空客户端列表
        self.connected_clients.clear()
        
        # 重置统计信息
        self.stats["camera_status"] = "disconnected"
        self.stats["connected_clients"] = 0
        
        logger.info("Video service cleanup completed")

# 全局视频服务实例
video_service = VideoService() 