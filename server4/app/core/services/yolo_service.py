# server4/app/core/services/yolo_service.py
# 20250619.18:00 YOLO物体检测服务
"""
YOLO AI物体检测服务
负责加载YOLO模型并进行实时物体检测
"""

import cv2
import logging
import numpy as np
import threading
import time
from typing import Optional, List, Dict, Any, Tuple
from datetime import datetime
import asyncio

# YOLO相关导入
try:
    from ultralytics import YOLO
    import torch
    YOLO_AVAILABLE = True
except ImportError as e:
    YOLO_AVAILABLE = False
    YOLO = None
    torch = None
    print(f"YOLO dependencies not available: {e}")

from ...config import settings

logger = logging.getLogger(__name__)

class YOLODetectionService:
    """YOLO物体检测服务类"""
    
    def __init__(self):
        self.model = None
        self.is_enabled = False
        self.is_loading = False
        self.detection_lock = threading.Lock()
        
        # 检测统计
        self.stats = {
            "total_detections": 0,
            "objects_detected": 0,
            "last_detection_time": None,
            "model_loaded": False,
            "model_info": {},
            "detection_classes": {}
        }
        
        # YOLO类别名称（COCO数据集）
        self.class_names = [
            'person', 'bicycle', 'car', 'motorcycle', 'airplane', 'bus', 'train', 'truck', 'boat',
            'traffic light', 'fire hydrant', 'stop sign', 'parking meter', 'bench', 'bird', 'cat',
            'dog', 'horse', 'sheep', 'cow', 'elephant', 'bear', 'zebra', 'giraffe', 'backpack',
            'umbrella', 'handbag', 'tie', 'suitcase', 'frisbee', 'skis', 'snowboard', 'sports ball',
            'kite', 'baseball bat', 'baseball glove', 'skateboard', 'surfboard', 'tennis racket',
            'bottle', 'wine glass', 'cup', 'fork', 'knife', 'spoon', 'bowl', 'banana', 'apple',
            'sandwich', 'orange', 'broccoli', 'carrot', 'hot dog', 'pizza', 'donut', 'cake',
            'chair', 'couch', 'potted plant', 'bed', 'dining table', 'toilet', 'tv', 'laptop',
            'mouse', 'remote', 'keyboard', 'cell phone', 'microwave', 'oven', 'toaster', 'sink',
            'refrigerator', 'book', 'clock', 'vase', 'scissors', 'teddy bear', 'hair drier',
            'toothbrush'
        ]
    
    async def initialize_model(self) -> bool:
        """异步初始化YOLO模型"""
        if not YOLO_AVAILABLE:
            logger.error("YOLO dependencies not available")
            return False
        
        if self.is_loading:
            logger.info("Model is already loading")
            return False
        
        self.is_loading = True
        
        try:
            logger.info(f"Loading YOLO model: {settings.YOLO_MODEL_PATH}")
            
            # 在线程池中加载模型以避免阻塞
            loop = asyncio.get_event_loop()
            success = await loop.run_in_executor(None, self._load_model_sync)
            
            if success:
                self.stats["model_loaded"] = True
                self.stats["model_info"] = {
                    "model_path": settings.YOLO_MODEL_PATH,
                    "device": str(self.model.device) if self.model else "unknown",
                    "model_type": "YOLOv8"
                }
                logger.info("YOLO model loaded successfully")
                return True
            else:
                logger.error("Failed to load YOLO model")
                return False
                
        except Exception as e:
            logger.error(f"Error loading YOLO model: {e}")
            return False
        finally:
            self.is_loading = False
    
    def _load_model_sync(self) -> bool:
        """同步加载YOLO模型"""
        try:
            # 尝试加载预训练模型
            self.model = YOLO(settings.YOLO_MODEL_PATH)
            
            # 强制使用CPU以避免GPU内存不足问题
            logger.info("Using CPU for inference (GPU memory may be insufficient)")
            self.model.to('cpu')
            
            # 预热模型
            dummy_image = np.zeros((640, 640, 3), dtype=np.uint8)
            _ = self.model(dummy_image, verbose=False)
            
            return True
            
        except Exception as e:
            logger.error(f"Error in sync model loading: {e}")
            return False
    
    def enable_detection(self) -> bool:
        """启用物体检测"""
        if not self.stats["model_loaded"]:
            logger.error("Cannot enable detection: model not loaded")
            return False
        
        self.is_enabled = True
        logger.info("YOLO detection enabled")
        return True
    
    def disable_detection(self):
        """禁用物体检测"""
        self.is_enabled = False
        logger.info("YOLO detection disabled")
    
    def detect_objects(self, frame: np.ndarray) -> Tuple[np.ndarray, List[Dict[str, Any]]]:
        """
        对帧进行物体检测
        返回: (带检测框的帧, 检测结果列表)
        """
        if not self.is_enabled or not self.model or not self.stats["model_loaded"]:
            return frame, []
        
        with self.detection_lock:
            try:
                start_time = time.time()
                
                # 运行YOLO检测
                results = self.model(frame, 
                                   conf=settings.YOLO_CONFIDENCE,
                                   iou=settings.YOLO_IOU_THRESHOLD,
                                   verbose=False)
                
                # 解析检测结果
                detections = []
                annotated_frame = frame.copy()
                
                if results and len(results) > 0:
                    result = results[0]
                    
                    if result.boxes is not None and len(result.boxes) > 0:
                        boxes = result.boxes.xyxy.cpu().numpy()  # 边界框坐标
                        confidences = result.boxes.conf.cpu().numpy()  # 置信度
                        class_ids = result.boxes.cls.cpu().numpy().astype(int)  # 类别ID
                        
                        for i, (box, conf, class_id) in enumerate(zip(boxes, confidences, class_ids)):
                            x1, y1, x2, y2 = map(int, box)
                            class_name = self.class_names[class_id] if class_id < len(self.class_names) else f"class_{class_id}"
                            
                            # 记录检测结果
                            detection = {
                                "id": i,
                                "class_id": int(class_id),
                                "class_name": class_name,
                                "confidence": float(conf),
                                "bbox": [x1, y1, x2, y2],
                                "center": [(x1 + x2) // 2, (y1 + y2) // 2],
                                "area": (x2 - x1) * (y2 - y1)
                            }
                            detections.append(detection)
                            
                            # 绘制检测框
                            color = self._get_class_color(class_id)
                            cv2.rectangle(annotated_frame, (x1, y1), (x2, y2), color, 2)
                            
                            # 绘制标签
                            label = f"{class_name}: {conf:.2f}"
                            label_size = cv2.getTextSize(label, cv2.FONT_HERSHEY_SIMPLEX, 0.5, 2)[0]
                            cv2.rectangle(annotated_frame, (x1, y1 - label_size[1] - 10), 
                                        (x1 + label_size[0], y1), color, -1)
                            cv2.putText(annotated_frame, label, (x1, y1 - 5), 
                                      cv2.FONT_HERSHEY_SIMPLEX, 0.5, (255, 255, 255), 2)
                
                # 更新统计信息
                self.stats["total_detections"] += 1
                self.stats["objects_detected"] = len(detections)
                self.stats["last_detection_time"] = datetime.now().isoformat()
                
                # 更新类别统计
                for detection in detections:
                    class_name = detection["class_name"]
                    if class_name not in self.stats["detection_classes"]:
                        self.stats["detection_classes"][class_name] = 0
                    self.stats["detection_classes"][class_name] += 1
                
                detection_time = time.time() - start_time
                logger.debug(f"Detection completed in {detection_time:.3f}s, found {len(detections)} objects")
                
                return annotated_frame, detections
                
            except Exception as e:
                logger.error(f"Error during object detection: {e}")
                return frame, []
    
    def _get_class_color(self, class_id: int) -> Tuple[int, int, int]:
        """获取类别对应的颜色"""
        # 为不同类别生成不同颜色
        colors = [
            (255, 0, 0),    # 红色 - person
            (0, 255, 0),    # 绿色 - bicycle
            (0, 0, 255),    # 蓝色 - car
            (255, 255, 0),  # 黄色 - motorcycle
            (255, 0, 255),  # 紫色 - airplane
            (0, 255, 255),  # 青色 - bus
            (128, 0, 128),  # 紫罗兰 - train
            (255, 165, 0),  # 橙色 - truck
        ]
        return colors[class_id % len(colors)]
    
    def get_stats(self) -> Dict[str, Any]:
        """获取检测统计信息"""
        return {
            **self.stats,
            "is_enabled": self.is_enabled,
            "is_loading": self.is_loading,
            "yolo_available": YOLO_AVAILABLE
        }
    
    def reset_stats(self):
        """重置统计信息"""
        self.stats["total_detections"] = 0
        self.stats["objects_detected"] = 0
        self.stats["detection_classes"] = {}
        logger.info("YOLO detection stats reset")
    
    async def cleanup(self):
        """清理资源"""
        logger.info("Cleaning up YOLO service...")
        self.disable_detection()
        
        if self.model:
            del self.model
            self.model = None
        
        self.stats["model_loaded"] = False
        logger.info("YOLO service cleanup completed")

# 全局YOLO服务实例
yolo_service = YOLODetectionService() 