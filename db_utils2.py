import os
import win32com.client
import pythoncom
import threading
import calendar
from datetime import datetime, timedelta
import tkinter as tk
from tkinter import ttk, messagebox, simpledialog
from tkcalendar import Calendar
import os
import xml.etree.ElementTree as ET

db_path = r"D:\actest25\6.mdb"
    
# --- Database writing function ---
def write_work_time(employee_data):
    """
    插入一条记录到 Access，并返回 (ok, msg, new_id)
    """
    pythoncom.CoInitialize()
    access = None
    try:
        #db_path = r"D:\actest25\6.mdb"
        access = win32com.client.Dispatch("Access.Application")
        access.OpenCurrentDatabase(db_path)
        db = access.CurrentDb()

        # 构造 INSERT 语句（保持和你原来一致）
        sql = f"""INSERT INTO 元作業時間
          (従業員ｺｰﾄﾞ, 日付, 機種, 号機, 工場製番, 工事番号,
           ﾕﾆｯﾄ番号, 区分, 項目, 時間, 所属ｺｰﾄﾞ)
         VALUES
          ('{employee_data['employee_id']}', 
           #{employee_data['date']}#,
           {f"'{employee_data['model']}'" if employee_data['model'] else 'NULL'},
           {f"'{employee_data['number']}'" if employee_data['number'] else 'NULL'},
           {f"'{employee_data['factory_number']}'" if employee_data['factory_number'] else 'NULL'},
           {f"'{employee_data['project_number']}'" if employee_data['project_number'] else 'NULL'},
           {f"'{employee_data['unit_number']}'" if employee_data['unit_number'] else 'NULL'},
           '{employee_data['category']}',
           '{employee_data['item']}',
           {employee_data['time']},
           '{employee_data['department']}'
          )"""
        db.Execute(sql)

        # 取回新插入行的自增 ID
        rs = db.OpenRecordset("SELECT @@IDENTITY AS NewID")
        new_id = rs.Fields("NewID").Value

        return True, "写入数据库成功", new_id

    except Exception as e:
        return False, f"写入数据库时出错: {e}", None

    finally:
        if access:
            try:
                access.CloseCurrentDatabase()
                access.Quit()
            except: pass
        pythoncom.CoUninitialize()

def delete_work_time_by_id(entry_id: str):
    """
    根据 ID 从 Access 删除一条记录，并返回 (ok, msg)
    """
    pythoncom.CoInitialize()
    access = None
    try:
        # db_path is global
        access = win32com.client.Dispatch("Access.Application")
        access.OpenCurrentDatabase(db_path)
        db = access.CurrentDb()

        # 假设主键列名为 ID，且 entry_id 是字符串，如果它是数字，则不需要引号
        # 为安全起见，最好验证 entry_id 的格式或类型
        sql = f"DELETE FROM 元作業時間 WHERE ID = {entry_id}"
        
        db.Execute(sql)
        
        # 检查是否真的删除了记录 (可选, Execute不返回受影响的行数)
        # 如果需要确认，可能需要先SELECT COUNT(*) ... WHERE ID = entry_id
        # 然后执行删除，再SELECT COUNT(*), 但对于简单删除，通常不这么做

        return True, "レコード削除成功。"

    except Exception as e:
        return False, f"データベースからの削除中にエラー発生: {e}"

    finally:
        if access:
            try:
                access.CloseCurrentDatabase()
                access.Quit()
            except: pass
        pythoncom.CoUninitialize()

def update_work_time_by_id(entry_id: str, employee_data: dict):
    """
    根据 ID 更新 Access 中的记录，并返回 (ok, msg)
    
    Args:
        entry_id: 要更新的记录ID (字符串)
        employee_data: 包含更新数据的字典
    """
    pythoncom.CoInitialize()
    access = None
    try:
        # db_path is global
        access = win32com.client.Dispatch("Access.Application")
        access.OpenCurrentDatabase(db_path)
        db = access.CurrentDb()

        # 构造 UPDATE 语句，参考Access VBA的格式
        sql = f"""UPDATE 元作業時間 SET 
            従業員ｺｰﾄﾞ = '{employee_data['employee_id']}',
            日付 = #{employee_data['date']}#,
            機種 = {f"'{employee_data['model']}'" if employee_data['model'] else 'NULL'},
            号機 = {f"'{employee_data['number']}'" if employee_data['number'] else 'NULL'},
            工場製番 = {f"'{employee_data['factory_number']}'" if employee_data['factory_number'] else 'NULL'},
            工事番号 = {f"'{employee_data['project_number']}'" if employee_data['project_number'] else 'NULL'},
            ﾕﾆｯﾄ番号 = {f"'{employee_data['unit_number']}'" if employee_data['unit_number'] else 'NULL'},
            区分 = '{employee_data['category']}',
            項目 = '{employee_data['item']}',
            時間 = {employee_data['time']},
            所属ｺｰﾄﾞ = '{employee_data['department']}'
            WHERE ID = {entry_id}"""
        
        db.Execute(sql)

        return True, "レコード更新成功。"

    except Exception as e:
        return False, f"データベースの更新中にエラー発生: {e}"

    finally:
        if access:
            try:
                access.CloseCurrentDatabase()
                access.Quit()
            except: pass
        pythoncom.CoUninitialize()

def update_specific_record():
    """
    更新ID为602610的记录示例
    """
    # 准备更新数据
    update_data = {
        'employee_id': '215829',
        'date': '2025/06/26',
        'model': '',  # 空字符串
        'number': '',  # 空字符串
        'factory_number': '',  # 空字符串
        'project_number': '24585',
        'unit_number': '',  # 空字符串
        'category': '3',
        'item': '7',
        'time': 8,
        'department': '131'
    }
    
    # 执行更新
    success, message = update_work_time_by_id('602610', update_data)
    
    if success:
        print(f"更新成功: {message}")
    else:
        print(f"更新失败: {message}")
    
    return success, message

# 测试函数
def test_all_operations():
    """
    测试所有数据库操作：插入、更新、删除
    """
    print("=== 数据库操作测试 ===")
    
    # 1. 测试插入
    print("\n1. 测试插入操作...")
    test_data = {
        'employee_id': '215829',
        'date': '2025/06/26',
        'model': 'TEST_MODEL',
        'number': 'TEST_NUMBER',
        'factory_number': 'TEST_FACTORY',
        'project_number': '24585',
        'unit_number': 'TEST_UNIT',
        'category': '3',
        'item': '7',
        'time': 8,
        'department': '131'
    }
    
    success, msg, new_id = write_work_time(test_data)
    if success:
        print(f"插入成功，新ID: {new_id}")
        test_id = str(new_id)
    else:
        print(f"插入失败: {msg}")
        return
    
    # 2. 测试更新
    print("\n2. 测试更新操作...")
    update_data = {
        'employee_id': '215829',
        'date': '2025/06/26',
        'model': '',  # 清空
        'number': '',  # 清空
        'factory_number': '',  # 清空
        'project_number': '24585',
        'unit_number': '',  # 清空
        'category': '3',
        'item': '7',
        'time': 8,
        'department': '131'
    }
    
    success, msg = update_work_time_by_id(test_id, update_data)
    if success:
        print(f"更新成功: {msg}")
    else:
        print(f"更新失败: {msg}")
    
    # 3. 测试删除
    print("\n3. 测试删除操作...")
    success, msg = delete_work_time_by_id(test_id)
    if success:
        print(f"删除成功: {msg}")
    else:
        print(f"删除失败: {msg}")
    
    print("\n=== 测试完成 ===")

# 主程序入口
if __name__ == "__main__":
    print("数据库操作工具")
    print("1. 更新特定记录 (ID: 602610)")
    print("2. 运行完整测试")
    print("3. 退出")
    
    choice = input("请选择操作 (1-3): ")
    
    if choice == "1":
        update_specific_record()
    elif choice == "2":
        test_all_operations()
    elif choice == "3":
        print("退出程序")
    else:
        print("无效选择")