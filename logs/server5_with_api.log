2025-07-10 11:48:39,635 - INFO - 🔊 f1监听器服务初始化完成
2025-07-10 11:48:39,636 - INFO - 📡 Server6客户端初始化 (新版): http://192.168.3.93:8019
2025-07-10 11:48:39,636 - INFO - 📤 f2推送回写引擎初始化完成 (修复版本: 直接轮询PostgreSQL)
2025-07-10 11:48:39,636 - INFO - 📡 Server6客户端初始化 (新版): http://192.168.3.93:8019
2025-07-10 11:48:39,637 - INFO - 📥 f3数据拉取器初始化完成 (Staging & UPSERT 模式)
2025-07-10 11:48:39,637 - INFO - 🎮 f4操作处理器初始化完成
2025-07-10 11:48:39,645 - INFO - Redis连接成功
2025-07-10 11:48:39,646 - INFO - Redis连接成功
2025-07-10 11:48:39,646 - INFO - Redis连接成功
2025-07-10 11:48:39,654 - INFO - ✅ MongoDB连接成功
2025-07-10 11:48:39,655 - INFO - ✅ MongoDB连接成功
2025-07-10 11:48:39,655 - INFO - ✅ MongoDB连接成功
2025-07-10 11:48:39,905 - INFO - ✅ PostgreSQL连接池创建成功
2025-07-10 11:48:39,906 - INFO - Redis连接成功
2025-07-10 11:48:39,906 - INFO - ✅ f3数据拉取服务启动成功，预定执行时间: ['02:00', '03:00']
2025-07-10 11:48:39,906 - INFO - 🚀 数据拉取调度器启动，预定运行时间: ['02:00', '03:00']
2025-07-10 11:48:39,906 - INFO - 下一次f3/f5同步任务将在 2025-07-11 02:00:00 执行，等待 51080 秒...
2025-07-10 11:48:39,999 - INFO - ✅ PostgreSQL连接池创建成功
2025-07-10 11:48:39,999 - INFO - ✅ f1监听器服务启动成功
2025-07-10 11:48:40,003 - INFO - ✅ PostgreSQL连接池创建成功
2025-07-10 11:48:40,004 - INFO - ✅ f2推送回写服务启动成功，工作线程数: 4
2025-07-10 11:48:40,004 - INFO - 🚀 推送工作线程启动: worker_1
2025-07-10 11:48:40,004 - INFO - 🚀 推送工作线程启动: worker_2
2025-07-10 11:48:40,004 - INFO - 🚀 推送工作线程启动: worker_3
2025-07-10 11:48:40,004 - INFO - 🚀 推送工作线程启动: worker_4
2025-07-10 11:48:40,023 - INFO - ✅ 分区创建成功: entries_2025_07
2025-07-10 11:48:40,027 - INFO - ✅ PostgreSQL连接池创建成功
2025-07-10 11:48:40,027 - INFO - ✅ f4操作处理服务启动成功
2025-07-10 11:48:40,027 - INFO - 🔌 推送工作线程停止: worker_4
2025-07-10 11:48:40,027 - INFO - 🔌 推送工作线程停止: worker_3
2025-07-10 11:48:40,029 - INFO - 🔌 推送工作线程停止: worker_2
2025-07-10 11:48:40,227 - INFO - 🔌 推送工作线程停止: worker_1
