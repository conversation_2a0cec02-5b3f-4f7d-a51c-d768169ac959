INFO:     Will watch for changes in these directories: ['/home/<USER>/Documents/testF/2025/ser25/server0613/MySuiteDatebase2402newuigood-yolo5-3/server2/app']
INFO:     Uvicorn running on http://0.0.0.0:8005 (Press CTRL+C to quit)
INFO:     Started reloader process [2455612] using WatchFiles
============================================================
🚀 Starting MySuite Chat Microservice
============================================================
📁 Working Directory: /home/<USER>/Documents/testF/2025/ser25/server0613/MySuiteDatebase2402newuigood-yolo5-3/server2
🌐 Service Port: 8005
📝 API Documentation: http://localhost:8005/docs
🔍 Health Check: http://localhost:8005/health
💬 WebSocket: ws://localhost:8005/ws/chat
============================================================
INFO:     Started server process [2455617]
INFO:     Waiting for application startup.
2025-07-15 13:18:02,558 - app.main - INFO - Starting MySuite Chat Microservice...
2025-07-15 13:18:02,744 - app.core.services.database_service - ERROR - Failed to connect to database: (sqlalchemy.dialects.postgresql.asyncpg.Error) <class 'asyncpg.exceptions.InvalidSchemaNameError'>: スキーマ"message"は存在しません
[SQL: 
CREATE TABLE message.chat_rooms (
	id SERIAL NOT NULL, 
	room_id VARCHAR(50) NOT NULL, 
	room_name VARCHAR(100) NOT NULL, 
	description TEXT, 
	is_private BOOLEAN, 
	is_active BOOLEAN, 
	max_members INTEGER, 
	created_at TIMESTAMP WITHOUT TIME ZONE, 
	updated_at TIMESTAMP WITHOUT TIME ZONE, 
	created_by VARCHAR(50), 
	PRIMARY KEY (id)
)

]
(Background on this error at: https://sqlalche.me/e/20/dbapi)
2025-07-15 13:18:02,744 - app.main - ERROR - Failed to initialize database: (sqlalchemy.dialects.postgresql.asyncpg.Error) <class 'asyncpg.exceptions.InvalidSchemaNameError'>: スキーマ"message"は存在しません
[SQL: 
CREATE TABLE message.chat_rooms (
	id SERIAL NOT NULL, 
	room_id VARCHAR(50) NOT NULL, 
	room_name VARCHAR(100) NOT NULL, 
	description TEXT, 
	is_private BOOLEAN, 
	is_active BOOLEAN, 
	max_members INTEGER, 
	created_at TIMESTAMP WITHOUT TIME ZONE, 
	updated_at TIMESTAMP WITHOUT TIME ZONE, 
	created_by VARCHAR(50), 
	PRIMARY KEY (id)
)

]
(Background on this error at: https://sqlalche.me/e/20/dbapi)
2025-07-15 13:18:02,746 - app.core.services.redis_service - INFO - Redis connected successfully at localhost:6379
2025-07-15 13:18:02,746 - app.main - INFO - Redis service initialized successfully
2025-07-15 13:18:02,746 - app.main - INFO - Message encryption service initialized successfully
2025-07-15 13:18:02,747 - app.main - INFO - File service initialized: 5 files, 0.02MB
INFO:     Application startup complete.
Configuration validated successfully
Upload directory: /home/<USER>/Documents/testF/2025/ser25/server0613/MySuiteDatebase2402newuigood-yolo5-3/server2/uploads
Database URL: postgresql+asyncpg://postgres:pojiami0602@************:5432/imdb
INFO:     127.0.0.1:57518 - "GET /health HTTP/1.1" 200 OK
INFO:     127.0.0.1:57534 - "GET / HTTP/1.1" 200 OK
