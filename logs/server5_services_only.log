2025-07-10 13:02:46,228 - INFO - 🔊 f1监听器服务初始化完成
2025-07-10 13:02:46,228 - INFO - 📡 Server6客户端初始化 (新版): http://************:8019
2025-07-10 13:02:46,228 - INFO - 📤 f2推送回写引擎初始化完成 (修复版本: 直接轮询PostgreSQL)
2025-07-10 13:02:46,228 - INFO - 📡 Server6客户端初始化 (新版): http://************:8019
2025-07-10 13:02:46,229 - INFO - 📥 f3数据拉取器初始化完成 (Staging & UPSERT 模式)
2025-07-10 13:02:46,229 - INFO - 🎮 f4操作处理器初始化完成
2025-07-10 13:02:46,238 - INFO - Redis连接成功
2025-07-10 13:02:46,238 - INFO - Redis连接成功
2025-07-10 13:02:46,238 - INFO - Redis连接成功
2025-07-10 13:02:46,244 - INFO - ✅ MongoDB连接成功
2025-07-10 13:02:46,245 - INFO - ✅ MongoDB连接成功
2025-07-10 13:02:46,246 - INFO - ✅ MongoDB连接成功
2025-07-10 13:02:46,554 - INFO - ✅ PostgreSQL连接池创建成功
2025-07-10 13:02:46,555 - INFO - Redis连接成功
2025-07-10 13:02:46,556 - INFO - ✅ f3数据拉取服务启动成功，预定执行时间: ['02:00', '03:00']
2025-07-10 13:02:46,556 - INFO - 🚀 数据拉取调度器启动，预定运行时间: ['02:00', '03:00']
2025-07-10 13:02:46,556 - INFO - 下一次f3/f5同步任务将在 2025-07-11 02:00:00 执行，等待 46633 秒...
2025-07-10 13:02:46,594 - INFO - ✅ PostgreSQL连接池创建成功
2025-07-10 13:02:46,594 - INFO - ✅ f1监听器服务启动成功
2025-07-10 13:02:46,604 - INFO - ✅ 分区创建成功: entries_2025_07
2025-07-10 13:02:46,608 - INFO - ✅ 分区创建成功: entries_2025_08
2025-07-10 13:02:46,632 - INFO - ✅ PostgreSQL连接池创建成功
2025-07-10 13:02:46,632 - INFO - ✅ f2推送回写服务启动成功，工作线程数: 4
2025-07-10 13:02:46,633 - INFO - 🚀 推送工作线程启动: worker_1
2025-07-10 13:02:46,633 - INFO - 🚀 推送工作线程启动: worker_2
2025-07-10 13:02:46,633 - INFO - 🚀 推送工作线程启动: worker_3
2025-07-10 13:02:46,633 - INFO - 🚀 推送工作线程启动: worker_4
2025-07-10 13:02:46,672 - INFO - ✅ PostgreSQL连接池创建成功
2025-07-10 13:02:46,672 - INFO - ✅ f4操作处理服务启动成功
2025-07-10 13:02:46,672 - INFO - 🔌 推送工作线程停止: worker_3
2025-07-10 13:02:46,672 - INFO - 🔌 推送工作线程停止: worker_4
2025-07-10 13:02:46,673 - INFO - 🔌 推送工作线程停止: worker_1
2025-07-10 13:02:46,674 - INFO - 🔌 推送工作线程停止: worker_2
2025-07-10 13:02:55,047 - INFO - 🔌 MongoDB连接已关闭
2025-07-10 13:02:55,049 - INFO - 🔌 MongoDB连接已关闭
2025-07-10 13:02:55,051 - INFO - 🔌 MongoDB连接已关闭
2025-07-10 13:02:55,052 - INFO - 🔌 f1监听器服务已停止
2025-07-10 13:02:55,052 - INFO - 🔌 f2推送回写服务已停止
2025-07-10 13:02:55,052 - INFO - 🔌 f4操作处理服务已停止
2025-07-10 13:02:55,250 - INFO - 🔌 PostgreSQL连接已关闭
2025-07-10 13:02:55,250 - INFO - 🔌 PostgreSQL连接已关闭
2025-07-10 13:02:55,251 - INFO - 🔌 MongoDB连接已关闭
2025-07-10 13:02:55,251 - INFO - 🔌 PostgreSQL连接已关闭
2025-07-10 13:02:55,252 - INFO - 🔌 MongoDB连接已关闭
2025-07-10 13:02:55,252 - INFO - 🔌 PostgreSQL连接已关闭
2025-07-10 13:02:55,253 - INFO - 🔌 MongoDB连接已关闭
2025-07-10 13:02:55,253 - INFO - 🔌 Redis连接已关闭
2025-07-10 13:02:55,253 - INFO - 🔌 Redis连接已关闭
2025-07-10 13:02:55,253 - INFO - 🔌 Redis连接已关闭
2025-07-10 13:02:55,253 - INFO - 🔌 f1监听器服务已停止
2025-07-10 13:02:55,253 - INFO - 🔌 f2推送回写服务已停止
2025-07-10 13:02:55,253 - INFO - 🔌 f4操作处理服务已停止
2025-07-15 11:30:27,416 - INFO - 🔊 f1监听器服务初始化完成
2025-07-15 11:30:27,416 - INFO - 📡 Server6客户端初始化 (新版): http://************:8019
2025-07-15 11:30:27,416 - INFO - 📤 f2推送回写引擎初始化完成 (修复版本: 直接轮询PostgreSQL)
2025-07-15 11:30:27,416 - INFO - 📡 Server6客户端初始化 (新版): http://************:8019
2025-07-15 11:30:27,417 - INFO - 📥 f3数据拉取器初始化完成 (Staging & UPSERT 模式)
2025-07-15 11:30:27,417 - INFO - 🎮 f4操作处理器初始化完成
2025-07-15 11:30:27,426 - INFO - Redis连接成功
2025-07-15 11:30:27,426 - INFO - Redis连接成功
2025-07-15 11:30:27,426 - INFO - Redis连接成功
2025-07-15 11:30:27,431 - INFO - ✅ MongoDB连接成功
2025-07-15 11:30:27,432 - INFO - ✅ MongoDB连接成功
2025-07-15 11:30:27,433 - INFO - ✅ MongoDB连接成功
2025-07-15 11:30:27,668 - INFO - ✅ PostgreSQL连接池创建成功
2025-07-15 11:30:27,669 - INFO - Redis连接成功
2025-07-15 11:30:27,669 - INFO - ✅ f3数据拉取服务启动成功，预定执行时间: ['02:00', '03:00']
2025-07-15 11:30:27,669 - INFO - 🚀 数据拉取调度器启动，预定运行时间: ['02:00', '03:00']
2025-07-15 11:30:27,669 - INFO - 下一次f3/f5同步任务将在 2025-07-16 02:00:00 执行，等待 52172 秒...
2025-07-15 11:30:27,707 - INFO - ✅ PostgreSQL连接池创建成功
2025-07-15 11:30:27,707 - INFO - ✅ f1监听器服务启动成功
2025-07-15 11:30:27,711 - INFO - ✅ PostgreSQL连接池创建成功
2025-07-15 11:30:27,711 - INFO - ✅ f2推送回写服务启动成功，工作线程数: 4
2025-07-15 11:30:27,711 - INFO - 🚀 推送工作线程启动: worker_1
2025-07-15 11:30:27,712 - INFO - 🚀 推送工作线程启动: worker_2
2025-07-15 11:30:27,712 - INFO - 🚀 推送工作线程启动: worker_3
2025-07-15 11:30:27,712 - INFO - 🚀 推送工作线程启动: worker_4
2025-07-15 11:30:27,715 - INFO - ✅ 分区创建成功: entries_2025_07
2025-07-15 11:30:27,718 - INFO - 📋 2025/07/03 + 16:10 + 处理队列项: INSERT - entry_id=145170 - queue_id=128 - 重试次数=0
2025-07-15 11:30:27,720 - INFO - ✅ 分区创建成功: entries_2025_08
2025-07-15 11:30:27,741 - INFO - ✅ PostgreSQL连接池创建成功
2025-07-15 11:30:27,741 - INFO - ✅ f4操作处理服务启动成功
2025-07-15 11:30:27,848 - INFO - 📡 开始监听频道: push_job
2025-07-15 11:30:27,849 - INFO - 📡 开始监听频道: partition_check
2025-07-15 11:30:27,850 - INFO - 🔄 执行INSERT操作: entry_id=145170, employee_id=215829
2025-07-15 11:30:27,850 - INFO - 📤 发送到Server6的数据: {'従業員ｺｰﾄﾞ': '215829', '日付': '2025-07-15', '機種': 'NULL', '号機': 'NULL', '工場製番': 'NULL', '工事番号': 'NULL', 'ﾕﾆｯﾄ番号': 'NULL', '区分': '2', '項目': '2', '時間': 2.0, '所属ｺｰﾄﾞ': '131'}
2025-07-15 11:30:27,850 - INFO - AIOHTTP session for Server6 created.
2025-07-15 11:30:27,851 - INFO - 📡 开始监听频道: sync_trigger
2025-07-15 11:30:32,523 - INFO - ✅ INSERT完成: entry_id=145170 -> external_id=603671
2025-07-15 11:31:15,830 - INFO - 📋 2025/07/03 + 16:10 + 处理队列项: INSERT - entry_id=145171 - queue_id=129 - 重试次数=0
2025-07-15 11:31:15,922 - INFO - 🔄 执行INSERT操作: entry_id=145171, employee_id=215829
2025-07-15 11:31:15,922 - INFO - 📤 发送到Server6的数据: {'従業員ｺｰﾄﾞ': '215829', '日付': '2025-07-15', '機種': 'NULL', '号機': 'NULL', '工場製番': 'NULL', '工事番号': 'NULL', 'ﾕﾆｯﾄ番号': 'NULL', '区分': '3', '項目': '3', '時間': 0.8, '所属ｺｰﾄﾞ': '131'}
2025-07-15 11:31:20,246 - INFO - ✅ INSERT完成: entry_id=145171 -> external_id=603672
2025-07-15 11:31:54,322 - INFO - 📋 2025/07/03 + 16:10 + 处理队列项: UPDATE - entry_id=145169 - queue_id=130 - 重试次数=0
2025-07-15 11:31:54,347 - ERROR - ❌ UPDATE操作失败: 找不到要更新的entry记录: entry_id=145169
Traceback (most recent call last):
  File "/home/<USER>/Documents/testF/2025/ser25/server0613/MySuiteDatebase2402newuigood-yolo5-3/server5/app/services/f2_push_writer.py", line 393, in _handle_update_operation_in_transaction
    raise ValueError(f"找不到要更新的entry记录: entry_id={entry_id}")
ValueError: 找不到要更新的entry记录: entry_id=145169
2025-07-15 11:31:54,348 - ERROR - ❌ 2025/07/03 + 16:10 + 队列项处理失败 (第1次): 找不到要更新的entry记录: entry_id=145169
2025-07-15 11:31:54,350 - WARNING - ⚠️ 2025/07/03 + 16:10 + 队列项将在下次循环重试: queue_id=130, retry_count=1
2025-07-15 11:31:54,355 - INFO - 📋 2025/07/03 + 16:10 + 处理队列项: UPDATE - entry_id=145169 - queue_id=130 - 重试次数=1
2025-07-15 11:31:54,371 - ERROR - ❌ UPDATE操作失败: 找不到要更新的entry记录: entry_id=145169
Traceback (most recent call last):
  File "/home/<USER>/Documents/testF/2025/ser25/server0613/MySuiteDatebase2402newuigood-yolo5-3/server5/app/services/f2_push_writer.py", line 393, in _handle_update_operation_in_transaction
    raise ValueError(f"找不到要更新的entry记录: entry_id={entry_id}")
ValueError: 找不到要更新的entry记录: entry_id=145169
2025-07-15 11:31:54,372 - ERROR - ❌ 2025/07/03 + 16:10 + 队列项处理失败 (第2次): 找不到要更新的entry记录: entry_id=145169
2025-07-15 11:31:54,373 - WARNING - ⚠️ 2025/07/03 + 16:10 + 队列项将在下次循环重试: queue_id=130, retry_count=2
2025-07-15 11:31:54,377 - INFO - 📋 2025/07/03 + 16:10 + 处理队列项: UPDATE - entry_id=145169 - queue_id=130 - 重试次数=2
2025-07-15 11:31:54,392 - ERROR - ❌ UPDATE操作失败: 找不到要更新的entry记录: entry_id=145169
Traceback (most recent call last):
  File "/home/<USER>/Documents/testF/2025/ser25/server0613/MySuiteDatebase2402newuigood-yolo5-3/server5/app/services/f2_push_writer.py", line 393, in _handle_update_operation_in_transaction
    raise ValueError(f"找不到要更新的entry记录: entry_id={entry_id}")
ValueError: 找不到要更新的entry记录: entry_id=145169
2025-07-15 11:31:54,392 - ERROR - ❌ 2025/07/03 + 16:10 + 队列项处理失败 (第3次): 找不到要更新的entry记录: entry_id=145169
2025-07-15 11:31:54,393 - WARNING - ⚠️ 2025/07/03 + 16:10 + 队列项将在下次循环重试: queue_id=130, retry_count=3
2025-07-15 11:31:54,397 - INFO - 📋 2025/07/03 + 16:10 + 处理队列项: UPDATE - entry_id=145169 - queue_id=130 - 重试次数=3
2025-07-15 11:31:54,413 - ERROR - ❌ UPDATE操作失败: 找不到要更新的entry记录: entry_id=145169
Traceback (most recent call last):
  File "/home/<USER>/Documents/testF/2025/ser25/server0613/MySuiteDatebase2402newuigood-yolo5-3/server5/app/services/f2_push_writer.py", line 393, in _handle_update_operation_in_transaction
    raise ValueError(f"找不到要更新的entry记录: entry_id={entry_id}")
ValueError: 找不到要更新的entry记录: entry_id=145169
2025-07-15 11:31:54,413 - ERROR - ❌ 2025/07/03 + 16:10 + 队列项处理失败 (第4次): 找不到要更新的entry记录: entry_id=145169
2025-07-15 11:31:54,414 - WARNING - ⚠️ 2025/07/03 + 16:10 + 队列项将在下次循环重试: queue_id=130, retry_count=4
2025-07-15 11:31:54,419 - INFO - 📋 2025/07/03 + 16:10 + 处理队列项: UPDATE - entry_id=145169 - queue_id=130 - 重试次数=4
2025-07-15 11:31:54,434 - ERROR - ❌ UPDATE操作失败: 找不到要更新的entry记录: entry_id=145169
Traceback (most recent call last):
  File "/home/<USER>/Documents/testF/2025/ser25/server0613/MySuiteDatebase2402newuigood-yolo5-3/server5/app/services/f2_push_writer.py", line 393, in _handle_update_operation_in_transaction
    raise ValueError(f"找不到要更新的entry记录: entry_id={entry_id}")
ValueError: 找不到要更新的entry记录: entry_id=145169
2025-07-15 11:31:54,434 - ERROR - ❌ 2025/07/03 + 16:10 + 队列项处理失败 (第5次): 找不到要更新的entry记录: entry_id=145169
2025-07-15 11:31:54,435 - WARNING - ⚠️ 2025/07/03 + 16:10 + 队列项将在下次循环重试: queue_id=130, retry_count=5
2025-07-15 11:31:54,439 - INFO - 📋 2025/07/03 + 16:10 + 处理队列项: UPDATE - entry_id=145169 - queue_id=130 - 重试次数=5
2025-07-15 11:31:54,451 - ERROR - ❌ UPDATE操作失败: 找不到要更新的entry记录: entry_id=145169
Traceback (most recent call last):
  File "/home/<USER>/Documents/testF/2025/ser25/server0613/MySuiteDatebase2402newuigood-yolo5-3/server5/app/services/f2_push_writer.py", line 393, in _handle_update_operation_in_transaction
    raise ValueError(f"找不到要更新的entry记录: entry_id={entry_id}")
ValueError: 找不到要更新的entry记录: entry_id=145169
2025-07-15 11:31:54,451 - ERROR - ❌ 2025/07/03 + 16:10 + 队列项处理失败 (第6次): 找不到要更新的entry记录: entry_id=145169
2025-07-15 11:31:54,452 - WARNING - ⚠️ 2025/07/03 + 16:10 + 队列项将在下次循环重试: queue_id=130, retry_count=6
2025-07-15 11:31:54,456 - INFO - 📋 2025/07/03 + 16:10 + 处理队列项: UPDATE - entry_id=145169 - queue_id=130 - 重试次数=6
2025-07-15 11:31:54,468 - ERROR - ❌ UPDATE操作失败: 找不到要更新的entry记录: entry_id=145169
Traceback (most recent call last):
  File "/home/<USER>/Documents/testF/2025/ser25/server0613/MySuiteDatebase2402newuigood-yolo5-3/server5/app/services/f2_push_writer.py", line 393, in _handle_update_operation_in_transaction
    raise ValueError(f"找不到要更新的entry记录: entry_id={entry_id}")
ValueError: 找不到要更新的entry记录: entry_id=145169
2025-07-15 11:31:54,468 - ERROR - ❌ 2025/07/03 + 16:10 + 队列项处理失败 (第7次): 找不到要更新的entry记录: entry_id=145169
2025-07-15 11:31:54,469 - WARNING - ⚠️ 2025/07/03 + 16:10 + 队列项将在下次循环重试: queue_id=130, retry_count=7
2025-07-15 11:31:54,473 - INFO - 📋 2025/07/03 + 16:10 + 处理队列项: UPDATE - entry_id=145169 - queue_id=130 - 重试次数=7
2025-07-15 11:31:54,485 - ERROR - ❌ UPDATE操作失败: 找不到要更新的entry记录: entry_id=145169
Traceback (most recent call last):
  File "/home/<USER>/Documents/testF/2025/ser25/server0613/MySuiteDatebase2402newuigood-yolo5-3/server5/app/services/f2_push_writer.py", line 393, in _handle_update_operation_in_transaction
    raise ValueError(f"找不到要更新的entry记录: entry_id={entry_id}")
ValueError: 找不到要更新的entry记录: entry_id=145169
2025-07-15 11:31:54,485 - ERROR - ❌ 2025/07/03 + 16:10 + 队列项处理失败 (第8次): 找不到要更新的entry记录: entry_id=145169
2025-07-15 11:31:54,486 - WARNING - ⚠️ 2025/07/03 + 16:10 + 队列项将在下次循环重试: queue_id=130, retry_count=8
2025-07-15 11:31:54,490 - INFO - 📋 2025/07/03 + 16:10 + 处理队列项: UPDATE - entry_id=145169 - queue_id=130 - 重试次数=8
2025-07-15 11:31:54,504 - ERROR - ❌ UPDATE操作失败: 找不到要更新的entry记录: entry_id=145169
Traceback (most recent call last):
  File "/home/<USER>/Documents/testF/2025/ser25/server0613/MySuiteDatebase2402newuigood-yolo5-3/server5/app/services/f2_push_writer.py", line 393, in _handle_update_operation_in_transaction
    raise ValueError(f"找不到要更新的entry记录: entry_id={entry_id}")
ValueError: 找不到要更新的entry记录: entry_id=145169
2025-07-15 11:31:54,504 - ERROR - ❌ 2025/07/03 + 16:10 + 队列项处理失败 (第9次): 找不到要更新的entry记录: entry_id=145169
2025-07-15 11:31:54,505 - WARNING - ⚠️ 2025/07/03 + 16:10 + 队列项将在下次循环重试: queue_id=130, retry_count=9
2025-07-15 11:31:54,509 - INFO - 📋 2025/07/03 + 16:10 + 处理队列项: UPDATE - entry_id=145169 - queue_id=130 - 重试次数=9
2025-07-15 11:31:54,521 - ERROR - ❌ UPDATE操作失败: 找不到要更新的entry记录: entry_id=145169
Traceback (most recent call last):
  File "/home/<USER>/Documents/testF/2025/ser25/server0613/MySuiteDatebase2402newuigood-yolo5-3/server5/app/services/f2_push_writer.py", line 393, in _handle_update_operation_in_transaction
    raise ValueError(f"找不到要更新的entry记录: entry_id={entry_id}")
ValueError: 找不到要更新的entry记录: entry_id=145169
2025-07-15 11:31:54,521 - ERROR - ❌ 2025/07/03 + 16:10 + 队列项处理失败 (第10次): 找不到要更新的entry记录: entry_id=145169
2025-07-15 11:31:54,521 - ERROR - 🚨 队列项超过最大重试次数，执行清理: queue_id=130, entry_id=145169
2025-07-15 11:31:54,539 - WARNING - ✅ 队列项清理完成: queue_id=130
2025-07-15 11:31:54,543 - INFO - 📋 2025/07/03 + 16:10 + 处理队列项: DELETE - entry_id=145169 - queue_id=131 - 重试次数=0
2025-07-15 11:31:54,544 - INFO - 🔄 执行DELETE操作: entry_id=145169, external_id=603668
2025-07-15 11:31:59,599 - INFO - ✅ DELETE完成: entry_id=145169, external_id=603668
2025-07-15 11:31:59,601 - INFO - ℹ️ DELETE操作跳过f6同步: entry_id=145169 (记录已删除)
2025-07-15 11:33:02,885 - INFO - 📋 2025/07/03 + 16:10 + 处理队列项: UPDATE - entry_id=145170 - queue_id=132 - 重试次数=0
2025-07-15 11:33:02,900 - ERROR - ❌ UPDATE操作失败: 找不到要更新的entry记录: entry_id=145170
Traceback (most recent call last):
  File "/home/<USER>/Documents/testF/2025/ser25/server0613/MySuiteDatebase2402newuigood-yolo5-3/server5/app/services/f2_push_writer.py", line 393, in _handle_update_operation_in_transaction
    raise ValueError(f"找不到要更新的entry记录: entry_id={entry_id}")
ValueError: 找不到要更新的entry记录: entry_id=145170
2025-07-15 11:33:02,901 - ERROR - ❌ 2025/07/03 + 16:10 + 队列项处理失败 (第1次): 找不到要更新的entry记录: entry_id=145170
2025-07-15 11:33:02,903 - WARNING - ⚠️ 2025/07/03 + 16:10 + 队列项将在下次循环重试: queue_id=132, retry_count=1
2025-07-15 11:33:02,908 - INFO - 📋 2025/07/03 + 16:10 + 处理队列项: UPDATE - entry_id=145170 - queue_id=132 - 重试次数=1
2025-07-15 11:33:02,922 - ERROR - ❌ UPDATE操作失败: 找不到要更新的entry记录: entry_id=145170
Traceback (most recent call last):
  File "/home/<USER>/Documents/testF/2025/ser25/server0613/MySuiteDatebase2402newuigood-yolo5-3/server5/app/services/f2_push_writer.py", line 393, in _handle_update_operation_in_transaction
    raise ValueError(f"找不到要更新的entry记录: entry_id={entry_id}")
ValueError: 找不到要更新的entry记录: entry_id=145170
2025-07-15 11:33:02,922 - ERROR - ❌ 2025/07/03 + 16:10 + 队列项处理失败 (第2次): 找不到要更新的entry记录: entry_id=145170
2025-07-15 11:33:02,923 - WARNING - ⚠️ 2025/07/03 + 16:10 + 队列项将在下次循环重试: queue_id=132, retry_count=2
2025-07-15 11:33:02,928 - INFO - 📋 2025/07/03 + 16:10 + 处理队列项: UPDATE - entry_id=145170 - queue_id=132 - 重试次数=2
2025-07-15 11:33:02,943 - ERROR - ❌ UPDATE操作失败: 找不到要更新的entry记录: entry_id=145170
Traceback (most recent call last):
  File "/home/<USER>/Documents/testF/2025/ser25/server0613/MySuiteDatebase2402newuigood-yolo5-3/server5/app/services/f2_push_writer.py", line 393, in _handle_update_operation_in_transaction
    raise ValueError(f"找不到要更新的entry记录: entry_id={entry_id}")
ValueError: 找不到要更新的entry记录: entry_id=145170
2025-07-15 11:33:02,943 - ERROR - ❌ 2025/07/03 + 16:10 + 队列项处理失败 (第3次): 找不到要更新的entry记录: entry_id=145170
2025-07-15 11:33:02,944 - WARNING - ⚠️ 2025/07/03 + 16:10 + 队列项将在下次循环重试: queue_id=132, retry_count=3
2025-07-15 11:33:02,949 - INFO - 📋 2025/07/03 + 16:10 + 处理队列项: UPDATE - entry_id=145170 - queue_id=132 - 重试次数=3
2025-07-15 11:33:02,964 - ERROR - ❌ UPDATE操作失败: 找不到要更新的entry记录: entry_id=145170
Traceback (most recent call last):
  File "/home/<USER>/Documents/testF/2025/ser25/server0613/MySuiteDatebase2402newuigood-yolo5-3/server5/app/services/f2_push_writer.py", line 393, in _handle_update_operation_in_transaction
    raise ValueError(f"找不到要更新的entry记录: entry_id={entry_id}")
ValueError: 找不到要更新的entry记录: entry_id=145170
2025-07-15 11:33:02,964 - ERROR - ❌ 2025/07/03 + 16:10 + 队列项处理失败 (第4次): 找不到要更新的entry记录: entry_id=145170
2025-07-15 11:33:02,965 - WARNING - ⚠️ 2025/07/03 + 16:10 + 队列项将在下次循环重试: queue_id=132, retry_count=4
2025-07-15 11:33:02,970 - INFO - 📋 2025/07/03 + 16:10 + 处理队列项: UPDATE - entry_id=145170 - queue_id=132 - 重试次数=4
2025-07-15 11:33:02,985 - ERROR - ❌ UPDATE操作失败: 找不到要更新的entry记录: entry_id=145170
Traceback (most recent call last):
  File "/home/<USER>/Documents/testF/2025/ser25/server0613/MySuiteDatebase2402newuigood-yolo5-3/server5/app/services/f2_push_writer.py", line 393, in _handle_update_operation_in_transaction
    raise ValueError(f"找不到要更新的entry记录: entry_id={entry_id}")
ValueError: 找不到要更新的entry记录: entry_id=145170
2025-07-15 11:33:02,985 - ERROR - ❌ 2025/07/03 + 16:10 + 队列项处理失败 (第5次): 找不到要更新的entry记录: entry_id=145170
2025-07-15 11:33:02,986 - WARNING - ⚠️ 2025/07/03 + 16:10 + 队列项将在下次循环重试: queue_id=132, retry_count=5
2025-07-15 11:33:02,990 - INFO - 📋 2025/07/03 + 16:10 + 处理队列项: UPDATE - entry_id=145170 - queue_id=132 - 重试次数=5
2025-07-15 11:33:03,002 - ERROR - ❌ UPDATE操作失败: 找不到要更新的entry记录: entry_id=145170
Traceback (most recent call last):
  File "/home/<USER>/Documents/testF/2025/ser25/server0613/MySuiteDatebase2402newuigood-yolo5-3/server5/app/services/f2_push_writer.py", line 393, in _handle_update_operation_in_transaction
    raise ValueError(f"找不到要更新的entry记录: entry_id={entry_id}")
ValueError: 找不到要更新的entry记录: entry_id=145170
2025-07-15 11:33:03,002 - ERROR - ❌ 2025/07/03 + 16:10 + 队列项处理失败 (第6次): 找不到要更新的entry记录: entry_id=145170
2025-07-15 11:33:03,003 - WARNING - ⚠️ 2025/07/03 + 16:10 + 队列项将在下次循环重试: queue_id=132, retry_count=6
2025-07-15 11:33:03,008 - INFO - 📋 2025/07/03 + 16:10 + 处理队列项: UPDATE - entry_id=145170 - queue_id=132 - 重试次数=6
2025-07-15 11:33:03,020 - ERROR - ❌ UPDATE操作失败: 找不到要更新的entry记录: entry_id=145170
Traceback (most recent call last):
  File "/home/<USER>/Documents/testF/2025/ser25/server0613/MySuiteDatebase2402newuigood-yolo5-3/server5/app/services/f2_push_writer.py", line 393, in _handle_update_operation_in_transaction
    raise ValueError(f"找不到要更新的entry记录: entry_id={entry_id}")
ValueError: 找不到要更新的entry记录: entry_id=145170
2025-07-15 11:33:03,020 - ERROR - ❌ 2025/07/03 + 16:10 + 队列项处理失败 (第7次): 找不到要更新的entry记录: entry_id=145170
2025-07-15 11:33:03,021 - WARNING - ⚠️ 2025/07/03 + 16:10 + 队列项将在下次循环重试: queue_id=132, retry_count=7
2025-07-15 11:33:03,025 - INFO - 📋 2025/07/03 + 16:10 + 处理队列项: UPDATE - entry_id=145170 - queue_id=132 - 重试次数=7
2025-07-15 11:33:03,037 - ERROR - ❌ UPDATE操作失败: 找不到要更新的entry记录: entry_id=145170
Traceback (most recent call last):
  File "/home/<USER>/Documents/testF/2025/ser25/server0613/MySuiteDatebase2402newuigood-yolo5-3/server5/app/services/f2_push_writer.py", line 393, in _handle_update_operation_in_transaction
    raise ValueError(f"找不到要更新的entry记录: entry_id={entry_id}")
ValueError: 找不到要更新的entry记录: entry_id=145170
2025-07-15 11:33:03,037 - ERROR - ❌ 2025/07/03 + 16:10 + 队列项处理失败 (第8次): 找不到要更新的entry记录: entry_id=145170
2025-07-15 11:33:03,038 - WARNING - ⚠️ 2025/07/03 + 16:10 + 队列项将在下次循环重试: queue_id=132, retry_count=8
2025-07-15 11:33:03,043 - INFO - 📋 2025/07/03 + 16:10 + 处理队列项: UPDATE - entry_id=145170 - queue_id=132 - 重试次数=8
2025-07-15 11:33:03,056 - ERROR - ❌ UPDATE操作失败: 找不到要更新的entry记录: entry_id=145170
Traceback (most recent call last):
  File "/home/<USER>/Documents/testF/2025/ser25/server0613/MySuiteDatebase2402newuigood-yolo5-3/server5/app/services/f2_push_writer.py", line 393, in _handle_update_operation_in_transaction
    raise ValueError(f"找不到要更新的entry记录: entry_id={entry_id}")
ValueError: 找不到要更新的entry记录: entry_id=145170
2025-07-15 11:33:03,056 - ERROR - ❌ 2025/07/03 + 16:10 + 队列项处理失败 (第9次): 找不到要更新的entry记录: entry_id=145170
2025-07-15 11:33:03,057 - WARNING - ⚠️ 2025/07/03 + 16:10 + 队列项将在下次循环重试: queue_id=132, retry_count=9
2025-07-15 11:33:03,062 - INFO - 📋 2025/07/03 + 16:10 + 处理队列项: UPDATE - entry_id=145170 - queue_id=132 - 重试次数=9
2025-07-15 11:33:03,074 - ERROR - ❌ UPDATE操作失败: 找不到要更新的entry记录: entry_id=145170
Traceback (most recent call last):
  File "/home/<USER>/Documents/testF/2025/ser25/server0613/MySuiteDatebase2402newuigood-yolo5-3/server5/app/services/f2_push_writer.py", line 393, in _handle_update_operation_in_transaction
    raise ValueError(f"找不到要更新的entry记录: entry_id={entry_id}")
ValueError: 找不到要更新的entry记录: entry_id=145170
2025-07-15 11:33:03,074 - ERROR - ❌ 2025/07/03 + 16:10 + 队列项处理失败 (第10次): 找不到要更新的entry记录: entry_id=145170
2025-07-15 11:33:03,075 - ERROR - 🚨 队列项超过最大重试次数，执行清理: queue_id=132, entry_id=145170
2025-07-15 11:33:03,092 - WARNING - ✅ 队列项清理完成: queue_id=132
2025-07-15 11:33:03,097 - INFO - 📋 2025/07/03 + 16:10 + 处理队列项: DELETE - entry_id=145170 - queue_id=133 - 重试次数=0
2025-07-15 11:33:03,097 - INFO - 🔄 执行DELETE操作: entry_id=145170, external_id=603671
2025-07-15 11:33:07,203 - INFO - ✅ DELETE完成: entry_id=145170, external_id=603671
2025-07-15 11:33:07,205 - INFO - ℹ️ DELETE操作跳过f6同步: entry_id=145170 (记录已删除)
2025-07-15 11:33:40,308 - INFO - 📋 2025/07/03 + 16:10 + 处理队列项: UPDATE - entry_id=145171 - queue_id=134 - 重试次数=0
2025-07-15 11:33:40,329 - INFO - 📋 2025/07/03 + 16:10 + 处理队列项: DELETE - entry_id=145171 - queue_id=135 - 重试次数=0
2025-07-15 11:33:40,331 - INFO - 🔄 执行DELETE操作: entry_id=145171, external_id=603672
2025-07-15 11:33:40,415 - ERROR - ❌ UPDATE操作失败: 找不到要更新的entry记录: entry_id=145171
Traceback (most recent call last):
  File "/home/<USER>/Documents/testF/2025/ser25/server0613/MySuiteDatebase2402newuigood-yolo5-3/server5/app/services/f2_push_writer.py", line 393, in _handle_update_operation_in_transaction
    raise ValueError(f"找不到要更新的entry记录: entry_id={entry_id}")
ValueError: 找不到要更新的entry记录: entry_id=145171
2025-07-15 11:33:40,415 - ERROR - ❌ 2025/07/03 + 16:10 + 队列项处理失败 (第1次): 找不到要更新的entry记录: entry_id=145171
2025-07-15 11:33:40,417 - WARNING - ⚠️ 2025/07/03 + 16:10 + 队列项将在下次循环重试: queue_id=134, retry_count=1
2025-07-15 11:33:40,426 - INFO - 📋 2025/07/03 + 16:10 + 处理队列项: UPDATE - entry_id=145171 - queue_id=134 - 重试次数=1
2025-07-15 11:33:40,444 - ERROR - ❌ UPDATE操作失败: 找不到要更新的entry记录: entry_id=145171
Traceback (most recent call last):
  File "/home/<USER>/Documents/testF/2025/ser25/server0613/MySuiteDatebase2402newuigood-yolo5-3/server5/app/services/f2_push_writer.py", line 393, in _handle_update_operation_in_transaction
    raise ValueError(f"找不到要更新的entry记录: entry_id={entry_id}")
ValueError: 找不到要更新的entry记录: entry_id=145171
2025-07-15 11:33:40,445 - ERROR - ❌ 2025/07/03 + 16:10 + 队列项处理失败 (第2次): 找不到要更新的entry记录: entry_id=145171
2025-07-15 11:33:40,446 - WARNING - ⚠️ 2025/07/03 + 16:10 + 队列项将在下次循环重试: queue_id=134, retry_count=2
2025-07-15 11:33:40,450 - INFO - 📋 2025/07/03 + 16:10 + 处理队列项: UPDATE - entry_id=145171 - queue_id=134 - 重试次数=2
2025-07-15 11:33:40,466 - ERROR - ❌ UPDATE操作失败: 找不到要更新的entry记录: entry_id=145171
Traceback (most recent call last):
  File "/home/<USER>/Documents/testF/2025/ser25/server0613/MySuiteDatebase2402newuigood-yolo5-3/server5/app/services/f2_push_writer.py", line 393, in _handle_update_operation_in_transaction
    raise ValueError(f"找不到要更新的entry记录: entry_id={entry_id}")
ValueError: 找不到要更新的entry记录: entry_id=145171
2025-07-15 11:33:40,466 - ERROR - ❌ 2025/07/03 + 16:10 + 队列项处理失败 (第3次): 找不到要更新的entry记录: entry_id=145171
2025-07-15 11:33:40,467 - WARNING - ⚠️ 2025/07/03 + 16:10 + 队列项将在下次循环重试: queue_id=134, retry_count=3
2025-07-15 11:33:40,471 - INFO - 📋 2025/07/03 + 16:10 + 处理队列项: UPDATE - entry_id=145171 - queue_id=134 - 重试次数=3
2025-07-15 11:33:40,490 - ERROR - ❌ UPDATE操作失败: 找不到要更新的entry记录: entry_id=145171
Traceback (most recent call last):
  File "/home/<USER>/Documents/testF/2025/ser25/server0613/MySuiteDatebase2402newuigood-yolo5-3/server5/app/services/f2_push_writer.py", line 393, in _handle_update_operation_in_transaction
    raise ValueError(f"找不到要更新的entry记录: entry_id={entry_id}")
ValueError: 找不到要更新的entry记录: entry_id=145171
2025-07-15 11:33:40,491 - ERROR - ❌ 2025/07/03 + 16:10 + 队列项处理失败 (第4次): 找不到要更新的entry记录: entry_id=145171
2025-07-15 11:33:40,492 - WARNING - ⚠️ 2025/07/03 + 16:10 + 队列项将在下次循环重试: queue_id=134, retry_count=4
2025-07-15 11:33:40,496 - INFO - 📋 2025/07/03 + 16:10 + 处理队列项: UPDATE - entry_id=145171 - queue_id=134 - 重试次数=4
2025-07-15 11:33:40,523 - ERROR - ❌ UPDATE操作失败: 找不到要更新的entry记录: entry_id=145171
Traceback (most recent call last):
  File "/home/<USER>/Documents/testF/2025/ser25/server0613/MySuiteDatebase2402newuigood-yolo5-3/server5/app/services/f2_push_writer.py", line 393, in _handle_update_operation_in_transaction
    raise ValueError(f"找不到要更新的entry记录: entry_id={entry_id}")
ValueError: 找不到要更新的entry记录: entry_id=145171
2025-07-15 11:33:40,524 - ERROR - ❌ 2025/07/03 + 16:10 + 队列项处理失败 (第5次): 找不到要更新的entry记录: entry_id=145171
2025-07-15 11:33:40,525 - WARNING - ⚠️ 2025/07/03 + 16:10 + 队列项将在下次循环重试: queue_id=134, retry_count=5
2025-07-15 11:33:40,529 - INFO - 📋 2025/07/03 + 16:10 + 处理队列项: UPDATE - entry_id=145171 - queue_id=134 - 重试次数=5
2025-07-15 11:33:40,560 - ERROR - ❌ UPDATE操作失败: 找不到要更新的entry记录: entry_id=145171
Traceback (most recent call last):
  File "/home/<USER>/Documents/testF/2025/ser25/server0613/MySuiteDatebase2402newuigood-yolo5-3/server5/app/services/f2_push_writer.py", line 393, in _handle_update_operation_in_transaction
    raise ValueError(f"找不到要更新的entry记录: entry_id={entry_id}")
ValueError: 找不到要更新的entry记录: entry_id=145171
2025-07-15 11:33:40,560 - ERROR - ❌ 2025/07/03 + 16:10 + 队列项处理失败 (第6次): 找不到要更新的entry记录: entry_id=145171
2025-07-15 11:33:40,561 - WARNING - ⚠️ 2025/07/03 + 16:10 + 队列项将在下次循环重试: queue_id=134, retry_count=6
2025-07-15 11:33:40,565 - INFO - 📋 2025/07/03 + 16:10 + 处理队列项: UPDATE - entry_id=145171 - queue_id=134 - 重试次数=6
2025-07-15 11:33:40,584 - ERROR - ❌ UPDATE操作失败: 找不到要更新的entry记录: entry_id=145171
Traceback (most recent call last):
  File "/home/<USER>/Documents/testF/2025/ser25/server0613/MySuiteDatebase2402newuigood-yolo5-3/server5/app/services/f2_push_writer.py", line 393, in _handle_update_operation_in_transaction
    raise ValueError(f"找不到要更新的entry记录: entry_id={entry_id}")
ValueError: 找不到要更新的entry记录: entry_id=145171
2025-07-15 11:33:40,585 - ERROR - ❌ 2025/07/03 + 16:10 + 队列项处理失败 (第7次): 找不到要更新的entry记录: entry_id=145171
2025-07-15 11:33:40,586 - WARNING - ⚠️ 2025/07/03 + 16:10 + 队列项将在下次循环重试: queue_id=134, retry_count=7
2025-07-15 11:33:40,590 - INFO - 📋 2025/07/03 + 16:10 + 处理队列项: UPDATE - entry_id=145171 - queue_id=134 - 重试次数=7
2025-07-15 11:33:40,605 - ERROR - ❌ UPDATE操作失败: 找不到要更新的entry记录: entry_id=145171
Traceback (most recent call last):
  File "/home/<USER>/Documents/testF/2025/ser25/server0613/MySuiteDatebase2402newuigood-yolo5-3/server5/app/services/f2_push_writer.py", line 393, in _handle_update_operation_in_transaction
    raise ValueError(f"找不到要更新的entry记录: entry_id={entry_id}")
ValueError: 找不到要更新的entry记录: entry_id=145171
2025-07-15 11:33:40,606 - ERROR - ❌ 2025/07/03 + 16:10 + 队列项处理失败 (第8次): 找不到要更新的entry记录: entry_id=145171
2025-07-15 11:33:40,607 - WARNING - ⚠️ 2025/07/03 + 16:10 + 队列项将在下次循环重试: queue_id=134, retry_count=8
2025-07-15 11:33:40,611 - INFO - 📋 2025/07/03 + 16:10 + 处理队列项: UPDATE - entry_id=145171 - queue_id=134 - 重试次数=8
2025-07-15 11:33:40,624 - ERROR - ❌ UPDATE操作失败: 找不到要更新的entry记录: entry_id=145171
Traceback (most recent call last):
  File "/home/<USER>/Documents/testF/2025/ser25/server0613/MySuiteDatebase2402newuigood-yolo5-3/server5/app/services/f2_push_writer.py", line 393, in _handle_update_operation_in_transaction
    raise ValueError(f"找不到要更新的entry记录: entry_id={entry_id}")
ValueError: 找不到要更新的entry记录: entry_id=145171
2025-07-15 11:33:40,624 - ERROR - ❌ 2025/07/03 + 16:10 + 队列项处理失败 (第9次): 找不到要更新的entry记录: entry_id=145171
2025-07-15 11:33:40,625 - WARNING - ⚠️ 2025/07/03 + 16:10 + 队列项将在下次循环重试: queue_id=134, retry_count=9
2025-07-15 11:33:40,629 - INFO - 📋 2025/07/03 + 16:10 + 处理队列项: UPDATE - entry_id=145171 - queue_id=134 - 重试次数=9
2025-07-15 11:33:40,647 - ERROR - ❌ UPDATE操作失败: 找不到要更新的entry记录: entry_id=145171
Traceback (most recent call last):
  File "/home/<USER>/Documents/testF/2025/ser25/server0613/MySuiteDatebase2402newuigood-yolo5-3/server5/app/services/f2_push_writer.py", line 393, in _handle_update_operation_in_transaction
    raise ValueError(f"找不到要更新的entry记录: entry_id={entry_id}")
ValueError: 找不到要更新的entry记录: entry_id=145171
2025-07-15 11:33:40,647 - ERROR - ❌ 2025/07/03 + 16:10 + 队列项处理失败 (第10次): 找不到要更新的entry记录: entry_id=145171
2025-07-15 11:33:40,647 - ERROR - 🚨 队列项超过最大重试次数，执行清理: queue_id=134, entry_id=145171
2025-07-15 11:33:40,671 - WARNING - ✅ 队列项清理完成: queue_id=134
2025-07-15 11:33:44,133 - INFO - ✅ DELETE完成: entry_id=145171, external_id=603672
2025-07-15 11:33:44,134 - INFO - ℹ️ DELETE操作跳过f6同步: entry_id=145171 (记录已删除)
2025-07-15 11:34:42,783 - INFO - 监听任务被取消
2025-07-15 11:34:42,785 - INFO - 🔌 MongoDB连接已关闭
2025-07-15 11:34:42,786 - INFO - 🔌 MongoDB连接已关闭
2025-07-15 11:34:42,788 - INFO - 🔌 Redis连接已关闭
2025-07-15 11:34:42,788 - INFO - 🔌 Redis连接已关闭
2025-07-15 11:34:42,792 - INFO - 🔌 PostgreSQL连接已关闭
2025-07-15 11:34:42,792 - INFO - 🔌 f1监听器服务已停止
2025-07-15 11:34:42,793 - INFO - 🔌 PostgreSQL连接已关闭
2025-07-15 11:34:42,793 - INFO - 🔌 f4操作处理服务已停止
2025-07-15 11:34:42,795 - INFO - 🔌 PostgreSQL连接已关闭
2025-07-15 11:34:42,796 - INFO - 🔌 Redis连接已关闭
2025-07-15 11:34:42,796 - INFO - 🔌 f3数据拉取服务已停止
2025-07-15 11:34:42,885 - INFO - 🔌 推送工作线程停止: worker_1
2025-07-15 11:34:42,929 - INFO - 🔌 推送工作线程停止: worker_2
2025-07-15 11:34:42,949 - INFO - 🔌 推送工作线程停止: worker_3
2025-07-15 11:34:42,980 - INFO - 🔌 推送工作线程停止: worker_4
2025-07-15 11:34:42,982 - INFO - 🔌 MongoDB连接已关闭
2025-07-15 11:34:42,982 - INFO - AIOHTTP session for Server6 closed.
2025-07-15 11:34:42,982 - INFO - 🔌 Redis连接已关闭
2025-07-15 11:34:42,987 - INFO - 🔌 PostgreSQL连接已关闭
2025-07-15 11:34:42,987 - INFO - 🔌 f2推送回写服务已停止
2025-07-15 11:34:42,988 - ERROR - Server5微服务启动失败: asyncio.run() cannot be called from a running event loop
2025-07-15 11:34:43,480 - INFO - 🔌 PostgreSQL连接已关闭
2025-07-15 11:34:43,481 - INFO - 🔌 MongoDB连接已关闭
2025-07-15 11:34:43,481 - INFO - 🔌 PostgreSQL连接已关闭
2025-07-15 11:34:43,482 - INFO - 🔌 MongoDB连接已关闭
2025-07-15 11:34:43,482 - INFO - 🔌 PostgreSQL连接已关闭
2025-07-15 11:34:43,482 - INFO - 🔌 MongoDB连接已关闭
2025-07-15 11:34:43,483 - INFO - 🔌 Redis连接已关闭
2025-07-15 11:34:43,483 - INFO - 🔌 Redis连接已关闭
2025-07-15 11:34:43,483 - INFO - 🔌 Redis连接已关闭
2025-07-15 11:34:43,484 - INFO - 🔌 f1监听器服务已停止
2025-07-15 11:34:43,484 - INFO - 🔌 f2推送回写服务已停止
2025-07-15 11:34:43,484 - INFO - 🔌 f4操作处理服务已停止
2025-07-15 11:34:43,507 - ERROR - Task was destroyed but it is pending!
task: <Task pending name='Task-625' coro=<Connection._cancel() running at /home/<USER>/miniconda3/envs/my_suite_unified/lib/python3.12/site-packages/asyncpg/connection.py:1646> wait_for=<Future pending cb=[Task.task_wakeup()]>>
2025-07-15 11:34:44,727 - INFO - 🔊 f1监听器服务初始化完成
2025-07-15 11:34:44,727 - INFO - 📡 Server6客户端初始化 (新版): http://************:8019
2025-07-15 11:34:44,728 - INFO - 📤 f2推送回写引擎初始化完成 (修复版本: 直接轮询PostgreSQL)
2025-07-15 11:34:44,728 - INFO - 📡 Server6客户端初始化 (新版): http://************:8019
2025-07-15 11:34:44,729 - INFO - 📥 f3数据拉取器初始化完成 (Staging & UPSERT 模式)
2025-07-15 11:34:44,729 - INFO - 🎮 f4操作处理器初始化完成
2025-07-15 11:34:44,738 - INFO - Redis连接成功
2025-07-15 11:34:44,738 - INFO - Redis连接成功
2025-07-15 11:34:44,738 - INFO - Redis连接成功
2025-07-15 11:34:44,745 - INFO - ✅ MongoDB连接成功
2025-07-15 11:34:44,745 - INFO - ✅ MongoDB连接成功
2025-07-15 11:34:44,745 - INFO - ✅ MongoDB连接成功
2025-07-15 11:34:44,974 - INFO - ✅ PostgreSQL连接池创建成功
2025-07-15 11:34:44,975 - INFO - Redis连接成功
2025-07-15 11:34:44,975 - INFO - ✅ f3数据拉取服务启动成功，预定执行时间: ['02:00', '03:00']
2025-07-15 11:34:44,975 - INFO - 🚀 数据拉取调度器启动，预定运行时间: ['02:00', '03:00']
2025-07-15 11:34:44,975 - INFO - 下一次f3/f5同步任务将在 2025-07-16 02:00:00 执行，等待 51915 秒...
2025-07-15 11:34:45,041 - INFO - ✅ PostgreSQL连接池创建成功
2025-07-15 11:34:45,041 - INFO - ✅ f1监听器服务启动成功
2025-07-15 11:34:45,062 - INFO - ✅ PostgreSQL连接池创建成功
2025-07-15 11:34:45,062 - INFO - ✅ f2推送回写服务启动成功，工作线程数: 4
2025-07-15 11:34:45,062 - INFO - 🚀 推送工作线程启动: worker_1
2025-07-15 11:34:45,062 - INFO - 🚀 推送工作线程启动: worker_2
2025-07-15 11:34:45,062 - INFO - 🚀 推送工作线程启动: worker_3
2025-07-15 11:34:45,063 - INFO - 🚀 推送工作线程启动: worker_4
2025-07-15 11:34:45,064 - INFO - ✅ 分区创建成功: entries_2025_07
2025-07-15 11:34:45,067 - INFO - ✅ PostgreSQL连接池创建成功
2025-07-15 11:34:45,067 - INFO - ✅ f4操作处理服务启动成功
2025-07-15 11:34:45,068 - INFO - ✅ 分区创建成功: entries_2025_08
2025-07-15 11:34:45,147 - INFO - 📡 开始监听频道: push_job
2025-07-15 11:34:45,148 - INFO - 📡 开始监听频道: partition_check
2025-07-15 11:34:45,149 - INFO - 📡 开始监听频道: sync_trigger
2025-07-15 11:34:53,092 - INFO - 📋 2025/07/03 + 16:10 + 处理队列项: INSERT - entry_id=145172 - queue_id=136 - 重试次数=0
2025-07-15 11:34:53,182 - INFO - 🔄 执行INSERT操作: entry_id=145172, employee_id=215829
2025-07-15 11:34:53,182 - INFO - 📤 发送到Server6的数据: {'従業員ｺｰﾄﾞ': '215829', '日付': '2025-07-15', '機種': 'NULL', '号機': 'NULL', '工場製番': 'NULL', '工事番号': 'NULL', 'ﾕﾆｯﾄ番号': 'NULL', '区分': '3', '項目': '3', '時間': 0.8, '所属ｺｰﾄﾞ': '131'}
2025-07-15 11:34:53,182 - INFO - AIOHTTP session for Server6 created.
2025-07-15 11:34:57,291 - INFO - ✅ INSERT完成: entry_id=145172 -> external_id=603673
2025-07-15 11:35:41,194 - INFO - 📋 2025/07/03 + 16:10 + 处理队列项: UPDATE - entry_id=145172 - queue_id=137 - 重试次数=0
2025-07-15 11:35:41,291 - ERROR - ❌ UPDATE操作失败: 找不到要更新的entry记录: entry_id=145172
Traceback (most recent call last):
  File "/home/<USER>/Documents/testF/2025/ser25/server0613/MySuiteDatebase2402newuigood-yolo5-3/server5/app/services/f2_push_writer.py", line 393, in _handle_update_operation_in_transaction
    raise ValueError(f"找不到要更新的entry记录: entry_id={entry_id}")
ValueError: 找不到要更新的entry记录: entry_id=145172
2025-07-15 11:35:41,292 - ERROR - ❌ 2025/07/03 + 16:10 + 队列项处理失败 (第1次): 找不到要更新的entry记录: entry_id=145172
2025-07-15 11:35:41,294 - WARNING - ⚠️ 2025/07/03 + 16:10 + 队列项将在下次循环重试: queue_id=137, retry_count=1
2025-07-15 11:35:41,299 - INFO - 📋 2025/07/03 + 16:10 + 处理队列项: UPDATE - entry_id=145172 - queue_id=137 - 重试次数=1
2025-07-15 11:35:41,314 - ERROR - ❌ UPDATE操作失败: 找不到要更新的entry记录: entry_id=145172
Traceback (most recent call last):
  File "/home/<USER>/Documents/testF/2025/ser25/server0613/MySuiteDatebase2402newuigood-yolo5-3/server5/app/services/f2_push_writer.py", line 393, in _handle_update_operation_in_transaction
    raise ValueError(f"找不到要更新的entry记录: entry_id={entry_id}")
ValueError: 找不到要更新的entry记录: entry_id=145172
2025-07-15 11:35:41,314 - ERROR - ❌ 2025/07/03 + 16:10 + 队列项处理失败 (第2次): 找不到要更新的entry记录: entry_id=145172
2025-07-15 11:35:41,315 - WARNING - ⚠️ 2025/07/03 + 16:10 + 队列项将在下次循环重试: queue_id=137, retry_count=2
2025-07-15 11:35:41,319 - INFO - 📋 2025/07/03 + 16:10 + 处理队列项: UPDATE - entry_id=145172 - queue_id=137 - 重试次数=2
2025-07-15 11:35:41,331 - INFO - 📋 2025/07/03 + 16:10 + 处理队列项: DELETE - entry_id=145172 - queue_id=138 - 重试次数=0
2025-07-15 11:35:41,332 - INFO - 🔄 执行DELETE操作: entry_id=145172, external_id=603673
2025-07-15 11:35:41,339 - ERROR - ❌ UPDATE操作失败: 找不到要更新的entry记录: entry_id=145172
Traceback (most recent call last):
  File "/home/<USER>/Documents/testF/2025/ser25/server0613/MySuiteDatebase2402newuigood-yolo5-3/server5/app/services/f2_push_writer.py", line 393, in _handle_update_operation_in_transaction
    raise ValueError(f"找不到要更新的entry记录: entry_id={entry_id}")
ValueError: 找不到要更新的entry记录: entry_id=145172
2025-07-15 11:35:41,340 - ERROR - ❌ 2025/07/03 + 16:10 + 队列项处理失败 (第3次): 找不到要更新的entry记录: entry_id=145172
2025-07-15 11:35:41,341 - WARNING - ⚠️ 2025/07/03 + 16:10 + 队列项将在下次循环重试: queue_id=137, retry_count=3
2025-07-15 11:35:41,345 - INFO - 📋 2025/07/03 + 16:10 + 处理队列项: UPDATE - entry_id=145172 - queue_id=137 - 重试次数=3
2025-07-15 11:35:41,367 - ERROR - ❌ UPDATE操作失败: 找不到要更新的entry记录: entry_id=145172
Traceback (most recent call last):
  File "/home/<USER>/Documents/testF/2025/ser25/server0613/MySuiteDatebase2402newuigood-yolo5-3/server5/app/services/f2_push_writer.py", line 393, in _handle_update_operation_in_transaction
    raise ValueError(f"找不到要更新的entry记录: entry_id={entry_id}")
ValueError: 找不到要更新的entry记录: entry_id=145172
2025-07-15 11:35:41,368 - ERROR - ❌ 2025/07/03 + 16:10 + 队列项处理失败 (第4次): 找不到要更新的entry记录: entry_id=145172
2025-07-15 11:35:41,369 - WARNING - ⚠️ 2025/07/03 + 16:10 + 队列项将在下次循环重试: queue_id=137, retry_count=4
2025-07-15 11:35:41,373 - INFO - 📋 2025/07/03 + 16:10 + 处理队列项: UPDATE - entry_id=145172 - queue_id=137 - 重试次数=4
2025-07-15 11:35:41,400 - ERROR - ❌ UPDATE操作失败: 找不到要更新的entry记录: entry_id=145172
Traceback (most recent call last):
  File "/home/<USER>/Documents/testF/2025/ser25/server0613/MySuiteDatebase2402newuigood-yolo5-3/server5/app/services/f2_push_writer.py", line 393, in _handle_update_operation_in_transaction
    raise ValueError(f"找不到要更新的entry记录: entry_id={entry_id}")
ValueError: 找不到要更新的entry记录: entry_id=145172
2025-07-15 11:35:41,401 - ERROR - ❌ 2025/07/03 + 16:10 + 队列项处理失败 (第5次): 找不到要更新的entry记录: entry_id=145172
2025-07-15 11:35:41,402 - WARNING - ⚠️ 2025/07/03 + 16:10 + 队列项将在下次循环重试: queue_id=137, retry_count=5
2025-07-15 11:35:41,406 - INFO - 📋 2025/07/03 + 16:10 + 处理队列项: UPDATE - entry_id=145172 - queue_id=137 - 重试次数=5
2025-07-15 11:35:41,422 - ERROR - ❌ UPDATE操作失败: 找不到要更新的entry记录: entry_id=145172
Traceback (most recent call last):
  File "/home/<USER>/Documents/testF/2025/ser25/server0613/MySuiteDatebase2402newuigood-yolo5-3/server5/app/services/f2_push_writer.py", line 393, in _handle_update_operation_in_transaction
    raise ValueError(f"找不到要更新的entry记录: entry_id={entry_id}")
ValueError: 找不到要更新的entry记录: entry_id=145172
2025-07-15 11:35:41,422 - ERROR - ❌ 2025/07/03 + 16:10 + 队列项处理失败 (第6次): 找不到要更新的entry记录: entry_id=145172
2025-07-15 11:35:41,423 - WARNING - ⚠️ 2025/07/03 + 16:10 + 队列项将在下次循环重试: queue_id=137, retry_count=6
2025-07-15 11:35:41,428 - INFO - 📋 2025/07/03 + 16:10 + 处理队列项: UPDATE - entry_id=145172 - queue_id=137 - 重试次数=6
2025-07-15 11:35:41,442 - ERROR - ❌ UPDATE操作失败: 找不到要更新的entry记录: entry_id=145172
Traceback (most recent call last):
  File "/home/<USER>/Documents/testF/2025/ser25/server0613/MySuiteDatebase2402newuigood-yolo5-3/server5/app/services/f2_push_writer.py", line 393, in _handle_update_operation_in_transaction
    raise ValueError(f"找不到要更新的entry记录: entry_id={entry_id}")
ValueError: 找不到要更新的entry记录: entry_id=145172
2025-07-15 11:35:41,442 - ERROR - ❌ 2025/07/03 + 16:10 + 队列项处理失败 (第7次): 找不到要更新的entry记录: entry_id=145172
2025-07-15 11:35:41,443 - WARNING - ⚠️ 2025/07/03 + 16:10 + 队列项将在下次循环重试: queue_id=137, retry_count=7
2025-07-15 11:35:41,448 - INFO - 📋 2025/07/03 + 16:10 + 处理队列项: UPDATE - entry_id=145172 - queue_id=137 - 重试次数=7
2025-07-15 11:35:41,477 - ERROR - ❌ UPDATE操作失败: 找不到要更新的entry记录: entry_id=145172
Traceback (most recent call last):
  File "/home/<USER>/Documents/testF/2025/ser25/server0613/MySuiteDatebase2402newuigood-yolo5-3/server5/app/services/f2_push_writer.py", line 393, in _handle_update_operation_in_transaction
    raise ValueError(f"找不到要更新的entry记录: entry_id={entry_id}")
ValueError: 找不到要更新的entry记录: entry_id=145172
2025-07-15 11:35:41,477 - ERROR - ❌ 2025/07/03 + 16:10 + 队列项处理失败 (第8次): 找不到要更新的entry记录: entry_id=145172
2025-07-15 11:35:41,478 - WARNING - ⚠️ 2025/07/03 + 16:10 + 队列项将在下次循环重试: queue_id=137, retry_count=8
2025-07-15 11:35:41,483 - INFO - 📋 2025/07/03 + 16:10 + 处理队列项: UPDATE - entry_id=145172 - queue_id=137 - 重试次数=8
2025-07-15 11:35:41,508 - ERROR - ❌ UPDATE操作失败: 找不到要更新的entry记录: entry_id=145172
Traceback (most recent call last):
  File "/home/<USER>/Documents/testF/2025/ser25/server0613/MySuiteDatebase2402newuigood-yolo5-3/server5/app/services/f2_push_writer.py", line 393, in _handle_update_operation_in_transaction
    raise ValueError(f"找不到要更新的entry记录: entry_id={entry_id}")
ValueError: 找不到要更新的entry记录: entry_id=145172
2025-07-15 11:35:41,508 - ERROR - ❌ 2025/07/03 + 16:10 + 队列项处理失败 (第9次): 找不到要更新的entry记录: entry_id=145172
2025-07-15 11:35:41,509 - WARNING - ⚠️ 2025/07/03 + 16:10 + 队列项将在下次循环重试: queue_id=137, retry_count=9
2025-07-15 11:35:41,514 - INFO - 📋 2025/07/03 + 16:10 + 处理队列项: UPDATE - entry_id=145172 - queue_id=137 - 重试次数=9
2025-07-15 11:35:41,538 - ERROR - ❌ UPDATE操作失败: 找不到要更新的entry记录: entry_id=145172
Traceback (most recent call last):
  File "/home/<USER>/Documents/testF/2025/ser25/server0613/MySuiteDatebase2402newuigood-yolo5-3/server5/app/services/f2_push_writer.py", line 393, in _handle_update_operation_in_transaction
    raise ValueError(f"找不到要更新的entry记录: entry_id={entry_id}")
ValueError: 找不到要更新的entry记录: entry_id=145172
2025-07-15 11:35:41,539 - ERROR - ❌ 2025/07/03 + 16:10 + 队列项处理失败 (第10次): 找不到要更新的entry记录: entry_id=145172
2025-07-15 11:35:41,539 - ERROR - 🚨 队列项超过最大重试次数，执行清理: queue_id=137, entry_id=145172
2025-07-15 11:35:41,562 - WARNING - ✅ 队列项清理完成: queue_id=137
2025-07-15 11:35:45,223 - INFO - ✅ DELETE完成: entry_id=145172, external_id=603673
2025-07-15 11:35:45,225 - INFO - ℹ️ DELETE操作跳过f6同步: entry_id=145172 (记录已删除)
2025-07-15 11:56:05,028 - INFO - 监听任务被取消
2025-07-15 11:56:05,031 - INFO - 🔌 MongoDB连接已关闭
2025-07-15 11:56:05,032 - INFO - 🔌 MongoDB连接已关闭
2025-07-15 11:56:05,033 - INFO - 🔌 Redis连接已关闭
2025-07-15 11:56:05,033 - INFO - 🔌 PostgreSQL连接已关闭
2025-07-15 11:56:05,033 - INFO - 🔌 Redis连接已关闭
2025-07-15 11:56:05,033 - INFO - 🔌 PostgreSQL连接已关闭
2025-07-15 11:56:05,033 - INFO - 🔌 PostgreSQL连接已关闭
2025-07-15 11:56:05,034 - INFO - 🔌 f1监听器服务已停止
2025-07-15 11:56:05,034 - INFO - 🔌 f4操作处理服务已停止
2025-07-15 11:56:05,034 - INFO - 🔌 推送工作线程停止: worker_1
2025-07-15 11:56:05,034 - INFO - 🔌 Redis连接已关闭
2025-07-15 11:56:05,034 - INFO - 🔌 f3数据拉取服务已停止
2025-07-15 11:56:05,034 - INFO - 🔌 推送工作线程停止: worker_4
2025-07-15 11:56:05,137 - INFO - 🔌 推送工作线程停止: worker_2
2025-07-15 11:56:05,164 - INFO - 🔌 推送工作线程停止: worker_3
2025-07-15 11:56:05,165 - INFO - 🔌 MongoDB连接已关闭
2025-07-15 11:56:05,165 - INFO - AIOHTTP session for Server6 closed.
2025-07-15 11:56:05,165 - INFO - 🔌 Redis连接已关闭
2025-07-15 11:56:05,171 - INFO - 🔌 PostgreSQL连接已关闭
2025-07-15 11:56:05,171 - INFO - 🔌 f2推送回写服务已停止
2025-07-15 11:56:05,172 - ERROR - Server5微服务启动失败: asyncio.run() cannot be called from a running event loop
2025-07-15 11:56:05,658 - INFO - 🔌 PostgreSQL连接已关闭
2025-07-15 11:56:05,658 - INFO - 🔌 MongoDB连接已关闭
2025-07-15 11:56:05,658 - INFO - 🔌 PostgreSQL连接已关闭
2025-07-15 11:56:05,658 - INFO - 🔌 MongoDB连接已关闭
2025-07-15 11:56:05,658 - INFO - 🔌 PostgreSQL连接已关闭
2025-07-15 11:56:05,658 - INFO - 🔌 MongoDB连接已关闭
2025-07-15 11:56:05,658 - INFO - 🔌 Redis连接已关闭
2025-07-15 11:56:05,658 - INFO - 🔌 Redis连接已关闭
2025-07-15 11:56:05,658 - INFO - 🔌 Redis连接已关闭
2025-07-15 11:56:05,658 - INFO - 🔌 f1监听器服务已停止
2025-07-15 11:56:05,658 - INFO - 🔌 f2推送回写服务已停止
2025-07-15 11:56:05,659 - INFO - 🔌 f4操作处理服务已停止
2025-07-15 11:56:05,681 - ERROR - Task was destroyed but it is pending!
task: <Task pending name='Task-2644' coro=<Connection._cancel() running at /home/<USER>/miniconda3/envs/my_suite_unified/lib/python3.12/site-packages/asyncpg/connection.py:1646> wait_for=<Future pending cb=[Task.task_wakeup()]>>
2025-07-15 11:56:07,307 - INFO - 🔊 f1监听器服务初始化完成
2025-07-15 11:56:07,307 - INFO - 📡 Server6客户端初始化 (新版): http://************:8019
2025-07-15 11:56:07,307 - INFO - 📤 f2推送回写引擎初始化完成 (修复版本: 直接轮询PostgreSQL)
2025-07-15 11:56:07,307 - INFO - 📡 Server6客户端初始化 (新版): http://************:8019
2025-07-15 11:56:07,308 - INFO - 📥 f3数据拉取器初始化完成 (Staging & UPSERT 模式)
2025-07-15 11:56:07,308 - INFO - 🎮 f4操作处理器初始化完成
2025-07-15 11:56:07,319 - INFO - Redis连接成功
2025-07-15 11:56:07,319 - INFO - Redis连接成功
2025-07-15 11:56:07,319 - INFO - Redis连接成功
2025-07-15 11:56:07,323 - INFO - ✅ MongoDB连接成功
2025-07-15 11:56:07,324 - INFO - ✅ MongoDB连接成功
2025-07-15 11:56:07,326 - INFO - ✅ MongoDB连接成功
2025-07-15 11:56:07,628 - INFO - ✅ PostgreSQL连接池创建成功
2025-07-15 11:56:07,628 - INFO - ✅ f1监听器服务启动成功
2025-07-15 11:56:07,649 - INFO - ✅ PostgreSQL连接池创建成功
2025-07-15 11:56:07,650 - INFO - Redis连接成功
2025-07-15 11:56:07,650 - INFO - ✅ f3数据拉取服务启动成功，预定执行时间: ['02:00', '03:00']
2025-07-15 11:56:07,650 - INFO - 🚀 数据拉取调度器启动，预定运行时间: ['02:00', '03:00']
2025-07-15 11:56:07,650 - INFO - 下一次f3/f5同步任务将在 2025-07-16 02:00:00 执行，等待 50632 秒...
2025-07-15 11:56:07,650 - INFO - ✅ 分区创建成功: entries_2025_07
2025-07-15 11:56:07,653 - INFO - ✅ PostgreSQL连接池创建成功
2025-07-15 11:56:07,653 - INFO - ✅ f2推送回写服务启动成功，工作线程数: 4
2025-07-15 11:56:07,653 - INFO - 🚀 推送工作线程启动: worker_1
2025-07-15 11:56:07,653 - INFO - 🚀 推送工作线程启动: worker_2
2025-07-15 11:56:07,654 - INFO - 🚀 推送工作线程启动: worker_3
2025-07-15 11:56:07,654 - INFO - 🚀 推送工作线程启动: worker_4
2025-07-15 11:56:07,655 - INFO - ✅ 分区创建成功: entries_2025_08
2025-07-15 11:56:07,712 - INFO - ✅ PostgreSQL连接池创建成功
2025-07-15 11:56:07,712 - INFO - ✅ f4操作处理服务启动成功
2025-07-15 11:56:07,754 - INFO - 📡 开始监听频道: push_job
2025-07-15 11:56:07,755 - INFO - 📡 开始监听频道: partition_check
2025-07-15 11:56:07,756 - INFO - 📡 开始监听频道: sync_trigger
2025-07-15 11:58:44,042 - INFO - 📋 2025/07/03 + 16:10 + 处理队列项: INSERT - entry_id=145173 - queue_id=139 - 重试次数=0
2025-07-15 11:58:44,135 - INFO - 🔄 执行INSERT操作: entry_id=145173, employee_id=215829
2025-07-15 11:58:44,135 - INFO - 📤 发送到Server6的数据: {'従業員ｺｰﾄﾞ': '215829', '日付': '2025-07-15', '機種': 'NULL', '号機': 'NULL', '工場製番': 'NULL', '工事番号': 'NULL', 'ﾕﾆｯﾄ番号': 'NULL', '区分': '5', '項目': '5', '時間': 0.1, '所属ｺｰﾄﾞ': '131'}
2025-07-15 11:58:44,135 - INFO - AIOHTTP session for Server6 created.
2025-07-15 11:58:48,944 - INFO - ✅ INSERT完成: entry_id=145173 -> external_id=603674
2025-07-15 11:59:26,131 - INFO - 📋 2025/07/03 + 16:10 + 处理队列项: UPDATE - entry_id=145173 - queue_id=140 - 重试次数=0
2025-07-15 11:59:26,195 - INFO - 📋 2025/07/03 + 16:10 + 处理队列项: DELETE - entry_id=145173 - queue_id=141 - 重试次数=0
2025-07-15 11:59:26,196 - INFO - 🔄 执行DELETE操作: entry_id=145173, external_id=603674
2025-07-15 11:59:26,228 - WARNING - ⚠️ UPDATE操作跳过: entry_id=145173 (记录已被删除)
2025-07-15 11:59:26,231 - INFO - ✅ UPDATE跳过完成: entry_id=145173 (记录已被删除)
2025-07-15 11:59:29,981 - INFO - ✅ DELETE完成: entry_id=145173, external_id=603674
2025-07-15 11:59:29,984 - INFO - ℹ️ DELETE操作跳过f6同步: entry_id=145173 (记录已删除)
2025-07-15 12:00:37,191 - INFO - 📋 2025/07/03 + 16:10 + 处理队列项: INSERT - entry_id=145174 - queue_id=142 - 重试次数=0
2025-07-15 12:00:37,208 - INFO - 🔄 执行INSERT操作: entry_id=145174, employee_id=215829
2025-07-15 12:00:37,208 - INFO - 📤 发送到Server6的数据: {'従業員ｺｰﾄﾞ': '215829', '日付': '2025-07-15', '機種': 'NULL', '号機': 'NULL', '工場製番': 'NULL', '工事番号': 'NULL', 'ﾕﾆｯﾄ番号': 'NULL', '区分': '5', '項目': '5', '時間': 0.1, '所属ｺｰﾄﾞ': '131'}
2025-07-15 12:00:41,471 - INFO - ✅ INSERT完成: entry_id=145174 -> external_id=603675
2025-07-15 12:15:27,492 - INFO - 📋 2025/07/03 + 16:10 + 处理队列项: INSERT - entry_id=145175 - queue_id=143 - 重试次数=0
2025-07-15 12:15:27,508 - INFO - 🔄 执行INSERT操作: entry_id=145175, employee_id=215829
2025-07-15 12:15:27,508 - INFO - 📤 发送到Server6的数据: {'従業員ｺｰﾄﾞ': '215829', '日付': '2025-07-15', '機種': 'NULL', '号機': 'NULL', '工場製番': 'NULL', '工事番号': 'NULL', 'ﾕﾆｯﾄ番号': 'NULL', '区分': '2', '項目': '2', '時間': 2.0, '所属ｺｰﾄﾞ': '131'}
2025-07-15 12:15:32,187 - INFO - ✅ INSERT完成: entry_id=145175 -> external_id=603680
2025-07-15 12:16:06,568 - INFO - 📋 2025/07/03 + 16:10 + 处理队列项: INSERT - entry_id=145175 - queue_id=144 - 重试次数=0
2025-07-15 12:16:06,682 - INFO - 🔄 执行INSERT操作: entry_id=145175, employee_id=215829
2025-07-15 12:16:06,682 - INFO - 📤 发送到Server6的数据: {'従業員ｺｰﾄﾞ': '215829', '日付': '2025-06-26', '機種': 'NULL', '号機': 'NULL', '工場製番': 'NULL', '工事番号': 'test', 'ﾕﾆｯﾄ番号': 'NULL', '区分': '3', '項目': '7', '時間': 0.1, '所属ｺｰﾄﾞ': '131'}
2025-07-15 12:16:10,975 - INFO - ✅ INSERT完成: entry_id=145175 -> external_id=603681
2025-07-15 12:17:56,505 - INFO - 📋 2025/07/03 + 16:10 + 处理队列项: INSERT - entry_id=145176 - queue_id=145 - 重试次数=0
2025-07-15 12:17:56,592 - INFO - 🔄 执行INSERT操作: entry_id=145176, employee_id=215829
2025-07-15 12:17:56,592 - INFO - 📤 发送到Server6的数据: {'従業員ｺｰﾄﾞ': '215829', '日付': '2025-07-15', '機種': 'NULL', '号機': 'NULL', '工場製番': 'NULL', '工事番号': 'NULL', 'ﾕﾆｯﾄ番号': 'NULL', '区分': '2', '項目': '2', '時間': 2.0, '所属ｺｰﾄﾞ': '131'}
2025-07-15 12:18:00,803 - INFO - ✅ INSERT完成: entry_id=145176 -> external_id=603682
2025-07-15 12:34:57,886 - INFO - 监听任务被取消
2025-07-15 12:34:57,888 - INFO - 🔌 MongoDB连接已关闭
2025-07-15 12:34:57,889 - INFO - 🔌 MongoDB连接已关闭
2025-07-15 12:34:57,890 - INFO - 🔌 Redis连接已关闭
2025-07-15 12:34:57,891 - INFO - 🔌 PostgreSQL连接已关闭
2025-07-15 12:34:57,891 - INFO - 🔌 Redis连接已关闭
2025-07-15 12:34:57,891 - INFO - 🔌 PostgreSQL连接已关闭
2025-07-15 12:34:57,891 - INFO - 🔌 PostgreSQL连接已关闭
2025-07-15 12:34:57,891 - INFO - 🔌 f1监听器服务已停止
2025-07-15 12:34:57,891 - INFO - 🔌 f4操作处理服务已停止
2025-07-15 12:34:57,891 - INFO - 🔌 Redis连接已关闭
2025-07-15 12:34:57,891 - INFO - 🔌 f3数据拉取服务已停止
2025-07-15 12:34:58,003 - INFO - 🔌 推送工作线程停止: worker_1
2025-07-15 12:34:58,036 - INFO - 🔌 推送工作线程停止: worker_3
2025-07-15 12:34:58,068 - INFO - 🔌 推送工作线程停止: worker_2
2025-07-15 12:34:58,096 - INFO - 🔌 推送工作线程停止: worker_4
2025-07-15 12:34:58,097 - INFO - 🔌 MongoDB连接已关闭
2025-07-15 12:34:58,098 - INFO - AIOHTTP session for Server6 closed.
2025-07-15 12:34:58,098 - INFO - 🔌 Redis连接已关闭
2025-07-15 12:34:58,103 - INFO - 🔌 PostgreSQL连接已关闭
2025-07-15 12:34:58,103 - INFO - 🔌 f2推送回写服务已停止
2025-07-15 12:34:58,103 - ERROR - Server5微服务启动失败: asyncio.run() cannot be called from a running event loop
2025-07-15 12:34:58,563 - INFO - 🔌 PostgreSQL连接已关闭
2025-07-15 12:34:58,564 - INFO - 🔌 MongoDB连接已关闭
2025-07-15 12:34:58,564 - INFO - 🔌 PostgreSQL连接已关闭
2025-07-15 12:34:58,565 - INFO - 🔌 MongoDB连接已关闭
2025-07-15 12:34:58,565 - INFO - 🔌 PostgreSQL连接已关闭
2025-07-15 12:34:58,566 - INFO - 🔌 MongoDB连接已关闭
2025-07-15 12:34:58,566 - INFO - 🔌 Redis连接已关闭
2025-07-15 12:34:58,566 - INFO - 🔌 Redis连接已关闭
2025-07-15 12:34:58,566 - INFO - 🔌 Redis连接已关闭
2025-07-15 12:34:58,566 - INFO - 🔌 f1监听器服务已停止
2025-07-15 12:34:58,567 - INFO - 🔌 f2推送回写服务已停止
2025-07-15 12:34:58,567 - INFO - 🔌 f4操作处理服务已停止
2025-07-15 12:34:58,590 - ERROR - Task was destroyed but it is pending!
task: <Task pending name='Task-4740' coro=<Connection._cancel() running at /home/<USER>/miniconda3/envs/my_suite_unified/lib/python3.12/site-packages/asyncpg/connection.py:1646> wait_for=<Future pending cb=[Task.task_wakeup()]>>
2025-07-15 12:34:59,874 - INFO - 🔊 f1监听器服务初始化完成
2025-07-15 12:34:59,874 - INFO - 📡 Server6客户端初始化 (新版): http://************:8019
2025-07-15 12:34:59,875 - INFO - 📤 f2推送回写引擎初始化完成 (修复版本: 直接轮询PostgreSQL)
2025-07-15 12:34:59,875 - INFO - 📡 Server6客户端初始化 (新版): http://************:8019
2025-07-15 12:34:59,875 - INFO - 📥 f3数据拉取器初始化完成 (Staging & UPSERT 模式)
2025-07-15 12:34:59,875 - INFO - 🎮 f4操作处理器初始化完成
2025-07-15 12:34:59,885 - INFO - Redis连接成功
2025-07-15 12:34:59,885 - INFO - Redis连接成功
2025-07-15 12:34:59,886 - INFO - Redis连接成功
2025-07-15 12:34:59,890 - INFO - ✅ MongoDB连接成功
2025-07-15 12:34:59,891 - INFO - ✅ MongoDB连接成功
2025-07-15 12:34:59,893 - INFO - ✅ MongoDB连接成功
2025-07-15 12:35:00,183 - INFO - ✅ PostgreSQL连接池创建成功
2025-07-15 12:35:00,201 - INFO - ✅ PostgreSQL连接池创建成功
2025-07-15 12:35:00,201 - INFO - ✅ f1监听器服务启动成功
2025-07-15 12:35:00,202 - INFO - Redis连接成功
2025-07-15 12:35:00,202 - INFO - ✅ f3数据拉取服务启动成功，预定执行时间: ['02:00', '03:00']
2025-07-15 12:35:00,202 - INFO - 🚀 数据拉取调度器启动，预定运行时间: ['02:00', '03:00']
2025-07-15 12:35:00,202 - INFO - 下一次f3/f5同步任务将在 2025-07-16 02:00:00 执行，等待 48300 秒...
2025-07-15 12:35:00,205 - INFO - ✅ PostgreSQL连接池创建成功
2025-07-15 12:35:00,205 - INFO - ✅ f2推送回写服务启动成功，工作线程数: 4
2025-07-15 12:35:00,205 - INFO - 🚀 推送工作线程启动: worker_1
2025-07-15 12:35:00,205 - INFO - 🚀 推送工作线程启动: worker_2
2025-07-15 12:35:00,205 - INFO - 🚀 推送工作线程启动: worker_3
2025-07-15 12:35:00,206 - INFO - 🚀 推送工作线程启动: worker_4
2025-07-15 12:35:00,228 - INFO - ✅ 分区创建成功: entries_2025_07
2025-07-15 12:35:00,232 - INFO - ✅ 分区创建成功: entries_2025_08
2025-07-15 12:35:00,233 - INFO - ✅ PostgreSQL连接池创建成功
2025-07-15 12:35:00,233 - INFO - ✅ f4操作处理服务启动成功
2025-07-15 12:35:00,343 - INFO - 📡 开始监听频道: push_job
2025-07-15 12:35:00,344 - INFO - 📡 开始监听频道: partition_check
2025-07-15 12:35:00,346 - INFO - 📡 开始监听频道: sync_trigger
2025-07-15 12:43:41,902 - INFO - 监听任务被取消
2025-07-15 12:43:41,903 - INFO - 🔌 MongoDB连接已关闭
2025-07-15 12:43:41,904 - INFO - 🔌 MongoDB连接已关闭
2025-07-15 12:43:41,905 - INFO - 🔌 Redis连接已关闭
2025-07-15 12:43:41,906 - INFO - 🔌 PostgreSQL连接已关闭
2025-07-15 12:43:41,906 - INFO - 🔌 Redis连接已关闭
2025-07-15 12:43:41,906 - INFO - 🔌 PostgreSQL连接已关闭
2025-07-15 12:43:41,906 - INFO - 🔌 PostgreSQL连接已关闭
2025-07-15 12:43:41,906 - INFO - 🔌 f1监听器服务已停止
2025-07-15 12:43:41,906 - INFO - 🔌 f4操作处理服务已停止
2025-07-15 12:43:41,906 - INFO - 🔌 Redis连接已关闭
2025-07-15 12:43:41,906 - INFO - 🔌 f3数据拉取服务已停止
2025-07-15 12:43:42,007 - INFO - 🔌 推送工作线程停止: worker_4
2025-07-15 12:43:42,042 - INFO - 🔌 推送工作线程停止: worker_1
2025-07-15 12:43:42,090 - INFO - 🔌 推送工作线程停止: worker_2
2025-07-15 12:43:42,117 - INFO - 🔌 推送工作线程停止: worker_3
2025-07-15 12:43:42,119 - INFO - 🔌 MongoDB连接已关闭
2025-07-15 12:43:42,119 - INFO - 🔌 Redis连接已关闭
2025-07-15 12:43:42,125 - INFO - 🔌 PostgreSQL连接已关闭
2025-07-15 12:43:42,126 - INFO - 🔌 f2推送回写服务已停止
2025-07-15 12:43:42,126 - ERROR - Server5微服务启动失败: asyncio.run() cannot be called from a running event loop
2025-07-15 12:43:42,536 - INFO - 🔌 PostgreSQL连接已关闭
2025-07-15 12:43:42,537 - INFO - 🔌 MongoDB连接已关闭
2025-07-15 12:43:42,537 - INFO - 🔌 PostgreSQL连接已关闭
2025-07-15 12:43:42,537 - INFO - 🔌 MongoDB连接已关闭
2025-07-15 12:43:42,538 - INFO - 🔌 PostgreSQL连接已关闭
2025-07-15 12:43:42,538 - INFO - 🔌 MongoDB连接已关闭
2025-07-15 12:43:42,539 - INFO - 🔌 Redis连接已关闭
2025-07-15 12:43:42,539 - INFO - 🔌 Redis连接已关闭
2025-07-15 12:43:42,539 - INFO - 🔌 Redis连接已关闭
2025-07-15 12:43:42,539 - INFO - 🔌 f1监听器服务已停止
2025-07-15 12:43:42,539 - INFO - 🔌 f2推送回写服务已停止
2025-07-15 12:43:42,539 - INFO - 🔌 f4操作处理服务已停止
2025-07-15 12:43:42,564 - ERROR - Task was destroyed but it is pending!
task: <Task pending name='Task-1119' coro=<Connection._cancel() running at /home/<USER>/miniconda3/envs/my_suite_unified/lib/python3.12/site-packages/asyncpg/connection.py:1646> wait_for=<Future pending cb=[Task.task_wakeup()]>>
2025-07-15 12:44:18,655 - INFO - 🔊 f1监听器服务初始化完成
2025-07-15 12:44:18,655 - INFO - 📡 Server6客户端初始化 (新版): http://************:8019
2025-07-15 12:44:18,655 - INFO - 📤 f2推送回写引擎初始化完成 (修复版本: 直接轮询PostgreSQL)
2025-07-15 12:44:18,655 - INFO - 📡 Server6客户端初始化 (新版): http://************:8019
2025-07-15 12:44:18,656 - INFO - 📥 f3数据拉取器初始化完成 (Staging & UPSERT 模式)
2025-07-15 12:44:18,656 - INFO - 🎮 f4操作处理器初始化完成
2025-07-15 12:44:18,666 - INFO - Redis连接成功
2025-07-15 12:44:18,666 - INFO - Redis连接成功
2025-07-15 12:44:18,666 - INFO - Redis连接成功
2025-07-15 12:44:18,672 - INFO - ✅ MongoDB连接成功
2025-07-15 12:44:18,672 - INFO - ✅ MongoDB连接成功
2025-07-15 12:44:18,673 - INFO - ✅ MongoDB连接成功
2025-07-15 12:44:19,026 - INFO - ✅ PostgreSQL连接池创建成功
2025-07-15 12:44:19,027 - INFO - Redis连接成功
2025-07-15 12:44:19,027 - INFO - ✅ f3数据拉取服务启动成功，预定执行时间: ['02:00', '03:00']
2025-07-15 12:44:19,027 - INFO - 🚀 数据拉取调度器启动，预定运行时间: ['02:00', '03:00']
2025-07-15 12:44:19,027 - INFO - 下一次f3/f5同步任务将在 2025-07-16 02:00:00 执行，等待 47741 秒...
2025-07-15 12:44:19,137 - INFO - ✅ PostgreSQL连接池创建成功
2025-07-15 12:44:19,137 - INFO - ✅ f2推送回写服务启动成功，工作线程数: 4
2025-07-15 12:44:19,137 - INFO - 🚀 推送工作线程启动: worker_1
2025-07-15 12:44:19,137 - INFO - 🚀 推送工作线程启动: worker_2
2025-07-15 12:44:19,137 - INFO - 🚀 推送工作线程启动: worker_3
2025-07-15 12:44:19,138 - INFO - 🚀 推送工作线程启动: worker_4
2025-07-15 12:44:19,158 - INFO - ✅ PostgreSQL连接池创建成功
2025-07-15 12:44:19,158 - INFO - ✅ f4操作处理服务启动成功
2025-07-15 12:44:19,163 - INFO - ✅ PostgreSQL连接池创建成功
2025-07-15 12:44:19,163 - INFO - ✅ f1监听器服务启动成功
2025-07-15 12:44:19,173 - INFO - ✅ 分区创建成功: entries_2025_07
2025-07-15 12:44:19,177 - INFO - ✅ 分区创建成功: entries_2025_08
2025-07-15 12:44:19,345 - INFO - 📡 开始监听频道: push_job
2025-07-15 12:44:19,346 - INFO - 📡 开始监听频道: partition_check
2025-07-15 12:44:19,347 - INFO - 📡 开始监听频道: sync_trigger
2025-07-15 12:44:37,203 - INFO - 📋 2025/07/03 + 16:10 + 处理队列项: UPDATE - entry_id=145176 - queue_id=146 - 重试次数=0
2025-07-15 12:44:37,297 - INFO - 🔄 执行UPDATE操作: entry_id=145176 -> external_id=603682
2025-07-15 12:44:37,297 - INFO - 📦 更新数据: {'employee_id': '215829', 'entry_date': '2025/07/15', 'model': None, 'number': None, 'factory_number': None, 'project_number': None, 'unit_number': None, 'category': '2', 'item': '2', 'duration': 2.0, 'department': '131'}
2025-07-15 12:44:37,298 - INFO - 📤 发送到Server6的更新数据: {'従業員ｺｰﾄﾞ': '215829', '日付': '2025-07-15', '機種': 'NULL', '号機': 'NULL', '工場製番': 'NULL', '工事番号': 'NULL', 'ﾕﾆｯﾄ番号': 'NULL', '区分': '2', '項目': '2', '時間': 2.0, '所属ｺｰﾄﾞ': '131'}
2025-07-15 12:44:37,298 - INFO - AIOHTTP session for Server6 created.
2025-07-15 12:44:41,806 - INFO - ✅ UPDATE完成: entry_id=145176 -> external_id=603682
2025-07-15 13:04:37,364 - INFO - 监听任务被取消
2025-07-15 13:04:37,366 - INFO - 🔌 MongoDB连接已关闭
2025-07-15 13:04:37,367 - INFO - 🔌 MongoDB连接已关闭
2025-07-15 13:04:37,368 - INFO - 🔌 Redis连接已关闭
2025-07-15 13:04:37,368 - INFO - 🔌 PostgreSQL连接已关闭
2025-07-15 13:04:37,368 - INFO - 🔌 Redis连接已关闭
2025-07-15 13:04:37,368 - INFO - 🔌 PostgreSQL连接已关闭
2025-07-15 13:04:37,368 - INFO - 🔌 PostgreSQL连接已关闭
2025-07-15 13:04:37,368 - INFO - 🔌 f1监听器服务已停止
2025-07-15 13:04:37,368 - INFO - 🔌 f4操作处理服务已停止
2025-07-15 13:04:37,369 - INFO - 🔌 Redis连接已关闭
2025-07-15 13:04:37,369 - INFO - 🔌 f3数据拉取服务已停止
2025-07-15 13:04:37,463 - INFO - 🔌 推送工作线程停止: worker_1
2025-07-15 13:04:37,501 - INFO - 🔌 推送工作线程停止: worker_3
2025-07-15 13:04:37,543 - INFO - 🔌 推送工作线程停止: worker_2
2025-07-15 13:04:37,580 - INFO - 🔌 推送工作线程停止: worker_4
2025-07-15 13:04:37,581 - INFO - 🔌 MongoDB连接已关闭
2025-07-15 13:04:37,581 - INFO - AIOHTTP session for Server6 closed.
2025-07-15 13:04:37,581 - INFO - 🔌 Redis连接已关闭
2025-07-15 13:04:37,585 - INFO - 🔌 PostgreSQL连接已关闭
2025-07-15 13:04:37,585 - INFO - 🔌 f2推送回写服务已停止
2025-07-15 13:04:37,586 - ERROR - Server5微服务启动失败: asyncio.run() cannot be called from a running event loop
2025-07-15 13:04:38,069 - INFO - 🔌 PostgreSQL连接已关闭
2025-07-15 13:04:38,070 - INFO - 🔌 MongoDB连接已关闭
2025-07-15 13:04:38,070 - INFO - 🔌 PostgreSQL连接已关闭
2025-07-15 13:04:38,070 - INFO - 🔌 MongoDB连接已关闭
2025-07-15 13:04:38,070 - INFO - 🔌 PostgreSQL连接已关闭
2025-07-15 13:04:38,070 - INFO - 🔌 MongoDB连接已关闭
2025-07-15 13:04:38,070 - INFO - 🔌 Redis连接已关闭
2025-07-15 13:04:38,070 - INFO - 🔌 Redis连接已关闭
2025-07-15 13:04:38,070 - INFO - 🔌 Redis连接已关闭
2025-07-15 13:04:38,070 - INFO - 🔌 f1监听器服务已停止
2025-07-15 13:04:38,070 - INFO - 🔌 f2推送回写服务已停止
2025-07-15 13:04:38,070 - INFO - 🔌 f4操作处理服务已停止
2025-07-15 13:04:38,092 - ERROR - Task was destroyed but it is pending!
task: <Task pending name='Task-2509' coro=<Connection._cancel() running at /home/<USER>/miniconda3/envs/my_suite_unified/lib/python3.12/site-packages/asyncpg/connection.py:1646> wait_for=<Future pending cb=[Task.task_wakeup()]>>
2025-07-15 13:04:38,932 - INFO - 🔊 f1监听器服务初始化完成
2025-07-15 13:04:38,932 - INFO - 📡 Server6客户端初始化 (新版): http://************:8019
2025-07-15 13:04:38,932 - INFO - 📤 f2推送回写引擎初始化完成 (修复版本: 直接轮询PostgreSQL)
2025-07-15 13:04:38,932 - INFO - 📡 Server6客户端初始化 (新版): http://************:8019
2025-07-15 13:04:38,933 - INFO - 📥 f3数据拉取器初始化完成 (Staging & UPSERT 模式)
2025-07-15 13:04:38,933 - INFO - 🎮 f4操作处理器初始化完成
2025-07-15 13:04:38,944 - INFO - Redis连接成功
2025-07-15 13:04:38,944 - INFO - Redis连接成功
2025-07-15 13:04:38,944 - INFO - Redis连接成功
2025-07-15 13:04:38,949 - INFO - ✅ MongoDB连接成功
2025-07-15 13:04:38,950 - INFO - ✅ MongoDB连接成功
2025-07-15 13:04:38,952 - INFO - ✅ MongoDB连接成功
2025-07-15 13:04:39,207 - INFO - ✅ PostgreSQL连接池创建成功
2025-07-15 13:04:39,207 - INFO - ✅ f1监听器服务启动成功
2025-07-15 13:04:39,232 - INFO - ✅ 分区创建成功: entries_2025_07
2025-07-15 13:04:39,235 - INFO - ✅ PostgreSQL连接池创建成功
2025-07-15 13:04:39,235 - INFO - ✅ f2推送回写服务启动成功，工作线程数: 4
2025-07-15 13:04:39,235 - INFO - 🚀 推送工作线程启动: worker_1
2025-07-15 13:04:39,236 - INFO - 🚀 推送工作线程启动: worker_2
2025-07-15 13:04:39,236 - INFO - 🚀 推送工作线程启动: worker_3
2025-07-15 13:04:39,236 - INFO - 🚀 推送工作线程启动: worker_4
2025-07-15 13:04:39,238 - INFO - ✅ 分区创建成功: entries_2025_08
2025-07-15 13:04:39,294 - INFO - ✅ PostgreSQL连接池创建成功
2025-07-15 13:04:39,294 - INFO - ✅ f4操作处理服务启动成功
2025-07-15 13:04:39,345 - INFO - ✅ PostgreSQL连接池创建成功
2025-07-15 13:04:39,345 - INFO - Redis连接成功
2025-07-15 13:04:39,346 - INFO - ✅ f3数据拉取服务启动成功，预定执行时间: ['02:00', '03:00']
2025-07-15 13:04:39,346 - INFO - 🚀 数据拉取调度器启动，预定运行时间: ['02:00', '03:00']
2025-07-15 13:04:39,346 - INFO - 下一次f3/f5同步任务将在 2025-07-16 02:00:00 执行，等待 46521 秒...
2025-07-15 13:04:39,350 - INFO - 📡 开始监听频道: push_job
2025-07-15 13:04:39,352 - INFO - 📡 开始监听频道: partition_check
2025-07-15 13:04:39,353 - INFO - 📡 开始监听频道: sync_trigger
2025-07-15 13:18:11,580 - INFO - 监听任务被取消
2025-07-15 13:18:11,584 - INFO - 🔌 MongoDB连接已关闭
2025-07-15 13:18:11,586 - INFO - 🔌 MongoDB连接已关闭
2025-07-15 13:18:11,590 - INFO - 🔌 Redis连接已关闭
2025-07-15 13:18:11,591 - INFO - 🔌 PostgreSQL连接已关闭
2025-07-15 13:18:11,591 - INFO - 🔌 Redis连接已关闭
2025-07-15 13:18:11,591 - INFO - 🔌 推送工作线程停止: worker_2
2025-07-15 13:18:11,592 - INFO - 🔌 PostgreSQL连接已关闭
2025-07-15 13:18:11,592 - INFO - 🔌 PostgreSQL连接已关闭
2025-07-15 13:18:11,592 - INFO - 🔌 推送工作线程停止: worker_4
2025-07-15 13:18:11,592 - INFO - 🔌 f1监听器服务已停止
2025-07-15 13:18:11,592 - INFO - 🔌 f4操作处理服务已停止
2025-07-15 13:18:11,592 - INFO - 🔌 Redis连接已关闭
2025-07-15 13:18:11,592 - INFO - 🔌 f3数据拉取服务已停止
2025-07-15 13:18:11,706 - INFO - 🔌 推送工作线程停止: worker_1
2025-07-15 13:18:11,725 - INFO - 🔌 推送工作线程停止: worker_3
2025-07-15 13:18:11,726 - INFO - 🔌 MongoDB连接已关闭
2025-07-15 13:18:11,726 - INFO - 🔌 Redis连接已关闭
2025-07-15 13:18:11,730 - INFO - 🔌 PostgreSQL连接已关闭
2025-07-15 13:18:11,730 - INFO - 🔌 f2推送回写服务已停止
2025-07-15 13:18:11,731 - ERROR - Server5微服务启动失败: asyncio.run() cannot be called from a running event loop
2025-07-15 13:18:12,205 - INFO - 🔌 PostgreSQL连接已关闭
2025-07-15 13:18:12,206 - INFO - 🔌 MongoDB连接已关闭
2025-07-15 13:18:12,206 - INFO - 🔌 PostgreSQL连接已关闭
2025-07-15 13:18:12,206 - INFO - 🔌 MongoDB连接已关闭
2025-07-15 13:18:12,206 - INFO - 🔌 PostgreSQL连接已关闭
2025-07-15 13:18:12,206 - INFO - 🔌 MongoDB连接已关闭
2025-07-15 13:18:12,206 - INFO - 🔌 Redis连接已关闭
2025-07-15 13:18:12,206 - INFO - 🔌 Redis连接已关闭
2025-07-15 13:18:12,206 - INFO - 🔌 Redis连接已关闭
2025-07-15 13:18:12,206 - INFO - 🔌 f1监听器服务已停止
2025-07-15 13:18:12,207 - INFO - 🔌 f2推送回写服务已停止
2025-07-15 13:18:12,207 - INFO - 🔌 f4操作处理服务已停止
2025-07-15 13:18:12,790 - INFO - 🔊 f1监听器服务初始化完成
2025-07-15 13:18:12,790 - INFO - 📡 Server6客户端初始化 (新版): http://************:8019
2025-07-15 13:18:12,791 - INFO - 📤 f2推送回写引擎初始化完成 (修复版本: 直接轮询PostgreSQL)
2025-07-15 13:18:12,791 - INFO - 📡 Server6客户端初始化 (新版): http://************:8019
2025-07-15 13:18:12,791 - INFO - 📥 f3数据拉取器初始化完成 (Staging & UPSERT 模式)
2025-07-15 13:18:12,792 - INFO - 🎮 f4操作处理器初始化完成
2025-07-15 13:18:12,802 - INFO - Redis连接成功
2025-07-15 13:18:12,802 - INFO - Redis连接成功
2025-07-15 13:18:12,802 - INFO - Redis连接成功
2025-07-15 13:18:12,807 - INFO - ✅ MongoDB连接成功
2025-07-15 13:18:12,808 - INFO - ✅ MongoDB连接成功
2025-07-15 13:18:12,809 - INFO - ✅ MongoDB连接成功
2025-07-15 13:18:13,137 - INFO - ✅ PostgreSQL连接池创建成功
2025-07-15 13:18:13,173 - INFO - ✅ PostgreSQL连接池创建成功
2025-07-15 13:18:13,173 - INFO - ✅ f1监听器服务启动成功
2025-07-15 13:18:13,174 - INFO - Redis连接成功
2025-07-15 13:18:13,174 - INFO - ✅ f3数据拉取服务启动成功，预定执行时间: ['02:00', '03:00']
2025-07-15 13:18:13,174 - INFO - 🚀 数据拉取调度器启动，预定运行时间: ['02:00', '03:00']
2025-07-15 13:18:13,175 - INFO - 下一次f3/f5同步任务将在 2025-07-16 02:00:00 执行，等待 45707 秒...
2025-07-15 13:18:13,193 - INFO - ✅ PostgreSQL连接池创建成功
2025-07-15 13:18:13,193 - INFO - ✅ f2推送回写服务启动成功，工作线程数: 4
2025-07-15 13:18:13,193 - INFO - 🚀 推送工作线程启动: worker_1
2025-07-15 13:18:13,193 - INFO - 🚀 推送工作线程启动: worker_2
2025-07-15 13:18:13,193 - INFO - 🚀 推送工作线程启动: worker_3
2025-07-15 13:18:13,194 - INFO - 🚀 推送工作线程启动: worker_4
2025-07-15 13:18:13,195 - INFO - ✅ 分区创建成功: entries_2025_07
2025-07-15 13:18:13,197 - INFO - ✅ PostgreSQL连接池创建成功
2025-07-15 13:18:13,198 - INFO - ✅ f4操作处理服务启动成功
2025-07-15 13:18:13,199 - INFO - ✅ 分区创建成功: entries_2025_08
2025-07-15 13:18:13,314 - INFO - 📡 开始监听频道: push_job
2025-07-15 13:18:13,316 - INFO - 📡 开始监听频道: partition_check
2025-07-15 13:18:13,317 - INFO - 📡 开始监听频道: sync_trigger
2025-07-15 13:18:49,281 - INFO - 📋 2025/07/03 + 16:10 + 处理队列项: INSERT - entry_id=145177 - queue_id=147 - 重试次数=0
2025-07-15 13:18:49,389 - INFO - 🔄 执行INSERT操作: entry_id=145177, employee_id=215829
2025-07-15 13:18:49,390 - INFO - 📤 发送到Server6的数据: {'従業員ｺｰﾄﾞ': '215829', '日付': '2025-07-15', '機種': 'NULL', '号機': 'NULL', '工場製番': 'NULL', '工事番号': 'NULL', 'ﾕﾆｯﾄ番号': 'NULL', '区分': '5', '項目': '5', '時間': 0.5, '所属ｺｰﾄﾞ': '131'}
2025-07-15 13:18:49,390 - INFO - AIOHTTP session for Server6 created.
2025-07-15 13:18:53,934 - INFO - ✅ INSERT完成: entry_id=145177 -> external_id=603683
2025-07-15 13:19:19,347 - INFO - 📋 2025/07/03 + 16:10 + 处理队列项: UPDATE - entry_id=145177 - queue_id=148 - 重试次数=0
2025-07-15 13:19:19,439 - INFO - 🔄 执行UPDATE操作: entry_id=145177 -> external_id=603683
2025-07-15 13:19:19,440 - INFO - 📦 更新数据: {'employee_id': '215829', 'entry_date': '2025/07/15', 'model': '5', 'number': None, 'factory_number': None, 'project_number': None, 'unit_number': None, 'category': '5', 'item': '5', 'duration': 0.1, 'department': '131'}
2025-07-15 13:19:19,440 - INFO - 📤 发送到Server6的更新数据: {'従業員ｺｰﾄﾞ': '215829', '日付': '2025-07-15', '機種': '5', '号機': 'NULL', '工場製番': 'NULL', '工事番号': 'NULL', 'ﾕﾆｯﾄ番号': 'NULL', '区分': '5', '項目': '5', '時間': 0.1, '所属ｺｰﾄﾞ': '131'}
2025-07-15 13:19:19,486 - INFO - 📋 2025/07/03 + 16:10 + 处理队列项: UPDATE - entry_id=145177 - queue_id=149 - 重试次数=0
2025-07-15 13:19:19,608 - INFO - 🔄 执行UPDATE操作: entry_id=145177 -> external_id=603683
2025-07-15 13:19:19,608 - INFO - 📦 更新数据: {'employee_id': '215829', 'entry_date': '2025/07/15', 'model': '5', 'number': None, 'factory_number': None, 'project_number': None, 'unit_number': None, 'category': '5', 'item': '5', 'duration': 0.1, 'department': '131'}
2025-07-15 13:19:19,608 - INFO - 📤 发送到Server6的更新数据: {'従業員ｺｰﾄﾞ': '215829', '日付': '2025-07-15', '機種': '5', '号機': 'NULL', '工場製番': 'NULL', '工事番号': 'NULL', 'ﾕﾆｯﾄ番号': 'NULL', '区分': '5', '項目': '5', '時間': 0.1, '所属ｺｰﾄﾞ': '131'}
2025-07-15 13:19:23,284 - INFO - ✅ UPDATE完成: entry_id=145177 -> external_id=603683
2025-07-15 13:19:27,077 - INFO - ✅ UPDATE完成: entry_id=145177 -> external_id=603683
2025-07-15 13:21:09,538 - INFO - 📋 2025/07/03 + 16:10 + 处理队列项: UPDATE - entry_id=145177 - queue_id=150 - 重试次数=0
2025-07-15 13:21:09,563 - INFO - 🔄 执行UPDATE操作: entry_id=145177 -> external_id=603683
2025-07-15 13:21:09,563 - INFO - 📦 更新数据: {'employee_id': '215829', 'entry_date': '2025/07/15', 'model': '5', 'number': None, 'factory_number': None, 'project_number': None, 'unit_number': None, 'category': '5', 'item': '5', 'duration': 0.1, 'department': '131'}
2025-07-15 13:21:09,563 - INFO - 📤 发送到Server6的更新数据: {'従業員ｺｰﾄﾞ': '215829', '日付': '2025-07-15', '機種': '5', '号機': 'NULL', '工場製番': 'NULL', '工事番号': 'NULL', 'ﾕﾆｯﾄ番号': 'NULL', '区分': '5', '項目': '5', '時間': 0.1, '所属ｺｰﾄﾞ': '131'}
2025-07-15 13:21:13,364 - INFO - ✅ UPDATE完成: entry_id=145177 -> external_id=603683
2025-07-15 13:43:38,703 - INFO - 📋 2025/07/03 + 16:10 + 处理队列项: UPDATE - entry_id=145177 - queue_id=151 - 重试次数=0
2025-07-15 13:43:38,720 - INFO - 🔄 执行UPDATE操作: entry_id=145177 -> external_id=603683
2025-07-15 13:43:38,721 - INFO - 📦 更新数据: {'employee_id': '215829', 'entry_date': '2025/07/15', 'model': '5', 'number': None, 'factory_number': None, 'project_number': None, 'unit_number': None, 'category': '5', 'item': '5', 'duration': 0.1, 'department': '131'}
2025-07-15 13:43:38,721 - INFO - 📤 发送到Server6的更新数据: {'従業員ｺｰﾄﾞ': '215829', '日付': '2025-07-15', '機種': '5', '号機': 'NULL', '工場製番': 'NULL', '工事番号': 'NULL', 'ﾕﾆｯﾄ番号': 'NULL', '区分': '5', '項目': '5', '時間': 0.1, '所属ｺｰﾄﾞ': '131'}
2025-07-15 13:43:43,423 - INFO - ✅ UPDATE完成: entry_id=145177 -> external_id=603683
2025-07-15 13:44:10,780 - INFO - 📋 2025/07/03 + 16:10 + 处理队列项: UPDATE - entry_id=145177 - queue_id=152 - 重试次数=0
2025-07-15 13:44:10,799 - INFO - 🔄 执行UPDATE操作: entry_id=145177 -> external_id=603683
2025-07-15 13:44:10,799 - INFO - 📦 更新数据: {'employee_id': '215829', 'entry_date': '2025/07/15', 'model': '5', 'number': None, 'factory_number': None, 'project_number': 'q', 'unit_number': None, 'category': '5', 'item': '5', 'duration': 0.1, 'department': '131'}
2025-07-15 13:44:10,799 - INFO - 📤 发送到Server6的更新数据: {'従業員ｺｰﾄﾞ': '215829', '日付': '2025-07-15', '機種': '5', '号機': 'NULL', '工場製番': 'NULL', '工事番号': 'q', 'ﾕﾆｯﾄ番号': 'NULL', '区分': '5', '項目': '5', '時間': 0.1, '所属ｺｰﾄﾞ': '131'}
2025-07-15 13:44:14,498 - INFO - ✅ UPDATE完成: entry_id=145177 -> external_id=603683
2025-07-15 13:45:01,601 - INFO - 📋 2025/07/03 + 16:10 + 处理队列项: UPDATE - entry_id=145177 - queue_id=153 - 重试次数=0
2025-07-15 13:45:01,625 - INFO - 🔄 执行UPDATE操作: entry_id=145177 -> external_id=603683
2025-07-15 13:45:01,625 - INFO - 📦 更新数据: {'employee_id': '215829', 'entry_date': '2025/07/15', 'model': '1', 'number': None, 'factory_number': None, 'project_number': 'q', 'unit_number': None, 'category': '1', 'item': '5', 'duration': 0.1, 'department': '131'}
2025-07-15 13:45:01,625 - INFO - 📤 发送到Server6的更新数据: {'従業員ｺｰﾄﾞ': '215829', '日付': '2025-07-15', '機種': '1', '号機': 'NULL', '工場製番': 'NULL', '工事番号': 'q', 'ﾕﾆｯﾄ番号': 'NULL', '区分': '1', '項目': '5', '時間': 0.1, '所属ｺｰﾄﾞ': '131'}
2025-07-15 13:45:06,426 - INFO - ✅ UPDATE完成: entry_id=145177 -> external_id=603683
2025-07-15 13:50:57,210 - INFO - 📋 2025/07/03 + 16:10 + 处理队列项: UPDATE - entry_id=145177 - queue_id=154 - 重试次数=0
2025-07-15 13:50:57,231 - INFO - 🔄 执行UPDATE操作: entry_id=145177 -> external_id=603683
2025-07-15 13:50:57,232 - INFO - 📦 更新数据: {'employee_id': '215829', 'entry_date': '2025/07/15', 'model': '1', 'number': None, 'factory_number': None, 'project_number': 'q', 'unit_number': None, 'category': '1', 'item': '5', 'duration': 0.1, 'department': '131'}
2025-07-15 13:50:57,232 - INFO - 📤 发送到Server6的更新数据: {'従業員ｺｰﾄﾞ': '215829', '日付': '2025-07-15', '機種': '1', '号機': 'NULL', '工場製番': 'NULL', '工事番号': 'q', 'ﾕﾆｯﾄ番号': 'NULL', '区分': '1', '項目': '5', '時間': 0.1, '所属ｺｰﾄﾞ': '131'}
2025-07-15 13:51:02,117 - INFO - ✅ UPDATE完成: entry_id=145177 -> external_id=603683
2025-07-15 13:51:32,769 - INFO - 监听任务被取消
2025-07-15 13:51:32,771 - INFO - 🔌 MongoDB连接已关闭
2025-07-15 13:51:32,772 - INFO - 🔌 MongoDB连接已关闭
2025-07-15 13:51:32,774 - INFO - 🔌 Redis连接已关闭
2025-07-15 13:51:32,775 - INFO - 🔌 PostgreSQL连接已关闭
2025-07-15 13:51:32,775 - INFO - 🔌 Redis连接已关闭
2025-07-15 13:51:32,775 - INFO - 🔌 PostgreSQL连接已关闭
2025-07-15 13:51:32,775 - INFO - 🔌 PostgreSQL连接已关闭
2025-07-15 13:51:32,775 - INFO - 🔌 f1监听器服务已停止
2025-07-15 13:51:32,775 - INFO - 🔌 f4操作处理服务已停止
2025-07-15 13:51:32,775 - INFO - 🔌 Redis连接已关闭
2025-07-15 13:51:32,775 - INFO - 🔌 f3数据拉取服务已停止
2025-07-15 13:51:32,875 - INFO - 🔌 推送工作线程停止: worker_1
2025-07-15 13:51:32,899 - INFO - 🔌 推送工作线程停止: worker_3
2025-07-15 13:51:32,917 - INFO - 🔌 推送工作线程停止: worker_2
2025-07-15 13:51:32,926 - INFO - 🔌 推送工作线程停止: worker_4
2025-07-15 13:51:32,928 - INFO - 🔌 MongoDB连接已关闭
2025-07-15 13:51:32,928 - INFO - AIOHTTP session for Server6 closed.
2025-07-15 13:51:32,928 - INFO - 🔌 Redis连接已关闭
2025-07-15 13:51:32,933 - INFO - 🔌 PostgreSQL连接已关闭
2025-07-15 13:51:32,933 - INFO - 🔌 f2推送回写服务已停止
2025-07-15 13:51:32,933 - ERROR - Server5微服务启动失败: asyncio.run() cannot be called from a running event loop
2025-07-15 13:51:33,310 - INFO - 🔌 PostgreSQL连接已关闭
2025-07-15 13:51:33,310 - INFO - 🔌 MongoDB连接已关闭
2025-07-15 13:51:33,310 - INFO - 🔌 PostgreSQL连接已关闭
2025-07-15 13:51:33,311 - INFO - 🔌 MongoDB连接已关闭
2025-07-15 13:51:33,311 - INFO - 🔌 PostgreSQL连接已关闭
2025-07-15 13:51:33,311 - INFO - 🔌 MongoDB连接已关闭
2025-07-15 13:51:33,312 - INFO - 🔌 Redis连接已关闭
2025-07-15 13:51:33,312 - INFO - 🔌 Redis连接已关闭
2025-07-15 13:51:33,312 - INFO - 🔌 Redis连接已关闭
2025-07-15 13:51:33,312 - INFO - 🔌 f1监听器服务已停止
2025-07-15 13:51:33,313 - INFO - 🔌 f2推送回写服务已停止
2025-07-15 13:51:33,313 - INFO - 🔌 f4操作处理服务已停止
2025-07-15 13:51:33,334 - ERROR - Task was destroyed but it is pending!
task: <Task pending name='Task-4080' coro=<Connection._cancel() running at /home/<USER>/miniconda3/envs/my_suite_unified/lib/python3.12/site-packages/asyncpg/connection.py:1646> wait_for=<Future pending cb=[Task.task_wakeup()]>>
2025-07-15 13:51:34,546 - INFO - 🔊 f1监听器服务初始化完成
2025-07-15 13:51:34,547 - INFO - 📡 Server6客户端初始化 (新版): http://************:8019
2025-07-15 13:51:34,547 - INFO - 📤 f2推送回写引擎初始化完成 (修复版本: 直接轮询PostgreSQL)
2025-07-15 13:51:34,547 - INFO - 📡 Server6客户端初始化 (新版): http://************:8019
2025-07-15 13:51:34,548 - INFO - 📥 f3数据拉取器初始化完成 (Staging & UPSERT 模式)
2025-07-15 13:51:34,548 - INFO - 🎮 f4操作处理器初始化完成
2025-07-15 13:51:34,557 - INFO - Redis连接成功
2025-07-15 13:51:34,558 - INFO - Redis连接成功
2025-07-15 13:51:34,558 - INFO - Redis连接成功
2025-07-15 13:51:34,564 - INFO - ✅ MongoDB连接成功
2025-07-15 13:51:34,564 - INFO - ✅ MongoDB连接成功
2025-07-15 13:51:34,566 - INFO - ✅ MongoDB连接成功
2025-07-15 13:51:34,776 - INFO - ✅ PostgreSQL连接池创建成功
2025-07-15 13:51:34,777 - INFO - Redis连接成功
2025-07-15 13:51:34,777 - INFO - ✅ f3数据拉取服务启动成功，预定执行时间: ['02:00', '03:00']
2025-07-15 13:51:34,777 - INFO - 🚀 数据拉取调度器启动，预定运行时间: ['02:00', '03:00']
2025-07-15 13:51:34,777 - INFO - 下一次f3/f5同步任务将在 2025-07-16 02:00:00 执行，等待 43705 秒...
2025-07-15 13:51:34,838 - INFO - ✅ PostgreSQL连接池创建成功
2025-07-15 13:51:34,839 - INFO - ✅ f1监听器服务启动成功
2025-07-15 13:51:34,859 - INFO - ✅ 分区创建成功: entries_2025_07
2025-07-15 13:51:34,879 - INFO - ✅ PostgreSQL连接池创建成功
2025-07-15 13:51:34,879 - INFO - ✅ f2推送回写服务启动成功，工作线程数: 4
2025-07-15 13:51:34,879 - INFO - 🚀 推送工作线程启动: worker_1
2025-07-15 13:51:34,879 - INFO - 🚀 推送工作线程启动: worker_2
2025-07-15 13:51:34,879 - INFO - 🚀 推送工作线程启动: worker_3
2025-07-15 13:51:34,880 - INFO - 🚀 推送工作线程启动: worker_4
2025-07-15 13:51:34,881 - INFO - ✅ 分区创建成功: entries_2025_08
2025-07-15 13:51:34,884 - INFO - ✅ PostgreSQL连接池创建成功
2025-07-15 13:51:34,884 - INFO - ✅ f4操作处理服务启动成功
2025-07-15 13:51:34,943 - INFO - 📡 开始监听频道: push_job
2025-07-15 13:51:34,945 - INFO - 📡 开始监听频道: partition_check
2025-07-15 13:51:34,946 - INFO - 📡 开始监听频道: sync_trigger
2025-07-15 13:52:04,951 - INFO - 📋 2025/07/03 + 16:10 + 处理队列项: UPDATE - entry_id=145177 - queue_id=155 - 重试次数=0
2025-07-15 13:52:05,047 - INFO - 🔄 执行UPDATE操作: entry_id=145177 -> external_id=603683
2025-07-15 13:52:05,047 - INFO - 📦 更新数据: {'employee_id': '215829', 'entry_date': '2025/07/15', 'model': '1', 'number': None, 'factory_number': None, 'project_number': 'q', 'unit_number': None, 'category': '1', 'item': '5', 'duration': 0.1, 'department': '131'}
2025-07-15 13:52:05,047 - INFO - 📤 发送到Server6的更新数据: {'従業員ｺｰﾄﾞ': '215829', '日付': '2025-07-15', '機種': '1', '号機': 'NULL', '工場製番': 'NULL', '工事番号': 'q', 'ﾕﾆｯﾄ番号': 'NULL', '区分': '1', '項目': '5', '時間': 0.1, '所属ｺｰﾄﾞ': '131'}
2025-07-15 13:52:05,047 - INFO - AIOHTTP session for Server6 created.
2025-07-15 13:52:09,697 - INFO - ✅ UPDATE完成: entry_id=145177 -> external_id=603683, source已更新为'system'
2025-07-15 13:54:11,213 - INFO - 📋 2025/07/03 + 16:10 + 处理队列项: UPDATE - entry_id=145177 - queue_id=156 - 重试次数=0
2025-07-15 13:54:11,312 - INFO - 🔄 执行UPDATE操作: entry_id=145177 -> external_id=603683
2025-07-15 13:54:11,312 - INFO - 📦 更新数据: {'employee_id': '215829', 'entry_date': '2025/07/15', 'model': '1', 'number': None, 'factory_number': None, 'project_number': 'q', 'unit_number': None, 'category': '1', 'item': '5', 'duration': 0.2, 'department': '131'}
2025-07-15 13:54:11,312 - INFO - 📤 发送到Server6的更新数据: {'従業員ｺｰﾄﾞ': '215829', '日付': '2025-07-15', '機種': '1', '号機': 'NULL', '工場製番': 'NULL', '工事番号': 'q', 'ﾕﾆｯﾄ番号': 'NULL', '区分': '1', '項目': '5', '時間': 0.2, '所属ｺｰﾄﾞ': '131'}
2025-07-15 13:54:11,370 - INFO - 📋 2025/07/03 + 16:10 + 处理队列项: UPDATE - entry_id=145177 - queue_id=157 - 重试次数=0
2025-07-15 13:54:11,477 - INFO - 🔄 执行UPDATE操作: entry_id=145177 -> external_id=603683
2025-07-15 13:54:11,477 - INFO - 📦 更新数据: {'employee_id': '215829', 'entry_date': '2025/07/15', 'model': '1', 'number': None, 'factory_number': None, 'project_number': 'q', 'unit_number': None, 'category': '1', 'item': '5', 'duration': 0.2, 'department': '131'}
2025-07-15 13:54:11,477 - INFO - 📤 发送到Server6的更新数据: {'従業員ｺｰﾄﾞ': '215829', '日付': '2025-07-15', '機種': '1', '号機': 'NULL', '工場製番': 'NULL', '工事番号': 'q', 'ﾕﾆｯﾄ番号': 'NULL', '区分': '1', '項目': '5', '時間': 0.2, '所属ｺｰﾄﾞ': '131'}
2025-07-15 13:54:17,250 - INFO - ✅ UPDATE完成: entry_id=145177 -> external_id=603683, source已更新为'system'
2025-07-15 13:54:22,304 - INFO - ✅ UPDATE完成: entry_id=145177 -> external_id=603683, source已更新为'system'
2025-07-15 14:05:46,544 - INFO - 监听任务被取消
2025-07-15 14:05:46,546 - INFO - 🔌 MongoDB连接已关闭
2025-07-15 14:05:46,547 - INFO - 🔌 MongoDB连接已关闭
2025-07-15 14:05:46,548 - INFO - 🔌 Redis连接已关闭
2025-07-15 14:05:46,548 - INFO - 🔌 PostgreSQL连接已关闭
2025-07-15 14:05:46,548 - INFO - 🔌 Redis连接已关闭
2025-07-15 14:05:46,548 - INFO - 🔌 PostgreSQL连接已关闭
2025-07-15 14:05:46,549 - INFO - 🔌 PostgreSQL连接已关闭
2025-07-15 14:05:46,549 - INFO - 🔌 f1监听器服务已停止
2025-07-15 14:05:46,549 - INFO - 🔌 f4操作处理服务已停止
2025-07-15 14:05:46,549 - INFO - 🔌 Redis连接已关闭
2025-07-15 14:05:46,549 - INFO - 🔌 f3数据拉取服务已停止
2025-07-15 14:05:46,652 - INFO - 🔌 推送工作线程停止: worker_4
2025-07-15 14:05:46,691 - INFO - 🔌 推送工作线程停止: worker_1
2025-07-15 14:05:46,728 - INFO - 🔌 推送工作线程停止: worker_2
2025-07-15 14:05:46,762 - INFO - 🔌 推送工作线程停止: worker_3
2025-07-15 14:05:46,763 - INFO - 🔌 MongoDB连接已关闭
2025-07-15 14:05:46,763 - INFO - AIOHTTP session for Server6 closed.
2025-07-15 14:05:46,763 - INFO - 🔌 Redis连接已关闭
2025-07-15 14:05:46,768 - INFO - 🔌 PostgreSQL连接已关闭
2025-07-15 14:05:46,768 - INFO - 🔌 f2推送回写服务已停止
2025-07-15 14:05:46,769 - ERROR - Server5微服务启动失败: asyncio.run() cannot be called from a running event loop
2025-07-15 14:05:47,143 - INFO - 🔌 PostgreSQL连接已关闭
2025-07-15 14:05:47,143 - INFO - 🔌 MongoDB连接已关闭
2025-07-15 14:05:47,143 - INFO - 🔌 PostgreSQL连接已关闭
2025-07-15 14:05:47,143 - INFO - 🔌 MongoDB连接已关闭
2025-07-15 14:05:47,143 - INFO - 🔌 PostgreSQL连接已关闭
2025-07-15 14:05:47,143 - INFO - 🔌 MongoDB连接已关闭
2025-07-15 14:05:47,144 - INFO - 🔌 Redis连接已关闭
2025-07-15 14:05:47,144 - INFO - 🔌 Redis连接已关闭
2025-07-15 14:05:47,144 - INFO - 🔌 Redis连接已关闭
2025-07-15 14:05:47,144 - INFO - 🔌 f1监听器服务已停止
2025-07-15 14:05:47,144 - INFO - 🔌 f2推送回写服务已停止
2025-07-15 14:05:47,144 - INFO - 🔌 f4操作处理服务已停止
2025-07-15 14:05:48,038 - INFO - 🔊 f1监听器服务初始化完成
2025-07-15 14:05:48,038 - INFO - 📡 Server6客户端初始化 (新版): http://************:8019
2025-07-15 14:05:48,038 - INFO - 📤 f2推送回写引擎初始化完成 (修复版本: 直接轮询PostgreSQL)
2025-07-15 14:05:48,038 - INFO - 📡 Server6客户端初始化 (新版): http://************:8019
2025-07-15 14:05:48,039 - INFO - 📥 f3数据拉取器初始化完成 (Staging & UPSERT 模式)
2025-07-15 14:05:48,039 - INFO - 🎮 f4操作处理器初始化完成
2025-07-15 14:05:48,049 - INFO - Redis连接成功
2025-07-15 14:05:48,049 - INFO - Redis连接成功
2025-07-15 14:05:48,049 - INFO - Redis连接成功
2025-07-15 14:05:48,055 - INFO - ✅ MongoDB连接成功
2025-07-15 14:05:48,056 - INFO - ✅ MongoDB连接成功
2025-07-15 14:05:48,057 - INFO - ✅ MongoDB连接成功
2025-07-15 14:05:48,340 - INFO - ✅ PostgreSQL连接池创建成功
2025-07-15 14:05:48,341 - INFO - Redis连接成功
2025-07-15 14:05:48,341 - INFO - ✅ f3数据拉取服务启动成功，预定执行时间: ['02:00', '03:00']
2025-07-15 14:05:48,341 - INFO - 🚀 数据拉取调度器启动，预定运行时间: ['02:00', '03:00']
2025-07-15 14:05:48,341 - INFO - 下一次f3/f5同步任务将在 2025-07-16 02:00:00 执行，等待 42852 秒...
2025-07-15 14:05:48,345 - INFO - ✅ PostgreSQL连接池创建成功
2025-07-15 14:05:48,345 - INFO - ✅ f1监听器服务启动成功
2025-07-15 14:05:48,354 - INFO - ✅ 分区创建成功: entries_2025_07
2025-07-15 14:05:48,358 - INFO - ✅ 分区创建成功: entries_2025_08
2025-07-15 14:05:48,388 - INFO - ✅ PostgreSQL连接池创建成功
2025-07-15 14:05:48,388 - INFO - ✅ f2推送回写服务启动成功，工作线程数: 4
2025-07-15 14:05:48,388 - INFO - 🚀 推送工作线程启动: worker_1
2025-07-15 14:05:48,389 - INFO - 🚀 推送工作线程启动: worker_2
2025-07-15 14:05:48,389 - INFO - 🚀 推送工作线程启动: worker_3
2025-07-15 14:05:48,389 - INFO - 🚀 推送工作线程启动: worker_4
2025-07-15 14:05:48,425 - INFO - ✅ PostgreSQL连接池创建成功
2025-07-15 14:05:48,425 - INFO - ✅ f4操作处理服务启动成功
2025-07-15 14:05:48,487 - INFO - 📡 开始监听频道: push_job
2025-07-15 14:05:48,489 - INFO - 📡 开始监听频道: partition_check
2025-07-15 14:05:48,490 - INFO - 📡 开始监听频道: sync_trigger
2025-07-15 14:06:30,488 - INFO - 📋 2025/07/03 + 16:10 + 处理队列项: UPDATE - entry_id=145177 - queue_id=158 - 重试次数=0
2025-07-15 14:06:30,488 - INFO - 📋 2025/07/03 + 16:10 + 处理队列项: UPDATE - entry_id=145177 - queue_id=159 - 重试次数=0
2025-07-15 14:06:30,600 - INFO - 🔄 执行UPDATE操作: entry_id=145177 -> external_id=603683
2025-07-15 14:06:30,600 - INFO - 📦 更新数据: {'employee_id': '215829', 'entry_date': '2025/07/15', 'model': '1', 'number': None, 'factory_number': None, 'project_number': 'q', 'unit_number': None, 'category': '1', 'item': '5', 'duration': 0.2, 'department': '131'}
2025-07-15 14:06:30,600 - INFO - 📤 发送到Server6的更新数据: {'従業員ｺｰﾄﾞ': '215829', '日付': '2025-07-15', '機種': '1', '号機': 'NULL', '工場製番': 'NULL', '工事番号': 'q', 'ﾕﾆｯﾄ番号': 'NULL', '区分': '1', '項目': '5', '時間': 0.2, '所属ｺｰﾄﾞ': '131'}
2025-07-15 14:06:30,600 - INFO - AIOHTTP session for Server6 created.
2025-07-15 14:06:30,609 - INFO - 🔄 执行UPDATE操作: entry_id=145177 -> external_id=603683
2025-07-15 14:06:30,609 - INFO - 📦 更新数据: {'employee_id': '215829', 'entry_date': '2025/07/15', 'model': '1', 'number': None, 'factory_number': None, 'project_number': 'q', 'unit_number': None, 'category': '1', 'item': '5', 'duration': 0.2, 'department': '131'}
2025-07-15 14:06:30,609 - INFO - 📤 发送到Server6的更新数据: {'従業員ｺｰﾄﾞ': '215829', '日付': '2025-07-15', '機種': '1', '号機': 'NULL', '工場製番': 'NULL', '工事番号': 'q', 'ﾕﾆｯﾄ番号': 'NULL', '区分': '1', '項目': '5', '時間': 0.2, '所属ｺｰﾄﾞ': '131'}
2025-07-15 14:06:35,422 - INFO - ✅ UPDATE完成: entry_id=145177 -> external_id=603683, source已更新为'system'
2025-07-15 14:06:41,926 - INFO - ✅ UPDATE完成: entry_id=145177 -> external_id=603683, source已更新为'system'
2025-07-15 14:15:00,644 - INFO - 监听任务被取消
2025-07-15 14:15:00,648 - INFO - 🔌 MongoDB连接已关闭
2025-07-15 14:15:00,650 - INFO - 🔌 MongoDB连接已关闭
2025-07-15 14:15:00,651 - INFO - 🔌 Redis连接已关闭
2025-07-15 14:15:00,651 - INFO - 🔌 PostgreSQL连接已关闭
2025-07-15 14:15:00,651 - INFO - 🔌 Redis连接已关闭
2025-07-15 14:15:00,651 - INFO - 🔌 PostgreSQL连接已关闭
2025-07-15 14:15:00,651 - INFO - 🔌 PostgreSQL连接已关闭
2025-07-15 14:15:00,651 - INFO - 🔌 推送工作线程停止: worker_1
2025-07-15 14:15:00,651 - INFO - 🔌 f1监听器服务已停止
2025-07-15 14:15:00,651 - INFO - 🔌 f4操作处理服务已停止
2025-07-15 14:15:00,652 - INFO - 🔌 推送工作线程停止: worker_4
2025-07-15 14:15:00,652 - INFO - 🔌 Redis连接已关闭
2025-07-15 14:15:00,652 - INFO - 🔌 f3数据拉取服务已停止
2025-07-15 14:15:00,764 - INFO - 🔌 推送工作线程停止: worker_2
2025-07-15 14:15:00,786 - INFO - 🔌 推送工作线程停止: worker_3
2025-07-15 14:15:00,787 - INFO - 🔌 MongoDB连接已关闭
2025-07-15 14:15:00,787 - INFO - AIOHTTP session for Server6 closed.
2025-07-15 14:15:00,787 - INFO - 🔌 Redis连接已关闭
2025-07-15 14:15:00,792 - INFO - 🔌 PostgreSQL连接已关闭
2025-07-15 14:15:00,792 - INFO - 🔌 f2推送回写服务已停止
2025-07-15 14:15:00,793 - ERROR - Server5微服务启动失败: asyncio.run() cannot be called from a running event loop
2025-07-15 14:15:01,239 - INFO - 🔌 PostgreSQL连接已关闭
2025-07-15 14:15:01,240 - INFO - 🔌 MongoDB连接已关闭
2025-07-15 14:15:01,240 - INFO - 🔌 PostgreSQL连接已关闭
2025-07-15 14:15:01,240 - INFO - 🔌 MongoDB连接已关闭
2025-07-15 14:15:01,241 - INFO - 🔌 PostgreSQL连接已关闭
2025-07-15 14:15:01,241 - INFO - 🔌 MongoDB连接已关闭
2025-07-15 14:15:01,241 - INFO - 🔌 Redis连接已关闭
2025-07-15 14:15:01,242 - INFO - 🔌 Redis连接已关闭
2025-07-15 14:15:01,242 - INFO - 🔌 Redis连接已关闭
2025-07-15 14:15:01,242 - INFO - 🔌 f1监听器服务已停止
2025-07-15 14:15:01,242 - INFO - 🔌 f2推送回写服务已停止
2025-07-15 14:15:01,242 - INFO - 🔌 f4操作处理服务已停止
2025-07-15 14:15:03,036 - INFO - 🔊 f1监听器服务初始化完成
2025-07-15 14:15:03,036 - INFO - 📡 Server6客户端初始化 (新版): http://************:8019
2025-07-15 14:15:03,036 - INFO - 📤 f2推送回写引擎初始化完成 (修复版本: 直接轮询PostgreSQL)
2025-07-15 14:15:03,036 - INFO - 📡 Server6客户端初始化 (新版): http://************:8019
2025-07-15 14:15:03,037 - INFO - 📥 f3数据拉取器初始化完成 (Staging & UPSERT 模式)
2025-07-15 14:15:03,037 - INFO - 🎮 f4操作处理器初始化完成
2025-07-15 14:15:03,046 - INFO - Redis连接成功
2025-07-15 14:15:03,046 - INFO - Redis连接成功
2025-07-15 14:15:03,047 - INFO - Redis连接成功
2025-07-15 14:15:03,052 - INFO - ✅ MongoDB连接成功
2025-07-15 14:15:03,053 - INFO - ✅ MongoDB连接成功
2025-07-15 14:15:03,056 - INFO - ✅ MongoDB连接成功
2025-07-15 14:15:03,375 - INFO - ✅ PostgreSQL连接池创建成功
2025-07-15 14:15:03,375 - INFO - ✅ f1监听器服务启动成功
2025-07-15 14:15:03,385 - INFO - ✅ 分区创建成功: entries_2025_07
2025-07-15 14:15:03,388 - INFO - ✅ 分区创建成功: entries_2025_08
2025-07-15 14:15:03,468 - INFO - ✅ PostgreSQL连接池创建成功
2025-07-15 14:15:03,469 - INFO - ✅ f2推送回写服务启动成功，工作线程数: 4
2025-07-15 14:15:03,469 - INFO - 🚀 推送工作线程启动: worker_1
2025-07-15 14:15:03,469 - INFO - 🚀 推送工作线程启动: worker_2
2025-07-15 14:15:03,469 - INFO - 🚀 推送工作线程启动: worker_3
2025-07-15 14:15:03,470 - INFO - 🚀 推送工作线程启动: worker_4
2025-07-15 14:15:03,474 - INFO - ✅ PostgreSQL连接池创建成功
2025-07-15 14:15:03,492 - INFO - Redis连接成功
2025-07-15 14:15:03,492 - INFO - ✅ f3数据拉取服务启动成功，预定执行时间: ['02:00', '03:00']
2025-07-15 14:15:03,493 - INFO - 🚀 数据拉取调度器启动，预定运行时间: ['02:00', '03:00']
2025-07-15 14:15:03,493 - INFO - 下一次f3/f5同步任务将在 2025-07-16 02:00:00 执行，等待 42297 秒...
2025-07-15 14:15:03,513 - INFO - ✅ PostgreSQL连接池创建成功
2025-07-15 14:15:03,513 - INFO - ✅ f4操作处理服务启动成功
2025-07-15 14:15:03,519 - INFO - 📡 开始监听频道: push_job
2025-07-15 14:15:03,521 - INFO - 📡 开始监听频道: partition_check
2025-07-15 14:15:03,522 - INFO - 📡 开始监听频道: sync_trigger
2025-07-15 14:15:35,571 - INFO - 📋 2025/07/03 + 16:10 + 处理队列项: UPDATE - entry_id=145177 - queue_id=160 - 重试次数=0
2025-07-15 14:15:35,571 - INFO - 📋 2025/07/03 + 16:10 + 处理队列项: UPDATE - entry_id=145177 - queue_id=161 - 重试次数=0
2025-07-15 14:15:35,677 - INFO - 🔄 执行UPDATE操作: entry_id=145177 -> external_id=603683
2025-07-15 14:15:35,677 - INFO - 📦 更新数据: {'employee_id': '215829', 'entry_date': '2025/07/15', 'model': None, 'number': None, 'factory_number': None, 'project_number': None, 'unit_number': None, 'category': '8', 'item': '9', 'duration': 0.2, 'department': '131'}
2025-07-15 14:15:35,677 - INFO - 📤 发送到Server6的更新数据: {'従業員ｺｰﾄﾞ': '215829', '日付': '2025-07-15', '機種': 'NULL', '号機': 'NULL', '工場製番': 'NULL', '工事番号': 'NULL', 'ﾕﾆｯﾄ番号': 'NULL', '区分': '8', '項目': '9', '時間': 0.2, '所属ｺｰﾄﾞ': '131'}
2025-07-15 14:15:35,678 - INFO - AIOHTTP session for Server6 created.
2025-07-15 14:15:35,678 - INFO - 🔄 执行UPDATE操作: entry_id=145177 -> external_id=603683
2025-07-15 14:15:35,678 - INFO - 📦 更新数据: {'employee_id': '215829', 'entry_date': '2025/07/15', 'model': None, 'number': None, 'factory_number': None, 'project_number': None, 'unit_number': None, 'category': '8', 'item': '9', 'duration': 0.2, 'department': '131'}
2025-07-15 14:15:35,678 - INFO - 📤 发送到Server6的更新数据: {'従業員ｺｰﾄﾞ': '215829', '日付': '2025-07-15', '機種': 'NULL', '号機': 'NULL', '工場製番': 'NULL', '工事番号': 'NULL', 'ﾕﾆｯﾄ番号': 'NULL', '区分': '8', '項目': '9', '時間': 0.2, '所属ｺｰﾄﾞ': '131'}
2025-07-15 14:15:40,390 - INFO - ✅ UPDATE完成: entry_id=145177 -> external_id=603683, source已更新为'system'
2025-07-15 14:15:46,182 - INFO - ✅ UPDATE完成: entry_id=145177 -> external_id=603683, source已更新为'system'
2025-07-15 14:17:12,399 - INFO - 📋 2025/07/03 + 16:10 + 处理队列项: UPDATE - entry_id=145177 - queue_id=162 - 重试次数=0
2025-07-15 14:17:12,415 - WARNING - ⚠️ UPDATE操作跳过: entry_id=145177 (记录已被删除)
2025-07-15 14:17:12,416 - INFO - ✅ UPDATE跳过完成: entry_id=145177 (记录已被删除)
2025-07-15 14:17:12,422 - INFO - 📋 2025/07/03 + 16:10 + 处理队列项: DELETE - entry_id=145177 - queue_id=163 - 重试次数=0
2025-07-15 14:17:12,423 - INFO - 🔄 执行DELETE操作: entry_id=145177, external_id=603683
2025-07-15 14:17:16,352 - INFO - ✅ DELETE完成: entry_id=145177, external_id=603683
2025-07-15 14:17:16,354 - INFO - ℹ️ DELETE操作跳过f6同步: entry_id=145177 (记录已删除)
2025-07-15 14:18:26,489 - INFO - 📋 2025/07/03 + 16:10 + 处理队列项: INSERT - entry_id=145178 - queue_id=164 - 重试次数=0
2025-07-15 14:18:26,507 - INFO - 🔄 执行INSERT操作: entry_id=145178, employee_id=215829
2025-07-15 14:18:26,507 - INFO - 📤 发送到Server6的数据: {'従業員ｺｰﾄﾞ': '215829', '日付': '2025-07-16', '機種': 'NULL', '号機': 'NULL', '工場製番': 'NULL', '工事番号': 'NULL', 'ﾕﾆｯﾄ番号': 'NULL', '区分': '1', '項目': '1', '時間': 0.1, '所属ｺｰﾄﾞ': '131'}
2025-07-15 14:18:30,907 - INFO - ✅ INSERT完成: entry_id=145178 -> external_id=603684
2025-07-15 14:19:08,155 - INFO - 📋 2025/07/03 + 16:10 + 处理队列项: UPDATE - entry_id=145178 - queue_id=165 - 重试次数=0
2025-07-15 14:19:08,188 - INFO - 📋 2025/07/03 + 16:10 + 处理队列项: UPDATE - entry_id=145178 - queue_id=166 - 重试次数=0
2025-07-15 14:19:08,260 - INFO - 🔄 执行UPDATE操作: entry_id=145178 -> external_id=603684
2025-07-15 14:19:08,260 - INFO - 📦 更新数据: {'employee_id': '215829', 'entry_date': '2025/07/16', 'model': None, 'number': None, 'factory_number': None, 'project_number': None, 'unit_number': None, 'category': '1', 'item': '1', 'duration': 2.1, 'department': '131'}
2025-07-15 14:19:08,260 - INFO - 📤 发送到Server6的更新数据: {'従業員ｺｰﾄﾞ': '215829', '日付': '2025-07-16', '機種': 'NULL', '号機': 'NULL', '工場製番': 'NULL', '工事番号': 'NULL', 'ﾕﾆｯﾄ番号': 'NULL', '区分': '1', '項目': '1', '時間': 2.1, '所属ｺｰﾄﾞ': '131'}
2025-07-15 14:19:08,294 - INFO - 🔄 执行UPDATE操作: entry_id=145178 -> external_id=603684
2025-07-15 14:19:08,294 - INFO - 📦 更新数据: {'employee_id': '215829', 'entry_date': '2025/07/16', 'model': None, 'number': None, 'factory_number': None, 'project_number': None, 'unit_number': None, 'category': '1', 'item': '1', 'duration': 2.1, 'department': '131'}
2025-07-15 14:19:08,294 - INFO - 📤 发送到Server6的更新数据: {'従業員ｺｰﾄﾞ': '215829', '日付': '2025-07-16', '機種': 'NULL', '号機': 'NULL', '工場製番': 'NULL', '工事番号': 'NULL', 'ﾕﾆｯﾄ番号': 'NULL', '区分': '1', '項目': '1', '時間': 2.1, '所属ｺｰﾄﾞ': '131'}
2025-07-15 14:19:12,874 - INFO - ✅ UPDATE完成: entry_id=145178 -> external_id=603684, source已更新为'system'
2025-07-15 14:19:18,466 - INFO - ✅ UPDATE完成: entry_id=145178 -> external_id=603684, source已更新为'system'
2025-07-15 14:28:22,240 - INFO - 监听任务被取消
2025-07-15 14:28:22,243 - INFO - 🔌 MongoDB连接已关闭
2025-07-15 14:28:22,245 - INFO - 🔌 MongoDB连接已关闭
2025-07-15 14:28:22,248 - INFO - 🔌 Redis连接已关闭
2025-07-15 14:28:22,248 - INFO - 🔌 PostgreSQL连接已关闭
2025-07-15 14:28:22,249 - INFO - 🔌 Redis连接已关闭
2025-07-15 14:28:22,249 - INFO - 🔌 PostgreSQL连接已关闭
2025-07-15 14:28:22,249 - INFO - 🔌 PostgreSQL连接已关闭
2025-07-15 14:28:22,249 - INFO - 🔌 f1监听器服务已停止
2025-07-15 14:28:22,249 - INFO - 🔌 f4操作处理服务已停止
2025-07-15 14:28:22,249 - INFO - 🔌 Redis连接已关闭
2025-07-15 14:28:22,249 - INFO - 🔌 f3数据拉取服务已停止
2025-07-15 14:28:22,371 - INFO - 🔌 推送工作线程停止: worker_1
2025-07-15 14:28:22,398 - INFO - 🔌 推送工作线程停止: worker_2
2025-07-15 14:28:22,439 - INFO - 🔌 推送工作线程停止: worker_3
2025-07-15 14:28:22,470 - INFO - 🔌 推送工作线程停止: worker_4
2025-07-15 14:28:22,472 - INFO - 🔌 MongoDB连接已关闭
2025-07-15 14:28:22,473 - INFO - AIOHTTP session for Server6 closed.
2025-07-15 14:28:22,474 - INFO - 🔌 Redis连接已关闭
2025-07-15 14:28:22,478 - INFO - 🔌 PostgreSQL连接已关闭
2025-07-15 14:28:22,478 - INFO - 🔌 f2推送回写服务已停止
2025-07-15 14:28:22,479 - ERROR - Server5微服务启动失败: asyncio.run() cannot be called from a running event loop
2025-07-15 14:28:22,797 - INFO - 🔌 PostgreSQL连接已关闭
2025-07-15 14:28:22,797 - INFO - 🔌 MongoDB连接已关闭
2025-07-15 14:28:22,798 - INFO - 🔌 PostgreSQL连接已关闭
2025-07-15 14:28:22,798 - INFO - 🔌 MongoDB连接已关闭
2025-07-15 14:28:22,798 - INFO - 🔌 PostgreSQL连接已关闭
2025-07-15 14:28:22,799 - INFO - 🔌 MongoDB连接已关闭
2025-07-15 14:28:22,799 - INFO - 🔌 Redis连接已关闭
2025-07-15 14:28:22,800 - INFO - 🔌 Redis连接已关闭
2025-07-15 14:28:22,800 - INFO - 🔌 Redis连接已关闭
2025-07-15 14:28:22,800 - INFO - 🔌 f1监听器服务已停止
2025-07-15 14:28:22,801 - INFO - 🔌 f2推送回写服务已停止
2025-07-15 14:28:22,801 - INFO - 🔌 f4操作处理服务已停止
2025-07-15 14:28:24,154 - INFO - 🔊 f1监听器服务初始化完成
2025-07-15 14:28:24,154 - INFO - 📡 Server6客户端初始化 (新版): http://************:8019
2025-07-15 14:28:24,155 - INFO - 📤 f2推送回写引擎初始化完成 (修复版本: 直接轮询PostgreSQL)
2025-07-15 14:28:24,155 - INFO - 📡 Server6客户端初始化 (新版): http://************:8019
2025-07-15 14:28:24,155 - INFO - 📥 f3数据拉取器初始化完成 (Staging & UPSERT 模式)
2025-07-15 14:28:24,155 - INFO - 🎮 f4操作处理器初始化完成
2025-07-15 14:28:24,165 - INFO - Redis连接成功
2025-07-15 14:28:24,165 - INFO - Redis连接成功
2025-07-15 14:28:24,165 - INFO - Redis连接成功
2025-07-15 14:28:24,170 - INFO - ✅ MongoDB连接成功
2025-07-15 14:28:24,170 - INFO - ✅ MongoDB连接成功
2025-07-15 14:28:24,172 - INFO - ✅ MongoDB连接成功
2025-07-15 14:28:24,413 - INFO - ✅ PostgreSQL连接池创建成功
2025-07-15 14:28:24,414 - INFO - Redis连接成功
2025-07-15 14:28:24,414 - INFO - ✅ f3数据拉取服务启动成功，预定执行时间: ['02:00', '03:00']
2025-07-15 14:28:24,414 - INFO - 🚀 数据拉取调度器启动，预定运行时间: ['02:00', '03:00']
2025-07-15 14:28:24,414 - INFO - 下一次f3/f5同步任务将在 2025-07-16 02:00:00 执行，等待 41496 秒...
2025-07-15 14:28:24,538 - INFO - ✅ PostgreSQL连接池创建成功
2025-07-15 14:28:24,538 - INFO - ✅ f1监听器服务启动成功
2025-07-15 14:28:24,558 - INFO - ✅ PostgreSQL连接池创建成功
2025-07-15 14:28:24,559 - INFO - ✅ f2推送回写服务启动成功，工作线程数: 4
2025-07-15 14:28:24,559 - INFO - 🚀 推送工作线程启动: worker_1
2025-07-15 14:28:24,559 - INFO - 🚀 推送工作线程启动: worker_2
2025-07-15 14:28:24,559 - INFO - 🚀 推送工作线程启动: worker_3
2025-07-15 14:28:24,560 - INFO - 🚀 推送工作线程启动: worker_4
2025-07-15 14:28:24,561 - INFO - ✅ 分区创建成功: entries_2025_07
2025-07-15 14:28:24,563 - INFO - ✅ PostgreSQL连接池创建成功
2025-07-15 14:28:24,564 - INFO - ✅ f4操作处理服务启动成功
2025-07-15 14:28:24,565 - INFO - ✅ 分区创建成功: entries_2025_08
2025-07-15 14:28:24,700 - INFO - 📡 开始监听频道: push_job
2025-07-15 14:28:24,701 - INFO - 📡 开始监听频道: partition_check
2025-07-15 14:28:24,702 - INFO - 📡 开始监听频道: sync_trigger
2025-07-15 14:29:04,643 - INFO - 📋 2025/07/03 + 16:10 + 处理队列项: UPDATE - entry_id=145178 - queue_id=167 - 重试次数=0
2025-07-15 14:29:04,738 - INFO - 🔄 执行UPDATE操作: entry_id=145178 -> external_id=603684
2025-07-15 14:29:04,738 - INFO - 📦 更新数据: {'employee_id': '215829', 'entry_date': '2025/07/16', 'model': None, 'number': None, 'factory_number': None, 'project_number': None, 'unit_number': None, 'category': '2', 'item': '2', 'duration': 2.1, 'department': '131'}
2025-07-15 14:29:04,738 - INFO - 📤 发送到Server6的更新数据: {'従業員ｺｰﾄﾞ': '215829', '日付': '2025-07-16', '機種': 'NULL', '号機': 'NULL', '工場製番': 'NULL', '工事番号': 'NULL', 'ﾕﾆｯﾄ番号': 'NULL', '区分': '2', '項目': '2', '時間': 2.1, '所属ｺｰﾄﾞ': '131'}
2025-07-15 14:29:04,739 - INFO - AIOHTTP session for Server6 created.
2025-07-15 14:29:09,099 - INFO - ✅ UPDATE完成: entry_id=145178 -> external_id=603684, source已更新为'system'
2025-07-15 14:30:36,831 - INFO - 📋 2025/07/03 + 16:10 + 处理队列项: UPDATE - entry_id=145178 - queue_id=168 - 重试次数=0
2025-07-15 14:30:36,929 - WARNING - ⚠️ UPDATE操作跳过: entry_id=145178 (记录已被删除)
2025-07-15 14:30:36,932 - INFO - ✅ UPDATE跳过完成: entry_id=145178 (记录已被删除)
2025-07-15 14:30:36,943 - INFO - 📋 2025/07/03 + 16:10 + 处理队列项: DELETE - entry_id=145178 - queue_id=169 - 重试次数=0
2025-07-15 14:30:36,944 - INFO - 🔄 执行DELETE操作: entry_id=145178, external_id=603684
2025-07-15 14:30:41,615 - INFO - ✅ DELETE完成: entry_id=145178, external_id=603684
2025-07-15 14:30:41,616 - INFO - ℹ️ DELETE操作跳过f6同步: entry_id=145178 (记录已删除)
2025-07-15 14:34:26,994 - INFO - 监听任务被取消
2025-07-15 14:34:26,996 - INFO - 🔌 MongoDB连接已关闭
2025-07-15 14:34:26,997 - INFO - 🔌 MongoDB连接已关闭
2025-07-15 14:34:26,998 - INFO - 🔌 Redis连接已关闭
2025-07-15 14:34:26,998 - INFO - 🔌 PostgreSQL连接已关闭
2025-07-15 14:34:26,998 - INFO - 🔌 Redis连接已关闭
2025-07-15 14:34:26,998 - INFO - 🔌 PostgreSQL连接已关闭
2025-07-15 14:34:26,998 - INFO - 🔌 PostgreSQL连接已关闭
2025-07-15 14:34:26,998 - INFO - 🔌 f1监听器服务已停止
2025-07-15 14:34:26,998 - INFO - 🔌 f4操作处理服务已停止
2025-07-15 14:34:26,999 - INFO - 🔌 Redis连接已关闭
2025-07-15 14:34:26,999 - INFO - 🔌 f3数据拉取服务已停止
2025-07-15 14:34:27,111 - INFO - 🔌 推送工作线程停止: worker_1
2025-07-15 14:34:27,135 - INFO - 🔌 推送工作线程停止: worker_2
2025-07-15 14:34:27,161 - INFO - 🔌 推送工作线程停止: worker_3
2025-07-15 14:34:27,191 - INFO - 🔌 推送工作线程停止: worker_4
2025-07-15 14:34:27,192 - INFO - 🔌 MongoDB连接已关闭
2025-07-15 14:34:27,193 - INFO - AIOHTTP session for Server6 closed.
2025-07-15 14:34:27,193 - INFO - 🔌 Redis连接已关闭
2025-07-15 14:34:27,197 - INFO - 🔌 PostgreSQL连接已关闭
2025-07-15 14:34:27,197 - INFO - 🔌 f2推送回写服务已停止
2025-07-15 14:34:27,198 - ERROR - Server5微服务启动失败: asyncio.run() cannot be called from a running event loop
2025-07-15 14:34:27,693 - INFO - 🔌 PostgreSQL连接已关闭
2025-07-15 14:34:27,693 - INFO - 🔌 MongoDB连接已关闭
2025-07-15 14:34:27,693 - INFO - 🔌 PostgreSQL连接已关闭
2025-07-15 14:34:27,693 - INFO - 🔌 MongoDB连接已关闭
2025-07-15 14:34:27,693 - INFO - 🔌 PostgreSQL连接已关闭
2025-07-15 14:34:27,693 - INFO - 🔌 MongoDB连接已关闭
2025-07-15 14:34:27,693 - INFO - 🔌 Redis连接已关闭
2025-07-15 14:34:27,693 - INFO - 🔌 Redis连接已关闭
2025-07-15 14:34:27,694 - INFO - 🔌 Redis连接已关闭
2025-07-15 14:34:27,694 - INFO - 🔌 f1监听器服务已停止
2025-07-15 14:34:27,694 - INFO - 🔌 f2推送回写服务已停止
2025-07-15 14:34:27,694 - INFO - 🔌 f4操作处理服务已停止
2025-07-15 14:34:27,716 - ERROR - Task was destroyed but it is pending!
task: <Task pending name='Task-805' coro=<Connection._cancel() running at /home/<USER>/miniconda3/envs/my_suite_unified/lib/python3.12/site-packages/asyncpg/connection.py:1646> wait_for=<Future pending cb=[Task.task_wakeup()]>>
2025-07-15 14:34:28,392 - INFO - 🔊 f1监听器服务初始化完成
2025-07-15 14:34:28,392 - INFO - 📡 Server6客户端初始化 (新版): http://************:8019
2025-07-15 14:34:28,392 - INFO - 📤 f2推送回写引擎初始化完成 (修复版本: 直接轮询PostgreSQL)
2025-07-15 14:34:28,392 - INFO - 📡 Server6客户端初始化 (新版): http://************:8019
2025-07-15 14:34:28,393 - INFO - 📥 f3数据拉取器初始化完成 (Staging & UPSERT 模式)
2025-07-15 14:34:28,393 - INFO - 🎮 f4操作处理器初始化完成
2025-07-15 14:34:28,403 - INFO - Redis连接成功
2025-07-15 14:34:28,403 - INFO - Redis连接成功
2025-07-15 14:34:28,403 - INFO - Redis连接成功
2025-07-15 14:34:28,407 - INFO - ✅ MongoDB连接成功
2025-07-15 14:34:28,409 - INFO - ✅ MongoDB连接成功
2025-07-15 14:34:28,410 - INFO - ✅ MongoDB连接成功
2025-07-15 14:34:28,745 - INFO - ✅ PostgreSQL连接池创建成功
2025-07-15 14:34:28,746 - INFO - Redis连接成功
2025-07-15 14:34:28,746 - INFO - ✅ f3数据拉取服务启动成功，预定执行时间: ['02:00', '03:00']
2025-07-15 14:34:28,746 - INFO - 🚀 数据拉取调度器启动，预定运行时间: ['02:00', '03:00']
2025-07-15 14:34:28,746 - INFO - 下一次f3/f5同步任务将在 2025-07-16 02:00:00 执行，等待 41131 秒...
2025-07-15 14:34:28,762 - INFO - ✅ PostgreSQL连接池创建成功
2025-07-15 14:34:28,762 - INFO - ✅ f2推送回写服务启动成功，工作线程数: 4
2025-07-15 14:34:28,763 - INFO - 🚀 推送工作线程启动: worker_1
2025-07-15 14:34:28,763 - INFO - 🚀 推送工作线程启动: worker_2
2025-07-15 14:34:28,763 - INFO - 🚀 推送工作线程启动: worker_3
2025-07-15 14:34:28,763 - INFO - 🚀 推送工作线程启动: worker_4
2025-07-15 14:34:28,767 - INFO - ✅ PostgreSQL连接池创建成功
2025-07-15 14:34:28,767 - INFO - ✅ f1监听器服务启动成功
2025-07-15 14:34:28,777 - INFO - ✅ 分区创建成功: entries_2025_07
2025-07-15 14:34:28,780 - INFO - ✅ 分区创建成功: entries_2025_08
2025-07-15 14:34:28,829 - INFO - ✅ PostgreSQL连接池创建成功
2025-07-15 14:34:28,829 - INFO - ✅ f4操作处理服务启动成功
2025-07-15 14:34:29,015 - INFO - 📡 开始监听频道: push_job
2025-07-15 14:34:29,017 - INFO - 📡 开始监听频道: partition_check
2025-07-15 14:34:29,018 - INFO - 📡 开始监听频道: sync_trigger
2025-07-15 14:35:30,934 - INFO - 📋 2025/07/03 + 16:10 + 处理队列项: INSERT - entry_id=145179 - queue_id=170 - 重试次数=0
2025-07-15 14:35:31,040 - INFO - 🔄 执行INSERT操作: entry_id=145179, employee_id=215829
2025-07-15 14:35:31,040 - INFO - 📤 发送到Server6的数据: {'従業員ｺｰﾄﾞ': '215829', '日付': '2025-07-15', '機種': 'NULL', '号機': 'NULL', '工場製番': 'NULL', '工事番号': 'NULL', 'ﾕﾆｯﾄ番号': 'NULL', '区分': '3', '項目': '3', '時間': 0.3, '所属ｺｰﾄﾞ': '131'}
2025-07-15 14:35:31,040 - INFO - AIOHTTP session for Server6 created.
2025-07-15 14:35:35,394 - INFO - ✅ INSERT完成: entry_id=145179 -> external_id=603685
2025-07-15 14:36:15,031 - INFO - 📋 2025/07/03 + 16:10 + 处理队列项: UPDATE - entry_id=145179 - queue_id=171 - 重试次数=0
2025-07-15 14:36:15,152 - WARNING - ⚠️ UPDATE操作跳过: entry_id=145179 (记录已被删除)
2025-07-15 14:36:15,155 - INFO - ✅ UPDATE跳过完成: entry_id=145179 (记录已被删除)
2025-07-15 14:36:15,161 - INFO - 📋 2025/07/03 + 16:10 + 处理队列项: DELETE - entry_id=145179 - queue_id=172 - 重试次数=0
2025-07-15 14:36:15,161 - INFO - 🔄 执行DELETE操作: entry_id=145179, external_id=603685
2025-07-15 14:36:19,246 - INFO - ✅ DELETE完成: entry_id=145179, external_id=603685
2025-07-15 14:36:19,249 - INFO - ℹ️ DELETE操作跳过f6同步: entry_id=145179 (记录已删除)
2025-07-15 14:40:11,732 - INFO - 📋 2025/07/03 + 16:10 + 处理队列项: DELETE - entry_id=145176 - queue_id=173 - 重试次数=0
2025-07-15 14:40:11,733 - INFO - 🔄 执行DELETE操作: entry_id=145176, external_id=603682
2025-07-15 14:40:16,575 - INFO - ✅ DELETE完成: entry_id=145176, external_id=603682
2025-07-15 14:40:16,577 - INFO - ℹ️ DELETE操作跳过f6同步: entry_id=145176 (记录已删除)
2025-07-15 15:00:58,751 - INFO - 📋 2025/07/03 + 16:10 + 处理队列项: INSERT - entry_id=145180 - queue_id=174 - 重试次数=0
2025-07-15 15:00:58,752 - INFO - 📋 2025/07/03 + 16:10 + 处理队列项: INSERT - entry_id=145181 - queue_id=175 - 重试次数=0
2025-07-15 15:00:58,752 - INFO - 📋 2025/07/03 + 16:10 + 处理队列项: INSERT - entry_id=145182 - queue_id=176 - 重试次数=0
2025-07-15 15:00:58,782 - INFO - 🔄 执行INSERT操作: entry_id=145181, employee_id=215829
2025-07-15 15:00:58,782 - INFO - 📤 发送到Server6的数据: {'従業員ｺｰﾄﾞ': '215829', '日付': '2025-07-02', '機種': 'NULL', '号機': 'NULL', '工場製番': 'NULL', '工事番号': '24585', 'ﾕﾆｯﾄ番号': 'NULL', '区分': '3', '項目': '7', '時間': 9.0, '所属ｺｰﾄﾞ': '131'}
2025-07-15 15:00:58,785 - INFO - 🔄 执行INSERT操作: entry_id=145180, employee_id=215829
2025-07-15 15:00:58,785 - INFO - 📤 发送到Server6的数据: {'従業員ｺｰﾄﾞ': '215829', '日付': '2025-07-01', '機種': 'NULL', '号機': 'NULL', '工場製番': 'NULL', '工事番号': '24585', 'ﾕﾆｯﾄ番号': 'NULL', '区分': '3', '項目': '7', '時間': 10.5, '所属ｺｰﾄﾞ': '131'}
2025-07-15 15:00:58,907 - INFO - 🔄 执行INSERT操作: entry_id=145182, employee_id=215829
2025-07-15 15:00:58,907 - INFO - 📤 发送到Server6的数据: {'従業員ｺｰﾄﾞ': '215829', '日付': '2025-07-03', '機種': 'NULL', '号機': 'NULL', '工場製番': 'NULL', '工事番号': '24585', 'ﾕﾆｯﾄ番号': 'NULL', '区分': '3', '項目': '7', '時間': 9.0, '所属ｺｰﾄﾞ': '131'}
2025-07-15 15:00:59,292 - INFO - 📋 2025/07/03 + 16:10 + 处理队列项: INSERT - entry_id=145183 - queue_id=177 - 重试次数=0
2025-07-15 15:00:59,418 - INFO - 🔄 执行INSERT操作: entry_id=145183, employee_id=215829
2025-07-15 15:00:59,418 - INFO - 📤 发送到Server6的数据: {'従業員ｺｰﾄﾞ': '215829', '日付': '2025-07-04', '機種': 'NULL', '号機': 'NULL', '工場製番': 'NULL', '工事番号': '24585', 'ﾕﾆｯﾄ番号': 'NULL', '区分': '3', '項目': '7', '時間': 9.0, '所属ｺｰﾄﾞ': '131'}
2025-07-15 15:01:08,327 - INFO - ✅ INSERT完成: entry_id=145181 -> external_id=603686
2025-07-15 15:01:08,333 - INFO - 📋 2025/07/03 + 16:10 + 处理队列项: INSERT - entry_id=145184 - queue_id=178 - 重试次数=0
2025-07-15 15:01:08,363 - INFO - 🔄 执行INSERT操作: entry_id=145184, employee_id=215829
2025-07-15 15:01:08,363 - INFO - 📤 发送到Server6的数据: {'従業員ｺｰﾄﾞ': '215829', '日付': '2025-07-07', '機種': 'NULL', '号機': 'NULL', '工場製番': 'NULL', '工事番号': '24585', 'ﾕﾆｯﾄ番号': 'NULL', '区分': '3', '項目': '7', '時間': 9.0, '所属ｺｰﾄﾞ': '131'}
2025-07-15 15:01:16,758 - INFO - ✅ INSERT完成: entry_id=145180 -> external_id=603687
2025-07-15 15:01:16,763 - INFO - ✅ INSERT完成: entry_id=145182 -> external_id=603688
2025-07-15 15:01:20,840 - INFO - ✅ INSERT完成: entry_id=145183 -> external_id=603689
2025-07-15 15:01:25,126 - INFO - ✅ INSERT完成: entry_id=145184 -> external_id=603690
2025-07-15 15:12:22,488 - INFO - 📋 2025/07/03 + 16:10 + 处理队列项: INSERT - entry_id=145185 - queue_id=179 - 重试次数=0
2025-07-15 15:12:22,489 - INFO - 📋 2025/07/03 + 16:10 + 处理队列项: INSERT - entry_id=145186 - queue_id=180 - 重试次数=0
2025-07-15 15:12:22,489 - INFO - 📋 2025/07/03 + 16:10 + 处理队列项: INSERT - entry_id=145187 - queue_id=181 - 重试次数=0
2025-07-15 15:12:22,516 - INFO - 🔄 执行INSERT操作: entry_id=145186, employee_id=215829
2025-07-15 15:12:22,516 - INFO - 📤 发送到Server6的数据: {'従業員ｺｰﾄﾞ': '215829', '日付': '2025-07-02', '機種': 'NULL', '号機': 'NULL', '工場製番': 'NULL', '工事番号': '24585', 'ﾕﾆｯﾄ番号': 'NULL', '区分': '3', '項目': '7', '時間': 9.0, '所属ｺｰﾄﾞ': '131'}
2025-07-15 15:12:22,517 - INFO - 🔄 执行INSERT操作: entry_id=145187, employee_id=215829
2025-07-15 15:12:22,517 - INFO - 📤 发送到Server6的数据: {'従業員ｺｰﾄﾞ': '215829', '日付': '2025-07-03', '機種': 'NULL', '号機': 'NULL', '工場製番': 'NULL', '工事番号': '24585', 'ﾕﾆｯﾄ番号': 'NULL', '区分': '3', '項目': '7', '時間': 9.0, '所属ｺｰﾄﾞ': '131'}
2025-07-15 15:12:22,520 - INFO - 🔄 执行INSERT操作: entry_id=145185, employee_id=215829
2025-07-15 15:12:22,520 - INFO - 📤 发送到Server6的数据: {'従業員ｺｰﾄﾞ': '215829', '日付': '2025-07-01', '機種': 'NULL', '号機': 'NULL', '工場製番': 'NULL', '工事番号': '24585', 'ﾕﾆｯﾄ番号': 'NULL', '区分': '3', '項目': '7', '時間': 10.5, '所属ｺｰﾄﾞ': '131'}
2025-07-15 15:12:22,571 - INFO - 📋 2025/07/03 + 16:10 + 处理队列项: INSERT - entry_id=145188 - queue_id=182 - 重试次数=0
2025-07-15 15:12:22,604 - INFO - 🔄 执行INSERT操作: entry_id=145188, employee_id=215829
2025-07-15 15:12:22,604 - INFO - 📤 发送到Server6的数据: {'従業員ｺｰﾄﾞ': '215829', '日付': '2025-07-04', '機種': 'NULL', '号機': 'NULL', '工場製番': 'NULL', '工事番号': '24585', 'ﾕﾆｯﾄ番号': 'NULL', '区分': '3', '項目': '7', '時間': 9.0, '所属ｺｰﾄﾞ': '131'}
2025-07-15 15:12:33,136 - INFO - ✅ INSERT完成: entry_id=145186 -> external_id=603691
2025-07-15 15:12:33,143 - INFO - 📋 2025/07/03 + 16:10 + 处理队列项: INSERT - entry_id=145189 - queue_id=183 - 重试次数=0
2025-07-15 15:12:33,174 - INFO - 🔄 执行INSERT操作: entry_id=145189, employee_id=215829
2025-07-15 15:12:33,174 - INFO - 📤 发送到Server6的数据: {'従業員ｺｰﾄﾞ': '215829', '日付': '2025-07-07', '機種': 'NULL', '号機': 'NULL', '工場製番': 'NULL', '工事番号': '24585', 'ﾕﾆｯﾄ番号': 'NULL', '区分': '3', '項目': '7', '時間': 9.0, '所属ｺｰﾄﾞ': '131'}
2025-07-15 15:12:41,251 - INFO - ✅ INSERT完成: entry_id=145185 -> external_id=603693
2025-07-15 15:12:41,255 - INFO - ✅ INSERT完成: entry_id=145187 -> external_id=603692
2025-07-15 15:12:45,426 - INFO - ✅ INSERT完成: entry_id=145188 -> external_id=603694
2025-07-15 15:12:49,312 - INFO - ✅ INSERT完成: entry_id=145189 -> external_id=603695
2025-07-15 15:16:09,818 - INFO - 📋 2025/07/03 + 16:10 + 处理队列项: INSERT - entry_id=145190 - queue_id=184 - 重试次数=0
2025-07-15 15:16:09,819 - INFO - 📋 2025/07/03 + 16:10 + 处理队列项: INSERT - entry_id=145191 - queue_id=185 - 重试次数=0
2025-07-15 15:16:09,819 - INFO - 📋 2025/07/03 + 16:10 + 处理队列项: INSERT - entry_id=145192 - queue_id=186 - 重试次数=0
2025-07-15 15:16:09,840 - INFO - 📋 2025/07/03 + 16:10 + 处理队列项: INSERT - entry_id=145193 - queue_id=187 - 重试次数=0
2025-07-15 15:16:09,844 - INFO - 🔄 执行INSERT操作: entry_id=145190, employee_id=215829
2025-07-15 15:16:09,844 - INFO - 📤 发送到Server6的数据: {'従業員ｺｰﾄﾞ': '215829', '日付': '2025-07-01', '機種': 'NULL', '号機': 'NULL', '工場製番': 'NULL', '工事番号': '24585', 'ﾕﾆｯﾄ番号': 'NULL', '区分': '3', '項目': '7', '時間': 10.5, '所属ｺｰﾄﾞ': '131'}
2025-07-15 15:16:09,845 - INFO - 🔄 执行INSERT操作: entry_id=145192, employee_id=215829
2025-07-15 15:16:09,845 - INFO - 📤 发送到Server6的数据: {'従業員ｺｰﾄﾞ': '215829', '日付': '2025-07-03', '機種': 'NULL', '号機': 'NULL', '工場製番': 'NULL', '工事番号': '24585', 'ﾕﾆｯﾄ番号': 'NULL', '区分': '3', '項目': '7', '時間': 9.0, '所属ｺｰﾄﾞ': '131'}
2025-07-15 15:16:09,851 - INFO - 🔄 执行INSERT操作: entry_id=145191, employee_id=215829
2025-07-15 15:16:09,851 - INFO - 📤 发送到Server6的数据: {'従業員ｺｰﾄﾞ': '215829', '日付': '2025-07-02', '機種': 'NULL', '号機': 'NULL', '工場製番': 'NULL', '工事番号': '24585', 'ﾕﾆｯﾄ番号': 'NULL', '区分': '3', '項目': '7', '時間': 9.0, '所属ｺｰﾄﾞ': '131'}
2025-07-15 15:16:09,871 - INFO - 🔄 执行INSERT操作: entry_id=145193, employee_id=215829
2025-07-15 15:16:09,872 - INFO - 📤 发送到Server6的数据: {'従業員ｺｰﾄﾞ': '215829', '日付': '2025-07-04', '機種': 'NULL', '号機': 'NULL', '工場製番': 'NULL', '工事番号': '24585', 'ﾕﾆｯﾄ番号': 'NULL', '区分': '3', '項目': '7', '時間': 9.0, '所属ｺｰﾄﾞ': '131'}
2025-07-15 15:16:22,133 - INFO - ✅ INSERT完成: entry_id=145190 -> external_id=603696
2025-07-15 15:16:22,140 - INFO - 📋 2025/07/03 + 16:10 + 处理队列项: INSERT - entry_id=145194 - queue_id=188 - 重试次数=0
2025-07-15 15:16:22,168 - INFO - 🔄 执行INSERT操作: entry_id=145194, employee_id=215829
2025-07-15 15:16:22,168 - INFO - 📤 发送到Server6的数据: {'従業員ｺｰﾄﾞ': '215829', '日付': '2025-07-07', '機種': 'NULL', '号機': 'NULL', '工場製番': 'NULL', '工事番号': '24585', 'ﾕﾆｯﾄ番号': 'NULL', '区分': '3', '項目': '7', '時間': 9.0, '所属ｺｰﾄﾞ': '131'}
2025-07-15 15:16:31,636 - INFO - ✅ INSERT完成: entry_id=145191 -> external_id=603698
2025-07-15 15:16:31,636 - INFO - ✅ INSERT完成: entry_id=145192 -> external_id=603697
2025-07-15 15:16:31,645 - INFO - 📋 2025/07/03 + 16:10 + 处理队列项: INSERT - entry_id=145195 - queue_id=189 - 重试次数=0
2025-07-15 15:16:31,647 - INFO - 📋 2025/07/03 + 16:10 + 处理队列项: INSERT - entry_id=145196 - queue_id=190 - 重试次数=0
2025-07-15 15:16:31,674 - INFO - 🔄 执行INSERT操作: entry_id=145196, employee_id=215829
2025-07-15 15:16:31,674 - INFO - 📤 发送到Server6的数据: {'従業員ｺｰﾄﾞ': '215829', '日付': '2025-07-09', '機種': 'NULL', '号機': 'NULL', '工場製番': 'HA0484', '工事番号': 'NULL', 'ﾕﾆｯﾄ番号': 'NULL', '区分': '0', '項目': '7', '時間': 8.0, '所属ｺｰﾄﾞ': '131'}
2025-07-15 15:16:31,676 - INFO - 🔄 执行INSERT操作: entry_id=145195, employee_id=215829
2025-07-15 15:16:31,676 - INFO - 📤 发送到Server6的数据: {'従業員ｺｰﾄﾞ': '215829', '日付': '2025-07-08', '機種': 'NULL', '号機': 'NULL', '工場製番': 'HA0484', '工事番号': 'NULL', 'ﾕﾆｯﾄ番号': 'NULL', '区分': '0', '項目': '7', '時間': 9.0, '所属ｺｰﾄﾞ': '131'}
2025-07-15 15:16:35,993 - INFO - ✅ INSERT完成: entry_id=145193 -> external_id=603699
2025-07-15 15:16:36,006 - INFO - 📋 2025/07/03 + 16:10 + 处理队列项: INSERT - entry_id=145197 - queue_id=191 - 重试次数=0
2025-07-15 15:16:36,038 - INFO - 🔄 执行INSERT操作: entry_id=145197, employee_id=215829
2025-07-15 15:16:36,038 - INFO - 📤 发送到Server6的数据: {'従業員ｺｰﾄﾞ': '215829', '日付': '2025-07-10', '機種': 'NULL', '号機': 'NULL', '工場製番': 'HA0484', '工事番号': 'NULL', 'ﾕﾆｯﾄ番号': 'NULL', '区分': '0', '項目': '7', '時間': 10.0, '所属ｺｰﾄﾞ': '131'}
2025-07-15 15:16:44,435 - INFO - ✅ INSERT完成: entry_id=145194 -> external_id=603700
2025-07-15 15:16:44,436 - INFO - ✅ INSERT完成: entry_id=145196 -> external_id=603701
2025-07-15 15:16:44,461 - INFO - 📋 2025/07/03 + 16:10 + 处理队列项: INSERT - entry_id=145198 - queue_id=192 - 重试次数=0
2025-07-15 15:16:44,461 - INFO - 📋 2025/07/03 + 16:10 + 处理队列项: INSERT - entry_id=145199 - queue_id=193 - 重试次数=0
2025-07-15 15:16:44,489 - INFO - 🔄 执行INSERT操作: entry_id=145198, employee_id=215829
2025-07-15 15:16:44,489 - INFO - 📤 发送到Server6的数据: {'従業員ｺｰﾄﾞ': '215829', '日付': '2025-07-15', '機種': 'TEST_DE', '号機': 'NULL', '工場製番': 'FACTORY', '工事番号': 'PROJECT', 'ﾕﾆｯﾄ番号': 'UNIT', '区分': '1', '項目': '1', '時間': 0.5, '所属ｺｰﾄﾞ': '131'}
2025-07-15 15:16:44,491 - INFO - 🔄 执行INSERT操作: entry_id=145199, employee_id=215829
2025-07-15 15:16:44,491 - INFO - 📤 发送到Server6的数据: {'従業員ｺｰﾄﾞ': '215829', '日付': '2025-07-15', '機種': 'NULL', '号機': 'NULL', '工場製番': 'NULL', '工事番号': 'NULL', 'ﾕﾆｯﾄ番号': 'NULL', '区分': '1', '項目': '1', '時間': 0.2, '所属ｺｰﾄﾞ': '131'}
2025-07-15 15:16:48,367 - INFO - ✅ INSERT完成: entry_id=145195 -> external_id=603702
2025-07-15 15:16:48,374 - INFO - 📋 2025/07/03 + 16:10 + 处理队列项: INSERT - entry_id=145200 - queue_id=194 - 重试次数=0
2025-07-15 15:16:48,397 - INFO - 🔄 执行INSERT操作: entry_id=145200, employee_id=215829
2025-07-15 15:16:48,397 - INFO - 📤 发送到Server6的数据: {'従業員ｺｰﾄﾞ': '215829', '日付': '2025-07-15', '機種': 'NULL', '号機': 'NULL', '工場製番': 'NULL', '工事番号': 'NULL', 'ﾕﾆｯﾄ番号': 'NULL', '区分': '1', '項目': '1', '時間': 0.2, '所属ｺｰﾄﾞ': '131'}
2025-07-15 15:16:56,885 - INFO - ✅ INSERT完成: entry_id=145197 -> external_id=603703
2025-07-15 15:16:56,886 - INFO - ✅ INSERT完成: entry_id=145198 -> external_id=603704
2025-07-15 15:16:56,893 - INFO - 📋 2025/07/03 + 16:10 + 处理队列项: INSERT - entry_id=145201 - queue_id=195 - 重试次数=0
2025-07-15 15:16:56,894 - INFO - 📋 2025/07/03 + 16:10 + 处理队列项: INSERT - entry_id=145202 - queue_id=196 - 重试次数=0
2025-07-15 15:16:56,924 - INFO - 🔄 执行INSERT操作: entry_id=145202, employee_id=215829
2025-07-15 15:16:56,924 - INFO - 📤 发送到Server6的数据: {'従業員ｺｰﾄﾞ': '215829', '日付': '2025-07-15', '機種': 'test', '号機': 'NULL', '工場製番': 'NULL', '工事番号': 'NULL', 'ﾕﾆｯﾄ番号': 'NULL', '区分': '4', '項目': '4', '時間': 4.0, '所属ｺｰﾄﾞ': '131'}
2025-07-15 15:16:56,925 - INFO - 🔄 执行INSERT操作: entry_id=145201, employee_id=215829
2025-07-15 15:16:56,925 - INFO - 📤 发送到Server6的数据: {'従業員ｺｰﾄﾞ': '215829', '日付': '2025-07-15', '機種': 'test_mo', '号機': 'NULL', '工場製番': 'test_fa', '工事番号': 'test_pr', 'ﾕﾆｯﾄ番号': 'test', '区分': '1', '項目': '1', '時間': 2.5, '所属ｺｰﾄﾞ': 'tes'}
2025-07-15 15:17:00,904 - INFO - ✅ INSERT完成: entry_id=145199 -> external_id=603705
2025-07-15 15:17:00,911 - INFO - 📋 2025/07/03 + 16:10 + 处理队列项: INSERT - entry_id=145203 - queue_id=197 - 重试次数=0
2025-07-15 15:17:00,937 - INFO - 🔄 执行INSERT操作: entry_id=145203, employee_id=215829
2025-07-15 15:17:00,937 - INFO - 📤 发送到Server6的数据: {'従業員ｺｰﾄﾞ': '215829', '日付': '2025-07-15', '機種': 'NULL', '号機': 'NULL', '工場製番': 'NULL', '工事番号': 'NULL', 'ﾕﾆｯﾄ番号': 'NULL', '区分': '3', '項目': '3', '時間': 0.6, '所属ｺｰﾄﾞ': '131'}
2025-07-15 15:17:10,001 - INFO - ✅ INSERT完成: entry_id=145202 -> external_id=603707
2025-07-15 15:17:10,026 - INFO - 📋 2025/07/03 + 16:10 + 处理队列项: INSERT - entry_id=145204 - queue_id=198 - 重试次数=0
2025-07-15 15:17:10,057 - INFO - 🔄 执行INSERT操作: entry_id=145204, employee_id=215829
2025-07-15 15:17:10,057 - INFO - 📤 发送到Server6的数据: {'従業員ｺｰﾄﾞ': '215829', '日付': '2025-07-15', '機種': 'NULL', '号機': 'NULL', '工場製番': 'NULL', '工事番号': 'NULL', 'ﾕﾆｯﾄ番号': 'NULL', '区分': '3', '項目': '3', '時間': 0.7, '所属ｺｰﾄﾞ': '131'}
2025-07-15 15:17:10,124 - INFO - ✅ INSERT完成: entry_id=145200 -> external_id=603706
2025-07-15 15:17:10,131 - INFO - 📋 2025/07/03 + 16:10 + 处理队列项: INSERT - entry_id=145205 - queue_id=199 - 重试次数=0
2025-07-15 15:17:10,163 - INFO - 🔄 执行INSERT操作: entry_id=145205, employee_id=215829
2025-07-15 15:17:10,163 - INFO - 📤 发送到Server6的数据: {'従業員ｺｰﾄﾞ': '215829', '日付': '2025-07-15', '機種': 'NULL', '号機': 'NULL', '工場製番': 'NULL', '工事番号': 'NULL', 'ﾕﾆｯﾄ番号': 'NULL', '区分': '2', '項目': '2', '時間': 2.0, '所属ｺｰﾄﾞ': '131'}
2025-07-15 15:17:15,559 - INFO - ✅ INSERT完成: entry_id=145201 -> external_id=603708
2025-07-15 15:17:15,566 - INFO - 📋 2025/07/03 + 16:10 + 处理队列项: INSERT - entry_id=145206 - queue_id=200 - 重试次数=0
2025-07-15 15:17:15,592 - INFO - 🔄 执行INSERT操作: entry_id=145206, employee_id=215829
2025-07-15 15:17:15,592 - INFO - 📤 发送到Server6的数据: {'従業員ｺｰﾄﾞ': '215829', '日付': '2025-07-15', '機種': 'NULL', '号機': 'NULL', '工場製番': 'NULL', '工事番号': 'NULL', 'ﾕﾆｯﾄ番号': 'NULL', '区分': '2', '項目': '2', '時間': 2.0, '所属ｺｰﾄﾞ': '131'}
2025-07-15 15:17:24,104 - INFO - ✅ INSERT完成: entry_id=145204 -> external_id=603710
2025-07-15 15:17:24,105 - INFO - ✅ INSERT完成: entry_id=145203 -> external_id=603709
2025-07-15 15:17:24,112 - INFO - 📋 2025/07/03 + 16:10 + 处理队列项: INSERT - entry_id=145207 - queue_id=201 - 重试次数=0
2025-07-15 15:17:24,113 - INFO - 📋 2025/07/03 + 16:10 + 处理队列项: INSERT - entry_id=145208 - queue_id=202 - 重试次数=0
2025-07-15 15:17:24,138 - INFO - 🔄 执行INSERT操作: entry_id=145207, employee_id=215829
2025-07-15 15:17:24,138 - INFO - 📤 发送到Server6的数据: {'従業員ｺｰﾄﾞ': '215829', '日付': '2025-07-15', '機種': 'NULL', '号機': 'NULL', '工場製番': 'NULL', '工事番号': 'NULL', 'ﾕﾆｯﾄ番号': 'NULL', '区分': '2', '項目': '2', '時間': 2.0, '所属ｺｰﾄﾞ': '131'}
2025-07-15 15:17:24,139 - INFO - 🔄 执行INSERT操作: entry_id=145208, employee_id=215829
2025-07-15 15:17:24,139 - INFO - 📤 发送到Server6的数据: {'従業員ｺｰﾄﾞ': '215829', '日付': '2025-07-02', '機種': 'NULL', '号機': 'NULL', '工場製番': 'NULL', '工事番号': '24585', 'ﾕﾆｯﾄ番号': 'NULL', '区分': '3', '項目': '7', '時間': 9.0, '所属ｺｰﾄﾞ': '131'}
2025-07-15 15:17:28,130 - INFO - ✅ INSERT完成: entry_id=145205 -> external_id=603711
2025-07-15 15:17:28,136 - INFO - 📋 2025/07/03 + 16:10 + 处理队列项: INSERT - entry_id=145209 - queue_id=203 - 重试次数=0
2025-07-15 15:17:28,162 - INFO - 🔄 执行INSERT操作: entry_id=145209, employee_id=215829
2025-07-15 15:17:28,162 - INFO - 📤 发送到Server6的数据: {'従業員ｺｰﾄﾞ': '215829', '日付': '2025-07-01', '機種': 'NULL', '号機': 'NULL', '工場製番': 'NULL', '工事番号': '24585', 'ﾕﾆｯﾄ番号': 'NULL', '区分': '3', '項目': '7', '時間': 10.5, '所属ｺｰﾄﾞ': '131'}
2025-07-15 15:17:36,680 - INFO - ✅ INSERT完成: entry_id=145206 -> external_id=603712
2025-07-15 15:17:36,680 - INFO - ✅ INSERT完成: entry_id=145207 -> external_id=603713
2025-07-15 15:17:36,686 - INFO - 📋 2025/07/03 + 16:10 + 处理队列项: INSERT - entry_id=145210 - queue_id=204 - 重试次数=0
2025-07-15 15:17:36,687 - INFO - 📋 2025/07/03 + 16:10 + 处理队列项: INSERT - entry_id=145211 - queue_id=205 - 重试次数=0
2025-07-15 15:17:36,714 - INFO - 🔄 执行INSERT操作: entry_id=145211, employee_id=215829
2025-07-15 15:17:36,714 - INFO - 📤 发送到Server6的数据: {'従業員ｺｰﾄﾞ': '215829', '日付': '2025-07-04', '機種': 'NULL', '号機': 'NULL', '工場製番': 'NULL', '工事番号': '24585', 'ﾕﾆｯﾄ番号': 'NULL', '区分': '3', '項目': '7', '時間': 9.0, '所属ｺｰﾄﾞ': '131'}
2025-07-15 15:17:36,718 - INFO - 🔄 执行INSERT操作: entry_id=145210, employee_id=215829
2025-07-15 15:17:36,718 - INFO - 📤 发送到Server6的数据: {'従業員ｺｰﾄﾞ': '215829', '日付': '2025-07-03', '機種': 'NULL', '号機': 'NULL', '工場製番': 'NULL', '工事番号': '24585', 'ﾕﾆｯﾄ番号': 'NULL', '区分': '3', '項目': '7', '時間': 9.0, '所属ｺｰﾄﾞ': '131'}
2025-07-15 15:17:40,617 - INFO - ✅ INSERT完成: entry_id=145208 -> external_id=603714
2025-07-15 15:17:40,624 - INFO - 📋 2025/07/03 + 16:10 + 处理队列项: INSERT - entry_id=145212 - queue_id=206 - 重试次数=0
2025-07-15 15:17:40,651 - INFO - 🔄 执行INSERT操作: entry_id=145212, employee_id=215829
2025-07-15 15:17:40,651 - INFO - 📤 发送到Server6的数据: {'従業員ｺｰﾄﾞ': '215829', '日付': '2025-07-07', '機種': 'NULL', '号機': 'NULL', '工場製番': 'NULL', '工事番号': '24585', 'ﾕﾆｯﾄ番号': 'NULL', '区分': '3', '項目': '7', '時間': 9.0, '所属ｺｰﾄﾞ': '131'}
2025-07-15 15:17:48,831 - INFO - ✅ INSERT完成: entry_id=145211 -> external_id=603716
2025-07-15 15:17:48,832 - INFO - ✅ INSERT完成: entry_id=145209 -> external_id=603715
2025-07-15 15:17:53,269 - INFO - ✅ INSERT完成: entry_id=145210 -> external_id=603717
2025-07-15 15:17:57,590 - INFO - ✅ INSERT完成: entry_id=145212 -> external_id=603718
2025-07-15 15:18:56,993 - INFO - 📋 2025/07/03 + 16:10 + 处理队列项: UPDATE - entry_id=145209 - queue_id=207 - 重试次数=0
2025-07-15 15:18:57,020 - INFO - 🔄 执行UPDATE操作: entry_id=145209 -> external_id=603715
2025-07-15 15:18:57,020 - INFO - 📦 更新数据: {'employee_id': '215829', 'entry_date': '2025/07/01', 'model': 'NULL', 'number': None, 'factory_number': 'NULL', 'project_number': '24585', 'unit_number': 'NULL', 'category': '3', 'item': '7', 'duration': 10.5, 'department': '131'}
2025-07-15 15:18:57,020 - INFO - 📤 发送到Server6的更新数据: {'従業員ｺｰﾄﾞ': '215829', '日付': '2025-07-01', '機種': 'NULL', '号機': 'NULL', '工場製番': 'NULL', '工事番号': '24585', 'ﾕﾆｯﾄ番号': 'NULL', '区分': '3', '項目': '7', '時間': 10.5, '所属ｺｰﾄﾞ': '131'}
2025-07-15 15:18:57,437 - INFO - 📋 2025/07/03 + 16:10 + 处理队列项: DELETE - entry_id=145209 - queue_id=208 - 重试次数=0
2025-07-15 15:18:57,440 - INFO - 🔄 执行DELETE操作: entry_id=145209, external_id=603715
2025-07-15 15:19:01,225 - INFO - ✅ UPDATE完成: entry_id=145209 -> external_id=603715, source已更新为'system'
2025-07-15 15:19:05,314 - INFO - ✅ DELETE完成: entry_id=145209, external_id=603715
2025-07-15 15:19:05,317 - INFO - ℹ️ DELETE操作跳过f6同步: entry_id=145209 (记录已删除)
2025-07-15 15:20:03,366 - INFO - 📋 2025/07/03 + 16:10 + 处理队列项: UPDATE - entry_id=145190 - queue_id=209 - 重试次数=0
2025-07-15 15:20:03,381 - WARNING - ⚠️ UPDATE操作跳过: entry_id=145190 (记录已被删除)
2025-07-15 15:20:03,382 - INFO - ✅ UPDATE跳过完成: entry_id=145190 (记录已被删除)
2025-07-15 15:20:03,387 - INFO - 📋 2025/07/03 + 16:10 + 处理队列项: DELETE - entry_id=145190 - queue_id=210 - 重试次数=0
2025-07-15 15:20:03,388 - INFO - 🔄 执行DELETE操作: entry_id=145190, external_id=603696
2025-07-15 15:20:07,807 - INFO - ✅ DELETE完成: entry_id=145190, external_id=603696
2025-07-15 15:20:07,809 - INFO - ℹ️ DELETE操作跳过f6同步: entry_id=145190 (记录已删除)
2025-07-15 15:20:31,573 - INFO - 📋 2025/07/03 + 16:10 + 处理队列项: UPDATE - entry_id=145185 - queue_id=211 - 重试次数=0
2025-07-15 15:20:31,590 - WARNING - ⚠️ UPDATE操作跳过: entry_id=145185 (记录已被删除)
2025-07-15 15:20:31,592 - INFO - ✅ UPDATE跳过完成: entry_id=145185 (记录已被删除)
2025-07-15 15:20:31,597 - INFO - 📋 2025/07/03 + 16:10 + 处理队列项: DELETE - entry_id=145185 - queue_id=212 - 重试次数=0
2025-07-15 15:20:31,598 - INFO - 🔄 执行DELETE操作: entry_id=145185, external_id=603693
2025-07-15 15:20:35,432 - INFO - ✅ DELETE完成: entry_id=145185, external_id=603693
2025-07-15 15:20:35,434 - INFO - ℹ️ DELETE操作跳过f6同步: entry_id=145185 (记录已删除)
2025-07-15 15:20:55,234 - INFO - 📋 2025/07/03 + 16:10 + 处理队列项: UPDATE - entry_id=145191 - queue_id=213 - 重试次数=0
2025-07-15 15:20:55,252 - WARNING - ⚠️ UPDATE操作跳过: entry_id=145191 (记录已被删除)
2025-07-15 15:20:55,254 - INFO - ✅ UPDATE跳过完成: entry_id=145191 (记录已被删除)
2025-07-15 15:20:55,259 - INFO - 📋 2025/07/03 + 16:10 + 处理队列项: DELETE - entry_id=145191 - queue_id=214 - 重试次数=0
2025-07-15 15:20:55,260 - INFO - 🔄 执行DELETE操作: entry_id=145191, external_id=603698
2025-07-15 15:20:59,276 - INFO - ✅ DELETE完成: entry_id=145191, external_id=603698
2025-07-15 15:20:59,278 - INFO - ℹ️ DELETE操作跳过f6同步: entry_id=145191 (记录已删除)
2025-07-15 15:21:05,294 - INFO - 📋 2025/07/03 + 16:10 + 处理队列项: UPDATE - entry_id=145186 - queue_id=215 - 重试次数=0
2025-07-15 15:21:05,315 - WARNING - ⚠️ UPDATE操作跳过: entry_id=145186 (记录已被删除)
2025-07-15 15:21:05,316 - INFO - ✅ UPDATE跳过完成: entry_id=145186 (记录已被删除)
2025-07-15 15:21:05,323 - INFO - 📋 2025/07/03 + 16:10 + 处理队列项: DELETE - entry_id=145186 - queue_id=216 - 重试次数=0
2025-07-15 15:21:05,324 - INFO - 🔄 执行DELETE操作: entry_id=145186, external_id=603691
2025-07-15 15:21:09,547 - INFO - ✅ DELETE完成: entry_id=145186, external_id=603691
2025-07-15 15:21:09,551 - INFO - ℹ️ DELETE操作跳过f6同步: entry_id=145186 (记录已删除)
2025-07-15 15:21:11,509 - INFO - 📋 2025/07/03 + 16:10 + 处理队列项: UPDATE - entry_id=145181 - queue_id=217 - 重试次数=0
2025-07-15 15:21:11,540 - WARNING - ⚠️ UPDATE操作跳过: entry_id=145181 (记录已被删除)
2025-07-15 15:21:11,541 - INFO - ✅ UPDATE跳过完成: entry_id=145181 (记录已被删除)
2025-07-15 15:21:11,547 - INFO - 📋 2025/07/03 + 16:10 + 处理队列项: DELETE - entry_id=145181 - queue_id=218 - 重试次数=0
2025-07-15 15:21:11,548 - INFO - 🔄 执行DELETE操作: entry_id=145181, external_id=603686
2025-07-15 15:21:15,586 - INFO - 📋 2025/07/03 + 16:10 + 处理队列项: UPDATE - entry_id=145192 - queue_id=219 - 重试次数=0
2025-07-15 15:21:15,609 - WARNING - ⚠️ UPDATE操作跳过: entry_id=145192 (记录已被删除)
2025-07-15 15:21:15,611 - INFO - ✅ UPDATE跳过完成: entry_id=145192 (记录已被删除)
2025-07-15 15:21:15,621 - INFO - 📋 2025/07/03 + 16:10 + 处理队列项: DELETE - entry_id=145192 - queue_id=220 - 重试次数=0
2025-07-15 15:21:15,623 - INFO - 🔄 执行DELETE操作: entry_id=145192, external_id=603697
2025-07-15 15:21:15,850 - INFO - ✅ DELETE完成: entry_id=145181, external_id=603686
2025-07-15 15:21:15,853 - INFO - ℹ️ DELETE操作跳过f6同步: entry_id=145181 (记录已删除)
2025-07-15 15:21:19,869 - INFO - 📋 2025/07/03 + 16:10 + 处理队列项: UPDATE - entry_id=145187 - queue_id=221 - 重试次数=0
2025-07-15 15:21:19,873 - INFO - ✅ DELETE完成: entry_id=145192, external_id=603697
2025-07-15 15:21:19,874 - INFO - ℹ️ DELETE操作跳过f6同步: entry_id=145192 (记录已删除)
2025-07-15 15:21:19,879 - INFO - 📋 2025/07/03 + 16:10 + 处理队列项: DELETE - entry_id=145187 - queue_id=222 - 重试次数=0
2025-07-15 15:21:19,879 - INFO - 🔄 执行DELETE操作: entry_id=145187, external_id=603692
2025-07-15 15:21:19,899 - WARNING - ⚠️ UPDATE操作跳过: entry_id=145187 (记录已被删除)
2025-07-15 15:21:19,900 - INFO - ✅ UPDATE跳过完成: entry_id=145187 (记录已被删除)
2025-07-15 15:21:23,818 - INFO - ✅ DELETE完成: entry_id=145187, external_id=603692
2025-07-15 15:21:23,819 - INFO - ℹ️ DELETE操作跳过f6同步: entry_id=145187 (记录已删除)
2025-07-15 15:21:25,827 - INFO - 📋 2025/07/03 + 16:10 + 处理队列项: UPDATE - entry_id=145182 - queue_id=223 - 重试次数=0
2025-07-15 15:21:25,844 - WARNING - ⚠️ UPDATE操作跳过: entry_id=145182 (记录已被删除)
2025-07-15 15:21:25,845 - INFO - ✅ UPDATE跳过完成: entry_id=145182 (记录已被删除)
2025-07-15 15:21:25,850 - INFO - 📋 2025/07/03 + 16:10 + 处理队列项: DELETE - entry_id=145182 - queue_id=224 - 重试次数=0
2025-07-15 15:21:25,851 - INFO - 🔄 执行DELETE操作: entry_id=145182, external_id=603688
2025-07-15 15:21:29,704 - INFO - ✅ DELETE完成: entry_id=145182, external_id=603688
2025-07-15 15:21:29,705 - INFO - ℹ️ DELETE操作跳过f6同步: entry_id=145182 (记录已删除)
2025-07-15 15:21:39,733 - INFO - 📋 2025/07/03 + 16:10 + 处理队列项: UPDATE - entry_id=145193 - queue_id=225 - 重试次数=0
2025-07-15 15:21:39,755 - WARNING - ⚠️ UPDATE操作跳过: entry_id=145193 (记录已被删除)
2025-07-15 15:21:39,756 - INFO - ✅ UPDATE跳过完成: entry_id=145193 (记录已被删除)
2025-07-15 15:21:39,762 - INFO - 📋 2025/07/03 + 16:10 + 处理队列项: DELETE - entry_id=145193 - queue_id=226 - 重试次数=0
2025-07-15 15:21:39,762 - INFO - 🔄 执行DELETE操作: entry_id=145193, external_id=603699
2025-07-15 15:21:43,564 - INFO - ✅ DELETE完成: entry_id=145193, external_id=603699
2025-07-15 15:21:43,566 - INFO - ℹ️ DELETE操作跳过f6同步: entry_id=145193 (记录已删除)
2025-07-15 15:21:43,571 - INFO - 📋 2025/07/03 + 16:10 + 处理队列项: UPDATE - entry_id=145188 - queue_id=227 - 重试次数=0
2025-07-15 15:21:43,596 - WARNING - ⚠️ UPDATE操作跳过: entry_id=145188 (记录已被删除)
2025-07-15 15:21:43,597 - INFO - ✅ UPDATE跳过完成: entry_id=145188 (记录已被删除)
2025-07-15 15:21:43,603 - INFO - 📋 2025/07/03 + 16:10 + 处理队列项: DELETE - entry_id=145188 - queue_id=228 - 重试次数=0
2025-07-15 15:21:43,604 - INFO - 🔄 执行DELETE操作: entry_id=145188, external_id=603694
2025-07-15 15:21:47,700 - INFO - ✅ DELETE完成: entry_id=145188, external_id=603694
2025-07-15 15:21:47,702 - INFO - ℹ️ DELETE操作跳过f6同步: entry_id=145188 (记录已删除)
2025-07-15 15:21:47,706 - INFO - 📋 2025/07/03 + 16:10 + 处理队列项: UPDATE - entry_id=145183 - queue_id=229 - 重试次数=0
2025-07-15 15:21:47,734 - WARNING - ⚠️ UPDATE操作跳过: entry_id=145183 (记录已被删除)
2025-07-15 15:21:47,735 - INFO - ✅ UPDATE跳过完成: entry_id=145183 (记录已被删除)
2025-07-15 15:21:47,742 - INFO - 📋 2025/07/03 + 16:10 + 处理队列项: DELETE - entry_id=145183 - queue_id=230 - 重试次数=0
2025-07-15 15:21:47,742 - INFO - 🔄 执行DELETE操作: entry_id=145183, external_id=603689
2025-07-15 15:21:51,838 - INFO - ✅ DELETE完成: entry_id=145183, external_id=603689
2025-07-15 15:21:51,840 - INFO - ℹ️ DELETE操作跳过f6同步: entry_id=145183 (记录已删除)
2025-07-15 15:21:51,844 - INFO - 📋 2025/07/03 + 16:10 + 处理队列项: UPDATE - entry_id=145194 - queue_id=231 - 重试次数=0
2025-07-15 15:21:51,870 - WARNING - ⚠️ UPDATE操作跳过: entry_id=145194 (记录已被删除)
2025-07-15 15:21:51,871 - INFO - ✅ UPDATE跳过完成: entry_id=145194 (记录已被删除)
2025-07-15 15:21:51,877 - INFO - 📋 2025/07/03 + 16:10 + 处理队列项: DELETE - entry_id=145194 - queue_id=232 - 重试次数=0
2025-07-15 15:21:51,877 - INFO - 🔄 执行DELETE操作: entry_id=145194, external_id=603700
2025-07-15 15:21:55,809 - INFO - ✅ DELETE完成: entry_id=145194, external_id=603700
2025-07-15 15:21:55,811 - INFO - ℹ️ DELETE操作跳过f6同步: entry_id=145194 (记录已删除)
2025-07-15 15:21:55,828 - INFO - 📋 2025/07/03 + 16:10 + 处理队列项: UPDATE - entry_id=145189 - queue_id=233 - 重试次数=0
2025-07-15 15:21:55,860 - WARNING - ⚠️ UPDATE操作跳过: entry_id=145189 (记录已被删除)
2025-07-15 15:21:55,861 - INFO - ✅ UPDATE跳过完成: entry_id=145189 (记录已被删除)
2025-07-15 15:21:55,881 - INFO - 📋 2025/07/03 + 16:10 + 处理队列项: DELETE - entry_id=145189 - queue_id=234 - 重试次数=0
2025-07-15 15:21:55,881 - INFO - 🔄 执行DELETE操作: entry_id=145189, external_id=603695
2025-07-15 15:22:00,004 - INFO - 📋 2025/07/03 + 16:10 + 处理队列项: UPDATE - entry_id=145184 - queue_id=235 - 重试次数=0
2025-07-15 15:22:00,019 - WARNING - ⚠️ UPDATE操作跳过: entry_id=145184 (记录已被删除)
2025-07-15 15:22:00,020 - INFO - ✅ UPDATE跳过完成: entry_id=145184 (记录已被删除)
2025-07-15 15:22:00,026 - INFO - 📋 2025/07/03 + 16:10 + 处理队列项: DELETE - entry_id=145184 - queue_id=236 - 重试次数=0
2025-07-15 15:22:00,027 - INFO - 🔄 执行DELETE操作: entry_id=145184, external_id=603690
2025-07-15 15:22:00,157 - INFO - ✅ DELETE完成: entry_id=145189, external_id=603695
2025-07-15 15:22:00,161 - INFO - ℹ️ DELETE操作跳过f6同步: entry_id=145189 (记录已删除)
2025-07-15 15:22:04,119 - INFO - ✅ DELETE完成: entry_id=145184, external_id=603690
2025-07-15 15:22:04,121 - INFO - ℹ️ DELETE操作跳过f6同步: entry_id=145184 (记录已删除)
2025-07-15 15:22:22,104 - INFO - 📋 2025/07/03 + 16:10 + 处理队列项: UPDATE - entry_id=145207 - queue_id=237 - 重试次数=0
2025-07-15 15:22:22,120 - WARNING - ⚠️ UPDATE操作跳过: entry_id=145207 (记录已被删除)
2025-07-15 15:22:22,121 - INFO - ✅ UPDATE跳过完成: entry_id=145207 (记录已被删除)
2025-07-15 15:22:22,127 - INFO - 📋 2025/07/03 + 16:10 + 处理队列项: DELETE - entry_id=145207 - queue_id=238 - 重试次数=0
2025-07-15 15:22:22,128 - INFO - 🔄 执行DELETE操作: entry_id=145207, external_id=603713
2025-07-15 15:22:25,931 - INFO - ✅ DELETE完成: entry_id=145207, external_id=603713
2025-07-15 15:22:25,933 - INFO - ℹ️ DELETE操作跳过f6同步: entry_id=145207 (记录已删除)
2025-07-15 15:22:27,943 - INFO - 📋 2025/07/03 + 16:10 + 处理队列项: UPDATE - entry_id=145206 - queue_id=239 - 重试次数=0
2025-07-15 15:22:27,974 - WARNING - ⚠️ UPDATE操作跳过: entry_id=145206 (记录已被删除)
2025-07-15 15:22:27,975 - INFO - ✅ UPDATE跳过完成: entry_id=145206 (记录已被删除)
2025-07-15 15:22:27,982 - INFO - 📋 2025/07/03 + 16:10 + 处理队列项: DELETE - entry_id=145206 - queue_id=240 - 重试次数=0
2025-07-15 15:22:27,983 - INFO - 🔄 执行DELETE操作: entry_id=145206, external_id=603712
2025-07-15 15:22:32,226 - INFO - 📋 2025/07/03 + 16:10 + 处理队列项: UPDATE - entry_id=145205 - queue_id=241 - 重试次数=0
2025-07-15 15:22:32,256 - INFO - 🔄 执行UPDATE操作: entry_id=145205 -> external_id=603711
2025-07-15 15:22:32,256 - INFO - 📦 更新数据: {'employee_id': '215829', 'entry_date': '2025/07/15', 'model': 'NULL', 'number': None, 'factory_number': 'NULL', 'project_number': 'NULL', 'unit_number': 'NULL', 'category': '2', 'item': '2', 'duration': 2.0, 'department': '131'}
2025-07-15 15:22:32,256 - INFO - 📤 发送到Server6的更新数据: {'従業員ｺｰﾄﾞ': '215829', '日付': '2025-07-15', '機種': 'NULL', '号機': 'NULL', '工場製番': 'NULL', '工事番号': 'NULL', 'ﾕﾆｯﾄ番号': 'NULL', '区分': '2', '項目': '2', '時間': 2.0, '所属ｺｰﾄﾞ': '131'}
2025-07-15 15:22:32,600 - INFO - ✅ DELETE完成: entry_id=145206, external_id=603712
2025-07-15 15:22:32,602 - INFO - ℹ️ DELETE操作跳过f6同步: entry_id=145206 (记录已删除)
2025-07-15 15:22:32,606 - INFO - 📋 2025/07/03 + 16:10 + 处理队列项: DELETE - entry_id=145205 - queue_id=242 - 重试次数=0
2025-07-15 15:22:32,607 - INFO - 🔄 执行DELETE操作: entry_id=145205, external_id=603711
2025-07-15 15:22:36,706 - INFO - ✅ UPDATE完成: entry_id=145205 -> external_id=603711, source已更新为'system'
2025-07-15 15:22:40,197 - INFO - 📋 2025/07/03 + 16:10 + 处理队列项: UPDATE - entry_id=145204 - queue_id=243 - 重试次数=0
2025-07-15 15:22:40,218 - INFO - 📋 2025/07/03 + 16:10 + 处理队列项: DELETE - entry_id=145204 - queue_id=244 - 重试次数=0
2025-07-15 15:22:40,221 - INFO - 🔄 执行DELETE操作: entry_id=145204, external_id=603710
2025-07-15 15:22:40,231 - WARNING - ⚠️ UPDATE操作跳过: entry_id=145204 (记录已被删除)
2025-07-15 15:22:40,233 - INFO - ✅ UPDATE跳过完成: entry_id=145204 (记录已被删除)
2025-07-15 15:22:40,717 - INFO - ✅ DELETE完成: entry_id=145205, external_id=603711
2025-07-15 15:22:40,720 - INFO - ℹ️ DELETE操作跳过f6同步: entry_id=145205 (记录已删除)
2025-07-15 15:22:44,253 - INFO - 📋 2025/07/03 + 16:10 + 处理队列项: UPDATE - entry_id=145203 - queue_id=245 - 重试次数=0
2025-07-15 15:22:44,277 - WARNING - ⚠️ UPDATE操作跳过: entry_id=145203 (记录已被删除)
2025-07-15 15:22:44,278 - INFO - ✅ UPDATE跳过完成: entry_id=145203 (记录已被删除)
2025-07-15 15:22:44,284 - INFO - 📋 2025/07/03 + 16:10 + 处理队列项: DELETE - entry_id=145203 - queue_id=246 - 重试次数=0
2025-07-15 15:22:44,284 - INFO - 🔄 执行DELETE操作: entry_id=145203, external_id=603709
2025-07-15 15:22:44,666 - INFO - ✅ DELETE完成: entry_id=145204, external_id=603710
2025-07-15 15:22:44,669 - INFO - ℹ️ DELETE操作跳过f6同步: entry_id=145204 (记录已删除)
2025-07-15 15:22:48,572 - INFO - ✅ DELETE完成: entry_id=145203, external_id=603709
2025-07-15 15:22:48,574 - INFO - ℹ️ DELETE操作跳过f6同步: entry_id=145203 (记录已删除)
2025-07-15 15:22:48,578 - INFO - 📋 2025/07/03 + 16:10 + 处理队列项: UPDATE - entry_id=145202 - queue_id=247 - 重试次数=0
2025-07-15 15:22:48,597 - WARNING - ⚠️ UPDATE操作跳过: entry_id=145202 (记录已被删除)
2025-07-15 15:22:48,598 - INFO - ✅ UPDATE跳过完成: entry_id=145202 (记录已被删除)
2025-07-15 15:22:48,604 - INFO - 📋 2025/07/03 + 16:10 + 处理队列项: DELETE - entry_id=145202 - queue_id=248 - 重试次数=0
2025-07-15 15:22:48,605 - INFO - 🔄 执行DELETE操作: entry_id=145202, external_id=603707
2025-07-15 15:22:52,696 - INFO - 📋 2025/07/03 + 16:10 + 处理队列项: UPDATE - entry_id=145201 - queue_id=249 - 重试次数=0
2025-07-15 15:22:52,716 - INFO - ✅ DELETE完成: entry_id=145202, external_id=603707
2025-07-15 15:22:52,717 - INFO - ℹ️ DELETE操作跳过f6同步: entry_id=145202 (记录已删除)
2025-07-15 15:22:52,721 - WARNING - ⚠️ UPDATE操作跳过: entry_id=145201 (记录已被删除)
2025-07-15 15:22:52,721 - INFO - 📋 2025/07/03 + 16:10 + 处理队列项: DELETE - entry_id=145201 - queue_id=250 - 重试次数=0
2025-07-15 15:22:52,722 - INFO - 🔄 执行DELETE操作: entry_id=145201, external_id=603708
2025-07-15 15:22:52,722 - INFO - ✅ UPDATE跳过完成: entry_id=145201 (记录已被删除)
2025-07-15 15:22:56,738 - INFO - 📋 2025/07/03 + 16:10 + 处理队列项: UPDATE - entry_id=145200 - queue_id=251 - 重试次数=0
2025-07-15 15:22:56,760 - INFO - 📋 2025/07/03 + 16:10 + 处理队列项: DELETE - entry_id=145200 - queue_id=252 - 重试次数=0
2025-07-15 15:22:56,760 - INFO - 🔄 执行DELETE操作: entry_id=145200, external_id=603706
2025-07-15 15:22:56,767 - WARNING - ⚠️ UPDATE操作跳过: entry_id=145200 (记录已被删除)
2025-07-15 15:22:56,768 - INFO - ✅ UPDATE跳过完成: entry_id=145200 (记录已被删除)
2025-07-15 15:22:56,940 - INFO - ✅ DELETE完成: entry_id=145201, external_id=603708
2025-07-15 15:22:56,942 - INFO - ℹ️ DELETE操作跳过f6同步: entry_id=145201 (记录已删除)
2025-07-15 15:23:00,768 - INFO - 📋 2025/07/03 + 16:10 + 处理队列项: UPDATE - entry_id=145199 - queue_id=253 - 重试次数=0
2025-07-15 15:23:00,782 - INFO - 📋 2025/07/03 + 16:10 + 处理队列项: DELETE - entry_id=145199 - queue_id=254 - 重试次数=0
2025-07-15 15:23:00,783 - INFO - 🔄 执行DELETE操作: entry_id=145199, external_id=603705
2025-07-15 15:23:00,800 - WARNING - ⚠️ UPDATE操作跳过: entry_id=145199 (记录已被删除)
2025-07-15 15:23:00,804 - INFO - ✅ UPDATE跳过完成: entry_id=145199 (记录已被删除)
2025-07-15 15:23:01,149 - INFO - ✅ DELETE完成: entry_id=145200, external_id=603706
2025-07-15 15:23:01,150 - INFO - ℹ️ DELETE操作跳过f6同步: entry_id=145200 (记录已删除)
2025-07-15 15:23:04,820 - INFO - 📋 2025/07/03 + 16:10 + 处理队列项: UPDATE - entry_id=145198 - queue_id=255 - 重试次数=0
2025-07-15 15:23:04,844 - WARNING - ⚠️ UPDATE操作跳过: entry_id=145198 (记录已被删除)
2025-07-15 15:23:04,845 - INFO - ✅ UPDATE跳过完成: entry_id=145198 (记录已被删除)
2025-07-15 15:23:04,851 - INFO - 📋 2025/07/03 + 16:10 + 处理队列项: DELETE - entry_id=145198 - queue_id=256 - 重试次数=0
2025-07-15 15:23:04,852 - INFO - 🔄 执行DELETE操作: entry_id=145198, external_id=603704
2025-07-15 15:23:05,135 - INFO - ✅ DELETE完成: entry_id=145199, external_id=603705
2025-07-15 15:23:05,136 - INFO - ℹ️ DELETE操作跳过f6同步: entry_id=145199 (记录已删除)
2025-07-15 15:23:09,196 - INFO - ✅ DELETE完成: entry_id=145198, external_id=603704
2025-07-15 15:23:09,198 - INFO - ℹ️ DELETE操作跳过f6同步: entry_id=145198 (记录已删除)
2025-07-15 15:34:28,906 - INFO - ✅ 分区创建成功: entries_2025_07
2025-07-15 15:34:28,910 - INFO - ✅ 分区创建成功: entries_2025_08
2025-07-15 15:37:28,910 - INFO - 📋 2025/07/03 + 16:10 + 处理队列项: UPDATE - entry_id=145531 - queue_id=257 - 重试次数=0
2025-07-15 15:37:28,947 - INFO - 🔄 执行UPDATE操作: entry_id=145531 -> external_id=603413
2025-07-15 15:37:28,947 - INFO - 📦 更新数据: {'employee_id': '215829', 'entry_date': '2025/07/01', 'model': None, 'number': None, 'factory_number': None, 'project_number': '24585', 'unit_number': None, 'category': '3', 'item': '7', 'duration': 10.5, 'department': '131'}
2025-07-15 15:37:28,947 - INFO - 📤 发送到Server6的更新数据: {'従業員ｺｰﾄﾞ': '215829', '日付': '2025-07-01', '機種': 'NULL', '号機': 'NULL', '工場製番': 'NULL', '工事番号': '24585', 'ﾕﾆｯﾄ番号': 'NULL', '区分': '3', '項目': '7', '時間': 10.5, '所属ｺｰﾄﾞ': '131'}
2025-07-15 15:37:29,020 - INFO - 📋 2025/07/03 + 16:10 + 处理队列项: DELETE - entry_id=145531 - queue_id=258 - 重试次数=0
2025-07-15 15:37:29,021 - INFO - 🔄 执行DELETE操作: entry_id=145531, external_id=603413
2025-07-15 15:37:32,781 - INFO - ✅ UPDATE完成: entry_id=145531 -> external_id=603413, source已更新为'system'
2025-07-15 15:37:37,464 - INFO - ✅ DELETE完成: entry_id=145531, external_id=603413
2025-07-15 15:37:37,466 - INFO - ℹ️ DELETE操作跳过f6同步: entry_id=145531 (记录已删除)
2025-07-15 15:38:13,545 - INFO - 📋 2025/07/03 + 16:10 + 处理队列项: UPDATE - entry_id=145540 - queue_id=259 - 重试次数=0
2025-07-15 15:38:13,578 - WARNING - ⚠️ UPDATE操作跳过: entry_id=145540 (记录已被删除)
2025-07-15 15:38:13,579 - INFO - ✅ UPDATE跳过完成: entry_id=145540 (记录已被删除)
2025-07-15 15:38:13,586 - INFO - 📋 2025/07/03 + 16:10 + 处理队列项: DELETE - entry_id=145540 - queue_id=260 - 重试次数=0
2025-07-15 15:38:13,587 - INFO - 🔄 执行DELETE操作: entry_id=145540, external_id=603406
2025-07-15 15:38:17,526 - INFO - ✅ DELETE完成: entry_id=145540, external_id=603406
2025-07-15 15:38:17,528 - INFO - ℹ️ DELETE操作跳过f6同步: entry_id=145540 (记录已删除)
2025-07-15 15:38:37,582 - INFO - 📋 2025/07/03 + 16:10 + 处理队列项: UPDATE - entry_id=145541 - queue_id=261 - 重试次数=0
2025-07-15 15:38:37,598 - WARNING - ⚠️ UPDATE操作跳过: entry_id=145541 (记录已被删除)
2025-07-15 15:38:37,599 - INFO - ✅ UPDATE跳过完成: entry_id=145541 (记录已被删除)
2025-07-15 15:38:37,609 - INFO - 📋 2025/07/03 + 16:10 + 处理队列项: DELETE - entry_id=145541 - queue_id=262 - 重试次数=0
2025-07-15 15:38:37,610 - INFO - 🔄 执行DELETE操作: entry_id=145541, external_id=603407
2025-07-15 15:38:42,051 - INFO - ✅ DELETE完成: entry_id=145541, external_id=603407
2025-07-15 15:38:42,054 - INFO - ℹ️ DELETE操作跳过f6同步: entry_id=145541 (记录已删除)
2025-07-15 15:38:54,975 - INFO - 📋 2025/07/03 + 16:10 + 处理队列项: UPDATE - entry_id=145542 - queue_id=263 - 重试次数=0
2025-07-15 15:38:54,993 - WARNING - ⚠️ UPDATE操作跳过: entry_id=145542 (记录已被删除)
2025-07-15 15:38:54,995 - INFO - ✅ UPDATE跳过完成: entry_id=145542 (记录已被删除)
2025-07-15 15:38:55,005 - INFO - 📋 2025/07/03 + 16:10 + 处理队列项: DELETE - entry_id=145542 - queue_id=264 - 重试次数=0
2025-07-15 15:38:55,006 - INFO - 🔄 执行DELETE操作: entry_id=145542, external_id=603408
2025-07-15 15:38:58,853 - INFO - ✅ DELETE完成: entry_id=145542, external_id=603408
2025-07-15 15:38:58,855 - INFO - ℹ️ DELETE操作跳过f6同步: entry_id=145542 (记录已删除)
2025-07-15 15:39:10,884 - INFO - 📋 2025/07/03 + 16:10 + 处理队列项: UPDATE - entry_id=145619 - queue_id=265 - 重试次数=0
2025-07-15 15:39:10,902 - WARNING - ⚠️ UPDATE操作跳过: entry_id=145619 (记录已被删除)
2025-07-15 15:39:10,903 - INFO - ✅ UPDATE跳过完成: entry_id=145619 (记录已被删除)
2025-07-15 15:39:10,910 - INFO - 📋 2025/07/03 + 16:10 + 处理队列项: DELETE - entry_id=145619 - queue_id=266 - 重试次数=0
2025-07-15 15:39:10,910 - INFO - 🔄 执行DELETE操作: entry_id=145619, external_id=603680
2025-07-15 15:39:14,912 - INFO - ✅ DELETE完成: entry_id=145619, external_id=603680
2025-07-15 15:39:14,914 - INFO - ℹ️ DELETE操作跳过f6同步: entry_id=145619 (记录已删除)
2025-07-15 15:39:14,919 - INFO - 📋 2025/07/03 + 16:10 + 处理队列项: UPDATE - entry_id=145617 - queue_id=267 - 重试次数=0
2025-07-15 15:39:14,950 - WARNING - ⚠️ UPDATE操作跳过: entry_id=145617 (记录已被删除)
2025-07-15 15:39:14,951 - INFO - ✅ UPDATE跳过完成: entry_id=145617 (记录已被删除)
2025-07-15 15:39:14,957 - INFO - 📋 2025/07/03 + 16:10 + 处理队列项: DELETE - entry_id=145617 - queue_id=268 - 重试次数=0
2025-07-15 15:39:14,958 - INFO - 🔄 执行DELETE操作: entry_id=145617, external_id=603670
2025-07-15 15:39:19,033 - INFO - ✅ DELETE完成: entry_id=145617, external_id=603670
2025-07-15 15:39:19,034 - INFO - ℹ️ DELETE操作跳过f6同步: entry_id=145617 (记录已删除)
2025-07-15 15:39:19,039 - INFO - 📋 2025/07/03 + 16:10 + 处理队列项: UPDATE - entry_id=145616 - queue_id=269 - 重试次数=0
2025-07-15 15:39:19,068 - INFO - 🔄 执行UPDATE操作: entry_id=145616 -> external_id=603669
2025-07-15 15:39:19,068 - INFO - 📦 更新数据: {'employee_id': '215829', 'entry_date': '2025/07/15', 'model': 'NULL', 'number': None, 'factory_number': 'NULL', 'project_number': 'NULL', 'unit_number': 'NULL', 'category': '2', 'item': '2', 'duration': 2.0, 'department': '131'}
2025-07-15 15:39:19,068 - INFO - 📤 发送到Server6的更新数据: {'従業員ｺｰﾄﾞ': '215829', '日付': '2025-07-15', '機種': 'NULL', '号機': 'NULL', '工場製番': 'NULL', '工事番号': 'NULL', 'ﾕﾆｯﾄ番号': 'NULL', '区分': '2', '項目': '2', '時間': 2.0, '所属ｺｰﾄﾞ': '131'}
2025-07-15 15:39:19,258 - INFO - 📋 2025/07/03 + 16:10 + 处理队列项: DELETE - entry_id=145616 - queue_id=270 - 重试次数=0
2025-07-15 15:39:19,260 - INFO - 🔄 执行DELETE操作: entry_id=145616, external_id=603669
2025-07-15 15:39:23,398 - INFO - ✅ UPDATE完成: entry_id=145616 -> external_id=603669, source已更新为'system'
2025-07-15 15:39:27,788 - INFO - ✅ DELETE完成: entry_id=145616, external_id=603669
2025-07-15 15:39:27,790 - INFO - ℹ️ DELETE操作跳过f6同步: entry_id=145616 (记录已删除)
2025-07-15 15:40:43,439 - INFO - 📋 2025/07/03 + 16:10 + 处理队列项: UPDATE - entry_id=145212 - queue_id=271 - 重试次数=0
2025-07-15 15:40:43,458 - WARNING - ⚠️ UPDATE操作跳过: entry_id=145212 (记录已被删除)
2025-07-15 15:40:43,460 - INFO - ✅ UPDATE跳过完成: entry_id=145212 (记录已被删除)
2025-07-15 15:40:43,466 - INFO - 📋 2025/07/03 + 16:10 + 处理队列项: DELETE - entry_id=145212 - queue_id=272 - 重试次数=0
2025-07-15 15:40:43,466 - INFO - 🔄 执行DELETE操作: entry_id=145212, external_id=603718
2025-07-15 15:40:47,411 - INFO - ✅ DELETE完成: entry_id=145212, external_id=603718
2025-07-15 15:40:47,412 - INFO - ℹ️ DELETE操作跳过f6同步: entry_id=145212 (记录已删除)
2025-07-15 15:42:31,639 - INFO - 📋 2025/07/03 + 16:10 + 处理队列项: UPDATE - entry_id=145611 - queue_id=273 - 重试次数=0
2025-07-15 15:42:31,655 - WARNING - ⚠️ UPDATE操作跳过: entry_id=145611 (记录已被删除)
2025-07-15 15:42:31,656 - INFO - ✅ UPDATE跳过完成: entry_id=145611 (记录已被删除)
2025-07-15 15:42:31,661 - INFO - 📋 2025/07/03 + 16:10 + 处理队列项: DELETE - entry_id=145611 - queue_id=274 - 重试次数=0
2025-07-15 15:42:31,661 - INFO - 🔄 执行DELETE操作: entry_id=145611, external_id=603661
2025-07-15 15:42:35,763 - INFO - ✅ DELETE完成: entry_id=145611, external_id=603661
2025-07-15 15:42:35,765 - INFO - ℹ️ DELETE操作跳过f6同步: entry_id=145611 (记录已删除)
2025-07-15 15:42:51,802 - INFO - 📋 2025/07/03 + 16:10 + 处理队列项: UPDATE - entry_id=145610 - queue_id=275 - 重试次数=0
2025-07-15 15:42:51,818 - WARNING - ⚠️ UPDATE操作跳过: entry_id=145610 (记录已被删除)
2025-07-15 15:42:51,819 - INFO - ✅ UPDATE跳过完成: entry_id=145610 (记录已被删除)
2025-07-15 15:42:51,829 - INFO - 📋 2025/07/03 + 16:10 + 处理队列项: DELETE - entry_id=145610 - queue_id=276 - 重试次数=0
2025-07-15 15:42:51,830 - INFO - 🔄 执行DELETE操作: entry_id=145610, external_id=603660
2025-07-15 15:42:55,786 - INFO - ✅ DELETE完成: entry_id=145610, external_id=603660
2025-07-15 15:42:55,790 - INFO - ℹ️ DELETE操作跳过f6同步: entry_id=145610 (记录已删除)
2025-07-15 15:43:17,842 - INFO - 📋 2025/07/03 + 16:10 + 处理队列项: UPDATE - entry_id=145609 - queue_id=277 - 重试次数=0
2025-07-15 15:43:17,861 - WARNING - ⚠️ UPDATE操作跳过: entry_id=145609 (记录已被删除)
2025-07-15 15:43:17,861 - INFO - ✅ UPDATE跳过完成: entry_id=145609 (记录已被删除)
2025-07-15 15:43:17,867 - INFO - 📋 2025/07/03 + 16:10 + 处理队列项: DELETE - entry_id=145609 - queue_id=278 - 重试次数=0
2025-07-15 15:43:17,868 - INFO - 🔄 执行DELETE操作: entry_id=145609, external_id=603657
2025-07-15 15:43:22,081 - INFO - ✅ DELETE完成: entry_id=145609, external_id=603657
2025-07-15 15:43:22,083 - INFO - ℹ️ DELETE操作跳过f6同步: entry_id=145609 (记录已删除)
2025-07-15 15:43:29,920 - INFO - 📋 2025/07/03 + 16:10 + 处理队列项: UPDATE - entry_id=145608 - queue_id=279 - 重试次数=0
2025-07-15 15:43:29,953 - WARNING - ⚠️ UPDATE操作跳过: entry_id=145608 (记录已被删除)
2025-07-15 15:43:29,956 - INFO - ✅ UPDATE跳过完成: entry_id=145608 (记录已被删除)
2025-07-15 15:43:29,964 - INFO - 📋 2025/07/03 + 16:10 + 处理队列项: DELETE - entry_id=145608 - queue_id=280 - 重试次数=0
2025-07-15 15:43:29,964 - INFO - 🔄 执行DELETE操作: entry_id=145608, external_id=603655
2025-07-15 15:43:33,907 - INFO - ✅ DELETE完成: entry_id=145608, external_id=603655
2025-07-15 15:43:33,908 - INFO - ℹ️ DELETE操作跳过f6同步: entry_id=145608 (记录已删除)
2025-07-15 15:43:54,167 - INFO - 📋 2025/07/03 + 16:10 + 处理队列项: UPDATE - entry_id=145598 - queue_id=281 - 重试次数=0
2025-07-15 15:43:54,183 - WARNING - ⚠️ UPDATE操作跳过: entry_id=145598 (记录已被删除)
2025-07-15 15:43:54,184 - INFO - ✅ UPDATE跳过完成: entry_id=145598 (记录已被删除)
2025-07-15 15:43:54,189 - INFO - 📋 2025/07/03 + 16:10 + 处理队列项: DELETE - entry_id=145598 - queue_id=282 - 重试次数=0
2025-07-15 15:43:54,190 - INFO - 🔄 执行DELETE操作: entry_id=145598, external_id=603643
2025-07-15 15:43:58,144 - INFO - ✅ DELETE完成: entry_id=145598, external_id=603643
2025-07-15 15:43:58,145 - INFO - ℹ️ DELETE操作跳过f6同步: entry_id=145598 (记录已删除)
2025-07-15 15:44:07,979 - INFO - 📋 2025/07/03 + 16:10 + 处理队列项: UPDATE - entry_id=145597 - queue_id=283 - 重试次数=0
2025-07-15 15:44:07,999 - WARNING - ⚠️ UPDATE操作跳过: entry_id=145597 (记录已被删除)
2025-07-15 15:44:08,000 - INFO - ✅ UPDATE跳过完成: entry_id=145597 (记录已被删除)
2025-07-15 15:44:08,006 - INFO - 📋 2025/07/03 + 16:10 + 处理队列项: DELETE - entry_id=145597 - queue_id=284 - 重试次数=0
2025-07-15 15:44:08,007 - INFO - 🔄 执行DELETE操作: entry_id=145597, external_id=603642
2025-07-15 15:44:11,950 - INFO - ✅ DELETE完成: entry_id=145597, external_id=603642
2025-07-15 15:44:11,952 - INFO - ℹ️ DELETE操作跳过f6同步: entry_id=145597 (记录已删除)
2025-07-15 15:44:13,961 - INFO - 📋 2025/07/03 + 16:10 + 处理队列项: UPDATE - entry_id=145596 - queue_id=285 - 重试次数=0
2025-07-15 15:44:13,986 - WARNING - ⚠️ UPDATE操作跳过: entry_id=145596 (记录已被删除)
2025-07-15 15:44:13,987 - INFO - ✅ UPDATE跳过完成: entry_id=145596 (记录已被删除)
2025-07-15 15:44:13,992 - INFO - 📋 2025/07/03 + 16:10 + 处理队列项: DELETE - entry_id=145596 - queue_id=286 - 重试次数=0
2025-07-15 15:44:13,992 - INFO - 🔄 执行DELETE操作: entry_id=145596, external_id=603639
2025-07-15 15:44:17,799 - INFO - ✅ DELETE完成: entry_id=145596, external_id=603639
2025-07-15 15:44:17,801 - INFO - ℹ️ DELETE操作跳过f6同步: entry_id=145596 (记录已删除)
2025-07-15 15:44:21,814 - INFO - 📋 2025/07/03 + 16:10 + 处理队列项: UPDATE - entry_id=145583 - queue_id=287 - 重试次数=0
2025-07-15 15:44:21,829 - INFO - 🔄 执行UPDATE操作: entry_id=145583 -> external_id=603624
2025-07-15 15:44:21,830 - INFO - 📦 更新数据: {'employee_id': '215829', 'entry_date': '2025/07/15', 'model': 'test', 'number': '1', 'factory_number': 'test', 'project_number': 'test', 'unit_number': 'test', 'category': '1', 'item': '1', 'duration': 0.5, 'department': '131'}
2025-07-15 15:44:21,830 - INFO - 📤 发送到Server6的更新数据: {'従業員ｺｰﾄﾞ': '215829', '日付': '2025-07-15', '機種': 'test', '号機': '1', '工場製番': 'test', '工事番号': 'test', 'ﾕﾆｯﾄ番号': 'test', '区分': '1', '項目': '1', '時間': 0.5, '所属ｺｰﾄﾞ': '131'}
2025-07-15 15:44:22,200 - INFO - 📋 2025/07/03 + 16:10 + 处理队列项: DELETE - entry_id=145583 - queue_id=288 - 重试次数=0
2025-07-15 15:44:22,201 - INFO - 🔄 执行DELETE操作: entry_id=145583, external_id=603624
2025-07-15 15:44:26,153 - INFO - ✅ UPDATE完成: entry_id=145583 -> external_id=603624, source已更新为'system'
2025-07-15 15:44:31,830 - INFO - ✅ DELETE完成: entry_id=145583, external_id=603624
2025-07-15 15:44:31,832 - INFO - ℹ️ DELETE操作跳过f6同步: entry_id=145583 (记录已删除)
2025-07-15 15:52:12,950 - INFO - 📋 2025/07/03 + 16:10 + 处理队列项: INSERT - entry_id=145952 - queue_id=289 - 重试次数=0
2025-07-15 15:52:12,968 - INFO - 🔄 执行INSERT操作: entry_id=145952, employee_id=215829
2025-07-15 15:52:12,968 - INFO - 📤 发送到Server6的数据: {'従業員ｺｰﾄﾞ': '215829', '日付': '2025-07-15', '機種': 'NULL', '号機': 'NULL', '工場製番': 'NULL', '工事番号': 'NULL', 'ﾕﾆｯﾄ番号': 'NULL', '区分': '1', '項目': '1', '時間': 1.0, '所属ｺｰﾄﾞ': '131'}
2025-07-15 15:52:18,925 - INFO - ✅ INSERT完成: entry_id=145952 -> external_id=603719
2025-07-15 16:12:10,554 - INFO - 监听任务被取消
2025-07-15 16:12:10,555 - INFO - 🔌 MongoDB连接已关闭
2025-07-15 16:12:10,557 - INFO - 🔌 MongoDB连接已关闭
2025-07-15 16:12:10,558 - INFO - 🔌 Redis连接已关闭
2025-07-15 16:12:10,558 - INFO - 🔌 PostgreSQL连接已关闭
2025-07-15 16:12:10,559 - INFO - 🔌 Redis连接已关闭
2025-07-15 16:12:10,559 - INFO - 🔌 PostgreSQL连接已关闭
2025-07-15 16:12:10,559 - INFO - 🔌 PostgreSQL连接已关闭
2025-07-15 16:12:10,559 - INFO - 🔌 f1监听器服务已停止
2025-07-15 16:12:10,559 - INFO - 🔌 f4操作处理服务已停止
2025-07-15 16:12:10,559 - INFO - 🔌 Redis连接已关闭
2025-07-15 16:12:10,559 - INFO - 🔌 f3数据拉取服务已停止
2025-07-15 16:12:10,713 - INFO - 🔌 推送工作线程停止: worker_2
2025-07-15 16:12:10,791 - INFO - 🔌 推送工作线程停止: worker_1
2025-07-15 16:12:10,824 - INFO - 🔌 推送工作线程停止: worker_3
2025-07-15 16:12:10,853 - INFO - 🔌 推送工作线程停止: worker_4
2025-07-15 16:12:10,855 - INFO - 🔌 MongoDB连接已关闭
2025-07-15 16:12:10,855 - INFO - AIOHTTP session for Server6 closed.
2025-07-15 16:12:10,855 - INFO - 🔌 Redis连接已关闭
2025-07-15 16:12:10,872 - INFO - 🔌 PostgreSQL连接已关闭
2025-07-15 16:12:10,872 - INFO - 🔌 f2推送回写服务已停止
2025-07-15 16:12:10,873 - ERROR - Server5微服务启动失败: asyncio.run() cannot be called from a running event loop
2025-07-15 16:12:11,236 - INFO - 🔌 PostgreSQL连接已关闭
2025-07-15 16:12:11,236 - INFO - 🔌 MongoDB连接已关闭
2025-07-15 16:12:11,236 - INFO - 🔌 PostgreSQL连接已关闭
2025-07-15 16:12:11,236 - INFO - 🔌 MongoDB连接已关闭
2025-07-15 16:12:11,236 - INFO - 🔌 PostgreSQL连接已关闭
2025-07-15 16:12:11,236 - INFO - 🔌 MongoDB连接已关闭
2025-07-15 16:12:11,236 - INFO - 🔌 Redis连接已关闭
2025-07-15 16:12:11,236 - INFO - 🔌 Redis连接已关闭
2025-07-15 16:12:11,236 - INFO - 🔌 Redis连接已关闭
2025-07-15 16:12:11,236 - INFO - 🔌 f1监听器服务已停止
2025-07-15 16:12:11,236 - INFO - 🔌 f2推送回写服务已停止
2025-07-15 16:12:11,236 - INFO - 🔌 f4操作处理服务已停止
2025-07-15 16:12:11,258 - ERROR - Task was destroyed but it is pending!
task: <Task pending name='Task-11770' coro=<Connection._cancel() running at /home/<USER>/miniconda3/envs/my_suite_unified/lib/python3.12/site-packages/asyncpg/connection.py:1646> wait_for=<Future pending cb=[Task.task_wakeup()]>>
2025-07-15 16:12:12,355 - INFO - 🔊 f1监听器服务初始化完成
2025-07-15 16:12:12,355 - INFO - 📡 Server6客户端初始化 (新版): http://************:8019
2025-07-15 16:12:12,355 - INFO - 📤 f2推送回写引擎初始化完成 (修复版本: 直接轮询PostgreSQL)
2025-07-15 16:12:12,355 - INFO - 📡 Server6客户端初始化 (新版): http://************:8019
2025-07-15 16:12:12,357 - INFO - 📥 f3数据拉取器初始化完成 (Staging & UPSERT 模式)
2025-07-15 16:12:12,357 - INFO - 🎮 f4操作处理器初始化完成
2025-07-15 16:12:12,371 - INFO - Redis连接成功
2025-07-15 16:12:12,371 - INFO - Redis连接成功
2025-07-15 16:12:12,371 - INFO - Redis连接成功
2025-07-15 16:12:12,377 - INFO - ✅ MongoDB连接成功
2025-07-15 16:12:12,377 - INFO - ✅ MongoDB连接成功
2025-07-15 16:12:12,380 - INFO - ✅ MongoDB连接成功
2025-07-15 16:12:12,802 - INFO - ✅ PostgreSQL连接池创建成功
2025-07-15 16:12:12,803 - INFO - Redis连接成功
2025-07-15 16:12:12,803 - INFO - ✅ f3数据拉取服务启动成功，预定执行时间: ['02:00', '03:00']
2025-07-15 16:12:12,803 - INFO - 🚀 数据拉取调度器启动，预定运行时间: ['02:00', '03:00']
2025-07-15 16:12:12,804 - INFO - 下一次f3/f5同步任务将在 2025-07-16 02:00:00 执行，等待 35267 秒...
2025-07-15 16:12:12,807 - INFO - ✅ PostgreSQL连接池创建成功
2025-07-15 16:12:12,807 - INFO - ✅ f1监听器服务启动成功
2025-07-15 16:12:12,840 - INFO - ✅ 分区创建成功: entries_2025_07
2025-07-15 16:12:12,844 - INFO - ✅ 分区创建成功: entries_2025_08
2025-07-15 16:12:12,845 - INFO - ✅ PostgreSQL连接池创建成功
2025-07-15 16:12:12,845 - INFO - ✅ f2推送回写服务启动成功，工作线程数: 4
2025-07-15 16:12:12,845 - INFO - 🚀 推送工作线程启动: worker_1
2025-07-15 16:12:12,845 - INFO - 🚀 推送工作线程启动: worker_2
2025-07-15 16:12:12,845 - INFO - 🚀 推送工作线程启动: worker_3
2025-07-15 16:12:12,846 - INFO - 🚀 推送工作线程启动: worker_4
2025-07-15 16:12:13,035 - INFO - ✅ PostgreSQL连接池创建成功
2025-07-15 16:12:13,036 - INFO - ✅ f4操作处理服务启动成功
2025-07-15 16:12:13,071 - INFO - 📡 开始监听频道: push_job
2025-07-15 16:12:13,072 - INFO - 📡 开始监听频道: partition_check
2025-07-15 16:12:13,074 - INFO - 📡 开始监听频道: sync_trigger
2025-07-15 16:12:49,294 - INFO - 📋 2025/07/03 + 16:10 + 处理队列项: INSERT - entry_id=145953 - queue_id=290 - 重试次数=0
2025-07-15 16:12:49,393 - INFO - 🔄 执行INSERT操作: entry_id=145953, employee_id=215829
2025-07-15 16:12:49,394 - INFO - 📤 发送到Server6的数据: {'従業員ｺｰﾄﾞ': '215829', '日付': '2025-07-15', '機種': '', '号機': '', '工場製番': '', '工事番号': '', 'ﾕﾆｯﾄ番号': '', '区分': '1', '項目': '1', '時間': 1.0, '所属ｺｰﾄﾞ': '131'}
2025-07-15 16:12:49,394 - INFO - AIOHTTP session for Server6 created.
2025-07-15 16:12:53,786 - INFO - ✅ INSERT完成: entry_id=145953 -> external_id=603720
2025-07-15 16:14:27,206 - INFO - 📋 2025/07/03 + 16:10 + 处理队列项: UPDATE - entry_id=145180 - queue_id=291 - 重试次数=0
2025-07-15 16:14:27,313 - INFO - 🔄 执行UPDATE操作: entry_id=145180 -> external_id=603687
2025-07-15 16:14:27,313 - INFO - 📦 更新数据: {'employee_id': '215829', 'entry_date': '2025/07/01', 'model': None, 'number': None, 'factory_number': None, 'project_number': '24585', 'unit_number': None, 'category': '3', 'item': '7', 'duration': 10.5, 'department': '131'}
2025-07-15 16:14:27,313 - INFO - 📤 发送到Server6的更新数据: {'従業員ｺｰﾄﾞ': '215829', '日付': '2025-07-01', '機種': '', '号機': '', '工場製番': '', '工事番号': '24585', 'ﾕﾆｯﾄ番号': '', '区分': '3', '項目': '7', '時間': 10.5, '所属ｺｰﾄﾞ': '131'}
2025-07-15 16:14:31,432 - INFO - ✅ UPDATE完成: entry_id=145180 -> external_id=603687, source已更新为'system'
2025-07-15 16:22:52,175 - INFO - 监听任务被取消
2025-07-15 16:22:52,176 - INFO - 🔌 MongoDB连接已关闭
2025-07-15 16:22:52,177 - INFO - 🔌 MongoDB连接已关闭
2025-07-15 16:22:52,178 - INFO - 🔌 Redis连接已关闭
2025-07-15 16:22:52,178 - INFO - 🔌 PostgreSQL连接已关闭
2025-07-15 16:22:52,178 - INFO - 🔌 Redis连接已关闭
2025-07-15 16:22:52,178 - INFO - 🔌 PostgreSQL连接已关闭
2025-07-15 16:22:52,178 - INFO - 🔌 PostgreSQL连接已关闭
2025-07-15 16:22:52,179 - INFO - 🔌 f1监听器服务已停止
2025-07-15 16:22:52,179 - INFO - 🔌 f4操作处理服务已停止
2025-07-15 16:22:52,179 - INFO - 🔌 Redis连接已关闭
2025-07-15 16:22:52,179 - INFO - 🔌 f3数据拉取服务已停止
2025-07-15 16:22:52,277 - INFO - 🔌 推送工作线程停止: worker_3
2025-07-15 16:22:52,325 - INFO - 🔌 推送工作线程停止: worker_1
2025-07-15 16:22:52,363 - INFO - 🔌 推送工作线程停止: worker_4
2025-07-15 16:22:52,389 - INFO - 🔌 推送工作线程停止: worker_2
2025-07-15 16:22:52,390 - INFO - 🔌 MongoDB连接已关闭
2025-07-15 16:22:52,390 - INFO - AIOHTTP session for Server6 closed.
2025-07-15 16:22:52,391 - INFO - 🔌 Redis连接已关闭
2025-07-15 16:22:52,396 - INFO - 🔌 PostgreSQL连接已关闭
2025-07-15 16:22:52,396 - INFO - 🔌 f2推送回写服务已停止
2025-07-15 16:22:52,397 - ERROR - Server5微服务启动失败: asyncio.run() cannot be called from a running event loop
2025-07-15 16:22:52,567 - INFO - 🔌 PostgreSQL连接已关闭
2025-07-15 16:22:52,568 - INFO - 🔌 MongoDB连接已关闭
2025-07-15 16:22:52,568 - INFO - 🔌 PostgreSQL连接已关闭
2025-07-15 16:22:52,568 - INFO - 🔌 MongoDB连接已关闭
2025-07-15 16:22:52,568 - INFO - 🔌 PostgreSQL连接已关闭
2025-07-15 16:22:52,568 - INFO - 🔌 MongoDB连接已关闭
2025-07-15 16:22:52,568 - INFO - 🔌 Redis连接已关闭
2025-07-15 16:22:52,568 - INFO - 🔌 Redis连接已关闭
2025-07-15 16:22:52,568 - INFO - 🔌 Redis连接已关闭
2025-07-15 16:22:52,568 - INFO - 🔌 f1监听器服务已停止
2025-07-15 16:22:52,568 - INFO - 🔌 f2推送回写服务已停止
2025-07-15 16:22:52,568 - INFO - 🔌 f4操作处理服务已停止
2025-07-15 16:22:52,589 - ERROR - Task was destroyed but it is pending!
task: <Task pending name='Task-1355' coro=<Connection._cancel() running at /home/<USER>/miniconda3/envs/my_suite_unified/lib/python3.12/site-packages/asyncpg/connection.py:1646> wait_for=<Future pending cb=[Task.task_wakeup()]>>
2025-07-15 16:22:53,793 - INFO - 🔊 f1监听器服务初始化完成
2025-07-15 16:22:53,793 - INFO - 📡 Server6客户端初始化 (新版): http://************:8019
2025-07-15 16:22:53,793 - INFO - 📤 f2推送回写引擎初始化完成 (修复版本: 直接轮询PostgreSQL)
2025-07-15 16:22:53,793 - INFO - 📡 Server6客户端初始化 (新版): http://************:8019
2025-07-15 16:22:53,794 - INFO - 📥 f3数据拉取器初始化完成 (Staging & UPSERT 模式)
2025-07-15 16:22:53,794 - INFO - 🎮 f4操作处理器初始化完成
2025-07-15 16:22:53,804 - INFO - Redis连接成功
2025-07-15 16:22:53,804 - INFO - Redis连接成功
2025-07-15 16:22:53,804 - INFO - Redis连接成功
2025-07-15 16:22:53,810 - INFO - ✅ MongoDB连接成功
2025-07-15 16:22:53,810 - INFO - ✅ MongoDB连接成功
2025-07-15 16:22:53,811 - INFO - ✅ MongoDB连接成功
2025-07-15 16:22:54,139 - INFO - ✅ PostgreSQL连接池创建成功
2025-07-15 16:22:54,158 - INFO - ✅ PostgreSQL连接池创建成功
2025-07-15 16:22:54,158 - INFO - ✅ f1监听器服务启动成功
2025-07-15 16:22:54,159 - INFO - Redis连接成功
2025-07-15 16:22:54,159 - INFO - ✅ f3数据拉取服务启动成功，预定执行时间: ['02:00', '03:00']
2025-07-15 16:22:54,159 - INFO - 🚀 数据拉取调度器启动，预定运行时间: ['02:00', '03:00']
2025-07-15 16:22:54,160 - INFO - 下一次f3/f5同步任务将在 2025-07-16 02:00:00 执行，等待 34626 秒...
2025-07-15 16:22:54,162 - INFO - ✅ PostgreSQL连接池创建成功
2025-07-15 16:22:54,162 - INFO - ✅ f2推送回写服务启动成功，工作线程数: 4
2025-07-15 16:22:54,162 - INFO - 🚀 推送工作线程启动: worker_1
2025-07-15 16:22:54,163 - INFO - 🚀 推送工作线程启动: worker_2
2025-07-15 16:22:54,163 - INFO - 🚀 推送工作线程启动: worker_3
2025-07-15 16:22:54,163 - INFO - 🚀 推送工作线程启动: worker_4
2025-07-15 16:22:54,168 - INFO - ✅ 分区创建成功: entries_2025_07
2025-07-15 16:22:54,171 - INFO - ✅ 分区创建成功: entries_2025_08
2025-07-15 16:22:54,281 - INFO - ✅ PostgreSQL连接池创建成功
2025-07-15 16:22:54,281 - INFO - ✅ f4操作处理服务启动成功
2025-07-15 16:22:54,340 - INFO - 📡 开始监听频道: push_job
2025-07-15 16:22:54,342 - INFO - 📡 开始监听频道: partition_check
2025-07-15 16:22:54,343 - INFO - 📡 开始监听频道: sync_trigger
2025-07-15 16:23:48,312 - INFO - 📋 2025/07/03 + 16:10 + 处理队列项: UPDATE - entry_id=145208 - queue_id=292 - 重试次数=0
2025-07-15 16:23:48,412 - INFO - 🔄 执行UPDATE操作: entry_id=145208 -> external_id=603714
2025-07-15 16:23:48,412 - INFO - 📦 更新数据: {'employee_id': '215829', 'entry_date': '2025/07/02', 'model': None, 'number': None, 'factory_number': None, 'project_number': '24585', 'unit_number': None, 'category': '3', 'item': '7', 'duration': 9.0, 'department': '131'}
2025-07-15 16:23:48,412 - INFO - 📤 发送到Server6的更新数据: {'従業員ｺｰﾄﾞ': '215829', '日付': '2025-07-02', '機種': None, '号機': None, '工場製番': None, '工事番号': '24585', 'ﾕﾆｯﾄ番号': None, '区分': '3', '項目': '7', '時間': 9.0, '所属ｺｰﾄﾞ': '131'}
2025-07-15 16:23:48,412 - INFO - AIOHTTP session for Server6 created.
2025-07-15 16:23:52,892 - INFO - ✅ UPDATE完成: entry_id=145208 -> external_id=603714, source已更新为'system'
2025-07-15 16:24:18,373 - INFO - 📋 2025/07/03 + 16:10 + 处理队列项: UPDATE - entry_id=145210 - queue_id=293 - 重试次数=0
2025-07-15 16:24:18,483 - INFO - 🔄 执行UPDATE操作: entry_id=145210 -> external_id=603717
2025-07-15 16:24:18,483 - INFO - 📦 更新数据: {'employee_id': '215829', 'entry_date': '2025/07/03', 'model': None, 'number': None, 'factory_number': None, 'project_number': '24585', 'unit_number': None, 'category': '3', 'item': '7', 'duration': 9.0, 'department': '131'}
2025-07-15 16:24:18,484 - INFO - 📤 发送到Server6的更新数据: {'従業員ｺｰﾄﾞ': '215829', '日付': '2025-07-03', '機種': None, '号機': None, '工場製番': None, '工事番号': '24585', 'ﾕﾆｯﾄ番号': None, '区分': '3', '項目': '7', '時間': 9.0, '所属ｺｰﾄﾞ': '131'}
2025-07-15 16:24:22,339 - INFO - ✅ UPDATE完成: entry_id=145210 -> external_id=603717, source已更新为'system'
2025-07-15 16:24:28,362 - INFO - 📋 2025/07/03 + 16:10 + 处理队列项: UPDATE - entry_id=145211 - queue_id=294 - 重试次数=0
2025-07-15 16:24:28,379 - INFO - 🔄 执行UPDATE操作: entry_id=145211 -> external_id=603716
2025-07-15 16:24:28,379 - INFO - 📦 更新数据: {'employee_id': '215829', 'entry_date': '2025/07/04', 'model': None, 'number': None, 'factory_number': None, 'project_number': '24585', 'unit_number': None, 'category': '3', 'item': '7', 'duration': 9.0, 'department': '131'}
2025-07-15 16:24:28,379 - INFO - 📤 发送到Server6的更新数据: {'従業員ｺｰﾄﾞ': '215829', '日付': '2025-07-04', '機種': None, '号機': None, '工場製番': None, '工事番号': '24585', 'ﾕﾆｯﾄ番号': None, '区分': '3', '項目': '7', '時間': 9.0, '所属ｺｰﾄﾞ': '131'}
2025-07-15 16:24:32,262 - INFO - ✅ UPDATE完成: entry_id=145211 -> external_id=603716, source已更新为'system'
2025-07-15 16:24:44,294 - INFO - 📋 2025/07/03 + 16:10 + 处理队列项: UPDATE - entry_id=145953 - queue_id=295 - 重试次数=0
2025-07-15 16:24:44,316 - INFO - 🔄 执行UPDATE操作: entry_id=145953 -> external_id=603720
2025-07-15 16:24:44,316 - INFO - 📦 更新数据: {'employee_id': '215829', 'entry_date': '2025/07/15', 'model': None, 'number': None, 'factory_number': None, 'project_number': None, 'unit_number': None, 'category': '1', 'item': '1', 'duration': 1.2, 'department': '131'}
2025-07-15 16:24:44,316 - INFO - 📤 发送到Server6的更新数据: {'従業員ｺｰﾄﾞ': '215829', '日付': '2025-07-15', '機種': None, '号機': None, '工場製番': None, '工事番号': None, 'ﾕﾆｯﾄ番号': None, '区分': '1', '項目': '1', '時間': 1.2, '所属ｺｰﾄﾞ': '131'}
2025-07-15 16:24:48,178 - INFO - ✅ UPDATE完成: entry_id=145953 -> external_id=603720, source已更新为'system'
2025-07-15 16:24:56,638 - INFO - 📋 2025/07/03 + 16:10 + 处理队列项: UPDATE - entry_id=145952 - queue_id=296 - 重试次数=0
2025-07-15 16:24:56,731 - INFO - 🔄 执行UPDATE操作: entry_id=145952 -> external_id=603719
2025-07-15 16:24:56,731 - INFO - 📦 更新数据: {'employee_id': '215829', 'entry_date': '2025/07/15', 'model': None, 'number': None, 'factory_number': None, 'project_number': None, 'unit_number': None, 'category': '1', 'item': '1', 'duration': 1.1, 'department': '131'}
2025-07-15 16:24:56,731 - INFO - 📤 发送到Server6的更新数据: {'従業員ｺｰﾄﾞ': '215829', '日付': '2025-07-15', '機種': None, '号機': None, '工場製番': None, '工事番号': None, 'ﾕﾆｯﾄ番号': None, '区分': '1', '項目': '1', '時間': 1.1, '所属ｺｰﾄﾞ': '131'}
2025-07-15 16:25:01,200 - INFO - ✅ UPDATE完成: entry_id=145952 -> external_id=603719, source已更新为'system'
2025-07-15 16:25:36,740 - INFO - 📋 2025/07/03 + 16:10 + 处理队列项: UPDATE - entry_id=145195 - queue_id=297 - 重试次数=0
2025-07-15 16:25:36,833 - INFO - 🔄 执行UPDATE操作: entry_id=145195 -> external_id=603702
2025-07-15 16:25:36,833 - INFO - 📦 更新数据: {'employee_id': '215829', 'entry_date': '2025/07/08', 'model': None, 'number': None, 'factory_number': 'HA0484', 'project_number': None, 'unit_number': None, 'category': '0', 'item': '7', 'duration': 9.1, 'department': '131'}
2025-07-15 16:25:36,833 - INFO - 📤 发送到Server6的更新数据: {'従業員ｺｰﾄﾞ': '215829', '日付': '2025-07-08', '機種': None, '号機': None, '工場製番': 'HA0484', '工事番号': None, 'ﾕﾆｯﾄ番号': None, '区分': '0', '項目': '7', '時間': 9.1, '所属ｺｰﾄﾞ': '131'}
2025-07-15 16:25:41,237 - INFO - ✅ UPDATE完成: entry_id=145195 -> external_id=603702, source已更新为'system'
2025-07-15 16:25:47,184 - INFO - 📋 2025/07/03 + 16:10 + 处理队列项: UPDATE - entry_id=145195 - queue_id=298 - 重试次数=0
2025-07-15 16:25:47,200 - INFO - 🔄 执行UPDATE操作: entry_id=145195 -> external_id=603702
2025-07-15 16:25:47,200 - INFO - 📦 更新数据: {'employee_id': '215829', 'entry_date': '2025/07/08', 'model': None, 'number': None, 'factory_number': 'HA0484', 'project_number': None, 'unit_number': None, 'category': '0', 'item': '7', 'duration': 9.0, 'department': '131'}
2025-07-15 16:25:47,201 - INFO - 📤 发送到Server6的更新数据: {'従業員ｺｰﾄﾞ': '215829', '日付': '2025-07-08', '機種': None, '号機': None, '工場製番': 'HA0484', '工事番号': None, 'ﾕﾆｯﾄ番号': None, '区分': '0', '項目': '7', '時間': 9.0, '所属ｺｰﾄﾞ': '131'}
2025-07-15 16:25:51,353 - INFO - ✅ UPDATE完成: entry_id=145195 -> external_id=603702, source已更新为'system'
2025-07-15 16:26:29,361 - INFO - 📋 2025/07/03 + 16:10 + 处理队列项: UPDATE - entry_id=145180 - queue_id=299 - 重试次数=0
2025-07-15 16:26:29,391 - INFO - 🔄 执行UPDATE操作: entry_id=145180 -> external_id=603687
2025-07-15 16:26:29,391 - INFO - 📦 更新数据: {'employee_id': '215829', 'entry_date': '2025/07/01', 'model': None, 'number': None, 'factory_number': None, 'project_number': '24585', 'unit_number': None, 'category': '3', 'item': '7', 'duration': 10.5, 'department': '131'}
2025-07-15 16:26:29,391 - INFO - 📤 发送到Server6的更新数据: {'従業員ｺｰﾄﾞ': '215829', '日付': '2025-07-01', '機種': None, '号機': None, '工場製番': None, '工事番号': '24585', 'ﾕﾆｯﾄ番号': None, '区分': '3', '項目': '7', '時間': 10.5, '所属ｺｰﾄﾞ': '131'}
2025-07-15 16:26:34,002 - INFO - ✅ UPDATE完成: entry_id=145180 -> external_id=603687, source已更新为'system'
2025-07-15 16:34:50,657 - INFO - 📋 2025/07/03 + 16:10 + 处理队列项: INSERT - entry_id=145954 - queue_id=300 - 重试次数=0
2025-07-15 16:34:50,674 - INFO - 🔄 执行INSERT操作: entry_id=145954, employee_id=215829
2025-07-15 16:34:50,674 - INFO - 📤 发送到Server6的数据: {'従業員ｺｰﾄﾞ': '215829', '日付': '2025-07-15', '機種': None, '号機': None, '工場製番': None, '工事番号': None, 'ﾕﾆｯﾄ番号': None, '区分': '1', '項目': '1', '時間': 1.0, '所属ｺｰﾄﾞ': '131'}
2025-07-15 16:34:53,220 - INFO - 📋 2025/07/03 + 16:10 + 处理队列项: INSERT - entry_id=145955 - queue_id=301 - 重试次数=0
2025-07-15 16:34:53,240 - INFO - 🔄 执行INSERT操作: entry_id=145955, employee_id=215829
2025-07-15 16:34:53,240 - INFO - 📤 发送到Server6的数据: {'従業員ｺｰﾄﾞ': '215829', '日付': '2025-07-15', '機種': None, '号機': None, '工場製番': None, '工事番号': None, 'ﾕﾆｯﾄ番号': None, '区分': '1', '項目': '1', '時間': 1.0, '所属ｺｰﾄﾞ': '131'}
2025-07-15 16:34:55,161 - INFO - ✅ INSERT完成: entry_id=145954 -> external_id=603721
2025-07-15 16:34:55,552 - INFO - 📋 2025/07/03 + 16:10 + 处理队列项: INSERT - entry_id=145956 - queue_id=302 - 重试次数=0
2025-07-15 16:34:55,585 - INFO - 🔄 执行INSERT操作: entry_id=145956, employee_id=215829
2025-07-15 16:34:55,586 - INFO - 📤 发送到Server6的数据: {'従業員ｺｰﾄﾞ': '215829', '日付': '2025-07-15', '機種': None, '号機': None, '工場製番': None, '工事番号': None, 'ﾕﾆｯﾄ番号': None, '区分': '1', '項目': '1', '時間': 1.0, '所属ｺｰﾄﾞ': '131'}
2025-07-15 16:34:58,675 - INFO - 📋 2025/07/03 + 16:10 + 处理队列项: INSERT - entry_id=145957 - queue_id=303 - 重试次数=0
2025-07-15 16:34:58,692 - INFO - 🔄 执行INSERT操作: entry_id=145957, employee_id=215829
2025-07-15 16:34:58,692 - INFO - 📤 发送到Server6的数据: {'従業員ｺｰﾄﾞ': '215829', '日付': '2025-07-15', '機種': None, '号機': None, '工場製番': None, '工事番号': None, 'ﾕﾆｯﾄ番号': None, '区分': '1', '項目': '1', '時間': 1.0, '所属ｺｰﾄﾞ': '131'}
2025-07-15 16:34:59,184 - INFO - 📋 2025/07/03 + 16:10 + 处理队列项: INSERT - entry_id=145958 - queue_id=304 - 重试次数=0
2025-07-15 16:34:59,199 - INFO - 🔄 执行INSERT操作: entry_id=145958, employee_id=215829
2025-07-15 16:34:59,200 - INFO - 📤 发送到Server6的数据: {'従業員ｺｰﾄﾞ': '215829', '日付': '2025-07-15', '機種': None, '号機': None, '工場製番': None, '工事番号': None, 'ﾕﾆｯﾄ番号': None, '区分': '1', '項目': '1', '時間': 1.0, '所属ｺｰﾄﾞ': '131'}
2025-07-15 16:35:01,083 - INFO - ✅ INSERT完成: entry_id=145955 -> external_id=603722
2025-07-15 16:35:03,093 - INFO - 📋 2025/07/03 + 16:10 + 处理队列项: INSERT - entry_id=145959 - queue_id=305 - 重试次数=0
2025-07-15 16:35:03,113 - INFO - 🔄 执行INSERT操作: entry_id=145959, employee_id=215829
2025-07-15 16:35:03,113 - INFO - 📤 发送到Server6的数据: {'従業員ｺｰﾄﾞ': '215829', '日付': '2025-07-15', '機種': None, '号機': None, '工場製番': None, '工事番号': None, 'ﾕﾆｯﾄ番号': None, '区分': '1', '項目': '1', '時間': 1.0, '所属ｺｰﾄﾞ': '131'}
2025-07-15 16:35:05,055 - INFO - ✅ INSERT完成: entry_id=145956 -> external_id=603723
2025-07-15 16:35:05,061 - INFO - 📋 2025/07/03 + 16:10 + 处理队列项: INSERT - entry_id=145960 - queue_id=306 - 重试次数=0
2025-07-15 16:35:05,087 - INFO - 🔄 执行INSERT操作: entry_id=145960, employee_id=215829
2025-07-15 16:35:05,087 - INFO - 📤 发送到Server6的数据: {'従業員ｺｰﾄﾞ': '215829', '日付': '2025-07-15', '機種': None, '号機': None, '工場製番': None, '工事番号': None, 'ﾕﾆｯﾄ番号': None, '区分': '1', '項目': '1', '時間': 1.0, '所属ｺｰﾄﾞ': '131'}
2025-07-15 16:35:08,929 - INFO - ✅ INSERT完成: entry_id=145957 -> external_id=603724
2025-07-15 16:35:08,934 - INFO - 📋 2025/07/03 + 16:10 + 处理队列项: INSERT - entry_id=145961 - queue_id=307 - 重试次数=0
2025-07-15 16:35:08,960 - INFO - 🔄 执行INSERT操作: entry_id=145961, employee_id=215829
2025-07-15 16:35:08,960 - INFO - 📤 发送到Server6的数据: {'従業員ｺｰﾄﾞ': '215829', '日付': '2025-07-15', '機種': None, '号機': None, '工場製番': None, '工事番号': None, 'ﾕﾆｯﾄ番号': None, '区分': '1', '項目': '1', '時間': 1.0, '所属ｺｰﾄﾞ': '131'}
2025-07-15 16:35:12,820 - INFO - ✅ INSERT完成: entry_id=145958 -> external_id=603725
2025-07-15 16:35:12,825 - INFO - 📋 2025/07/03 + 16:10 + 处理队列项: INSERT - entry_id=145962 - queue_id=308 - 重试次数=0
2025-07-15 16:35:12,840 - INFO - 🔄 执行INSERT操作: entry_id=145962, employee_id=215829
2025-07-15 16:35:12,840 - INFO - 📤 发送到Server6的数据: {'従業員ｺｰﾄﾞ': '215829', '日付': '2025-07-15', '機種': None, '号機': None, '工場製番': None, '工事番号': None, 'ﾕﾆｯﾄ番号': None, '区分': '1', '項目': '1', '時間': 1.0, '所属ｺｰﾄﾞ': '131'}
2025-07-15 16:35:17,080 - INFO - ✅ INSERT完成: entry_id=145959 -> external_id=603726
2025-07-15 16:35:17,086 - INFO - 📋 2025/07/03 + 16:10 + 处理队列项: INSERT - entry_id=145963 - queue_id=309 - 重试次数=0
2025-07-15 16:35:17,113 - INFO - 🔄 执行INSERT操作: entry_id=145963, employee_id=215829
2025-07-15 16:35:17,113 - INFO - 📤 发送到Server6的数据: {'従業員ｺｰﾄﾞ': '215829', '日付': '2025-07-15', '機種': None, '号機': None, '工場製番': None, '工事番号': None, 'ﾕﾆｯﾄ番号': None, '区分': '1', '項目': '1', '時間': 1.0, '所属ｺｰﾄﾞ': '131'}
2025-07-15 16:35:20,868 - INFO - ✅ INSERT完成: entry_id=145960 -> external_id=603727
2025-07-15 16:35:20,874 - INFO - 📋 2025/07/03 + 16:10 + 处理队列项: INSERT - entry_id=145964 - queue_id=310 - 重试次数=0
2025-07-15 16:35:20,903 - INFO - 🔄 执行INSERT操作: entry_id=145964, employee_id=215829
2025-07-15 16:35:20,903 - INFO - 📤 发送到Server6的数据: {'従業員ｺｰﾄﾞ': '215829', '日付': '2025-07-15', '機種': None, '号機': None, '工場製番': None, '工事番号': None, 'ﾕﾆｯﾄ番号': None, '区分': '1', '項目': '1', '時間': 1.0, '所属ｺｰﾄﾞ': '131'}
2025-07-15 16:35:24,881 - INFO - ✅ INSERT完成: entry_id=145961 -> external_id=603728
2025-07-15 16:35:28,995 - INFO - ✅ INSERT完成: entry_id=145962 -> external_id=603729
2025-07-15 16:35:33,074 - INFO - ✅ INSERT完成: entry_id=145963 -> external_id=603730
2025-07-15 16:35:37,089 - INFO - ✅ INSERT完成: entry_id=145964 -> external_id=603731
2025-07-15 16:37:39,217 - INFO - 📋 2025/07/03 + 16:10 + 处理队列项: UPDATE - entry_id=145954 - queue_id=311 - 重试次数=0
2025-07-15 16:37:39,233 - WARNING - ⚠️ UPDATE操作跳过: entry_id=145954 (记录已被删除)
2025-07-15 16:37:39,235 - INFO - ✅ UPDATE跳过完成: entry_id=145954 (记录已被删除)
2025-07-15 16:37:39,240 - INFO - 📋 2025/07/03 + 16:10 + 处理队列项: DELETE - entry_id=145954 - queue_id=312 - 重试次数=0
2025-07-15 16:37:39,241 - INFO - 🔄 执行DELETE操作: entry_id=145954, external_id=603721
2025-07-15 16:37:43,260 - INFO - ✅ DELETE完成: entry_id=145954, external_id=603721
2025-07-15 16:37:43,264 - INFO - ℹ️ DELETE操作跳过f6同步: entry_id=145954 (记录已删除)
2025-07-15 16:38:23,401 - INFO - 📋 2025/07/03 + 16:10 + 处理队列项: UPDATE - entry_id=145955 - queue_id=313 - 重试次数=0
2025-07-15 16:38:23,425 - INFO - 🔄 执行UPDATE操作: entry_id=145955 -> external_id=603722
2025-07-15 16:38:23,425 - INFO - 📦 更新数据: {'employee_id': '215829', 'entry_date': '2025/07/15', 'model': None, 'number': None, 'factory_number': None, 'project_number': None, 'unit_number': None, 'category': '1', 'item': '1', 'duration': 1.0, 'department': '131'}
2025-07-15 16:38:23,425 - INFO - 📤 发送到Server6的更新数据: {'従業員ｺｰﾄﾞ': '215829', '日付': '2025-07-15', '機種': None, '号機': None, '工場製番': None, '工事番号': None, 'ﾕﾆｯﾄ番号': None, '区分': '1', '項目': '1', '時間': 1.0, '所属ｺｰﾄﾞ': '131'}
2025-07-15 16:38:25,374 - INFO - 📋 2025/07/03 + 16:10 + 处理队列项: DELETE - entry_id=145955 - queue_id=314 - 重试次数=0
2025-07-15 16:38:25,375 - INFO - 🔄 执行DELETE操作: entry_id=145955, external_id=603722
2025-07-15 16:38:27,320 - INFO - ✅ UPDATE完成: entry_id=145955 -> external_id=603722, source已更新为'system'
2025-07-15 16:38:29,336 - INFO - 📋 2025/07/03 + 16:10 + 处理队列项: UPDATE - entry_id=145956 - queue_id=315 - 重试次数=0
2025-07-15 16:38:29,353 - WARNING - ⚠️ UPDATE操作跳过: entry_id=145956 (记录已被删除)
2025-07-15 16:38:29,354 - INFO - ✅ UPDATE跳过完成: entry_id=145956 (记录已被删除)
2025-07-15 16:38:29,359 - INFO - 📋 2025/07/03 + 16:10 + 处理队列项: DELETE - entry_id=145956 - queue_id=316 - 重试次数=0
2025-07-15 16:38:29,360 - INFO - 🔄 执行DELETE操作: entry_id=145956, external_id=603723
2025-07-15 16:38:31,167 - INFO - ✅ DELETE完成: entry_id=145955, external_id=603722
2025-07-15 16:38:31,169 - INFO - ℹ️ DELETE操作跳过f6同步: entry_id=145955 (记录已删除)
2025-07-15 16:38:33,177 - INFO - 📋 2025/07/03 + 16:10 + 处理队列项: UPDATE - entry_id=145957 - queue_id=317 - 重试次数=0
2025-07-15 16:38:33,201 - WARNING - ⚠️ UPDATE操作跳过: entry_id=145957 (记录已被删除)
2025-07-15 16:38:33,202 - INFO - ✅ UPDATE跳过完成: entry_id=145957 (记录已被删除)
2025-07-15 16:38:33,207 - INFO - 📋 2025/07/03 + 16:10 + 处理队列项: DELETE - entry_id=145957 - queue_id=318 - 重试次数=0
2025-07-15 16:38:33,208 - INFO - 🔄 执行DELETE操作: entry_id=145957, external_id=603724
2025-07-15 16:38:35,160 - INFO - ✅ DELETE完成: entry_id=145956, external_id=603723
2025-07-15 16:38:35,163 - INFO - ℹ️ DELETE操作跳过f6同步: entry_id=145956 (记录已删除)
2025-07-15 16:38:37,172 - INFO - 📋 2025/07/03 + 16:10 + 处理队列项: UPDATE - entry_id=145958 - queue_id=319 - 重试次数=0
2025-07-15 16:38:37,195 - WARNING - ⚠️ UPDATE操作跳过: entry_id=145958 (记录已被删除)
2025-07-15 16:38:37,196 - INFO - ✅ UPDATE跳过完成: entry_id=145958 (记录已被删除)
2025-07-15 16:38:37,201 - INFO - 📋 2025/07/03 + 16:10 + 处理队列项: DELETE - entry_id=145958 - queue_id=320 - 重试次数=0
2025-07-15 16:38:37,202 - INFO - 🔄 执行DELETE操作: entry_id=145958, external_id=603725
2025-07-15 16:38:38,900 - INFO - ✅ DELETE完成: entry_id=145957, external_id=603724
2025-07-15 16:38:38,902 - INFO - ℹ️ DELETE操作跳过f6同步: entry_id=145957 (记录已删除)
2025-07-15 16:38:40,909 - INFO - 📋 2025/07/03 + 16:10 + 处理队列项: UPDATE - entry_id=145959 - queue_id=321 - 重试次数=0
2025-07-15 16:38:40,926 - WARNING - ⚠️ UPDATE操作跳过: entry_id=145959 (记录已被删除)
2025-07-15 16:38:40,927 - INFO - ✅ UPDATE跳过完成: entry_id=145959 (记录已被删除)
2025-07-15 16:38:40,932 - INFO - 📋 2025/07/03 + 16:10 + 处理队列项: DELETE - entry_id=145959 - queue_id=322 - 重试次数=0
2025-07-15 16:38:40,933 - INFO - 🔄 执行DELETE操作: entry_id=145959, external_id=603726
2025-07-15 16:38:42,748 - INFO - ✅ DELETE完成: entry_id=145958, external_id=603725
2025-07-15 16:38:42,752 - INFO - ℹ️ DELETE操作跳过f6同步: entry_id=145958 (记录已删除)
2025-07-15 16:38:44,763 - INFO - 📋 2025/07/03 + 16:10 + 处理队列项: UPDATE - entry_id=145960 - queue_id=323 - 重试次数=0
2025-07-15 16:38:44,775 - WARNING - ⚠️ UPDATE操作跳过: entry_id=145960 (记录已被删除)
2025-07-15 16:38:44,776 - INFO - ✅ UPDATE跳过完成: entry_id=145960 (记录已被删除)
2025-07-15 16:38:44,781 - INFO - 📋 2025/07/03 + 16:10 + 处理队列项: DELETE - entry_id=145960 - queue_id=324 - 重试次数=0
2025-07-15 16:38:44,782 - INFO - 🔄 执行DELETE操作: entry_id=145960, external_id=603727
2025-07-15 16:38:46,714 - INFO - ✅ DELETE完成: entry_id=145959, external_id=603726
2025-07-15 16:38:46,716 - INFO - ℹ️ DELETE操作跳过f6同步: entry_id=145959 (记录已删除)
2025-07-15 16:38:47,549 - INFO - 📋 2025/07/03 + 16:10 + 处理队列项: UPDATE - entry_id=145961 - queue_id=325 - 重试次数=0
2025-07-15 16:38:47,582 - INFO - 🔄 执行UPDATE操作: entry_id=145961 -> external_id=603728
2025-07-15 16:38:47,582 - INFO - 📦 更新数据: {'employee_id': '215829', 'entry_date': '2025/07/15', 'model': None, 'number': None, 'factory_number': None, 'project_number': None, 'unit_number': None, 'category': '1', 'item': '1', 'duration': 1.0, 'department': '131'}
2025-07-15 16:38:47,582 - INFO - 📤 发送到Server6的更新数据: {'従業員ｺｰﾄﾞ': '215829', '日付': '2025-07-15', '機種': None, '号機': None, '工場製番': None, '工事番号': None, 'ﾕﾆｯﾄ番号': None, '区分': '1', '項目': '1', '時間': 1.0, '所属ｺｰﾄﾞ': '131'}
2025-07-15 16:38:48,728 - INFO - 📋 2025/07/03 + 16:10 + 处理队列项: DELETE - entry_id=145961 - queue_id=326 - 重试次数=0
2025-07-15 16:38:48,730 - INFO - 🔄 执行DELETE操作: entry_id=145961, external_id=603728
2025-07-15 16:38:51,557 - INFO - 📋 2025/07/03 + 16:10 + 处理队列项: UPDATE - entry_id=145962 - queue_id=327 - 重试次数=0
2025-07-15 16:38:51,574 - WARNING - ⚠️ UPDATE操作跳过: entry_id=145962 (记录已被删除)
2025-07-15 16:38:51,575 - INFO - ✅ UPDATE跳过完成: entry_id=145962 (记录已被删除)
2025-07-15 16:38:51,580 - INFO - 📋 2025/07/03 + 16:10 + 处理队列项: DELETE - entry_id=145962 - queue_id=328 - 重试次数=0
2025-07-15 16:38:51,581 - INFO - 🔄 执行DELETE操作: entry_id=145962, external_id=603729
2025-07-15 16:38:52,487 - INFO - ✅ DELETE完成: entry_id=145960, external_id=603727
2025-07-15 16:38:52,490 - INFO - ℹ️ DELETE操作跳过f6同步: entry_id=145960 (记录已删除)
2025-07-15 16:38:56,080 - INFO - ✅ UPDATE完成: entry_id=145961 -> external_id=603728, source已更新为'system'
2025-07-15 16:38:56,501 - INFO - 📋 2025/07/03 + 16:10 + 处理队列项: UPDATE - entry_id=145963 - queue_id=329 - 重试次数=0
2025-07-15 16:38:56,527 - INFO - 🔄 执行UPDATE操作: entry_id=145963 -> external_id=603730
2025-07-15 16:38:56,527 - INFO - 📦 更新数据: {'employee_id': '215829', 'entry_date': '2025/07/15', 'model': None, 'number': None, 'factory_number': None, 'project_number': None, 'unit_number': None, 'category': '1', 'item': '1', 'duration': 1.0, 'department': '131'}
2025-07-15 16:38:56,527 - INFO - 📤 发送到Server6的更新数据: {'従業員ｺｰﾄﾞ': '215829', '日付': '2025-07-15', '機種': None, '号機': None, '工場製番': None, '工事番号': None, 'ﾕﾆｯﾄ番号': None, '区分': '1', '項目': '1', '時間': 1.0, '所属ｺｰﾄﾞ': '131'}
2025-07-15 16:38:58,095 - INFO - 📋 2025/07/03 + 16:10 + 处理队列项: DELETE - entry_id=145963 - queue_id=330 - 重试次数=0
2025-07-15 16:38:58,096 - INFO - 🔄 执行DELETE操作: entry_id=145963, external_id=603730
2025-07-15 16:39:00,013 - INFO - ✅ DELETE完成: entry_id=145961, external_id=603728
2025-07-15 16:39:00,015 - INFO - ℹ️ DELETE操作跳过f6同步: entry_id=145961 (记录已删除)
2025-07-15 16:39:02,025 - INFO - 📋 2025/07/03 + 16:10 + 处理队列项: UPDATE - entry_id=145964 - queue_id=331 - 重试次数=0
2025-07-15 16:39:02,059 - WARNING - ⚠️ UPDATE操作跳过: entry_id=145964 (记录已被删除)
2025-07-15 16:39:02,060 - INFO - ✅ UPDATE跳过完成: entry_id=145964 (记录已被删除)
2025-07-15 16:39:02,072 - INFO - 📋 2025/07/03 + 16:10 + 处理队列项: DELETE - entry_id=145964 - queue_id=332 - 重试次数=0
2025-07-15 16:39:02,073 - INFO - 🔄 执行DELETE操作: entry_id=145964, external_id=603731
2025-07-15 16:39:03,803 - INFO - ✅ DELETE完成: entry_id=145962, external_id=603729
2025-07-15 16:39:03,806 - INFO - ℹ️ DELETE操作跳过f6同步: entry_id=145962 (记录已删除)
2025-07-15 16:39:07,727 - INFO - ✅ UPDATE完成: entry_id=145963 -> external_id=603730, source已更新为'system'
2025-07-15 16:39:15,817 - INFO - ✅ DELETE完成: entry_id=145964, external_id=603731
2025-07-15 16:39:15,817 - INFO - ✅ DELETE完成: entry_id=145963, external_id=603730
2025-07-15 16:39:15,819 - INFO - ℹ️ DELETE操作跳过f6同步: entry_id=145964 (记录已删除)
2025-07-15 16:39:15,822 - INFO - ℹ️ DELETE操作跳过f6同步: entry_id=145963 (记录已删除)
2025-07-15 16:40:25,888 - INFO - 📋 2025/07/03 + 16:10 + 处理队列项: UPDATE - entry_id=145952 - queue_id=333 - 重试次数=0
2025-07-15 16:40:25,906 - WARNING - ⚠️ UPDATE操作跳过: entry_id=145952 (记录已被删除)
2025-07-15 16:40:25,907 - INFO - ✅ UPDATE跳过完成: entry_id=145952 (记录已被删除)
2025-07-15 16:40:25,913 - INFO - 📋 2025/07/03 + 16:10 + 处理队列项: DELETE - entry_id=145952 - queue_id=334 - 重试次数=0
2025-07-15 16:40:25,914 - INFO - 🔄 执行DELETE操作: entry_id=145952, external_id=603719
2025-07-15 16:40:29,950 - INFO - ✅ DELETE完成: entry_id=145952, external_id=603719
2025-07-15 16:40:29,952 - INFO - ℹ️ DELETE操作跳过f6同步: entry_id=145952 (记录已删除)
2025-07-15 16:43:34,392 - INFO - 📋 2025/07/03 + 16:10 + 处理队列项: INSERT - entry_id=145965 - queue_id=335 - 重试次数=0
2025-07-15 16:43:34,415 - INFO - 🔄 执行INSERT操作: entry_id=145965, employee_id=215829
2025-07-15 16:43:34,415 - INFO - 📤 发送到Server6的数据: {'従業員ｺｰﾄﾞ': '215829', '日付': '2025-07-15', '機種': None, '号機': None, '工場製番': 'HA0484', '工事番号': None, 'ﾕﾆｯﾄ番号': None, '区分': '0', '項目': '7', '時間': 8.0, '所属ｺｰﾄﾞ': '131'}
2025-07-15 16:43:38,748 - INFO - ✅ INSERT完成: entry_id=145965 -> external_id=603732
2025-07-15 16:48:58,579 - INFO - 监听任务被取消
2025-07-15 16:48:58,581 - INFO - 🔌 MongoDB连接已关闭
2025-07-15 16:48:58,583 - INFO - 🔌 MongoDB连接已关闭
2025-07-15 16:48:58,584 - INFO - 🔌 Redis连接已关闭
2025-07-15 16:48:58,585 - INFO - 🔌 PostgreSQL连接已关闭
2025-07-15 16:48:58,585 - INFO - 🔌 Redis连接已关闭
2025-07-15 16:48:58,585 - INFO - 🔌 PostgreSQL连接已关闭
2025-07-15 16:48:58,585 - INFO - 🔌 PostgreSQL连接已关闭
2025-07-15 16:48:58,585 - INFO - 🔌 f1监听器服务已停止
2025-07-15 16:48:58,585 - INFO - 🔌 f4操作处理服务已停止
2025-07-15 16:48:58,585 - INFO - 🔌 Redis连接已关闭
2025-07-15 16:48:58,586 - INFO - 🔌 f3数据拉取服务已停止
2025-07-15 16:48:58,691 - INFO - 🔌 推送工作线程停止: worker_2
2025-07-15 16:48:58,736 - INFO - 🔌 推送工作线程停止: worker_3
2025-07-15 16:48:58,778 - INFO - 🔌 推送工作线程停止: worker_1
2025-07-15 16:48:58,779 - INFO - 🔌 推送工作线程停止: worker_4
2025-07-15 16:48:58,780 - INFO - 🔌 MongoDB连接已关闭
2025-07-15 16:48:58,781 - INFO - AIOHTTP session for Server6 closed.
2025-07-15 16:48:58,781 - INFO - 🔌 Redis连接已关闭
2025-07-15 16:48:58,787 - INFO - 🔌 PostgreSQL连接已关闭
2025-07-15 16:48:58,787 - INFO - 🔌 f2推送回写服务已停止
2025-07-15 16:48:58,787 - ERROR - Server5微服务启动失败: asyncio.run() cannot be called from a running event loop
2025-07-15 16:48:59,205 - INFO - 🔌 PostgreSQL连接已关闭
2025-07-15 16:48:59,205 - INFO - 🔌 MongoDB连接已关闭
2025-07-15 16:48:59,205 - INFO - 🔌 PostgreSQL连接已关闭
2025-07-15 16:48:59,205 - INFO - 🔌 MongoDB连接已关闭
2025-07-15 16:48:59,205 - INFO - 🔌 PostgreSQL连接已关闭
2025-07-15 16:48:59,205 - INFO - 🔌 MongoDB连接已关闭
2025-07-15 16:48:59,205 - INFO - 🔌 Redis连接已关闭
2025-07-15 16:48:59,205 - INFO - 🔌 Redis连接已关闭
2025-07-15 16:48:59,205 - INFO - 🔌 Redis连接已关闭
2025-07-15 16:48:59,206 - INFO - 🔌 f1监听器服务已停止
2025-07-15 16:48:59,206 - INFO - 🔌 f2推送回写服务已停止
2025-07-15 16:48:59,206 - INFO - 🔌 f4操作处理服务已停止
2025-07-15 16:48:59,233 - ERROR - Task was destroyed but it is pending!
task: <Task pending name='Task-3202' coro=<Connection._cancel() running at /home/<USER>/miniconda3/envs/my_suite_unified/lib/python3.12/site-packages/asyncpg/connection.py:1646> wait_for=<Future pending cb=[Task.task_wakeup()]>>
2025-07-15 16:49:05,300 - INFO - 🔊 f1监听器服务初始化完成
2025-07-15 16:49:05,300 - INFO - 📡 Server6客户端初始化 (新版): http://************:8019
2025-07-15 16:49:05,300 - INFO - 📤 f2推送回写引擎初始化完成 (修复版本: 直接轮询PostgreSQL)
2025-07-15 16:49:05,300 - INFO - 📡 Server6客户端初始化 (新版): http://************:8019
2025-07-15 16:49:05,301 - INFO - 📥 f3数据拉取器初始化完成 (Staging & UPSERT 模式)
2025-07-15 16:49:05,301 - INFO - 🎮 f4操作处理器初始化完成
2025-07-15 16:49:05,317 - INFO - Redis连接成功
2025-07-15 16:49:05,318 - INFO - Redis连接成功
2025-07-15 16:49:05,318 - INFO - Redis连接成功
2025-07-15 16:49:05,322 - INFO - ✅ MongoDB连接成功
2025-07-15 16:49:05,324 - INFO - ✅ MongoDB连接成功
2025-07-15 16:49:05,325 - INFO - ✅ MongoDB连接成功
2025-07-15 16:49:05,552 - INFO - ✅ PostgreSQL连接池创建成功
2025-07-15 16:49:05,553 - INFO - Redis连接成功
2025-07-15 16:49:05,553 - INFO - ✅ f3数据拉取服务启动成功，预定执行时间: ['02:00', '03:00']
2025-07-15 16:49:05,553 - INFO - 🚀 数据拉取调度器启动，预定运行时间: ['02:00', '03:00']
2025-07-15 16:49:05,553 - INFO - 下一次f3/f5同步任务将在 2025-07-16 02:00:00 执行，等待 33054 秒...
2025-07-15 16:49:05,635 - INFO - ✅ PostgreSQL连接池创建成功
2025-07-15 16:49:05,635 - INFO - ✅ f1监听器服务启动成功
2025-07-15 16:49:05,659 - INFO - ✅ 分区创建成功: entries_2025_07
2025-07-15 16:49:05,662 - INFO - ✅ PostgreSQL连接池创建成功
2025-07-15 16:49:05,663 - INFO - ✅ f2推送回写服务启动成功，工作线程数: 4
2025-07-15 16:49:05,663 - INFO - 🚀 推送工作线程启动: worker_1
2025-07-15 16:49:05,663 - INFO - 🚀 推送工作线程启动: worker_2
2025-07-15 16:49:05,663 - INFO - 🚀 推送工作线程启动: worker_3
2025-07-15 16:49:05,663 - INFO - 🚀 推送工作线程启动: worker_4
2025-07-15 16:49:05,664 - INFO - ✅ 分区创建成功: entries_2025_08
2025-07-15 16:49:05,693 - INFO - ✅ PostgreSQL连接池创建成功
2025-07-15 16:49:05,694 - INFO - ✅ f4操作处理服务启动成功
2025-07-15 16:49:05,767 - INFO - 📡 开始监听频道: push_job
2025-07-15 16:49:05,769 - INFO - 📡 开始监听频道: partition_check
2025-07-15 16:49:05,770 - INFO - 📡 开始监听频道: sync_trigger
2025-07-15 16:49:39,750 - INFO - 📋 2025/07/03 + 16:10 + 处理队列项: UPDATE - entry_id=145965 - queue_id=336 - 重试次数=0
2025-07-15 16:49:39,842 - INFO - 🔄 执行UPDATE操作: entry_id=145965 -> external_id=603732
2025-07-15 16:49:39,842 - INFO - 📦 更新数据: {'employee_id': '215829', 'entry_date': '2025/07/15', 'model': None, 'number': None, 'factory_number': 'HA0484', 'project_number': None, 'unit_number': None, 'category': '0', 'item': '7', 'duration': 8.0, 'department': '131'}
2025-07-15 16:49:39,842 - INFO - 📤 发送到Server6的更新数据: {'従業員ｺｰﾄﾞ': '215829', '日付': '2025-07-15', '機種': None, '号機': None, '工場製番': 'HA0484', '工事番号': None, 'ﾕﾆｯﾄ番号': None, '区分': '0', '項目': '7', '時間': 8.0, '所属ｺｰﾄﾞ': '131'}
2025-07-15 16:49:39,842 - INFO - AIOHTTP session for Server6 created.
2025-07-15 16:49:44,208 - INFO - ✅ UPDATE完成: entry_id=145965 -> external_id=603732, source已更新为'system'
2025-07-15 16:51:10,124 - INFO - 📋 2025/07/03 + 16:10 + 处理队列项: UPDATE - entry_id=145965 - queue_id=337 - 重试次数=0
2025-07-15 16:51:10,239 - INFO - 🔄 执行UPDATE操作: entry_id=145965 -> external_id=603732
2025-07-15 16:51:10,239 - INFO - 📦 更新数据: {'employee_id': '215829', 'entry_date': '2025/07/15', 'model': None, 'number': None, 'factory_number': 'HA0484', 'project_number': None, 'unit_number': None, 'category': '0', 'item': '7', 'duration': 8.0, 'department': '131'}
2025-07-15 16:51:10,239 - INFO - 📤 发送到Server6的更新数据: {'従業員ｺｰﾄﾞ': '215829', '日付': '2025-07-15', '機種': None, '号機': None, '工場製番': 'HA0484', '工事番号': None, 'ﾕﾆｯﾄ番号': None, '区分': '0', '項目': '7', '時間': 8.0, '所属ｺｰﾄﾞ': '131'}
2025-07-15 16:51:14,170 - INFO - ✅ UPDATE完成: entry_id=145965 -> external_id=603732, source已更新为'system'
2025-07-15 16:52:08,060 - INFO - 📋 2025/07/03 + 16:10 + 处理队列项: UPDATE - entry_id=145965 - queue_id=338 - 重试次数=0
2025-07-15 16:52:08,177 - INFO - 🔄 执行UPDATE操作: entry_id=145965 -> external_id=603732
2025-07-15 16:52:08,177 - INFO - 📦 更新数据: {'employee_id': '215829', 'entry_date': '2025/07/15', 'model': None, 'number': None, 'factory_number': 'HA0484', 'project_number': None, 'unit_number': None, 'category': '0', 'item': '7', 'duration': 8.0, 'department': '131'}
2025-07-15 16:52:08,177 - INFO - 📤 发送到Server6的更新数据: {'従業員ｺｰﾄﾞ': '215829', '日付': '2025-07-15', '機種': None, '号機': None, '工場製番': 'HA0484', '工事番号': None, 'ﾕﾆｯﾄ番号': None, '区分': '0', '項目': '7', '時間': 8.0, '所属ｺｰﾄﾞ': '131'}
2025-07-15 16:52:12,151 - INFO - ✅ UPDATE完成: entry_id=145965 -> external_id=603732, source已更新为'system'
2025-07-15 16:52:46,231 - INFO - 📋 2025/07/03 + 16:10 + 处理队列项: INSERT - entry_id=145966 - queue_id=339 - 重试次数=0
2025-07-15 16:52:46,246 - INFO - 🔄 执行INSERT操作: entry_id=145966, employee_id=215829
2025-07-15 16:52:46,246 - INFO - 📤 发送到Server6的数据: {'従業員ｺｰﾄﾞ': '215829', '日付': '2025-07-11', '機種': None, '号機': None, '工場製番': 'HA0484', '工事番号': None, 'ﾕﾆｯﾄ番号': None, '区分': '0', '項目': '7', '時間': 8.0, '所属ｺｰﾄﾞ': '131'}
2025-07-15 16:52:50,409 - INFO - ✅ INSERT完成: entry_id=145966 -> external_id=603733
2025-07-15 16:56:04,833 - INFO - 📋 2025/07/03 + 16:10 + 处理队列项: UPDATE - entry_id=145966 - queue_id=340 - 重试次数=0
2025-07-15 16:56:04,968 - INFO - 🔄 执行UPDATE操作: entry_id=145966 -> external_id=603733
2025-07-15 16:56:04,969 - INFO - 📦 更新数据: {'employee_id': '215829', 'entry_date': '2025/07/11', 'model': None, 'number': None, 'factory_number': 'HA0484', 'project_number': None, 'unit_number': None, 'category': '0', 'item': '7', 'duration': 8.0, 'department': '131'}
2025-07-15 16:56:04,969 - INFO - 📤 发送到Server6的更新数据: {'従業員ｺｰﾄﾞ': '215829', '日付': '2025-07-11', '機種': None, '号機': None, '工場製番': 'HA0484', '工事番号': None, 'ﾕﾆｯﾄ番号': None, '区分': '0', '項目': '7', '時間': 8.0, '所属ｺｰﾄﾞ': '131'}
2025-07-15 16:56:09,368 - INFO - ✅ UPDATE完成: entry_id=145966 -> external_id=603733, source已更新为'system'
2025-07-15 16:59:53,739 - INFO - 监听任务被取消
2025-07-15 16:59:53,740 - INFO - 🔌 MongoDB连接已关闭
2025-07-15 16:59:53,742 - INFO - 🔌 MongoDB连接已关闭
2025-07-15 16:59:53,743 - INFO - 🔌 Redis连接已关闭
2025-07-15 16:59:53,743 - INFO - 🔌 PostgreSQL连接已关闭
2025-07-15 16:59:53,743 - INFO - 🔌 Redis连接已关闭
2025-07-15 16:59:53,743 - INFO - 🔌 PostgreSQL连接已关闭
2025-07-15 16:59:53,743 - INFO - 🔌 PostgreSQL连接已关闭
2025-07-15 16:59:53,743 - INFO - 🔌 f1监听器服务已停止
2025-07-15 16:59:53,743 - INFO - 🔌 f4操作处理服务已停止
2025-07-15 16:59:53,744 - INFO - 🔌 Redis连接已关闭
2025-07-15 16:59:53,744 - INFO - 🔌 f3数据拉取服务已停止
2025-07-15 16:59:53,873 - INFO - 🔌 推送工作线程停止: worker_2
2025-07-15 16:59:53,893 - INFO - 🔌 推送工作线程停止: worker_1
2025-07-15 16:59:53,953 - INFO - 🔌 推送工作线程停止: worker_3
2025-07-15 16:59:53,969 - INFO - 🔌 推送工作线程停止: worker_4
2025-07-15 16:59:53,970 - INFO - 🔌 MongoDB连接已关闭
2025-07-15 16:59:53,971 - INFO - AIOHTTP session for Server6 closed.
2025-07-15 16:59:53,971 - INFO - 🔌 Redis连接已关闭
2025-07-15 16:59:53,980 - INFO - 🔌 PostgreSQL连接已关闭
2025-07-15 16:59:53,980 - INFO - 🔌 f2推送回写服务已停止
2025-07-15 16:59:53,980 - ERROR - Server5微服务启动失败: asyncio.run() cannot be called from a running event loop
2025-07-15 16:59:54,414 - INFO - 🔌 PostgreSQL连接已关闭
2025-07-15 16:59:54,414 - INFO - 🔌 MongoDB连接已关闭
2025-07-15 16:59:54,414 - INFO - 🔌 PostgreSQL连接已关闭
2025-07-15 16:59:54,414 - INFO - 🔌 MongoDB连接已关闭
2025-07-15 16:59:54,414 - INFO - 🔌 PostgreSQL连接已关闭
2025-07-15 16:59:54,415 - INFO - 🔌 MongoDB连接已关闭
2025-07-15 16:59:54,415 - INFO - 🔌 Redis连接已关闭
2025-07-15 16:59:54,415 - INFO - 🔌 Redis连接已关闭
2025-07-15 16:59:54,415 - INFO - 🔌 Redis连接已关闭
2025-07-15 16:59:54,415 - INFO - 🔌 f1监听器服务已停止
2025-07-15 16:59:54,415 - INFO - 🔌 f2推送回写服务已停止
2025-07-15 16:59:54,415 - INFO - 🔌 f4操作处理服务已停止
2025-07-15 16:59:54,438 - ERROR - Task was destroyed but it is pending!
task: <Task pending name='Task-1380' coro=<Connection._cancel() running at /home/<USER>/miniconda3/envs/my_suite_unified/lib/python3.12/site-packages/asyncpg/connection.py:1646> wait_for=<Future pending cb=[Task.task_wakeup()]>>
2025-07-15 16:59:55,606 - INFO - 🔊 f1监听器服务初始化完成
2025-07-15 16:59:55,606 - INFO - 📡 Server6客户端初始化 (新版): http://************:8019
2025-07-15 16:59:55,607 - INFO - 📤 f2推送回写引擎初始化完成 (修复版本: 直接轮询PostgreSQL)
2025-07-15 16:59:55,607 - INFO - 📡 Server6客户端初始化 (新版): http://************:8019
2025-07-15 16:59:55,608 - INFO - 📥 f3数据拉取器初始化完成 (Staging & UPSERT 模式)
2025-07-15 16:59:55,608 - INFO - 🎮 f4操作处理器初始化完成
2025-07-15 16:59:55,617 - INFO - Redis连接成功
2025-07-15 16:59:55,617 - INFO - Redis连接成功
2025-07-15 16:59:55,617 - INFO - Redis连接成功
2025-07-15 16:59:55,624 - INFO - ✅ MongoDB连接成功
2025-07-15 16:59:55,624 - INFO - ✅ MongoDB连接成功
2025-07-15 16:59:55,626 - INFO - ✅ MongoDB连接成功
2025-07-15 16:59:55,842 - INFO - ✅ PostgreSQL连接池创建成功
2025-07-15 16:59:55,843 - INFO - Redis连接成功
2025-07-15 16:59:55,843 - INFO - ✅ f3数据拉取服务启动成功，预定执行时间: ['02:00', '03:00']
2025-07-15 16:59:55,844 - INFO - 🚀 数据拉取调度器启动，预定运行时间: ['02:00', '03:00']
2025-07-15 16:59:55,844 - INFO - 下一次f3/f5同步任务将在 2025-07-16 02:00:00 执行，等待 32404 秒...
2025-07-15 16:59:55,895 - INFO - ✅ PostgreSQL连接池创建成功
2025-07-15 16:59:55,895 - INFO - ✅ f1监听器服务启动成功
2025-07-15 16:59:55,918 - INFO - ✅ 分区创建成功: entries_2025_07
2025-07-15 16:59:55,922 - INFO - ✅ 分区创建成功: entries_2025_08
2025-07-15 16:59:55,923 - INFO - ✅ PostgreSQL连接池创建成功
2025-07-15 16:59:55,923 - INFO - ✅ f2推送回写服务启动成功，工作线程数: 4
2025-07-15 16:59:55,923 - INFO - 🚀 推送工作线程启动: worker_1
2025-07-15 16:59:55,923 - INFO - 🚀 推送工作线程启动: worker_2
2025-07-15 16:59:55,923 - INFO - 🚀 推送工作线程启动: worker_3
2025-07-15 16:59:55,924 - INFO - 🚀 推送工作线程启动: worker_4
2025-07-15 16:59:55,948 - INFO - ✅ PostgreSQL连接池创建成功
2025-07-15 16:59:55,948 - INFO - ✅ f4操作处理服务启动成功
2025-07-15 16:59:56,017 - INFO - 📡 开始监听频道: push_job
2025-07-15 16:59:56,018 - INFO - 📡 开始监听频道: partition_check
2025-07-15 16:59:56,020 - INFO - 📡 开始监听频道: sync_trigger
2025-07-15 17:00:36,029 - INFO - 📋 2025/07/03 + 16:10 + 处理队列项: INSERT - entry_id=145967 - queue_id=341 - 重试次数=0
2025-07-15 17:00:36,152 - INFO - 🔄 执行INSERT操作: entry_id=145967, employee_id=215829
2025-07-15 17:00:36,152 - INFO - 📤 发送到Server6的数据: {'従業員ｺｰﾄﾞ': '215829', '日付': '2025-07-15', '機種': None, '号機': None, '工場製番': None, '工事番号': None, 'ﾕﾆｯﾄ番号': None, '区分': '0', '項目': '0', '時間': 1.0, '所属ｺｰﾄﾞ': '131'}
2025-07-15 17:00:36,152 - INFO - AIOHTTP session for Server6 created.
2025-07-15 17:00:40,065 - INFO - ✅ INSERT完成: entry_id=145967 -> external_id=603734
2025-07-15 17:07:54,021 - INFO - 监听任务被取消
2025-07-15 17:07:54,025 - INFO - 🔌 MongoDB连接已关闭
2025-07-15 17:07:54,028 - INFO - 🔌 MongoDB连接已关闭
2025-07-15 17:07:54,031 - INFO - 🔌 Redis连接已关闭
2025-07-15 17:07:54,032 - INFO - 🔌 PostgreSQL连接已关闭
2025-07-15 17:07:54,032 - INFO - 🔌 Redis连接已关闭
2025-07-15 17:07:54,032 - INFO - 🔌 推送工作线程停止: worker_2
2025-07-15 17:07:54,032 - INFO - 🔌 PostgreSQL连接已关闭
2025-07-15 17:07:54,032 - INFO - 🔌 PostgreSQL连接已关闭
2025-07-15 17:07:54,032 - INFO - 🔌 f1监听器服务已停止
2025-07-15 17:07:54,032 - INFO - 🔌 f4操作处理服务已停止
2025-07-15 17:07:54,032 - INFO - 🔌 Redis连接已关闭
2025-07-15 17:07:54,032 - INFO - 🔌 f3数据拉取服务已停止
2025-07-15 17:07:54,126 - INFO - 🔌 推送工作线程停止: worker_1
2025-07-15 17:07:54,150 - INFO - 🔌 推送工作线程停止: worker_3
2025-07-15 17:07:54,182 - INFO - 🔌 推送工作线程停止: worker_4
2025-07-15 17:07:54,183 - INFO - 🔌 MongoDB连接已关闭
2025-07-15 17:07:54,183 - INFO - AIOHTTP session for Server6 closed.
2025-07-15 17:07:54,183 - INFO - 🔌 Redis连接已关闭
2025-07-15 17:07:54,188 - INFO - 🔌 PostgreSQL连接已关闭
2025-07-15 17:07:54,188 - INFO - 🔌 f2推送回写服务已停止
2025-07-15 17:07:54,189 - ERROR - Server5微服务启动失败: asyncio.run() cannot be called from a running event loop
2025-07-15 17:07:54,664 - INFO - 🔌 PostgreSQL连接已关闭
2025-07-15 17:07:54,664 - INFO - 🔌 MongoDB连接已关闭
2025-07-15 17:07:54,664 - INFO - 🔌 PostgreSQL连接已关闭
2025-07-15 17:07:54,664 - INFO - 🔌 MongoDB连接已关闭
2025-07-15 17:07:54,664 - INFO - 🔌 PostgreSQL连接已关闭
2025-07-15 17:07:54,664 - INFO - 🔌 MongoDB连接已关闭
2025-07-15 17:07:54,664 - INFO - 🔌 Redis连接已关闭
2025-07-15 17:07:54,664 - INFO - 🔌 Redis连接已关闭
2025-07-15 17:07:54,664 - INFO - 🔌 Redis连接已关闭
2025-07-15 17:07:54,664 - INFO - 🔌 f1监听器服务已停止
2025-07-15 17:07:54,664 - INFO - 🔌 f2推送回写服务已停止
2025-07-15 17:07:54,664 - INFO - 🔌 f4操作处理服务已停止
2025-07-15 17:07:55,383 - INFO - 🔊 f1监听器服务初始化完成
2025-07-15 17:07:55,383 - INFO - 📡 Server6客户端初始化 (新版): http://************:8019
2025-07-15 17:07:55,383 - INFO - 📤 f2推送回写引擎初始化完成 (修复版本: 直接轮询PostgreSQL)
2025-07-15 17:07:55,383 - INFO - 📡 Server6客户端初始化 (新版): http://************:8019
2025-07-15 17:07:55,384 - INFO - 📥 f3数据拉取器初始化完成 (Staging & UPSERT 模式)
2025-07-15 17:07:55,384 - INFO - 🎮 f4操作处理器初始化完成
2025-07-15 17:07:55,395 - INFO - Redis连接成功
2025-07-15 17:07:55,395 - INFO - Redis连接成功
2025-07-15 17:07:55,395 - INFO - Redis连接成功
2025-07-15 17:07:55,400 - INFO - ✅ MongoDB连接成功
2025-07-15 17:07:55,401 - INFO - ✅ MongoDB连接成功
2025-07-15 17:07:55,403 - INFO - ✅ MongoDB连接成功
2025-07-15 17:07:55,640 - INFO - ✅ PostgreSQL连接池创建成功
2025-07-15 17:07:55,641 - INFO - Redis连接成功
2025-07-15 17:07:55,641 - INFO - ✅ f3数据拉取服务启动成功，预定执行时间: ['02:00', '03:00']
2025-07-15 17:07:55,641 - INFO - 🚀 数据拉取调度器启动，预定运行时间: ['02:00', '03:00']
2025-07-15 17:07:55,641 - INFO - 下一次f3/f5同步任务将在 2025-07-16 02:00:00 执行，等待 31924 秒...
2025-07-15 17:07:55,704 - INFO - ✅ PostgreSQL连接池创建成功
2025-07-15 17:07:55,705 - INFO - ✅ f1监听器服务启动成功
2025-07-15 17:07:55,714 - INFO - ✅ 分区创建成功: entries_2025_07
2025-07-15 17:07:55,718 - INFO - ✅ 分区创建成功: entries_2025_08
2025-07-15 17:07:55,757 - INFO - ✅ PostgreSQL连接池创建成功
2025-07-15 17:07:55,757 - INFO - ✅ f2推送回写服务启动成功，工作线程数: 4
2025-07-15 17:07:55,757 - INFO - 🚀 推送工作线程启动: worker_1
2025-07-15 17:07:55,757 - INFO - 🚀 推送工作线程启动: worker_2
2025-07-15 17:07:55,757 - INFO - 🚀 推送工作线程启动: worker_3
2025-07-15 17:07:55,758 - INFO - 🚀 推送工作线程启动: worker_4
2025-07-15 17:07:55,762 - INFO - ✅ PostgreSQL连接池创建成功
2025-07-15 17:07:55,762 - INFO - ✅ f4操作处理服务启动成功
2025-07-15 17:07:55,845 - INFO - 📡 开始监听频道: push_job
2025-07-15 17:07:55,846 - INFO - 📡 开始监听频道: partition_check
2025-07-15 17:07:55,848 - INFO - 📡 开始监听频道: sync_trigger
2025-07-15 17:08:23,821 - INFO - 📋 2025/07/03 + 16:10 + 处理队列项: UPDATE - entry_id=145967 - queue_id=342 - 重试次数=0
2025-07-15 17:08:23,822 - INFO - 📋 2025/07/03 + 16:10 + 处理队列项: DELETE - entry_id=145967 - queue_id=343 - 重试次数=0
2025-07-15 17:08:23,823 - INFO - 🔄 执行DELETE操作: entry_id=145967, external_id=603734
2025-07-15 17:08:23,823 - INFO - AIOHTTP session for Server6 created.
2025-07-15 17:08:23,927 - WARNING - ⚠️ UPDATE操作跳过: entry_id=145967 (记录已被删除)
2025-07-15 17:08:23,931 - INFO - ✅ UPDATE跳过完成: entry_id=145967 (记录已被删除)
2025-07-15 17:08:28,182 - INFO - ✅ DELETE完成: entry_id=145967, external_id=603734
2025-07-15 17:08:28,184 - INFO - ℹ️ DELETE操作跳过f6同步: entry_id=145967 (记录已删除)
2025-07-15 17:08:43,980 - INFO - 📋 2025/07/03 + 16:10 + 处理队列项: INSERT - entry_id=145968 - queue_id=344 - 重试次数=0
2025-07-15 17:08:43,999 - INFO - 🔄 执行INSERT操作: entry_id=145968, employee_id=215829
2025-07-15 17:08:43,999 - INFO - 🔍 调试 - 从entries表读取的原始数据: <Record entry_date=datetime.date(2025, 7, 15) id=145968 external_id=None ts=datetime.datetime(2025, 7, 15, 8, 8, 43, 294554, tzinfo=datetime.timezone.utc) employee_id='215829' model=None number=None factory_number=None project_number=None unit_number=None category=0 item=0 duration=Decimal('1.00') department='131' source='user'>
2025-07-15 17:08:44,000 - INFO - 🔍 调试 - category类型: <class 'int'>, 值: 0
2025-07-15 17:08:44,000 - INFO - 🔍 调试 - item类型: <class 'int'>, 值: 0
2025-07-15 17:08:44,000 - INFO - 🔍 调试 - 准备发送给Server6的数据: {'employee_id': '215829', 'entry_date': '2025/07/15', 'model': None, 'number': None, 'factory_number': None, 'project_number': None, 'unit_number': None, 'category': '0', 'item': '0', 'duration': 1.0, 'department': '131'}
2025-07-15 17:08:44,000 - INFO - 📤 发送到Server6的数据: {'従業員ｺｰﾄﾞ': '215829', '日付': '2025-07-15', '機種': None, '号機': None, '工場製番': None, '工事番号': None, 'ﾕﾆｯﾄ番号': None, '区分': '0', '項目': '0', '時間': 1.0, '所属ｺｰﾄﾞ': '131'}
2025-07-15 17:08:44,000 - INFO - 🔍 调试 - category最终值: 0, 类型: <class 'str'>
2025-07-15 17:08:44,000 - INFO - 🔍 调试 - item最终值: 0, 类型: <class 'str'>
2025-07-15 17:08:48,008 - INFO - ✅ INSERT完成: entry_id=145968 -> external_id=603735
2025-07-15 17:10:28,579 - INFO - 监听任务被取消
2025-07-15 17:10:28,580 - INFO - 🔌 MongoDB连接已关闭
2025-07-15 17:10:28,582 - INFO - 🔌 MongoDB连接已关闭
2025-07-15 17:10:28,582 - INFO - 🔌 Redis连接已关闭
2025-07-15 17:10:28,582 - INFO - 🔌 Redis连接已关闭
2025-07-15 17:10:28,588 - INFO - 🔌 PostgreSQL连接已关闭
2025-07-15 17:10:28,588 - INFO - 🔌 PostgreSQL连接已关闭
2025-07-15 17:10:28,588 - INFO - 🔌 f4操作处理服务已停止
2025-07-15 17:10:28,588 - INFO - 🔌 Redis连接已关闭
2025-07-15 17:10:28,588 - INFO - 🔌 f3数据拉取服务已停止
2025-07-15 17:10:28,588 - INFO - 🔌 PostgreSQL连接已关闭
2025-07-15 17:10:28,588 - INFO - 🔌 f1监听器服务已停止
2025-07-15 17:10:28,675 - INFO - 🔌 推送工作线程停止: worker_1
2025-07-15 17:10:28,691 - INFO - 🔌 推送工作线程停止: worker_2
2025-07-15 17:10:28,728 - INFO - 🔌 推送工作线程停止: worker_3
2025-07-15 17:10:28,759 - INFO - 🔌 推送工作线程停止: worker_4
2025-07-15 17:10:28,760 - INFO - 🔌 MongoDB连接已关闭
2025-07-15 17:10:28,760 - INFO - AIOHTTP session for Server6 closed.
2025-07-15 17:10:28,761 - INFO - 🔌 Redis连接已关闭
2025-07-15 17:10:28,765 - INFO - 🔌 PostgreSQL连接已关闭
2025-07-15 17:10:28,765 - INFO - 🔌 f2推送回写服务已停止
2025-07-15 17:10:28,766 - ERROR - Server5微服务启动失败: asyncio.run() cannot be called from a running event loop
2025-07-15 17:10:28,946 - INFO - 🔌 PostgreSQL连接已关闭
2025-07-15 17:10:28,946 - INFO - 🔌 MongoDB连接已关闭
2025-07-15 17:10:28,946 - INFO - 🔌 PostgreSQL连接已关闭
2025-07-15 17:10:28,946 - INFO - 🔌 MongoDB连接已关闭
2025-07-15 17:10:28,946 - INFO - 🔌 PostgreSQL连接已关闭
2025-07-15 17:10:28,946 - INFO - 🔌 MongoDB连接已关闭
2025-07-15 17:10:28,946 - INFO - 🔌 Redis连接已关闭
2025-07-15 17:10:28,946 - INFO - 🔌 Redis连接已关闭
2025-07-15 17:10:28,946 - INFO - 🔌 Redis连接已关闭
2025-07-15 17:10:28,947 - INFO - 🔌 f1监听器服务已停止
2025-07-15 17:10:28,947 - INFO - 🔌 f2推送回写服务已停止
2025-07-15 17:10:28,947 - INFO - 🔌 f4操作处理服务已停止
2025-07-15 17:10:28,966 - ERROR - Task was destroyed but it is pending!
task: <Task pending name='Task-389' coro=<Connection._cancel() running at /home/<USER>/miniconda3/envs/my_suite_unified/lib/python3.12/site-packages/asyncpg/connection.py:1646> wait_for=<Future pending cb=[Task.task_wakeup()]>>
2025-07-15 17:10:30,041 - INFO - 🔊 f1监听器服务初始化完成
2025-07-15 17:10:30,042 - INFO - 📡 Server6客户端初始化 (新版): http://************:8019
2025-07-15 17:10:30,042 - INFO - 📤 f2推送回写引擎初始化完成 (修复版本: 直接轮询PostgreSQL)
2025-07-15 17:10:30,042 - INFO - 📡 Server6客户端初始化 (新版): http://************:8019
2025-07-15 17:10:30,043 - INFO - 📥 f3数据拉取器初始化完成 (Staging & UPSERT 模式)
2025-07-15 17:10:30,043 - INFO - 🎮 f4操作处理器初始化完成
2025-07-15 17:10:30,053 - INFO - Redis连接成功
2025-07-15 17:10:30,053 - INFO - Redis连接成功
2025-07-15 17:10:30,053 - INFO - Redis连接成功
2025-07-15 17:10:30,058 - INFO - ✅ MongoDB连接成功
2025-07-15 17:10:30,058 - INFO - ✅ MongoDB连接成功
2025-07-15 17:10:30,059 - INFO - ✅ MongoDB连接成功
2025-07-15 17:10:30,367 - INFO - ✅ PostgreSQL连接池创建成功
2025-07-15 17:10:30,368 - INFO - Redis连接成功
2025-07-15 17:10:30,368 - INFO - ✅ f3数据拉取服务启动成功，预定执行时间: ['02:00', '03:00']
2025-07-15 17:10:30,368 - INFO - 🚀 数据拉取调度器启动，预定运行时间: ['02:00', '03:00']
2025-07-15 17:10:30,368 - INFO - 下一次f3/f5同步任务将在 2025-07-16 02:00:00 执行，等待 31770 秒...
2025-07-15 17:10:30,399 - INFO - ✅ PostgreSQL连接池创建成功
2025-07-15 17:10:30,399 - INFO - ✅ f1监听器服务启动成功
2025-07-15 17:10:30,408 - INFO - ✅ 分区创建成功: entries_2025_07
2025-07-15 17:10:30,429 - INFO - ✅ 分区创建成功: entries_2025_08
2025-07-15 17:10:30,433 - INFO - ✅ PostgreSQL连接池创建成功
2025-07-15 17:10:30,433 - INFO - ✅ f2推送回写服务启动成功，工作线程数: 4
2025-07-15 17:10:30,434 - INFO - 🚀 推送工作线程启动: worker_1
2025-07-15 17:10:30,434 - INFO - 🚀 推送工作线程启动: worker_2
2025-07-15 17:10:30,434 - INFO - 🚀 推送工作线程启动: worker_3
2025-07-15 17:10:30,435 - INFO - 🚀 推送工作线程启动: worker_4
2025-07-15 17:10:30,475 - INFO - ✅ PostgreSQL连接池创建成功
2025-07-15 17:10:30,475 - INFO - ✅ f4操作处理服务启动成功
2025-07-15 17:10:30,524 - INFO - 📡 开始监听频道: push_job
2025-07-15 17:10:30,525 - INFO - 📡 开始监听频道: partition_check
2025-07-15 17:10:30,526 - INFO - 📡 开始监听频道: sync_trigger
2025-07-15 17:10:50,491 - INFO - 📋 2025/07/03 + 16:10 + 处理队列项: UPDATE - entry_id=145968 - queue_id=345 - 重试次数=0
2025-07-15 17:10:50,492 - INFO - 📋 2025/07/03 + 16:10 + 处理队列项: DELETE - entry_id=145968 - queue_id=346 - 重试次数=0
2025-07-15 17:10:50,493 - INFO - 🔄 执行DELETE操作: entry_id=145968, external_id=603735
2025-07-15 17:10:50,493 - INFO - AIOHTTP session for Server6 created.
2025-07-15 17:10:50,611 - WARNING - ⚠️ UPDATE操作跳过: entry_id=145968 (记录已被删除)
2025-07-15 17:10:50,614 - INFO - ✅ UPDATE跳过完成: entry_id=145968 (记录已被删除)
2025-07-15 17:10:54,338 - INFO - ✅ DELETE完成: entry_id=145968, external_id=603735
2025-07-15 17:10:54,340 - INFO - ℹ️ DELETE操作跳过f6同步: entry_id=145968 (记录已删除)
2025-07-15 17:11:02,363 - INFO - 📋 2025/07/03 + 16:10 + 处理队列项: INSERT - entry_id=145969 - queue_id=347 - 重试次数=0
2025-07-15 17:11:02,455 - INFO - 🔄 执行INSERT操作: entry_id=145969, employee_id=215829
2025-07-15 17:11:02,456 - INFO - 🔍 调试 - 从entries表读取的原始数据: <Record entry_date=datetime.date(2025, 7, 15) id=145969 external_id=None ts=datetime.datetime(2025, 7, 15, 8, 11, 2, 207278, tzinfo=datetime.timezone.utc) employee_id='215829' model=None number=None factory_number=None project_number=None unit_number=None category=0 item=0 duration=Decimal('1.00') department='131' source='user'>
2025-07-15 17:11:02,456 - INFO - 🔍 调试 - category类型: <class 'int'>, 值: 0
2025-07-15 17:11:02,456 - INFO - 🔍 调试 - item类型: <class 'int'>, 值: 0
2025-07-15 17:11:02,457 - INFO - 🔍 调试 - 准备发送给Server6的数据: {'employee_id': '215829', 'entry_date': '2025/07/15', 'model': None, 'number': None, 'factory_number': None, 'project_number': None, 'unit_number': None, 'category': '0', 'item': '0', 'duration': 1.0, 'department': '131'}
2025-07-15 17:11:02,457 - INFO - 📤 发送到Server6的数据: {'従業員ｺｰﾄﾞ': '215829', '日付': '2025-07-15', '機種': None, '号機': None, '工場製番': None, '工事番号': None, 'ﾕﾆｯﾄ番号': None, 'category': '0', '区分': '0', 'item': '0', '項目': '0', '時間': 1.0, '所属ｺｰﾄﾞ': '131'}
2025-07-15 17:11:02,457 - INFO - 🔍 调试 - category最终值: 0, 类型: <class 'str'>
2025-07-15 17:11:02,458 - INFO - 🔍 调试 - item最终值: 0, 类型: <class 'str'>
2025-07-15 17:11:06,264 - INFO - ✅ INSERT完成: entry_id=145969 -> external_id=603736
2025-07-15 17:11:48,115 - INFO - 监听任务被取消
2025-07-15 17:11:48,119 - INFO - 🔌 MongoDB连接已关闭
2025-07-15 17:11:48,122 - INFO - 🔌 MongoDB连接已关闭
2025-07-15 17:11:48,127 - INFO - 🔌 Redis连接已关闭
2025-07-15 17:11:48,127 - INFO - 🔌 Redis连接已关闭
2025-07-15 17:11:48,127 - INFO - 🔌 推送工作线程停止: worker_1
2025-07-15 17:11:48,128 - INFO - 🔌 推送工作线程停止: worker_2
2025-07-15 17:11:48,129 - INFO - 🔌 PostgreSQL连接已关闭
2025-07-15 17:11:48,129 - INFO - 🔌 PostgreSQL连接已关闭
2025-07-15 17:11:48,129 - INFO - 🔌 Redis连接已关闭
2025-07-15 17:11:48,129 - INFO - 🔌 f3数据拉取服务已停止
2025-07-15 17:11:48,129 - INFO - 🔌 f1监听器服务已停止
2025-07-15 17:11:48,130 - INFO - 🔌 PostgreSQL连接已关闭
2025-07-15 17:11:48,130 - INFO - 🔌 f4操作处理服务已停止
2025-07-15 17:11:48,225 - INFO - 🔌 推送工作线程停止: worker_3
2025-07-15 17:11:48,241 - INFO - 🔌 推送工作线程停止: worker_4
2025-07-15 17:11:48,242 - INFO - 🔌 MongoDB连接已关闭
2025-07-15 17:11:48,242 - INFO - AIOHTTP session for Server6 closed.
2025-07-15 17:11:48,242 - INFO - 🔌 Redis连接已关闭
2025-07-15 17:11:48,246 - INFO - 🔌 PostgreSQL连接已关闭
2025-07-15 17:11:48,246 - INFO - 🔌 f2推送回写服务已停止
2025-07-15 17:11:48,247 - ERROR - Server5微服务启动失败: asyncio.run() cannot be called from a running event loop
2025-07-15 17:11:48,583 - INFO - 🔌 PostgreSQL连接已关闭
2025-07-15 17:11:48,583 - INFO - 🔌 MongoDB连接已关闭
2025-07-15 17:11:48,583 - INFO - 🔌 PostgreSQL连接已关闭
2025-07-15 17:11:48,583 - INFO - 🔌 MongoDB连接已关闭
2025-07-15 17:11:48,583 - INFO - 🔌 PostgreSQL连接已关闭
2025-07-15 17:11:48,583 - INFO - 🔌 MongoDB连接已关闭
2025-07-15 17:11:48,583 - INFO - 🔌 Redis连接已关闭
2025-07-15 17:11:48,583 - INFO - 🔌 Redis连接已关闭
2025-07-15 17:11:48,583 - INFO - 🔌 Redis连接已关闭
2025-07-15 17:11:48,583 - INFO - 🔌 f1监听器服务已停止
2025-07-15 17:11:48,584 - INFO - 🔌 f2推送回写服务已停止
2025-07-15 17:11:48,584 - INFO - 🔌 f4操作处理服务已停止
2025-07-15 17:11:50,762 - INFO - 🔊 f1监听器服务初始化完成
2025-07-15 17:11:50,762 - INFO - 📡 Server6客户端初始化 (新版): http://************:8019
2025-07-15 17:11:50,762 - INFO - 📤 f2推送回写引擎初始化完成 (修复版本: 直接轮询PostgreSQL)
2025-07-15 17:11:50,762 - INFO - 📡 Server6客户端初始化 (新版): http://************:8019
2025-07-15 17:11:50,763 - INFO - 📥 f3数据拉取器初始化完成 (Staging & UPSERT 模式)
2025-07-15 17:11:50,763 - INFO - 🎮 f4操作处理器初始化完成
2025-07-15 17:11:50,772 - INFO - Redis连接成功
2025-07-15 17:11:50,772 - INFO - Redis连接成功
2025-07-15 17:11:50,773 - INFO - Redis连接成功
2025-07-15 17:11:50,779 - INFO - ✅ MongoDB连接成功
2025-07-15 17:11:50,779 - INFO - ✅ MongoDB连接成功
2025-07-15 17:11:50,780 - INFO - ✅ MongoDB连接成功
2025-07-15 17:11:51,005 - INFO - ✅ PostgreSQL连接池创建成功
2025-07-15 17:11:51,006 - INFO - Redis连接成功
2025-07-15 17:11:51,006 - INFO - ✅ f3数据拉取服务启动成功，预定执行时间: ['02:00', '03:00']
2025-07-15 17:11:51,007 - INFO - 🚀 数据拉取调度器启动，预定运行时间: ['02:00', '03:00']
2025-07-15 17:11:51,007 - INFO - 下一次f3/f5同步任务将在 2025-07-16 02:00:00 执行，等待 31689 秒...
2025-07-15 17:11:51,068 - INFO - ✅ PostgreSQL连接池创建成功
2025-07-15 17:11:51,068 - INFO - ✅ f1监听器服务启动成功
2025-07-15 17:11:51,077 - INFO - ✅ 分区创建成功: entries_2025_07
2025-07-15 17:11:51,080 - INFO - ✅ 分区创建成功: entries_2025_08
2025-07-15 17:11:51,121 - INFO - ✅ PostgreSQL连接池创建成功
2025-07-15 17:11:51,121 - INFO - ✅ f2推送回写服务启动成功，工作线程数: 4
2025-07-15 17:11:51,121 - INFO - 🚀 推送工作线程启动: worker_1
2025-07-15 17:11:51,121 - INFO - 🚀 推送工作线程启动: worker_2
2025-07-15 17:11:51,121 - INFO - 🚀 推送工作线程启动: worker_3
2025-07-15 17:11:51,122 - INFO - 🚀 推送工作线程启动: worker_4
2025-07-15 17:11:51,125 - INFO - ✅ PostgreSQL连接池创建成功
2025-07-15 17:11:51,126 - INFO - ✅ f4操作处理服务启动成功
2025-07-15 17:11:51,206 - INFO - 📡 开始监听频道: push_job
2025-07-15 17:11:51,208 - INFO - 📡 开始监听频道: partition_check
2025-07-15 17:11:51,225 - INFO - 📡 开始监听频道: sync_trigger
2025-07-15 17:13:45,381 - INFO - 📋 2025/07/03 + 16:10 + 处理队列项: UPDATE - entry_id=145969 - queue_id=348 - 重试次数=0
2025-07-15 17:13:45,381 - INFO - 📋 2025/07/03 + 16:10 + 处理队列项: DELETE - entry_id=145969 - queue_id=349 - 重试次数=0
2025-07-15 17:13:45,384 - INFO - 🔄 执行DELETE操作: entry_id=145969, external_id=603736
2025-07-15 17:13:45,384 - INFO - AIOHTTP session for Server6 created.
2025-07-15 17:13:45,505 - WARNING - ⚠️ UPDATE操作跳过: entry_id=145969 (记录已被删除)
2025-07-15 17:13:45,508 - INFO - ✅ UPDATE跳过完成: entry_id=145969 (记录已被删除)
2025-07-15 17:13:49,530 - INFO - ✅ DELETE完成: entry_id=145969, external_id=603736
2025-07-15 17:13:49,531 - INFO - ℹ️ DELETE操作跳过f6同步: entry_id=145969 (记录已删除)
2025-07-15 17:14:21,578 - INFO - 监听任务被取消
2025-07-15 17:14:21,580 - INFO - 🔌 MongoDB连接已关闭
2025-07-15 17:14:21,581 - INFO - 🔌 MongoDB连接已关闭
2025-07-15 17:14:21,582 - INFO - 🔌 Redis连接已关闭
2025-07-15 17:14:21,582 - INFO - 🔌 Redis连接已关闭
2025-07-15 17:14:21,586 - INFO - 🔌 PostgreSQL连接已关闭
2025-07-15 17:14:21,587 - INFO - 🔌 Redis连接已关闭
2025-07-15 17:14:21,587 - INFO - 🔌 f3数据拉取服务已停止
2025-07-15 17:14:21,587 - INFO - 🔌 PostgreSQL连接已关闭
2025-07-15 17:14:21,587 - INFO - 🔌 f1监听器服务已停止
2025-07-15 17:14:21,587 - INFO - 🔌 PostgreSQL连接已关闭
2025-07-15 17:14:21,587 - INFO - 🔌 f4操作处理服务已停止
2025-07-15 17:14:21,669 - INFO - 🔌 推送工作线程停止: worker_1
2025-07-15 17:14:21,690 - INFO - 🔌 推送工作线程停止: worker_2
2025-07-15 17:14:21,738 - INFO - 🔌 推送工作线程停止: worker_3
2025-07-15 17:14:21,763 - INFO - 🔌 推送工作线程停止: worker_4
2025-07-15 17:14:21,764 - INFO - 🔌 MongoDB连接已关闭
2025-07-15 17:14:21,764 - INFO - AIOHTTP session for Server6 closed.
2025-07-15 17:14:21,764 - INFO - 🔌 Redis连接已关闭
2025-07-15 17:14:21,768 - INFO - 🔌 PostgreSQL连接已关闭
2025-07-15 17:14:21,768 - INFO - 🔌 f2推送回写服务已停止
2025-07-15 17:14:21,769 - ERROR - Server5微服务启动失败: asyncio.run() cannot be called from a running event loop
2025-07-15 17:14:21,815 - INFO - 🔌 PostgreSQL连接已关闭
2025-07-15 17:14:21,816 - INFO - 🔌 MongoDB连接已关闭
2025-07-15 17:14:21,816 - INFO - 🔌 PostgreSQL连接已关闭
2025-07-15 17:14:21,816 - INFO - 🔌 MongoDB连接已关闭
2025-07-15 17:14:21,816 - INFO - 🔌 PostgreSQL连接已关闭
2025-07-15 17:14:21,816 - INFO - 🔌 MongoDB连接已关闭
2025-07-15 17:14:21,816 - INFO - 🔌 Redis连接已关闭
2025-07-15 17:14:21,816 - INFO - 🔌 Redis连接已关闭
2025-07-15 17:14:21,816 - INFO - 🔌 Redis连接已关闭
2025-07-15 17:14:21,816 - INFO - 🔌 f1监听器服务已停止
2025-07-15 17:14:21,816 - INFO - 🔌 f2推送回写服务已停止
2025-07-15 17:14:21,816 - INFO - 🔌 f4操作处理服务已停止
2025-07-15 17:14:21,838 - ERROR - Task was destroyed but it is pending!
task: <Task pending name='Task-381' coro=<Connection._cancel() running at /home/<USER>/miniconda3/envs/my_suite_unified/lib/python3.12/site-packages/asyncpg/connection.py:1646> wait_for=<Future pending cb=[Task.task_wakeup()]>>
2025-07-15 17:14:23,058 - INFO - 🔊 f1监听器服务初始化完成
2025-07-15 17:14:23,058 - INFO - 📡 Server6客户端初始化 (新版): http://************:8019
2025-07-15 17:14:23,058 - INFO - 📤 f2推送回写引擎初始化完成 (修复版本: 直接轮询PostgreSQL)
2025-07-15 17:14:23,058 - INFO - 📡 Server6客户端初始化 (新版): http://************:8019
2025-07-15 17:14:23,059 - INFO - 📥 f3数据拉取器初始化完成 (Staging & UPSERT 模式)
2025-07-15 17:14:23,059 - INFO - 🎮 f4操作处理器初始化完成
2025-07-15 17:14:23,069 - INFO - Redis连接成功
2025-07-15 17:14:23,070 - INFO - Redis连接成功
2025-07-15 17:14:23,070 - INFO - Redis连接成功
2025-07-15 17:14:23,074 - INFO - ✅ MongoDB连接成功
2025-07-15 17:14:23,075 - INFO - ✅ MongoDB连接成功
2025-07-15 17:14:23,076 - INFO - ✅ MongoDB连接成功
2025-07-15 17:14:23,335 - INFO - ✅ PostgreSQL连接池创建成功
2025-07-15 17:14:23,336 - INFO - Redis连接成功
2025-07-15 17:14:23,336 - INFO - ✅ f3数据拉取服务启动成功，预定执行时间: ['02:00', '03:00']
2025-07-15 17:14:23,336 - INFO - 🚀 数据拉取调度器启动，预定运行时间: ['02:00', '03:00']
2025-07-15 17:14:23,337 - INFO - 下一次f3/f5同步任务将在 2025-07-16 02:00:00 执行，等待 31537 秒...
2025-07-15 17:14:23,387 - INFO - ✅ PostgreSQL连接池创建成功
2025-07-15 17:14:23,387 - INFO - ✅ f1监听器服务启动成功
2025-07-15 17:14:23,397 - INFO - ✅ 分区创建成功: entries_2025_07
2025-07-15 17:14:23,400 - INFO - ✅ 分区创建成功: entries_2025_08
2025-07-15 17:14:23,443 - INFO - ✅ PostgreSQL连接池创建成功
2025-07-15 17:14:23,443 - INFO - ✅ f2推送回写服务启动成功，工作线程数: 4
2025-07-15 17:14:23,443 - INFO - 🚀 推送工作线程启动: worker_1
2025-07-15 17:14:23,443 - INFO - 🚀 推送工作线程启动: worker_2
2025-07-15 17:14:23,444 - INFO - 🚀 推送工作线程启动: worker_3
2025-07-15 17:14:23,444 - INFO - 🚀 推送工作线程启动: worker_4
2025-07-15 17:14:23,448 - INFO - ✅ PostgreSQL连接池创建成功
2025-07-15 17:14:23,448 - INFO - ✅ f4操作处理服务启动成功
2025-07-15 17:14:23,535 - INFO - 📡 开始监听频道: push_job
2025-07-15 17:14:23,536 - INFO - 📡 开始监听频道: partition_check
2025-07-15 17:14:23,537 - INFO - 📡 开始监听频道: sync_trigger
2025-07-15 17:15:03,545 - INFO - 📋 2025/07/03 + 16:10 + 处理队列项: INSERT - entry_id=145970 - queue_id=350 - 重试次数=0
2025-07-15 17:15:03,650 - INFO - 🔄 执行INSERT操作: entry_id=145970, employee_id=215829
2025-07-15 17:15:03,650 - INFO - 🔍 调试 - 从entries表读取的原始数据: <Record entry_date=datetime.date(2025, 7, 15) id=145970 external_id=None ts=datetime.datetime(2025, 7, 15, 8, 15, 3, 59434, tzinfo=datetime.timezone.utc) employee_id='215829' model=None number=None factory_number=None project_number=None unit_number=None category=0 item=0 duration=Decimal('1.00') department='131' source='user'>
2025-07-15 17:15:03,650 - INFO - 🔍 调试 - category类型: <class 'int'>, 值: 0
2025-07-15 17:15:03,651 - INFO - 🔍 调试 - item类型: <class 'int'>, 值: 0
2025-07-15 17:15:03,651 - INFO - 🔍 调试 - 准备发送给Server6的数据: {'employee_id': '215829', 'entry_date': '2025/07/15', 'model': None, 'number': None, 'factory_number': None, 'project_number': None, 'unit_number': None, 'category': '0', 'item': '0', 'duration': 1.0, 'department': '131'}
2025-07-15 17:15:03,651 - INFO - 📤 发送到Server6的数据: {'従業員ｺｰﾄﾞ': '215829', '日付': '2025-07-15', '機種': None, '号機': None, '工場製番': None, '工事番号': None, 'ﾕﾆｯﾄ番号': None, '区分': '0', '項目': '0', '時間': 1.0, '所属ｺｰﾄﾞ': '131'}
2025-07-15 17:15:03,651 - INFO - 🔍 调试 - category最终值: 0, 类型: <class 'str'>
2025-07-15 17:15:03,652 - INFO - 🔍 调试 - item最终值: 0, 类型: <class 'str'>
2025-07-15 17:15:03,652 - INFO - AIOHTTP session for Server6 created.
2025-07-15 17:15:07,557 - INFO - ✅ INSERT完成: entry_id=145970 -> external_id=603737
2025-07-15 17:15:17,577 - INFO - 📋 2025/07/03 + 16:10 + 处理队列项: UPDATE - entry_id=145970 - queue_id=351 - 重试次数=0
2025-07-15 17:15:17,629 - INFO - 📋 2025/07/03 + 16:10 + 处理队列项: DELETE - entry_id=145970 - queue_id=352 - 重试次数=0
2025-07-15 17:15:17,630 - INFO - 🔄 执行DELETE操作: entry_id=145970, external_id=603737
2025-07-15 17:15:17,686 - WARNING - ⚠️ UPDATE操作跳过: entry_id=145970 (记录已被删除)
2025-07-15 17:15:17,689 - INFO - ✅ UPDATE跳过完成: entry_id=145970 (记录已被删除)
2025-07-15 17:15:21,427 - INFO - ✅ DELETE完成: entry_id=145970, external_id=603737
2025-07-15 17:15:21,429 - INFO - ℹ️ DELETE操作跳过f6同步: entry_id=145970 (记录已删除)
2025-07-15 17:21:44,682 - INFO - 监听任务被取消
2025-07-15 17:21:44,683 - INFO - 🔌 MongoDB连接已关闭
2025-07-15 17:21:44,684 - INFO - 🔌 MongoDB连接已关闭
2025-07-15 17:21:44,685 - INFO - 🔌 Redis连接已关闭
2025-07-15 17:21:44,685 - INFO - 🔌 PostgreSQL连接已关闭
2025-07-15 17:21:44,686 - INFO - 🔌 Redis连接已关闭
2025-07-15 17:21:44,686 - INFO - 🔌 PostgreSQL连接已关闭
2025-07-15 17:21:44,686 - INFO - 🔌 PostgreSQL连接已关闭
2025-07-15 17:21:44,686 - INFO - 🔌 f1监听器服务已停止
2025-07-15 17:21:44,686 - INFO - 🔌 f4操作处理服务已停止
2025-07-15 17:21:44,686 - INFO - 🔌 Redis连接已关闭
2025-07-15 17:21:44,686 - INFO - 🔌 f3数据拉取服务已停止
2025-07-15 17:21:44,848 - INFO - 🔌 推送工作线程停止: worker_3
2025-07-15 17:21:44,899 - INFO - 🔌 推送工作线程停止: worker_1
2025-07-15 17:21:44,930 - INFO - 🔌 推送工作线程停止: worker_2
2025-07-15 17:21:44,964 - INFO - 🔌 推送工作线程停止: worker_4
2025-07-15 17:21:44,965 - INFO - 🔌 MongoDB连接已关闭
2025-07-15 17:21:44,966 - INFO - AIOHTTP session for Server6 closed.
2025-07-15 17:21:44,966 - INFO - 🔌 Redis连接已关闭
2025-07-15 17:21:44,973 - INFO - 🔌 PostgreSQL连接已关闭
2025-07-15 17:21:44,973 - INFO - 🔌 f2推送回写服务已停止
2025-07-15 17:21:44,974 - ERROR - Server5微服务启动失败: asyncio.run() cannot be called from a running event loop
2025-07-15 17:21:45,214 - INFO - 🔌 PostgreSQL连接已关闭
2025-07-15 17:21:45,215 - INFO - 🔌 MongoDB连接已关闭
2025-07-15 17:21:45,215 - INFO - 🔌 PostgreSQL连接已关闭
2025-07-15 17:21:45,215 - INFO - 🔌 MongoDB连接已关闭
2025-07-15 17:21:45,215 - INFO - 🔌 PostgreSQL连接已关闭
2025-07-15 17:21:45,215 - INFO - 🔌 MongoDB连接已关闭
2025-07-15 17:21:45,215 - INFO - 🔌 Redis连接已关闭
2025-07-15 17:21:45,215 - INFO - 🔌 Redis连接已关闭
2025-07-15 17:21:45,215 - INFO - 🔌 Redis连接已关闭
2025-07-15 17:21:45,215 - INFO - 🔌 f1监听器服务已停止
2025-07-15 17:21:45,215 - INFO - 🔌 f2推送回写服务已停止
2025-07-15 17:21:45,216 - INFO - 🔌 f4操作处理服务已停止
2025-07-15 17:21:46,741 - INFO - 🔊 f1监听器服务初始化完成
2025-07-15 17:21:46,741 - INFO - 📡 Server6客户端初始化 (新版): http://************:8019
2025-07-15 17:21:46,742 - INFO - 📤 f2推送回写引擎初始化完成 (修复版本: 直接轮询PostgreSQL)
2025-07-15 17:21:46,742 - INFO - 📡 Server6客户端初始化 (新版): http://************:8019
2025-07-15 17:21:46,742 - INFO - 📥 f3数据拉取器初始化完成 (Staging & UPSERT 模式)
2025-07-15 17:21:46,743 - INFO - 🎮 f4操作处理器初始化完成
2025-07-15 17:21:46,752 - INFO - Redis连接成功
2025-07-15 17:21:46,752 - INFO - Redis连接成功
2025-07-15 17:21:46,752 - INFO - Redis连接成功
2025-07-15 17:21:46,758 - INFO - ✅ MongoDB连接成功
2025-07-15 17:21:46,759 - INFO - ✅ MongoDB连接成功
2025-07-15 17:21:46,759 - INFO - ✅ MongoDB连接成功
2025-07-15 17:21:47,020 - INFO - ✅ PostgreSQL连接池创建成功
2025-07-15 17:21:47,020 - INFO - Redis连接成功
2025-07-15 17:21:47,021 - INFO - ✅ f3数据拉取服务启动成功，预定执行时间: ['02:00', '03:00']
2025-07-15 17:21:47,021 - INFO - 🚀 数据拉取调度器启动，预定运行时间: ['02:00', '03:00']
2025-07-15 17:21:47,021 - INFO - 下一次f3/f5同步任务将在 2025-07-16 02:00:00 执行，等待 31093 秒...
2025-07-15 17:21:47,027 - INFO - ✅ PostgreSQL连接池创建成功
2025-07-15 17:21:47,027 - INFO - ✅ f1监听器服务启动成功
2025-07-15 17:21:47,046 - INFO - ✅ 分区创建成功: entries_2025_07
2025-07-15 17:21:47,050 - INFO - ✅ 分区创建成功: entries_2025_08
2025-07-15 17:21:47,050 - INFO - ✅ PostgreSQL连接池创建成功
2025-07-15 17:21:47,050 - INFO - ✅ f2推送回写服务启动成功，工作线程数: 4
2025-07-15 17:21:47,050 - INFO - 🚀 推送工作线程启动: worker_1
2025-07-15 17:21:47,050 - INFO - 🚀 推送工作线程启动: worker_2
2025-07-15 17:21:47,051 - INFO - 🚀 推送工作线程启动: worker_3
2025-07-15 17:21:47,051 - INFO - 🚀 推送工作线程启动: worker_4
2025-07-15 17:21:47,110 - INFO - ✅ PostgreSQL连接池创建成功
2025-07-15 17:21:47,110 - INFO - ✅ f4操作处理服务启动成功
2025-07-15 17:21:47,161 - INFO - 📡 开始监听频道: push_job
2025-07-15 17:21:47,162 - INFO - 📡 开始监听频道: partition_check
2025-07-15 17:21:47,164 - INFO - 📡 开始监听频道: sync_trigger
2025-07-15 17:22:13,129 - INFO - 📋 2025/07/03 + 16:10 + 处理队列项: INSERT - entry_id=145971 - queue_id=353 - 重试次数=0
2025-07-15 17:22:13,238 - INFO - 🔄 执行INSERT操作: entry_id=145971, employee_id=215829
2025-07-15 17:22:13,239 - INFO - 🔍 调试 - 从entries表读取的原始数据: <Record entry_date=datetime.date(2025, 7, 15) id=145971 external_id=None ts=datetime.datetime(2025, 7, 15, 8, 22, 13, 89279, tzinfo=datetime.timezone.utc) employee_id='215829' model=None number=None factory_number=None project_number=None unit_number=None category=0 item=0 duration=Decimal('0.30') department='131' source='user'>
2025-07-15 17:22:13,239 - INFO - 🔍 调试 - category类型: <class 'int'>, 值: 0
2025-07-15 17:22:13,239 - INFO - 🔍 调试 - item类型: <class 'int'>, 值: 0
2025-07-15 17:22:13,239 - INFO - 🔍 调试 - 准备发送给Server6的数据: {'employee_id': '215829', 'entry_date': '2025/07/15', 'model': None, 'number': None, 'factory_number': None, 'project_number': None, 'unit_number': None, 'category': '0', 'item': '0', 'duration': 0.3, 'department': '131'}
2025-07-15 17:22:13,240 - INFO - 📤 发送到Server6的数据: {'従業員ｺｰﾄﾞ': '215829', '日付': '2025-07-15', '機種': None, '号機': None, '工場製番': None, '工事番号': None, 'ﾕﾆｯﾄ番号': None, '区分': '0', '項目': '0', '時間': 0.3, '所属ｺｰﾄﾞ': '131'}
2025-07-15 17:22:13,240 - INFO - 🔍 调试 - category最终值: 0, 类型: <class 'str'>
2025-07-15 17:22:13,240 - INFO - 🔍 调试 - item最终值: 0, 类型: <class 'str'>
2025-07-15 17:22:13,240 - INFO - AIOHTTP session for Server6 created.
2025-07-15 17:22:17,301 - INFO - ✅ INSERT完成: entry_id=145971 -> external_id=603738
2025-07-15 17:24:13,559 - INFO - 监听任务被取消
2025-07-15 17:24:13,560 - INFO - 🔌 MongoDB连接已关闭
2025-07-15 17:24:13,562 - INFO - 🔌 MongoDB连接已关闭
2025-07-15 17:24:13,562 - INFO - 🔌 Redis连接已关闭
2025-07-15 17:24:13,562 - INFO - 🔌 Redis连接已关闭
2025-07-15 17:24:13,567 - INFO - 🔌 PostgreSQL连接已关闭
2025-07-15 17:24:13,567 - INFO - 🔌 f1监听器服务已停止
2025-07-15 17:24:13,568 - INFO - 🔌 PostgreSQL连接已关闭
2025-07-15 17:24:13,568 - INFO - 🔌 Redis连接已关闭
2025-07-15 17:24:13,568 - INFO - 🔌 f3数据拉取服务已停止
2025-07-15 17:24:13,569 - INFO - 🔌 PostgreSQL连接已关闭
2025-07-15 17:24:13,569 - INFO - 🔌 f4操作处理服务已停止
2025-07-15 17:24:13,654 - INFO - 🔌 推送工作线程停止: worker_2
2025-07-15 17:24:13,692 - INFO - 🔌 推送工作线程停止: worker_1
2025-07-15 17:24:13,715 - INFO - 🔌 推送工作线程停止: worker_4
2025-07-15 17:24:13,753 - INFO - 🔌 推送工作线程停止: worker_3
2025-07-15 17:24:13,755 - INFO - 🔌 MongoDB连接已关闭
2025-07-15 17:24:13,755 - INFO - AIOHTTP session for Server6 closed.
2025-07-15 17:24:13,755 - INFO - 🔌 Redis连接已关闭
2025-07-15 17:24:13,759 - INFO - 🔌 PostgreSQL连接已关闭
2025-07-15 17:24:13,759 - INFO - 🔌 f2推送回写服务已停止
2025-07-15 17:24:13,760 - ERROR - Server5微服务启动失败: asyncio.run() cannot be called from a running event loop
2025-07-15 17:24:13,803 - INFO - 🔌 PostgreSQL连接已关闭
2025-07-15 17:24:13,803 - INFO - 🔌 MongoDB连接已关闭
2025-07-15 17:24:13,803 - INFO - 🔌 PostgreSQL连接已关闭
2025-07-15 17:24:13,803 - INFO - 🔌 MongoDB连接已关闭
2025-07-15 17:24:13,804 - INFO - 🔌 PostgreSQL连接已关闭
2025-07-15 17:24:13,804 - INFO - 🔌 MongoDB连接已关闭
2025-07-15 17:24:13,804 - INFO - 🔌 Redis连接已关闭
2025-07-15 17:24:13,804 - INFO - 🔌 Redis连接已关闭
2025-07-15 17:24:13,804 - INFO - 🔌 Redis连接已关闭
2025-07-15 17:24:13,804 - INFO - 🔌 f1监听器服务已停止
2025-07-15 17:24:13,804 - INFO - 🔌 f2推送回写服务已停止
2025-07-15 17:24:13,804 - INFO - 🔌 f4操作处理服务已停止
2025-07-15 17:24:13,827 - ERROR - Task was destroyed but it is pending!
task: <Task pending name='Task-370' coro=<Connection._cancel() running at /home/<USER>/miniconda3/envs/my_suite_unified/lib/python3.12/site-packages/asyncpg/connection.py:1646> wait_for=<Future pending cb=[Task.task_wakeup()]>>
2025-07-15 17:24:16,018 - INFO - 🔊 f1监听器服务初始化完成
2025-07-15 17:24:16,018 - INFO - 📡 Server6客户端初始化 (新版): http://************:8019
2025-07-15 17:24:16,018 - INFO - 📤 f2推送回写引擎初始化完成 (修复版本: 直接轮询PostgreSQL)
2025-07-15 17:24:16,018 - INFO - 📡 Server6客户端初始化 (新版): http://************:8019
2025-07-15 17:24:16,019 - INFO - 📥 f3数据拉取器初始化完成 (Staging & UPSERT 模式)
2025-07-15 17:24:16,019 - INFO - 🎮 f4操作处理器初始化完成
2025-07-15 17:24:16,028 - INFO - Redis连接成功
2025-07-15 17:24:16,028 - INFO - Redis连接成功
2025-07-15 17:24:16,028 - INFO - Redis连接成功
2025-07-15 17:24:16,035 - INFO - ✅ MongoDB连接成功
2025-07-15 17:24:16,037 - INFO - ✅ MongoDB连接成功
2025-07-15 17:24:16,037 - INFO - ✅ MongoDB连接成功
2025-07-15 17:24:16,412 - INFO - ✅ PostgreSQL连接池创建成功
2025-07-15 17:24:16,412 - INFO - ✅ f1监听器服务启动成功
2025-07-15 17:24:16,433 - INFO - ✅ PostgreSQL连接池创建成功
2025-07-15 17:24:16,433 - INFO - ✅ f4操作处理服务启动成功
2025-07-15 17:24:16,434 - INFO - ✅ 分区创建成功: entries_2025_07
2025-07-15 17:24:16,437 - INFO - ✅ PostgreSQL连接池创建成功
2025-07-15 17:24:16,438 - INFO - ✅ 分区创建成功: entries_2025_08
2025-07-15 17:24:16,439 - INFO - Redis连接成功
2025-07-15 17:24:16,439 - INFO - ✅ f3数据拉取服务启动成功，预定执行时间: ['02:00', '03:00']
2025-07-15 17:24:16,439 - INFO - 🚀 数据拉取调度器启动，预定运行时间: ['02:00', '03:00']
2025-07-15 17:24:16,439 - INFO - 下一次f3/f5同步任务将在 2025-07-16 02:00:00 执行，等待 30944 秒...
2025-07-15 17:24:16,478 - INFO - ✅ PostgreSQL连接池创建成功
2025-07-15 17:24:16,478 - INFO - ✅ f2推送回写服务启动成功，工作线程数: 4
2025-07-15 17:24:16,478 - INFO - 🚀 推送工作线程启动: worker_1
2025-07-15 17:24:16,478 - INFO - 🚀 推送工作线程启动: worker_2
2025-07-15 17:24:16,478 - INFO - 🚀 推送工作线程启动: worker_3
2025-07-15 17:24:16,479 - INFO - 🚀 推送工作线程启动: worker_4
2025-07-15 17:24:16,537 - INFO - 📡 开始监听频道: push_job
2025-07-15 17:24:16,538 - INFO - 📡 开始监听频道: partition_check
2025-07-15 17:24:16,540 - INFO - 📡 开始监听频道: sync_trigger
2025-07-15 17:32:19,614 - INFO - 📋 2025/07/03 + 16:10 + 处理队列项: UPDATE - entry_id=145971 - queue_id=354 - 重试次数=0
2025-07-15 17:32:19,615 - INFO - 📋 2025/07/03 + 16:10 + 处理队列项: DELETE - entry_id=145971 - queue_id=355 - 重试次数=0
2025-07-15 17:32:19,617 - INFO - 🔄 执行DELETE操作: entry_id=145971, external_id=603738
2025-07-15 17:32:19,617 - INFO - AIOHTTP session for Server6 created.
2025-07-15 17:32:19,754 - WARNING - ⚠️ UPDATE操作跳过: entry_id=145971 (记录已被删除)
2025-07-15 17:32:19,757 - INFO - ✅ UPDATE跳过完成: entry_id=145971 (记录已被删除)
2025-07-15 17:32:24,033 - INFO - ✅ DELETE完成: entry_id=145971, external_id=603738
2025-07-15 17:32:24,035 - INFO - ℹ️ DELETE操作跳过f6同步: entry_id=145971 (记录已删除)
2025-07-15 17:32:29,785 - INFO - 📋 2025/07/03 + 16:10 + 处理队列项: UPDATE - entry_id=145965 - queue_id=356 - 重试次数=0
2025-07-15 17:32:29,815 - INFO - 🔄 执行UPDATE操作: entry_id=145965 -> external_id=603732
2025-07-15 17:32:29,815 - INFO - 📦 更新数据: {'employee_id': '215829', 'entry_date': '2025/07/15', 'model': None, 'number': None, 'factory_number': 'HA0484', 'project_number': None, 'unit_number': None, 'category': '0', 'item': '7', 'duration': 9.0, 'department': '131'}
2025-07-15 17:32:29,815 - INFO - 📤 发送到Server6的更新数据: {'従業員ｺｰﾄﾞ': '215829', '日付': '2025-07-15', '機種': None, '号機': None, '工場製番': 'HA0484', '工事番号': None, 'ﾕﾆｯﾄ番号': None, '区分': '0', '項目': '7', '時間': 9.0, '所属ｺｰﾄﾞ': '131'}
2025-07-15 17:32:33,695 - INFO - ✅ UPDATE完成: entry_id=145965 -> external_id=603732, source已更新为'system'
2025-07-15 18:24:16,599 - INFO - ✅ 分区创建成功: entries_2025_07
2025-07-15 18:24:16,602 - INFO - ✅ 分区创建成功: entries_2025_08
2025-07-15 19:24:16,773 - INFO - ✅ 分区创建成功: entries_2025_07
2025-07-15 19:24:16,776 - INFO - ✅ 分区创建成功: entries_2025_08
2025-07-15 20:24:16,941 - INFO - ✅ 分区创建成功: entries_2025_07
2025-07-15 20:24:16,945 - INFO - ✅ 分区创建成功: entries_2025_08
2025-07-15 21:24:17,056 - INFO - ✅ 分区创建成功: entries_2025_07
2025-07-15 21:24:17,059 - INFO - ✅ 分区创建成功: entries_2025_08
2025-07-15 22:24:17,188 - INFO - ✅ 分区创建成功: entries_2025_07
2025-07-15 22:24:17,191 - INFO - ✅ 分区创建成功: entries_2025_08
2025-07-15 23:24:17,308 - INFO - ✅ 分区创建成功: entries_2025_07
2025-07-15 23:24:17,311 - INFO - ✅ 分区创建成功: entries_2025_08
2025-07-16 00:24:17,424 - INFO - ✅ 分区创建成功: entries_2025_07
2025-07-16 00:24:17,427 - INFO - ✅ 分区创建成功: entries_2025_08
2025-07-16 01:24:17,545 - INFO - ✅ 分区创建成功: entries_2025_07
2025-07-16 01:24:22,548 - ERROR - ❌ 同步日志记录失败: ************:27017: timed out (configured timeouts: socketTimeoutMS: 20000.0ms, connectTimeoutMS: 20000.0ms), Timeout: 5.0s, Topology Description: <TopologyDescription id: 687610307724503d2d237621, topology_type: Single, servers: [<ServerDescription ('************', 27017) server_type: Unknown, rtt: None, error=NetworkTimeout('************:27017: timed out (configured timeouts: socketTimeoutMS: 20000.0ms, connectTimeoutMS: 20000.0ms)')>]>
2025-07-16 01:24:22,553 - INFO - ✅ 分区创建成功: entries_2025_08
2025-07-16 01:24:27,698 - ERROR - ❌ 同步日志记录失败: ************:27017: timed out (configured timeouts: socketTimeoutMS: 20000.0ms, connectTimeoutMS: 20000.0ms), Timeout: 5.0s, Topology Description: <TopologyDescription id: 687610307724503d2d237621, topology_type: Single, servers: [<ServerDescription ('************', 27017) server_type: Unknown, rtt: None, error=NetworkTimeout('************:27017: timed out (configured timeouts: socketTimeoutMS: 20000.0ms, connectTimeoutMS: 20000.0ms)')>]>
2025-07-16 02:00:00,001 - INFO - ⏰ 到达预定时间，开始执行 f3/f5 同步周期...
2025-07-16 02:00:00,001 - INFO - --- 开始 f3 (UPSERT) 阶段 ---
2025-07-16 02:00:00,002 - INFO - --- f3 (UPSERT) 阶段完成 ---
2025-07-16 02:00:00,002 - INFO - --- 准备启动 f5 (Deletion) 阶段 ---
2025-07-16 02:00:00,002 - INFO - 📡 Server6客户端初始化 (新版): http://************:8019
2025-07-16 02:00:00,002 - INFO - 🗑️ f5删除同步服务初始化完成
2025-07-16 02:00:00,014 - INFO - Redis连接成功
2025-07-16 02:00:00,226 - INFO - ✅ PostgreSQL连接池创建成功
2025-07-16 02:00:05,014 - ERROR - ❌ MongoDB连接失败: No servers found yet, Timeout: 5.0s, Topology Description: <TopologyDescription id: 687689107724503d2d23763f, topology_type: Unknown, servers: [<ServerDescription ('************', 27017) server_type: Unknown, rtt: None>]>
2025-07-16 02:00:05,014 - ERROR - ❌ 数据库连接失败，删除同步服务无法启动: [True, True, False]
2025-07-16 02:00:05,014 - INFO - --- 开始执行f5删除同步任务 ---
2025-07-16 02:00:05,014 - INFO - 删除同步范围: 从 2025-05-17 到 2025-07-16
2025-07-16 02:00:05,015 - INFO - AIOHTTP session for Server6 created.
2025-07-16 02:00:45,701 - INFO - 在MDB中找到 3717 个唯一的 external_id。
2025-07-16 02:00:45,764 - INFO - 在PostgreSQL中找到 3449 个唯一的 external_id。
2025-07-16 02:00:45,764 - WARNING - 发现 3 条需要从PostgreSQL删除的记录。
2025-07-16 02:00:47,890 - INFO - 成功删除了 3 条记录 (已禁用触发器)
2025-07-16 02:00:47,895 - INFO - 成功执行删除操作，影响了 3 条记录。
2025-07-16 02:00:47,895 - ERROR - ❌ 同步日志记录失败: 'NoneType' object has no attribute 'sync_logs'
2025-07-16 02:00:47,895 - INFO - --- f5删除同步任务完成，耗时 42.88 秒。本次共删除 3 条记录 ---
2025-07-16 02:00:47,895 - INFO - 🔌 MongoDB连接已关闭
2025-07-16 02:00:47,895 - INFO - AIOHTTP session for Server6 closed.
2025-07-16 02:00:47,895 - INFO - 🔌 Redis连接已关闭
2025-07-16 02:00:47,911 - INFO - 🔌 PostgreSQL连接已关闭
2025-07-16 02:00:47,911 - INFO - 🔌 f5删除同步服务已停止
2025-07-16 02:00:47,911 - INFO - --- f5 (Deletion) 阶段完成 ---
2025-07-16 02:00:47,911 - INFO - 下一次f3/f5同步任务将在 2025-07-16 03:00:00 执行，等待 3552 秒...
2025-07-16 02:24:27,850 - INFO - ✅ 分区创建成功: entries_2025_07
2025-07-16 02:24:32,853 - ERROR - ❌ 同步日志记录失败: ************:27017: timed out (configured timeouts: socketTimeoutMS: 20000.0ms, connectTimeoutMS: 20000.0ms), Timeout: 5.0s, Topology Description: <TopologyDescription id: 687610307724503d2d237621, topology_type: Single, servers: [<ServerDescription ('************', 27017) server_type: Unknown, rtt: None, error=NetworkTimeout('************:27017: timed out (configured timeouts: socketTimeoutMS: 20000.0ms, connectTimeoutMS: 20000.0ms)')>]>
2025-07-16 02:24:32,855 - INFO - ✅ 分区创建成功: entries_2025_08
2025-07-16 02:24:37,857 - ERROR - ❌ 同步日志记录失败: ************:27017: timed out (configured timeouts: socketTimeoutMS: 20000.0ms, connectTimeoutMS: 20000.0ms), Timeout: 5.0s, Topology Description: <TopologyDescription id: 687610307724503d2d237621, topology_type: Single, servers: [<ServerDescription ('************', 27017) server_type: Unknown, rtt: None, error=NetworkTimeout('************:27017: timed out (configured timeouts: socketTimeoutMS: 20000.0ms, connectTimeoutMS: 20000.0ms)')>]>
2025-07-16 03:00:00,001 - INFO - ⏰ 到达预定时间，开始执行 f3/f5 同步周期...
2025-07-16 03:00:00,002 - INFO - --- 开始 f3 (UPSERT) 阶段 ---
2025-07-16 03:00:00,002 - INFO - --- f3 (UPSERT) 阶段完成 ---
2025-07-16 03:00:00,002 - INFO - --- 准备启动 f5 (Deletion) 阶段 ---
2025-07-16 03:00:00,002 - INFO - 📡 Server6客户端初始化 (新版): http://************:8019
2025-07-16 03:00:00,002 - INFO - 🗑️ f5删除同步服务初始化完成
2025-07-16 03:00:00,007 - INFO - Redis连接成功
2025-07-16 03:00:00,223 - INFO - ✅ PostgreSQL连接池创建成功
2025-07-16 03:00:05,007 - ERROR - ❌ MongoDB连接失败: No servers found yet, Timeout: 5.0s, Topology Description: <TopologyDescription id: 687697207724503d2d237642, topology_type: Unknown, servers: [<ServerDescription ('************', 27017) server_type: Unknown, rtt: None>]>
2025-07-16 03:00:05,008 - ERROR - ❌ 数据库连接失败，删除同步服务无法启动: [True, True, False]
2025-07-16 03:00:05,008 - INFO - --- 开始执行f5删除同步任务 ---
2025-07-16 03:00:05,008 - INFO - 删除同步范围: 从 2025-05-17 到 2025-07-16
2025-07-16 03:00:05,008 - INFO - AIOHTTP session for Server6 created.
2025-07-16 03:00:45,309 - INFO - 在MDB中找到 3717 个唯一的 external_id。
2025-07-16 03:00:45,350 - INFO - 在PostgreSQL中找到 3446 个唯一的 external_id。
2025-07-16 03:00:45,350 - INFO - ✅ 检查完成。没有发现在PostgreSQL中存在但在MDB中已被删除的记录。
2025-07-16 03:00:45,351 - INFO - --- f5删除同步任务完成，耗时 40.34 秒。本次共删除 0 条记录 ---
2025-07-16 03:00:45,351 - INFO - 🔌 MongoDB连接已关闭
2025-07-16 03:00:45,352 - INFO - AIOHTTP session for Server6 closed.
2025-07-16 03:00:45,352 - INFO - 🔌 Redis连接已关闭
2025-07-16 03:00:45,357 - INFO - 🔌 PostgreSQL连接已关闭
2025-07-16 03:00:45,357 - INFO - 🔌 f5删除同步服务已停止
2025-07-16 03:00:45,358 - INFO - --- f5 (Deletion) 阶段完成 ---
2025-07-16 03:00:45,358 - INFO - 下一次f3/f5同步任务将在 2025-07-17 02:00:00 执行，等待 82755 秒...
2025-07-16 03:24:37,993 - INFO - ✅ 分区创建成功: entries_2025_07
2025-07-16 03:24:42,997 - ERROR - ❌ 同步日志记录失败: ************:27017: timed out (configured timeouts: socketTimeoutMS: 20000.0ms, connectTimeoutMS: 20000.0ms), Timeout: 5.0s, Topology Description: <TopologyDescription id: 687610307724503d2d237621, topology_type: Single, servers: [<ServerDescription ('************', 27017) server_type: Unknown, rtt: None, error=NetworkTimeout('************:27017: timed out (configured timeouts: socketTimeoutMS: 20000.0ms, connectTimeoutMS: 20000.0ms)')>]>
2025-07-16 03:24:43,002 - INFO - ✅ 分区创建成功: entries_2025_08
2025-07-16 03:24:48,005 - ERROR - ❌ 同步日志记录失败: ************:27017: timed out (configured timeouts: socketTimeoutMS: 20000.0ms, connectTimeoutMS: 20000.0ms), Timeout: 5.0s, Topology Description: <TopologyDescription id: 687610307724503d2d237621, topology_type: Single, servers: [<ServerDescription ('************', 27017) server_type: Unknown, rtt: None, error=NetworkTimeout('************:27017: timed out (configured timeouts: socketTimeoutMS: 20000.0ms, connectTimeoutMS: 20000.0ms)')>]>
2025-07-16 04:24:48,119 - INFO - ✅ 分区创建成功: entries_2025_07
2025-07-16 04:24:53,588 - ERROR - ❌ 同步日志记录失败: ************:27017: timed out (configured timeouts: socketTimeoutMS: 20000.0ms, connectTimeoutMS: 20000.0ms), Timeout: 5.0s, Topology Description: <TopologyDescription id: 687610307724503d2d237621, topology_type: Single, servers: [<ServerDescription ('************', 27017) server_type: Unknown, rtt: None, error=NetworkTimeout('************:27017: timed out (configured timeouts: socketTimeoutMS: 20000.0ms, connectTimeoutMS: 20000.0ms)')>]>
2025-07-16 04:24:53,593 - INFO - ✅ 分区创建成功: entries_2025_08
2025-07-16 04:24:58,597 - ERROR - ❌ 同步日志记录失败: ************:27017: timed out (configured timeouts: socketTimeoutMS: 20000.0ms, connectTimeoutMS: 20000.0ms), Timeout: 5.0s, Topology Description: <TopologyDescription id: 687610307724503d2d237621, topology_type: Single, servers: [<ServerDescription ('************', 27017) server_type: Unknown, rtt: None, error=NetworkTimeout('************:27017: timed out (configured timeouts: socketTimeoutMS: 20000.0ms, connectTimeoutMS: 20000.0ms)')>]>
2025-07-16 05:24:58,724 - INFO - ✅ 分区创建成功: entries_2025_07
2025-07-16 05:25:03,727 - ERROR - ❌ 同步日志记录失败: ************:27017: timed out (configured timeouts: socketTimeoutMS: 20000.0ms, connectTimeoutMS: 20000.0ms), Timeout: 5.0s, Topology Description: <TopologyDescription id: 687610307724503d2d237621, topology_type: Single, servers: [<ServerDescription ('************', 27017) server_type: Unknown, rtt: None, error=NetworkTimeout('************:27017: timed out (configured timeouts: socketTimeoutMS: 20000.0ms, connectTimeoutMS: 20000.0ms)')>]>
2025-07-16 05:25:03,729 - INFO - ✅ 分区创建成功: entries_2025_08
2025-07-16 05:25:09,053 - ERROR - ❌ 同步日志记录失败: ************:27017: timed out (configured timeouts: socketTimeoutMS: 20000.0ms, connectTimeoutMS: 20000.0ms), Timeout: 5.0s, Topology Description: <TopologyDescription id: 687610307724503d2d237621, topology_type: Single, servers: [<ServerDescription ('************', 27017) server_type: Unknown, rtt: None, error=NetworkTimeout('************:27017: timed out (configured timeouts: socketTimeoutMS: 20000.0ms, connectTimeoutMS: 20000.0ms)')>]>
2025-07-16 06:25:09,183 - INFO - ✅ 分区创建成功: entries_2025_07
2025-07-16 06:25:14,186 - ERROR - ❌ 同步日志记录失败: ************:27017: timed out (configured timeouts: socketTimeoutMS: 20000.0ms, connectTimeoutMS: 20000.0ms), Timeout: 5.0s, Topology Description: <TopologyDescription id: 687610307724503d2d237621, topology_type: Single, servers: [<ServerDescription ('************', 27017) server_type: Unknown, rtt: None, error=NetworkTimeout('************:27017: timed out (configured timeouts: socketTimeoutMS: 20000.0ms, connectTimeoutMS: 20000.0ms)')>]>
2025-07-16 06:25:14,188 - INFO - ✅ 分区创建成功: entries_2025_08
2025-07-16 06:25:19,421 - ERROR - ❌ 同步日志记录失败: ************:27017: timed out (configured timeouts: socketTimeoutMS: 20000.0ms, connectTimeoutMS: 20000.0ms), Timeout: 5.0s, Topology Description: <TopologyDescription id: 687610307724503d2d237621, topology_type: Single, servers: [<ServerDescription ('************', 27017) server_type: Unknown, rtt: None, error=NetworkTimeout('************:27017: timed out (configured timeouts: socketTimeoutMS: 20000.0ms, connectTimeoutMS: 20000.0ms)')>]>
2025-07-16 07:25:19,554 - INFO - ✅ 分区创建成功: entries_2025_07
2025-07-16 07:25:24,557 - ERROR - ❌ 同步日志记录失败: ************:27017: timed out (configured timeouts: socketTimeoutMS: 20000.0ms, connectTimeoutMS: 20000.0ms), Timeout: 5.0s, Topology Description: <TopologyDescription id: 687610307724503d2d237621, topology_type: Single, servers: [<ServerDescription ('************', 27017) server_type: Unknown, rtt: None, error=NetworkTimeout('************:27017: timed out (configured timeouts: socketTimeoutMS: 20000.0ms, connectTimeoutMS: 20000.0ms)')>]>
2025-07-16 07:25:24,562 - INFO - ✅ 分区创建成功: entries_2025_08
2025-07-16 07:25:29,567 - ERROR - ❌ 同步日志记录失败: ************:27017: timed out (configured timeouts: socketTimeoutMS: 20000.0ms, connectTimeoutMS: 20000.0ms), Timeout: 5.0s, Topology Description: <TopologyDescription id: 687610307724503d2d237621, topology_type: Single, servers: [<ServerDescription ('************', 27017) server_type: Unknown, rtt: None, error=NetworkTimeout('************:27017: timed out (configured timeouts: socketTimeoutMS: 20000.0ms, connectTimeoutMS: 20000.0ms)')>]>
2025-07-16 07:42:24,154 - INFO - 📋 2025/07/03 + 16:10 + 处理队列项: INSERT - entry_id=145972 - queue_id=357 - 重试次数=0
2025-07-16 07:42:24,250 - ERROR - ❌ INSERT操作失败: 找不到entry记录: entry_id=145972
Traceback (most recent call last):
  File "/home/<USER>/Documents/testF/2025/ser25/server0613/MySuiteDatebase2402newuigood-yolo5-3/server5/app/services/f2_push_writer.py", line 300, in _handle_insert_operation_in_transaction
    raise ValueError(f"找不到entry记录: entry_id={entry_id}")
ValueError: 找不到entry记录: entry_id=145972
2025-07-16 07:42:24,250 - ERROR - ❌ 2025/07/03 + 16:10 + 队列项处理失败 (第1次): 找不到entry记录: entry_id=145972
2025-07-16 07:42:24,252 - WARNING - ⚠️ 2025/07/03 + 16:10 + 队列项将在下次循环重试: queue_id=357, retry_count=1
2025-07-16 07:42:24,384 - INFO - 📋 2025/07/03 + 16:10 + 处理队列项: DELETE - entry_id=145972 - queue_id=358 - 重试次数=0
2025-07-16 07:42:24,386 - INFO - 🔄 DELETE操作跳过MDB删除: entry_id=145972 (无external_id，记录未同步到MDB)
2025-07-16 07:42:24,388 - INFO - ✅ DELETE跳过完成: entry_id=145972 (记录未同步到MDB)
2025-07-16 07:42:27,986 - ERROR - ❌ 性能指标记录失败: ************:27017: timed out (configured timeouts: socketTimeoutMS: 20000.0ms, connectTimeoutMS: 20000.0ms), Timeout: 5.0s, Topology Description: <TopologyDescription id: 687610307724503d2d237621, topology_type: Single, servers: [<ServerDescription ('************', 27017) server_type: Unknown, rtt: None, error=NetworkTimeout('************:27017: timed out (configured timeouts: socketTimeoutMS: 20000.0ms, connectTimeoutMS: 20000.0ms)')>]>
2025-07-16 07:42:28,093 - ERROR - ❌ 性能指标记录失败: ************:27017: timed out (configured timeouts: socketTimeoutMS: 20000.0ms, connectTimeoutMS: 20000.0ms), Timeout: 5.0s, Topology Description: <TopologyDescription id: 687610307724503d2d237621, topology_type: Single, servers: [<ServerDescription ('************', 27017) server_type: Unknown, rtt: None, error=NetworkTimeout('************:27017: timed out (configured timeouts: socketTimeoutMS: 20000.0ms, connectTimeoutMS: 20000.0ms)')>]>
2025-07-16 07:42:29,254 - ERROR - ❌ 错误日志记录失败: ************:27017: timed out (configured timeouts: socketTimeoutMS: 20000.0ms, connectTimeoutMS: 20000.0ms), Timeout: 5.0s, Topology Description: <TopologyDescription id: 687610307724503d2d237622, topology_type: Single, servers: [<ServerDescription ('************', 27017) server_type: Unknown, rtt: None, error=NetworkTimeout('************:27017: timed out (configured timeouts: socketTimeoutMS: 20000.0ms, connectTimeoutMS: 20000.0ms)')>]>
2025-07-16 07:42:29,258 - INFO - 📋 2025/07/03 + 16:10 + 处理队列项: INSERT - entry_id=145972 - queue_id=357 - 重试次数=1
2025-07-16 07:42:29,272 - ERROR - ❌ INSERT操作失败: 找不到entry记录: entry_id=145972
Traceback (most recent call last):
  File "/home/<USER>/Documents/testF/2025/ser25/server0613/MySuiteDatebase2402newuigood-yolo5-3/server5/app/services/f2_push_writer.py", line 300, in _handle_insert_operation_in_transaction
    raise ValueError(f"找不到entry记录: entry_id={entry_id}")
ValueError: 找不到entry记录: entry_id=145972
2025-07-16 07:42:29,273 - ERROR - ❌ 2025/07/03 + 16:10 + 队列项处理失败 (第2次): 找不到entry记录: entry_id=145972
2025-07-16 07:42:29,274 - WARNING - ⚠️ 2025/07/03 + 16:10 + 队列项将在下次循环重试: queue_id=357, retry_count=2
2025-07-16 07:42:29,390 - ERROR - ❌ 同步日志记录失败: ************:27017: timed out (configured timeouts: socketTimeoutMS: 20000.0ms, connectTimeoutMS: 20000.0ms), Timeout: 5.0s, Topology Description: <TopologyDescription id: 687610307724503d2d237622, topology_type: Single, servers: [<ServerDescription ('************', 27017) server_type: Unknown, rtt: None, error=NetworkTimeout('************:27017: timed out (configured timeouts: socketTimeoutMS: 20000.0ms, connectTimeoutMS: 20000.0ms)')>]>
2025-07-16 07:42:34,276 - ERROR - ❌ 错误日志记录失败: ************:27017: timed out (configured timeouts: socketTimeoutMS: 20000.0ms, connectTimeoutMS: 20000.0ms), Timeout: 5.0s, Topology Description: <TopologyDescription id: 687610307724503d2d237622, topology_type: Single, servers: [<ServerDescription ('************', 27017) server_type: Unknown, rtt: None, error=NetworkTimeout('************:27017: timed out (configured timeouts: socketTimeoutMS: 20000.0ms, connectTimeoutMS: 20000.0ms)')>]>
2025-07-16 07:42:34,280 - INFO - 📋 2025/07/03 + 16:10 + 处理队列项: INSERT - entry_id=145972 - queue_id=357 - 重试次数=2
2025-07-16 07:42:34,294 - ERROR - ❌ INSERT操作失败: 找不到entry记录: entry_id=145972
Traceback (most recent call last):
  File "/home/<USER>/Documents/testF/2025/ser25/server0613/MySuiteDatebase2402newuigood-yolo5-3/server5/app/services/f2_push_writer.py", line 300, in _handle_insert_operation_in_transaction
    raise ValueError(f"找不到entry记录: entry_id={entry_id}")
ValueError: 找不到entry记录: entry_id=145972
2025-07-16 07:42:34,294 - ERROR - ❌ 2025/07/03 + 16:10 + 队列项处理失败 (第3次): 找不到entry记录: entry_id=145972
2025-07-16 07:42:34,295 - WARNING - ⚠️ 2025/07/03 + 16:10 + 队列项将在下次循环重试: queue_id=357, retry_count=3
2025-07-16 07:42:34,392 - ERROR - ❌ 性能指标记录失败: ************:27017: timed out (configured timeouts: socketTimeoutMS: 20000.0ms, connectTimeoutMS: 20000.0ms), Timeout: 5.0s, Topology Description: <TopologyDescription id: 687610307724503d2d237622, topology_type: Single, servers: [<ServerDescription ('************', 27017) server_type: Unknown, rtt: None, error=NetworkTimeout('************:27017: timed out (configured timeouts: socketTimeoutMS: 20000.0ms, connectTimeoutMS: 20000.0ms)')>]>
2025-07-16 07:42:39,296 - ERROR - ❌ 错误日志记录失败: ************:27017: timed out (configured timeouts: socketTimeoutMS: 20000.0ms, connectTimeoutMS: 20000.0ms), Timeout: 5.0s, Topology Description: <TopologyDescription id: 687610307724503d2d237622, topology_type: Single, servers: [<ServerDescription ('************', 27017) server_type: Unknown, rtt: None, error=NetworkTimeout('************:27017: timed out (configured timeouts: socketTimeoutMS: 20000.0ms, connectTimeoutMS: 20000.0ms)')>]>
2025-07-16 07:42:39,300 - INFO - 📋 2025/07/03 + 16:10 + 处理队列项: INSERT - entry_id=145972 - queue_id=357 - 重试次数=3
2025-07-16 07:42:39,320 - ERROR - ❌ INSERT操作失败: 找不到entry记录: entry_id=145972
Traceback (most recent call last):
  File "/home/<USER>/Documents/testF/2025/ser25/server0613/MySuiteDatebase2402newuigood-yolo5-3/server5/app/services/f2_push_writer.py", line 300, in _handle_insert_operation_in_transaction
    raise ValueError(f"找不到entry记录: entry_id={entry_id}")
ValueError: 找不到entry记录: entry_id=145972
2025-07-16 07:42:39,320 - ERROR - ❌ 2025/07/03 + 16:10 + 队列项处理失败 (第4次): 找不到entry记录: entry_id=145972
2025-07-16 07:42:39,321 - WARNING - ⚠️ 2025/07/03 + 16:10 + 队列项将在下次循环重试: queue_id=357, retry_count=4
2025-07-16 07:42:44,430 - ERROR - ❌ 错误日志记录失败: ************:27017: timed out (configured timeouts: socketTimeoutMS: 20000.0ms, connectTimeoutMS: 20000.0ms), Timeout: 5.0s, Topology Description: <TopologyDescription id: 687610307724503d2d237622, topology_type: Single, servers: [<ServerDescription ('************', 27017) server_type: Unknown, rtt: None, error=NetworkTimeout('************:27017: timed out (configured timeouts: socketTimeoutMS: 20000.0ms, connectTimeoutMS: 20000.0ms)')>]>
2025-07-16 07:42:44,433 - INFO - 📋 2025/07/03 + 16:10 + 处理队列项: INSERT - entry_id=145972 - queue_id=357 - 重试次数=4
2025-07-16 07:42:44,447 - ERROR - ❌ INSERT操作失败: 找不到entry记录: entry_id=145972
Traceback (most recent call last):
  File "/home/<USER>/Documents/testF/2025/ser25/server0613/MySuiteDatebase2402newuigood-yolo5-3/server5/app/services/f2_push_writer.py", line 300, in _handle_insert_operation_in_transaction
    raise ValueError(f"找不到entry记录: entry_id={entry_id}")
ValueError: 找不到entry记录: entry_id=145972
2025-07-16 07:42:44,447 - ERROR - ❌ 2025/07/03 + 16:10 + 队列项处理失败 (第5次): 找不到entry记录: entry_id=145972
2025-07-16 07:42:44,449 - WARNING - ⚠️ 2025/07/03 + 16:10 + 队列项将在下次循环重试: queue_id=357, retry_count=5
2025-07-16 07:42:49,450 - ERROR - ❌ 错误日志记录失败: ************:27017: timed out (configured timeouts: socketTimeoutMS: 20000.0ms, connectTimeoutMS: 20000.0ms), Timeout: 5.0s, Topology Description: <TopologyDescription id: 687610307724503d2d237622, topology_type: Single, servers: [<ServerDescription ('************', 27017) server_type: Unknown, rtt: None, error=NetworkTimeout('************:27017: timed out (configured timeouts: socketTimeoutMS: 20000.0ms, connectTimeoutMS: 20000.0ms)')>]>
2025-07-16 07:42:49,454 - INFO - 📋 2025/07/03 + 16:10 + 处理队列项: INSERT - entry_id=145972 - queue_id=357 - 重试次数=5
2025-07-16 07:42:49,467 - ERROR - ❌ INSERT操作失败: 找不到entry记录: entry_id=145972
Traceback (most recent call last):
  File "/home/<USER>/Documents/testF/2025/ser25/server0613/MySuiteDatebase2402newuigood-yolo5-3/server5/app/services/f2_push_writer.py", line 300, in _handle_insert_operation_in_transaction
    raise ValueError(f"找不到entry记录: entry_id={entry_id}")
ValueError: 找不到entry记录: entry_id=145972
2025-07-16 07:42:49,468 - ERROR - ❌ 2025/07/03 + 16:10 + 队列项处理失败 (第6次): 找不到entry记录: entry_id=145972
2025-07-16 07:42:49,469 - WARNING - ⚠️ 2025/07/03 + 16:10 + 队列项将在下次循环重试: queue_id=357, retry_count=6
2025-07-16 07:42:52,429 - INFO - 📋 2025/07/03 + 16:10 + 处理队列项: INSERT - entry_id=145973 - queue_id=359 - 重试次数=0
2025-07-16 07:42:52,518 - INFO - 🔄 执行INSERT操作: entry_id=145973, employee_id=215829
2025-07-16 07:42:52,518 - INFO - 🔍 调试 - 从entries表读取的原始数据: <Record entry_date=datetime.date(2025, 7, 15) id=145973 external_id=None ts=datetime.datetime(2025, 7, 15, 22, 42, 51, 493010, tzinfo=datetime.timezone.utc) employee_id='215829' model=None number=None factory_number=None project_number='TEST001' unit_number=None category=0 item=0 duration=Decimal('0.50') department='131' source='user'>
2025-07-16 07:42:52,518 - INFO - 🔍 调试 - category类型: <class 'int'>, 值: 0
2025-07-16 07:42:52,518 - INFO - 🔍 调试 - item类型: <class 'int'>, 值: 0
2025-07-16 07:42:52,518 - INFO - 🔍 调试 - 准备发送给Server6的数据: {'employee_id': '215829', 'entry_date': '2025/07/15', 'model': None, 'number': None, 'factory_number': None, 'project_number': 'TEST001', 'unit_number': None, 'category': '0', 'item': '0', 'duration': 0.5, 'department': '131'}
2025-07-16 07:42:52,518 - INFO - 📤 发送到Server6的数据: {'従業員ｺｰﾄﾞ': '215829', '日付': '2025-07-15', '機種': None, '号機': None, '工場製番': None, '工事番号': 'TEST001', 'ﾕﾆｯﾄ番号': None, '区分': '0', '項目': '0', '時間': 0.5, '所属ｺｰﾄﾞ': '131'}
2025-07-16 07:42:52,518 - INFO - 🔍 调试 - category最终值: 0, 类型: <class 'str'>
2025-07-16 07:42:52,518 - INFO - 🔍 调试 - item最终值: 0, 类型: <class 'str'>
2025-07-16 07:42:54,471 - ERROR - ❌ 错误日志记录失败: ************:27017: timed out (configured timeouts: socketTimeoutMS: 20000.0ms, connectTimeoutMS: 20000.0ms), Timeout: 5.0s, Topology Description: <TopologyDescription id: 687610307724503d2d237622, topology_type: Single, servers: [<ServerDescription ('************', 27017) server_type: Unknown, rtt: None, error=NetworkTimeout('************:27017: timed out (configured timeouts: socketTimeoutMS: 20000.0ms, connectTimeoutMS: 20000.0ms)')>]>
2025-07-16 07:42:54,476 - INFO - 📋 2025/07/03 + 16:10 + 处理队列项: INSERT - entry_id=145972 - queue_id=357 - 重试次数=6
2025-07-16 07:42:54,498 - ERROR - ❌ INSERT操作失败: 找不到entry记录: entry_id=145972
Traceback (most recent call last):
  File "/home/<USER>/Documents/testF/2025/ser25/server0613/MySuiteDatebase2402newuigood-yolo5-3/server5/app/services/f2_push_writer.py", line 300, in _handle_insert_operation_in_transaction
    raise ValueError(f"找不到entry记录: entry_id={entry_id}")
ValueError: 找不到entry记录: entry_id=145972
2025-07-16 07:42:54,499 - ERROR - ❌ 2025/07/03 + 16:10 + 队列项处理失败 (第7次): 找不到entry记录: entry_id=145972
2025-07-16 07:42:54,500 - WARNING - ⚠️ 2025/07/03 + 16:10 + 队列项将在下次循环重试: queue_id=357, retry_count=7
2025-07-16 07:42:56,445 - INFO - 📋 2025/07/03 + 16:10 + 处理队列项: UPDATE - entry_id=145973 - queue_id=360 - 重试次数=0
2025-07-16 07:42:56,445 - INFO - 📋 2025/07/03 + 16:10 + 处理队列项: DELETE - entry_id=145973 - queue_id=361 - 重试次数=0
2025-07-16 07:42:56,446 - INFO - 🔄 UPDATE操作转换为INSERT: entry_id=145973 (无external_id，记录未同步到MDB)
2025-07-16 07:42:56,447 - INFO - 🔄 DELETE操作跳过MDB删除: entry_id=145973 (无external_id，记录未同步到MDB)
2025-07-16 07:42:56,450 - INFO - ✅ DELETE跳过完成: entry_id=145973 (记录未同步到MDB)
2025-07-16 07:42:56,503 - ERROR - ❌ 性能指标记录失败: ************:27017: timed out (configured timeouts: socketTimeoutMS: 20000.0ms, connectTimeoutMS: 20000.0ms), Timeout: 5.0s, Topology Description: <TopologyDescription id: 687610307724503d2d237621, topology_type: Single, servers: [<ServerDescription ('************', 27017) server_type: Unknown, rtt: None, error=NetworkTimeout('************:27017: timed out (configured timeouts: socketTimeoutMS: 20000.0ms, connectTimeoutMS: 20000.0ms)')>]>
2025-07-16 07:42:56,623 - ERROR - ❌ INSERT操作失败: 找不到entry记录: entry_id=145973
Traceback (most recent call last):
  File "/home/<USER>/Documents/testF/2025/ser25/server0613/MySuiteDatebase2402newuigood-yolo5-3/server5/app/services/f2_push_writer.py", line 300, in _handle_insert_operation_in_transaction
    raise ValueError(f"找不到entry记录: entry_id={entry_id}")
ValueError: 找不到entry记录: entry_id=145973
2025-07-16 07:42:56,623 - ERROR - ❌ UPDATE操作失败: 找不到entry记录: entry_id=145973
Traceback (most recent call last):
  File "/home/<USER>/Documents/testF/2025/ser25/server0613/MySuiteDatebase2402newuigood-yolo5-3/server5/app/services/f2_push_writer.py", line 392, in _handle_update_operation_in_transaction
    await self._handle_insert_operation_in_transaction(conn, item, worker_id)
  File "/home/<USER>/Documents/testF/2025/ser25/server0613/MySuiteDatebase2402newuigood-yolo5-3/server5/app/services/f2_push_writer.py", line 300, in _handle_insert_operation_in_transaction
    raise ValueError(f"找不到entry记录: entry_id={entry_id}")
ValueError: 找不到entry记录: entry_id=145973
2025-07-16 07:42:56,623 - ERROR - ❌ 2025/07/03 + 16:10 + 队列项处理失败 (第1次): 找不到entry记录: entry_id=145973
2025-07-16 07:42:56,627 - WARNING - ⚠️ 2025/07/03 + 16:10 + 队列项将在下次循环重试: queue_id=360, retry_count=1
2025-07-16 07:42:57,157 - INFO - ✅ INSERT完成: entry_id=145973 -> external_id=603744
2025-07-16 07:42:59,502 - ERROR - ❌ 错误日志记录失败: ************:27017: timed out (configured timeouts: socketTimeoutMS: 20000.0ms, connectTimeoutMS: 20000.0ms), Timeout: 5.0s, Topology Description: <TopologyDescription id: 687610307724503d2d237622, topology_type: Single, servers: [<ServerDescription ('************', 27017) server_type: Unknown, rtt: None, error=NetworkTimeout('************:27017: timed out (configured timeouts: socketTimeoutMS: 20000.0ms, connectTimeoutMS: 20000.0ms)')>]>
2025-07-16 07:42:59,506 - INFO - 📋 2025/07/03 + 16:10 + 处理队列项: INSERT - entry_id=145972 - queue_id=357 - 重试次数=7
2025-07-16 07:42:59,520 - ERROR - ❌ INSERT操作失败: 找不到entry记录: entry_id=145972
Traceback (most recent call last):
  File "/home/<USER>/Documents/testF/2025/ser25/server0613/MySuiteDatebase2402newuigood-yolo5-3/server5/app/services/f2_push_writer.py", line 300, in _handle_insert_operation_in_transaction
    raise ValueError(f"找不到entry记录: entry_id={entry_id}")
ValueError: 找不到entry记录: entry_id=145972
2025-07-16 07:42:59,520 - ERROR - ❌ 2025/07/03 + 16:10 + 队列项处理失败 (第8次): 找不到entry记录: entry_id=145972
2025-07-16 07:42:59,521 - WARNING - ⚠️ 2025/07/03 + 16:10 + 队列项将在下次循环重试: queue_id=357, retry_count=8
2025-07-16 07:42:59,768 - ERROR - ❌ 性能指标记录失败: ************:27017: timed out (configured timeouts: socketTimeoutMS: 20000.0ms, connectTimeoutMS: 20000.0ms), Timeout: 5.0s, Topology Description: <TopologyDescription id: 687610307724503d2d237621, topology_type: Single, servers: [<ServerDescription ('************', 27017) server_type: Unknown, rtt: None, error=NetworkTimeout('************:27017: timed out (configured timeouts: socketTimeoutMS: 20000.0ms, connectTimeoutMS: 20000.0ms)')>]>
2025-07-16 07:42:59,806 - ERROR - ❌ 性能指标记录失败: ************:27017: timed out (configured timeouts: socketTimeoutMS: 20000.0ms, connectTimeoutMS: 20000.0ms), Timeout: 5.0s, Topology Description: <TopologyDescription id: 687610307724503d2d237621, topology_type: Single, servers: [<ServerDescription ('************', 27017) server_type: Unknown, rtt: None, error=NetworkTimeout('************:27017: timed out (configured timeouts: socketTimeoutMS: 20000.0ms, connectTimeoutMS: 20000.0ms)')>]>
2025-07-16 07:43:01,452 - ERROR - ❌ 同步日志记录失败: ************:27017: timed out (configured timeouts: socketTimeoutMS: 20000.0ms, connectTimeoutMS: 20000.0ms), Timeout: 5.0s, Topology Description: <TopologyDescription id: 687610307724503d2d237622, topology_type: Single, servers: [<ServerDescription ('************', 27017) server_type: Unknown, rtt: None, error=NetworkTimeout('************:27017: timed out (configured timeouts: socketTimeoutMS: 20000.0ms, connectTimeoutMS: 20000.0ms)')>]>
2025-07-16 07:43:01,629 - ERROR - ❌ 错误日志记录失败: ************:27017: timed out (configured timeouts: socketTimeoutMS: 20000.0ms, connectTimeoutMS: 20000.0ms), Timeout: 5.0s, Topology Description: <TopologyDescription id: 687610307724503d2d237622, topology_type: Single, servers: [<ServerDescription ('************', 27017) server_type: Unknown, rtt: None, error=NetworkTimeout('************:27017: timed out (configured timeouts: socketTimeoutMS: 20000.0ms, connectTimeoutMS: 20000.0ms)')>]>
2025-07-16 07:43:01,633 - INFO - 📋 2025/07/03 + 16:10 + 处理队列项: UPDATE - entry_id=145973 - queue_id=360 - 重试次数=1
2025-07-16 07:43:01,634 - INFO - 🔄 UPDATE操作转换为INSERT: entry_id=145973 (无external_id，记录未同步到MDB)
2025-07-16 07:43:01,648 - ERROR - ❌ INSERT操作失败: 找不到entry记录: entry_id=145973
Traceback (most recent call last):
  File "/home/<USER>/Documents/testF/2025/ser25/server0613/MySuiteDatebase2402newuigood-yolo5-3/server5/app/services/f2_push_writer.py", line 300, in _handle_insert_operation_in_transaction
    raise ValueError(f"找不到entry记录: entry_id={entry_id}")
ValueError: 找不到entry记录: entry_id=145973
2025-07-16 07:43:01,648 - ERROR - ❌ UPDATE操作失败: 找不到entry记录: entry_id=145973
Traceback (most recent call last):
  File "/home/<USER>/Documents/testF/2025/ser25/server0613/MySuiteDatebase2402newuigood-yolo5-3/server5/app/services/f2_push_writer.py", line 392, in _handle_update_operation_in_transaction
    await self._handle_insert_operation_in_transaction(conn, item, worker_id)
  File "/home/<USER>/Documents/testF/2025/ser25/server0613/MySuiteDatebase2402newuigood-yolo5-3/server5/app/services/f2_push_writer.py", line 300, in _handle_insert_operation_in_transaction
    raise ValueError(f"找不到entry记录: entry_id={entry_id}")
ValueError: 找不到entry记录: entry_id=145973
2025-07-16 07:43:01,648 - ERROR - ❌ 2025/07/03 + 16:10 + 队列项处理失败 (第2次): 找不到entry记录: entry_id=145973
2025-07-16 07:43:01,649 - WARNING - ⚠️ 2025/07/03 + 16:10 + 队列项将在下次循环重试: queue_id=360, retry_count=2
2025-07-16 07:43:02,159 - ERROR - ❌ 同步日志记录失败: ************:27017: timed out (configured timeouts: socketTimeoutMS: 20000.0ms, connectTimeoutMS: 20000.0ms), Timeout: 5.0s, Topology Description: <TopologyDescription id: 687610307724503d2d237622, topology_type: Single, servers: [<ServerDescription ('************', 27017) server_type: Unknown, rtt: None, error=NetworkTimeout('************:27017: timed out (configured timeouts: socketTimeoutMS: 20000.0ms, connectTimeoutMS: 20000.0ms)')>]>
2025-07-16 07:43:04,950 - ERROR - ❌ 错误日志记录失败: ************:27017: timed out (configured timeouts: socketTimeoutMS: 20000.0ms, connectTimeoutMS: 20000.0ms), Timeout: 5.0s, Topology Description: <TopologyDescription id: 687610307724503d2d237622, topology_type: Single, servers: [<ServerDescription ('************', 27017) server_type: Unknown, rtt: None, error=NetworkTimeout('************:27017: timed out (configured timeouts: socketTimeoutMS: 20000.0ms, connectTimeoutMS: 20000.0ms)')>]>
2025-07-16 07:43:04,954 - INFO - 📋 2025/07/03 + 16:10 + 处理队列项: INSERT - entry_id=145972 - queue_id=357 - 重试次数=8
2025-07-16 07:43:04,965 - ERROR - ❌ INSERT操作失败: 找不到entry记录: entry_id=145972
Traceback (most recent call last):
  File "/home/<USER>/Documents/testF/2025/ser25/server0613/MySuiteDatebase2402newuigood-yolo5-3/server5/app/services/f2_push_writer.py", line 300, in _handle_insert_operation_in_transaction
    raise ValueError(f"找不到entry记录: entry_id={entry_id}")
ValueError: 找不到entry记录: entry_id=145972
2025-07-16 07:43:04,965 - ERROR - ❌ 2025/07/03 + 16:10 + 队列项处理失败 (第9次): 找不到entry记录: entry_id=145972
2025-07-16 07:43:04,966 - WARNING - ⚠️ 2025/07/03 + 16:10 + 队列项将在下次循环重试: queue_id=357, retry_count=9
2025-07-16 07:43:06,950 - ERROR - ❌ 错误日志记录失败: ************:27017: timed out (configured timeouts: socketTimeoutMS: 20000.0ms, connectTimeoutMS: 20000.0ms), Timeout: 5.0s, Topology Description: <TopologyDescription id: 687610307724503d2d237622, topology_type: Single, servers: [<ServerDescription ('************', 27017) server_type: Unknown, rtt: None, error=NetworkTimeout('************:27017: timed out (configured timeouts: socketTimeoutMS: 20000.0ms, connectTimeoutMS: 20000.0ms)')>]>
2025-07-16 07:43:06,950 - ERROR - ❌ 性能指标记录失败: ************:27017: timed out (configured timeouts: socketTimeoutMS: 20000.0ms, connectTimeoutMS: 20000.0ms), Timeout: 5.0s, Topology Description: <TopologyDescription id: 687610307724503d2d237622, topology_type: Single, servers: [<ServerDescription ('************', 27017) server_type: Unknown, rtt: None, error=NetworkTimeout('************:27017: timed out (configured timeouts: socketTimeoutMS: 20000.0ms, connectTimeoutMS: 20000.0ms)')>]>
2025-07-16 07:43:06,954 - INFO - 📋 2025/07/03 + 16:10 + 处理队列项: UPDATE - entry_id=145973 - queue_id=360 - 重试次数=2
2025-07-16 07:43:06,955 - INFO - 🔄 UPDATE操作转换为INSERT: entry_id=145973 (无external_id，记录未同步到MDB)
2025-07-16 07:43:06,972 - ERROR - ❌ INSERT操作失败: 找不到entry记录: entry_id=145973
Traceback (most recent call last):
  File "/home/<USER>/Documents/testF/2025/ser25/server0613/MySuiteDatebase2402newuigood-yolo5-3/server5/app/services/f2_push_writer.py", line 300, in _handle_insert_operation_in_transaction
    raise ValueError(f"找不到entry记录: entry_id={entry_id}")
ValueError: 找不到entry记录: entry_id=145973
2025-07-16 07:43:06,972 - ERROR - ❌ UPDATE操作失败: 找不到entry记录: entry_id=145973
Traceback (most recent call last):
  File "/home/<USER>/Documents/testF/2025/ser25/server0613/MySuiteDatebase2402newuigood-yolo5-3/server5/app/services/f2_push_writer.py", line 392, in _handle_update_operation_in_transaction
    await self._handle_insert_operation_in_transaction(conn, item, worker_id)
  File "/home/<USER>/Documents/testF/2025/ser25/server0613/MySuiteDatebase2402newuigood-yolo5-3/server5/app/services/f2_push_writer.py", line 300, in _handle_insert_operation_in_transaction
    raise ValueError(f"找不到entry记录: entry_id={entry_id}")
ValueError: 找不到entry记录: entry_id=145973
2025-07-16 07:43:06,973 - ERROR - ❌ 2025/07/03 + 16:10 + 队列项处理失败 (第3次): 找不到entry记录: entry_id=145973
2025-07-16 07:43:06,974 - WARNING - ⚠️ 2025/07/03 + 16:10 + 队列项将在下次循环重试: queue_id=360, retry_count=3
2025-07-16 07:43:07,450 - ERROR - ❌ 性能指标记录失败: ************:27017: timed out (configured timeouts: socketTimeoutMS: 20000.0ms, connectTimeoutMS: 20000.0ms), Timeout: 5.0s, Topology Description: <TopologyDescription id: 687610307724503d2d237622, topology_type: Single, servers: [<ServerDescription ('************', 27017) server_type: Unknown, rtt: None, error=NetworkTimeout('************:27017: timed out (configured timeouts: socketTimeoutMS: 20000.0ms, connectTimeoutMS: 20000.0ms)')>]>
2025-07-16 07:43:09,968 - ERROR - ❌ 错误日志记录失败: ************:27017: timed out (configured timeouts: socketTimeoutMS: 20000.0ms, connectTimeoutMS: 20000.0ms), Timeout: 5.0s, Topology Description: <TopologyDescription id: 687610307724503d2d237622, topology_type: Single, servers: [<ServerDescription ('************', 27017) server_type: Unknown, rtt: None, error=NetworkTimeout('************:27017: timed out (configured timeouts: socketTimeoutMS: 20000.0ms, connectTimeoutMS: 20000.0ms)')>]>
2025-07-16 07:43:09,972 - INFO - 📋 2025/07/03 + 16:10 + 处理队列项: INSERT - entry_id=145972 - queue_id=357 - 重试次数=9
2025-07-16 07:43:09,995 - ERROR - ❌ INSERT操作失败: 找不到entry记录: entry_id=145972
Traceback (most recent call last):
  File "/home/<USER>/Documents/testF/2025/ser25/server0613/MySuiteDatebase2402newuigood-yolo5-3/server5/app/services/f2_push_writer.py", line 300, in _handle_insert_operation_in_transaction
    raise ValueError(f"找不到entry记录: entry_id={entry_id}")
ValueError: 找不到entry记录: entry_id=145972
2025-07-16 07:43:09,995 - ERROR - ❌ 2025/07/03 + 16:10 + 队列项处理失败 (第10次): 找不到entry记录: entry_id=145972
2025-07-16 07:43:09,995 - ERROR - 🚨 队列项超过最大重试次数，执行清理: queue_id=357, entry_id=145972
2025-07-16 07:43:11,975 - ERROR - ❌ 错误日志记录失败: ************:27017: timed out (configured timeouts: socketTimeoutMS: 20000.0ms, connectTimeoutMS: 20000.0ms), Timeout: 5.0s, Topology Description: <TopologyDescription id: 687610307724503d2d237622, topology_type: Single, servers: [<ServerDescription ('************', 27017) server_type: Unknown, rtt: None, error=NetworkTimeout('************:27017: timed out (configured timeouts: socketTimeoutMS: 20000.0ms, connectTimeoutMS: 20000.0ms)')>]>
2025-07-16 07:43:11,979 - INFO - 📋 2025/07/03 + 16:10 + 处理队列项: UPDATE - entry_id=145973 - queue_id=360 - 重试次数=3
2025-07-16 07:43:11,980 - INFO - 🔄 UPDATE操作转换为INSERT: entry_id=145973 (无external_id，记录未同步到MDB)
2025-07-16 07:43:11,996 - ERROR - ❌ INSERT操作失败: 找不到entry记录: entry_id=145973
Traceback (most recent call last):
  File "/home/<USER>/Documents/testF/2025/ser25/server0613/MySuiteDatebase2402newuigood-yolo5-3/server5/app/services/f2_push_writer.py", line 300, in _handle_insert_operation_in_transaction
    raise ValueError(f"找不到entry记录: entry_id={entry_id}")
ValueError: 找不到entry记录: entry_id=145973
2025-07-16 07:43:11,996 - ERROR - ❌ UPDATE操作失败: 找不到entry记录: entry_id=145973
Traceback (most recent call last):
  File "/home/<USER>/Documents/testF/2025/ser25/server0613/MySuiteDatebase2402newuigood-yolo5-3/server5/app/services/f2_push_writer.py", line 392, in _handle_update_operation_in_transaction
    await self._handle_insert_operation_in_transaction(conn, item, worker_id)
  File "/home/<USER>/Documents/testF/2025/ser25/server0613/MySuiteDatebase2402newuigood-yolo5-3/server5/app/services/f2_push_writer.py", line 300, in _handle_insert_operation_in_transaction
    raise ValueError(f"找不到entry记录: entry_id={entry_id}")
ValueError: 找不到entry记录: entry_id=145973
2025-07-16 07:43:11,996 - ERROR - ❌ 2025/07/03 + 16:10 + 队列项处理失败 (第4次): 找不到entry记录: entry_id=145973
2025-07-16 07:43:11,997 - WARNING - ⚠️ 2025/07/03 + 16:10 + 队列项将在下次循环重试: queue_id=360, retry_count=4
2025-07-16 07:43:15,023 - ERROR - ❌ 同步日志记录失败: ************:27017: timed out (configured timeouts: socketTimeoutMS: 20000.0ms, connectTimeoutMS: 20000.0ms), Timeout: 5.0s, Topology Description: <TopologyDescription id: 687610307724503d2d237622, topology_type: Single, servers: [<ServerDescription ('************', 27017) server_type: Unknown, rtt: None, error=NetworkTimeout('************:27017: timed out (configured timeouts: socketTimeoutMS: 20000.0ms, connectTimeoutMS: 20000.0ms)')>]>
2025-07-16 07:43:15,023 - WARNING - ✅ 队列项清理完成: queue_id=357
2025-07-16 07:43:16,999 - ERROR - ❌ 错误日志记录失败: ************:27017: timed out (configured timeouts: socketTimeoutMS: 20000.0ms, connectTimeoutMS: 20000.0ms), Timeout: 5.0s, Topology Description: <TopologyDescription id: 687610307724503d2d237622, topology_type: Single, servers: [<ServerDescription ('************', 27017) server_type: Unknown, rtt: None, error=NetworkTimeout('************:27017: timed out (configured timeouts: socketTimeoutMS: 20000.0ms, connectTimeoutMS: 20000.0ms)')>]>
2025-07-16 07:43:17,002 - INFO - 📋 2025/07/03 + 16:10 + 处理队列项: UPDATE - entry_id=145973 - queue_id=360 - 重试次数=4
2025-07-16 07:43:17,003 - INFO - 🔄 UPDATE操作转换为INSERT: entry_id=145973 (无external_id，记录未同步到MDB)
2025-07-16 07:43:17,017 - ERROR - ❌ INSERT操作失败: 找不到entry记录: entry_id=145973
Traceback (most recent call last):
  File "/home/<USER>/Documents/testF/2025/ser25/server0613/MySuiteDatebase2402newuigood-yolo5-3/server5/app/services/f2_push_writer.py", line 300, in _handle_insert_operation_in_transaction
    raise ValueError(f"找不到entry记录: entry_id={entry_id}")
ValueError: 找不到entry记录: entry_id=145973
2025-07-16 07:43:17,018 - ERROR - ❌ UPDATE操作失败: 找不到entry记录: entry_id=145973
Traceback (most recent call last):
  File "/home/<USER>/Documents/testF/2025/ser25/server0613/MySuiteDatebase2402newuigood-yolo5-3/server5/app/services/f2_push_writer.py", line 392, in _handle_update_operation_in_transaction
    await self._handle_insert_operation_in_transaction(conn, item, worker_id)
  File "/home/<USER>/Documents/testF/2025/ser25/server0613/MySuiteDatebase2402newuigood-yolo5-3/server5/app/services/f2_push_writer.py", line 300, in _handle_insert_operation_in_transaction
    raise ValueError(f"找不到entry记录: entry_id={entry_id}")
ValueError: 找不到entry记录: entry_id=145973
2025-07-16 07:43:17,018 - ERROR - ❌ 2025/07/03 + 16:10 + 队列项处理失败 (第5次): 找不到entry记录: entry_id=145973
2025-07-16 07:43:17,019 - WARNING - ⚠️ 2025/07/03 + 16:10 + 队列项将在下次循环重试: queue_id=360, retry_count=5
2025-07-16 07:43:20,025 - ERROR - ❌ 错误日志记录失败: ************:27017: timed out (configured timeouts: socketTimeoutMS: 20000.0ms, connectTimeoutMS: 20000.0ms), Timeout: 5.0s, Topology Description: <TopologyDescription id: 687610307724503d2d237622, topology_type: Single, servers: [<ServerDescription ('************', 27017) server_type: Unknown, rtt: None, error=NetworkTimeout('************:27017: timed out (configured timeouts: socketTimeoutMS: 20000.0ms, connectTimeoutMS: 20000.0ms)')>]>
2025-07-16 07:43:22,021 - ERROR - ❌ 错误日志记录失败: ************:27017: timed out (configured timeouts: socketTimeoutMS: 20000.0ms, connectTimeoutMS: 20000.0ms), Timeout: 5.0s, Topology Description: <TopologyDescription id: 687610307724503d2d237622, topology_type: Single, servers: [<ServerDescription ('************', 27017) server_type: Unknown, rtt: None, error=NetworkTimeout('************:27017: timed out (configured timeouts: socketTimeoutMS: 20000.0ms, connectTimeoutMS: 20000.0ms)')>]>
2025-07-16 07:43:22,025 - INFO - 📋 2025/07/03 + 16:10 + 处理队列项: UPDATE - entry_id=145973 - queue_id=360 - 重试次数=5
2025-07-16 07:43:22,025 - INFO - 🔄 UPDATE操作转换为INSERT: entry_id=145973 (无external_id，记录未同步到MDB)
2025-07-16 07:43:22,044 - ERROR - ❌ INSERT操作失败: 找不到entry记录: entry_id=145973
Traceback (most recent call last):
  File "/home/<USER>/Documents/testF/2025/ser25/server0613/MySuiteDatebase2402newuigood-yolo5-3/server5/app/services/f2_push_writer.py", line 300, in _handle_insert_operation_in_transaction
    raise ValueError(f"找不到entry记录: entry_id={entry_id}")
ValueError: 找不到entry记录: entry_id=145973
2025-07-16 07:43:22,044 - ERROR - ❌ UPDATE操作失败: 找不到entry记录: entry_id=145973
Traceback (most recent call last):
  File "/home/<USER>/Documents/testF/2025/ser25/server0613/MySuiteDatebase2402newuigood-yolo5-3/server5/app/services/f2_push_writer.py", line 392, in _handle_update_operation_in_transaction
    await self._handle_insert_operation_in_transaction(conn, item, worker_id)
  File "/home/<USER>/Documents/testF/2025/ser25/server0613/MySuiteDatebase2402newuigood-yolo5-3/server5/app/services/f2_push_writer.py", line 300, in _handle_insert_operation_in_transaction
    raise ValueError(f"找不到entry记录: entry_id={entry_id}")
ValueError: 找不到entry记录: entry_id=145973
2025-07-16 07:43:22,044 - ERROR - ❌ 2025/07/03 + 16:10 + 队列项处理失败 (第6次): 找不到entry记录: entry_id=145973
2025-07-16 07:43:22,046 - WARNING - ⚠️ 2025/07/03 + 16:10 + 队列项将在下次循环重试: queue_id=360, retry_count=6
2025-07-16 07:43:27,470 - ERROR - ❌ 错误日志记录失败: ************:27017: timed out (configured timeouts: socketTimeoutMS: 20000.0ms, connectTimeoutMS: 20000.0ms), Timeout: 5.0s, Topology Description: <TopologyDescription id: 687610307724503d2d237622, topology_type: Single, servers: [<ServerDescription ('************', 27017) server_type: Unknown, rtt: None, error=NetworkTimeout('************:27017: timed out (configured timeouts: socketTimeoutMS: 20000.0ms, connectTimeoutMS: 20000.0ms)')>]>
2025-07-16 07:43:27,474 - INFO - 📋 2025/07/03 + 16:10 + 处理队列项: UPDATE - entry_id=145973 - queue_id=360 - 重试次数=6
2025-07-16 07:43:27,474 - INFO - 🔄 UPDATE操作转换为INSERT: entry_id=145973 (无external_id，记录未同步到MDB)
2025-07-16 07:43:27,487 - ERROR - ❌ INSERT操作失败: 找不到entry记录: entry_id=145973
Traceback (most recent call last):
  File "/home/<USER>/Documents/testF/2025/ser25/server0613/MySuiteDatebase2402newuigood-yolo5-3/server5/app/services/f2_push_writer.py", line 300, in _handle_insert_operation_in_transaction
    raise ValueError(f"找不到entry记录: entry_id={entry_id}")
ValueError: 找不到entry记录: entry_id=145973
2025-07-16 07:43:27,488 - ERROR - ❌ UPDATE操作失败: 找不到entry记录: entry_id=145973
Traceback (most recent call last):
  File "/home/<USER>/Documents/testF/2025/ser25/server0613/MySuiteDatebase2402newuigood-yolo5-3/server5/app/services/f2_push_writer.py", line 392, in _handle_update_operation_in_transaction
    await self._handle_insert_operation_in_transaction(conn, item, worker_id)
  File "/home/<USER>/Documents/testF/2025/ser25/server0613/MySuiteDatebase2402newuigood-yolo5-3/server5/app/services/f2_push_writer.py", line 300, in _handle_insert_operation_in_transaction
    raise ValueError(f"找不到entry记录: entry_id={entry_id}")
ValueError: 找不到entry记录: entry_id=145973
2025-07-16 07:43:27,488 - ERROR - ❌ 2025/07/03 + 16:10 + 队列项处理失败 (第7次): 找不到entry记录: entry_id=145973
2025-07-16 07:43:27,489 - WARNING - ⚠️ 2025/07/03 + 16:10 + 队列项将在下次循环重试: queue_id=360, retry_count=7
2025-07-16 07:43:32,490 - ERROR - ❌ 错误日志记录失败: ************:27017: timed out (configured timeouts: socketTimeoutMS: 20000.0ms, connectTimeoutMS: 20000.0ms), Timeout: 5.0s, Topology Description: <TopologyDescription id: 687610307724503d2d237622, topology_type: Single, servers: [<ServerDescription ('************', 27017) server_type: Unknown, rtt: None, error=NetworkTimeout('************:27017: timed out (configured timeouts: socketTimeoutMS: 20000.0ms, connectTimeoutMS: 20000.0ms)')>]>
2025-07-16 07:43:32,494 - INFO - 📋 2025/07/03 + 16:10 + 处理队列项: UPDATE - entry_id=145973 - queue_id=360 - 重试次数=7
2025-07-16 07:43:32,495 - INFO - 🔄 UPDATE操作转换为INSERT: entry_id=145973 (无external_id，记录未同步到MDB)
2025-07-16 07:43:32,507 - ERROR - ❌ INSERT操作失败: 找不到entry记录: entry_id=145973
Traceback (most recent call last):
  File "/home/<USER>/Documents/testF/2025/ser25/server0613/MySuiteDatebase2402newuigood-yolo5-3/server5/app/services/f2_push_writer.py", line 300, in _handle_insert_operation_in_transaction
    raise ValueError(f"找不到entry记录: entry_id={entry_id}")
ValueError: 找不到entry记录: entry_id=145973
2025-07-16 07:43:32,507 - ERROR - ❌ UPDATE操作失败: 找不到entry记录: entry_id=145973
Traceback (most recent call last):
  File "/home/<USER>/Documents/testF/2025/ser25/server0613/MySuiteDatebase2402newuigood-yolo5-3/server5/app/services/f2_push_writer.py", line 392, in _handle_update_operation_in_transaction
    await self._handle_insert_operation_in_transaction(conn, item, worker_id)
  File "/home/<USER>/Documents/testF/2025/ser25/server0613/MySuiteDatebase2402newuigood-yolo5-3/server5/app/services/f2_push_writer.py", line 300, in _handle_insert_operation_in_transaction
    raise ValueError(f"找不到entry记录: entry_id={entry_id}")
ValueError: 找不到entry记录: entry_id=145973
2025-07-16 07:43:32,507 - ERROR - ❌ 2025/07/03 + 16:10 + 队列项处理失败 (第8次): 找不到entry记录: entry_id=145973
2025-07-16 07:43:32,508 - WARNING - ⚠️ 2025/07/03 + 16:10 + 队列项将在下次循环重试: queue_id=360, retry_count=8
2025-07-16 07:43:36,060 - INFO - 📋 2025/07/03 + 16:10 + 处理队列项: INSERT - entry_id=145974 - queue_id=362 - 重试次数=0
2025-07-16 07:43:36,072 - INFO - 🔄 执行INSERT操作: entry_id=145974, employee_id=215829
2025-07-16 07:43:36,072 - INFO - 🔍 调试 - 从entries表读取的原始数据: <Record entry_date=datetime.date(2025, 7, 15) id=145974 external_id=None ts=datetime.datetime(2025, 7, 15, 22, 43, 35, 664246, tzinfo=datetime.timezone.utc) employee_id='215829' model=None number=None factory_number=None project_number='TEST001' unit_number=None category=0 item=0 duration=Decimal('0.50') department='131' source='user'>
2025-07-16 07:43:36,072 - INFO - 🔍 调试 - category类型: <class 'int'>, 值: 0
2025-07-16 07:43:36,072 - INFO - 🔍 调试 - item类型: <class 'int'>, 值: 0
2025-07-16 07:43:36,072 - INFO - 🔍 调试 - 准备发送给Server6的数据: {'employee_id': '215829', 'entry_date': '2025/07/15', 'model': None, 'number': None, 'factory_number': None, 'project_number': 'TEST001', 'unit_number': None, 'category': '0', 'item': '0', 'duration': 0.5, 'department': '131'}
2025-07-16 07:43:36,073 - INFO - 📤 发送到Server6的数据: {'従業員ｺｰﾄﾞ': '215829', '日付': '2025-07-15', '機種': None, '号機': None, '工場製番': None, '工事番号': 'TEST001', 'ﾕﾆｯﾄ番号': None, '区分': '0', '項目': '0', '時間': 0.5, '所属ｺｰﾄﾞ': '131'}
2025-07-16 07:43:36,073 - INFO - 🔍 调试 - category最终值: 0, 类型: <class 'str'>
2025-07-16 07:43:36,073 - INFO - 🔍 调试 - item最终值: 0, 类型: <class 'str'>
2025-07-16 07:43:37,510 - ERROR - ❌ 错误日志记录失败: ************:27017: timed out (configured timeouts: socketTimeoutMS: 20000.0ms, connectTimeoutMS: 20000.0ms), Timeout: 5.0s, Topology Description: <TopologyDescription id: 687610307724503d2d237622, topology_type: Single, servers: [<ServerDescription ('************', 27017) server_type: Unknown, rtt: None, error=NetworkTimeout('************:27017: timed out (configured timeouts: socketTimeoutMS: 20000.0ms, connectTimeoutMS: 20000.0ms)')>]>
2025-07-16 07:43:37,513 - INFO - 📋 2025/07/03 + 16:10 + 处理队列项: UPDATE - entry_id=145973 - queue_id=360 - 重试次数=8
2025-07-16 07:43:37,514 - INFO - 🔄 UPDATE操作转换为INSERT: entry_id=145973 (无external_id，记录未同步到MDB)
2025-07-16 07:43:37,533 - ERROR - ❌ INSERT操作失败: 找不到entry记录: entry_id=145973
Traceback (most recent call last):
  File "/home/<USER>/Documents/testF/2025/ser25/server0613/MySuiteDatebase2402newuigood-yolo5-3/server5/app/services/f2_push_writer.py", line 300, in _handle_insert_operation_in_transaction
    raise ValueError(f"找不到entry记录: entry_id={entry_id}")
ValueError: 找不到entry记录: entry_id=145973
2025-07-16 07:43:37,533 - ERROR - ❌ UPDATE操作失败: 找不到entry记录: entry_id=145973
Traceback (most recent call last):
  File "/home/<USER>/Documents/testF/2025/ser25/server0613/MySuiteDatebase2402newuigood-yolo5-3/server5/app/services/f2_push_writer.py", line 392, in _handle_update_operation_in_transaction
    await self._handle_insert_operation_in_transaction(conn, item, worker_id)
  File "/home/<USER>/Documents/testF/2025/ser25/server0613/MySuiteDatebase2402newuigood-yolo5-3/server5/app/services/f2_push_writer.py", line 300, in _handle_insert_operation_in_transaction
    raise ValueError(f"找不到entry记录: entry_id={entry_id}")
ValueError: 找不到entry记录: entry_id=145973
2025-07-16 07:43:37,533 - ERROR - ❌ 2025/07/03 + 16:10 + 队列项处理失败 (第9次): 找不到entry记录: entry_id=145973
2025-07-16 07:43:37,535 - WARNING - ⚠️ 2025/07/03 + 16:10 + 队列项将在下次循环重试: queue_id=360, retry_count=9
2025-07-16 07:43:39,009 - INFO - 📋 2025/07/03 + 16:10 + 处理队列项: UPDATE - entry_id=145974 - queue_id=363 - 重试次数=0
2025-07-16 07:43:39,010 - INFO - 🔄 UPDATE操作转换为INSERT: entry_id=145974 (无external_id，记录未同步到MDB)
2025-07-16 07:43:39,092 - ERROR - ❌ INSERT操作失败: 找不到entry记录: entry_id=145974
Traceback (most recent call last):
  File "/home/<USER>/Documents/testF/2025/ser25/server0613/MySuiteDatebase2402newuigood-yolo5-3/server5/app/services/f2_push_writer.py", line 300, in _handle_insert_operation_in_transaction
    raise ValueError(f"找不到entry记录: entry_id={entry_id}")
ValueError: 找不到entry记录: entry_id=145974
2025-07-16 07:43:39,093 - ERROR - ❌ UPDATE操作失败: 找不到entry记录: entry_id=145974
Traceback (most recent call last):
  File "/home/<USER>/Documents/testF/2025/ser25/server0613/MySuiteDatebase2402newuigood-yolo5-3/server5/app/services/f2_push_writer.py", line 392, in _handle_update_operation_in_transaction
    await self._handle_insert_operation_in_transaction(conn, item, worker_id)
  File "/home/<USER>/Documents/testF/2025/ser25/server0613/MySuiteDatebase2402newuigood-yolo5-3/server5/app/services/f2_push_writer.py", line 300, in _handle_insert_operation_in_transaction
    raise ValueError(f"找不到entry记录: entry_id={entry_id}")
ValueError: 找不到entry记录: entry_id=145974
2025-07-16 07:43:39,093 - ERROR - ❌ 2025/07/03 + 16:10 + 队列项处理失败 (第1次): 找不到entry记录: entry_id=145974
2025-07-16 07:43:39,094 - WARNING - ⚠️ 2025/07/03 + 16:10 + 队列项将在下次循环重试: queue_id=363, retry_count=1
2025-07-16 07:43:39,518 - INFO - 📋 2025/07/03 + 16:10 + 处理队列项: DELETE - entry_id=145974 - queue_id=364 - 重试次数=0
2025-07-16 07:43:39,519 - INFO - 🔄 DELETE操作跳过MDB删除: entry_id=145974 (无external_id，记录未同步到MDB)
2025-07-16 07:43:39,520 - INFO - ✅ DELETE跳过完成: entry_id=145974 (记录未同步到MDB)
2025-07-16 07:43:39,784 - INFO - ✅ INSERT完成: entry_id=145974 -> external_id=603745
2025-07-16 07:43:40,675 - ERROR - ❌ 性能指标记录失败: ************:27017: timed out (configured timeouts: socketTimeoutMS: 20000.0ms, connectTimeoutMS: 20000.0ms), Timeout: 5.0s, Topology Description: <TopologyDescription id: 687610307724503d2d237621, topology_type: Single, servers: [<ServerDescription ('************', 27017) server_type: Unknown, rtt: None, error=NetworkTimeout('************:27017: timed out (configured timeouts: socketTimeoutMS: 20000.0ms, connectTimeoutMS: 20000.0ms)')>]>
2025-07-16 07:43:42,536 - ERROR - ❌ 错误日志记录失败: ************:27017: timed out (configured timeouts: socketTimeoutMS: 20000.0ms, connectTimeoutMS: 20000.0ms), Timeout: 5.0s, Topology Description: <TopologyDescription id: 687610307724503d2d237622, topology_type: Single, servers: [<ServerDescription ('************', 27017) server_type: Unknown, rtt: None, error=NetworkTimeout('************:27017: timed out (configured timeouts: socketTimeoutMS: 20000.0ms, connectTimeoutMS: 20000.0ms)')>]>
2025-07-16 07:43:42,540 - INFO - 📋 2025/07/03 + 16:10 + 处理队列项: UPDATE - entry_id=145973 - queue_id=360 - 重试次数=9
2025-07-16 07:43:42,541 - INFO - 🔄 UPDATE操作转换为INSERT: entry_id=145973 (无external_id，记录未同步到MDB)
2025-07-16 07:43:42,555 - ERROR - ❌ INSERT操作失败: 找不到entry记录: entry_id=145973
Traceback (most recent call last):
  File "/home/<USER>/Documents/testF/2025/ser25/server0613/MySuiteDatebase2402newuigood-yolo5-3/server5/app/services/f2_push_writer.py", line 300, in _handle_insert_operation_in_transaction
    raise ValueError(f"找不到entry记录: entry_id={entry_id}")
ValueError: 找不到entry记录: entry_id=145973
2025-07-16 07:43:42,555 - ERROR - ❌ UPDATE操作失败: 找不到entry记录: entry_id=145973
Traceback (most recent call last):
  File "/home/<USER>/Documents/testF/2025/ser25/server0613/MySuiteDatebase2402newuigood-yolo5-3/server5/app/services/f2_push_writer.py", line 392, in _handle_update_operation_in_transaction
    await self._handle_insert_operation_in_transaction(conn, item, worker_id)
  File "/home/<USER>/Documents/testF/2025/ser25/server0613/MySuiteDatebase2402newuigood-yolo5-3/server5/app/services/f2_push_writer.py", line 300, in _handle_insert_operation_in_transaction
    raise ValueError(f"找不到entry记录: entry_id={entry_id}")
ValueError: 找不到entry记录: entry_id=145973
2025-07-16 07:43:42,555 - ERROR - ❌ 2025/07/03 + 16:10 + 队列项处理失败 (第10次): 找不到entry记录: entry_id=145973
2025-07-16 07:43:42,555 - ERROR - 🚨 队列项超过最大重试次数，执行清理: queue_id=360, entry_id=145973
2025-07-16 07:43:43,843 - ERROR - ❌ 性能指标记录失败: ************:27017: timed out (configured timeouts: socketTimeoutMS: 20000.0ms, connectTimeoutMS: 20000.0ms), Timeout: 5.0s, Topology Description: <TopologyDescription id: 687610307724503d2d237621, topology_type: Single, servers: [<ServerDescription ('************', 27017) server_type: Unknown, rtt: None, error=NetworkTimeout('************:27017: timed out (configured timeouts: socketTimeoutMS: 20000.0ms, connectTimeoutMS: 20000.0ms)')>]>
2025-07-16 07:43:43,894 - ERROR - ❌ 性能指标记录失败: ************:27017: timed out (configured timeouts: socketTimeoutMS: 20000.0ms, connectTimeoutMS: 20000.0ms), Timeout: 5.0s, Topology Description: <TopologyDescription id: 687610307724503d2d237621, topology_type: Single, servers: [<ServerDescription ('************', 27017) server_type: Unknown, rtt: None, error=NetworkTimeout('************:27017: timed out (configured timeouts: socketTimeoutMS: 20000.0ms, connectTimeoutMS: 20000.0ms)')>]>
2025-07-16 07:43:44,488 - ERROR - ❌ 错误日志记录失败: ************:27017: timed out (configured timeouts: socketTimeoutMS: 20000.0ms, connectTimeoutMS: 20000.0ms), Timeout: 5.0s, Topology Description: <TopologyDescription id: 687610307724503d2d237622, topology_type: Single, servers: [<ServerDescription ('************', 27017) server_type: Unknown, rtt: None, error=NetworkTimeout('************:27017: timed out (configured timeouts: socketTimeoutMS: 20000.0ms, connectTimeoutMS: 20000.0ms)')>]>
2025-07-16 07:43:44,492 - INFO - 📋 2025/07/03 + 16:10 + 处理队列项: UPDATE - entry_id=145974 - queue_id=363 - 重试次数=1
2025-07-16 07:43:44,493 - INFO - 🔄 UPDATE操作转换为INSERT: entry_id=145974 (无external_id，记录未同步到MDB)
2025-07-16 07:43:44,507 - ERROR - ❌ INSERT操作失败: 找不到entry记录: entry_id=145974
Traceback (most recent call last):
  File "/home/<USER>/Documents/testF/2025/ser25/server0613/MySuiteDatebase2402newuigood-yolo5-3/server5/app/services/f2_push_writer.py", line 300, in _handle_insert_operation_in_transaction
    raise ValueError(f"找不到entry记录: entry_id={entry_id}")
ValueError: 找不到entry记录: entry_id=145974
2025-07-16 07:43:44,507 - ERROR - ❌ UPDATE操作失败: 找不到entry记录: entry_id=145974
Traceback (most recent call last):
  File "/home/<USER>/Documents/testF/2025/ser25/server0613/MySuiteDatebase2402newuigood-yolo5-3/server5/app/services/f2_push_writer.py", line 392, in _handle_update_operation_in_transaction
    await self._handle_insert_operation_in_transaction(conn, item, worker_id)
  File "/home/<USER>/Documents/testF/2025/ser25/server0613/MySuiteDatebase2402newuigood-yolo5-3/server5/app/services/f2_push_writer.py", line 300, in _handle_insert_operation_in_transaction
    raise ValueError(f"找不到entry记录: entry_id={entry_id}")
ValueError: 找不到entry记录: entry_id=145974
2025-07-16 07:43:44,508 - ERROR - ❌ 2025/07/03 + 16:10 + 队列项处理失败 (第2次): 找不到entry记录: entry_id=145974
2025-07-16 07:43:44,509 - WARNING - ⚠️ 2025/07/03 + 16:10 + 队列项将在下次循环重试: queue_id=363, retry_count=2
2025-07-16 07:43:44,989 - ERROR - ❌ 同步日志记录失败: ************:27017: timed out (configured timeouts: socketTimeoutMS: 20000.0ms, connectTimeoutMS: 20000.0ms), Timeout: 5.0s, Topology Description: <TopologyDescription id: 687610307724503d2d237622, topology_type: Single, servers: [<ServerDescription ('************', 27017) server_type: Unknown, rtt: None, error=NetworkTimeout('************:27017: timed out (configured timeouts: socketTimeoutMS: 20000.0ms, connectTimeoutMS: 20000.0ms)')>]>
2025-07-16 07:43:44,989 - ERROR - ❌ 同步日志记录失败: ************:27017: timed out (configured timeouts: socketTimeoutMS: 20000.0ms, connectTimeoutMS: 20000.0ms), Timeout: 5.0s, Topology Description: <TopologyDescription id: 687610307724503d2d237622, topology_type: Single, servers: [<ServerDescription ('************', 27017) server_type: Unknown, rtt: None, error=NetworkTimeout('************:27017: timed out (configured timeouts: socketTimeoutMS: 20000.0ms, connectTimeoutMS: 20000.0ms)')>]>
2025-07-16 07:43:47,989 - ERROR - ❌ 同步日志记录失败: ************:27017: timed out (configured timeouts: socketTimeoutMS: 20000.0ms, connectTimeoutMS: 20000.0ms), Timeout: 5.0s, Topology Description: <TopologyDescription id: 687610307724503d2d237622, topology_type: Single, servers: [<ServerDescription ('************', 27017) server_type: Unknown, rtt: None, error=NetworkTimeout('************:27017: timed out (configured timeouts: socketTimeoutMS: 20000.0ms, connectTimeoutMS: 20000.0ms)')>]>
2025-07-16 07:43:47,990 - WARNING - ✅ 队列项清理完成: queue_id=360
2025-07-16 07:43:49,511 - ERROR - ❌ 错误日志记录失败: ************:27017: timed out (configured timeouts: socketTimeoutMS: 20000.0ms, connectTimeoutMS: 20000.0ms), Timeout: 5.0s, Topology Description: <TopologyDescription id: 687610307724503d2d237622, topology_type: Single, servers: [<ServerDescription ('************', 27017) server_type: Unknown, rtt: None, error=NetworkTimeout('************:27017: timed out (configured timeouts: socketTimeoutMS: 20000.0ms, connectTimeoutMS: 20000.0ms)')>]>
2025-07-16 07:43:49,515 - INFO - 📋 2025/07/03 + 16:10 + 处理队列项: UPDATE - entry_id=145974 - queue_id=363 - 重试次数=2
2025-07-16 07:43:49,515 - INFO - 🔄 UPDATE操作转换为INSERT: entry_id=145974 (无external_id，记录未同步到MDB)
2025-07-16 07:43:49,529 - ERROR - ❌ INSERT操作失败: 找不到entry记录: entry_id=145974
Traceback (most recent call last):
  File "/home/<USER>/Documents/testF/2025/ser25/server0613/MySuiteDatebase2402newuigood-yolo5-3/server5/app/services/f2_push_writer.py", line 300, in _handle_insert_operation_in_transaction
    raise ValueError(f"找不到entry记录: entry_id={entry_id}")
ValueError: 找不到entry记录: entry_id=145974
2025-07-16 07:43:49,529 - ERROR - ❌ UPDATE操作失败: 找不到entry记录: entry_id=145974
Traceback (most recent call last):
  File "/home/<USER>/Documents/testF/2025/ser25/server0613/MySuiteDatebase2402newuigood-yolo5-3/server5/app/services/f2_push_writer.py", line 392, in _handle_update_operation_in_transaction
    await self._handle_insert_operation_in_transaction(conn, item, worker_id)
  File "/home/<USER>/Documents/testF/2025/ser25/server0613/MySuiteDatebase2402newuigood-yolo5-3/server5/app/services/f2_push_writer.py", line 300, in _handle_insert_operation_in_transaction
    raise ValueError(f"找不到entry记录: entry_id={entry_id}")
ValueError: 找不到entry记录: entry_id=145974
2025-07-16 07:43:49,529 - ERROR - ❌ 2025/07/03 + 16:10 + 队列项处理失败 (第3次): 找不到entry记录: entry_id=145974
2025-07-16 07:43:49,531 - WARNING - ⚠️ 2025/07/03 + 16:10 + 队列项将在下次循环重试: queue_id=363, retry_count=3
2025-07-16 07:43:49,991 - ERROR - ❌ 性能指标记录失败: ************:27017: timed out (configured timeouts: socketTimeoutMS: 20000.0ms, connectTimeoutMS: 20000.0ms), Timeout: 5.0s, Topology Description: <TopologyDescription id: 687610307724503d2d237622, topology_type: Single, servers: [<ServerDescription ('************', 27017) server_type: Unknown, rtt: None, error=NetworkTimeout('************:27017: timed out (configured timeouts: socketTimeoutMS: 20000.0ms, connectTimeoutMS: 20000.0ms)')>]>
2025-07-16 07:43:49,991 - ERROR - ❌ 性能指标记录失败: ************:27017: timed out (configured timeouts: socketTimeoutMS: 20000.0ms, connectTimeoutMS: 20000.0ms), Timeout: 5.0s, Topology Description: <TopologyDescription id: 687610307724503d2d237622, topology_type: Single, servers: [<ServerDescription ('************', 27017) server_type: Unknown, rtt: None, error=NetworkTimeout('************:27017: timed out (configured timeouts: socketTimeoutMS: 20000.0ms, connectTimeoutMS: 20000.0ms)')>]>
2025-07-16 07:43:52,991 - ERROR - ❌ 错误日志记录失败: ************:27017: timed out (configured timeouts: socketTimeoutMS: 20000.0ms, connectTimeoutMS: 20000.0ms), Timeout: 5.0s, Topology Description: <TopologyDescription id: 687610307724503d2d237622, topology_type: Single, servers: [<ServerDescription ('************', 27017) server_type: Unknown, rtt: None, error=NetworkTimeout('************:27017: timed out (configured timeouts: socketTimeoutMS: 20000.0ms, connectTimeoutMS: 20000.0ms)')>]>
2025-07-16 07:43:54,532 - ERROR - ❌ 错误日志记录失败: ************:27017: timed out (configured timeouts: socketTimeoutMS: 20000.0ms, connectTimeoutMS: 20000.0ms), Timeout: 5.0s, Topology Description: <TopologyDescription id: 687610307724503d2d237622, topology_type: Single, servers: [<ServerDescription ('************', 27017) server_type: Unknown, rtt: None, error=NetworkTimeout('************:27017: timed out (configured timeouts: socketTimeoutMS: 20000.0ms, connectTimeoutMS: 20000.0ms)')>]>
2025-07-16 07:43:54,536 - INFO - 📋 2025/07/03 + 16:10 + 处理队列项: UPDATE - entry_id=145974 - queue_id=363 - 重试次数=3
2025-07-16 07:43:54,537 - INFO - 🔄 UPDATE操作转换为INSERT: entry_id=145974 (无external_id，记录未同步到MDB)
2025-07-16 07:43:54,551 - ERROR - ❌ INSERT操作失败: 找不到entry记录: entry_id=145974
Traceback (most recent call last):
  File "/home/<USER>/Documents/testF/2025/ser25/server0613/MySuiteDatebase2402newuigood-yolo5-3/server5/app/services/f2_push_writer.py", line 300, in _handle_insert_operation_in_transaction
    raise ValueError(f"找不到entry记录: entry_id={entry_id}")
ValueError: 找不到entry记录: entry_id=145974
2025-07-16 07:43:54,551 - ERROR - ❌ UPDATE操作失败: 找不到entry记录: entry_id=145974
Traceback (most recent call last):
  File "/home/<USER>/Documents/testF/2025/ser25/server0613/MySuiteDatebase2402newuigood-yolo5-3/server5/app/services/f2_push_writer.py", line 392, in _handle_update_operation_in_transaction
    await self._handle_insert_operation_in_transaction(conn, item, worker_id)
  File "/home/<USER>/Documents/testF/2025/ser25/server0613/MySuiteDatebase2402newuigood-yolo5-3/server5/app/services/f2_push_writer.py", line 300, in _handle_insert_operation_in_transaction
    raise ValueError(f"找不到entry记录: entry_id={entry_id}")
ValueError: 找不到entry记录: entry_id=145974
2025-07-16 07:43:54,551 - ERROR - ❌ 2025/07/03 + 16:10 + 队列项处理失败 (第4次): 找不到entry记录: entry_id=145974
2025-07-16 07:43:54,552 - WARNING - ⚠️ 2025/07/03 + 16:10 + 队列项将在下次循环重试: queue_id=363, retry_count=4
2025-07-16 07:43:59,554 - ERROR - ❌ 错误日志记录失败: ************:27017: timed out (configured timeouts: socketTimeoutMS: 20000.0ms, connectTimeoutMS: 20000.0ms), Timeout: 5.0s, Topology Description: <TopologyDescription id: 687610307724503d2d237622, topology_type: Single, servers: [<ServerDescription ('************', 27017) server_type: Unknown, rtt: None, error=NetworkTimeout('************:27017: timed out (configured timeouts: socketTimeoutMS: 20000.0ms, connectTimeoutMS: 20000.0ms)')>]>
2025-07-16 07:43:59,557 - INFO - 📋 2025/07/03 + 16:10 + 处理队列项: UPDATE - entry_id=145974 - queue_id=363 - 重试次数=4
2025-07-16 07:43:59,558 - INFO - 🔄 UPDATE操作转换为INSERT: entry_id=145974 (无external_id，记录未同步到MDB)
2025-07-16 07:43:59,573 - ERROR - ❌ INSERT操作失败: 找不到entry记录: entry_id=145974
Traceback (most recent call last):
  File "/home/<USER>/Documents/testF/2025/ser25/server0613/MySuiteDatebase2402newuigood-yolo5-3/server5/app/services/f2_push_writer.py", line 300, in _handle_insert_operation_in_transaction
    raise ValueError(f"找不到entry记录: entry_id={entry_id}")
ValueError: 找不到entry记录: entry_id=145974
2025-07-16 07:43:59,573 - ERROR - ❌ UPDATE操作失败: 找不到entry记录: entry_id=145974
Traceback (most recent call last):
  File "/home/<USER>/Documents/testF/2025/ser25/server0613/MySuiteDatebase2402newuigood-yolo5-3/server5/app/services/f2_push_writer.py", line 392, in _handle_update_operation_in_transaction
    await self._handle_insert_operation_in_transaction(conn, item, worker_id)
  File "/home/<USER>/Documents/testF/2025/ser25/server0613/MySuiteDatebase2402newuigood-yolo5-3/server5/app/services/f2_push_writer.py", line 300, in _handle_insert_operation_in_transaction
    raise ValueError(f"找不到entry记录: entry_id={entry_id}")
ValueError: 找不到entry记录: entry_id=145974
2025-07-16 07:43:59,574 - ERROR - ❌ 2025/07/03 + 16:10 + 队列项处理失败 (第5次): 找不到entry记录: entry_id=145974
2025-07-16 07:43:59,575 - WARNING - ⚠️ 2025/07/03 + 16:10 + 队列项将在下次循环重试: queue_id=363, retry_count=5
2025-07-16 07:44:05,002 - ERROR - ❌ 错误日志记录失败: ************:27017: timed out (configured timeouts: socketTimeoutMS: 20000.0ms, connectTimeoutMS: 20000.0ms), Timeout: 5.0s, Topology Description: <TopologyDescription id: 687610307724503d2d237622, topology_type: Single, servers: [<ServerDescription ('************', 27017) server_type: Unknown, rtt: None, error=NetworkTimeout('************:27017: timed out (configured timeouts: socketTimeoutMS: 20000.0ms, connectTimeoutMS: 20000.0ms)')>]>
2025-07-16 07:44:05,006 - INFO - 📋 2025/07/03 + 16:10 + 处理队列项: UPDATE - entry_id=145974 - queue_id=363 - 重试次数=5
2025-07-16 07:44:05,007 - INFO - 🔄 UPDATE操作转换为INSERT: entry_id=145974 (无external_id，记录未同步到MDB)
2025-07-16 07:44:05,032 - ERROR - ❌ INSERT操作失败: 找不到entry记录: entry_id=145974
Traceback (most recent call last):
  File "/home/<USER>/Documents/testF/2025/ser25/server0613/MySuiteDatebase2402newuigood-yolo5-3/server5/app/services/f2_push_writer.py", line 300, in _handle_insert_operation_in_transaction
    raise ValueError(f"找不到entry记录: entry_id={entry_id}")
ValueError: 找不到entry记录: entry_id=145974
2025-07-16 07:44:05,032 - ERROR - ❌ UPDATE操作失败: 找不到entry记录: entry_id=145974
Traceback (most recent call last):
  File "/home/<USER>/Documents/testF/2025/ser25/server0613/MySuiteDatebase2402newuigood-yolo5-3/server5/app/services/f2_push_writer.py", line 392, in _handle_update_operation_in_transaction
    await self._handle_insert_operation_in_transaction(conn, item, worker_id)
  File "/home/<USER>/Documents/testF/2025/ser25/server0613/MySuiteDatebase2402newuigood-yolo5-3/server5/app/services/f2_push_writer.py", line 300, in _handle_insert_operation_in_transaction
    raise ValueError(f"找不到entry记录: entry_id={entry_id}")
ValueError: 找不到entry记录: entry_id=145974
2025-07-16 07:44:05,032 - ERROR - ❌ 2025/07/03 + 16:10 + 队列项处理失败 (第6次): 找不到entry记录: entry_id=145974
2025-07-16 07:44:05,033 - WARNING - ⚠️ 2025/07/03 + 16:10 + 队列项将在下次循环重试: queue_id=363, retry_count=6
2025-07-16 07:44:10,035 - ERROR - ❌ 错误日志记录失败: ************:27017: timed out (configured timeouts: socketTimeoutMS: 20000.0ms, connectTimeoutMS: 20000.0ms), Timeout: 5.0s, Topology Description: <TopologyDescription id: 687610307724503d2d237622, topology_type: Single, servers: [<ServerDescription ('************', 27017) server_type: Unknown, rtt: None, error=NetworkTimeout('************:27017: timed out (configured timeouts: socketTimeoutMS: 20000.0ms, connectTimeoutMS: 20000.0ms)')>]>
2025-07-16 07:44:10,038 - INFO - 📋 2025/07/03 + 16:10 + 处理队列项: UPDATE - entry_id=145974 - queue_id=363 - 重试次数=6
2025-07-16 07:44:10,039 - INFO - 🔄 UPDATE操作转换为INSERT: entry_id=145974 (无external_id，记录未同步到MDB)
2025-07-16 07:44:10,052 - ERROR - ❌ INSERT操作失败: 找不到entry记录: entry_id=145974
Traceback (most recent call last):
  File "/home/<USER>/Documents/testF/2025/ser25/server0613/MySuiteDatebase2402newuigood-yolo5-3/server5/app/services/f2_push_writer.py", line 300, in _handle_insert_operation_in_transaction
    raise ValueError(f"找不到entry记录: entry_id={entry_id}")
ValueError: 找不到entry记录: entry_id=145974
2025-07-16 07:44:10,052 - ERROR - ❌ UPDATE操作失败: 找不到entry记录: entry_id=145974
Traceback (most recent call last):
  File "/home/<USER>/Documents/testF/2025/ser25/server0613/MySuiteDatebase2402newuigood-yolo5-3/server5/app/services/f2_push_writer.py", line 392, in _handle_update_operation_in_transaction
    await self._handle_insert_operation_in_transaction(conn, item, worker_id)
  File "/home/<USER>/Documents/testF/2025/ser25/server0613/MySuiteDatebase2402newuigood-yolo5-3/server5/app/services/f2_push_writer.py", line 300, in _handle_insert_operation_in_transaction
    raise ValueError(f"找不到entry记录: entry_id={entry_id}")
ValueError: 找不到entry记录: entry_id=145974
2025-07-16 07:44:10,053 - ERROR - ❌ 2025/07/03 + 16:10 + 队列项处理失败 (第7次): 找不到entry记录: entry_id=145974
2025-07-16 07:44:10,053 - WARNING - ⚠️ 2025/07/03 + 16:10 + 队列项将在下次循环重试: queue_id=363, retry_count=7
2025-07-16 07:44:15,055 - ERROR - ❌ 错误日志记录失败: ************:27017: timed out (configured timeouts: socketTimeoutMS: 20000.0ms, connectTimeoutMS: 20000.0ms), Timeout: 5.0s, Topology Description: <TopologyDescription id: 687610307724503d2d237622, topology_type: Single, servers: [<ServerDescription ('************', 27017) server_type: Unknown, rtt: None, error=NetworkTimeout('************:27017: timed out (configured timeouts: socketTimeoutMS: 20000.0ms, connectTimeoutMS: 20000.0ms)')>]>
2025-07-16 07:44:15,059 - INFO - 📋 2025/07/03 + 16:10 + 处理队列项: UPDATE - entry_id=145974 - queue_id=363 - 重试次数=7
2025-07-16 07:44:15,060 - INFO - 🔄 UPDATE操作转换为INSERT: entry_id=145974 (无external_id，记录未同步到MDB)
2025-07-16 07:44:15,074 - ERROR - ❌ INSERT操作失败: 找不到entry记录: entry_id=145974
Traceback (most recent call last):
  File "/home/<USER>/Documents/testF/2025/ser25/server0613/MySuiteDatebase2402newuigood-yolo5-3/server5/app/services/f2_push_writer.py", line 300, in _handle_insert_operation_in_transaction
    raise ValueError(f"找不到entry记录: entry_id={entry_id}")
ValueError: 找不到entry记录: entry_id=145974
2025-07-16 07:44:15,075 - ERROR - ❌ UPDATE操作失败: 找不到entry记录: entry_id=145974
Traceback (most recent call last):
  File "/home/<USER>/Documents/testF/2025/ser25/server0613/MySuiteDatebase2402newuigood-yolo5-3/server5/app/services/f2_push_writer.py", line 392, in _handle_update_operation_in_transaction
    await self._handle_insert_operation_in_transaction(conn, item, worker_id)
  File "/home/<USER>/Documents/testF/2025/ser25/server0613/MySuiteDatebase2402newuigood-yolo5-3/server5/app/services/f2_push_writer.py", line 300, in _handle_insert_operation_in_transaction
    raise ValueError(f"找不到entry记录: entry_id={entry_id}")
ValueError: 找不到entry记录: entry_id=145974
2025-07-16 07:44:15,075 - ERROR - ❌ 2025/07/03 + 16:10 + 队列项处理失败 (第8次): 找不到entry记录: entry_id=145974
2025-07-16 07:44:15,076 - WARNING - ⚠️ 2025/07/03 + 16:10 + 队列项将在下次循环重试: queue_id=363, retry_count=8
2025-07-16 07:44:20,078 - ERROR - ❌ 错误日志记录失败: ************:27017: timed out (configured timeouts: socketTimeoutMS: 20000.0ms, connectTimeoutMS: 20000.0ms), Timeout: 5.0s, Topology Description: <TopologyDescription id: 687610307724503d2d237622, topology_type: Single, servers: [<ServerDescription ('************', 27017) server_type: Unknown, rtt: None, error=NetworkTimeout('************:27017: timed out (configured timeouts: socketTimeoutMS: 20000.0ms, connectTimeoutMS: 20000.0ms)')>]>
2025-07-16 07:44:20,081 - INFO - 📋 2025/07/03 + 16:10 + 处理队列项: UPDATE - entry_id=145974 - queue_id=363 - 重试次数=8
2025-07-16 07:44:20,082 - INFO - 🔄 UPDATE操作转换为INSERT: entry_id=145974 (无external_id，记录未同步到MDB)
2025-07-16 07:44:20,099 - ERROR - ❌ INSERT操作失败: 找不到entry记录: entry_id=145974
Traceback (most recent call last):
  File "/home/<USER>/Documents/testF/2025/ser25/server0613/MySuiteDatebase2402newuigood-yolo5-3/server5/app/services/f2_push_writer.py", line 300, in _handle_insert_operation_in_transaction
    raise ValueError(f"找不到entry记录: entry_id={entry_id}")
ValueError: 找不到entry记录: entry_id=145974
2025-07-16 07:44:20,099 - ERROR - ❌ UPDATE操作失败: 找不到entry记录: entry_id=145974
Traceback (most recent call last):
  File "/home/<USER>/Documents/testF/2025/ser25/server0613/MySuiteDatebase2402newuigood-yolo5-3/server5/app/services/f2_push_writer.py", line 392, in _handle_update_operation_in_transaction
    await self._handle_insert_operation_in_transaction(conn, item, worker_id)
  File "/home/<USER>/Documents/testF/2025/ser25/server0613/MySuiteDatebase2402newuigood-yolo5-3/server5/app/services/f2_push_writer.py", line 300, in _handle_insert_operation_in_transaction
    raise ValueError(f"找不到entry记录: entry_id={entry_id}")
ValueError: 找不到entry记录: entry_id=145974
2025-07-16 07:44:20,100 - ERROR - ❌ 2025/07/03 + 16:10 + 队列项处理失败 (第9次): 找不到entry记录: entry_id=145974
2025-07-16 07:44:20,100 - WARNING - ⚠️ 2025/07/03 + 16:10 + 队列项将在下次循环重试: queue_id=363, retry_count=9
2025-07-16 07:44:25,523 - ERROR - ❌ 错误日志记录失败: ************:27017: timed out (configured timeouts: socketTimeoutMS: 20000.0ms, connectTimeoutMS: 20000.0ms), Timeout: 5.0s, Topology Description: <TopologyDescription id: 687610307724503d2d237622, topology_type: Single, servers: [<ServerDescription ('************', 27017) server_type: Unknown, rtt: None, error=NetworkTimeout('************:27017: timed out (configured timeouts: socketTimeoutMS: 20000.0ms, connectTimeoutMS: 20000.0ms)')>]>
2025-07-16 07:44:25,527 - INFO - 📋 2025/07/03 + 16:10 + 处理队列项: UPDATE - entry_id=145974 - queue_id=363 - 重试次数=9
2025-07-16 07:44:25,528 - INFO - 🔄 UPDATE操作转换为INSERT: entry_id=145974 (无external_id，记录未同步到MDB)
2025-07-16 07:44:25,540 - ERROR - ❌ INSERT操作失败: 找不到entry记录: entry_id=145974
Traceback (most recent call last):
  File "/home/<USER>/Documents/testF/2025/ser25/server0613/MySuiteDatebase2402newuigood-yolo5-3/server5/app/services/f2_push_writer.py", line 300, in _handle_insert_operation_in_transaction
    raise ValueError(f"找不到entry记录: entry_id={entry_id}")
ValueError: 找不到entry记录: entry_id=145974
2025-07-16 07:44:25,540 - ERROR - ❌ UPDATE操作失败: 找不到entry记录: entry_id=145974
Traceback (most recent call last):
  File "/home/<USER>/Documents/testF/2025/ser25/server0613/MySuiteDatebase2402newuigood-yolo5-3/server5/app/services/f2_push_writer.py", line 392, in _handle_update_operation_in_transaction
    await self._handle_insert_operation_in_transaction(conn, item, worker_id)
  File "/home/<USER>/Documents/testF/2025/ser25/server0613/MySuiteDatebase2402newuigood-yolo5-3/server5/app/services/f2_push_writer.py", line 300, in _handle_insert_operation_in_transaction
    raise ValueError(f"找不到entry记录: entry_id={entry_id}")
ValueError: 找不到entry记录: entry_id=145974
2025-07-16 07:44:25,541 - ERROR - ❌ 2025/07/03 + 16:10 + 队列项处理失败 (第10次): 找不到entry记录: entry_id=145974
2025-07-16 07:44:25,541 - ERROR - 🚨 队列项超过最大重试次数，执行清理: queue_id=363, entry_id=145974
2025-07-16 07:44:30,559 - ERROR - ❌ 同步日志记录失败: ************:27017: timed out (configured timeouts: socketTimeoutMS: 20000.0ms, connectTimeoutMS: 20000.0ms), Timeout: 5.0s, Topology Description: <TopologyDescription id: 687610307724503d2d237622, topology_type: Single, servers: [<ServerDescription ('************', 27017) server_type: Unknown, rtt: None, error=NetworkTimeout('************:27017: timed out (configured timeouts: socketTimeoutMS: 20000.0ms, connectTimeoutMS: 20000.0ms)')>]>
2025-07-16 07:44:30,559 - WARNING - ✅ 队列项清理完成: queue_id=363
2025-07-16 07:44:35,561 - ERROR - ❌ 错误日志记录失败: ************:27017: timed out (configured timeouts: socketTimeoutMS: 20000.0ms, connectTimeoutMS: 20000.0ms), Timeout: 5.0s, Topology Description: <TopologyDescription id: 687610307724503d2d237622, topology_type: Single, servers: [<ServerDescription ('************', 27017) server_type: Unknown, rtt: None, error=NetworkTimeout('************:27017: timed out (configured timeouts: socketTimeoutMS: 20000.0ms, connectTimeoutMS: 20000.0ms)')>]>
2025-07-16 08:03:55,450 - INFO - 📋 2025/07/03 + 16:10 + 处理队列项: INSERT - entry_id=145975 - queue_id=365 - 重试次数=0
2025-07-16 08:03:55,466 - INFO - 🔄 执行INSERT操作: entry_id=145975, employee_id=215829
2025-07-16 08:03:55,466 - INFO - 🔍 调试 - 从entries表读取的原始数据: <Record entry_date=datetime.date(2025, 7, 16) id=145975 external_id=None ts=datetime.datetime(2025, 7, 15, 23, 3, 55, 393449, tzinfo=datetime.timezone.utc) employee_id='215829' model=None number=None factory_number=None project_number=None unit_number=None category=0 item=0 duration=Decimal('0.10') department='131' source='user'>
2025-07-16 08:03:55,467 - INFO - 🔍 调试 - category类型: <class 'int'>, 值: 0
2025-07-16 08:03:55,467 - INFO - 🔍 调试 - item类型: <class 'int'>, 值: 0
2025-07-16 08:03:55,467 - INFO - 🔍 调试 - 准备发送给Server6的数据: {'employee_id': '215829', 'entry_date': '2025/07/16', 'model': None, 'number': None, 'factory_number': None, 'project_number': None, 'unit_number': None, 'category': '0', 'item': '0', 'duration': 0.1, 'department': '131'}
2025-07-16 08:03:55,467 - INFO - 📤 发送到Server6的数据: {'従業員ｺｰﾄﾞ': '215829', '日付': '2025-07-16', '機種': None, '号機': None, '工場製番': None, '工事番号': None, 'ﾕﾆｯﾄ番号': None, '区分': '0', '項目': '0', '時間': 0.1, '所属ｺｰﾄﾞ': '131'}
2025-07-16 08:03:55,467 - INFO - 🔍 调试 - category最终值: 0, 类型: <class 'str'>
2025-07-16 08:03:55,467 - INFO - 🔍 调试 - item最终值: 0, 类型: <class 'str'>
2025-07-16 08:03:59,259 - INFO - ✅ INSERT完成: entry_id=145975 -> external_id=603749
2025-07-16 08:04:00,404 - ERROR - ❌ 性能指标记录失败: ************:27017: timed out (configured timeouts: socketTimeoutMS: 20000.0ms, connectTimeoutMS: 20000.0ms), Timeout: 5.0s, Topology Description: <TopologyDescription id: 687610307724503d2d237621, topology_type: Single, servers: [<ServerDescription ('************', 27017) server_type: Unknown, rtt: None, error=NetworkTimeout('************:27017: timed out (configured timeouts: socketTimeoutMS: 20000.0ms, connectTimeoutMS: 20000.0ms)')>]>
2025-07-16 08:04:04,260 - ERROR - ❌ 同步日志记录失败: ************:27017: timed out (configured timeouts: socketTimeoutMS: 20000.0ms, connectTimeoutMS: 20000.0ms), Timeout: 5.0s, Topology Description: <TopologyDescription id: 687610307724503d2d237622, topology_type: Single, servers: [<ServerDescription ('************', 27017) server_type: Unknown, rtt: None, error=NetworkTimeout('************:27017: timed out (configured timeouts: socketTimeoutMS: 20000.0ms, connectTimeoutMS: 20000.0ms)')>]>
2025-07-16 08:04:09,750 - ERROR - ❌ 性能指标记录失败: ************:27017: timed out (configured timeouts: socketTimeoutMS: 20000.0ms, connectTimeoutMS: 20000.0ms), Timeout: 5.0s, Topology Description: <TopologyDescription id: 687610307724503d2d237622, topology_type: Single, servers: [<ServerDescription ('************', 27017) server_type: Unknown, rtt: None, error=NetworkTimeout('************:27017: timed out (configured timeouts: socketTimeoutMS: 20000.0ms, connectTimeoutMS: 20000.0ms)')>]>
2025-07-16 08:10:00,474 - INFO - 📋 2025/07/03 + 16:10 + 处理队列项: INSERT - entry_id=145976 - queue_id=366 - 重试次数=0
2025-07-16 08:10:00,491 - INFO - 🔄 执行INSERT操作: entry_id=145976, employee_id=215829
2025-07-16 08:10:00,492 - INFO - 🔍 调试 - 从entries表读取的原始数据: <Record entry_date=datetime.date(2025, 7, 16) id=145976 external_id=None ts=datetime.datetime(2025, 7, 15, 23, 9, 59, 820130, tzinfo=datetime.timezone.utc) employee_id='215829' model=None number=None factory_number=None project_number=None unit_number=None category=0 item=0 duration=Decimal('0.10') department='131' source='user'>
2025-07-16 08:10:00,492 - INFO - 🔍 调试 - category类型: <class 'int'>, 值: 0
2025-07-16 08:10:00,492 - INFO - 🔍 调试 - item类型: <class 'int'>, 值: 0
2025-07-16 08:10:00,492 - INFO - 🔍 调试 - 准备发送给Server6的数据: {'employee_id': '215829', 'entry_date': '2025/07/16', 'model': None, 'number': None, 'factory_number': None, 'project_number': None, 'unit_number': None, 'category': '0', 'item': '0', 'duration': 0.1, 'department': '131'}
2025-07-16 08:10:00,492 - INFO - 📤 发送到Server6的数据: {'従業員ｺｰﾄﾞ': '215829', '日付': '2025-07-16', '機種': None, '号機': None, '工場製番': None, '工事番号': None, 'ﾕﾆｯﾄ番号': None, '区分': '0', '項目': '0', '時間': 0.1, '所属ｺｰﾄﾞ': '131'}
2025-07-16 08:10:00,492 - INFO - 🔍 调试 - category最终值: 0, 类型: <class 'str'>
2025-07-16 08:10:00,492 - INFO - 🔍 调试 - item最终值: 0, 类型: <class 'str'>
2025-07-16 08:10:04,019 - INFO - ✅ INSERT完成: entry_id=145976 -> external_id=603755
2025-07-16 08:10:04,831 - ERROR - ❌ 性能指标记录失败: ************:27017: timed out (configured timeouts: socketTimeoutMS: 20000.0ms, connectTimeoutMS: 20000.0ms), Timeout: 5.0s, Topology Description: <TopologyDescription id: 687610307724503d2d237621, topology_type: Single, servers: [<ServerDescription ('************', 27017) server_type: Unknown, rtt: None, error=NetworkTimeout('************:27017: timed out (configured timeouts: socketTimeoutMS: 20000.0ms, connectTimeoutMS: 20000.0ms)')>]>
2025-07-16 08:10:09,020 - ERROR - ❌ 同步日志记录失败: ************:27017: timed out (configured timeouts: socketTimeoutMS: 20000.0ms, connectTimeoutMS: 20000.0ms), Timeout: 5.0s, Topology Description: <TopologyDescription id: 687610307724503d2d237622, topology_type: Single, servers: [<ServerDescription ('************', 27017) server_type: Unknown, rtt: None, error=NetworkTimeout('************:27017: timed out (configured timeouts: socketTimeoutMS: 20000.0ms, connectTimeoutMS: 20000.0ms)')>]>
2025-07-16 08:10:14,023 - ERROR - ❌ 性能指标记录失败: ************:27017: timed out (configured timeouts: socketTimeoutMS: 20000.0ms, connectTimeoutMS: 20000.0ms), Timeout: 5.0s, Topology Description: <TopologyDescription id: 687610307724503d2d237622, topology_type: Single, servers: [<ServerDescription ('************', 27017) server_type: Unknown, rtt: None, error=NetworkTimeout('************:27017: timed out (configured timeouts: socketTimeoutMS: 20000.0ms, connectTimeoutMS: 20000.0ms)')>]>
2025-07-16 08:10:46,088 - INFO - 📋 2025/07/03 + 16:10 + 处理队列项: INSERT - entry_id=145977 - queue_id=367 - 重试次数=0
2025-07-16 08:10:46,103 - INFO - 🔄 执行INSERT操作: entry_id=145977, employee_id=215829
2025-07-16 08:10:46,103 - INFO - 🔍 调试 - 从entries表读取的原始数据: <Record entry_date=datetime.date(2025, 7, 16) id=145977 external_id=None ts=datetime.datetime(2025, 7, 15, 23, 10, 45, 993482, tzinfo=datetime.timezone.utc) employee_id='215829' model=None number=None factory_number=None project_number=None unit_number=None category=0 item=0 duration=Decimal('0.10') department='131' source='user'>
2025-07-16 08:10:46,103 - INFO - 🔍 调试 - category类型: <class 'int'>, 值: 0
2025-07-16 08:10:46,103 - INFO - 🔍 调试 - item类型: <class 'int'>, 值: 0
2025-07-16 08:10:46,103 - INFO - 🔍 调试 - 准备发送给Server6的数据: {'employee_id': '215829', 'entry_date': '2025/07/16', 'model': None, 'number': None, 'factory_number': None, 'project_number': None, 'unit_number': None, 'category': '0', 'item': '0', 'duration': 0.1, 'department': '131'}
2025-07-16 08:10:46,103 - INFO - 📤 发送到Server6的数据: {'従業員ｺｰﾄﾞ': '215829', '日付': '2025-07-16', '機種': None, '号機': None, '工場製番': None, '工事番号': None, 'ﾕﾆｯﾄ番号': None, '区分': '0', '項目': '0', '時間': 0.1, '所属ｺｰﾄﾞ': '131'}
2025-07-16 08:10:46,103 - INFO - 🔍 调试 - category最终值: 0, 类型: <class 'str'>
2025-07-16 08:10:46,103 - INFO - 🔍 调试 - item最终值: 0, 类型: <class 'str'>
2025-07-16 08:10:49,520 - INFO - ✅ INSERT完成: entry_id=145977 -> external_id=603756
2025-07-16 08:10:51,004 - ERROR - ❌ 性能指标记录失败: ************:27017: timed out (configured timeouts: socketTimeoutMS: 20000.0ms, connectTimeoutMS: 20000.0ms), Timeout: 5.0s, Topology Description: <TopologyDescription id: 687610307724503d2d237621, topology_type: Single, servers: [<ServerDescription ('************', 27017) server_type: Unknown, rtt: None, error=NetworkTimeout('************:27017: timed out (configured timeouts: socketTimeoutMS: 20000.0ms, connectTimeoutMS: 20000.0ms)')>]>
2025-07-16 08:10:54,522 - ERROR - ❌ 同步日志记录失败: ************:27017: timed out (configured timeouts: socketTimeoutMS: 20000.0ms, connectTimeoutMS: 20000.0ms), Timeout: 5.0s, Topology Description: <TopologyDescription id: 687610307724503d2d237622, topology_type: Single, servers: [<ServerDescription ('************', 27017) server_type: Unknown, rtt: None, error=NetworkTimeout('************:27017: timed out (configured timeouts: socketTimeoutMS: 20000.0ms, connectTimeoutMS: 20000.0ms)')>]>
2025-07-16 08:10:59,526 - ERROR - ❌ 性能指标记录失败: ************:27017: timed out (configured timeouts: socketTimeoutMS: 20000.0ms, connectTimeoutMS: 20000.0ms), Timeout: 5.0s, Topology Description: <TopologyDescription id: 687610307724503d2d237622, topology_type: Single, servers: [<ServerDescription ('************', 27017) server_type: Unknown, rtt: None, error=NetworkTimeout('************:27017: timed out (configured timeouts: socketTimeoutMS: 20000.0ms, connectTimeoutMS: 20000.0ms)')>]>
2025-07-16 08:14:27,281 - INFO - 📋 2025/07/03 + 16:10 + 处理队列项: INSERT - entry_id=145978 - queue_id=368 - 重试次数=0
2025-07-16 08:14:27,297 - INFO - 🔄 执行INSERT操作: entry_id=145978, employee_id=215829
2025-07-16 08:14:27,298 - INFO - 🔍 调试 - 从entries表读取的原始数据: <Record entry_date=datetime.date(2025, 7, 16) id=145978 external_id=None ts=datetime.datetime(2025, 7, 15, 23, 14, 26, 614546, tzinfo=datetime.timezone.utc) employee_id='215829' model='' number='' factory_number='' project_number='TEST001' unit_number='' category=0 item=0 duration=Decimal('0.10') department='131' source='user'>
2025-07-16 08:14:27,298 - INFO - 🔍 调试 - category类型: <class 'int'>, 值: 0
2025-07-16 08:14:27,298 - INFO - 🔍 调试 - item类型: <class 'int'>, 值: 0
2025-07-16 08:14:27,298 - INFO - 🔍 调试 - 准备发送给Server6的数据: {'employee_id': '215829', 'entry_date': '2025/07/16', 'model': None, 'number': None, 'factory_number': None, 'project_number': 'TEST001', 'unit_number': None, 'category': '0', 'item': '0', 'duration': 0.1, 'department': '131'}
2025-07-16 08:14:27,298 - INFO - 📤 发送到Server6的数据: {'従業員ｺｰﾄﾞ': '215829', '日付': '2025-07-16', '機種': None, '号機': None, '工場製番': None, '工事番号': 'TEST001', 'ﾕﾆｯﾄ番号': None, '区分': '0', '項目': '0', '時間': 0.1, '所属ｺｰﾄﾞ': '131'}
2025-07-16 08:14:27,298 - INFO - 🔍 调试 - category最终值: 0, 类型: <class 'str'>
2025-07-16 08:14:27,298 - INFO - 🔍 调试 - item最终值: 0, 类型: <class 'str'>
2025-07-16 08:14:31,002 - INFO - ✅ INSERT完成: entry_id=145978 -> external_id=603757
2025-07-16 08:14:31,609 - ERROR - ❌ 性能指标记录失败: ************:27017: timed out (configured timeouts: socketTimeoutMS: 20000.0ms, connectTimeoutMS: 20000.0ms), Timeout: 5.0s, Topology Description: <TopologyDescription id: 687610307724503d2d237621, topology_type: Single, servers: [<ServerDescription ('************', 27017) server_type: Unknown, rtt: None, error=NetworkTimeout('************:27017: timed out (configured timeouts: socketTimeoutMS: 20000.0ms, connectTimeoutMS: 20000.0ms)')>]>
2025-07-16 08:14:36,004 - ERROR - ❌ 同步日志记录失败: ************:27017: timed out (configured timeouts: socketTimeoutMS: 20000.0ms, connectTimeoutMS: 20000.0ms), Timeout: 5.0s, Topology Description: <TopologyDescription id: 687610307724503d2d237622, topology_type: Single, servers: [<ServerDescription ('************', 27017) server_type: Unknown, rtt: None, error=NetworkTimeout('************:27017: timed out (configured timeouts: socketTimeoutMS: 20000.0ms, connectTimeoutMS: 20000.0ms)')>]>
2025-07-16 08:14:41,006 - ERROR - ❌ 性能指标记录失败: ************:27017: timed out (configured timeouts: socketTimeoutMS: 20000.0ms, connectTimeoutMS: 20000.0ms), Timeout: 5.0s, Topology Description: <TopologyDescription id: 687610307724503d2d237622, topology_type: Single, servers: [<ServerDescription ('************', 27017) server_type: Unknown, rtt: None, error=NetworkTimeout('************:27017: timed out (configured timeouts: socketTimeoutMS: 20000.0ms, connectTimeoutMS: 20000.0ms)')>]>
