2025-07-03 16:38:51,742 - server5-2.test - INFO - 🧪 2025/07/03 + 16:30 + server5-2 测试工具启动（真实采集）
2025-07-03 16:38:51,742 - server5-2.test - INFO - ============================================================
2025-07-03 16:38:51,742 - server5-2.test - INFO - 🎯 2025/07/03 + 16:30 + 开始运行所有测试
2025-07-03 16:38:51,742 - server5-2.test - INFO - 🧪 设置测试环境
2025-07-03 16:38:51,994 - server5-2.timeprotab_manager - INFO - ✅ timeprotab数据库连接池创建成功
2025-07-03 16:38:52,009 - server5-2.timeprotab_manager - INFO - ✅ 基础分区表 timeprotab 初始化完成
2025-07-03 16:38:52,010 - server5-2.test - INFO - ✅ 测试环境设置完成
2025-07-03 16:38:52,010 - server5-2.test - INFO - 🔍 2025/07/03 + 16:30 + 运行测试: 2025/07/03 + 16:30 + 数据库操作
2025-07-03 16:38:52,010 - server5-2.test - INFO - 🧪 2025/07/03 + 16:30 + 测试数据库操作
2025-07-03 16:38:52,018 - server5-2.timeprotab_manager - INFO - 📅 分区 timeprotab_2506 已存在
2025-07-03 16:38:52,019 - server5-2.test - INFO - ✅ 2025/07/03 + 16:30 + 分区创建测试成功: 2506
2025-07-03 16:38:52,020 - server5-2.timeprotab_manager - INFO - 📅 分区 timeprotab_2507 已存在
2025-07-03 16:38:52,021 - server5-2.test - INFO - ✅ 2025/07/03 + 16:30 + 分区创建测试成功: 2507
2025-07-03 16:38:52,030 - server5-2.timeprotab_manager - INFO - 📅 分区 timeprotab_2506 已存在
2025-07-03 16:38:52,034 - server5-2.timeprotab_manager - ERROR - ❌ 批量插入失败: invalid input for query argument $8: '08:30' ('str' object has no attribute 'hour')
2025-07-03 16:38:52,035 - server5-2.test - ERROR - ❌ 2025/07/03 + 16:30 + 数据库操作测试失败: invalid input for query argument $8: '08:30' ('str' object has no attribute 'hour')
2025-07-03 16:38:52,035 - server5-2.test - ERROR - ❌ 2025/07/03 + 16:30 + 测试失败: 2025/07/03 + 16:30 + 数据库操作
2025-07-03 16:38:53,036 - server5-2.test - INFO - 🔍 2025/07/03 + 16:30 + 运行测试: 2025/07/03 + 16:30 + 分区管理
2025-07-03 16:38:53,036 - server5-2.test - INFO - 🧪 2025/07/03 + 16:30 + 测试分区管理
2025-07-03 16:38:53,039 - server5-2.timeprotab_manager - INFO - 📅 分区 timeprotab_2506 已存在
2025-07-03 16:38:53,040 - server5-2.test - INFO - ✅ 2025/07/03 + 16:30 + 分区创建成功: 2506
2025-07-03 16:38:53,042 - server5-2.timeprotab_manager - INFO - 📅 分区 timeprotab_2507 已存在
2025-07-03 16:38:53,043 - server5-2.test - INFO - ✅ 2025/07/03 + 16:30 + 分区创建成功: 2507
2025-07-03 16:38:53,052 - server5-2.test - INFO - ✅ 2025/07/03 + 16:30 + 分区管理测试成功: 创建了 2 个分区
2025-07-03 16:38:53,052 - server5-2.test - INFO - ✅ 2025/07/03 + 16:30 + 测试通过: 2025/07/03 + 16:30 + 分区管理
2025-07-03 16:38:54,054 - server5-2.test - INFO - 🔍 2025/07/03 + 16:30 + 运行测试: 2025/07/03 + 16:30 + XML解析
2025-07-03 16:38:54,054 - server5-2.test - INFO - 🧪 2025/07/03 + 16:30 + 测试XML解析
2025-07-03 16:38:54,059 - server5-2.timepro_collector - INFO - ✅ XML解析完成: 2 条记录
2025-07-03 16:38:54,060 - server5-2.test - INFO - ✅ 2025/07/03 + 16:30 + XML解析测试成功: 2 条记录
2025-07-03 16:38:54,063 - server5-2.timeprotab_manager - INFO - 📅 分区 timeprotab_2506 已存在
2025-07-03 16:38:54,066 - server5-2.timeprotab_manager - ERROR - ❌ 批量插入失败: invalid input for query argument $18: '' ('str' object has no attribute 'hour')
2025-07-03 16:38:54,066 - server5-2.test - ERROR - ❌ 2025/07/03 + 16:30 + XML解析测试失败: invalid input for query argument $18: '' ('str' object has no attribute 'hour')
2025-07-03 16:38:54,067 - server5-2.test - ERROR - ❌ 2025/07/03 + 16:30 + 测试失败: 2025/07/03 + 16:30 + XML解析
2025-07-03 16:38:55,068 - server5-2.test - INFO - 🔍 2025/07/03 + 16:30 + 运行测试: 2025/07/03 + 16:30 + 真实采集工作流
2025-07-03 16:38:55,068 - server5-2.test - INFO - 🧪 2025/07/03 + 16:30 + 测试真实采集工作流
2025-07-03 16:38:55,291 - server5-2.timeprotab_manager - INFO - ✅ timeprotab数据库连接池创建成功
2025-07-03 16:38:55,305 - server5-2.timeprotab_manager - INFO - ✅ 基础分区表 timeprotab 初始化完成
2025-07-03 16:38:55,306 - server5-2.timepro_collector - INFO - 📅 定时任务已设置: 每天 1:00 开始采集
2025-07-03 16:38:55,307 - server5-2.timepro_collector - INFO - ⏰ 调度器线程启动
2025-07-03 16:38:55,307 - server5-2.timepro_collector - INFO - ✅ timepro采集服务启动成功
2025-07-03 16:38:55,307 - server5-2.timepro_collector - INFO - 🧪 2025/07/03 + 16:30 + 开始真实采集工作流
2025-07-03 16:38:55,307 - server5-2.timepro_collector - INFO - 📅 采集 215829 的 202507 数据
2025-07-03 16:38:55,307 - server5-2.timepro_collector - INFO - 🌐 2025/07/03 + 16:30 + 真实调用timepro网站: user_id=215829
2025-07-03 16:38:55,323 - server5-2.timepro_collector - ERROR - ❌ 2025/07/03 + 16:30 + timepro数据获取失败: Error accessing login page: HTTPConnectionPool(host='timepro.gongsi.co.jp', port=80): Max retries exceeded with url: /xgweb/login.asp (Caused by NameResolutionError("<urllib3.connection.HTTPConnection object at 0x78932332a210>: Failed to resolve 'timepro.gongsi.co.jp' ([Errno -2] Name or service not known)"))
2025-07-03 16:38:55,323 - server5-2.timepro_collector - ERROR - ❌ 2025/07/03 + 16:30 + HTML数据获取失败: 2025/07/03 + 16:30 + 未获取到HTML数据
2025-07-03 16:38:55,323 - server5-2.timepro_collector - INFO - 📝 生成模拟HTML数据: data/html_files/215829_202507.html
2025-07-03 16:38:55,323 - server5-2.timepro_collector - INFO - 🔄 2025/07/03 + 16:30 + 真实HTML转XML: data/html_files/215829_202507.html -> data/xml_files/215829_202507.xml
2025-07-03 16:38:55,327 - server5-2.timepro_collector - ERROR - ❌ 2025/07/03 + 16:30 + HTML转XML失败: 未生成XML内容
2025-07-03 16:38:55,327 - server5-2.timepro_collector - ERROR - ❌ 2025/07/03 + 16:30 + XML转换失败: 2025/07/03 + 16:30 + HTML转XML失败
2025-07-03 16:38:55,328 - server5-2.timepro_collector - INFO - 📝 生成模拟XML数据: data/xml_files/215829_202507.xml
2025-07-03 16:38:55,328 - server5-2.timepro_collector - INFO - ✅ XML解析完成: 2 条记录
2025-07-03 16:38:55,336 - server5-2.timeprotab_manager - INFO - 📅 分区 timeprotab_2507 已存在
2025-07-03 16:38:55,347 - server5-2.timeprotab_manager - INFO - 📅 分区 timeprotab_2507 已存在
2025-07-03 16:38:55,351 - server5-2.timeprotab_manager - ERROR - ❌ 批量插入失败: invalid input for query argument $18: '' ('str' object has no attribute 'hour')
2025-07-03 16:38:55,352 - server5-2.timepro_collector - ERROR - ❌ 用户月份数据采集失败: invalid input for query argument $18: '' ('str' object has no attribute 'hour')
2025-07-03 16:38:55,352 - server5-2.timepro_collector - ERROR - ❌ 2025/07/03 + 16:30 + 真实采集工作流失败: invalid input for query argument $18: '' ('str' object has no attribute 'hour')
2025-07-03 16:38:55,360 - server5-2.test - INFO - ✅ 2025/07/03 + 16:30 + 真实采集工作流测试成功
2025-07-03 16:38:55,364 - server5-2.timeprotab_manager - INFO - 🔌 timeprotab数据库连接池已关闭
2025-07-03 16:38:55,364 - server5-2.timepro_collector - INFO - 🔌 timepro采集服务已停止
2025-07-03 16:38:55,364 - server5-2.test - INFO - ✅ 2025/07/03 + 16:30 + 测试通过: 2025/07/03 + 16:30 + 真实采集工作流
2025-07-03 16:38:56,366 - server5-2.test - INFO - 🔍 2025/07/03 + 16:30 + 运行测试: 2025/07/03 + 16:30 + 实际用户真实采集
2025-07-03 16:38:56,366 - server5-2.test - INFO - 🧪 2025/07/03 + 16:30 + 测试实际用户真实采集
2025-07-03 16:38:56,366 - server5-2.test - INFO - 🔐 2025/07/03 + 16:30 + 测试用户: 215829
2025-07-03 16:38:56,366 - server5-2.test - INFO - 🔑 2025/07/03 + 16:30 + 测试密码: jiaban01
2025-07-03 16:38:56,672 - server5-2.timeprotab_manager - INFO - ✅ timeprotab数据库连接池创建成功
2025-07-03 16:38:56,685 - server5-2.timeprotab_manager - INFO - ✅ 基础分区表 timeprotab 初始化完成
2025-07-03 16:38:56,687 - server5-2.timepro_collector - INFO - 📅 定时任务已设置: 每天 1:00 开始采集
2025-07-03 16:38:56,687 - server5-2.timepro_collector - INFO - ⏰ 调度器线程启动
2025-07-03 16:38:56,687 - server5-2.timepro_collector - INFO - ✅ timepro采集服务启动成功
2025-07-03 16:38:56,687 - server5-2.timepro_collector - INFO - 📅 2025/07/03 + 16:30 + 开始采集指定月份数据: 215829 - ['202506', '202507']
2025-07-03 16:38:56,687 - server5-2.timepro_collector - INFO - 📅 2025/07/03 + 16:30 + 采集月份: 2025/06
2025-07-03 16:38:56,687 - server5-2.timepro_collector - INFO - 📅 采集 215829 的 202506 数据
2025-07-03 16:38:56,687 - server5-2.timepro_collector - INFO - 🌐 2025/07/03 + 16:30 + 真实调用timepro网站: user_id=215829
2025-07-03 16:38:56,691 - server5-2.timepro_collector - ERROR - ❌ 2025/07/03 + 16:30 + timepro数据获取失败: Error accessing login page: HTTPConnectionPool(host='timepro.gongsi.co.jp', port=80): Max retries exceeded with url: /xgweb/login.asp (Caused by NameResolutionError("<urllib3.connection.HTTPConnection object at 0x78932332a180>: Failed to resolve 'timepro.gongsi.co.jp' ([Errno -2] Name or service not known)"))
2025-07-03 16:38:56,691 - server5-2.timepro_collector - ERROR - ❌ 2025/07/03 + 16:30 + HTML数据获取失败: 2025/07/03 + 16:30 + 未获取到HTML数据
2025-07-03 16:38:56,692 - server5-2.timepro_collector - INFO - 📝 生成模拟HTML数据: data/html_files/215829_202506.html
2025-07-03 16:38:56,692 - server5-2.timepro_collector - INFO - 🔄 2025/07/03 + 16:30 + 真实HTML转XML: data/html_files/215829_202506.html -> data/xml_files/215829_202506.xml
2025-07-03 16:38:56,692 - server5-2.timepro_collector - ERROR - ❌ 2025/07/03 + 16:30 + HTML转XML失败: 未生成XML内容
2025-07-03 16:38:56,692 - server5-2.timepro_collector - ERROR - ❌ 2025/07/03 + 16:30 + XML转换失败: 2025/07/03 + 16:30 + HTML转XML失败
2025-07-03 16:38:56,692 - server5-2.timepro_collector - INFO - 📝 生成模拟XML数据: data/xml_files/215829_202506.xml
2025-07-03 16:38:56,693 - server5-2.timepro_collector - INFO - ✅ XML解析完成: 2 条记录
2025-07-03 16:38:56,700 - server5-2.timeprotab_manager - INFO - 📅 分区 timeprotab_2506 已存在
2025-07-03 16:38:56,710 - server5-2.timeprotab_manager - INFO - 📅 分区 timeprotab_2507 已存在
2025-07-03 16:38:56,714 - server5-2.timeprotab_manager - ERROR - ❌ 批量插入失败: invalid input for query argument $18: '' ('str' object has no attribute 'hour')
2025-07-03 16:38:56,714 - server5-2.timepro_collector - ERROR - ❌ 用户月份数据采集失败: invalid input for query argument $18: '' ('str' object has no attribute 'hour')
2025-07-03 16:38:56,714 - server5-2.timepro_collector - ERROR - ❌ 2025/07/03 + 16:30 + 指定月份数据采集失败: invalid input for query argument $18: '' ('str' object has no attribute 'hour')
2025-07-03 16:38:56,714 - server5-2.test - ERROR - ❌ 2025/07/03 + 16:30 + 实际用户真实采集失败: invalid input for query argument $18: '' ('str' object has no attribute 'hour')
2025-07-03 16:38:56,718 - server5-2.timeprotab_manager - INFO - 🔌 timeprotab数据库连接池已关闭
2025-07-03 16:38:56,718 - server5-2.timepro_collector - INFO - 🔌 timepro采集服务已停止
2025-07-03 16:38:56,718 - server5-2.test - ERROR - ❌ 2025/07/03 + 16:30 + 测试失败: 2025/07/03 + 16:30 + 实际用户真实采集
2025-07-03 16:38:57,719 - server5-2.test - INFO - ============================================================
2025-07-03 16:38:57,720 - server5-2.test - INFO - 📊 2025/07/03 + 16:30 + 测试结果汇总:
2025-07-03 16:38:57,720 - server5-2.test - INFO -   2025/07/03 + 16:30 + 数据库操作: ❌ FAIL
2025-07-03 16:38:57,720 - server5-2.test - INFO -   2025/07/03 + 16:30 + 分区管理: ✅ PASS
2025-07-03 16:38:57,720 - server5-2.test - INFO -   2025/07/03 + 16:30 + XML解析: ❌ FAIL
2025-07-03 16:38:57,721 - server5-2.test - INFO -   2025/07/03 + 16:30 + 真实采集工作流: ✅ PASS
2025-07-03 16:38:57,721 - server5-2.test - INFO -   2025/07/03 + 16:30 + 实际用户真实采集: ❌ FAIL
2025-07-03 16:38:57,721 - server5-2.test - INFO - 2025/07/03 + 16:30 + 总计: 2/5 测试通过
2025-07-03 16:38:57,721 - server5-2.test - INFO - ============================================================
2025-07-03 16:38:57,726 - server5-2.timeprotab_manager - INFO - 🔌 timeprotab数据库连接池已关闭
2025-07-03 16:38:57,726 - server5-2.test - INFO - ✅ 测试环境清理完成
2025-07-03 16:38:57,726 - server5-2.test - ERROR - ❌ 2025/07/03 + 16:30 + 部分测试失败
2025-07-03 16:52:35,636 - server5-2.test - INFO - 🧪 server5-2 测试工具启动（真实采集）
2025-07-03 16:52:35,636 - server5-2.test - INFO - ============================================================
2025-07-03 16:52:35,636 - server5-2.test - INFO - 🎯 开始运行所有测试
2025-07-03 16:52:35,636 - server5-2.test - INFO - 🧪 设置测试环境
2025-07-03 16:52:35,907 - server5-2.timeprotab_manager - INFO - ✅ timeprotab数据库连接池创建成功
2025-07-03 16:52:35,923 - server5-2.timeprotab_manager - INFO - ✅ 基础分区表 timeprotab 初始化完成
2025-07-03 16:52:35,924 - server5-2.test - INFO - ✅ 测试环境设置完成
2025-07-03 16:52:35,924 - server5-2.test - INFO - 🔍 运行测试: 数据库操作
2025-07-03 16:52:35,924 - server5-2.test - INFO - 🧪 测试数据库操作
2025-07-03 16:52:35,931 - server5-2.timeprotab_manager - INFO - 📅 分区 timeprotab_2506 已存在
2025-07-03 16:52:35,932 - server5-2.test - INFO - ✅ 分区创建测试成功: 2506
2025-07-03 16:52:35,934 - server5-2.timeprotab_manager - INFO - 📅 分区 timeprotab_2507 已存在
2025-07-03 16:52:35,934 - server5-2.test - INFO - ✅ 分区创建测试成功: 2507
2025-07-03 16:52:35,943 - server5-2.timeprotab_manager - INFO - 📅 分区 timeprotab_2506 已存在
2025-07-03 16:52:35,950 - server5-2.timeprotab_manager - INFO - 📅 分区 timeprotab_2507 已存在
2025-07-03 16:52:35,954 - server5-2.timeprotab_manager - INFO - ✅ 批量插入完成: 2 条记录
2025-07-03 16:52:35,954 - server5-2.test - INFO - ✅ 数据插入测试成功: 2 条记录
2025-07-03 16:52:35,959 - server5-2.timeprotab_manager - INFO - 📊 查询到 4 条记录: 215829 - 2506
2025-07-03 16:52:35,959 - server5-2.test - INFO - ✅ 数据查询测试成功 (2506): 4 条记录
2025-07-03 16:52:35,960 - server5-2.test - INFO - 📊 查询记录: 215829 - 2025-06-01 - 测试数据 - 实际用户215829 - 6月
2025-07-03 16:52:35,960 - server5-2.test - INFO - 📊 查询记录: 215829 - 2025-06-01 - XML解析测试 - 6月
2025-07-03 16:52:35,962 - server5-2.timeprotab_manager - INFO - 📊 查询到 9 条记录: 215829 - 2507
2025-07-03 16:52:35,963 - server5-2.test - INFO - ✅ 数据查询测试成功 (2507): 9 条记录
2025-07-03 16:52:35,963 - server5-2.test - INFO - 📊 查询记录: 215829 - 2025-07-01 - 2025/07/03 + 16:25 + 测试数据 - 实际用户215829
2025-07-03 16:52:35,963 - server5-2.test - INFO - 📊 查询记录: 215829 - 2025-07-01 - 测试数据 - 实际用户215829 - 7月
2025-07-03 16:52:35,969 - server5-2.test - INFO - ✅ 状态获取测试成功
2025-07-03 16:52:35,969 - server5-2.test - INFO - ✅ 测试通过: 数据库操作
2025-07-03 16:52:36,971 - server5-2.test - INFO - 🔍 运行测试: 分区管理
2025-07-03 16:52:36,971 - server5-2.test - INFO - 🧪 测试分区管理
2025-07-03 16:52:36,972 - server5-2.timeprotab_manager - INFO - 📅 分区 timeprotab_2506 已存在
2025-07-03 16:52:36,973 - server5-2.test - INFO - ✅ 分区创建成功: 2506
2025-07-03 16:52:36,975 - server5-2.timeprotab_manager - INFO - 📅 分区 timeprotab_2507 已存在
2025-07-03 16:52:36,976 - server5-2.test - INFO - ✅ 分区创建成功: 2507
2025-07-03 16:52:36,978 - server5-2.test - INFO - ✅ 分区管理测试成功: 创建了 2 个分区
2025-07-03 16:52:36,978 - server5-2.test - INFO - ✅ 测试通过: 分区管理
2025-07-03 16:52:37,980 - server5-2.test - INFO - 🔍 运行测试: XML解析
2025-07-03 16:52:37,980 - server5-2.test - INFO - 🧪 测试XML解析
2025-07-03 16:52:37,982 - server5-2.timepro_collector - INFO - ✅ XML解析完成: 2 条记录
2025-07-03 16:52:37,982 - server5-2.test - INFO - ✅ XML解析测试成功: 2 条记录
2025-07-03 16:52:37,985 - server5-2.timeprotab_manager - INFO - 📅 分区 timeprotab_2506 已存在
2025-07-03 16:52:37,988 - server5-2.timeprotab_manager - INFO - 📅 分区 timeprotab_2507 已存在
2025-07-03 16:52:37,991 - server5-2.timeprotab_manager - INFO - ✅ 批量插入完成: 2 条记录
2025-07-03 16:52:37,991 - server5-2.test - INFO - ✅ 解析数据插入测试成功: 2 条记录
2025-07-03 16:52:37,995 - server5-2.test - INFO - ✅ 测试通过: XML解析
2025-07-03 16:52:38,996 - server5-2.test - INFO - 🔍 运行测试: 真实采集工作流
2025-07-03 16:52:38,997 - server5-2.test - INFO - 🧪 测试真实采集工作流
2025-07-03 16:52:39,210 - server5-2.timeprotab_manager - INFO - ✅ timeprotab数据库连接池创建成功
2025-07-03 16:52:39,225 - server5-2.timeprotab_manager - INFO - ✅ 基础分区表 timeprotab 初始化完成
2025-07-03 16:52:39,226 - server5-2.timepro_collector - INFO - 📅 定时任务已设置: 每天 1:00 开始采集
2025-07-03 16:52:39,226 - server5-2.timepro_collector - INFO - ⏰ 调度器线程启动
2025-07-03 16:52:39,226 - server5-2.timepro_collector - INFO - ✅ timepro采集服务启动成功
2025-07-03 16:52:39,226 - server5-2.timepro_collector - INFO - 🧪 开始真实采集工作流
2025-07-03 16:52:39,226 - server5-2.timepro_collector - INFO - 📅 采集 215829 的 202507 数据
2025-07-03 16:52:39,226 - server5-2.timepro_collector - INFO - 🌐 真实调用timepro网站: user_id=215829
2025-07-03 16:52:39,231 - server5-2.timepro_collector - ERROR - ❌ timepro数据获取失败: Error accessing login page: HTTPConnectionPool(host='timepro.gongsi.co.jp', port=80): Max retries exceeded with url: /xgweb/login.asp (Caused by NameResolutionError("<urllib3.connection.HTTPConnection object at 0x710822b320c0>: Failed to resolve 'timepro.gongsi.co.jp' ([Errno -2] Name or service not known)"))
2025-07-03 16:52:39,231 - server5-2.timepro_collector - ERROR - ❌ 真实数据获取失败: 未获取到HTML数据
2025-07-03 16:52:39,231 - server5-2.timepro_collector - INFO - 🔄 使用模拟数据作为备选
2025-07-03 16:52:39,232 - server5-2.timepro_collector - INFO - 📝 生成模拟HTML数据: data/html_files/215829_202507.html
2025-07-03 16:52:39,232 - server5-2.timepro_collector - INFO - 🔄 真实HTML转XML: data/html_files/215829_202507.html -> data/xml_files/215829_202507.xml
2025-07-03 16:52:39,232 - server5-2.timepro_collector - ERROR - ❌ HTML转XML失败: 未生成XML内容
2025-07-03 16:52:39,232 - server5-2.timepro_collector - ERROR - ❌ HTML转XML失败: HTML转XML失败
2025-07-03 16:52:39,232 - server5-2.timepro_collector - INFO - 🔄 使用模拟XML数据作为备选
2025-07-03 16:52:39,233 - server5-2.timepro_collector - INFO - 📝 生成模拟XML数据: data/xml_files/215829_202507.xml
2025-07-03 16:52:39,233 - server5-2.timepro_collector - INFO - ✅ XML解析完成: 2 条记录
2025-07-03 16:52:39,242 - server5-2.timeprotab_manager - INFO - 📅 分区 timeprotab_2507 已存在
2025-07-03 16:52:39,252 - server5-2.timeprotab_manager - INFO - 📅 分区 timeprotab_2507 已存在
2025-07-03 16:52:39,258 - server5-2.timeprotab_manager - INFO - 📅 分区 timeprotab_2507 已存在
2025-07-03 16:52:39,261 - server5-2.timeprotab_manager - INFO - ✅ 批量插入完成: 2 条记录
2025-07-03 16:52:39,261 - server5-2.timepro_collector - INFO - ✅ 数据插入成功: 2 条记录
2025-07-03 16:52:39,261 - server5-2.timepro_collector - INFO - ✅ 真实采集工作流完成
2025-07-03 16:52:39,271 - server5-2.test - INFO - ✅ 真实采集工作流测试成功
2025-07-03 16:52:39,276 - server5-2.timeprotab_manager - INFO - 🔌 timeprotab数据库连接池已关闭
2025-07-03 16:52:39,276 - server5-2.timepro_collector - INFO - 🔌 timepro采集服务已停止
2025-07-03 16:52:39,276 - server5-2.test - INFO - ✅ 测试通过: 真实采集工作流
2025-07-03 16:52:40,277 - server5-2.test - INFO - 🔍 运行测试: 实际用户真实采集
2025-07-03 16:52:40,278 - server5-2.test - INFO - 🧪 测试实际用户真实采集
2025-07-03 16:52:40,278 - server5-2.test - INFO - 🔐 测试用户: 215829
2025-07-03 16:52:40,278 - server5-2.test - INFO - 🔑 测试密码: jiaban01
2025-07-03 16:52:40,565 - server5-2.timeprotab_manager - INFO - ✅ timeprotab数据库连接池创建成功
2025-07-03 16:52:40,582 - server5-2.timeprotab_manager - INFO - ✅ 基础分区表 timeprotab 初始化完成
2025-07-03 16:52:40,582 - server5-2.timepro_collector - INFO - 📅 定时任务已设置: 每天 1:00 开始采集
2025-07-03 16:52:40,583 - server5-2.timepro_collector - INFO - ⏰ 调度器线程启动
2025-07-03 16:52:40,583 - server5-2.timepro_collector - INFO - ✅ timepro采集服务启动成功
2025-07-03 16:52:40,583 - server5-2.timepro_collector - INFO - 📅 开始采集指定月份数据: 215829 - ['202506', '202507']
2025-07-03 16:52:40,583 - server5-2.timepro_collector - INFO - 📅 采集月份: 2025/06
2025-07-03 16:52:40,583 - server5-2.timepro_collector - INFO - 📅 采集 215829 的 202506 数据
2025-07-03 16:52:40,583 - server5-2.timepro_collector - INFO - 🌐 真实调用timepro网站: user_id=215829
2025-07-03 16:52:40,587 - server5-2.timepro_collector - ERROR - ❌ timepro数据获取失败: Error accessing login page: HTTPConnectionPool(host='timepro.gongsi.co.jp', port=80): Max retries exceeded with url: /xgweb/login.asp (Caused by NameResolutionError("<urllib3.connection.HTTPConnection object at 0x710822b32b70>: Failed to resolve 'timepro.gongsi.co.jp' ([Errno -2] Name or service not known)"))
2025-07-03 16:52:40,587 - server5-2.timepro_collector - ERROR - ❌ 真实数据获取失败: 未获取到HTML数据
2025-07-03 16:52:40,587 - server5-2.timepro_collector - INFO - 🔄 使用模拟数据作为备选
2025-07-03 16:52:40,587 - server5-2.timepro_collector - INFO - 📝 生成模拟HTML数据: data/html_files/215829_202506.html
2025-07-03 16:52:40,587 - server5-2.timepro_collector - INFO - 🔄 真实HTML转XML: data/html_files/215829_202506.html -> data/xml_files/215829_202506.xml
2025-07-03 16:52:40,588 - server5-2.timepro_collector - ERROR - ❌ HTML转XML失败: 未生成XML内容
2025-07-03 16:52:40,588 - server5-2.timepro_collector - ERROR - ❌ HTML转XML失败: HTML转XML失败
2025-07-03 16:52:40,588 - server5-2.timepro_collector - INFO - 🔄 使用模拟XML数据作为备选
2025-07-03 16:52:40,588 - server5-2.timepro_collector - INFO - 📝 生成模拟XML数据: data/xml_files/215829_202506.xml
2025-07-03 16:52:40,588 - server5-2.timepro_collector - INFO - ✅ XML解析完成: 2 条记录
2025-07-03 16:52:40,596 - server5-2.timeprotab_manager - INFO - 📅 分区 timeprotab_2506 已存在
2025-07-03 16:52:40,605 - server5-2.timeprotab_manager - INFO - 📅 分区 timeprotab_2507 已存在
2025-07-03 16:52:40,611 - server5-2.timeprotab_manager - INFO - 📅 分区 timeprotab_2507 已存在
2025-07-03 16:52:40,614 - server5-2.timeprotab_manager - INFO - ✅ 批量插入完成: 2 条记录
2025-07-03 16:52:40,614 - server5-2.timepro_collector - INFO - ✅ 数据插入成功: 2 条记录
2025-07-03 16:52:50,624 - server5-2.timepro_collector - INFO - 📅 采集月份: 2025/07
2025-07-03 16:52:50,624 - server5-2.timepro_collector - INFO - 📅 采集 215829 的 202507 数据
2025-07-03 16:52:50,624 - server5-2.timepro_collector - INFO - 🌐 真实调用timepro网站: user_id=215829
2025-07-03 16:52:50,628 - server5-2.timepro_collector - ERROR - ❌ timepro数据获取失败: Error accessing login page: HTTPConnectionPool(host='timepro.gongsi.co.jp', port=80): Max retries exceeded with url: /xgweb/login.asp (Caused by NameResolutionError("<urllib3.connection.HTTPConnection object at 0x710822b33470>: Failed to resolve 'timepro.gongsi.co.jp' ([Errno -2] Name or service not known)"))
2025-07-03 16:52:50,628 - server5-2.timepro_collector - ERROR - ❌ 真实数据获取失败: 未获取到HTML数据
2025-07-03 16:52:50,628 - server5-2.timepro_collector - INFO - 🔄 使用模拟数据作为备选
2025-07-03 16:52:50,628 - server5-2.timepro_collector - INFO - 📝 生成模拟HTML数据: data/html_files/215829_202507.html
2025-07-03 16:52:50,629 - server5-2.timepro_collector - INFO - 🔄 真实HTML转XML: data/html_files/215829_202507.html -> data/xml_files/215829_202507.xml
2025-07-03 16:52:50,630 - server5-2.timepro_collector - ERROR - ❌ HTML转XML失败: 未生成XML内容
2025-07-03 16:52:50,630 - server5-2.timepro_collector - ERROR - ❌ HTML转XML失败: HTML转XML失败
2025-07-03 16:52:50,630 - server5-2.timepro_collector - INFO - 🔄 使用模拟XML数据作为备选
2025-07-03 16:52:50,630 - server5-2.timepro_collector - INFO - 📝 生成模拟XML数据: data/xml_files/215829_202507.xml
2025-07-03 16:52:50,630 - server5-2.timepro_collector - INFO - ✅ XML解析完成: 2 条记录
2025-07-03 16:52:50,632 - server5-2.timeprotab_manager - INFO - 📅 分区 timeprotab_2507 已存在
2025-07-03 16:52:50,634 - server5-2.timeprotab_manager - INFO - 📅 分区 timeprotab_2507 已存在
2025-07-03 16:52:50,637 - server5-2.timeprotab_manager - INFO - 📅 分区 timeprotab_2507 已存在
2025-07-03 16:52:50,640 - server5-2.timeprotab_manager - INFO - ✅ 批量插入完成: 2 条记录
2025-07-03 16:52:50,640 - server5-2.timepro_collector - INFO - ✅ 数据插入成功: 2 条记录
2025-07-03 16:53:00,650 - server5-2.timepro_collector - INFO - ✅ 指定月份数据采集完成: 215829
2025-07-03 16:53:00,651 - server5-2.test - INFO - ✅ 实际用户真实采集测试成功
2025-07-03 16:53:00,654 - server5-2.timeprotab_manager - INFO - 📊 查询到 5 条记录: 215829 - 2506
2025-07-03 16:53:00,656 - server5-2.test - INFO - ✅ 验证采集数据成功 (2506): 5 条记录
2025-07-03 16:53:00,656 - server5-2.test - INFO - 📊 采集记录: 215829 - 2025-06-01 - 测试数据 - 实际用户215829 - 6月
2025-07-03 16:53:00,657 - server5-2.test - INFO - 📊 采集记录: 215829 - 2025-06-01 - XML解析测试 - 6月
2025-07-03 16:53:00,657 - server5-2.test - INFO - 📊 采集记录: 215829 - 2025-06-01 - 测试数据 - 实际用户215829 - 6月
2025-07-03 16:53:00,660 - server5-2.timeprotab_manager - INFO - 📊 查询到 16 条记录: 215829 - 2507
2025-07-03 16:53:00,661 - server5-2.test - INFO - ✅ 验证采集数据成功 (2507): 16 条记录
2025-07-03 16:53:00,662 - server5-2.test - INFO - 📊 采集记录: 215829 - 2025-07-01 - 2025/07/03 + 16:25 + 测试数据 - 实际用户215829
2025-07-03 16:53:00,662 - server5-2.test - INFO - 📊 采集记录: 215829 - 2025-07-01 - 测试数据 - 实际用户215829 - 7月
2025-07-03 16:53:00,662 - server5-2.test - INFO - 📊 采集记录: 215829 - 2025-07-01 - XML解析测试 - 7月
2025-07-03 16:53:00,668 - server5-2.timeprotab_manager - INFO - 🔌 timeprotab数据库连接池已关闭
2025-07-03 16:53:00,668 - server5-2.timepro_collector - INFO - 🔌 timepro采集服务已停止
2025-07-03 16:53:00,669 - server5-2.test - INFO - ✅ 测试通过: 实际用户真实采集
2025-07-03 16:53:01,670 - server5-2.test - INFO - ============================================================
2025-07-03 16:53:01,670 - server5-2.test - INFO - 📊 测试结果汇总:
2025-07-03 16:53:01,670 - server5-2.test - INFO -   数据库操作: ✅ PASS
2025-07-03 16:53:01,670 - server5-2.test - INFO -   分区管理: ✅ PASS
2025-07-03 16:53:01,670 - server5-2.test - INFO -   XML解析: ✅ PASS
2025-07-03 16:53:01,670 - server5-2.test - INFO -   真实采集工作流: ✅ PASS
2025-07-03 16:53:01,670 - server5-2.test - INFO -   实际用户真实采集: ✅ PASS
2025-07-03 16:53:01,670 - server5-2.test - INFO - 总计: 5/5 测试通过
2025-07-03 16:53:01,670 - server5-2.test - INFO - ============================================================
2025-07-03 16:53:01,673 - server5-2.timeprotab_manager - INFO - 🔌 timeprotab数据库连接池已关闭
2025-07-03 16:53:01,673 - server5-2.test - INFO - ✅ 测试环境清理完成
2025-07-03 16:53:01,673 - server5-2.test - INFO - 🎉 所有测试通过！
2025-07-03 17:23:16,731 - server5-2.test - INFO - 🧪 server5-2 测试工具启动 (真实、无文件采集)
2025-07-03 17:23:16,732 - server5-2.test - INFO - ============================================================
2025-07-03 17:23:16,732 - server5-2.test - INFO - 🎯 开始运行所有测试 (无文件内存流)
2025-07-03 17:23:16,732 - server5-2.test - INFO - 🧪 设置测试环境
2025-07-03 17:23:17,073 - server5-2.timeprotab_manager - INFO - ✅ timeprotab数据库连接池创建成功
2025-07-03 17:23:17,073 - server5-2.test - INFO - 为了确保数据库约束和表结构最新，将删除并重建 timeprotab 表...
2025-07-03 17:23:17,100 - server5-2.test - INFO - 旧表 timeprotab 已删除。
2025-07-03 17:23:17,117 - server5-2.timeprotab_manager - INFO - ✅ 基础分区表 timeprotab 初始化完成
2025-07-03 17:23:17,118 - server5-2.test - INFO - ✅ 测试环境设置完成
2025-07-03 17:23:17,118 - server5-2.test - INFO - 
==================== 数据库连接 ====================
2025-07-03 17:23:17,118 - server5-2.test - INFO - 🧪 (1/3) 测试数据库连接
2025-07-03 17:23:17,126 - server5-2.test - INFO - ✅ 数据库连接和状态获取测试成功
2025-07-03 17:23:18,127 - server5-2.test - INFO - 
==================== 采集器初始化 ====================
2025-07-03 17:23:18,127 - server5-2.test - INFO - 🧪 (2/3) 测试采集器初始化和关闭
2025-07-03 17:23:18,346 - server5-2.timeprotab_manager - INFO - ✅ timeprotab数据库连接池创建成功
2025-07-03 17:23:18,361 - server5-2.timeprotab_manager - INFO - ✅ 基础分区表 timeprotab 初始化完成
2025-07-03 17:23:18,365 - server5-2.timepro_collector - INFO - ✅ timepro采集服务启动成功
2025-07-03 17:23:18,365 - server5-2.timepro_collector - INFO - 📅 定时任务已设置: 每天 01:00 开始采集
2025-07-03 17:23:18,365 - server5-2.timepro_collector - INFO - ⏰ 调度器线程启动
2025-07-03 17:23:18,365 - server5-2.test - INFO - ✅ 采集器启动成功
2025-07-03 17:23:18,369 - server5-2.timeprotab_manager - INFO - 🔌 timeprotab数据库连接池已关闭
2025-07-03 17:23:18,370 - server5-2.timepro_collector - INFO - 🔌 timepro采集服务已停止
2025-07-03 17:23:18,370 - server5-2.test - INFO - ✅ 采集器停止成功
2025-07-03 17:23:19,371 - server5-2.test - INFO - 
==================== 核心功能: 采集2025年6月真实数据 ====================
2025-07-03 17:23:19,371 - server5-2.test - INFO - 🧪 (3/3) 核心测试: 采集2025年6月的真实数据
2025-07-03 17:23:19,371 - server5-2.test - INFO - 重新启动采集器以进行核心测试...
2025-07-03 17:23:19,581 - server5-2.timeprotab_manager - INFO - ✅ timeprotab数据库连接池创建成功
2025-07-03 17:23:19,595 - server5-2.timeprotab_manager - INFO - ✅ 基础分区表 timeprotab 初始化完成
2025-07-03 17:23:19,596 - server5-2.timepro_collector - INFO - ✅ timepro采集服务启动成功
2025-07-03 17:23:19,596 - server5-2.timepro_collector - INFO - 📅 定时任务已设置: 每天 01:00 开始采集
2025-07-03 17:23:19,596 - server5-2.timepro_collector - INFO - ⏰ 调度器线程启动
2025-07-03 17:23:19,596 - server5-2.test - INFO - 🔐 测试用户: 215829
2025-07-03 17:23:19,596 - server5-2.test - INFO - 📅 开始采集指定月份: 202506
2025-07-03 17:23:19,596 - server5-2.timepro_collector - INFO - 📅 开始为用户 '215829' 采集 202506 的数据
2025-07-03 17:23:19,596 - server5-2.timepro_collector - INFO - 🌐 正在调用timepro.py获取 202506 的数据, 用户: 215829
2025-07-03 17:23:20,810 - server5-2.timepro_collector - INFO - ✅ timepro.py 数据获取成功。
2025-07-03 17:23:20,810 - server5-2.timepro_collector - INFO - ✅ 成功在内存中获取HTML内容。
2025-07-03 17:23:20,828 - server5-2.timepro_collector - INFO - ✅ 成功从HTML解析了 30 条记录。
2025-07-03 17:23:20,878 - server5-2.timeprotab_manager - INFO - ✅ 创建分区成功: timeprotab_2506 (2025-06-01 ~ 2025-07-01)
2025-07-03 17:23:20,889 - server5-2.timeprotab_manager - INFO - 📅 分区 timeprotab_2506 已存在
2025-07-03 17:23:20,898 - server5-2.timeprotab_manager - INFO - 📅 分区 timeprotab_2506 已存在
2025-07-03 17:23:20,901 - server5-2.timeprotab_manager - INFO - 📅 分区 timeprotab_2506 已存在
2025-07-03 17:23:20,903 - server5-2.timeprotab_manager - INFO - 📅 分区 timeprotab_2506 已存在
2025-07-03 17:23:20,905 - server5-2.timeprotab_manager - INFO - 📅 分区 timeprotab_2506 已存在
2025-07-03 17:23:20,908 - server5-2.timeprotab_manager - INFO - 📅 分区 timeprotab_2506 已存在
2025-07-03 17:23:20,910 - server5-2.timeprotab_manager - INFO - 📅 分区 timeprotab_2506 已存在
2025-07-03 17:23:20,912 - server5-2.timeprotab_manager - INFO - 📅 分区 timeprotab_2506 已存在
2025-07-03 17:23:20,914 - server5-2.timeprotab_manager - INFO - 📅 分区 timeprotab_2506 已存在
2025-07-03 17:23:20,916 - server5-2.timeprotab_manager - INFO - 📅 分区 timeprotab_2506 已存在
2025-07-03 17:23:20,918 - server5-2.timeprotab_manager - INFO - 📅 分区 timeprotab_2506 已存在
2025-07-03 17:23:20,920 - server5-2.timeprotab_manager - INFO - 📅 分区 timeprotab_2506 已存在
2025-07-03 17:23:20,922 - server5-2.timeprotab_manager - INFO - 📅 分区 timeprotab_2506 已存在
2025-07-03 17:23:20,924 - server5-2.timeprotab_manager - INFO - 📅 分区 timeprotab_2506 已存在
2025-07-03 17:23:20,927 - server5-2.timeprotab_manager - INFO - 📅 分区 timeprotab_2506 已存在
2025-07-03 17:23:20,929 - server5-2.timeprotab_manager - INFO - 📅 分区 timeprotab_2506 已存在
2025-07-03 17:23:20,931 - server5-2.timeprotab_manager - INFO - 📅 分区 timeprotab_2506 已存在
2025-07-03 17:23:20,933 - server5-2.timeprotab_manager - INFO - 📅 分区 timeprotab_2506 已存在
2025-07-03 17:23:20,935 - server5-2.timeprotab_manager - INFO - 📅 分区 timeprotab_2506 已存在
2025-07-03 17:23:20,937 - server5-2.timeprotab_manager - INFO - 📅 分区 timeprotab_2506 已存在
2025-07-03 17:23:20,939 - server5-2.timeprotab_manager - INFO - 📅 分区 timeprotab_2506 已存在
2025-07-03 17:23:20,941 - server5-2.timeprotab_manager - INFO - 📅 分区 timeprotab_2506 已存在
2025-07-03 17:23:20,943 - server5-2.timeprotab_manager - INFO - 📅 分区 timeprotab_2506 已存在
2025-07-03 17:23:20,945 - server5-2.timeprotab_manager - INFO - 📅 分区 timeprotab_2506 已存在
2025-07-03 17:23:20,947 - server5-2.timeprotab_manager - INFO - 📅 分区 timeprotab_2506 已存在
2025-07-03 17:23:20,949 - server5-2.timeprotab_manager - INFO - 📅 分区 timeprotab_2506 已存在
2025-07-03 17:23:20,952 - server5-2.timeprotab_manager - INFO - 📅 分区 timeprotab_2506 已存在
2025-07-03 17:23:20,954 - server5-2.timeprotab_manager - INFO - 📅 分区 timeprotab_2506 已存在
2025-07-03 17:23:20,956 - server5-2.timeprotab_manager - INFO - 📅 分区 timeprotab_2506 已存在
2025-07-03 17:23:20,958 - server5-2.timeprotab_manager - INFO - 📅 分区 timeprotab_2506 已存在
2025-07-03 17:23:20,961 - server5-2.timeprotab_manager - INFO - ✅ 批量插入/更新了 30 条记录
2025-07-03 17:23:20,961 - server5-2.timepro_collector - INFO - ✅ 数据库插入成功: 30 条记录 (202506)。
2025-07-03 17:23:20,961 - server5-2.test - INFO - ✅ 指定月份真实数据采集调用完成
2025-07-03 17:23:20,961 - server5-2.test - INFO - 🔍 正在从数据库验证 2506 月份的数据...
2025-07-03 17:23:22,966 - server5-2.timeprotab_manager - INFO - 📊 查询到 30 条记录: 215829 - 2506
2025-07-03 17:23:22,966 - server5-2.test - INFO - 🎉 验证成功! 在数据库中找到 30 条记录 (2506)。
2025-07-03 17:23:22,967 - server5-2.test - INFO - --- 前5条记录示例 ---
2025-07-03 17:23:22,967 - server5-2.test - INFO -   - 日期: 2025-06-01, 出勤: None, コメント: 
2025-07-03 17:23:22,967 - server5-2.test - INFO -   - 日期: 2025-06-02, 出勤: 07:21:00, コメント: 計測システム開発
2025-07-03 17:23:22,967 - server5-2.test - INFO -   - 日期: 2025-06-03, 出勤: 07:18:00, コメント: 計測システム開発
2025-07-03 17:23:22,967 - server5-2.test - INFO -   - 日期: 2025-06-04, 出勤: 07:24:00, コメント: 計測システム開発
2025-07-03 17:23:22,967 - server5-2.test - INFO -   - 日期: 2025-06-05, 出勤: 07:18:00, コメント: 計測システム開発
2025-07-03 17:23:22,967 - server5-2.test - INFO - --------------------
2025-07-03 17:23:23,968 - server5-2.test - INFO - 
==================================================
2025-07-03 17:23:23,968 - server5-2.test - INFO - 📊 测试结果汇总:
2025-07-03 17:23:23,968 - server5-2.test - INFO -   - 数据库连接: ✅ 通过
2025-07-03 17:23:23,968 - server5-2.test - INFO -   - 采集器初始化: ✅ 通过
2025-07-03 17:23:23,968 - server5-2.test - INFO -   - 核心功能: 采集2025年6月真实数据: ✅ 通过
2025-07-03 17:23:23,968 - server5-2.test - INFO - 总计: 3/3 测试通过
2025-07-03 17:23:23,968 - server5-2.test - INFO - ==================================================
2025-07-03 17:23:23,972 - server5-2.timeprotab_manager - INFO - 🔌 timeprotab数据库连接池已关闭
2025-07-03 17:23:23,973 - server5-2.timepro_collector - INFO - 🔌 timepro采集服务已停止
2025-07-03 17:23:23,973 - server5-2.timeprotab_manager - INFO - 🔌 timeprotab数据库连接池已关闭
2025-07-03 17:23:23,973 - server5-2.test - INFO - ✅ 测试环境清理完成
2025-07-03 17:23:23,973 - server5-2.test - INFO - 🎉 所有测试通过！
2025-07-03 17:23:51,322 - server5-2.test - INFO - 🧪 server5-2 测试工具启动 (真实、无文件采集)
2025-07-03 17:23:51,322 - server5-2.test - INFO - ============================================================
2025-07-03 17:23:51,322 - server5-2.test - INFO - 🎯 开始运行所有测试 (无文件内存流)
2025-07-03 17:23:51,322 - server5-2.test - INFO - 🧪 设置测试环境
2025-07-03 17:23:51,528 - server5-2.timeprotab_manager - INFO - ✅ timeprotab数据库连接池创建成功
2025-07-03 17:23:51,528 - server5-2.test - INFO - 为了确保数据库约束和表结构最新，将删除并重建 timeprotab 表...
2025-07-03 17:23:51,554 - server5-2.test - INFO - 旧表 timeprotab 已删除。
2025-07-03 17:23:51,572 - server5-2.timeprotab_manager - INFO - ✅ 基础分区表 timeprotab 初始化完成
2025-07-03 17:23:51,573 - server5-2.test - INFO - ✅ 测试环境设置完成
2025-07-03 17:23:51,574 - server5-2.test - INFO - 
==================== 数据库连接 ====================
2025-07-03 17:23:51,574 - server5-2.test - INFO - 🧪 (1/3) 测试数据库连接
2025-07-03 17:23:51,582 - server5-2.test - INFO - ✅ 数据库连接和状态获取测试成功
2025-07-03 17:23:52,583 - server5-2.test - INFO - 
==================== 采集器初始化 ====================
2025-07-03 17:23:52,583 - server5-2.test - INFO - 🧪 (2/3) 测试采集器初始化和关闭
2025-07-03 17:23:52,788 - server5-2.timeprotab_manager - INFO - ✅ timeprotab数据库连接池创建成功
2025-07-03 17:23:52,801 - server5-2.timeprotab_manager - INFO - ✅ 基础分区表 timeprotab 初始化完成
2025-07-03 17:23:52,802 - server5-2.timepro_collector - INFO - ✅ timepro采集服务启动成功
2025-07-03 17:23:52,802 - server5-2.timepro_collector - INFO - 📅 定时任务已设置: 每天 01:00 开始采集
2025-07-03 17:23:52,802 - server5-2.timepro_collector - INFO - ⏰ 调度器线程启动
2025-07-03 17:23:52,802 - server5-2.test - INFO - ✅ 采集器启动成功
2025-07-03 17:23:52,806 - server5-2.timeprotab_manager - INFO - 🔌 timeprotab数据库连接池已关闭
2025-07-03 17:23:52,806 - server5-2.timepro_collector - INFO - 🔌 timepro采集服务已停止
2025-07-03 17:23:52,806 - server5-2.test - INFO - ✅ 采集器停止成功
2025-07-03 17:23:53,807 - server5-2.test - INFO - 
==================== 核心功能: 采集2025年6月真实数据 ====================
2025-07-03 17:23:53,807 - server5-2.test - INFO - 🧪 (3/3) 核心测试: 采集2025年6月的真实数据
2025-07-03 17:23:53,807 - server5-2.test - INFO - 重新启动采集器以进行核心测试...
2025-07-03 17:23:54,054 - server5-2.timeprotab_manager - INFO - ✅ timeprotab数据库连接池创建成功
2025-07-03 17:23:54,071 - server5-2.timeprotab_manager - INFO - ✅ 基础分区表 timeprotab 初始化完成
2025-07-03 17:23:54,072 - server5-2.timepro_collector - INFO - ✅ timepro采集服务启动成功
2025-07-03 17:23:54,073 - server5-2.timepro_collector - INFO - 📅 定时任务已设置: 每天 01:00 开始采集
2025-07-03 17:23:54,073 - server5-2.timepro_collector - INFO - ⏰ 调度器线程启动
2025-07-03 17:23:54,073 - server5-2.test - INFO - 🔐 测试用户: 215829
2025-07-03 17:23:54,073 - server5-2.test - INFO - 📅 开始采集指定月份: 202505
2025-07-03 17:23:54,073 - server5-2.timepro_collector - INFO - 📅 开始为用户 '215829' 采集 202505 的数据
2025-07-03 17:23:54,073 - server5-2.timepro_collector - INFO - 🌐 正在调用timepro.py获取 202505 的数据, 用户: 215829
2025-07-03 17:23:55,283 - server5-2.timepro_collector - INFO - ✅ timepro.py 数据获取成功。
2025-07-03 17:23:55,283 - server5-2.timepro_collector - INFO - ✅ 成功在内存中获取HTML内容。
2025-07-03 17:23:55,303 - server5-2.timepro_collector - INFO - ✅ 成功从HTML解析了 31 条记录。
2025-07-03 17:23:55,360 - server5-2.timeprotab_manager - INFO - ✅ 创建分区成功: timeprotab_2505 (2025-05-01 ~ 2025-06-01)
2025-07-03 17:23:55,372 - server5-2.timeprotab_manager - INFO - 📅 分区 timeprotab_2505 已存在
2025-07-03 17:23:55,382 - server5-2.timeprotab_manager - INFO - 📅 分区 timeprotab_2505 已存在
2025-07-03 17:23:55,385 - server5-2.timeprotab_manager - INFO - 📅 分区 timeprotab_2505 已存在
2025-07-03 17:23:55,388 - server5-2.timeprotab_manager - INFO - 📅 分区 timeprotab_2505 已存在
2025-07-03 17:23:55,390 - server5-2.timeprotab_manager - INFO - 📅 分区 timeprotab_2505 已存在
2025-07-03 17:23:55,393 - server5-2.timeprotab_manager - INFO - 📅 分区 timeprotab_2505 已存在
2025-07-03 17:23:55,395 - server5-2.timeprotab_manager - INFO - 📅 分区 timeprotab_2505 已存在
2025-07-03 17:23:55,397 - server5-2.timeprotab_manager - INFO - 📅 分区 timeprotab_2505 已存在
2025-07-03 17:23:55,399 - server5-2.timeprotab_manager - INFO - 📅 分区 timeprotab_2505 已存在
2025-07-03 17:23:55,401 - server5-2.timeprotab_manager - INFO - 📅 分区 timeprotab_2505 已存在
2025-07-03 17:23:55,404 - server5-2.timeprotab_manager - INFO - 📅 分区 timeprotab_2505 已存在
2025-07-03 17:23:55,406 - server5-2.timeprotab_manager - INFO - 📅 分区 timeprotab_2505 已存在
2025-07-03 17:23:55,408 - server5-2.timeprotab_manager - INFO - 📅 分区 timeprotab_2505 已存在
2025-07-03 17:23:55,410 - server5-2.timeprotab_manager - INFO - 📅 分区 timeprotab_2505 已存在
2025-07-03 17:23:55,412 - server5-2.timeprotab_manager - INFO - 📅 分区 timeprotab_2505 已存在
2025-07-03 17:23:55,414 - server5-2.timeprotab_manager - INFO - 📅 分区 timeprotab_2505 已存在
2025-07-03 17:23:55,416 - server5-2.timeprotab_manager - INFO - 📅 分区 timeprotab_2505 已存在
2025-07-03 17:23:55,418 - server5-2.timeprotab_manager - INFO - 📅 分区 timeprotab_2505 已存在
2025-07-03 17:23:55,420 - server5-2.timeprotab_manager - INFO - 📅 分区 timeprotab_2505 已存在
2025-07-03 17:23:55,422 - server5-2.timeprotab_manager - INFO - 📅 分区 timeprotab_2505 已存在
2025-07-03 17:23:55,425 - server5-2.timeprotab_manager - INFO - 📅 分区 timeprotab_2505 已存在
2025-07-03 17:23:55,427 - server5-2.timeprotab_manager - INFO - 📅 分区 timeprotab_2505 已存在
2025-07-03 17:23:55,429 - server5-2.timeprotab_manager - INFO - 📅 分区 timeprotab_2505 已存在
2025-07-03 17:23:55,431 - server5-2.timeprotab_manager - INFO - 📅 分区 timeprotab_2505 已存在
2025-07-03 17:23:55,433 - server5-2.timeprotab_manager - INFO - 📅 分区 timeprotab_2505 已存在
2025-07-03 17:23:55,435 - server5-2.timeprotab_manager - INFO - 📅 分区 timeprotab_2505 已存在
2025-07-03 17:23:55,437 - server5-2.timeprotab_manager - INFO - 📅 分区 timeprotab_2505 已存在
2025-07-03 17:23:55,439 - server5-2.timeprotab_manager - INFO - 📅 分区 timeprotab_2505 已存在
2025-07-03 17:23:55,441 - server5-2.timeprotab_manager - INFO - 📅 分区 timeprotab_2505 已存在
2025-07-03 17:23:55,443 - server5-2.timeprotab_manager - INFO - 📅 分区 timeprotab_2505 已存在
2025-07-03 17:23:55,445 - server5-2.timeprotab_manager - INFO - 📅 分区 timeprotab_2505 已存在
2025-07-03 17:23:55,448 - server5-2.timeprotab_manager - INFO - ✅ 批量插入/更新了 31 条记录
2025-07-03 17:23:55,448 - server5-2.timepro_collector - INFO - ✅ 数据库插入成功: 31 条记录 (202505)。
2025-07-03 17:23:55,448 - server5-2.test - INFO - ✅ 指定月份真实数据采集调用完成
2025-07-03 17:23:55,448 - server5-2.test - INFO - 🔍 正在从数据库验证 2505 月份的数据...
2025-07-03 17:23:57,456 - server5-2.timeprotab_manager - INFO - 📊 查询到 31 条记录: 215829 - 2505
2025-07-03 17:23:57,456 - server5-2.test - INFO - 🎉 验证成功! 在数据库中找到 31 条记录 (2505)。
2025-07-03 17:23:57,457 - server5-2.test - INFO - --- 前5条记录示例 ---
2025-07-03 17:23:57,457 - server5-2.test - INFO -   - 日期: 2025-05-01, 出勤: None, コメント: 
2025-07-03 17:23:57,457 - server5-2.test - INFO -   - 日期: 2025-05-02, 出勤: None, コメント: 
2025-07-03 17:23:57,457 - server5-2.test - INFO -   - 日期: 2025-05-03, 出勤: None, コメント: 
2025-07-03 17:23:57,457 - server5-2.test - INFO -   - 日期: 2025-05-04, 出勤: None, コメント: 
2025-07-03 17:23:57,457 - server5-2.test - INFO -   - 日期: 2025-05-05, 出勤: 08:12:00, コメント: 
2025-07-03 17:23:57,457 - server5-2.test - INFO - --------------------
2025-07-03 17:23:58,458 - server5-2.test - INFO - 
==================================================
2025-07-03 17:23:58,458 - server5-2.test - INFO - 📊 测试结果汇总:
2025-07-03 17:23:58,458 - server5-2.test - INFO -   - 数据库连接: ✅ 通过
2025-07-03 17:23:58,458 - server5-2.test - INFO -   - 采集器初始化: ✅ 通过
2025-07-03 17:23:58,458 - server5-2.test - INFO -   - 核心功能: 采集2025年6月真实数据: ✅ 通过
2025-07-03 17:23:58,458 - server5-2.test - INFO - 总计: 3/3 测试通过
2025-07-03 17:23:58,458 - server5-2.test - INFO - ==================================================
2025-07-03 17:23:58,462 - server5-2.timeprotab_manager - INFO - 🔌 timeprotab数据库连接池已关闭
2025-07-03 17:23:58,462 - server5-2.timepro_collector - INFO - 🔌 timepro采集服务已停止
2025-07-03 17:23:58,462 - server5-2.timeprotab_manager - INFO - 🔌 timeprotab数据库连接池已关闭
2025-07-03 17:23:58,462 - server5-2.test - INFO - ✅ 测试环境清理完成
2025-07-03 17:23:58,462 - server5-2.test - INFO - 🎉 所有测试通过！
2025-07-03 17:26:40,514 - server5-2.test - INFO - 🧪 server5-2 测试工具启动 (真实、无文件采集)
2025-07-03 17:26:40,514 - server5-2.test - INFO - ============================================================
2025-07-03 17:26:40,514 - server5-2.test - INFO - 🎯 开始运行所有测试 (无文件内存流)
2025-07-03 17:26:40,514 - server5-2.test - INFO - 🧪 设置测试环境
2025-07-03 17:26:40,750 - server5-2.timeprotab_manager - INFO - ✅ timeprotab数据库连接池创建成功
2025-07-03 17:26:40,764 - server5-2.timeprotab_manager - INFO - ✅ 基础分区表 timeprotab 初始化完成
2025-07-03 17:26:40,766 - server5-2.test - INFO - ✅ 测试环境设置完成
2025-07-03 17:26:40,766 - server5-2.test - INFO - 
==================== 数据库连接 ====================
2025-07-03 17:26:40,766 - server5-2.test - INFO - 🧪 (1/3) 测试数据库连接
2025-07-03 17:26:40,776 - server5-2.test - INFO - ✅ 数据库连接和状态获取测试成功
2025-07-03 17:26:41,778 - server5-2.test - INFO - 
==================== 采集器初始化 ====================
2025-07-03 17:26:41,778 - server5-2.test - INFO - 🧪 (2/3) 测试采集器初始化和关闭
2025-07-03 17:26:42,008 - server5-2.timeprotab_manager - INFO - ✅ timeprotab数据库连接池创建成功
2025-07-03 17:26:42,022 - server5-2.timeprotab_manager - INFO - ✅ 基础分区表 timeprotab 初始化完成
2025-07-03 17:26:42,023 - server5-2.timepro_collector - INFO - ✅ timepro采集服务启动成功
2025-07-03 17:26:42,023 - server5-2.timepro_collector - INFO - 📅 定时任务已设置: 每天 01:00 开始采集
2025-07-03 17:26:42,023 - server5-2.timepro_collector - INFO - ⏰ 调度器线程启动
2025-07-03 17:26:42,023 - server5-2.test - INFO - ✅ 采集器启动成功
2025-07-03 17:26:42,027 - server5-2.timeprotab_manager - INFO - 🔌 timeprotab数据库连接池已关闭
2025-07-03 17:26:42,027 - server5-2.timepro_collector - INFO - 🔌 timepro采集服务已停止
2025-07-03 17:26:42,027 - server5-2.test - INFO - ✅ 采集器停止成功
2025-07-03 17:26:43,028 - server5-2.test - INFO - 
==================== 核心功能: 采集2025年6月真实数据 ====================
2025-07-03 17:26:43,028 - server5-2.test - INFO - 🧪 (3/3) 核心测试: 采集2025年6月的真实数据
2025-07-03 17:26:43,028 - server5-2.test - INFO - 重新启动采集器以进行核心测试...
2025-07-03 17:26:43,271 - server5-2.timeprotab_manager - INFO - ✅ timeprotab数据库连接池创建成功
2025-07-03 17:26:43,284 - server5-2.timeprotab_manager - INFO - ✅ 基础分区表 timeprotab 初始化完成
2025-07-03 17:26:43,285 - server5-2.timepro_collector - INFO - ✅ timepro采集服务启动成功
2025-07-03 17:26:43,285 - server5-2.timepro_collector - INFO - 📅 定时任务已设置: 每天 01:00 开始采集
2025-07-03 17:26:43,285 - server5-2.timepro_collector - INFO - ⏰ 调度器线程启动
2025-07-03 17:26:43,285 - server5-2.test - INFO - 🔐 测试用户: 215829
2025-07-03 17:26:43,285 - server5-2.test - INFO - 📅 开始采集指定月份: 202506
2025-07-03 17:26:43,285 - server5-2.timepro_collector - INFO - 📅 开始为用户 '215829' 采集 202506 的数据
2025-07-03 17:26:43,285 - server5-2.timepro_collector - INFO - 🌐 正在调用timepro.py获取 202506 的数据, 用户: 215829
2025-07-03 17:26:44,493 - server5-2.timepro_collector - INFO - ✅ timepro.py 数据获取成功。
2025-07-03 17:26:44,493 - server5-2.timepro_collector - INFO - ✅ 成功在内存中获取HTML内容。
2025-07-03 17:26:44,512 - server5-2.timepro_collector - INFO - ✅ 成功从HTML解析了 30 条记录。
2025-07-03 17:26:44,554 - server5-2.timeprotab_manager - INFO - ✅ 创建分区成功: timeprotab_2506 (2025-06-01 ~ 2025-07-01)
2025-07-03 17:26:44,562 - server5-2.timeprotab_manager - INFO - 📅 分区 timeprotab_2506 已存在
2025-07-03 17:26:44,570 - server5-2.timeprotab_manager - INFO - 📅 分区 timeprotab_2506 已存在
2025-07-03 17:26:44,573 - server5-2.timeprotab_manager - INFO - 📅 分区 timeprotab_2506 已存在
2025-07-03 17:26:44,575 - server5-2.timeprotab_manager - INFO - 📅 分区 timeprotab_2506 已存在
2025-07-03 17:26:44,578 - server5-2.timeprotab_manager - INFO - 📅 分区 timeprotab_2506 已存在
2025-07-03 17:26:44,580 - server5-2.timeprotab_manager - INFO - 📅 分区 timeprotab_2506 已存在
2025-07-03 17:26:44,582 - server5-2.timeprotab_manager - INFO - 📅 分区 timeprotab_2506 已存在
2025-07-03 17:26:44,584 - server5-2.timeprotab_manager - INFO - 📅 分区 timeprotab_2506 已存在
2025-07-03 17:26:44,586 - server5-2.timeprotab_manager - INFO - 📅 分区 timeprotab_2506 已存在
2025-07-03 17:26:44,589 - server5-2.timeprotab_manager - INFO - 📅 分区 timeprotab_2506 已存在
2025-07-03 17:26:44,591 - server5-2.timeprotab_manager - INFO - 📅 分区 timeprotab_2506 已存在
2025-07-03 17:26:44,593 - server5-2.timeprotab_manager - INFO - 📅 分区 timeprotab_2506 已存在
2025-07-03 17:26:44,595 - server5-2.timeprotab_manager - INFO - 📅 分区 timeprotab_2506 已存在
2025-07-03 17:26:44,597 - server5-2.timeprotab_manager - INFO - 📅 分区 timeprotab_2506 已存在
2025-07-03 17:26:44,599 - server5-2.timeprotab_manager - INFO - 📅 分区 timeprotab_2506 已存在
2025-07-03 17:26:44,601 - server5-2.timeprotab_manager - INFO - 📅 分区 timeprotab_2506 已存在
2025-07-03 17:26:44,603 - server5-2.timeprotab_manager - INFO - 📅 分区 timeprotab_2506 已存在
2025-07-03 17:26:44,605 - server5-2.timeprotab_manager - INFO - 📅 分区 timeprotab_2506 已存在
2025-07-03 17:26:44,607 - server5-2.timeprotab_manager - INFO - 📅 分区 timeprotab_2506 已存在
2025-07-03 17:26:44,610 - server5-2.timeprotab_manager - INFO - 📅 分区 timeprotab_2506 已存在
2025-07-03 17:26:44,612 - server5-2.timeprotab_manager - INFO - 📅 分区 timeprotab_2506 已存在
2025-07-03 17:26:44,614 - server5-2.timeprotab_manager - INFO - 📅 分区 timeprotab_2506 已存在
2025-07-03 17:26:44,616 - server5-2.timeprotab_manager - INFO - 📅 分区 timeprotab_2506 已存在
2025-07-03 17:26:44,618 - server5-2.timeprotab_manager - INFO - 📅 分区 timeprotab_2506 已存在
2025-07-03 17:26:44,620 - server5-2.timeprotab_manager - INFO - 📅 分区 timeprotab_2506 已存在
2025-07-03 17:26:44,622 - server5-2.timeprotab_manager - INFO - 📅 分区 timeprotab_2506 已存在
2025-07-03 17:26:44,624 - server5-2.timeprotab_manager - INFO - 📅 分区 timeprotab_2506 已存在
2025-07-03 17:26:44,626 - server5-2.timeprotab_manager - INFO - 📅 分区 timeprotab_2506 已存在
2025-07-03 17:26:44,628 - server5-2.timeprotab_manager - INFO - 📅 分区 timeprotab_2506 已存在
2025-07-03 17:26:44,630 - server5-2.timeprotab_manager - INFO - 📅 分区 timeprotab_2506 已存在
2025-07-03 17:26:44,633 - server5-2.timeprotab_manager - INFO - ✅ 批量插入/更新了 30 条记录
2025-07-03 17:26:44,633 - server5-2.timepro_collector - INFO - ✅ 数据库插入成功: 30 条记录 (202506)。
2025-07-03 17:26:44,634 - server5-2.test - INFO - ✅ 指定月份真实数据采集调用完成
2025-07-03 17:26:44,634 - server5-2.test - INFO - 🔍 正在从数据库验证 2506 月份的数据...
2025-07-03 17:26:46,641 - server5-2.timeprotab_manager - INFO - 📊 查询到 30 条记录: 215829 - 2506
2025-07-03 17:26:46,643 - server5-2.test - INFO - 🎉 验证成功! 在数据库中找到 30 条记录 (2506)。
2025-07-03 17:26:46,643 - server5-2.test - INFO - --- 前5条记录示例 ---
2025-07-03 17:26:46,643 - server5-2.test - INFO -   - 日期: 2025-06-01, 出勤: None, コメント: 
2025-07-03 17:26:46,643 - server5-2.test - INFO -   - 日期: 2025-06-02, 出勤: 07:21:00, コメント: 計測システム開発
2025-07-03 17:26:46,644 - server5-2.test - INFO -   - 日期: 2025-06-03, 出勤: 07:18:00, コメント: 計測システム開発
2025-07-03 17:26:46,644 - server5-2.test - INFO -   - 日期: 2025-06-04, 出勤: 07:24:00, コメント: 計測システム開発
2025-07-03 17:26:46,644 - server5-2.test - INFO -   - 日期: 2025-06-05, 出勤: 07:18:00, コメント: 計測システム開発
2025-07-03 17:26:46,644 - server5-2.test - INFO - --------------------
2025-07-03 17:26:47,646 - server5-2.test - INFO - 
==================================================
2025-07-03 17:26:47,646 - server5-2.test - INFO - 📊 测试结果汇总:
2025-07-03 17:26:47,646 - server5-2.test - INFO -   - 数据库连接: ✅ 通过
2025-07-03 17:26:47,646 - server5-2.test - INFO -   - 采集器初始化: ✅ 通过
2025-07-03 17:26:47,647 - server5-2.test - INFO -   - 核心功能: 采集2025年6月真实数据: ✅ 通过
2025-07-03 17:26:47,647 - server5-2.test - INFO - 总计: 3/3 测试通过
2025-07-03 17:26:47,647 - server5-2.test - INFO - ==================================================
2025-07-03 17:26:47,652 - server5-2.timeprotab_manager - INFO - 🔌 timeprotab数据库连接池已关闭
2025-07-03 17:26:47,652 - server5-2.timepro_collector - INFO - 🔌 timepro采集服务已停止
2025-07-03 17:26:47,652 - server5-2.timeprotab_manager - INFO - 🔌 timeprotab数据库连接池已关闭
2025-07-03 17:26:47,652 - server5-2.test - INFO - ✅ 测试环境清理完成
2025-07-03 17:26:47,652 - server5-2.test - INFO - 🎉 所有测试通过！
2025-07-03 17:35:55,887 - server5-2.main - INFO - ============================================================
2025-07-03 17:35:55,887 - server5-2.main - INFO - 🎯 启动 Server5-2 API 服务...
2025-07-03 17:35:55,887 - server5-2.main - INFO -    - 后台定时采集: 每日 01:00
2025-07-03 17:35:55,887 - server5-2.main - INFO -    - API接口: http://127.0.0.1:8002
2025-07-03 17:35:55,887 - server5-2.main - INFO - ============================================================
2025-07-03 17:35:55,902 - server5-2.main - INFO - 🚀 启动后台定时采集服务...
2025-07-03 17:35:56,114 - server5-2.timeprotab_manager - INFO - ✅ timeprotab数据库连接池创建成功
2025-07-03 17:35:56,130 - server5-2.timeprotab_manager - INFO - ✅ 基础分区表 timeprotab 初始化完成
2025-07-03 17:35:56,132 - server5-2.timepro_collector - INFO - ✅ timepro采集服务启动成功
2025-07-03 17:35:56,133 - server5-2.timepro_collector - INFO - 📅 定时任务已设置: 每天 01:00 开始采集
2025-07-03 17:35:56,133 - server5-2.timepro_collector - INFO - ⏰ 调度器线程启动
2025-07-03 17:35:56,133 - server5-2.main - INFO - ✅ 后台定时采集服务已成功启动，每日凌晨1点执行。
2025-07-03 17:36:35,285 - server5-2.main - INFO - API触发 -> 开始为用户 215829 采集 2025-7 的数据
2025-07-03 17:36:35,285 - server5-2.timepro_collector - INFO - 📅 开始为用户 '215829' 采集 202507 的数据
2025-07-03 17:36:35,285 - server5-2.timepro_collector - INFO - 🌐 正在调用timepro.py获取 202507 的数据, 用户: 215829
2025-07-03 17:36:36,471 - server5-2.timepro_collector - INFO - ✅ timepro.py 数据获取成功。
2025-07-03 17:36:36,471 - server5-2.timepro_collector - INFO - ✅ 成功在内存中获取HTML内容。
2025-07-03 17:36:36,491 - server5-2.timepro_collector - INFO - ✅ 成功从HTML解析了 31 条记录。
2025-07-03 17:36:36,492 - server5-2.timepro_collector - WARNING - 无法解析时间 '__:__' (键: 退勤時刻)，将设为None。
2025-07-03 17:36:36,492 - server5-2.timepro_collector - WARNING - 无法解析时间 '__:__' (键: 出勤時刻)，将设为None。
2025-07-03 17:36:36,492 - server5-2.timepro_collector - WARNING - 无法解析时间 '__:__' (键: 退勤時刻)，将设为None。
2025-07-03 17:36:36,492 - server5-2.timepro_collector - WARNING - 无法解析时间 '__:__' (键: 出勤時刻)，将设为None。
2025-07-03 17:36:36,492 - server5-2.timepro_collector - WARNING - 无法解析时间 '__:__' (键: 退勤時刻)，将设为None。
2025-07-03 17:36:36,492 - server5-2.timepro_collector - WARNING - 无法解析时间 '__:__' (键: 出勤時刻)，将设为None。
2025-07-03 17:36:36,492 - server5-2.timepro_collector - WARNING - 无法解析时间 '__:__' (键: 退勤時刻)，将设为None。
2025-07-03 17:36:36,492 - server5-2.timepro_collector - WARNING - 无法解析时间 '__:__' (键: 出勤時刻)，将设为None。
2025-07-03 17:36:36,492 - server5-2.timepro_collector - WARNING - 无法解析时间 '__:__' (键: 退勤時刻)，将设为None。
2025-07-03 17:36:36,493 - server5-2.timepro_collector - WARNING - 无法解析时间 '__:__' (键: 出勤時刻)，将设为None。
2025-07-03 17:36:36,493 - server5-2.timepro_collector - WARNING - 无法解析时间 '__:__' (键: 退勤時刻)，将设为None。
2025-07-03 17:36:36,493 - server5-2.timepro_collector - WARNING - 无法解析时间 '__:__' (键: 出勤時刻)，将设为None。
2025-07-03 17:36:36,493 - server5-2.timepro_collector - WARNING - 无法解析时间 '__:__' (键: 退勤時刻)，将设为None。
2025-07-03 17:36:36,493 - server5-2.timepro_collector - WARNING - 无法解析时间 '__:__' (键: 出勤時刻)，将设为None。
2025-07-03 17:36:36,493 - server5-2.timepro_collector - WARNING - 无法解析时间 '__:__' (键: 退勤時刻)，将设为None。
2025-07-03 17:36:36,493 - server5-2.timepro_collector - WARNING - 无法解析时间 '__:__' (键: 出勤時刻)，将设为None。
2025-07-03 17:36:36,493 - server5-2.timepro_collector - WARNING - 无法解析时间 '__:__' (键: 退勤時刻)，将设为None。
2025-07-03 17:36:36,493 - server5-2.timepro_collector - WARNING - 无法解析时间 '__:__' (键: 出勤時刻)，将设为None。
2025-07-03 17:36:36,493 - server5-2.timepro_collector - WARNING - 无法解析时间 '__:__' (键: 退勤時刻)，将设为None。
2025-07-03 17:36:36,493 - server5-2.timepro_collector - WARNING - 无法解析时间 '__:__' (键: 出勤時刻)，将设为None。
2025-07-03 17:36:36,493 - server5-2.timepro_collector - WARNING - 无法解析时间 '__:__' (键: 退勤時刻)，将设为None。
2025-07-03 17:36:36,493 - server5-2.timepro_collector - WARNING - 无法解析时间 '__:__' (键: 出勤時刻)，将设为None。
2025-07-03 17:36:36,493 - server5-2.timepro_collector - WARNING - 无法解析时间 '__:__' (键: 退勤時刻)，将设为None。
2025-07-03 17:36:36,493 - server5-2.timepro_collector - WARNING - 无法解析时间 '__:__' (键: 出勤時刻)，将设为None。
2025-07-03 17:36:36,493 - server5-2.timepro_collector - WARNING - 无法解析时间 '__:__' (键: 退勤時刻)，将设为None。
2025-07-03 17:36:36,494 - server5-2.timepro_collector - WARNING - 无法解析时间 '__:__' (键: 出勤時刻)，将设为None。
2025-07-03 17:36:36,494 - server5-2.timepro_collector - WARNING - 无法解析时间 '__:__' (键: 退勤時刻)，将设为None。
2025-07-03 17:36:36,494 - server5-2.timepro_collector - WARNING - 无法解析时间 '__:__' (键: 出勤時刻)，将设为None。
2025-07-03 17:36:36,494 - server5-2.timepro_collector - WARNING - 无法解析时间 '__:__' (键: 退勤時刻)，将设为None。
2025-07-03 17:36:36,494 - server5-2.timepro_collector - WARNING - 无法解析时间 '__:__' (键: 出勤時刻)，将设为None。
2025-07-03 17:36:36,494 - server5-2.timepro_collector - WARNING - 无法解析时间 '__:__' (键: 退勤時刻)，将设为None。
2025-07-03 17:36:36,494 - server5-2.timepro_collector - WARNING - 无法解析时间 '__:__' (键: 出勤時刻)，将设为None。
2025-07-03 17:36:36,494 - server5-2.timepro_collector - WARNING - 无法解析时间 '__:__' (键: 退勤時刻)，将设为None。
2025-07-03 17:36:36,494 - server5-2.timepro_collector - WARNING - 无法解析时间 '__:__' (键: 出勤時刻)，将设为None。
2025-07-03 17:36:36,494 - server5-2.timepro_collector - WARNING - 无法解析时间 '__:__' (键: 退勤時刻)，将设为None。
2025-07-03 17:36:36,494 - server5-2.timepro_collector - WARNING - 无法解析时间 '__:__' (键: 出勤時刻)，将设为None。
2025-07-03 17:36:36,494 - server5-2.timepro_collector - WARNING - 无法解析时间 '__:__' (键: 退勤時刻)，将设为None。
2025-07-03 17:36:36,494 - server5-2.timepro_collector - WARNING - 无法解析时间 '__:__' (键: 出勤時刻)，将设为None。
2025-07-03 17:36:36,494 - server5-2.timepro_collector - WARNING - 无法解析时间 '__:__' (键: 退勤時刻)，将设为None。
2025-07-03 17:36:36,542 - server5-2.timeprotab_manager - INFO - ✅ 创建分区成功: timeprotab_2507 (2025-07-01 ~ 2025-08-01)
2025-07-03 17:36:36,553 - server5-2.timeprotab_manager - INFO - 📅 分区 timeprotab_2507 已存在
2025-07-03 17:36:36,561 - server5-2.timeprotab_manager - INFO - 📅 分区 timeprotab_2507 已存在
2025-07-03 17:36:36,564 - server5-2.timeprotab_manager - INFO - 📅 分区 timeprotab_2507 已存在
2025-07-03 17:36:36,566 - server5-2.timeprotab_manager - INFO - 📅 分区 timeprotab_2507 已存在
2025-07-03 17:36:36,569 - server5-2.timeprotab_manager - INFO - 📅 分区 timeprotab_2507 已存在
2025-07-03 17:36:36,571 - server5-2.timeprotab_manager - INFO - 📅 分区 timeprotab_2507 已存在
2025-07-03 17:36:36,573 - server5-2.timeprotab_manager - INFO - 📅 分区 timeprotab_2507 已存在
2025-07-03 17:36:36,575 - server5-2.timeprotab_manager - INFO - 📅 分区 timeprotab_2507 已存在
2025-07-03 17:36:36,577 - server5-2.timeprotab_manager - INFO - 📅 分区 timeprotab_2507 已存在
2025-07-03 17:36:36,580 - server5-2.timeprotab_manager - INFO - 📅 分区 timeprotab_2507 已存在
2025-07-03 17:36:36,582 - server5-2.timeprotab_manager - INFO - 📅 分区 timeprotab_2507 已存在
2025-07-03 17:36:36,584 - server5-2.timeprotab_manager - INFO - 📅 分区 timeprotab_2507 已存在
2025-07-03 17:36:36,586 - server5-2.timeprotab_manager - INFO - 📅 分区 timeprotab_2507 已存在
2025-07-03 17:36:36,588 - server5-2.timeprotab_manager - INFO - 📅 分区 timeprotab_2507 已存在
2025-07-03 17:36:36,590 - server5-2.timeprotab_manager - INFO - 📅 分区 timeprotab_2507 已存在
2025-07-03 17:36:36,592 - server5-2.timeprotab_manager - INFO - 📅 分区 timeprotab_2507 已存在
2025-07-03 17:36:36,594 - server5-2.timeprotab_manager - INFO - 📅 分区 timeprotab_2507 已存在
2025-07-03 17:36:36,596 - server5-2.timeprotab_manager - INFO - 📅 分区 timeprotab_2507 已存在
2025-07-03 17:36:36,598 - server5-2.timeprotab_manager - INFO - 📅 分区 timeprotab_2507 已存在
2025-07-03 17:36:36,600 - server5-2.timeprotab_manager - INFO - 📅 分区 timeprotab_2507 已存在
2025-07-03 17:36:36,602 - server5-2.timeprotab_manager - INFO - 📅 分区 timeprotab_2507 已存在
2025-07-03 17:36:36,604 - server5-2.timeprotab_manager - INFO - 📅 分区 timeprotab_2507 已存在
2025-07-03 17:36:36,607 - server5-2.timeprotab_manager - INFO - 📅 分区 timeprotab_2507 已存在
2025-07-03 17:36:36,609 - server5-2.timeprotab_manager - INFO - 📅 分区 timeprotab_2507 已存在
2025-07-03 17:36:36,611 - server5-2.timeprotab_manager - INFO - 📅 分区 timeprotab_2507 已存在
2025-07-03 17:36:36,613 - server5-2.timeprotab_manager - INFO - 📅 分区 timeprotab_2507 已存在
2025-07-03 17:36:36,615 - server5-2.timeprotab_manager - INFO - 📅 分区 timeprotab_2507 已存在
2025-07-03 17:36:36,617 - server5-2.timeprotab_manager - INFO - 📅 分区 timeprotab_2507 已存在
2025-07-03 17:36:36,619 - server5-2.timeprotab_manager - INFO - 📅 分区 timeprotab_2507 已存在
2025-07-03 17:36:36,621 - server5-2.timeprotab_manager - INFO - 📅 分区 timeprotab_2507 已存在
2025-07-03 17:36:36,623 - server5-2.timeprotab_manager - INFO - 📅 分区 timeprotab_2507 已存在
2025-07-03 17:36:36,626 - server5-2.timeprotab_manager - INFO - ✅ 批量插入/更新了 31 条记录
2025-07-03 17:36:36,626 - server5-2.timepro_collector - INFO - ✅ 数据库插入成功: 31 条记录 (202507)。
2025-07-03 17:36:36,629 - server5-2.timeprotab_manager - INFO - 📊 查询到 31 条记录: 215829 - 2507
2025-07-03 17:36:36,630 - server5-2.main - INFO - API触发 -> 采集调用完成。在数据库中找到 31 条关于 2025-7 的记录。
2025-07-04 01:00:00,751 - server5-2.timepro_collector - INFO - 🚀 开始所有用户的月度数据采集
2025-07-04 01:00:00,752 - server5-2.timepro_collector - INFO - 👤 开始为用户 215829 采集
2025-07-04 01:00:00,752 - server5-2.timepro_collector - INFO - 📅 开始为用户 '215829' 采集 202507 的数据
2025-07-04 01:00:00,752 - server5-2.timepro_collector - INFO - 🌐 正在调用timepro.py获取 202507 的数据, 用户: 215829
2025-07-04 01:00:02,309 - server5-2.timepro_collector - INFO - ✅ timepro.py 数据获取成功。
2025-07-04 01:00:02,310 - server5-2.timepro_collector - INFO - ✅ 成功在内存中获取HTML内容。
2025-07-04 01:00:02,335 - server5-2.timepro_collector - INFO - ✅ 成功从HTML解析了 31 条记录。
2025-07-04 01:00:02,335 - server5-2.timepro_collector - WARNING - 无法解析时间 '__:__' (键: 出勤時刻)，将设为None。
2025-07-04 01:00:02,335 - server5-2.timepro_collector - WARNING - 无法解析时间 '__:__' (键: 退勤時刻)，将设为None。
2025-07-04 01:00:02,335 - server5-2.timepro_collector - WARNING - 无法解析时间 '__:__' (键: 出勤時刻)，将设为None。
2025-07-04 01:00:02,335 - server5-2.timepro_collector - WARNING - 无法解析时间 '__:__' (键: 退勤時刻)，将设为None。
2025-07-04 01:00:02,335 - server5-2.timepro_collector - WARNING - 无法解析时间 '__:__' (键: 出勤時刻)，将设为None。
2025-07-04 01:00:02,335 - server5-2.timepro_collector - WARNING - 无法解析时间 '__:__' (键: 退勤時刻)，将设为None。
2025-07-04 01:00:02,335 - server5-2.timepro_collector - WARNING - 无法解析时间 '__:__' (键: 出勤時刻)，将设为None。
2025-07-04 01:00:02,335 - server5-2.timepro_collector - WARNING - 无法解析时间 '__:__' (键: 退勤時刻)，将设为None。
2025-07-04 01:00:02,335 - server5-2.timepro_collector - WARNING - 无法解析时间 '__:__' (键: 出勤時刻)，将设为None。
2025-07-04 01:00:02,335 - server5-2.timepro_collector - WARNING - 无法解析时间 '__:__' (键: 退勤時刻)，将设为None。
2025-07-04 01:00:02,335 - server5-2.timepro_collector - WARNING - 无法解析时间 '__:__' (键: 出勤時刻)，将设为None。
2025-07-04 01:00:02,335 - server5-2.timepro_collector - WARNING - 无法解析时间 '__:__' (键: 退勤時刻)，将设为None。
2025-07-04 01:00:02,335 - server5-2.timepro_collector - WARNING - 无法解析时间 '__:__' (键: 出勤時刻)，将设为None。
2025-07-04 01:00:02,335 - server5-2.timepro_collector - WARNING - 无法解析时间 '__:__' (键: 退勤時刻)，将设为None。
2025-07-04 01:00:02,335 - server5-2.timepro_collector - WARNING - 无法解析时间 '__:__' (键: 出勤時刻)，将设为None。
2025-07-04 01:00:02,335 - server5-2.timepro_collector - WARNING - 无法解析时间 '__:__' (键: 退勤時刻)，将设为None。
2025-07-04 01:00:02,336 - server5-2.timepro_collector - WARNING - 无法解析时间 '__:__' (键: 出勤時刻)，将设为None。
2025-07-04 01:00:02,336 - server5-2.timepro_collector - WARNING - 无法解析时间 '__:__' (键: 退勤時刻)，将设为None。
2025-07-04 01:00:02,336 - server5-2.timepro_collector - WARNING - 无法解析时间 '__:__' (键: 出勤時刻)，将设为None。
2025-07-04 01:00:02,336 - server5-2.timepro_collector - WARNING - 无法解析时间 '__:__' (键: 退勤時刻)，将设为None。
2025-07-04 01:00:02,336 - server5-2.timepro_collector - WARNING - 无法解析时间 '__:__' (键: 出勤時刻)，将设为None。
2025-07-04 01:00:02,336 - server5-2.timepro_collector - WARNING - 无法解析时间 '__:__' (键: 退勤時刻)，将设为None。
2025-07-04 01:00:02,336 - server5-2.timepro_collector - WARNING - 无法解析时间 '__:__' (键: 出勤時刻)，将设为None。
2025-07-04 01:00:02,336 - server5-2.timepro_collector - WARNING - 无法解析时间 '__:__' (键: 退勤時刻)，将设为None。
2025-07-04 01:00:02,336 - server5-2.timepro_collector - WARNING - 无法解析时间 '__:__' (键: 出勤時刻)，将设为None。
2025-07-04 01:00:02,336 - server5-2.timepro_collector - WARNING - 无法解析时间 '__:__' (键: 退勤時刻)，将设为None。
2025-07-04 01:00:02,336 - server5-2.timepro_collector - WARNING - 无法解析时间 '__:__' (键: 出勤時刻)，将设为None。
2025-07-04 01:00:02,336 - server5-2.timepro_collector - WARNING - 无法解析时间 '__:__' (键: 退勤時刻)，将设为None。
2025-07-04 01:00:02,336 - server5-2.timepro_collector - WARNING - 无法解析时间 '__:__' (键: 出勤時刻)，将设为None。
2025-07-04 01:00:02,336 - server5-2.timepro_collector - WARNING - 无法解析时间 '__:__' (键: 退勤時刻)，将设为None。
2025-07-04 01:00:02,336 - server5-2.timepro_collector - WARNING - 无法解析时间 '__:__' (键: 出勤時刻)，将设为None。
2025-07-04 01:00:02,336 - server5-2.timepro_collector - WARNING - 无法解析时间 '__:__' (键: 退勤時刻)，将设为None。
2025-07-04 01:00:02,336 - server5-2.timepro_collector - WARNING - 无法解析时间 '__:__' (键: 出勤時刻)，将设为None。
2025-07-04 01:00:02,336 - server5-2.timepro_collector - WARNING - 无法解析时间 '__:__' (键: 退勤時刻)，将设为None。
2025-07-04 01:00:02,336 - server5-2.timepro_collector - WARNING - 无法解析时间 '__:__' (键: 出勤時刻)，将设为None。
2025-07-04 01:00:02,336 - server5-2.timepro_collector - WARNING - 无法解析时间 '__:__' (键: 退勤時刻)，将设为None。
2025-07-04 01:00:02,336 - server5-2.timepro_collector - WARNING - 无法解析时间 '__:__' (键: 出勤時刻)，将设为None。
2025-07-04 01:00:02,336 - server5-2.timepro_collector - WARNING - 无法解析时间 '__:__' (键: 退勤時刻)，将设为None。
2025-07-04 01:00:02,337 - server5-2.timeprotab_manager - ERROR - ❌ 创建分区失败 2507: Task <Task pending name='Task-41' coro=<TimeproCollector.collect_all_users_current_and_last_month() running at /home/<USER>/Documents/testF/2025/ser25/server0613/MySuiteDatebase2402newuigood-yolo5-3/server5-2/services/timepro_collector.py:97> cb=[run_until_complete.<locals>.done_cb()]> got Future <Future pending> attached to a different loop
2025-07-04 01:00:02,338 - server5-2.timeprotab_manager - ERROR - ❌ 批量插入失败: Task <Task pending name='Task-41' coro=<TimeproCollector.collect_all_users_current_and_last_month() running at /home/<USER>/Documents/testF/2025/ser25/server0613/MySuiteDatebase2402newuigood-yolo5-3/server5-2/services/timepro_collector.py:97> cb=[run_until_complete.<locals>.done_cb()]> got Future <Future pending> attached to a different loop
2025-07-04 01:00:02,338 - server5-2.timepro_collector - ERROR - ❌ 数据库插入失败 (202507)。
2025-07-04 01:00:02,338 - server5-2.timepro_collector - INFO - 📅 开始为用户 '215829' 采集 202506 的数据
2025-07-04 01:00:02,338 - server5-2.timepro_collector - INFO - 🌐 正在调用timepro.py获取 202506 的数据, 用户: 215829
2025-07-04 01:00:03,472 - server5-2.timepro_collector - INFO - ✅ timepro.py 数据获取成功。
2025-07-04 01:00:03,473 - server5-2.timepro_collector - INFO - ✅ 成功在内存中获取HTML内容。
2025-07-04 01:00:03,498 - server5-2.timepro_collector - INFO - ✅ 成功从HTML解析了 30 条记录。
2025-07-04 01:00:03,499 - server5-2.timeprotab_manager - ERROR - ❌ 创建分区失败 2506: Task <Task pending name='Task-41' coro=<TimeproCollector.collect_all_users_current_and_last_month() running at /home/<USER>/Documents/testF/2025/ser25/server0613/MySuiteDatebase2402newuigood-yolo5-3/server5-2/services/timepro_collector.py:102> cb=[run_until_complete.<locals>.done_cb()]> got Future <Future pending> attached to a different loop
2025-07-04 01:00:03,500 - server5-2.timeprotab_manager - ERROR - ❌ 批量插入失败: Task <Task pending name='Task-41' coro=<TimeproCollector.collect_all_users_current_and_last_month() running at /home/<USER>/Documents/testF/2025/ser25/server0613/MySuiteDatebase2402newuigood-yolo5-3/server5-2/services/timepro_collector.py:102> cb=[run_until_complete.<locals>.done_cb()]> got Future <Future pending> attached to a different loop
2025-07-04 01:00:03,500 - server5-2.timepro_collector - ERROR - ❌ 数据库插入失败 (202506)。
2025-07-04 01:00:03,500 - server5-2.timepro_collector - INFO - ⏳ 用户 215829 采集完成，等待15秒...
2025-07-04 01:00:18,515 - server5-2.timepro_collector - INFO - 👤 开始为用户 200001 采集
2025-07-04 01:00:18,515 - server5-2.timepro_collector - INFO - 📅 开始为用户 '200001' 采集 202507 的数据
2025-07-04 01:00:18,515 - server5-2.timepro_collector - INFO - 🌐 正在调用timepro.py获取 202507 的数据, 用户: 200001
2025-07-04 01:00:18,560 - server5-2.timepro_collector - ERROR - ❌ timepro.py 数据获取失败，未返回HTML内容。
2025-07-04 01:00:18,560 - server5-2.timepro_collector - ERROR - ❌ 用户 '200001' (202507) 的采集失败: 未能从网站获取HTML内容。
Traceback (most recent call last):
  File "/home/<USER>/Documents/testF/2025/ser25/server0613/MySuiteDatebase2402newuigood-yolo5-3/server5-2/services/timepro_collector.py", line 128, in _collect_user_month_data
    raise ValueError("未能从网站获取HTML内容。")
ValueError: 未能从网站获取HTML内容。
2025-07-04 01:00:18,561 - server5-2.timepro_collector - ERROR - ❌ 用户 200001 的数据采集失败: 未能从网站获取HTML内容。
2025-07-04 01:00:18,561 - server5-2.timepro_collector - INFO - ⏳ 用户 200001 采集完成，等待15秒...
2025-07-04 01:00:33,576 - server5-2.timepro_collector - INFO - ✅ 所有用户月度数据采集完成
2025-07-04 07:49:30,279 - server5-2.main - INFO - 🔌 停止后台服务
2025-07-04 07:49:30,279 - server5-2.timeprotab_manager - INFO - 🔌 timeprotab数据库连接池已关闭
2025-07-04 07:49:30,279 - server5-2.timepro_collector - INFO - 🔌 timepro采集服务已停止
2025-07-04 07:49:33,317 - server5-2.main - INFO - ============================================================
2025-07-04 07:49:33,317 - server5-2.main - INFO - 🎯 启动 Server5-2 API 服务...
2025-07-04 07:49:33,317 - server5-2.main - INFO -    - 后台定时采集: 每日 01:00
2025-07-04 07:49:33,317 - server5-2.main - INFO -    - API接口: http://127.0.0.1:8002
2025-07-04 07:49:33,317 - server5-2.main - INFO - ============================================================
2025-07-04 07:49:33,332 - server5-2.main - INFO - 🚀 启动后台定时采集服务...
2025-07-04 07:49:33,664 - server5-2.timeprotab_manager - INFO - ✅ timeprotab数据库连接池创建成功
2025-07-04 07:49:33,707 - server5-2.timeprotab_manager - INFO - ✅ 基础分区表 timeprotab 初始化完成
2025-07-04 07:49:33,710 - server5-2.timepro_collector - INFO - ✅ timepro采集服务启动成功
2025-07-04 07:49:33,710 - server5-2.timepro_collector - INFO - 📅 定时任务已设置: 每天 01:00 开始采集
2025-07-04 07:49:33,710 - server5-2.timepro_collector - INFO - ⏰ 调度器线程启动
2025-07-04 07:49:33,710 - server5-2.main - INFO - ✅ 后台定时采集服务已成功启动，每日凌晨1点执行。
2025-07-04 07:51:00,723 - server5-2.timepro_collector - INFO - 🚀 开始所有用户的月度数据采集
2025-07-04 07:51:00,724 - server5-2.timepro_collector - INFO - 👥 本次自动采集任务将为 2 个用户执行。
2025-07-04 07:51:00,724 - server5-2.timepro_collector - INFO - 👤 开始为用户 215829 采集
2025-07-04 07:51:00,724 - server5-2.timepro_collector - INFO - 📅 开始为用户 '215829' 采集 202507 的数据
2025-07-04 07:51:00,724 - server5-2.timepro_collector - INFO - 🌐 正在调用timepro.py获取 202507 的数据, 用户: 215829
2025-07-04 07:51:01,968 - server5-2.timepro_collector - INFO - ✅ timepro.py 数据获取成功。
2025-07-04 07:51:01,968 - server5-2.timepro_collector - INFO - ✅ 成功在内存中获取HTML内容。
2025-07-04 07:51:01,988 - server5-2.timepro_collector - INFO - ✅ 成功从HTML解析了 31 条记录。
2025-07-04 07:51:01,989 - server5-2.timepro_collector - WARNING - 无法解析时间 '__:__' (键: 退勤時刻)，将设为None。
2025-07-04 07:51:01,989 - server5-2.timepro_collector - WARNING - 无法解析时间 '__:__' (键: 出勤時刻)，将设为None。
2025-07-04 07:51:01,989 - server5-2.timepro_collector - WARNING - 无法解析时间 '__:__' (键: 退勤時刻)，将设为None。
2025-07-04 07:51:01,989 - server5-2.timepro_collector - WARNING - 无法解析时间 '__:__' (键: 出勤時刻)，将设为None。
2025-07-04 07:51:01,989 - server5-2.timepro_collector - WARNING - 无法解析时间 '__:__' (键: 退勤時刻)，将设为None。
2025-07-04 07:51:01,989 - server5-2.timepro_collector - WARNING - 无法解析时间 '__:__' (键: 出勤時刻)，将设为None。
2025-07-04 07:51:01,989 - server5-2.timepro_collector - WARNING - 无法解析时间 '__:__' (键: 退勤時刻)，将设为None。
2025-07-04 07:51:01,989 - server5-2.timepro_collector - WARNING - 无法解析时间 '__:__' (键: 出勤時刻)，将设为None。
2025-07-04 07:51:01,989 - server5-2.timepro_collector - WARNING - 无法解析时间 '__:__' (键: 退勤時刻)，将设为None。
2025-07-04 07:51:01,989 - server5-2.timepro_collector - WARNING - 无法解析时间 '__:__' (键: 出勤時刻)，将设为None。
2025-07-04 07:51:01,989 - server5-2.timepro_collector - WARNING - 无法解析时间 '__:__' (键: 退勤時刻)，将设为None。
2025-07-04 07:51:01,989 - server5-2.timepro_collector - WARNING - 无法解析时间 '__:__' (键: 出勤時刻)，将设为None。
2025-07-04 07:51:01,989 - server5-2.timepro_collector - WARNING - 无法解析时间 '__:__' (键: 退勤時刻)，将设为None。
2025-07-04 07:51:01,990 - server5-2.timepro_collector - WARNING - 无法解析时间 '__:__' (键: 出勤時刻)，将设为None。
2025-07-04 07:51:01,990 - server5-2.timepro_collector - WARNING - 无法解析时间 '__:__' (键: 退勤時刻)，将设为None。
2025-07-04 07:51:01,990 - server5-2.timepro_collector - WARNING - 无法解析时间 '__:__' (键: 出勤時刻)，将设为None。
2025-07-04 07:51:01,990 - server5-2.timepro_collector - WARNING - 无法解析时间 '__:__' (键: 退勤時刻)，将设为None。
2025-07-04 07:51:01,990 - server5-2.timepro_collector - WARNING - 无法解析时间 '__:__' (键: 出勤時刻)，将设为None。
2025-07-04 07:51:01,990 - server5-2.timepro_collector - WARNING - 无法解析时间 '__:__' (键: 退勤時刻)，将设为None。
2025-07-04 07:51:01,990 - server5-2.timepro_collector - WARNING - 无法解析时间 '__:__' (键: 出勤時刻)，将设为None。
2025-07-04 07:51:01,990 - server5-2.timepro_collector - WARNING - 无法解析时间 '__:__' (键: 退勤時刻)，将设为None。
2025-07-04 07:51:01,990 - server5-2.timepro_collector - WARNING - 无法解析时间 '__:__' (键: 出勤時刻)，将设为None。
2025-07-04 07:51:01,990 - server5-2.timepro_collector - WARNING - 无法解析时间 '__:__' (键: 退勤時刻)，将设为None。
2025-07-04 07:51:01,990 - server5-2.timepro_collector - WARNING - 无法解析时间 '__:__' (键: 出勤時刻)，将设为None。
2025-07-04 07:51:01,990 - server5-2.timepro_collector - WARNING - 无法解析时间 '__:__' (键: 退勤時刻)，将设为None。
2025-07-04 07:51:01,990 - server5-2.timepro_collector - WARNING - 无法解析时间 '__:__' (键: 出勤時刻)，将设为None。
2025-07-04 07:51:01,990 - server5-2.timepro_collector - WARNING - 无法解析时间 '__:__' (键: 退勤時刻)，将设为None。
2025-07-04 07:51:01,990 - server5-2.timepro_collector - WARNING - 无法解析时间 '__:__' (键: 出勤時刻)，将设为None。
2025-07-04 07:51:01,990 - server5-2.timepro_collector - WARNING - 无法解析时间 '__:__' (键: 退勤時刻)，将设为None。
2025-07-04 07:51:01,990 - server5-2.timepro_collector - WARNING - 无法解析时间 '__:__' (键: 出勤時刻)，将设为None。
2025-07-04 07:51:01,990 - server5-2.timepro_collector - WARNING - 无法解析时间 '__:__' (键: 退勤時刻)，将设为None。
2025-07-04 07:51:01,990 - server5-2.timepro_collector - WARNING - 无法解析时间 '__:__' (键: 出勤時刻)，将设为None。
2025-07-04 07:51:01,990 - server5-2.timepro_collector - WARNING - 无法解析时间 '__:__' (键: 退勤時刻)，将设为None。
2025-07-04 07:51:01,990 - server5-2.timepro_collector - WARNING - 无法解析时间 '__:__' (键: 出勤時刻)，将设为None。
2025-07-04 07:51:01,990 - server5-2.timepro_collector - WARNING - 无法解析时间 '__:__' (键: 退勤時刻)，将设为None。
2025-07-04 07:51:01,990 - server5-2.timepro_collector - WARNING - 无法解析时间 '__:__' (键: 出勤時刻)，将设为None。
2025-07-04 07:51:01,990 - server5-2.timepro_collector - WARNING - 无法解析时间 '__:__' (键: 退勤時刻)，将设为None。
2025-07-04 07:51:01,991 - server5-2.timeprotab_manager - ERROR - ❌ 创建分区失败 2507: cannot perform operation: another operation is in progress
2025-07-04 07:51:01,991 - server5-2.timeprotab_manager - ERROR - ❌ 批量插入失败: Task <Task pending name='Task-6' coro=<TimeproCollector.collect_all_users_current_and_last_month() running at /home/<USER>/Documents/testF/2025/ser25/server0613/MySuiteDatebase2402newuigood-yolo5-3/server5-2/services/timepro_collector.py:106> cb=[run_until_complete.<locals>.done_cb()]> got Future <Future pending> attached to a different loop
2025-07-04 07:51:01,991 - server5-2.timepro_collector - ERROR - ❌ 数据库插入失败 (202507)。
2025-07-04 07:51:01,992 - server5-2.timepro_collector - INFO - 📅 开始为用户 '215829' 采集 202506 的数据
2025-07-04 07:51:01,992 - server5-2.timepro_collector - INFO - 🌐 正在调用timepro.py获取 202506 的数据, 用户: 215829
2025-07-04 07:51:03,234 - server5-2.timepro_collector - INFO - ✅ timepro.py 数据获取成功。
2025-07-04 07:51:03,234 - server5-2.timepro_collector - INFO - ✅ 成功在内存中获取HTML内容。
2025-07-04 07:51:03,253 - server5-2.timepro_collector - INFO - ✅ 成功从HTML解析了 30 条记录。
2025-07-04 07:51:03,254 - server5-2.timeprotab_manager - ERROR - ❌ 创建分区失败 2506: Task <Task pending name='Task-6' coro=<TimeproCollector.collect_all_users_current_and_last_month() running at /home/<USER>/Documents/testF/2025/ser25/server0613/MySuiteDatebase2402newuigood-yolo5-3/server5-2/services/timepro_collector.py:111> cb=[run_until_complete.<locals>.done_cb()]> got Future <Future pending> attached to a different loop
2025-07-04 07:51:03,255 - server5-2.timeprotab_manager - ERROR - ❌ 批量插入失败: Task <Task pending name='Task-6' coro=<TimeproCollector.collect_all_users_current_and_last_month() running at /home/<USER>/Documents/testF/2025/ser25/server0613/MySuiteDatebase2402newuigood-yolo5-3/server5-2/services/timepro_collector.py:111> cb=[run_until_complete.<locals>.done_cb()]> got Future <Future pending> attached to a different loop
2025-07-04 07:51:03,255 - server5-2.timepro_collector - ERROR - ❌ 数据库插入失败 (202506)。
2025-07-04 07:51:03,255 - server5-2.timepro_collector - INFO - ⏳ 用户 215829 采集完成，等待10秒...
2025-07-04 07:51:13,265 - server5-2.timepro_collector - INFO - 👤 开始为用户 999999 采集
2025-07-04 07:51:13,265 - server5-2.timepro_collector - INFO - 📅 开始为用户 '999999' 采集 202507 的数据
2025-07-04 07:51:13,266 - server5-2.timepro_collector - INFO - 🌐 正在调用timepro.py获取 202507 的数据, 用户: testuser
2025-07-04 07:51:13,350 - server5-2.timepro_collector - ERROR - ❌ timepro.py 数据获取失败，未返回HTML内容。
2025-07-04 07:51:13,350 - server5-2.timepro_collector - ERROR - ❌ 用户 '999999' (202507) 的采集失败: 未能从网站获取HTML内容。
Traceback (most recent call last):
  File "/home/<USER>/Documents/testF/2025/ser25/server0613/MySuiteDatebase2402newuigood-yolo5-3/server5-2/services/timepro_collector.py", line 137, in _collect_user_month_data
    raise ValueError("未能从网站获取HTML内容。")
ValueError: 未能从网站获取HTML内容。
2025-07-04 07:51:13,350 - server5-2.timepro_collector - ERROR - ❌ 用户 999999 的数据采集失败: 未能从网站获取HTML内容。
2025-07-04 07:51:13,350 - server5-2.timepro_collector - INFO - ⏳ 用户 999999 采集完成，等待10秒...
2025-07-04 07:51:23,354 - server5-2.timepro_collector - INFO - ✅ 所有用户月度数据采集完成
2025-07-04 07:54:37,243 - server5-2.main - INFO - 🔌 停止后台服务
2025-07-04 07:54:37,243 - server5-2.timeprotab_manager - INFO - 🔌 timeprotab数据库连接池已关闭
2025-07-04 07:54:37,244 - server5-2.timepro_collector - INFO - 🔌 timepro采集服务已停止
2025-07-04 07:54:38,377 - server5-2.main - INFO - ============================================================
2025-07-04 07:54:38,377 - server5-2.main - INFO - 🎯 启动 Server5-2 API 服务...
2025-07-04 07:54:38,377 - server5-2.main - INFO -    - 后台定时采集: 每日 01:00
2025-07-04 07:54:38,377 - server5-2.main - INFO -    - API接口: http://127.0.0.1:8002
2025-07-04 07:54:38,377 - server5-2.main - INFO - ============================================================
2025-07-04 07:54:38,390 - server5-2.main - INFO - 🚀 启动后台定时采集服务...
2025-07-04 07:54:38,390 - server5-2.timepro_collector - INFO - 🚀 启动 timepro采集服务...
2025-07-04 07:54:38,390 - server5-2.timeprotab_manager - ERROR - ❌ 初始化基础分区表失败: 'NoneType' object has no attribute 'acquire'
2025-07-04 07:54:38,391 - server5-2.main - ERROR - ❌ 后台服务启动失败: 'NoneType' object has no attribute 'acquire'
Traceback (most recent call last):
  File "/home/<USER>/Documents/testF/2025/ser25/server0613/MySuiteDatebase2402newuigood-yolo5-3/server5-2/start_server5_2.py", line 57, in start
    await self.collector.start()
  File "/home/<USER>/Documents/testF/2025/ser25/server0613/MySuiteDatebase2402newuigood-yolo5-3/server5-2/services/timepro_collector.py", line 63, in start
    await self.db_manager.initialize_base_table()
  File "/home/<USER>/Documents/testF/2025/ser25/server0613/MySuiteDatebase2402newuigood-yolo5-3/server5-2/database/timeprotab_manager.py", line 52, in initialize_base_table
    async with self.pool.acquire() as conn:
               ^^^^^^^^^^^^^^^^^
AttributeError: 'NoneType' object has no attribute 'acquire'
2025-07-04 07:55:29,951 - server5-2.main - INFO - 🔌 停止后台服务
2025-07-04 07:55:29,951 - server5-2.timepro_collector - INFO - 🔌 timepro采集服务已停止
2025-07-04 07:55:31,464 - server5-2.main - INFO - ============================================================
2025-07-04 07:55:31,464 - server5-2.main - INFO - 🎯 启动 Server5-2 API 服务...
2025-07-04 07:55:31,464 - server5-2.main - INFO -    - 后台定时采集: 每日 01:00
2025-07-04 07:55:31,464 - server5-2.main - INFO -    - API接口: http://127.0.0.1:8002
2025-07-04 07:55:31,464 - server5-2.main - INFO - ============================================================
2025-07-04 07:55:31,481 - server5-2.main - INFO - 🚀 启动后台定时采集服务...
2025-07-04 07:55:31,482 - server5-2.timepro_collector - INFO - 🚀 启动 timepro采集服务...
2025-07-04 07:55:31,482 - server5-2.timeprotab_manager - ERROR - ❌ 初始化基础分区表失败: 'NoneType' object has no attribute 'acquire'
2025-07-04 07:55:31,482 - server5-2.main - ERROR - ❌ 后台服务启动失败: 'NoneType' object has no attribute 'acquire'
Traceback (most recent call last):
  File "/home/<USER>/Documents/testF/2025/ser25/server0613/MySuiteDatebase2402newuigood-yolo5-3/server5-2/start_server5_2.py", line 57, in start
    await self.collector.start()
  File "/home/<USER>/Documents/testF/2025/ser25/server0613/MySuiteDatebase2402newuigood-yolo5-3/server5-2/services/timepro_collector.py", line 63, in start
    await self.db_manager.initialize_base_table()
  File "/home/<USER>/Documents/testF/2025/ser25/server0613/MySuiteDatebase2402newuigood-yolo5-3/server5-2/database/timeprotab_manager.py", line 52, in initialize_base_table
    async with self.pool.acquire() as conn:
               ^^^^^^^^^^^^^^^^^
AttributeError: 'NoneType' object has no attribute 'acquire'
2025-07-04 07:58:56,431 - server5-2.main - INFO - 🔌 停止后台服务
2025-07-04 07:58:56,431 - server5-2.timepro_collector - INFO - 🔌 timepro采集服务已停止
2025-07-04 07:58:57,596 - server5-2.main - INFO - ============================================================
2025-07-04 07:58:57,596 - server5-2.main - INFO - 🎯 启动 Server5-2 API 服务...
2025-07-04 07:58:57,596 - server5-2.main - INFO -    - 后台定时采集: 每日 01:00
2025-07-04 07:58:57,596 - server5-2.main - INFO -    - API接口: http://127.0.0.1:8002
2025-07-04 07:58:57,596 - server5-2.main - INFO - ============================================================
2025-07-04 07:58:57,609 - server5-2.main - INFO - 🚀 启动后台定时采集服务...
2025-07-04 07:58:57,609 - server5-2.timepro_collector - INFO - TimeproCollector instance created.
2025-07-04 07:58:57,609 - server5-2.timepro_collector - INFO - 🚀 启动 timepro采集服务...
2025-07-04 07:58:57,610 - server5-2.timepro_collector - INFO - 内部创建并连接新的DB Manager实例。
2025-07-04 07:58:57,818 - server5-2.timeprotab_manager - INFO - ✅ timeprotab数据库连接池创建成功
2025-07-04 07:58:57,833 - server5-2.timeprotab_manager - INFO - ✅ 基础分区表 timeprotab 初始化完成
2025-07-04 07:58:57,834 - server5-2.timepro_collector - INFO - ✅ timepro采集服务启动成功
2025-07-04 07:58:57,835 - server5-2.timepro_collector - INFO - 📅 定时任务已设置: 每天 07:51 开始采集
2025-07-04 07:58:57,835 - server5-2.timepro_collector - INFO - ⏰ 调度器线程启动
2025-07-04 07:58:57,835 - server5-2.main - INFO - ✅ 后台定时采集服务已成功启动，每日凌晨1点执行。
2025-07-04 07:59:00,835 - server5-2.timepro_collector - INFO - ⏰ 调度器线程: 检测到定时任务，准备提交到主事件循环...
2025-07-04 07:59:00,836 - server5-2.timepro_collector - INFO - 🚀 开始所有用户的月度数据采集
2025-07-04 07:59:00,836 - server5-2.timepro_collector - INFO - 👥 本次自动采集任务将为 2 个用户执行。
2025-07-04 07:59:00,836 - server5-2.timepro_collector - INFO - 👤 开始为用户 215829 采集
2025-07-04 07:59:00,836 - server5-2.timepro_collector - INFO - 📅 开始为用户 '215829' 采集 202507 的数据
2025-07-04 07:59:00,836 - server5-2.timepro_collector - INFO - 🌐 正在调用timepro.py获取 202507 的数据, 用户: 215829
2025-07-04 07:59:01,991 - server5-2.timepro_collector - INFO - ✅ timepro.py 数据获取成功。
2025-07-04 07:59:01,991 - server5-2.timepro_collector - INFO - ✅ 成功在内存中获取HTML内容。
2025-07-04 07:59:02,010 - server5-2.timepro_collector - INFO - ✅ 成功从HTML解析了 31 条记录。
2025-07-04 07:59:02,011 - server5-2.timepro_collector - WARNING - 无法解析时间 '__:__' (键: 退勤時刻)，将设为None。
2025-07-04 07:59:02,011 - server5-2.timepro_collector - WARNING - 无法解析时间 '__:__' (键: 出勤時刻)，将设为None。
2025-07-04 07:59:02,011 - server5-2.timepro_collector - WARNING - 无法解析时间 '__:__' (键: 退勤時刻)，将设为None。
2025-07-04 07:59:02,011 - server5-2.timepro_collector - WARNING - 无法解析时间 '__:__' (键: 出勤時刻)，将设为None。
2025-07-04 07:59:02,011 - server5-2.timepro_collector - WARNING - 无法解析时间 '__:__' (键: 退勤時刻)，将设为None。
2025-07-04 07:59:02,011 - server5-2.timepro_collector - WARNING - 无法解析时间 '__:__' (键: 出勤時刻)，将设为None。
2025-07-04 07:59:02,011 - server5-2.timepro_collector - WARNING - 无法解析时间 '__:__' (键: 退勤時刻)，将设为None。
2025-07-04 07:59:02,011 - server5-2.timepro_collector - WARNING - 无法解析时间 '__:__' (键: 出勤時刻)，将设为None。
2025-07-04 07:59:02,012 - server5-2.timepro_collector - WARNING - 无法解析时间 '__:__' (键: 退勤時刻)，将设为None。
2025-07-04 07:59:02,012 - server5-2.timepro_collector - WARNING - 无法解析时间 '__:__' (键: 出勤時刻)，将设为None。
2025-07-04 07:59:02,012 - server5-2.timepro_collector - WARNING - 无法解析时间 '__:__' (键: 退勤時刻)，将设为None。
2025-07-04 07:59:02,012 - server5-2.timepro_collector - WARNING - 无法解析时间 '__:__' (键: 出勤時刻)，将设为None。
2025-07-04 07:59:02,012 - server5-2.timepro_collector - WARNING - 无法解析时间 '__:__' (键: 退勤時刻)，将设为None。
2025-07-04 07:59:02,012 - server5-2.timepro_collector - WARNING - 无法解析时间 '__:__' (键: 出勤時刻)，将设为None。
2025-07-04 07:59:02,012 - server5-2.timepro_collector - WARNING - 无法解析时间 '__:__' (键: 退勤時刻)，将设为None。
2025-07-04 07:59:02,012 - server5-2.timepro_collector - WARNING - 无法解析时间 '__:__' (键: 出勤時刻)，将设为None。
2025-07-04 07:59:02,012 - server5-2.timepro_collector - WARNING - 无法解析时间 '__:__' (键: 退勤時刻)，将设为None。
2025-07-04 07:59:02,012 - server5-2.timepro_collector - WARNING - 无法解析时间 '__:__' (键: 出勤時刻)，将设为None。
2025-07-04 07:59:02,012 - server5-2.timepro_collector - WARNING - 无法解析时间 '__:__' (键: 退勤時刻)，将设为None。
2025-07-04 07:59:02,012 - server5-2.timepro_collector - WARNING - 无法解析时间 '__:__' (键: 出勤時刻)，将设为None。
2025-07-04 07:59:02,012 - server5-2.timepro_collector - WARNING - 无法解析时间 '__:__' (键: 退勤時刻)，将设为None。
2025-07-04 07:59:02,012 - server5-2.timepro_collector - WARNING - 无法解析时间 '__:__' (键: 出勤時刻)，将设为None。
2025-07-04 07:59:02,012 - server5-2.timepro_collector - WARNING - 无法解析时间 '__:__' (键: 退勤時刻)，将设为None。
2025-07-04 07:59:02,012 - server5-2.timepro_collector - WARNING - 无法解析时间 '__:__' (键: 出勤時刻)，将设为None。
2025-07-04 07:59:02,013 - server5-2.timepro_collector - WARNING - 无法解析时间 '__:__' (键: 退勤時刻)，将设为None。
2025-07-04 07:59:02,013 - server5-2.timepro_collector - WARNING - 无法解析时间 '__:__' (键: 出勤時刻)，将设为None。
2025-07-04 07:59:02,013 - server5-2.timepro_collector - WARNING - 无法解析时间 '__:__' (键: 退勤時刻)，将设为None。
2025-07-04 07:59:02,013 - server5-2.timepro_collector - WARNING - 无法解析时间 '__:__' (键: 出勤時刻)，将设为None。
2025-07-04 07:59:02,013 - server5-2.timepro_collector - WARNING - 无法解析时间 '__:__' (键: 退勤時刻)，将设为None。
2025-07-04 07:59:02,013 - server5-2.timepro_collector - WARNING - 无法解析时间 '__:__' (键: 出勤時刻)，将设为None。
2025-07-04 07:59:02,013 - server5-2.timepro_collector - WARNING - 无法解析时间 '__:__' (键: 退勤時刻)，将设为None。
2025-07-04 07:59:02,013 - server5-2.timepro_collector - WARNING - 无法解析时间 '__:__' (键: 出勤時刻)，将设为None。
2025-07-04 07:59:02,013 - server5-2.timepro_collector - WARNING - 无法解析时间 '__:__' (键: 退勤時刻)，将设为None。
2025-07-04 07:59:02,013 - server5-2.timepro_collector - WARNING - 无法解析时间 '__:__' (键: 出勤時刻)，将设为None。
2025-07-04 07:59:02,013 - server5-2.timepro_collector - WARNING - 无法解析时间 '__:__' (键: 退勤時刻)，将设为None。
2025-07-04 07:59:02,013 - server5-2.timepro_collector - WARNING - 无法解析时间 '__:__' (键: 出勤時刻)，将设为None。
2025-07-04 07:59:02,013 - server5-2.timepro_collector - WARNING - 无法解析时间 '__:__' (键: 退勤時刻)，将设为None。
2025-07-04 07:59:02,024 - server5-2.timeprotab_manager - INFO - 📅 分区 timeprotab_2507 已存在
2025-07-04 07:59:02,034 - server5-2.timeprotab_manager - INFO - 📅 分区 timeprotab_2507 已存在
2025-07-04 07:59:02,042 - server5-2.timeprotab_manager - INFO - 📅 分区 timeprotab_2507 已存在
2025-07-04 07:59:02,045 - server5-2.timeprotab_manager - INFO - 📅 分区 timeprotab_2507 已存在
2025-07-04 07:59:02,047 - server5-2.timeprotab_manager - INFO - 📅 分区 timeprotab_2507 已存在
2025-07-04 07:59:02,050 - server5-2.timeprotab_manager - INFO - 📅 分区 timeprotab_2507 已存在
2025-07-04 07:59:02,053 - server5-2.timeprotab_manager - INFO - 📅 分区 timeprotab_2507 已存在
2025-07-04 07:59:02,055 - server5-2.timeprotab_manager - INFO - 📅 分区 timeprotab_2507 已存在
2025-07-04 07:59:02,057 - server5-2.timeprotab_manager - INFO - 📅 分区 timeprotab_2507 已存在
2025-07-04 07:59:02,059 - server5-2.timeprotab_manager - INFO - 📅 分区 timeprotab_2507 已存在
2025-07-04 07:59:02,061 - server5-2.timeprotab_manager - INFO - 📅 分区 timeprotab_2507 已存在
2025-07-04 07:59:02,063 - server5-2.timeprotab_manager - INFO - 📅 分区 timeprotab_2507 已存在
2025-07-04 07:59:02,065 - server5-2.timeprotab_manager - INFO - 📅 分区 timeprotab_2507 已存在
2025-07-04 07:59:02,067 - server5-2.timeprotab_manager - INFO - 📅 分区 timeprotab_2507 已存在
2025-07-04 07:59:02,070 - server5-2.timeprotab_manager - INFO - 📅 分区 timeprotab_2507 已存在
2025-07-04 07:59:02,072 - server5-2.timeprotab_manager - INFO - 📅 分区 timeprotab_2507 已存在
2025-07-04 07:59:02,074 - server5-2.timeprotab_manager - INFO - 📅 分区 timeprotab_2507 已存在
2025-07-04 07:59:02,076 - server5-2.timeprotab_manager - INFO - 📅 分区 timeprotab_2507 已存在
2025-07-04 07:59:02,078 - server5-2.timeprotab_manager - INFO - 📅 分区 timeprotab_2507 已存在
2025-07-04 07:59:02,080 - server5-2.timeprotab_manager - INFO - 📅 分区 timeprotab_2507 已存在
2025-07-04 07:59:02,082 - server5-2.timeprotab_manager - INFO - 📅 分区 timeprotab_2507 已存在
2025-07-04 07:59:02,084 - server5-2.timeprotab_manager - INFO - 📅 分区 timeprotab_2507 已存在
2025-07-04 07:59:02,086 - server5-2.timeprotab_manager - INFO - 📅 分区 timeprotab_2507 已存在
2025-07-04 07:59:02,088 - server5-2.timeprotab_manager - INFO - 📅 分区 timeprotab_2507 已存在
2025-07-04 07:59:02,090 - server5-2.timeprotab_manager - INFO - 📅 分区 timeprotab_2507 已存在
2025-07-04 07:59:02,092 - server5-2.timeprotab_manager - INFO - 📅 分区 timeprotab_2507 已存在
2025-07-04 07:59:02,094 - server5-2.timeprotab_manager - INFO - 📅 分区 timeprotab_2507 已存在
2025-07-04 07:59:02,097 - server5-2.timeprotab_manager - INFO - 📅 分区 timeprotab_2507 已存在
2025-07-04 07:59:02,099 - server5-2.timeprotab_manager - INFO - 📅 分区 timeprotab_2507 已存在
2025-07-04 07:59:02,101 - server5-2.timeprotab_manager - INFO - 📅 分区 timeprotab_2507 已存在
2025-07-04 07:59:02,103 - server5-2.timeprotab_manager - INFO - 📅 分区 timeprotab_2507 已存在
2025-07-04 07:59:02,105 - server5-2.timeprotab_manager - INFO - 📅 分区 timeprotab_2507 已存在
2025-07-04 07:59:02,108 - server5-2.timeprotab_manager - INFO - ✅ 批量插入/更新了 31 条记录
2025-07-04 07:59:02,108 - server5-2.timepro_collector - INFO - ✅ 数据库插入成功: 31 条记录 (202507)。
2025-07-04 07:59:02,108 - server5-2.timepro_collector - INFO - 📅 开始为用户 '215829' 采集 202506 的数据
2025-07-04 07:59:02,108 - server5-2.timepro_collector - INFO - 🌐 正在调用timepro.py获取 202506 的数据, 用户: 215829
2025-07-04 07:59:03,274 - server5-2.timepro_collector - INFO - ✅ timepro.py 数据获取成功。
2025-07-04 07:59:03,274 - server5-2.timepro_collector - INFO - ✅ 成功在内存中获取HTML内容。
2025-07-04 07:59:03,292 - server5-2.timepro_collector - INFO - ✅ 成功从HTML解析了 30 条记录。
2025-07-04 07:59:03,295 - server5-2.timeprotab_manager - INFO - 📅 分区 timeprotab_2506 已存在
2025-07-04 07:59:03,297 - server5-2.timeprotab_manager - INFO - 📅 分区 timeprotab_2506 已存在
2025-07-04 07:59:03,300 - server5-2.timeprotab_manager - INFO - 📅 分区 timeprotab_2506 已存在
2025-07-04 07:59:03,302 - server5-2.timeprotab_manager - INFO - 📅 分区 timeprotab_2506 已存在
2025-07-04 07:59:03,304 - server5-2.timeprotab_manager - INFO - 📅 分区 timeprotab_2506 已存在
2025-07-04 07:59:03,306 - server5-2.timeprotab_manager - INFO - 📅 分区 timeprotab_2506 已存在
2025-07-04 07:59:03,308 - server5-2.timeprotab_manager - INFO - 📅 分区 timeprotab_2506 已存在
2025-07-04 07:59:03,310 - server5-2.timeprotab_manager - INFO - 📅 分区 timeprotab_2506 已存在
2025-07-04 07:59:03,312 - server5-2.timeprotab_manager - INFO - 📅 分区 timeprotab_2506 已存在
2025-07-04 07:59:03,315 - server5-2.timeprotab_manager - INFO - 📅 分区 timeprotab_2506 已存在
2025-07-04 07:59:03,317 - server5-2.timeprotab_manager - INFO - 📅 分区 timeprotab_2506 已存在
2025-07-04 07:59:03,319 - server5-2.timeprotab_manager - INFO - 📅 分区 timeprotab_2506 已存在
2025-07-04 07:59:03,321 - server5-2.timeprotab_manager - INFO - 📅 分区 timeprotab_2506 已存在
2025-07-04 07:59:03,323 - server5-2.timeprotab_manager - INFO - 📅 分区 timeprotab_2506 已存在
2025-07-04 07:59:03,325 - server5-2.timeprotab_manager - INFO - 📅 分区 timeprotab_2506 已存在
2025-07-04 07:59:03,327 - server5-2.timeprotab_manager - INFO - 📅 分区 timeprotab_2506 已存在
2025-07-04 07:59:03,329 - server5-2.timeprotab_manager - INFO - 📅 分区 timeprotab_2506 已存在
2025-07-04 07:59:03,331 - server5-2.timeprotab_manager - INFO - 📅 分区 timeprotab_2506 已存在
2025-07-04 07:59:03,333 - server5-2.timeprotab_manager - INFO - 📅 分区 timeprotab_2506 已存在
2025-07-04 07:59:03,335 - server5-2.timeprotab_manager - INFO - 📅 分区 timeprotab_2506 已存在
2025-07-04 07:59:03,337 - server5-2.timeprotab_manager - INFO - 📅 分区 timeprotab_2506 已存在
2025-07-04 07:59:03,340 - server5-2.timeprotab_manager - INFO - 📅 分区 timeprotab_2506 已存在
2025-07-04 07:59:03,342 - server5-2.timeprotab_manager - INFO - 📅 分区 timeprotab_2506 已存在
2025-07-04 07:59:03,344 - server5-2.timeprotab_manager - INFO - 📅 分区 timeprotab_2506 已存在
2025-07-04 07:59:03,346 - server5-2.timeprotab_manager - INFO - 📅 分区 timeprotab_2506 已存在
2025-07-04 07:59:03,348 - server5-2.timeprotab_manager - INFO - 📅 分区 timeprotab_2506 已存在
2025-07-04 07:59:03,350 - server5-2.timeprotab_manager - INFO - 📅 分区 timeprotab_2506 已存在
2025-07-04 07:59:03,352 - server5-2.timeprotab_manager - INFO - 📅 分区 timeprotab_2506 已存在
2025-07-04 07:59:03,354 - server5-2.timeprotab_manager - INFO - 📅 分区 timeprotab_2506 已存在
2025-07-04 07:59:03,356 - server5-2.timeprotab_manager - INFO - 📅 分区 timeprotab_2506 已存在
2025-07-04 07:59:03,358 - server5-2.timeprotab_manager - INFO - 📅 分区 timeprotab_2506 已存在
2025-07-04 07:59:03,364 - server5-2.timeprotab_manager - INFO - ✅ 批量插入/更新了 30 条记录
2025-07-04 07:59:03,365 - server5-2.timepro_collector - INFO - ✅ 数据库插入成功: 30 条记录 (202506)。
2025-07-04 07:59:03,365 - server5-2.timepro_collector - INFO - ⏳ 用户 215829 采集完成，等待10秒...
2025-07-04 07:59:13,365 - server5-2.timepro_collector - INFO - 👤 开始为用户 999999 采集
2025-07-04 07:59:13,365 - server5-2.timepro_collector - INFO - 📅 开始为用户 '999999' 采集 202507 的数据
2025-07-04 07:59:13,365 - server5-2.timepro_collector - INFO - 🌐 正在调用timepro.py获取 202507 的数据, 用户: testuser
2025-07-04 07:59:13,411 - server5-2.timepro_collector - ERROR - ❌ timepro.py 数据获取失败，未返回HTML内容。
2025-07-04 07:59:13,411 - server5-2.timepro_collector - ERROR - ❌ 用户 '999999' (202507) 的采集失败: 未能从网站获取HTML内容。
Traceback (most recent call last):
  File "/home/<USER>/Documents/testF/2025/ser25/server0613/MySuiteDatebase2402newuigood-yolo5-3/server5-2/services/timepro_collector.py", line 174, in _collect_user_month_data
    raise ValueError("未能从网站获取HTML内容。")
ValueError: 未能从网站获取HTML内容。
2025-07-04 07:59:13,411 - server5-2.timepro_collector - ERROR - ❌ 用户 999999 的数据采集失败: 未能从网站获取HTML内容。
2025-07-04 07:59:13,411 - server5-2.timepro_collector - INFO - ⏳ 用户 999999 采集完成，等待10秒...
2025-07-04 07:59:23,411 - server5-2.timepro_collector - INFO - ✅ 所有用户月度数据采集完成
2025-07-04 07:59:23,411 - server5-2.timepro_collector - INFO - ✅ 定时采集任务在主事件循环中执行完毕。
2025-07-04 08:00:37,800 - server5-2.main - INFO - 🔌 停止后台服务
2025-07-04 08:00:37,804 - server5-2.timeprotab_manager - INFO - 🔌 timeprotab数据库连接池已关闭
2025-07-04 08:00:37,805 - server5-2.timepro_collector - INFO - 🔌 timepro采集服务已停止
2025-07-10 18:14:44,861 - server5-2.test - INFO - 🧪 server5-2 测试工具启动 (真实、无文件采集)
2025-07-10 18:14:44,861 - server5-2.test - INFO - ============================================================
2025-07-10 18:14:44,861 - server5-2.test - INFO - 🎯 开始运行所有测试 (无文件内存流)
2025-07-10 18:14:44,861 - server5-2.test - INFO - 🧪 设置测试环境
2025-07-10 18:14:45,112 - server5-2.timeprotab_manager - INFO - ✅ timeprotab数据库连接池创建成功
2025-07-10 18:14:45,138 - server5-2.timeprotab_manager - INFO - ✅ 基础分区表 timeprotab 初始化完成
2025-07-10 18:14:45,141 - server5-2.timepro_collector - INFO - TimeproCollector instance created.
2025-07-10 18:14:45,141 - server5-2.test - INFO - ✅ 测试环境设置完成
2025-07-10 18:14:45,141 - server5-2.test - INFO - 
==================== 数据库连接 ====================
2025-07-10 18:14:45,141 - server5-2.test - INFO - 🧪 (1/3) 测试数据库连接
2025-07-10 18:14:45,161 - server5-2.test - INFO - ✅ 数据库连接和状态获取测试成功
2025-07-10 18:14:46,162 - server5-2.test - INFO - 
==================== 采集器初始化 ====================
2025-07-10 18:14:46,162 - server5-2.test - INFO - 🧪 (2/3) 测试采集器初始化和关闭
2025-07-10 18:14:46,162 - server5-2.timepro_collector - INFO - 🚀 启动 timepro采集服务...
2025-07-10 18:14:46,162 - server5-2.test - ERROR - ❌ 采集器初始化测试失败: 'TimeprotabManager' object has no attribute 'is_connected'
Traceback (most recent call last):
  File "/home/<USER>/Documents/testF/2025/ser25/server0613/MySuiteDatebase2402newuigood-yolo5-3/server5-2/test_server5_2.py", line 93, in test_collector_initialization
    await self.collector.start()
  File "/home/<USER>/Documents/testF/2025/ser25/server0613/MySuiteDatebase2402newuigood-yolo5-3/server5-2/services/timepro_collector.py", line 64, in start
    elif not self.db_manager.is_connected():
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
AttributeError: 'TimeprotabManager' object has no attribute 'is_connected'. Did you mean: 'disconnect'?
2025-07-10 18:14:46,163 - server5-2.test - ERROR - ❌ 测试失败: 采集器初始化。后续测试可能受影响。
2025-07-10 18:14:47,164 - server5-2.test - INFO - 
==================== 核心功能: 采集2025年6月真实数据 ====================
2025-07-10 18:14:47,164 - server5-2.test - INFO - 🧪 (3/3) 核心测试: 采集2025年6月的真实数据
2025-07-10 18:14:47,164 - server5-2.test - INFO - 🔐 测试用户: 215829
2025-07-10 18:14:47,164 - server5-2.test - INFO - 📅 开始采集指定月份: 202506
2025-07-10 18:14:47,164 - server5-2.timepro_collector - INFO - 📅 开始为用户 '215829' 采集 202506 的数据
2025-07-10 18:14:47,164 - server5-2.timepro_collector - INFO - 🌐 正在调用timepro.py获取 202506 的数据, 用户: 215829
2025-07-10 18:14:48,409 - server5-2.timepro_collector - INFO - ✅ timepro.py 数据获取成功。
2025-07-10 18:14:48,409 - server5-2.timepro_collector - INFO - ✅ 成功在内存中获取HTML内容。
2025-07-10 18:14:48,428 - server5-2.timepro_collector - INFO - ✅ 成功从HTML解析了 30 条记录。
2025-07-10 18:14:48,439 - server5-2.timeprotab_manager - INFO - 📅 分区 timeprotab_2506 已存在
2025-07-10 18:14:48,452 - server5-2.timeprotab_manager - INFO - 📅 分区 timeprotab_2506 已存在
2025-07-10 18:14:48,459 - server5-2.timeprotab_manager - INFO - 📅 分区 timeprotab_2506 已存在
2025-07-10 18:14:48,462 - server5-2.timeprotab_manager - INFO - 📅 分区 timeprotab_2506 已存在
2025-07-10 18:14:48,465 - server5-2.timeprotab_manager - INFO - 📅 分区 timeprotab_2506 已存在
2025-07-10 18:14:48,469 - server5-2.timeprotab_manager - INFO - 📅 分区 timeprotab_2506 已存在
2025-07-10 18:14:48,471 - server5-2.timeprotab_manager - INFO - 📅 分区 timeprotab_2506 已存在
2025-07-10 18:14:48,473 - server5-2.timeprotab_manager - INFO - 📅 分区 timeprotab_2506 已存在
2025-07-10 18:14:48,476 - server5-2.timeprotab_manager - INFO - 📅 分区 timeprotab_2506 已存在
2025-07-10 18:14:48,478 - server5-2.timeprotab_manager - INFO - 📅 分区 timeprotab_2506 已存在
2025-07-10 18:14:48,480 - server5-2.timeprotab_manager - INFO - 📅 分区 timeprotab_2506 已存在
2025-07-10 18:14:48,482 - server5-2.timeprotab_manager - INFO - 📅 分区 timeprotab_2506 已存在
2025-07-10 18:14:48,484 - server5-2.timeprotab_manager - INFO - 📅 分区 timeprotab_2506 已存在
2025-07-10 18:14:48,486 - server5-2.timeprotab_manager - INFO - 📅 分区 timeprotab_2506 已存在
2025-07-10 18:14:48,488 - server5-2.timeprotab_manager - INFO - 📅 分区 timeprotab_2506 已存在
2025-07-10 18:14:48,490 - server5-2.timeprotab_manager - INFO - 📅 分区 timeprotab_2506 已存在
2025-07-10 18:14:48,492 - server5-2.timeprotab_manager - INFO - 📅 分区 timeprotab_2506 已存在
2025-07-10 18:14:48,494 - server5-2.timeprotab_manager - INFO - 📅 分区 timeprotab_2506 已存在
2025-07-10 18:14:48,496 - server5-2.timeprotab_manager - INFO - 📅 分区 timeprotab_2506 已存在
2025-07-10 18:14:48,499 - server5-2.timeprotab_manager - INFO - 📅 分区 timeprotab_2506 已存在
2025-07-10 18:14:48,501 - server5-2.timeprotab_manager - INFO - 📅 分区 timeprotab_2506 已存在
2025-07-10 18:14:48,503 - server5-2.timeprotab_manager - INFO - 📅 分区 timeprotab_2506 已存在
2025-07-10 18:14:48,505 - server5-2.timeprotab_manager - INFO - 📅 分区 timeprotab_2506 已存在
2025-07-10 18:14:48,507 - server5-2.timeprotab_manager - INFO - 📅 分区 timeprotab_2506 已存在
2025-07-10 18:14:48,509 - server5-2.timeprotab_manager - INFO - 📅 分区 timeprotab_2506 已存在
2025-07-10 18:14:48,511 - server5-2.timeprotab_manager - INFO - 📅 分区 timeprotab_2506 已存在
2025-07-10 18:14:48,513 - server5-2.timeprotab_manager - INFO - 📅 分区 timeprotab_2506 已存在
2025-07-10 18:14:48,515 - server5-2.timeprotab_manager - INFO - 📅 分区 timeprotab_2506 已存在
2025-07-10 18:14:48,517 - server5-2.timeprotab_manager - INFO - 📅 分区 timeprotab_2506 已存在
2025-07-10 18:14:48,520 - server5-2.timeprotab_manager - INFO - 📅 分区 timeprotab_2506 已存在
2025-07-10 18:14:48,522 - server5-2.timeprotab_manager - INFO - 📅 分区 timeprotab_2506 已存在
2025-07-10 18:14:48,524 - server5-2.timeprotab_manager - INFO - ✅ 批量插入/更新了 30 条记录
2025-07-10 18:14:48,525 - server5-2.timepro_collector - INFO - ✅ 数据库插入成功: 30 条记录 (202506)。
2025-07-10 18:14:48,525 - server5-2.test - INFO - ✅ 指定月份真实数据采集调用完成
2025-07-10 18:14:48,525 - server5-2.test - INFO - 🔍 正在从数据库验证 2506 月份的数据...
2025-07-10 18:14:50,532 - server5-2.timeprotab_manager - INFO - 📊 查询到 30 条记录: 215829 - 2506
2025-07-10 18:14:50,533 - server5-2.test - INFO - 🎉 验证成功! 在数据库中找到 30 条记录 (2506)。
2025-07-10 18:14:50,533 - server5-2.test - INFO - --- 前5条记录示例 ---
2025-07-10 18:14:50,533 - server5-2.test - INFO -   - 日期: 2025-06-01, 出勤: None, コメント: 
2025-07-10 18:14:50,533 - server5-2.test - INFO -   - 日期: 2025-06-02, 出勤: 07:21:00, コメント: 計測システム開発
2025-07-10 18:14:50,533 - server5-2.test - INFO -   - 日期: 2025-06-03, 出勤: 07:18:00, コメント: 計測システム開発
2025-07-10 18:14:50,533 - server5-2.test - INFO -   - 日期: 2025-06-04, 出勤: 07:24:00, コメント: 計測システム開発
2025-07-10 18:14:50,533 - server5-2.test - INFO -   - 日期: 2025-06-05, 出勤: 07:18:00, コメント: 計測システム開発
2025-07-10 18:14:50,533 - server5-2.test - INFO - --------------------
2025-07-10 18:14:51,534 - server5-2.test - INFO - 
==================================================
2025-07-10 18:14:51,535 - server5-2.test - INFO - 📊 测试结果汇总:
2025-07-10 18:14:51,535 - server5-2.test - INFO -   - 数据库连接: ✅ 通过
2025-07-10 18:14:51,535 - server5-2.test - INFO -   - 采集器初始化: ❌ 失败
2025-07-10 18:14:51,535 - server5-2.test - INFO -   - 核心功能: 采集2025年6月真实数据: ✅ 通过
2025-07-10 18:14:51,535 - server5-2.test - INFO - 总计: 2/3 测试通过
2025-07-10 18:14:51,535 - server5-2.test - INFO - ==================================================
2025-07-10 18:14:51,540 - server5-2.timeprotab_manager - INFO - 🔌 timeprotab数据库连接池已关闭
2025-07-10 18:14:51,540 - server5-2.timepro_collector - INFO - 🔌 timepro采集服务已停止
2025-07-10 18:14:51,540 - server5-2.timeprotab_manager - INFO - 🔌 timeprotab数据库连接池已关闭
2025-07-10 18:14:51,540 - server5-2.test - INFO - ✅ 测试环境清理完成
2025-07-10 18:14:51,540 - server5-2.test - ERROR - ❌ 部分或全部测试失败。
2025-07-10 18:16:01,162 - server5-2.test - INFO - 🧪 server5-2 测试工具启动 (真实、无文件采集)
2025-07-10 18:16:01,162 - server5-2.test - INFO - ============================================================
2025-07-10 18:16:01,162 - server5-2.test - INFO - 🎯 开始运行所有测试 (无文件内存流)
2025-07-10 18:16:01,162 - server5-2.test - INFO - 🧪 设置测试环境
2025-07-10 18:16:01,446 - server5-2.timeprotab_manager - INFO - ✅ timeprotab数据库连接池创建成功
2025-07-10 18:16:01,460 - server5-2.timeprotab_manager - INFO - ✅ 基础分区表 timeprotab 初始化完成
2025-07-10 18:16:01,460 - server5-2.timepro_collector - INFO - TimeproCollector instance created.
2025-07-10 18:16:01,461 - server5-2.test - INFO - ✅ 测试环境设置完成
2025-07-10 18:16:01,461 - server5-2.test - INFO - 
==================== 数据库连接 ====================
2025-07-10 18:16:01,461 - server5-2.test - INFO - 🧪 (1/3) 测试数据库连接
2025-07-10 18:16:01,475 - server5-2.test - INFO - ✅ 数据库连接和状态获取测试成功
2025-07-10 18:16:02,476 - server5-2.test - INFO - 
==================== 采集器初始化 ====================
2025-07-10 18:16:02,476 - server5-2.test - INFO - 🧪 (2/3) 测试采集器初始化和关闭
2025-07-10 18:16:02,477 - server5-2.timepro_collector - INFO - 🚀 启动 timepro采集服务...
2025-07-10 18:16:02,477 - server5-2.test - ERROR - ❌ 采集器初始化测试失败: 'TimeprotabManager' object has no attribute 'is_connected'
Traceback (most recent call last):
  File "/home/<USER>/Documents/testF/2025/ser25/server0613/MySuiteDatebase2402newuigood-yolo5-3/server5-2/test_server5_2.py", line 93, in test_collector_initialization
    await self.collector.start()
  File "/home/<USER>/Documents/testF/2025/ser25/server0613/MySuiteDatebase2402newuigood-yolo5-3/server5-2/services/timepro_collector.py", line 64, in start
    elif not self.db_manager.is_connected():
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
AttributeError: 'TimeprotabManager' object has no attribute 'is_connected'. Did you mean: 'disconnect'?
2025-07-10 18:16:02,478 - server5-2.test - ERROR - ❌ 测试失败: 采集器初始化。后续测试可能受影响。
2025-07-10 18:16:03,479 - server5-2.test - INFO - 
==================== 核心功能: 采集2025年6月真实数据 ====================
2025-07-10 18:16:03,479 - server5-2.test - INFO - 🧪 (3/3) 核心测试: 采集2025年6月的真实数据
2025-07-10 18:16:03,479 - server5-2.test - INFO - 🔐 测试用户: 215829
2025-07-10 18:16:03,479 - server5-2.test - INFO - 📅 开始采集指定月份: 202507
2025-07-10 18:16:03,479 - server5-2.timepro_collector - INFO - 📅 开始为用户 '215829' 采集 202507 的数据
2025-07-10 18:16:03,479 - server5-2.timepro_collector - INFO - 🌐 正在调用timepro.py获取 202507 的数据, 用户: 215829
2025-07-10 18:16:04,695 - server5-2.timepro_collector - INFO - ✅ timepro.py 数据获取成功。
2025-07-10 18:16:04,695 - server5-2.timepro_collector - INFO - ✅ 成功在内存中获取HTML内容。
2025-07-10 18:16:04,714 - server5-2.timepro_collector - INFO - ✅ 成功从HTML解析了 31 条记录。
2025-07-10 18:16:04,715 - server5-2.timepro_collector - WARNING - 无法解析时间 '__:__' (键: 退勤時刻)，将设为None。
2025-07-10 18:16:04,715 - server5-2.timepro_collector - WARNING - 无法解析时间 '__:__' (键: 出勤時刻)，将设为None。
2025-07-10 18:16:04,715 - server5-2.timepro_collector - WARNING - 无法解析时间 '__:__' (键: 退勤時刻)，将设为None。
2025-07-10 18:16:04,715 - server5-2.timepro_collector - WARNING - 无法解析时间 '__:__' (键: 出勤時刻)，将设为None。
2025-07-10 18:16:04,715 - server5-2.timepro_collector - WARNING - 无法解析时间 '__:__' (键: 退勤時刻)，将设为None。
2025-07-10 18:16:04,715 - server5-2.timepro_collector - WARNING - 无法解析时间 '__:__' (键: 出勤時刻)，将设为None。
2025-07-10 18:16:04,715 - server5-2.timepro_collector - WARNING - 无法解析时间 '__:__' (键: 退勤時刻)，将设为None。
2025-07-10 18:16:04,715 - server5-2.timepro_collector - WARNING - 无法解析时间 '__:__' (键: 出勤時刻)，将设为None。
2025-07-10 18:16:04,715 - server5-2.timepro_collector - WARNING - 无法解析时间 '__:__' (键: 退勤時刻)，将设为None。
2025-07-10 18:16:04,715 - server5-2.timepro_collector - WARNING - 无法解析时间 '__:__' (键: 出勤時刻)，将设为None。
2025-07-10 18:16:04,715 - server5-2.timepro_collector - WARNING - 无法解析时间 '__:__' (键: 退勤時刻)，将设为None。
2025-07-10 18:16:04,715 - server5-2.timepro_collector - WARNING - 无法解析时间 '__:__' (键: 出勤時刻)，将设为None。
2025-07-10 18:16:04,715 - server5-2.timepro_collector - WARNING - 无法解析时间 '__:__' (键: 退勤時刻)，将设为None。
2025-07-10 18:16:04,716 - server5-2.timepro_collector - WARNING - 无法解析时间 '__:__' (键: 出勤時刻)，将设为None。
2025-07-10 18:16:04,716 - server5-2.timepro_collector - WARNING - 无法解析时间 '__:__' (键: 退勤時刻)，将设为None。
2025-07-10 18:16:04,716 - server5-2.timepro_collector - WARNING - 无法解析时间 '__:__' (键: 出勤時刻)，将设为None。
2025-07-10 18:16:04,716 - server5-2.timepro_collector - WARNING - 无法解析时间 '__:__' (键: 退勤時刻)，将设为None。
2025-07-10 18:16:04,716 - server5-2.timepro_collector - WARNING - 无法解析时间 '__:__' (键: 出勤時刻)，将设为None。
2025-07-10 18:16:04,716 - server5-2.timepro_collector - WARNING - 无法解析时间 '__:__' (键: 退勤時刻)，将设为None。
2025-07-10 18:16:04,716 - server5-2.timepro_collector - WARNING - 无法解析时间 '__:__' (键: 出勤時刻)，将设为None。
2025-07-10 18:16:04,716 - server5-2.timepro_collector - WARNING - 无法解析时间 '__:__' (键: 退勤時刻)，将设为None。
2025-07-10 18:16:04,716 - server5-2.timepro_collector - WARNING - 无法解析时间 '__:__' (键: 出勤時刻)，将设为None。
2025-07-10 18:16:04,716 - server5-2.timepro_collector - WARNING - 无法解析时间 '__:__' (键: 退勤時刻)，将设为None。
2025-07-10 18:16:04,716 - server5-2.timepro_collector - WARNING - 无法解析时间 '__:__' (键: 出勤時刻)，将设为None。
2025-07-10 18:16:04,716 - server5-2.timepro_collector - WARNING - 无法解析时间 '__:__' (键: 退勤時刻)，将设为None。
2025-07-10 18:16:04,716 - server5-2.timepro_collector - WARNING - 无法解析时间 '__:__' (键: 出勤時刻)，将设为None。
2025-07-10 18:16:04,716 - server5-2.timepro_collector - WARNING - 无法解析时间 '__:__' (键: 退勤時刻)，将设为None。
2025-07-10 18:16:04,716 - server5-2.timepro_collector - WARNING - 无法解析时间 '__:__' (键: 出勤時刻)，将设为None。
2025-07-10 18:16:04,716 - server5-2.timepro_collector - WARNING - 无法解析时间 '__:__' (键: 退勤時刻)，将设为None。
2025-07-10 18:16:04,722 - server5-2.timeprotab_manager - INFO - 📅 分区 timeprotab_2507 已存在
2025-07-10 18:16:04,731 - server5-2.timeprotab_manager - INFO - 📅 分区 timeprotab_2507 已存在
2025-07-10 18:16:04,737 - server5-2.timeprotab_manager - INFO - 📅 分区 timeprotab_2507 已存在
2025-07-10 18:16:04,740 - server5-2.timeprotab_manager - INFO - 📅 分区 timeprotab_2507 已存在
2025-07-10 18:16:04,742 - server5-2.timeprotab_manager - INFO - 📅 分区 timeprotab_2507 已存在
2025-07-10 18:16:04,745 - server5-2.timeprotab_manager - INFO - 📅 分区 timeprotab_2507 已存在
2025-07-10 18:16:04,748 - server5-2.timeprotab_manager - INFO - 📅 分区 timeprotab_2507 已存在
2025-07-10 18:16:04,750 - server5-2.timeprotab_manager - INFO - 📅 分区 timeprotab_2507 已存在
2025-07-10 18:16:04,752 - server5-2.timeprotab_manager - INFO - 📅 分区 timeprotab_2507 已存在
2025-07-10 18:16:04,754 - server5-2.timeprotab_manager - INFO - 📅 分区 timeprotab_2507 已存在
2025-07-10 18:16:04,756 - server5-2.timeprotab_manager - INFO - 📅 分区 timeprotab_2507 已存在
2025-07-10 18:16:04,758 - server5-2.timeprotab_manager - INFO - 📅 分区 timeprotab_2507 已存在
2025-07-10 18:16:04,760 - server5-2.timeprotab_manager - INFO - 📅 分区 timeprotab_2507 已存在
2025-07-10 18:16:04,762 - server5-2.timeprotab_manager - INFO - 📅 分区 timeprotab_2507 已存在
2025-07-10 18:16:04,764 - server5-2.timeprotab_manager - INFO - 📅 分区 timeprotab_2507 已存在
2025-07-10 18:16:04,766 - server5-2.timeprotab_manager - INFO - 📅 分区 timeprotab_2507 已存在
2025-07-10 18:16:04,769 - server5-2.timeprotab_manager - INFO - 📅 分区 timeprotab_2507 已存在
2025-07-10 18:16:04,771 - server5-2.timeprotab_manager - INFO - 📅 分区 timeprotab_2507 已存在
2025-07-10 18:16:04,773 - server5-2.timeprotab_manager - INFO - 📅 分区 timeprotab_2507 已存在
2025-07-10 18:16:04,775 - server5-2.timeprotab_manager - INFO - 📅 分区 timeprotab_2507 已存在
2025-07-10 18:16:04,778 - server5-2.timeprotab_manager - INFO - 📅 分区 timeprotab_2507 已存在
2025-07-10 18:16:04,780 - server5-2.timeprotab_manager - INFO - 📅 分区 timeprotab_2507 已存在
2025-07-10 18:16:04,782 - server5-2.timeprotab_manager - INFO - 📅 分区 timeprotab_2507 已存在
2025-07-10 18:16:04,784 - server5-2.timeprotab_manager - INFO - 📅 分区 timeprotab_2507 已存在
2025-07-10 18:16:04,786 - server5-2.timeprotab_manager - INFO - 📅 分区 timeprotab_2507 已存在
2025-07-10 18:16:04,788 - server5-2.timeprotab_manager - INFO - 📅 分区 timeprotab_2507 已存在
2025-07-10 18:16:04,790 - server5-2.timeprotab_manager - INFO - 📅 分区 timeprotab_2507 已存在
2025-07-10 18:16:04,792 - server5-2.timeprotab_manager - INFO - 📅 分区 timeprotab_2507 已存在
2025-07-10 18:16:04,794 - server5-2.timeprotab_manager - INFO - 📅 分区 timeprotab_2507 已存在
2025-07-10 18:16:04,796 - server5-2.timeprotab_manager - INFO - 📅 分区 timeprotab_2507 已存在
2025-07-10 18:16:04,798 - server5-2.timeprotab_manager - INFO - 📅 分区 timeprotab_2507 已存在
2025-07-10 18:16:04,801 - server5-2.timeprotab_manager - INFO - 📅 分区 timeprotab_2507 已存在
2025-07-10 18:16:04,804 - server5-2.timeprotab_manager - INFO - ✅ 批量插入/更新了 31 条记录
2025-07-10 18:16:04,804 - server5-2.timepro_collector - INFO - ✅ 数据库插入成功: 31 条记录 (202507)。
2025-07-10 18:16:04,804 - server5-2.test - INFO - ✅ 指定月份真实数据采集调用完成
2025-07-10 18:16:04,804 - server5-2.test - INFO - 🔍 正在从数据库验证 2507 月份的数据...
2025-07-10 18:16:06,810 - server5-2.timeprotab_manager - INFO - 📊 查询到 31 条记录: 215829 - 2507
2025-07-10 18:16:06,811 - server5-2.test - INFO - 🎉 验证成功! 在数据库中找到 31 条记录 (2507)。
2025-07-10 18:16:06,811 - server5-2.test - INFO - --- 前5条记录示例 ---
2025-07-10 18:16:06,811 - server5-2.test - INFO -   - 日期: 2025-07-01, 出勤: 07:18:00, コメント: 
2025-07-10 18:16:06,811 - server5-2.test - INFO -   - 日期: 2025-07-02, 出勤: 07:18:00, コメント: 
2025-07-10 18:16:06,811 - server5-2.test - INFO -   - 日期: 2025-07-03, 出勤: 07:18:00, コメント: 
2025-07-10 18:16:06,811 - server5-2.test - INFO -   - 日期: 2025-07-04, 出勤: 07:16:00, コメント: 
2025-07-10 18:16:06,811 - server5-2.test - INFO -   - 日期: 2025-07-05, 出勤: None, コメント: 
2025-07-10 18:16:06,811 - server5-2.test - INFO - --------------------
2025-07-10 18:16:07,813 - server5-2.test - INFO - 
==================================================
2025-07-10 18:16:07,813 - server5-2.test - INFO - 📊 测试结果汇总:
2025-07-10 18:16:07,813 - server5-2.test - INFO -   - 数据库连接: ✅ 通过
2025-07-10 18:16:07,814 - server5-2.test - INFO -   - 采集器初始化: ❌ 失败
2025-07-10 18:16:07,814 - server5-2.test - INFO -   - 核心功能: 采集2025年6月真实数据: ✅ 通过
2025-07-10 18:16:07,814 - server5-2.test - INFO - 总计: 2/3 测试通过
2025-07-10 18:16:07,814 - server5-2.test - INFO - ==================================================
2025-07-10 18:16:07,819 - server5-2.timeprotab_manager - INFO - 🔌 timeprotab数据库连接池已关闭
2025-07-10 18:16:07,819 - server5-2.timepro_collector - INFO - 🔌 timepro采集服务已停止
2025-07-10 18:16:07,819 - server5-2.timeprotab_manager - INFO - 🔌 timeprotab数据库连接池已关闭
2025-07-10 18:16:07,819 - server5-2.test - INFO - ✅ 测试环境清理完成
2025-07-10 18:16:07,819 - server5-2.test - ERROR - ❌ 部分或全部测试失败。
2025-07-11 08:36:25,289 - server5-2.test - INFO - 🧪 server5-2 测试工具启动 (真实、无文件采集)
2025-07-11 08:36:25,289 - server5-2.test - INFO - ============================================================
2025-07-11 08:36:25,289 - server5-2.test - INFO - 🎯 开始运行所有测试 (无文件内存流)
2025-07-11 08:36:25,289 - server5-2.test - INFO - 🧪 设置测试环境
2025-07-11 08:36:25,529 - server5-2.timeprotab_manager - INFO - ✅ timeprotab数据库连接池创建成功
2025-07-11 08:36:25,570 - server5-2.timeprotab_manager - INFO - ✅ 基础分区表 timeprotab 初始化完成
2025-07-11 08:36:25,571 - server5-2.timepro_collector - INFO - TimeproCollector instance created.
2025-07-11 08:36:25,572 - server5-2.test - INFO - ✅ 测试环境设置完成
2025-07-11 08:36:25,572 - server5-2.test - INFO - 
==================== 数据库连接 ====================
2025-07-11 08:36:25,572 - server5-2.test - INFO - 🧪 (1/3) 测试数据库连接
2025-07-11 08:36:25,588 - server5-2.test - INFO - ✅ 数据库连接和状态获取测试成功
2025-07-11 08:36:26,589 - server5-2.test - INFO - 
==================== 采集器初始化 ====================
2025-07-11 08:36:26,589 - server5-2.test - INFO - 🧪 (2/3) 测试采集器初始化和关闭
2025-07-11 08:36:26,589 - server5-2.timepro_collector - INFO - 🚀 启动 timepro采集服务...
2025-07-11 08:36:26,589 - server5-2.test - ERROR - ❌ 采集器初始化测试失败: 'TimeprotabManager' object has no attribute 'is_connected'
Traceback (most recent call last):
  File "/home/<USER>/Documents/testF/2025/ser25/server0613/MySuiteDatebase2402newuigood-yolo5-3/server5-2/test_server5_2.py", line 93, in test_collector_initialization
    await self.collector.start()
  File "/home/<USER>/Documents/testF/2025/ser25/server0613/MySuiteDatebase2402newuigood-yolo5-3/server5-2/services/timepro_collector.py", line 64, in start
    elif not self.db_manager.is_connected():
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
AttributeError: 'TimeprotabManager' object has no attribute 'is_connected'. Did you mean: 'disconnect'?
2025-07-11 08:36:26,590 - server5-2.test - ERROR - ❌ 测试失败: 采集器初始化。后续测试可能受影响。
2025-07-11 08:36:27,591 - server5-2.test - INFO - 
==================== 核心功能: 采集2025年6月真实数据 ====================
2025-07-11 08:36:27,591 - server5-2.test - INFO - 🧪 (3/3) 核心测试: 采集2025年6月的真实数据
2025-07-11 08:36:27,591 - server5-2.test - INFO - 🔐 测试用户: 215829
2025-07-11 08:36:27,592 - server5-2.test - INFO - 📅 开始采集指定月份: 202507
2025-07-11 08:36:27,592 - server5-2.timepro_collector - INFO - 📅 开始为用户 '215829' 采集 202507 的数据
2025-07-11 08:36:27,592 - server5-2.timepro_collector - INFO - 🌐 正在调用timepro.py获取 202507 的数据, 用户: 215829
2025-07-11 08:36:29,100 - server5-2.timepro_collector - INFO - ✅ timepro.py 数据获取成功。
2025-07-11 08:36:29,100 - server5-2.timepro_collector - INFO - ✅ 成功在内存中获取HTML内容。
2025-07-11 08:36:29,119 - server5-2.timepro_collector - INFO - ✅ 成功从HTML解析了 31 条记录。
2025-07-11 08:36:29,121 - server5-2.timepro_collector - WARNING - 无法解析时间 '__:__' (键: 退勤時刻)，将设为None。
2025-07-11 08:36:29,121 - server5-2.timepro_collector - WARNING - 无法解析时间 '__:__' (键: 出勤時刻)，将设为None。
2025-07-11 08:36:29,121 - server5-2.timepro_collector - WARNING - 无法解析时间 '__:__' (键: 退勤時刻)，将设为None。
2025-07-11 08:36:29,121 - server5-2.timepro_collector - WARNING - 无法解析时间 '__:__' (键: 出勤時刻)，将设为None。
2025-07-11 08:36:29,121 - server5-2.timepro_collector - WARNING - 无法解析时间 '__:__' (键: 退勤時刻)，将设为None。
2025-07-11 08:36:29,121 - server5-2.timepro_collector - WARNING - 无法解析时间 '__:__' (键: 出勤時刻)，将设为None。
2025-07-11 08:36:29,121 - server5-2.timepro_collector - WARNING - 无法解析时间 '__:__' (键: 退勤時刻)，将设为None。
2025-07-11 08:36:29,121 - server5-2.timepro_collector - WARNING - 无法解析时间 '__:__' (键: 出勤時刻)，将设为None。
2025-07-11 08:36:29,121 - server5-2.timepro_collector - WARNING - 无法解析时间 '__:__' (键: 退勤時刻)，将设为None。
2025-07-11 08:36:29,121 - server5-2.timepro_collector - WARNING - 无法解析时间 '__:__' (键: 出勤時刻)，将设为None。
2025-07-11 08:36:29,121 - server5-2.timepro_collector - WARNING - 无法解析时间 '__:__' (键: 退勤時刻)，将设为None。
2025-07-11 08:36:29,122 - server5-2.timepro_collector - WARNING - 无法解析时间 '__:__' (键: 出勤時刻)，将设为None。
2025-07-11 08:36:29,122 - server5-2.timepro_collector - WARNING - 无法解析时间 '__:__' (键: 退勤時刻)，将设为None。
2025-07-11 08:36:29,122 - server5-2.timepro_collector - WARNING - 无法解析时间 '__:__' (键: 出勤時刻)，将设为None。
2025-07-11 08:36:29,122 - server5-2.timepro_collector - WARNING - 无法解析时间 '__:__' (键: 退勤時刻)，将设为None。
2025-07-11 08:36:29,122 - server5-2.timepro_collector - WARNING - 无法解析时间 '__:__' (键: 出勤時刻)，将设为None。
2025-07-11 08:36:29,122 - server5-2.timepro_collector - WARNING - 无法解析时间 '__:__' (键: 退勤時刻)，将设为None。
2025-07-11 08:36:29,122 - server5-2.timepro_collector - WARNING - 无法解析时间 '__:__' (键: 出勤時刻)，将设为None。
2025-07-11 08:36:29,122 - server5-2.timepro_collector - WARNING - 无法解析时间 '__:__' (键: 退勤時刻)，将设为None。
2025-07-11 08:36:29,122 - server5-2.timepro_collector - WARNING - 无法解析时间 '__:__' (键: 出勤時刻)，将设为None。
2025-07-11 08:36:29,122 - server5-2.timepro_collector - WARNING - 无法解析时间 '__:__' (键: 退勤時刻)，将设为None。
2025-07-11 08:36:29,122 - server5-2.timepro_collector - WARNING - 无法解析时间 '__:__' (键: 出勤時刻)，将设为None。
2025-07-11 08:36:29,122 - server5-2.timepro_collector - WARNING - 无法解析时间 '__:__' (键: 退勤時刻)，将设为None。
2025-07-11 08:36:29,122 - server5-2.timepro_collector - WARNING - 无法解析时间 '__:__' (键: 出勤時刻)，将设为None。
2025-07-11 08:36:29,122 - server5-2.timepro_collector - WARNING - 无法解析时间 '__:__' (键: 退勤時刻)，将设为None。
2025-07-11 08:36:29,122 - server5-2.timepro_collector - WARNING - 无法解析时间 '__:__' (键: 出勤時刻)，将设为None。
2025-07-11 08:36:29,123 - server5-2.timepro_collector - WARNING - 无法解析时间 '__:__' (键: 退勤時刻)，将设为None。
2025-07-11 08:36:29,131 - server5-2.timeprotab_manager - INFO - 📅 分区 timeprotab_2507 已存在
2025-07-11 08:36:29,142 - server5-2.timeprotab_manager - INFO - 📅 分区 timeprotab_2507 已存在
2025-07-11 08:36:29,149 - server5-2.timeprotab_manager - INFO - 📅 分区 timeprotab_2507 已存在
2025-07-11 08:36:29,151 - server5-2.timeprotab_manager - INFO - 📅 分区 timeprotab_2507 已存在
2025-07-11 08:36:29,154 - server5-2.timeprotab_manager - INFO - 📅 分区 timeprotab_2507 已存在
2025-07-11 08:36:29,157 - server5-2.timeprotab_manager - INFO - 📅 分区 timeprotab_2507 已存在
2025-07-11 08:36:29,159 - server5-2.timeprotab_manager - INFO - 📅 分区 timeprotab_2507 已存在
2025-07-11 08:36:29,161 - server5-2.timeprotab_manager - INFO - 📅 分区 timeprotab_2507 已存在
2025-07-11 08:36:29,164 - server5-2.timeprotab_manager - INFO - 📅 分区 timeprotab_2507 已存在
2025-07-11 08:36:29,166 - server5-2.timeprotab_manager - INFO - 📅 分区 timeprotab_2507 已存在
2025-07-11 08:36:29,168 - server5-2.timeprotab_manager - INFO - 📅 分区 timeprotab_2507 已存在
2025-07-11 08:36:29,170 - server5-2.timeprotab_manager - INFO - 📅 分区 timeprotab_2507 已存在
2025-07-11 08:36:29,172 - server5-2.timeprotab_manager - INFO - 📅 分区 timeprotab_2507 已存在
2025-07-11 08:36:29,174 - server5-2.timeprotab_manager - INFO - 📅 分区 timeprotab_2507 已存在
2025-07-11 08:36:29,176 - server5-2.timeprotab_manager - INFO - 📅 分区 timeprotab_2507 已存在
2025-07-11 08:36:29,178 - server5-2.timeprotab_manager - INFO - 📅 分区 timeprotab_2507 已存在
2025-07-11 08:36:29,180 - server5-2.timeprotab_manager - INFO - 📅 分区 timeprotab_2507 已存在
2025-07-11 08:36:29,182 - server5-2.timeprotab_manager - INFO - 📅 分区 timeprotab_2507 已存在
2025-07-11 08:36:29,185 - server5-2.timeprotab_manager - INFO - 📅 分区 timeprotab_2507 已存在
2025-07-11 08:36:29,187 - server5-2.timeprotab_manager - INFO - 📅 分区 timeprotab_2507 已存在
2025-07-11 08:36:29,189 - server5-2.timeprotab_manager - INFO - 📅 分区 timeprotab_2507 已存在
2025-07-11 08:36:29,191 - server5-2.timeprotab_manager - INFO - 📅 分区 timeprotab_2507 已存在
2025-07-11 08:36:29,193 - server5-2.timeprotab_manager - INFO - 📅 分区 timeprotab_2507 已存在
2025-07-11 08:36:29,195 - server5-2.timeprotab_manager - INFO - 📅 分区 timeprotab_2507 已存在
2025-07-11 08:36:29,197 - server5-2.timeprotab_manager - INFO - 📅 分区 timeprotab_2507 已存在
2025-07-11 08:36:29,199 - server5-2.timeprotab_manager - INFO - 📅 分区 timeprotab_2507 已存在
2025-07-11 08:36:29,201 - server5-2.timeprotab_manager - INFO - 📅 分区 timeprotab_2507 已存在
2025-07-11 08:36:29,203 - server5-2.timeprotab_manager - INFO - 📅 分区 timeprotab_2507 已存在
2025-07-11 08:36:29,205 - server5-2.timeprotab_manager - INFO - 📅 分区 timeprotab_2507 已存在
2025-07-11 08:36:29,207 - server5-2.timeprotab_manager - INFO - 📅 分区 timeprotab_2507 已存在
2025-07-11 08:36:29,210 - server5-2.timeprotab_manager - INFO - 📅 分区 timeprotab_2507 已存在
2025-07-11 08:36:29,212 - server5-2.timeprotab_manager - INFO - 📅 分区 timeprotab_2507 已存在
2025-07-11 08:36:29,214 - server5-2.timeprotab_manager - INFO - ✅ 批量插入/更新了 31 条记录
2025-07-11 08:36:29,214 - server5-2.timepro_collector - INFO - ✅ 数据库插入成功: 31 条记录 (202507)。
2025-07-11 08:36:29,214 - server5-2.test - INFO - ✅ 指定月份真实数据采集调用完成
2025-07-11 08:36:29,215 - server5-2.test - INFO - 🔍 正在从数据库验证 2507 月份的数据...
2025-07-11 08:36:31,222 - server5-2.timeprotab_manager - INFO - 📊 查询到 31 条记录: 215829 - 2507
2025-07-11 08:36:31,223 - server5-2.test - INFO - 🎉 验证成功! 在数据库中找到 31 条记录 (2507)。
2025-07-11 08:36:31,223 - server5-2.test - INFO - --- 前5条记录示例 ---
2025-07-11 08:36:31,223 - server5-2.test - INFO -   - 日期: 2025-07-01, 出勤: 07:18:00, コメント: マルチシステム開発
2025-07-11 08:36:31,223 - server5-2.test - INFO -   - 日期: 2025-07-02, 出勤: 07:18:00, コメント: マルチシステム開発
2025-07-11 08:36:31,223 - server5-2.test - INFO -   - 日期: 2025-07-03, 出勤: 07:18:00, コメント: マルチシステム開発
2025-07-11 08:36:31,223 - server5-2.test - INFO -   - 日期: 2025-07-04, 出勤: 07:16:00, コメント: マルチシステム開発
2025-07-11 08:36:31,223 - server5-2.test - INFO -   - 日期: 2025-07-05, 出勤: None, コメント: 
2025-07-11 08:36:31,223 - server5-2.test - INFO - --------------------
2025-07-11 08:36:32,224 - server5-2.test - INFO - 
==================================================
2025-07-11 08:36:32,224 - server5-2.test - INFO - 📊 测试结果汇总:
2025-07-11 08:36:32,224 - server5-2.test - INFO -   - 数据库连接: ✅ 通过
2025-07-11 08:36:32,224 - server5-2.test - INFO -   - 采集器初始化: ❌ 失败
2025-07-11 08:36:32,224 - server5-2.test - INFO -   - 核心功能: 采集2025年6月真实数据: ✅ 通过
2025-07-11 08:36:32,225 - server5-2.test - INFO - 总计: 2/3 测试通过
2025-07-11 08:36:32,225 - server5-2.test - INFO - ==================================================
2025-07-11 08:36:32,229 - server5-2.timeprotab_manager - INFO - 🔌 timeprotab数据库连接池已关闭
2025-07-11 08:36:32,229 - server5-2.timepro_collector - INFO - 🔌 timepro采集服务已停止
2025-07-11 08:36:32,229 - server5-2.timeprotab_manager - INFO - 🔌 timeprotab数据库连接池已关闭
2025-07-11 08:36:32,229 - server5-2.test - INFO - ✅ 测试环境清理完成
2025-07-11 08:36:32,229 - server5-2.test - ERROR - ❌ 部分或全部测试失败。
2025-07-11 14:13:37,032 - server5-2.test - INFO - 🧪 server5-2 测试工具启动 (真实、无文件采集)
2025-07-11 14:13:37,032 - server5-2.test - INFO - ============================================================
2025-07-11 14:13:37,032 - server5-2.test - INFO - 🎯 开始运行所有测试 (无文件内存流)
2025-07-11 14:13:37,032 - server5-2.test - INFO - 🧪 设置测试环境
2025-07-11 14:13:37,259 - server5-2.timeprotab_manager - INFO - ✅ timeprotab数据库连接池创建成功
2025-07-11 14:13:37,292 - server5-2.timeprotab_manager - INFO - ✅ 基础分区表 timeprotab 初始化完成
2025-07-11 14:13:37,294 - server5-2.timepro_collector - INFO - TimeproCollector instance created.
2025-07-11 14:13:37,294 - server5-2.test - INFO - ✅ 测试环境设置完成
2025-07-11 14:13:37,294 - server5-2.test - INFO - 
==================== 数据库连接 ====================
2025-07-11 14:13:37,294 - server5-2.test - INFO - 🧪 (1/3) 测试数据库连接
2025-07-11 14:13:37,334 - server5-2.test - INFO - ✅ 数据库连接和状态获取测试成功
2025-07-11 14:13:38,336 - server5-2.test - INFO - 
==================== 采集器初始化 ====================
2025-07-11 14:13:38,336 - server5-2.test - INFO - 🧪 (2/3) 测试采集器初始化和关闭
2025-07-11 14:13:38,336 - server5-2.timepro_collector - INFO - 🚀 启动 timepro采集服务...
2025-07-11 14:13:38,336 - server5-2.test - ERROR - ❌ 采集器初始化测试失败: 'TimeprotabManager' object has no attribute 'is_connected'
Traceback (most recent call last):
  File "/home/<USER>/Documents/testF/2025/ser25/server0613/MySuiteDatebase2402newuigood-yolo5-3/server5-2/test_server5_2.py", line 93, in test_collector_initialization
    await self.collector.start()
  File "/home/<USER>/Documents/testF/2025/ser25/server0613/MySuiteDatebase2402newuigood-yolo5-3/server5-2/services/timepro_collector.py", line 64, in start
    elif not self.db_manager.is_connected():
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
AttributeError: 'TimeprotabManager' object has no attribute 'is_connected'. Did you mean: 'disconnect'?
2025-07-11 14:13:38,337 - server5-2.test - ERROR - ❌ 测试失败: 采集器初始化。后续测试可能受影响。
2025-07-11 14:13:39,338 - server5-2.test - INFO - 
==================== 核心功能: 采集2025年6月真实数据 ====================
2025-07-11 14:13:39,338 - server5-2.test - INFO - 🧪 (3/3) 核心测试: 采集2025年6月的真实数据
2025-07-11 14:13:39,338 - server5-2.test - INFO - 🔐 测试用户: 215829
2025-07-11 14:13:39,339 - server5-2.test - INFO - 📅 开始采集指定月份: 202507
2025-07-11 14:13:39,339 - server5-2.timepro_collector - INFO - 📅 开始为用户 '215829' 采集 202507 的数据
2025-07-11 14:13:39,339 - server5-2.timepro_collector - INFO - 🌐 正在调用timepro.py获取 202507 的数据, 用户: 215829
2025-07-11 14:13:40,599 - server5-2.timepro_collector - INFO - ✅ timepro.py 数据获取成功。
2025-07-11 14:13:40,599 - server5-2.timepro_collector - INFO - ✅ 成功在内存中获取HTML内容。
2025-07-11 14:13:40,619 - server5-2.timepro_collector - INFO - ✅ 成功从HTML解析了 31 条记录。
2025-07-11 14:13:40,620 - server5-2.timepro_collector - WARNING - 无法解析时间 '__:__' (键: 退勤時刻)，将设为None。
2025-07-11 14:13:40,620 - server5-2.timepro_collector - WARNING - 无法解析时间 '__:__' (键: 出勤時刻)，将设为None。
2025-07-11 14:13:40,620 - server5-2.timepro_collector - WARNING - 无法解析时间 '__:__' (键: 退勤時刻)，将设为None。
2025-07-11 14:13:40,620 - server5-2.timepro_collector - WARNING - 无法解析时间 '__:__' (键: 出勤時刻)，将设为None。
2025-07-11 14:13:40,620 - server5-2.timepro_collector - WARNING - 无法解析时间 '__:__' (键: 退勤時刻)，将设为None。
2025-07-11 14:13:40,620 - server5-2.timepro_collector - WARNING - 无法解析时间 '__:__' (键: 出勤時刻)，将设为None。
2025-07-11 14:13:40,620 - server5-2.timepro_collector - WARNING - 无法解析时间 '__:__' (键: 退勤時刻)，将设为None。
2025-07-11 14:13:40,620 - server5-2.timepro_collector - WARNING - 无法解析时间 '__:__' (键: 出勤時刻)，将设为None。
2025-07-11 14:13:40,620 - server5-2.timepro_collector - WARNING - 无法解析时间 '__:__' (键: 退勤時刻)，将设为None。
2025-07-11 14:13:40,620 - server5-2.timepro_collector - WARNING - 无法解析时间 '__:__' (键: 出勤時刻)，将设为None。
2025-07-11 14:13:40,620 - server5-2.timepro_collector - WARNING - 无法解析时间 '__:__' (键: 退勤時刻)，将设为None。
2025-07-11 14:13:40,620 - server5-2.timepro_collector - WARNING - 无法解析时间 '__:__' (键: 出勤時刻)，将设为None。
2025-07-11 14:13:40,620 - server5-2.timepro_collector - WARNING - 无法解析时间 '__:__' (键: 退勤時刻)，将设为None。
2025-07-11 14:13:40,620 - server5-2.timepro_collector - WARNING - 无法解析时间 '__:__' (键: 出勤時刻)，将设为None。
2025-07-11 14:13:40,620 - server5-2.timepro_collector - WARNING - 无法解析时间 '__:__' (键: 退勤時刻)，将设为None。
2025-07-11 14:13:40,620 - server5-2.timepro_collector - WARNING - 无法解析时间 '__:__' (键: 出勤時刻)，将设为None。
2025-07-11 14:13:40,621 - server5-2.timepro_collector - WARNING - 无法解析时间 '__:__' (键: 退勤時刻)，将设为None。
2025-07-11 14:13:40,621 - server5-2.timepro_collector - WARNING - 无法解析时间 '__:__' (键: 出勤時刻)，将设为None。
2025-07-11 14:13:40,621 - server5-2.timepro_collector - WARNING - 无法解析时间 '__:__' (键: 退勤時刻)，将设为None。
2025-07-11 14:13:40,621 - server5-2.timepro_collector - WARNING - 无法解析时间 '__:__' (键: 出勤時刻)，将设为None。
2025-07-11 14:13:40,621 - server5-2.timepro_collector - WARNING - 无法解析时间 '__:__' (键: 退勤時刻)，将设为None。
2025-07-11 14:13:40,621 - server5-2.timepro_collector - WARNING - 无法解析时间 '__:__' (键: 出勤時刻)，将设为None。
2025-07-11 14:13:40,621 - server5-2.timepro_collector - WARNING - 无法解析时间 '__:__' (键: 退勤時刻)，将设为None。
2025-07-11 14:13:40,621 - server5-2.timepro_collector - WARNING - 无法解析时间 '__:__' (键: 出勤時刻)，将设为None。
2025-07-11 14:13:40,621 - server5-2.timepro_collector - WARNING - 无法解析时间 '__:__' (键: 退勤時刻)，将设为None。
2025-07-11 14:13:40,621 - server5-2.timepro_collector - WARNING - 无法解析时间 '__:__' (键: 出勤時刻)，将设为None。
2025-07-11 14:13:40,621 - server5-2.timepro_collector - WARNING - 无法解析时间 '__:__' (键: 退勤時刻)，将设为None。
2025-07-11 14:13:40,632 - server5-2.timeprotab_manager - INFO - 📅 分区 timeprotab_2507 已存在
2025-07-11 14:13:40,642 - server5-2.timeprotab_manager - INFO - 📅 分区 timeprotab_2507 已存在
2025-07-11 14:13:40,648 - server5-2.timeprotab_manager - INFO - 📅 分区 timeprotab_2507 已存在
2025-07-11 14:13:40,651 - server5-2.timeprotab_manager - INFO - 📅 分区 timeprotab_2507 已存在
2025-07-11 14:13:40,653 - server5-2.timeprotab_manager - INFO - 📅 分区 timeprotab_2507 已存在
2025-07-11 14:13:40,656 - server5-2.timeprotab_manager - INFO - 📅 分区 timeprotab_2507 已存在
2025-07-11 14:13:40,658 - server5-2.timeprotab_manager - INFO - 📅 分区 timeprotab_2507 已存在
2025-07-11 14:13:40,660 - server5-2.timeprotab_manager - INFO - 📅 分区 timeprotab_2507 已存在
2025-07-11 14:13:40,662 - server5-2.timeprotab_manager - INFO - 📅 分区 timeprotab_2507 已存在
2025-07-11 14:13:40,665 - server5-2.timeprotab_manager - INFO - 📅 分区 timeprotab_2507 已存在
2025-07-11 14:13:40,667 - server5-2.timeprotab_manager - INFO - 📅 分区 timeprotab_2507 已存在
2025-07-11 14:13:40,669 - server5-2.timeprotab_manager - INFO - 📅 分区 timeprotab_2507 已存在
2025-07-11 14:13:40,671 - server5-2.timeprotab_manager - INFO - 📅 分区 timeprotab_2507 已存在
2025-07-11 14:13:40,673 - server5-2.timeprotab_manager - INFO - 📅 分区 timeprotab_2507 已存在
2025-07-11 14:13:40,675 - server5-2.timeprotab_manager - INFO - 📅 分区 timeprotab_2507 已存在
2025-07-11 14:13:40,677 - server5-2.timeprotab_manager - INFO - 📅 分区 timeprotab_2507 已存在
2025-07-11 14:13:40,680 - server5-2.timeprotab_manager - INFO - 📅 分区 timeprotab_2507 已存在
2025-07-11 14:13:40,682 - server5-2.timeprotab_manager - INFO - 📅 分区 timeprotab_2507 已存在
2025-07-11 14:13:40,684 - server5-2.timeprotab_manager - INFO - 📅 分区 timeprotab_2507 已存在
2025-07-11 14:13:40,686 - server5-2.timeprotab_manager - INFO - 📅 分区 timeprotab_2507 已存在
2025-07-11 14:13:40,688 - server5-2.timeprotab_manager - INFO - 📅 分区 timeprotab_2507 已存在
2025-07-11 14:13:40,690 - server5-2.timeprotab_manager - INFO - 📅 分区 timeprotab_2507 已存在
2025-07-11 14:13:40,692 - server5-2.timeprotab_manager - INFO - 📅 分区 timeprotab_2507 已存在
2025-07-11 14:13:40,694 - server5-2.timeprotab_manager - INFO - 📅 分区 timeprotab_2507 已存在
2025-07-11 14:13:40,696 - server5-2.timeprotab_manager - INFO - 📅 分区 timeprotab_2507 已存在
2025-07-11 14:13:40,698 - server5-2.timeprotab_manager - INFO - 📅 分区 timeprotab_2507 已存在
2025-07-11 14:13:40,700 - server5-2.timeprotab_manager - INFO - 📅 分区 timeprotab_2507 已存在
2025-07-11 14:13:40,702 - server5-2.timeprotab_manager - INFO - 📅 分区 timeprotab_2507 已存在
2025-07-11 14:13:40,704 - server5-2.timeprotab_manager - INFO - 📅 分区 timeprotab_2507 已存在
2025-07-11 14:13:40,707 - server5-2.timeprotab_manager - INFO - 📅 分区 timeprotab_2507 已存在
2025-07-11 14:13:40,709 - server5-2.timeprotab_manager - INFO - 📅 分区 timeprotab_2507 已存在
2025-07-11 14:13:40,711 - server5-2.timeprotab_manager - INFO - 📅 分区 timeprotab_2507 已存在
2025-07-11 14:13:40,714 - server5-2.timeprotab_manager - INFO - ✅ 批量插入/更新了 31 条记录
2025-07-11 14:13:40,714 - server5-2.timepro_collector - INFO - ✅ 数据库插入成功: 31 条记录 (202507)。
2025-07-11 14:13:40,714 - server5-2.test - INFO - ✅ 指定月份真实数据采集调用完成
2025-07-11 14:13:40,714 - server5-2.test - INFO - 🔍 正在从数据库验证 2507 月份的数据...
2025-07-11 14:13:42,721 - server5-2.timeprotab_manager - INFO - 📊 查询到 31 条记录: 215829 - 2507
2025-07-11 14:13:42,722 - server5-2.test - INFO - 🎉 验证成功! 在数据库中找到 31 条记录 (2507)。
2025-07-11 14:13:42,723 - server5-2.test - INFO - --- 前5条记录示例 ---
2025-07-11 14:13:42,723 - server5-2.test - INFO -   - 日期: 2025-07-01, 出勤: 07:18:00, コメント: マルチシステム開発
2025-07-11 14:13:42,724 - server5-2.test - INFO -   - 日期: 2025-07-02, 出勤: 07:18:00, コメント: マルチシステム開発
2025-07-11 14:13:42,724 - server5-2.test - INFO -   - 日期: 2025-07-03, 出勤: 07:18:00, コメント: マルチシステム開発
2025-07-11 14:13:42,724 - server5-2.test - INFO -   - 日期: 2025-07-04, 出勤: 07:16:00, コメント: マルチシステム開発
2025-07-11 14:13:42,724 - server5-2.test - INFO -   - 日期: 2025-07-05, 出勤: None, コメント: 
2025-07-11 14:13:42,724 - server5-2.test - INFO - --------------------
2025-07-11 14:13:43,726 - server5-2.test - INFO - 
==================================================
2025-07-11 14:13:43,726 - server5-2.test - INFO - 📊 测试结果汇总:
2025-07-11 14:13:43,726 - server5-2.test - INFO -   - 数据库连接: ✅ 通过
2025-07-11 14:13:43,726 - server5-2.test - INFO -   - 采集器初始化: ❌ 失败
2025-07-11 14:13:43,727 - server5-2.test - INFO -   - 核心功能: 采集2025年6月真实数据: ✅ 通过
2025-07-11 14:13:43,727 - server5-2.test - INFO - 总计: 2/3 测试通过
2025-07-11 14:13:43,727 - server5-2.test - INFO - ==================================================
2025-07-11 14:13:43,738 - server5-2.timeprotab_manager - INFO - 🔌 timeprotab数据库连接池已关闭
2025-07-11 14:13:43,738 - server5-2.timepro_collector - INFO - 🔌 timepro采集服务已停止
2025-07-11 14:13:43,738 - server5-2.timeprotab_manager - INFO - 🔌 timeprotab数据库连接池已关闭
2025-07-11 14:13:43,739 - server5-2.test - INFO - ✅ 测试环境清理完成
2025-07-11 14:13:43,739 - server5-2.test - ERROR - ❌ 部分或全部测试失败。
2025-07-11 14:14:02,066 - server5-2.test - INFO - 🧪 server5-2 测试工具启动 (真实、无文件采集)
2025-07-11 14:14:02,066 - server5-2.test - INFO - ============================================================
2025-07-11 14:14:02,066 - server5-2.test - INFO - 🎯 开始运行所有测试 (无文件内存流)
2025-07-11 14:14:02,066 - server5-2.test - INFO - 🧪 设置测试环境
2025-07-11 14:14:02,320 - server5-2.timeprotab_manager - INFO - ✅ timeprotab数据库连接池创建成功
2025-07-11 14:14:02,336 - server5-2.timeprotab_manager - INFO - ✅ 基础分区表 timeprotab 初始化完成
2025-07-11 14:14:02,338 - server5-2.timepro_collector - INFO - TimeproCollector instance created.
2025-07-11 14:14:02,338 - server5-2.test - INFO - ✅ 测试环境设置完成
2025-07-11 14:14:02,338 - server5-2.test - INFO - 
==================== 数据库连接 ====================
2025-07-11 14:14:02,338 - server5-2.test - INFO - 🧪 (1/3) 测试数据库连接
2025-07-11 14:14:02,356 - server5-2.test - INFO - ✅ 数据库连接和状态获取测试成功
2025-07-11 14:14:03,357 - server5-2.test - INFO - 
==================== 采集器初始化 ====================
2025-07-11 14:14:03,357 - server5-2.test - INFO - 🧪 (2/3) 测试采集器初始化和关闭
2025-07-11 14:14:03,357 - server5-2.timepro_collector - INFO - 🚀 启动 timepro采集服务...
2025-07-11 14:14:03,358 - server5-2.test - ERROR - ❌ 采集器初始化测试失败: 'TimeprotabManager' object has no attribute 'is_connected'
Traceback (most recent call last):
  File "/home/<USER>/Documents/testF/2025/ser25/server0613/MySuiteDatebase2402newuigood-yolo5-3/server5-2/test_server5_2.py", line 93, in test_collector_initialization
    await self.collector.start()
  File "/home/<USER>/Documents/testF/2025/ser25/server0613/MySuiteDatebase2402newuigood-yolo5-3/server5-2/services/timepro_collector.py", line 64, in start
    elif not self.db_manager.is_connected():
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
AttributeError: 'TimeprotabManager' object has no attribute 'is_connected'. Did you mean: 'disconnect'?
2025-07-11 14:14:03,359 - server5-2.test - ERROR - ❌ 测试失败: 采集器初始化。后续测试可能受影响。
2025-07-11 14:14:04,360 - server5-2.test - INFO - 
==================== 核心功能: 采集2025年6月真实数据 ====================
2025-07-11 14:14:04,360 - server5-2.test - INFO - 🧪 (3/3) 核心测试: 采集2025年6月的真实数据
2025-07-11 14:14:04,360 - server5-2.test - INFO - 🔐 测试用户: 215829
2025-07-11 14:14:04,360 - server5-2.test - INFO - 📅 开始采集指定月份: 202504
2025-07-11 14:14:04,360 - server5-2.timepro_collector - INFO - 📅 开始为用户 '215829' 采集 202504 的数据
2025-07-11 14:14:04,360 - server5-2.timepro_collector - INFO - 🌐 正在调用timepro.py获取 202504 的数据, 用户: 215829
2025-07-11 14:14:05,546 - server5-2.timepro_collector - INFO - ✅ timepro.py 数据获取成功。
2025-07-11 14:14:05,546 - server5-2.timepro_collector - INFO - ✅ 成功在内存中获取HTML内容。
2025-07-11 14:14:05,565 - server5-2.timepro_collector - INFO - ✅ 成功从HTML解析了 30 条记录。
2025-07-11 14:14:05,636 - server5-2.timeprotab_manager - INFO - ✅ 创建分区成功: timeprotab_2504 (2025-04-01 ~ 2025-05-01)
2025-07-11 14:14:05,646 - server5-2.timeprotab_manager - INFO - 📅 分区 timeprotab_2504 已存在
2025-07-11 14:14:05,655 - server5-2.timeprotab_manager - INFO - 📅 分区 timeprotab_2504 已存在
2025-07-11 14:14:05,658 - server5-2.timeprotab_manager - INFO - 📅 分区 timeprotab_2504 已存在
2025-07-11 14:14:05,660 - server5-2.timeprotab_manager - INFO - 📅 分区 timeprotab_2504 已存在
2025-07-11 14:14:05,663 - server5-2.timeprotab_manager - INFO - 📅 分区 timeprotab_2504 已存在
2025-07-11 14:14:05,665 - server5-2.timeprotab_manager - INFO - 📅 分区 timeprotab_2504 已存在
2025-07-11 14:14:05,667 - server5-2.timeprotab_manager - INFO - 📅 分区 timeprotab_2504 已存在
2025-07-11 14:14:05,669 - server5-2.timeprotab_manager - INFO - 📅 分区 timeprotab_2504 已存在
2025-07-11 14:14:05,671 - server5-2.timeprotab_manager - INFO - 📅 分区 timeprotab_2504 已存在
2025-07-11 14:14:05,674 - server5-2.timeprotab_manager - INFO - 📅 分区 timeprotab_2504 已存在
2025-07-11 14:14:05,676 - server5-2.timeprotab_manager - INFO - 📅 分区 timeprotab_2504 已存在
2025-07-11 14:14:05,678 - server5-2.timeprotab_manager - INFO - 📅 分区 timeprotab_2504 已存在
2025-07-11 14:14:05,680 - server5-2.timeprotab_manager - INFO - 📅 分区 timeprotab_2504 已存在
2025-07-11 14:14:05,682 - server5-2.timeprotab_manager - INFO - 📅 分区 timeprotab_2504 已存在
2025-07-11 14:14:05,684 - server5-2.timeprotab_manager - INFO - 📅 分区 timeprotab_2504 已存在
2025-07-11 14:14:05,686 - server5-2.timeprotab_manager - INFO - 📅 分区 timeprotab_2504 已存在
2025-07-11 14:14:05,688 - server5-2.timeprotab_manager - INFO - 📅 分区 timeprotab_2504 已存在
2025-07-11 14:14:05,690 - server5-2.timeprotab_manager - INFO - 📅 分区 timeprotab_2504 已存在
2025-07-11 14:14:05,692 - server5-2.timeprotab_manager - INFO - 📅 分区 timeprotab_2504 已存在
2025-07-11 14:14:05,694 - server5-2.timeprotab_manager - INFO - 📅 分区 timeprotab_2504 已存在
2025-07-11 14:14:05,696 - server5-2.timeprotab_manager - INFO - 📅 分区 timeprotab_2504 已存在
2025-07-11 14:14:05,698 - server5-2.timeprotab_manager - INFO - 📅 分区 timeprotab_2504 已存在
2025-07-11 14:14:05,701 - server5-2.timeprotab_manager - INFO - 📅 分区 timeprotab_2504 已存在
2025-07-11 14:14:05,703 - server5-2.timeprotab_manager - INFO - 📅 分区 timeprotab_2504 已存在
2025-07-11 14:14:05,705 - server5-2.timeprotab_manager - INFO - 📅 分区 timeprotab_2504 已存在
2025-07-11 14:14:05,707 - server5-2.timeprotab_manager - INFO - 📅 分区 timeprotab_2504 已存在
2025-07-11 14:14:05,709 - server5-2.timeprotab_manager - INFO - 📅 分区 timeprotab_2504 已存在
2025-07-11 14:14:05,711 - server5-2.timeprotab_manager - INFO - 📅 分区 timeprotab_2504 已存在
2025-07-11 14:14:05,713 - server5-2.timeprotab_manager - INFO - 📅 分区 timeprotab_2504 已存在
2025-07-11 14:14:05,715 - server5-2.timeprotab_manager - INFO - 📅 分区 timeprotab_2504 已存在
2025-07-11 14:14:05,718 - server5-2.timeprotab_manager - INFO - ✅ 批量插入/更新了 30 条记录
2025-07-11 14:14:05,718 - server5-2.timepro_collector - INFO - ✅ 数据库插入成功: 30 条记录 (202504)。
2025-07-11 14:14:05,718 - server5-2.test - INFO - ✅ 指定月份真实数据采集调用完成
2025-07-11 14:14:05,718 - server5-2.test - INFO - 🔍 正在从数据库验证 2504 月份的数据...
2025-07-11 14:14:07,726 - server5-2.timeprotab_manager - INFO - 📊 查询到 30 条记录: 215829 - 2504
2025-07-11 14:14:07,727 - server5-2.test - INFO - 🎉 验证成功! 在数据库中找到 30 条记录 (2504)。
2025-07-11 14:14:07,728 - server5-2.test - INFO - --- 前5条记录示例 ---
2025-07-11 14:14:07,728 - server5-2.test - INFO -   - 日期: 2025-04-01, 出勤: 07:24:00, コメント: 計測システム開発
2025-07-11 14:14:07,728 - server5-2.test - INFO -   - 日期: 2025-04-02, 出勤: 07:22:00, コメント: 計測システム開発
2025-07-11 14:14:07,728 - server5-2.test - INFO -   - 日期: 2025-04-03, 出勤: 07:21:00, コメント: 計測システム開発
2025-07-11 14:14:07,729 - server5-2.test - INFO -   - 日期: 2025-04-04, 出勤: 07:20:00, コメント: 計測システム開発
2025-07-11 14:14:07,729 - server5-2.test - INFO -   - 日期: 2025-04-05, 出勤: None, コメント: 
2025-07-11 14:14:07,729 - server5-2.test - INFO - --------------------
2025-07-11 14:14:08,730 - server5-2.test - INFO - 
==================================================
2025-07-11 14:14:08,730 - server5-2.test - INFO - 📊 测试结果汇总:
2025-07-11 14:14:08,730 - server5-2.test - INFO -   - 数据库连接: ✅ 通过
2025-07-11 14:14:08,730 - server5-2.test - INFO -   - 采集器初始化: ❌ 失败
2025-07-11 14:14:08,730 - server5-2.test - INFO -   - 核心功能: 采集2025年6月真实数据: ✅ 通过
2025-07-11 14:14:08,730 - server5-2.test - INFO - 总计: 2/3 测试通过
2025-07-11 14:14:08,730 - server5-2.test - INFO - ==================================================
2025-07-11 14:14:08,734 - server5-2.timeprotab_manager - INFO - 🔌 timeprotab数据库连接池已关闭
2025-07-11 14:14:08,734 - server5-2.timepro_collector - INFO - 🔌 timepro采集服务已停止
2025-07-11 14:14:08,734 - server5-2.timeprotab_manager - INFO - 🔌 timeprotab数据库连接池已关闭
2025-07-11 14:14:08,734 - server5-2.test - INFO - ✅ 测试环境清理完成
2025-07-11 14:14:08,734 - server5-2.test - ERROR - ❌ 部分或全部测试失败。
