INFO:     Will watch for changes in these directories: ['/home/<USER>/Documents/testF/2025/ser25/server0613/MySuiteDatebase2402newuigood-yolo5-3/server3/app']
INFO:     Uvicorn running on http://0.0.0.0:8006 (Press CTRL+C to quit)
INFO:     Started reloader process [1841590] using WatchFiles
============================================================
🚀 Starting MySuite Auth Microservice
============================================================
📁 Working Directory: /home/<USER>/Documents/testF/2025/ser25/server0613/MySuiteDatebase2402newuigood-yolo5-3/server3
🌐 Service Port: 8006
📝 API Documentation: http://localhost:8006/docs
🔍 Health Check: http://localhost:8006/health
============================================================
INFO:     Started server process [1841594]
INFO:     Waiting for application startup.
=== Auth Service Starting Up ===
2025-07-09 16:12:45,281 INFO sqlalchemy.engine.Engine select pg_catalog.version()
2025-07-09 16:12:45,281 INFO sqlalchemy.engine.Engine [raw sql] ()
2025-07-09 16:12:45,284 INFO sqlalchemy.engine.Engine select current_schema()
2025-07-09 16:12:45,284 INFO sqlalchemy.engine.Engine [raw sql] ()
2025-07-09 16:12:45,287 INFO sqlalchemy.engine.Engine show standard_conforming_strings
2025-07-09 16:12:45,287 INFO sqlalchemy.engine.Engine [raw sql] ()
2025-07-09 16:12:45,289 INFO sqlalchemy.engine.Engine BEGIN (implicit)
2025-07-09 16:12:45,292 INFO sqlalchemy.engine.Engine SELECT pg_catalog.pg_class.relname 
FROM pg_catalog.pg_class JOIN pg_catalog.pg_namespace ON pg_catalog.pg_namespace.oid = pg_catalog.pg_class.relnamespace 
WHERE pg_catalog.pg_class.relname = $1::VARCHAR AND pg_catalog.pg_class.relkind = ANY (ARRAY[$2::VARCHAR, $3::VARCHAR, $4::VARCHAR, $5::VARCHAR, $6::VARCHAR]) AND pg_catalog.pg_table_is_visible(pg_catalog.pg_class.oid) AND pg_catalog.pg_namespace.nspname != $7::VARCHAR
2025-07-09 16:12:45,292 INFO sqlalchemy.engine.Engine [generated in 0.00016s] ('users', 'r', 'p', 'f', 'v', 'm', 'pg_catalog')
2025-07-09 16:12:45,298 INFO sqlalchemy.engine.Engine SELECT pg_catalog.pg_class.relname 
FROM pg_catalog.pg_class JOIN pg_catalog.pg_namespace ON pg_catalog.pg_namespace.oid = pg_catalog.pg_class.relnamespace 
WHERE pg_catalog.pg_class.relname = $1::VARCHAR AND pg_catalog.pg_class.relkind = ANY (ARRAY[$2::VARCHAR, $3::VARCHAR, $4::VARCHAR, $5::VARCHAR, $6::VARCHAR]) AND pg_catalog.pg_table_is_visible(pg_catalog.pg_class.oid) AND pg_catalog.pg_namespace.nspname != $7::VARCHAR
2025-07-09 16:12:45,298 INFO sqlalchemy.engine.Engine [cached since 0.005991s ago] ('hardware_fingerprints', 'r', 'p', 'f', 'v', 'm', 'pg_catalog')
2025-07-09 16:12:45,299 INFO sqlalchemy.engine.Engine COMMIT
INFO:     Application startup complete.
✅ Database for auth service initialized!
🚀 Auth Service startup complete.
INFO:     127.0.0.1:38340 - "GET /health HTTP/1.1" 200 OK
INFO:     127.0.0.1:38352 - "GET /health HTTP/1.1" 200 OK
2025-07-09 16:13:07,637 INFO sqlalchemy.engine.Engine BEGIN (implicit)
2025-07-09 16:13:07,638 INFO sqlalchemy.engine.Engine SELECT hardware_fingerprints.employee_id, hardware_fingerprints.fingerprint, hardware_fingerprints.registration_date 
FROM hardware_fingerprints 
WHERE hardware_fingerprints.employee_id = $1::VARCHAR
2025-07-09 16:13:07,638 INFO sqlalchemy.engine.Engine [generated in 0.00012s] ('215829',)
2025-07-09 16:13:07,643 INFO sqlalchemy.engine.Engine ROLLBACK
INFO:     127.0.0.1:0 - "POST /api/hardware/verify HTTP/1.0" 200 OK
INFO:     127.0.0.1:0 - "POST /login HTTP/1.0" 200 OK
INFO:     127.0.0.1:59672 - "POST /login HTTP/1.1" 200 OK
INFO:     127.0.0.1:59678 - "GET /api/program/permissions/215829 HTTP/1.1" 200 OK
INFO:     127.0.0.1:59690 - "POST /api/program/authorize HTTP/1.1" 200 OK
INFO:     127.0.0.1:59706 - "GET /api/verify HTTP/1.1" 200 OK
INFO:     127.0.0.1:59716 - "GET /api/verify HTTP/1.1" 200 OK
2025-07-09 16:19:19,204 INFO sqlalchemy.engine.Engine BEGIN (implicit)
2025-07-09 16:19:19,205 INFO sqlalchemy.engine.Engine SELECT hardware_fingerprints.employee_id, hardware_fingerprints.fingerprint, hardware_fingerprints.registration_date 
FROM hardware_fingerprints 
WHERE hardware_fingerprints.employee_id = $1::VARCHAR
2025-07-09 16:19:19,205 INFO sqlalchemy.engine.Engine [cached since 371.6s ago] ('215829',)
2025-07-09 16:19:19,209 INFO sqlalchemy.engine.Engine ROLLBACK
INFO:     127.0.0.1:0 - "POST /api/hardware/verify HTTP/1.0" 200 OK
INFO:     127.0.0.1:0 - "POST /login HTTP/1.0" 200 OK
INFO:     127.0.0.1:50566 - "POST /login HTTP/1.1" 200 OK
INFO:     127.0.0.1:50580 - "GET /api/program/permissions/215829 HTTP/1.1" 200 OK
INFO:     127.0.0.1:50590 - "POST /api/program/authorize HTTP/1.1" 200 OK
INFO:     127.0.0.1:50604 - "GET /api/verify HTTP/1.1" 200 OK
INFO:     127.0.0.1:50618 - "GET /api/verify HTTP/1.1" 200 OK
2025-07-09 16:31:20,381 INFO sqlalchemy.engine.Engine BEGIN (implicit)
2025-07-09 16:31:20,382 INFO sqlalchemy.engine.Engine SELECT hardware_fingerprints.employee_id, hardware_fingerprints.fingerprint, hardware_fingerprints.registration_date 
FROM hardware_fingerprints 
WHERE hardware_fingerprints.employee_id = $1::VARCHAR
2025-07-09 16:31:20,382 INFO sqlalchemy.engine.Engine [cached since 1093s ago] ('215829',)
2025-07-09 16:31:20,386 INFO sqlalchemy.engine.Engine ROLLBACK
INFO:     127.0.0.1:0 - "POST /api/hardware/verify HTTP/1.0" 200 OK
INFO:     127.0.0.1:0 - "POST /login HTTP/1.0" 200 OK
INFO:     127.0.0.1:54372 - "POST /login HTTP/1.1" 200 OK
INFO:     127.0.0.1:54388 - "GET /api/program/permissions/215829 HTTP/1.1" 200 OK
INFO:     127.0.0.1:54402 - "POST /api/program/authorize HTTP/1.1" 200 OK
INFO:     127.0.0.1:54406 - "GET /api/verify HTTP/1.1" 200 OK
INFO:     127.0.0.1:54422 - "GET /api/verify HTTP/1.1" 200 OK
2025-07-09 16:32:03,544 INFO sqlalchemy.engine.Engine BEGIN (implicit)
2025-07-09 16:32:03,545 INFO sqlalchemy.engine.Engine SELECT hardware_fingerprints.employee_id, hardware_fingerprints.fingerprint, hardware_fingerprints.registration_date 
FROM hardware_fingerprints 
WHERE hardware_fingerprints.employee_id = $1::VARCHAR
2025-07-09 16:32:03,545 INFO sqlalchemy.engine.Engine [cached since 1136s ago] ('215829',)
2025-07-09 16:32:03,547 INFO sqlalchemy.engine.Engine ROLLBACK
INFO:     127.0.0.1:0 - "POST /api/hardware/verify HTTP/1.0" 200 OK
INFO:     127.0.0.1:0 - "POST /login HTTP/1.0" 200 OK
INFO:     127.0.0.1:55992 - "POST /login HTTP/1.1" 200 OK
INFO:     127.0.0.1:55996 - "GET /api/program/permissions/215829 HTTP/1.1" 200 OK
INFO:     127.0.0.1:55998 - "POST /api/program/authorize HTTP/1.1" 200 OK
INFO:     127.0.0.1:56006 - "GET /api/verify HTTP/1.1" 200 OK
INFO:     127.0.0.1:56012 - "GET /api/verify HTTP/1.1" 200 OK
INFO:     127.0.0.1:35492 - "POST /api/program/authorize HTTP/1.1" 200 OK
INFO:     127.0.0.1:35496 - "GET /api/verify HTTP/1.1" 200 OK
INFO:     127.0.0.1:35506 - "GET /api/verify HTTP/1.1" 200 OK
2025-07-09 16:59:28,342 INFO sqlalchemy.engine.Engine BEGIN (implicit)
2025-07-09 16:59:28,343 INFO sqlalchemy.engine.Engine SELECT hardware_fingerprints.employee_id, hardware_fingerprints.fingerprint, hardware_fingerprints.registration_date 
FROM hardware_fingerprints 
WHERE hardware_fingerprints.employee_id = $1::VARCHAR
2025-07-09 16:59:28,343 INFO sqlalchemy.engine.Engine [cached since 2781s ago] ('215829',)
2025-07-09 16:59:28,344 INFO sqlalchemy.engine.Engine ROLLBACK
INFO:     127.0.0.1:0 - "POST /api/hardware/verify HTTP/1.0" 200 OK
INFO:     127.0.0.1:0 - "POST /login HTTP/1.0" 200 OK
INFO:     127.0.0.1:42106 - "POST /login HTTP/1.1" 200 OK
INFO:     127.0.0.1:42110 - "GET /api/program/permissions/215829 HTTP/1.1" 200 OK
INFO:     127.0.0.1:42118 - "POST /api/program/authorize HTTP/1.1" 200 OK
INFO:     127.0.0.1:42134 - "GET /api/verify HTTP/1.1" 200 OK
INFO:     127.0.0.1:42148 - "GET /api/verify HTTP/1.1" 200 OK
2025-07-09 17:09:31,268 INFO sqlalchemy.engine.Engine BEGIN (implicit)
2025-07-09 17:09:31,269 INFO sqlalchemy.engine.Engine SELECT hardware_fingerprints.employee_id, hardware_fingerprints.fingerprint, hardware_fingerprints.registration_date 
FROM hardware_fingerprints 
WHERE hardware_fingerprints.employee_id = $1::VARCHAR
2025-07-09 17:09:31,269 INFO sqlalchemy.engine.Engine [cached since 3384s ago] ('215829',)
2025-07-09 17:09:31,273 INFO sqlalchemy.engine.Engine ROLLBACK
INFO:     127.0.0.1:0 - "POST /api/hardware/verify HTTP/1.0" 200 OK
INFO:     127.0.0.1:0 - "POST /login HTTP/1.0" 200 OK
INFO:     127.0.0.1:43074 - "POST /login HTTP/1.1" 200 OK
INFO:     127.0.0.1:43088 - "GET /api/program/permissions/215829 HTTP/1.1" 200 OK
INFO:     127.0.0.1:43096 - "POST /api/program/authorize HTTP/1.1" 200 OK
INFO:     127.0.0.1:43110 - "GET /api/verify HTTP/1.1" 200 OK
INFO:     127.0.0.1:43120 - "GET /api/verify HTTP/1.1" 200 OK
2025-07-09 17:17:20,360 INFO sqlalchemy.engine.Engine BEGIN (implicit)
2025-07-09 17:17:20,360 INFO sqlalchemy.engine.Engine SELECT hardware_fingerprints.employee_id, hardware_fingerprints.fingerprint, hardware_fingerprints.registration_date 
FROM hardware_fingerprints 
WHERE hardware_fingerprints.employee_id = $1::VARCHAR
2025-07-09 17:17:20,360 INFO sqlalchemy.engine.Engine [cached since 3853s ago] ('215829',)
2025-07-09 17:17:20,362 INFO sqlalchemy.engine.Engine ROLLBACK
INFO:     127.0.0.1:0 - "POST /api/hardware/verify HTTP/1.0" 200 OK
INFO:     127.0.0.1:0 - "POST /login HTTP/1.0" 200 OK
INFO:     127.0.0.1:32868 - "POST /login HTTP/1.1" 200 OK
INFO:     127.0.0.1:32880 - "GET /api/program/permissions/215829 HTTP/1.1" 200 OK
INFO:     127.0.0.1:32892 - "POST /api/program/authorize HTTP/1.1" 200 OK
INFO:     127.0.0.1:32908 - "GET /api/verify HTTP/1.1" 200 OK
INFO:     127.0.0.1:32920 - "GET /api/verify HTTP/1.1" 200 OK
2025-07-09 17:18:24,167 INFO sqlalchemy.engine.Engine BEGIN (implicit)
2025-07-09 17:18:24,168 INFO sqlalchemy.engine.Engine SELECT hardware_fingerprints.employee_id, hardware_fingerprints.fingerprint, hardware_fingerprints.registration_date 
FROM hardware_fingerprints 
WHERE hardware_fingerprints.employee_id = $1::VARCHAR
2025-07-09 17:18:24,169 INFO sqlalchemy.engine.Engine [cached since 3917s ago] ('215829',)
2025-07-09 17:18:24,172 INFO sqlalchemy.engine.Engine ROLLBACK
INFO:     127.0.0.1:0 - "POST /api/hardware/verify HTTP/1.0" 200 OK
INFO:     127.0.0.1:0 - "POST /login HTTP/1.0" 200 OK
INFO:     127.0.0.1:43540 - "POST /login HTTP/1.1" 200 OK
INFO:     127.0.0.1:43548 - "GET /api/program/permissions/215829 HTTP/1.1" 200 OK
INFO:     127.0.0.1:43558 - "POST /api/program/authorize HTTP/1.1" 200 OK
INFO:     127.0.0.1:43572 - "GET /api/verify HTTP/1.1" 200 OK
INFO:     127.0.0.1:43578 - "GET /api/verify HTTP/1.1" 200 OK
2025-07-09 17:19:21,142 INFO sqlalchemy.engine.Engine BEGIN (implicit)
2025-07-09 17:19:21,142 INFO sqlalchemy.engine.Engine SELECT hardware_fingerprints.employee_id, hardware_fingerprints.fingerprint, hardware_fingerprints.registration_date 
FROM hardware_fingerprints 
WHERE hardware_fingerprints.employee_id = $1::VARCHAR
2025-07-09 17:19:21,143 INFO sqlalchemy.engine.Engine [cached since 3974s ago] ('215829',)
2025-07-09 17:19:21,144 INFO sqlalchemy.engine.Engine ROLLBACK
INFO:     127.0.0.1:0 - "POST /api/hardware/verify HTTP/1.0" 200 OK
INFO:     127.0.0.1:0 - "POST /login HTTP/1.0" 200 OK
INFO:     127.0.0.1:38910 - "POST /login HTTP/1.1" 200 OK
INFO:     127.0.0.1:38920 - "GET /api/program/permissions/215829 HTTP/1.1" 200 OK
INFO:     127.0.0.1:38924 - "POST /api/program/authorize HTTP/1.1" 200 OK
INFO:     127.0.0.1:38934 - "GET /api/verify HTTP/1.1" 200 OK
INFO:     127.0.0.1:38946 - "GET /api/verify HTTP/1.1" 200 OK
2025-07-09 17:20:38,040 INFO sqlalchemy.engine.Engine BEGIN (implicit)
2025-07-09 17:20:38,041 INFO sqlalchemy.engine.Engine SELECT hardware_fingerprints.employee_id, hardware_fingerprints.fingerprint, hardware_fingerprints.registration_date 
FROM hardware_fingerprints 
WHERE hardware_fingerprints.employee_id = $1::VARCHAR
2025-07-09 17:20:38,041 INFO sqlalchemy.engine.Engine [cached since 4050s ago] ('215829',)
2025-07-09 17:20:38,043 INFO sqlalchemy.engine.Engine ROLLBACK
INFO:     127.0.0.1:0 - "POST /api/hardware/verify HTTP/1.0" 200 OK
INFO:     127.0.0.1:0 - "POST /login HTTP/1.0" 200 OK
INFO:     127.0.0.1:57752 - "POST /login HTTP/1.1" 200 OK
INFO:     127.0.0.1:57756 - "GET /api/program/permissions/215829 HTTP/1.1" 200 OK
INFO:     127.0.0.1:57760 - "POST /api/program/authorize HTTP/1.1" 200 OK
INFO:     127.0.0.1:57762 - "GET /api/verify HTTP/1.1" 200 OK
INFO:     127.0.0.1:57764 - "GET /api/verify HTTP/1.1" 200 OK
INFO:     127.0.0.1:36570 - "POST /api/program/authorize HTTP/1.1" 200 OK
INFO:     127.0.0.1:36574 - "GET /api/verify HTTP/1.1" 200 OK
INFO:     127.0.0.1:36584 - "GET /api/verify HTTP/1.1" 200 OK
2025-07-09 17:28:26,450 INFO sqlalchemy.engine.Engine BEGIN (implicit)
2025-07-09 17:28:26,451 INFO sqlalchemy.engine.Engine SELECT hardware_fingerprints.employee_id, hardware_fingerprints.fingerprint, hardware_fingerprints.registration_date 
FROM hardware_fingerprints 
WHERE hardware_fingerprints.employee_id = $1::VARCHAR
2025-07-09 17:28:26,451 INFO sqlalchemy.engine.Engine [cached since 4519s ago] ('215829',)
2025-07-09 17:28:26,453 INFO sqlalchemy.engine.Engine ROLLBACK
INFO:     127.0.0.1:0 - "POST /api/hardware/verify HTTP/1.0" 200 OK
INFO:     127.0.0.1:0 - "POST /login HTTP/1.0" 200 OK
INFO:     127.0.0.1:35592 - "POST /login HTTP/1.1" 200 OK
INFO:     127.0.0.1:35594 - "GET /api/program/permissions/215829 HTTP/1.1" 200 OK
INFO:     127.0.0.1:35596 - "POST /api/program/authorize HTTP/1.1" 200 OK
INFO:     127.0.0.1:35610 - "GET /api/verify HTTP/1.1" 200 OK
INFO:     127.0.0.1:35612 - "GET /api/verify HTTP/1.1" 200 OK
2025-07-09 17:29:08,546 INFO sqlalchemy.engine.Engine BEGIN (implicit)
2025-07-09 17:29:08,546 INFO sqlalchemy.engine.Engine SELECT hardware_fingerprints.employee_id, hardware_fingerprints.fingerprint, hardware_fingerprints.registration_date 
FROM hardware_fingerprints 
WHERE hardware_fingerprints.employee_id = $1::VARCHAR
2025-07-09 17:29:08,546 INFO sqlalchemy.engine.Engine [cached since 4561s ago] ('215829',)
2025-07-09 17:29:08,548 INFO sqlalchemy.engine.Engine ROLLBACK
INFO:     127.0.0.1:0 - "POST /api/hardware/verify HTTP/1.0" 200 OK
INFO:     127.0.0.1:0 - "POST /login HTTP/1.0" 200 OK
INFO:     127.0.0.1:40024 - "POST /login HTTP/1.1" 200 OK
INFO:     127.0.0.1:40034 - "GET /api/program/permissions/215829 HTTP/1.1" 200 OK
INFO:     127.0.0.1:40044 - "POST /api/program/authorize HTTP/1.1" 200 OK
INFO:     127.0.0.1:40052 - "GET /api/verify HTTP/1.1" 200 OK
INFO:     127.0.0.1:40056 - "GET /api/verify HTTP/1.1" 200 OK
2025-07-09 17:31:03,734 INFO sqlalchemy.engine.Engine BEGIN (implicit)
2025-07-09 17:31:03,734 INFO sqlalchemy.engine.Engine SELECT hardware_fingerprints.employee_id, hardware_fingerprints.fingerprint, hardware_fingerprints.registration_date 
FROM hardware_fingerprints 
WHERE hardware_fingerprints.employee_id = $1::VARCHAR
2025-07-09 17:31:03,734 INFO sqlalchemy.engine.Engine [cached since 4676s ago] ('215829',)
2025-07-09 17:31:03,736 INFO sqlalchemy.engine.Engine ROLLBACK
INFO:     127.0.0.1:0 - "POST /api/hardware/verify HTTP/1.0" 200 OK
INFO:     127.0.0.1:0 - "POST /login HTTP/1.0" 200 OK
INFO:     127.0.0.1:55150 - "POST /login HTTP/1.1" 200 OK
INFO:     127.0.0.1:55158 - "GET /api/program/permissions/215829 HTTP/1.1" 200 OK
INFO:     127.0.0.1:55160 - "POST /api/program/authorize HTTP/1.1" 200 OK
INFO:     127.0.0.1:55164 - "GET /api/verify HTTP/1.1" 200 OK
INFO:     127.0.0.1:55174 - "GET /api/verify HTTP/1.1" 200 OK
2025-07-09 17:33:10,725 INFO sqlalchemy.engine.Engine BEGIN (implicit)
2025-07-09 17:33:10,726 INFO sqlalchemy.engine.Engine SELECT hardware_fingerprints.employee_id, hardware_fingerprints.fingerprint, hardware_fingerprints.registration_date 
FROM hardware_fingerprints 
WHERE hardware_fingerprints.employee_id = $1::VARCHAR
2025-07-09 17:33:10,726 INFO sqlalchemy.engine.Engine [cached since 4803s ago] ('215829',)
2025-07-09 17:33:10,728 INFO sqlalchemy.engine.Engine ROLLBACK
INFO:     127.0.0.1:0 - "POST /api/hardware/verify HTTP/1.0" 200 OK
INFO:     127.0.0.1:0 - "POST /login HTTP/1.0" 200 OK
INFO:     127.0.0.1:36530 - "POST /login HTTP/1.1" 200 OK
INFO:     127.0.0.1:36536 - "GET /api/program/permissions/215829 HTTP/1.1" 200 OK
INFO:     127.0.0.1:36538 - "POST /api/program/authorize HTTP/1.1" 200 OK
INFO:     127.0.0.1:36540 - "GET /api/verify HTTP/1.1" 200 OK
INFO:     127.0.0.1:36550 - "GET /api/verify HTTP/1.1" 200 OK
2025-07-09 17:33:46,620 INFO sqlalchemy.engine.Engine BEGIN (implicit)
2025-07-09 17:33:46,621 INFO sqlalchemy.engine.Engine SELECT hardware_fingerprints.employee_id, hardware_fingerprints.fingerprint, hardware_fingerprints.registration_date 
FROM hardware_fingerprints 
WHERE hardware_fingerprints.employee_id = $1::VARCHAR
2025-07-09 17:33:46,621 INFO sqlalchemy.engine.Engine [cached since 4839s ago] ('215829',)
2025-07-09 17:33:46,624 INFO sqlalchemy.engine.Engine ROLLBACK
INFO:     127.0.0.1:0 - "POST /api/hardware/verify HTTP/1.0" 200 OK
INFO:     127.0.0.1:0 - "POST /login HTTP/1.0" 200 OK
INFO:     127.0.0.1:53882 - "POST /login HTTP/1.1" 200 OK
INFO:     127.0.0.1:53894 - "GET /api/program/permissions/215829 HTTP/1.1" 200 OK
INFO:     127.0.0.1:53900 - "POST /api/program/authorize HTTP/1.1" 200 OK
INFO:     127.0.0.1:53914 - "GET /api/verify HTTP/1.1" 200 OK
INFO:     127.0.0.1:53924 - "GET /api/verify HTTP/1.1" 200 OK
2025-07-09 17:40:35,120 INFO sqlalchemy.engine.Engine BEGIN (implicit)
2025-07-09 17:40:35,120 INFO sqlalchemy.engine.Engine SELECT hardware_fingerprints.employee_id, hardware_fingerprints.fingerprint, hardware_fingerprints.registration_date 
FROM hardware_fingerprints 
WHERE hardware_fingerprints.employee_id = $1::VARCHAR
2025-07-09 17:40:35,120 INFO sqlalchemy.engine.Engine [cached since 5247s ago] ('215829',)
2025-07-09 17:40:35,123 INFO sqlalchemy.engine.Engine ROLLBACK
INFO:     127.0.0.1:0 - "POST /api/hardware/verify HTTP/1.0" 200 OK
INFO:     127.0.0.1:0 - "POST /login HTTP/1.0" 200 OK
INFO:     127.0.0.1:41274 - "POST /login HTTP/1.1" 200 OK
INFO:     127.0.0.1:41284 - "GET /api/program/permissions/215829 HTTP/1.1" 200 OK
INFO:     127.0.0.1:41286 - "POST /api/program/authorize HTTP/1.1" 200 OK
INFO:     127.0.0.1:41292 - "GET /api/verify HTTP/1.1" 200 OK
INFO:     127.0.0.1:41294 - "GET /api/verify HTTP/1.1" 200 OK
2025-07-10 07:30:16,148 INFO sqlalchemy.engine.Engine BEGIN (implicit)
2025-07-10 07:30:16,148 INFO sqlalchemy.engine.Engine SELECT hardware_fingerprints.employee_id, hardware_fingerprints.fingerprint, hardware_fingerprints.registration_date 
FROM hardware_fingerprints 
WHERE hardware_fingerprints.employee_id = $1::VARCHAR
2025-07-10 07:30:16,148 INFO sqlalchemy.engine.Engine [cached since 5.503e+04s ago] ('215829',)
2025-07-10 07:30:16,150 INFO sqlalchemy.engine.Engine ROLLBACK
INFO:     127.0.0.1:0 - "POST /api/hardware/verify HTTP/1.0" 200 OK
INFO:     127.0.0.1:0 - "POST /login HTTP/1.0" 200 OK
INFO:     127.0.0.1:49482 - "POST /login HTTP/1.1" 200 OK
INFO:     127.0.0.1:49490 - "GET /api/program/permissions/215829 HTTP/1.1" 200 OK
INFO:     127.0.0.1:49504 - "POST /api/program/authorize HTTP/1.1" 200 OK
INFO:     127.0.0.1:49510 - "GET /api/verify HTTP/1.1" 200 OK
INFO:     127.0.0.1:49512 - "GET /api/verify HTTP/1.1" 200 OK
2025-07-10 07:31:25,339 INFO sqlalchemy.engine.Engine BEGIN (implicit)
2025-07-10 07:31:25,340 INFO sqlalchemy.engine.Engine SELECT hardware_fingerprints.employee_id, hardware_fingerprints.fingerprint, hardware_fingerprints.registration_date 
FROM hardware_fingerprints 
WHERE hardware_fingerprints.employee_id = $1::VARCHAR
2025-07-10 07:31:25,340 INFO sqlalchemy.engine.Engine [cached since 5.51e+04s ago] ('215829',)
2025-07-10 07:31:25,343 INFO sqlalchemy.engine.Engine ROLLBACK
INFO:     127.0.0.1:0 - "POST /api/hardware/verify HTTP/1.0" 200 OK
INFO:     127.0.0.1:0 - "POST /login HTTP/1.0" 200 OK
INFO:     127.0.0.1:37600 - "POST /login HTTP/1.1" 200 OK
INFO:     127.0.0.1:37610 - "GET /api/program/permissions/215829 HTTP/1.1" 200 OK
INFO:     127.0.0.1:37618 - "POST /api/program/authorize HTTP/1.1" 200 OK
INFO:     127.0.0.1:37622 - "GET /api/verify HTTP/1.1" 200 OK
INFO:     127.0.0.1:37634 - "GET /api/verify HTTP/1.1" 200 OK
2025-07-10 07:45:39,284 INFO sqlalchemy.engine.Engine BEGIN (implicit)
2025-07-10 07:45:39,284 INFO sqlalchemy.engine.Engine SELECT hardware_fingerprints.employee_id, hardware_fingerprints.fingerprint, hardware_fingerprints.registration_date 
FROM hardware_fingerprints 
WHERE hardware_fingerprints.employee_id = $1::VARCHAR
2025-07-10 07:45:39,285 INFO sqlalchemy.engine.Engine [cached since 5.595e+04s ago] ('215829',)
2025-07-10 07:45:39,286 INFO sqlalchemy.engine.Engine ROLLBACK
INFO:     127.0.0.1:0 - "POST /api/hardware/verify HTTP/1.0" 200 OK
INFO:     127.0.0.1:0 - "POST /login HTTP/1.0" 200 OK
INFO:     127.0.0.1:50312 - "POST /login HTTP/1.1" 200 OK
INFO:     127.0.0.1:50326 - "GET /api/program/permissions/215829 HTTP/1.1" 200 OK
INFO:     127.0.0.1:50332 - "POST /api/program/authorize HTTP/1.1" 200 OK
INFO:     127.0.0.1:50344 - "GET /api/verify HTTP/1.1" 200 OK
INFO:     127.0.0.1:50350 - "GET /api/verify HTTP/1.1" 200 OK
2025-07-10 07:53:03,853 INFO sqlalchemy.engine.Engine BEGIN (implicit)
2025-07-10 07:53:03,853 INFO sqlalchemy.engine.Engine SELECT hardware_fingerprints.employee_id, hardware_fingerprints.fingerprint, hardware_fingerprints.registration_date 
FROM hardware_fingerprints 
WHERE hardware_fingerprints.employee_id = $1::VARCHAR
2025-07-10 07:53:03,853 INFO sqlalchemy.engine.Engine [cached since 5.64e+04s ago] ('215829',)
2025-07-10 07:53:03,855 INFO sqlalchemy.engine.Engine ROLLBACK
INFO:     127.0.0.1:0 - "POST /api/hardware/verify HTTP/1.0" 200 OK
INFO:     127.0.0.1:0 - "POST /login HTTP/1.0" 200 OK
INFO:     127.0.0.1:44770 - "POST /login HTTP/1.1" 200 OK
INFO:     127.0.0.1:44780 - "GET /api/program/permissions/215829 HTTP/1.1" 200 OK
INFO:     127.0.0.1:44794 - "POST /api/program/authorize HTTP/1.1" 200 OK
INFO:     127.0.0.1:44800 - "GET /api/verify HTTP/1.1" 200 OK
INFO:     127.0.0.1:44814 - "GET /api/verify HTTP/1.1" 200 OK
2025-07-10 07:53:39,792 INFO sqlalchemy.engine.Engine BEGIN (implicit)
2025-07-10 07:53:39,793 INFO sqlalchemy.engine.Engine SELECT hardware_fingerprints.employee_id, hardware_fingerprints.fingerprint, hardware_fingerprints.registration_date 
FROM hardware_fingerprints 
WHERE hardware_fingerprints.employee_id = $1::VARCHAR
2025-07-10 07:53:39,793 INFO sqlalchemy.engine.Engine [cached since 5.643e+04s ago] ('215829',)
2025-07-10 07:53:39,797 INFO sqlalchemy.engine.Engine ROLLBACK
INFO:     127.0.0.1:0 - "POST /api/hardware/verify HTTP/1.0" 200 OK
INFO:     127.0.0.1:0 - "POST /login HTTP/1.0" 200 OK
INFO:     127.0.0.1:50400 - "POST /login HTTP/1.1" 200 OK
INFO:     127.0.0.1:50406 - "GET /api/program/permissions/215829 HTTP/1.1" 200 OK
INFO:     127.0.0.1:50410 - "POST /api/program/authorize HTTP/1.1" 200 OK
INFO:     127.0.0.1:50412 - "GET /api/verify HTTP/1.1" 200 OK
INFO:     127.0.0.1:50426 - "GET /api/verify HTTP/1.1" 200 OK
2025-07-10 08:04:06,573 INFO sqlalchemy.engine.Engine BEGIN (implicit)
2025-07-10 08:04:06,574 INFO sqlalchemy.engine.Engine SELECT hardware_fingerprints.employee_id, hardware_fingerprints.fingerprint, hardware_fingerprints.registration_date 
FROM hardware_fingerprints 
WHERE hardware_fingerprints.employee_id = $1::VARCHAR
2025-07-10 08:04:06,574 INFO sqlalchemy.engine.Engine [cached since 5.706e+04s ago] ('215829',)
2025-07-10 08:04:06,577 INFO sqlalchemy.engine.Engine ROLLBACK
INFO:     127.0.0.1:0 - "POST /api/hardware/verify HTTP/1.0" 200 OK
INFO:     127.0.0.1:0 - "POST /login HTTP/1.0" 200 OK
INFO:     127.0.0.1:56150 - "POST /login HTTP/1.1" 200 OK
INFO:     127.0.0.1:56152 - "GET /api/program/permissions/215829 HTTP/1.1" 200 OK
INFO:     127.0.0.1:56154 - "POST /api/program/authorize HTTP/1.1" 200 OK
INFO:     127.0.0.1:56168 - "GET /api/verify HTTP/1.1" 200 OK
INFO:     127.0.0.1:56170 - "GET /api/verify HTTP/1.1" 200 OK
2025-07-10 08:04:56,034 INFO sqlalchemy.engine.Engine BEGIN (implicit)
2025-07-10 08:04:56,035 INFO sqlalchemy.engine.Engine SELECT hardware_fingerprints.employee_id, hardware_fingerprints.fingerprint, hardware_fingerprints.registration_date 
FROM hardware_fingerprints 
WHERE hardware_fingerprints.employee_id = $1::VARCHAR
2025-07-10 08:04:56,035 INFO sqlalchemy.engine.Engine [cached since 5.711e+04s ago] ('215829',)
2025-07-10 08:04:56,036 INFO sqlalchemy.engine.Engine ROLLBACK
INFO:     127.0.0.1:0 - "POST /api/hardware/verify HTTP/1.0" 200 OK
INFO:     127.0.0.1:0 - "POST /login HTTP/1.0" 200 OK
INFO:     127.0.0.1:58024 - "POST /login HTTP/1.1" 200 OK
INFO:     127.0.0.1:58038 - "GET /api/program/permissions/215829 HTTP/1.1" 200 OK
INFO:     127.0.0.1:58050 - "POST /api/program/authorize HTTP/1.1" 200 OK
INFO:     127.0.0.1:58054 - "GET /api/verify HTTP/1.1" 200 OK
INFO:     127.0.0.1:58064 - "GET /api/verify HTTP/1.1" 200 OK
2025-07-10 08:08:13,857 INFO sqlalchemy.engine.Engine BEGIN (implicit)
2025-07-10 08:08:13,857 INFO sqlalchemy.engine.Engine SELECT hardware_fingerprints.employee_id, hardware_fingerprints.fingerprint, hardware_fingerprints.registration_date 
FROM hardware_fingerprints 
WHERE hardware_fingerprints.employee_id = $1::VARCHAR
2025-07-10 08:08:13,857 INFO sqlalchemy.engine.Engine [cached since 5.731e+04s ago] ('215829',)
2025-07-10 08:08:13,859 INFO sqlalchemy.engine.Engine ROLLBACK
INFO:     127.0.0.1:0 - "POST /api/hardware/verify HTTP/1.0" 200 OK
INFO:     127.0.0.1:0 - "POST /login HTTP/1.0" 200 OK
INFO:     127.0.0.1:45124 - "POST /login HTTP/1.1" 200 OK
INFO:     127.0.0.1:45128 - "GET /api/program/permissions/215829 HTTP/1.1" 200 OK
INFO:     127.0.0.1:45142 - "POST /api/program/authorize HTTP/1.1" 200 OK
INFO:     127.0.0.1:45144 - "GET /api/verify HTTP/1.1" 200 OK
INFO:     127.0.0.1:45158 - "GET /api/verify HTTP/1.1" 200 OK
2025-07-10 08:08:57,232 INFO sqlalchemy.engine.Engine BEGIN (implicit)
2025-07-10 08:08:57,232 INFO sqlalchemy.engine.Engine SELECT hardware_fingerprints.employee_id, hardware_fingerprints.fingerprint, hardware_fingerprints.registration_date 
FROM hardware_fingerprints 
WHERE hardware_fingerprints.employee_id = $1::VARCHAR
2025-07-10 08:08:57,233 INFO sqlalchemy.engine.Engine [cached since 5.735e+04s ago] ('215829',)
2025-07-10 08:08:57,236 INFO sqlalchemy.engine.Engine ROLLBACK
INFO:     127.0.0.1:0 - "POST /api/hardware/verify HTTP/1.0" 200 OK
INFO:     127.0.0.1:0 - "POST /login HTTP/1.0" 200 OK
INFO:     127.0.0.1:38410 - "POST /login HTTP/1.1" 200 OK
INFO:     127.0.0.1:38422 - "GET /api/program/permissions/215829 HTTP/1.1" 200 OK
INFO:     127.0.0.1:38438 - "POST /api/program/authorize HTTP/1.1" 200 OK
INFO:     127.0.0.1:38442 - "GET /api/verify HTTP/1.1" 200 OK
INFO:     127.0.0.1:38444 - "GET /api/verify HTTP/1.1" 200 OK
2025-07-10 08:10:02,993 INFO sqlalchemy.engine.Engine BEGIN (implicit)
2025-07-10 08:10:02,994 INFO sqlalchemy.engine.Engine SELECT hardware_fingerprints.employee_id, hardware_fingerprints.fingerprint, hardware_fingerprints.registration_date 
FROM hardware_fingerprints 
WHERE hardware_fingerprints.employee_id = $1::VARCHAR
2025-07-10 08:10:02,994 INFO sqlalchemy.engine.Engine [cached since 5.742e+04s ago] ('215829',)
2025-07-10 08:10:02,995 INFO sqlalchemy.engine.Engine ROLLBACK
INFO:     127.0.0.1:0 - "POST /api/hardware/verify HTTP/1.0" 200 OK
INFO:     127.0.0.1:0 - "POST /login HTTP/1.0" 200 OK
INFO:     127.0.0.1:52508 - "POST /login HTTP/1.1" 200 OK
INFO:     127.0.0.1:52524 - "GET /api/program/permissions/215829 HTTP/1.1" 200 OK
INFO:     127.0.0.1:40552 - "POST /api/program/authorize HTTP/1.1" 200 OK
INFO:     127.0.0.1:40556 - "GET /api/verify HTTP/1.1" 200 OK
INFO:     127.0.0.1:40564 - "GET /api/verify HTTP/1.1" 200 OK
2025-07-10 08:10:44,000 INFO sqlalchemy.engine.Engine BEGIN (implicit)
2025-07-10 08:10:44,000 INFO sqlalchemy.engine.Engine SELECT hardware_fingerprints.employee_id, hardware_fingerprints.fingerprint, hardware_fingerprints.registration_date 
FROM hardware_fingerprints 
WHERE hardware_fingerprints.employee_id = $1::VARCHAR
2025-07-10 08:10:44,001 INFO sqlalchemy.engine.Engine [cached since 5.746e+04s ago] ('215829',)
2025-07-10 08:10:44,002 INFO sqlalchemy.engine.Engine ROLLBACK
INFO:     127.0.0.1:0 - "POST /api/hardware/verify HTTP/1.0" 200 OK
INFO:     127.0.0.1:0 - "POST /login HTTP/1.0" 200 OK
INFO:     127.0.0.1:48072 - "POST /login HTTP/1.1" 200 OK
INFO:     127.0.0.1:48084 - "GET /api/program/permissions/215829 HTTP/1.1" 200 OK
INFO:     127.0.0.1:48094 - "POST /api/program/authorize HTTP/1.1" 200 OK
INFO:     127.0.0.1:48104 - "GET /api/verify HTTP/1.1" 200 OK
INFO:     127.0.0.1:48112 - "GET /api/verify HTTP/1.1" 200 OK
2025-07-10 08:11:40,271 INFO sqlalchemy.engine.Engine BEGIN (implicit)
2025-07-10 08:11:40,271 INFO sqlalchemy.engine.Engine SELECT hardware_fingerprints.employee_id, hardware_fingerprints.fingerprint, hardware_fingerprints.registration_date 
FROM hardware_fingerprints 
WHERE hardware_fingerprints.employee_id = $1::VARCHAR
2025-07-10 08:11:40,272 INFO sqlalchemy.engine.Engine [cached since 5.751e+04s ago] ('215829',)
2025-07-10 08:11:40,273 INFO sqlalchemy.engine.Engine ROLLBACK
INFO:     127.0.0.1:0 - "POST /api/hardware/verify HTTP/1.0" 200 OK
INFO:     127.0.0.1:0 - "POST /login HTTP/1.0" 200 OK
INFO:     127.0.0.1:33370 - "POST /login HTTP/1.1" 200 OK
INFO:     127.0.0.1:33378 - "GET /api/program/permissions/215829 HTTP/1.1" 200 OK
INFO:     127.0.0.1:33392 - "POST /api/program/authorize HTTP/1.1" 200 OK
INFO:     127.0.0.1:33394 - "GET /api/verify HTTP/1.1" 200 OK
INFO:     127.0.0.1:33398 - "GET /api/verify HTTP/1.1" 200 OK
2025-07-10 08:12:42,548 INFO sqlalchemy.engine.Engine BEGIN (implicit)
2025-07-10 08:12:42,549 INFO sqlalchemy.engine.Engine SELECT hardware_fingerprints.employee_id, hardware_fingerprints.fingerprint, hardware_fingerprints.registration_date 
FROM hardware_fingerprints 
WHERE hardware_fingerprints.employee_id = $1::VARCHAR
2025-07-10 08:12:42,549 INFO sqlalchemy.engine.Engine [cached since 5.757e+04s ago] ('215829',)
2025-07-10 08:12:42,552 INFO sqlalchemy.engine.Engine ROLLBACK
INFO:     127.0.0.1:0 - "POST /api/hardware/verify HTTP/1.0" 200 OK
INFO:     127.0.0.1:0 - "POST /login HTTP/1.0" 200 OK
INFO:     127.0.0.1:49000 - "POST /login HTTP/1.1" 200 OK
INFO:     127.0.0.1:49016 - "GET /api/program/permissions/215829 HTTP/1.1" 200 OK
INFO:     127.0.0.1:53754 - "POST /api/program/authorize HTTP/1.1" 200 OK
INFO:     127.0.0.1:53764 - "GET /api/verify HTTP/1.1" 200 OK
INFO:     127.0.0.1:53776 - "GET /api/verify HTTP/1.1" 200 OK
2025-07-10 08:13:30,268 INFO sqlalchemy.engine.Engine BEGIN (implicit)
2025-07-10 08:13:30,268 INFO sqlalchemy.engine.Engine SELECT hardware_fingerprints.employee_id, hardware_fingerprints.fingerprint, hardware_fingerprints.registration_date 
FROM hardware_fingerprints 
WHERE hardware_fingerprints.employee_id = $1::VARCHAR
2025-07-10 08:13:30,268 INFO sqlalchemy.engine.Engine [cached since 5.762e+04s ago] ('215829',)
2025-07-10 08:13:30,270 INFO sqlalchemy.engine.Engine ROLLBACK
INFO:     127.0.0.1:0 - "POST /api/hardware/verify HTTP/1.0" 200 OK
INFO:     127.0.0.1:0 - "POST /login HTTP/1.0" 200 OK
INFO:     127.0.0.1:39924 - "POST /login HTTP/1.1" 200 OK
INFO:     127.0.0.1:39934 - "GET /api/program/permissions/215829 HTTP/1.1" 200 OK
INFO:     127.0.0.1:39944 - "POST /api/program/authorize HTTP/1.1" 200 OK
INFO:     127.0.0.1:39948 - "GET /api/verify HTTP/1.1" 200 OK
INFO:     127.0.0.1:39950 - "GET /api/verify HTTP/1.1" 200 OK
2025-07-10 08:15:17,578 INFO sqlalchemy.engine.Engine BEGIN (implicit)
2025-07-10 08:15:17,579 INFO sqlalchemy.engine.Engine SELECT hardware_fingerprints.employee_id, hardware_fingerprints.fingerprint, hardware_fingerprints.registration_date 
FROM hardware_fingerprints 
WHERE hardware_fingerprints.employee_id = $1::VARCHAR
2025-07-10 08:15:17,579 INFO sqlalchemy.engine.Engine [cached since 5.773e+04s ago] ('215829',)
2025-07-10 08:15:17,580 INFO sqlalchemy.engine.Engine ROLLBACK
INFO:     127.0.0.1:0 - "POST /api/hardware/verify HTTP/1.0" 200 OK
INFO:     127.0.0.1:0 - "POST /login HTTP/1.0" 200 OK
INFO:     127.0.0.1:56290 - "POST /login HTTP/1.1" 200 OK
INFO:     127.0.0.1:56300 - "GET /api/program/permissions/215829 HTTP/1.1" 200 OK
INFO:     127.0.0.1:56304 - "POST /api/program/authorize HTTP/1.1" 200 OK
INFO:     127.0.0.1:56308 - "GET /api/verify HTTP/1.1" 200 OK
INFO:     127.0.0.1:56310 - "GET /api/verify HTTP/1.1" 200 OK
2025-07-10 08:16:11,184 INFO sqlalchemy.engine.Engine BEGIN (implicit)
2025-07-10 08:16:11,184 INFO sqlalchemy.engine.Engine SELECT hardware_fingerprints.employee_id, hardware_fingerprints.fingerprint, hardware_fingerprints.registration_date 
FROM hardware_fingerprints 
WHERE hardware_fingerprints.employee_id = $1::VARCHAR
2025-07-10 08:16:11,184 INFO sqlalchemy.engine.Engine [cached since 5.778e+04s ago] ('215829',)
2025-07-10 08:16:11,186 INFO sqlalchemy.engine.Engine ROLLBACK
INFO:     127.0.0.1:0 - "POST /api/hardware/verify HTTP/1.0" 200 OK
INFO:     127.0.0.1:0 - "POST /login HTTP/1.0" 200 OK
INFO:     127.0.0.1:56322 - "POST /login HTTP/1.1" 200 OK
INFO:     127.0.0.1:56332 - "GET /api/program/permissions/215829 HTTP/1.1" 200 OK
INFO:     127.0.0.1:56340 - "POST /api/program/authorize HTTP/1.1" 200 OK
INFO:     127.0.0.1:56352 - "GET /api/verify HTTP/1.1" 200 OK
INFO:     127.0.0.1:56364 - "GET /api/verify HTTP/1.1" 200 OK
2025-07-10 08:16:43,823 INFO sqlalchemy.engine.Engine BEGIN (implicit)
2025-07-10 08:16:43,823 INFO sqlalchemy.engine.Engine SELECT hardware_fingerprints.employee_id, hardware_fingerprints.fingerprint, hardware_fingerprints.registration_date 
FROM hardware_fingerprints 
WHERE hardware_fingerprints.employee_id = $1::VARCHAR
2025-07-10 08:16:43,823 INFO sqlalchemy.engine.Engine [cached since 5.782e+04s ago] ('215829',)
2025-07-10 08:16:43,825 INFO sqlalchemy.engine.Engine ROLLBACK
INFO:     127.0.0.1:0 - "POST /api/hardware/verify HTTP/1.0" 200 OK
INFO:     127.0.0.1:0 - "POST /login HTTP/1.0" 200 OK
INFO:     127.0.0.1:49680 - "POST /login HTTP/1.1" 200 OK
INFO:     127.0.0.1:49692 - "GET /api/program/permissions/215829 HTTP/1.1" 200 OK
INFO:     127.0.0.1:49704 - "POST /api/program/authorize HTTP/1.1" 200 OK
INFO:     127.0.0.1:49716 - "GET /api/verify HTTP/1.1" 200 OK
INFO:     127.0.0.1:49732 - "GET /api/verify HTTP/1.1" 200 OK
2025-07-10 08:55:11,495 INFO sqlalchemy.engine.Engine BEGIN (implicit)
2025-07-10 08:55:11,495 INFO sqlalchemy.engine.Engine SELECT hardware_fingerprints.employee_id, hardware_fingerprints.fingerprint, hardware_fingerprints.registration_date 
FROM hardware_fingerprints 
WHERE hardware_fingerprints.employee_id = $1::VARCHAR
2025-07-10 08:55:11,495 INFO sqlalchemy.engine.Engine [cached since 6.012e+04s ago] ('215829',)
2025-07-10 08:55:11,497 INFO sqlalchemy.engine.Engine ROLLBACK
INFO:     127.0.0.1:0 - "POST /api/hardware/verify HTTP/1.0" 200 OK
INFO:     127.0.0.1:0 - "POST /login HTTP/1.0" 200 OK
INFO:     127.0.0.1:33918 - "POST /login HTTP/1.1" 200 OK
INFO:     127.0.0.1:33922 - "GET /api/program/permissions/215829 HTTP/1.1" 200 OK
INFO:     127.0.0.1:33924 - "POST /api/program/authorize HTTP/1.1" 200 OK
INFO:     127.0.0.1:33940 - "GET /api/verify HTTP/1.1" 200 OK
INFO:     127.0.0.1:33956 - "GET /api/verify HTTP/1.1" 200 OK
2025-07-10 08:57:35,262 INFO sqlalchemy.engine.Engine BEGIN (implicit)
2025-07-10 08:57:35,263 INFO sqlalchemy.engine.Engine SELECT hardware_fingerprints.employee_id, hardware_fingerprints.fingerprint, hardware_fingerprints.registration_date 
FROM hardware_fingerprints 
WHERE hardware_fingerprints.employee_id = $1::VARCHAR
2025-07-10 08:57:35,263 INFO sqlalchemy.engine.Engine [cached since 6.027e+04s ago] ('215829',)
2025-07-10 08:57:35,264 INFO sqlalchemy.engine.Engine ROLLBACK
INFO:     127.0.0.1:0 - "POST /api/hardware/verify HTTP/1.0" 200 OK
INFO:     127.0.0.1:0 - "POST /login HTTP/1.0" 200 OK
INFO:     127.0.0.1:43422 - "POST /login HTTP/1.1" 200 OK
INFO:     127.0.0.1:43432 - "GET /api/program/permissions/215829 HTTP/1.1" 200 OK
INFO:     127.0.0.1:43440 - "POST /api/program/authorize HTTP/1.1" 200 OK
INFO:     127.0.0.1:43444 - "GET /api/verify HTTP/1.1" 200 OK
INFO:     127.0.0.1:43452 - "GET /api/verify HTTP/1.1" 200 OK
2025-07-10 08:58:13,317 INFO sqlalchemy.engine.Engine BEGIN (implicit)
2025-07-10 08:58:13,318 INFO sqlalchemy.engine.Engine SELECT hardware_fingerprints.employee_id, hardware_fingerprints.fingerprint, hardware_fingerprints.registration_date 
FROM hardware_fingerprints 
WHERE hardware_fingerprints.employee_id = $1::VARCHAR
2025-07-10 08:58:13,318 INFO sqlalchemy.engine.Engine [cached since 6.031e+04s ago] ('215829',)
2025-07-10 08:58:13,321 INFO sqlalchemy.engine.Engine ROLLBACK
INFO:     127.0.0.1:0 - "POST /api/hardware/verify HTTP/1.0" 200 OK
INFO:     127.0.0.1:0 - "POST /login HTTP/1.0" 200 OK
INFO:     127.0.0.1:52228 - "POST /login HTTP/1.1" 200 OK
INFO:     127.0.0.1:52244 - "GET /api/program/permissions/215829 HTTP/1.1" 200 OK
INFO:     127.0.0.1:49768 - "POST /api/program/authorize HTTP/1.1" 200 OK
INFO:     127.0.0.1:49776 - "GET /api/verify HTTP/1.1" 200 OK
INFO:     127.0.0.1:49788 - "GET /api/verify HTTP/1.1" 200 OK
2025-07-10 09:27:56,312 INFO sqlalchemy.engine.Engine BEGIN (implicit)
2025-07-10 09:27:56,312 INFO sqlalchemy.engine.Engine SELECT hardware_fingerprints.employee_id, hardware_fingerprints.fingerprint, hardware_fingerprints.registration_date 
FROM hardware_fingerprints 
WHERE hardware_fingerprints.employee_id = $1::VARCHAR
2025-07-10 09:27:56,312 INFO sqlalchemy.engine.Engine [cached since 6.209e+04s ago] ('215829',)
2025-07-10 09:27:56,314 INFO sqlalchemy.engine.Engine ROLLBACK
INFO:     127.0.0.1:0 - "POST /api/hardware/verify HTTP/1.0" 200 OK
INFO:     127.0.0.1:0 - "POST /login HTTP/1.0" 200 OK
INFO:     127.0.0.1:36052 - "POST /login HTTP/1.1" 200 OK
INFO:     127.0.0.1:36066 - "GET /api/program/permissions/215829 HTTP/1.1" 200 OK
INFO:     127.0.0.1:36082 - "POST /api/program/authorize HTTP/1.1" 200 OK
INFO:     127.0.0.1:36088 - "GET /api/verify HTTP/1.1" 200 OK
INFO:     127.0.0.1:36090 - "GET /api/verify HTTP/1.1" 200 OK
2025-07-10 14:09:56,511 INFO sqlalchemy.engine.Engine BEGIN (implicit)
2025-07-10 14:09:56,511 INFO sqlalchemy.engine.Engine SELECT hardware_fingerprints.employee_id, hardware_fingerprints.fingerprint, hardware_fingerprints.registration_date 
FROM hardware_fingerprints 
WHERE hardware_fingerprints.employee_id = $1::VARCHAR
2025-07-10 14:09:56,512 INFO sqlalchemy.engine.Engine [cached since 7.901e+04s ago] ('215829',)
2025-07-10 14:09:56,515 INFO sqlalchemy.engine.Engine ROLLBACK
INFO:     127.0.0.1:0 - "POST /api/hardware/verify HTTP/1.0" 200 OK
INFO:     127.0.0.1:0 - "POST /login HTTP/1.0" 200 OK
INFO:     127.0.0.1:53400 - "POST /login HTTP/1.1" 200 OK
INFO:     127.0.0.1:53416 - "GET /api/program/permissions/215829 HTTP/1.1" 200 OK
INFO:     127.0.0.1:53420 - "POST /api/program/authorize HTTP/1.1" 200 OK
INFO:     127.0.0.1:53422 - "GET /api/verify HTTP/1.1" 200 OK
INFO:     127.0.0.1:53438 - "GET /api/verify HTTP/1.1" 200 OK
2025-07-10 14:27:01,141 INFO sqlalchemy.engine.Engine BEGIN (implicit)
2025-07-10 14:27:01,142 INFO sqlalchemy.engine.Engine SELECT hardware_fingerprints.employee_id, hardware_fingerprints.fingerprint, hardware_fingerprints.registration_date 
FROM hardware_fingerprints 
WHERE hardware_fingerprints.employee_id = $1::VARCHAR
2025-07-10 14:27:01,142 INFO sqlalchemy.engine.Engine [cached since 8.003e+04s ago] ('215829',)
2025-07-10 14:27:01,146 INFO sqlalchemy.engine.Engine ROLLBACK
INFO:     127.0.0.1:0 - "POST /api/hardware/verify HTTP/1.0" 200 OK
INFO:     127.0.0.1:0 - "POST /login HTTP/1.0" 200 OK
INFO:     127.0.0.1:52112 - "POST /login HTTP/1.1" 200 OK
INFO:     127.0.0.1:52122 - "GET /api/program/permissions/215829 HTTP/1.1" 200 OK
INFO:     127.0.0.1:52130 - "POST /api/program/authorize HTTP/1.1" 200 OK
INFO:     127.0.0.1:52134 - "GET /api/verify HTTP/1.1" 200 OK
INFO:     127.0.0.1:52144 - "GET /api/verify HTTP/1.1" 200 OK
2025-07-10 14:27:54,510 INFO sqlalchemy.engine.Engine BEGIN (implicit)
2025-07-10 14:27:54,510 INFO sqlalchemy.engine.Engine SELECT hardware_fingerprints.employee_id, hardware_fingerprints.fingerprint, hardware_fingerprints.registration_date 
FROM hardware_fingerprints 
WHERE hardware_fingerprints.employee_id = $1::VARCHAR
2025-07-10 14:27:54,510 INFO sqlalchemy.engine.Engine [cached since 8.009e+04s ago] ('215829',)
2025-07-10 14:27:54,512 INFO sqlalchemy.engine.Engine ROLLBACK
INFO:     127.0.0.1:0 - "POST /api/hardware/verify HTTP/1.0" 200 OK
INFO:     127.0.0.1:0 - "POST /login HTTP/1.0" 200 OK
INFO:     127.0.0.1:55222 - "POST /login HTTP/1.1" 200 OK
INFO:     127.0.0.1:55230 - "GET /api/program/permissions/215829 HTTP/1.1" 200 OK
INFO:     127.0.0.1:55244 - "POST /api/program/authorize HTTP/1.1" 200 OK
INFO:     127.0.0.1:55250 - "GET /api/verify HTTP/1.1" 200 OK
INFO:     127.0.0.1:55262 - "GET /api/verify HTTP/1.1" 200 OK
2025-07-10 14:40:32,683 INFO sqlalchemy.engine.Engine BEGIN (implicit)
2025-07-10 14:40:32,684 INFO sqlalchemy.engine.Engine SELECT hardware_fingerprints.employee_id, hardware_fingerprints.fingerprint, hardware_fingerprints.registration_date 
FROM hardware_fingerprints 
WHERE hardware_fingerprints.employee_id = $1::VARCHAR
2025-07-10 14:40:32,684 INFO sqlalchemy.engine.Engine [cached since 8.085e+04s ago] ('215829',)
2025-07-10 14:40:32,688 INFO sqlalchemy.engine.Engine ROLLBACK
INFO:     127.0.0.1:0 - "POST /api/hardware/verify HTTP/1.0" 200 OK
INFO:     127.0.0.1:0 - "POST /login HTTP/1.0" 200 OK
INFO:     127.0.0.1:35826 - "POST /login HTTP/1.1" 200 OK
INFO:     127.0.0.1:35842 - "GET /api/program/permissions/215829 HTTP/1.1" 200 OK
INFO:     127.0.0.1:35844 - "POST /api/program/authorize HTTP/1.1" 200 OK
INFO:     127.0.0.1:35850 - "GET /api/verify HTTP/1.1" 200 OK
INFO:     127.0.0.1:51242 - "GET /api/verify HTTP/1.1" 200 OK
INFO:     127.0.0.1:39914 - "POST /api/program/authorize HTTP/1.1" 200 OK
INFO:     127.0.0.1:39924 - "GET /api/verify HTTP/1.1" 200 OK
INFO:     127.0.0.1:39926 - "GET /api/verify HTTP/1.1" 200 OK
2025-07-10 14:42:14,075 INFO sqlalchemy.engine.Engine BEGIN (implicit)
2025-07-10 14:42:14,075 INFO sqlalchemy.engine.Engine SELECT hardware_fingerprints.employee_id, hardware_fingerprints.fingerprint, hardware_fingerprints.registration_date 
FROM hardware_fingerprints 
WHERE hardware_fingerprints.employee_id = $1::VARCHAR
2025-07-10 14:42:14,075 INFO sqlalchemy.engine.Engine [cached since 8.095e+04s ago] ('215829',)
2025-07-10 14:42:14,077 INFO sqlalchemy.engine.Engine ROLLBACK
INFO:     127.0.0.1:0 - "POST /api/hardware/verify HTTP/1.0" 200 OK
INFO:     127.0.0.1:0 - "POST /login HTTP/1.0" 200 OK
INFO:     127.0.0.1:43146 - "POST /login HTTP/1.1" 200 OK
INFO:     127.0.0.1:43156 - "GET /api/program/permissions/215829 HTTP/1.1" 200 OK
INFO:     127.0.0.1:43166 - "POST /api/program/authorize HTTP/1.1" 200 OK
INFO:     127.0.0.1:43178 - "GET /api/verify HTTP/1.1" 200 OK
INFO:     127.0.0.1:43194 - "GET /api/verify HTTP/1.1" 200 OK
2025-07-10 14:52:42,937 INFO sqlalchemy.engine.Engine BEGIN (implicit)
2025-07-10 14:52:42,937 INFO sqlalchemy.engine.Engine SELECT hardware_fingerprints.employee_id, hardware_fingerprints.fingerprint, hardware_fingerprints.registration_date 
FROM hardware_fingerprints 
WHERE hardware_fingerprints.employee_id = $1::VARCHAR
2025-07-10 14:52:42,938 INFO sqlalchemy.engine.Engine [cached since 8.158e+04s ago] ('215829',)
2025-07-10 14:52:42,939 INFO sqlalchemy.engine.Engine ROLLBACK
INFO:     127.0.0.1:0 - "POST /api/hardware/verify HTTP/1.0" 200 OK
INFO:     127.0.0.1:0 - "POST /login HTTP/1.0" 200 OK
INFO:     127.0.0.1:56944 - "POST /login HTTP/1.1" 200 OK
INFO:     127.0.0.1:56952 - "GET /api/program/permissions/215829 HTTP/1.1" 200 OK
INFO:     127.0.0.1:48880 - "POST /api/program/authorize HTTP/1.1" 200 OK
INFO:     127.0.0.1:48894 - "GET /api/verify HTTP/1.1" 200 OK
INFO:     127.0.0.1:48896 - "GET /api/verify HTTP/1.1" 200 OK
2025-07-10 14:55:17,399 INFO sqlalchemy.engine.Engine BEGIN (implicit)
2025-07-10 14:55:17,399 INFO sqlalchemy.engine.Engine SELECT hardware_fingerprints.employee_id, hardware_fingerprints.fingerprint, hardware_fingerprints.registration_date 
FROM hardware_fingerprints 
WHERE hardware_fingerprints.employee_id = $1::VARCHAR
2025-07-10 14:55:17,399 INFO sqlalchemy.engine.Engine [cached since 8.173e+04s ago] ('215829',)
2025-07-10 14:55:17,401 INFO sqlalchemy.engine.Engine ROLLBACK
INFO:     127.0.0.1:0 - "POST /api/hardware/verify HTTP/1.0" 200 OK
INFO:     127.0.0.1:0 - "POST /login HTTP/1.0" 200 OK
INFO:     127.0.0.1:57554 - "POST /login HTTP/1.1" 200 OK
INFO:     127.0.0.1:57560 - "GET /api/program/permissions/215829 HTTP/1.1" 200 OK
INFO:     127.0.0.1:57566 - "POST /api/program/authorize HTTP/1.1" 200 OK
INFO:     127.0.0.1:57580 - "GET /api/verify HTTP/1.1" 200 OK
INFO:     127.0.0.1:57596 - "GET /api/verify HTTP/1.1" 200 OK
2025-07-10 15:10:59,062 INFO sqlalchemy.engine.Engine BEGIN (implicit)
2025-07-10 15:10:59,063 INFO sqlalchemy.engine.Engine SELECT hardware_fingerprints.employee_id, hardware_fingerprints.fingerprint, hardware_fingerprints.registration_date 
FROM hardware_fingerprints 
WHERE hardware_fingerprints.employee_id = $1::VARCHAR
2025-07-10 15:10:59,063 INFO sqlalchemy.engine.Engine [cached since 8.267e+04s ago] ('215829',)
2025-07-10 15:10:59,064 INFO sqlalchemy.engine.Engine ROLLBACK
INFO:     127.0.0.1:0 - "POST /api/hardware/verify HTTP/1.0" 200 OK
INFO:     127.0.0.1:0 - "POST /login HTTP/1.0" 200 OK
INFO:     127.0.0.1:55286 - "POST /login HTTP/1.1" 200 OK
INFO:     127.0.0.1:55294 - "GET /api/program/permissions/215829 HTTP/1.1" 200 OK
INFO:     127.0.0.1:55308 - "POST /api/program/authorize HTTP/1.1" 200 OK
INFO:     127.0.0.1:55310 - "GET /api/verify HTTP/1.1" 200 OK
INFO:     127.0.0.1:55314 - "GET /api/verify HTTP/1.1" 200 OK
2025-07-10 15:18:17,150 INFO sqlalchemy.engine.Engine BEGIN (implicit)
2025-07-10 15:18:17,151 INFO sqlalchemy.engine.Engine SELECT hardware_fingerprints.employee_id, hardware_fingerprints.fingerprint, hardware_fingerprints.registration_date 
FROM hardware_fingerprints 
WHERE hardware_fingerprints.employee_id = $1::VARCHAR
2025-07-10 15:18:17,151 INFO sqlalchemy.engine.Engine [cached since 8.311e+04s ago] ('215829',)
2025-07-10 15:18:17,152 INFO sqlalchemy.engine.Engine ROLLBACK
INFO:     127.0.0.1:0 - "POST /api/hardware/verify HTTP/1.0" 200 OK
INFO:     127.0.0.1:0 - "POST /login HTTP/1.0" 200 OK
INFO:     127.0.0.1:35796 - "POST /login HTTP/1.1" 200 OK
INFO:     127.0.0.1:35802 - "GET /api/program/permissions/215829 HTTP/1.1" 200 OK
INFO:     127.0.0.1:35816 - "POST /api/program/authorize HTTP/1.1" 200 OK
INFO:     127.0.0.1:35830 - "GET /api/verify HTTP/1.1" 200 OK
INFO:     127.0.0.1:35846 - "GET /api/verify HTTP/1.1" 200 OK
2025-07-10 15:19:12,849 INFO sqlalchemy.engine.Engine BEGIN (implicit)
2025-07-10 15:19:12,850 INFO sqlalchemy.engine.Engine SELECT hardware_fingerprints.employee_id, hardware_fingerprints.fingerprint, hardware_fingerprints.registration_date 
FROM hardware_fingerprints 
WHERE hardware_fingerprints.employee_id = $1::VARCHAR
2025-07-10 15:19:12,850 INFO sqlalchemy.engine.Engine [cached since 8.317e+04s ago] ('215829',)
2025-07-10 15:19:12,851 INFO sqlalchemy.engine.Engine ROLLBACK
INFO:     127.0.0.1:0 - "POST /api/hardware/verify HTTP/1.0" 200 OK
INFO:     127.0.0.1:0 - "POST /login HTTP/1.0" 200 OK
INFO:     127.0.0.1:47454 - "POST /login HTTP/1.1" 200 OK
INFO:     127.0.0.1:47458 - "GET /api/program/permissions/215829 HTTP/1.1" 200 OK
INFO:     127.0.0.1:40140 - "POST /api/program/authorize HTTP/1.1" 200 OK
INFO:     127.0.0.1:40144 - "GET /api/verify HTTP/1.1" 200 OK
INFO:     127.0.0.1:40158 - "GET /api/verify HTTP/1.1" 200 OK
2025-07-10 15:36:13,942 INFO sqlalchemy.engine.Engine BEGIN (implicit)
2025-07-10 15:36:13,943 INFO sqlalchemy.engine.Engine SELECT hardware_fingerprints.employee_id, hardware_fingerprints.fingerprint, hardware_fingerprints.registration_date 
FROM hardware_fingerprints 
WHERE hardware_fingerprints.employee_id = $1::VARCHAR
2025-07-10 15:36:13,943 INFO sqlalchemy.engine.Engine [cached since 8.419e+04s ago] ('215829',)
2025-07-10 15:36:13,947 INFO sqlalchemy.engine.Engine ROLLBACK
INFO:     127.0.0.1:0 - "POST /api/hardware/verify HTTP/1.0" 200 OK
INFO:     127.0.0.1:0 - "POST /login HTTP/1.0" 200 OK
INFO:     127.0.0.1:60688 - "POST /login HTTP/1.1" 200 OK
INFO:     127.0.0.1:60694 - "GET /api/program/permissions/215829 HTTP/1.1" 200 OK
INFO:     127.0.0.1:60698 - "POST /api/program/authorize HTTP/1.1" 200 OK
INFO:     127.0.0.1:60710 - "GET /api/verify HTTP/1.1" 200 OK
INFO:     127.0.0.1:60726 - "GET /api/verify HTTP/1.1" 200 OK
2025-07-10 15:37:25,543 INFO sqlalchemy.engine.Engine BEGIN (implicit)
2025-07-10 15:37:25,543 INFO sqlalchemy.engine.Engine SELECT hardware_fingerprints.employee_id, hardware_fingerprints.fingerprint, hardware_fingerprints.registration_date 
FROM hardware_fingerprints 
WHERE hardware_fingerprints.employee_id = $1::VARCHAR
2025-07-10 15:37:25,544 INFO sqlalchemy.engine.Engine [cached since 8.426e+04s ago] ('215829',)
2025-07-10 15:37:25,545 INFO sqlalchemy.engine.Engine ROLLBACK
INFO:     127.0.0.1:0 - "POST /api/hardware/verify HTTP/1.0" 200 OK
INFO:     127.0.0.1:0 - "POST /login HTTP/1.0" 200 OK
INFO:     127.0.0.1:58012 - "POST /login HTTP/1.1" 200 OK
INFO:     127.0.0.1:58022 - "GET /api/program/permissions/215829 HTTP/1.1" 200 OK
INFO:     127.0.0.1:58034 - "POST /api/program/authorize HTTP/1.1" 200 OK
INFO:     127.0.0.1:58040 - "GET /api/verify HTTP/1.1" 200 OK
INFO:     127.0.0.1:58054 - "GET /api/verify HTTP/1.1" 200 OK
2025-07-10 15:39:13,032 INFO sqlalchemy.engine.Engine BEGIN (implicit)
2025-07-10 15:39:13,033 INFO sqlalchemy.engine.Engine SELECT hardware_fingerprints.employee_id, hardware_fingerprints.fingerprint, hardware_fingerprints.registration_date 
FROM hardware_fingerprints 
WHERE hardware_fingerprints.employee_id = $1::VARCHAR
2025-07-10 15:39:13,033 INFO sqlalchemy.engine.Engine [cached since 8.437e+04s ago] ('215829',)
2025-07-10 15:39:13,036 INFO sqlalchemy.engine.Engine ROLLBACK
INFO:     127.0.0.1:0 - "POST /api/hardware/verify HTTP/1.0" 200 OK
INFO:     127.0.0.1:0 - "POST /login HTTP/1.0" 200 OK
INFO:     127.0.0.1:43586 - "POST /login HTTP/1.1" 200 OK
INFO:     127.0.0.1:43596 - "GET /api/program/permissions/215829 HTTP/1.1" 200 OK
INFO:     127.0.0.1:44324 - "POST /api/program/authorize HTTP/1.1" 200 OK
INFO:     127.0.0.1:44340 - "GET /api/verify HTTP/1.1" 200 OK
INFO:     127.0.0.1:44342 - "GET /api/verify HTTP/1.1" 200 OK
2025-07-10 15:46:26,984 INFO sqlalchemy.engine.Engine BEGIN (implicit)
2025-07-10 15:46:26,985 INFO sqlalchemy.engine.Engine SELECT hardware_fingerprints.employee_id, hardware_fingerprints.fingerprint, hardware_fingerprints.registration_date 
FROM hardware_fingerprints 
WHERE hardware_fingerprints.employee_id = $1::VARCHAR
2025-07-10 15:46:26,985 INFO sqlalchemy.engine.Engine [cached since 8.48e+04s ago] ('215829',)
2025-07-10 15:46:26,986 INFO sqlalchemy.engine.Engine ROLLBACK
INFO:     127.0.0.1:0 - "POST /api/hardware/verify HTTP/1.0" 200 OK
INFO:     127.0.0.1:0 - "POST /login HTTP/1.0" 200 OK
INFO:     127.0.0.1:39168 - "POST /login HTTP/1.1" 200 OK
INFO:     127.0.0.1:39178 - "GET /api/program/permissions/215829 HTTP/1.1" 200 OK
INFO:     127.0.0.1:39190 - "POST /api/program/authorize HTTP/1.1" 200 OK
INFO:     127.0.0.1:39198 - "GET /api/verify HTTP/1.1" 200 OK
INFO:     127.0.0.1:39202 - "GET /api/verify HTTP/1.1" 200 OK
2025-07-10 15:52:12,080 INFO sqlalchemy.engine.Engine BEGIN (implicit)
2025-07-10 15:52:12,081 INFO sqlalchemy.engine.Engine SELECT hardware_fingerprints.employee_id, hardware_fingerprints.fingerprint, hardware_fingerprints.registration_date 
FROM hardware_fingerprints 
WHERE hardware_fingerprints.employee_id = $1::VARCHAR
2025-07-10 15:52:12,081 INFO sqlalchemy.engine.Engine [cached since 8.514e+04s ago] ('215829',)
2025-07-10 15:52:12,084 INFO sqlalchemy.engine.Engine ROLLBACK
INFO:     127.0.0.1:0 - "POST /api/hardware/verify HTTP/1.0" 200 OK
INFO:     127.0.0.1:0 - "POST /login HTTP/1.0" 200 OK
INFO:     127.0.0.1:56976 - "POST /login HTTP/1.1" 200 OK
INFO:     127.0.0.1:56988 - "GET /api/program/permissions/215829 HTTP/1.1" 200 OK
INFO:     127.0.0.1:57002 - "POST /api/program/authorize HTTP/1.1" 200 OK
INFO:     127.0.0.1:57014 - "GET /api/verify HTTP/1.1" 200 OK
INFO:     127.0.0.1:57028 - "GET /api/verify HTTP/1.1" 200 OK
2025-07-10 15:56:17,026 INFO sqlalchemy.engine.Engine BEGIN (implicit)
2025-07-10 15:56:17,026 INFO sqlalchemy.engine.Engine SELECT hardware_fingerprints.employee_id, hardware_fingerprints.fingerprint, hardware_fingerprints.registration_date 
FROM hardware_fingerprints 
WHERE hardware_fingerprints.employee_id = $1::VARCHAR
2025-07-10 15:56:17,026 INFO sqlalchemy.engine.Engine [cached since 8.539e+04s ago] ('215829',)
2025-07-10 15:56:17,028 INFO sqlalchemy.engine.Engine ROLLBACK
INFO:     127.0.0.1:0 - "POST /api/hardware/verify HTTP/1.0" 200 OK
INFO:     127.0.0.1:0 - "POST /login HTTP/1.0" 200 OK
INFO:     127.0.0.1:45376 - "POST /login HTTP/1.1" 200 OK
INFO:     127.0.0.1:45380 - "GET /api/program/permissions/215829 HTTP/1.1" 200 OK
INFO:     127.0.0.1:45396 - "POST /api/program/authorize HTTP/1.1" 200 OK
INFO:     127.0.0.1:45402 - "GET /api/verify HTTP/1.1" 200 OK
INFO:     127.0.0.1:45410 - "GET /api/verify HTTP/1.1" 200 OK
2025-07-10 15:57:51,571 INFO sqlalchemy.engine.Engine BEGIN (implicit)
2025-07-10 15:57:51,571 INFO sqlalchemy.engine.Engine SELECT hardware_fingerprints.employee_id, hardware_fingerprints.fingerprint, hardware_fingerprints.registration_date 
FROM hardware_fingerprints 
WHERE hardware_fingerprints.employee_id = $1::VARCHAR
2025-07-10 15:57:51,572 INFO sqlalchemy.engine.Engine [cached since 8.548e+04s ago] ('215829',)
2025-07-10 15:57:51,573 INFO sqlalchemy.engine.Engine ROLLBACK
INFO:     127.0.0.1:0 - "POST /api/hardware/verify HTTP/1.0" 200 OK
INFO:     127.0.0.1:0 - "POST /login HTTP/1.0" 200 OK
INFO:     127.0.0.1:55646 - "POST /login HTTP/1.1" 200 OK
INFO:     127.0.0.1:55656 - "GET /api/program/permissions/215829 HTTP/1.1" 200 OK
INFO:     127.0.0.1:55658 - "POST /api/program/authorize HTTP/1.1" 200 OK
INFO:     127.0.0.1:55670 - "GET /api/verify HTTP/1.1" 200 OK
INFO:     127.0.0.1:55684 - "GET /api/verify HTTP/1.1" 200 OK
2025-07-10 16:08:30,551 INFO sqlalchemy.engine.Engine BEGIN (implicit)
2025-07-10 16:08:30,551 INFO sqlalchemy.engine.Engine SELECT hardware_fingerprints.employee_id, hardware_fingerprints.fingerprint, hardware_fingerprints.registration_date 
FROM hardware_fingerprints 
WHERE hardware_fingerprints.employee_id = $1::VARCHAR
2025-07-10 16:08:30,551 INFO sqlalchemy.engine.Engine [cached since 8.612e+04s ago] ('215829',)
2025-07-10 16:08:30,553 INFO sqlalchemy.engine.Engine ROLLBACK
INFO:     127.0.0.1:0 - "POST /api/hardware/verify HTTP/1.0" 200 OK
INFO:     127.0.0.1:0 - "POST /login HTTP/1.0" 200 OK
INFO:     127.0.0.1:51584 - "POST /login HTTP/1.1" 200 OK
INFO:     127.0.0.1:51592 - "GET /api/program/permissions/215829 HTTP/1.1" 200 OK
INFO:     127.0.0.1:51598 - "POST /api/program/authorize HTTP/1.1" 200 OK
INFO:     127.0.0.1:51610 - "GET /api/verify HTTP/1.1" 200 OK
INFO:     127.0.0.1:51624 - "GET /api/verify HTTP/1.1" 200 OK
2025-07-10 16:19:12,657 INFO sqlalchemy.engine.Engine BEGIN (implicit)
2025-07-10 16:19:12,657 INFO sqlalchemy.engine.Engine SELECT hardware_fingerprints.employee_id, hardware_fingerprints.fingerprint, hardware_fingerprints.registration_date 
FROM hardware_fingerprints 
WHERE hardware_fingerprints.employee_id = $1::VARCHAR
2025-07-10 16:19:12,657 INFO sqlalchemy.engine.Engine [cached since 8.677e+04s ago] ('215829',)
2025-07-10 16:19:12,659 INFO sqlalchemy.engine.Engine ROLLBACK
INFO:     127.0.0.1:0 - "POST /api/hardware/verify HTTP/1.0" 200 OK
INFO:     127.0.0.1:0 - "POST /login HTTP/1.0" 200 OK
INFO:     127.0.0.1:50442 - "POST /login HTTP/1.1" 200 OK
INFO:     127.0.0.1:50450 - "GET /api/program/permissions/215829 HTTP/1.1" 200 OK
INFO:     127.0.0.1:40476 - "POST /api/program/authorize HTTP/1.1" 200 OK
INFO:     127.0.0.1:40490 - "GET /api/verify HTTP/1.1" 200 OK
INFO:     127.0.0.1:40498 - "GET /api/verify HTTP/1.1" 200 OK
2025-07-10 16:20:34,413 INFO sqlalchemy.engine.Engine BEGIN (implicit)
2025-07-10 16:20:34,414 INFO sqlalchemy.engine.Engine SELECT hardware_fingerprints.employee_id, hardware_fingerprints.fingerprint, hardware_fingerprints.registration_date 
FROM hardware_fingerprints 
WHERE hardware_fingerprints.employee_id = $1::VARCHAR
2025-07-10 16:20:34,414 INFO sqlalchemy.engine.Engine [cached since 8.685e+04s ago] ('215829',)
2025-07-10 16:20:34,415 INFO sqlalchemy.engine.Engine ROLLBACK
INFO:     127.0.0.1:0 - "POST /api/hardware/verify HTTP/1.0" 200 OK
INFO:     127.0.0.1:0 - "POST /login HTTP/1.0" 200 OK
INFO:     127.0.0.1:36206 - "POST /login HTTP/1.1" 200 OK
INFO:     127.0.0.1:36208 - "GET /api/program/permissions/215829 HTTP/1.1" 200 OK
INFO:     127.0.0.1:36212 - "POST /api/program/authorize HTTP/1.1" 200 OK
INFO:     127.0.0.1:36214 - "GET /api/verify HTTP/1.1" 200 OK
INFO:     127.0.0.1:36220 - "GET /api/verify HTTP/1.1" 200 OK
2025-07-10 16:21:19,077 INFO sqlalchemy.engine.Engine BEGIN (implicit)
2025-07-10 16:21:19,077 INFO sqlalchemy.engine.Engine SELECT hardware_fingerprints.employee_id, hardware_fingerprints.fingerprint, hardware_fingerprints.registration_date 
FROM hardware_fingerprints 
WHERE hardware_fingerprints.employee_id = $1::VARCHAR
2025-07-10 16:21:19,077 INFO sqlalchemy.engine.Engine [cached since 8.689e+04s ago] ('215829',)
2025-07-10 16:21:19,079 INFO sqlalchemy.engine.Engine ROLLBACK
INFO:     127.0.0.1:0 - "POST /api/hardware/verify HTTP/1.0" 200 OK
INFO:     127.0.0.1:0 - "POST /login HTTP/1.0" 200 OK
INFO:     127.0.0.1:35126 - "POST /login HTTP/1.1" 200 OK
INFO:     127.0.0.1:35142 - "GET /api/program/permissions/215829 HTTP/1.1" 200 OK
INFO:     127.0.0.1:35150 - "POST /api/program/authorize HTTP/1.1" 200 OK
INFO:     127.0.0.1:35154 - "GET /api/verify HTTP/1.1" 200 OK
INFO:     127.0.0.1:35166 - "GET /api/verify HTTP/1.1" 200 OK
2025-07-10 16:26:23,239 INFO sqlalchemy.engine.Engine BEGIN (implicit)
2025-07-10 16:26:23,239 INFO sqlalchemy.engine.Engine SELECT hardware_fingerprints.employee_id, hardware_fingerprints.fingerprint, hardware_fingerprints.registration_date 
FROM hardware_fingerprints 
WHERE hardware_fingerprints.employee_id = $1::VARCHAR
2025-07-10 16:26:23,239 INFO sqlalchemy.engine.Engine [cached since 8.72e+04s ago] ('215829',)
2025-07-10 16:26:23,241 INFO sqlalchemy.engine.Engine ROLLBACK
INFO:     127.0.0.1:0 - "POST /api/hardware/verify HTTP/1.0" 200 OK
INFO:     127.0.0.1:0 - "POST /login HTTP/1.0" 200 OK
INFO:     127.0.0.1:42906 - "POST /login HTTP/1.1" 200 OK
INFO:     127.0.0.1:42912 - "GET /api/program/permissions/215829 HTTP/1.1" 200 OK
INFO:     127.0.0.1:35024 - "POST /api/program/authorize HTTP/1.1" 200 OK
INFO:     127.0.0.1:35036 - "GET /api/verify HTTP/1.1" 200 OK
INFO:     127.0.0.1:35038 - "GET /api/verify HTTP/1.1" 200 OK
2025-07-10 16:29:30,579 INFO sqlalchemy.engine.Engine BEGIN (implicit)
2025-07-10 16:29:30,579 INFO sqlalchemy.engine.Engine SELECT hardware_fingerprints.employee_id, hardware_fingerprints.fingerprint, hardware_fingerprints.registration_date 
FROM hardware_fingerprints 
WHERE hardware_fingerprints.employee_id = $1::VARCHAR
2025-07-10 16:29:30,579 INFO sqlalchemy.engine.Engine [cached since 8.738e+04s ago] ('215829',)
2025-07-10 16:29:30,581 INFO sqlalchemy.engine.Engine ROLLBACK
INFO:     127.0.0.1:0 - "POST /api/hardware/verify HTTP/1.0" 200 OK
INFO:     127.0.0.1:0 - "POST /login HTTP/1.0" 200 OK
INFO:     127.0.0.1:60190 - "POST /login HTTP/1.1" 200 OK
INFO:     127.0.0.1:60200 - "GET /api/program/permissions/215829 HTTP/1.1" 200 OK
INFO:     127.0.0.1:60208 - "POST /api/program/authorize HTTP/1.1" 200 OK
INFO:     127.0.0.1:60224 - "GET /api/verify HTTP/1.1" 200 OK
INFO:     127.0.0.1:60226 - "GET /api/verify HTTP/1.1" 200 OK
2025-07-10 16:30:09,264 INFO sqlalchemy.engine.Engine BEGIN (implicit)
2025-07-10 16:30:09,265 INFO sqlalchemy.engine.Engine SELECT hardware_fingerprints.employee_id, hardware_fingerprints.fingerprint, hardware_fingerprints.registration_date 
FROM hardware_fingerprints 
WHERE hardware_fingerprints.employee_id = $1::VARCHAR
2025-07-10 16:30:09,265 INFO sqlalchemy.engine.Engine [cached since 8.742e+04s ago] ('215829',)
2025-07-10 16:30:09,268 INFO sqlalchemy.engine.Engine ROLLBACK
INFO:     127.0.0.1:0 - "POST /api/hardware/verify HTTP/1.0" 200 OK
INFO:     127.0.0.1:0 - "POST /login HTTP/1.0" 200 OK
INFO:     127.0.0.1:41098 - "POST /login HTTP/1.1" 200 OK
INFO:     127.0.0.1:41106 - "GET /api/program/permissions/215829 HTTP/1.1" 200 OK
INFO:     127.0.0.1:41122 - "POST /api/program/authorize HTTP/1.1" 200 OK
INFO:     127.0.0.1:41126 - "GET /api/verify HTTP/1.1" 200 OK
INFO:     127.0.0.1:41130 - "GET /api/verify HTTP/1.1" 200 OK
2025-07-10 16:31:24,488 INFO sqlalchemy.engine.Engine BEGIN (implicit)
2025-07-10 16:31:24,488 INFO sqlalchemy.engine.Engine SELECT hardware_fingerprints.employee_id, hardware_fingerprints.fingerprint, hardware_fingerprints.registration_date 
FROM hardware_fingerprints 
WHERE hardware_fingerprints.employee_id = $1::VARCHAR
2025-07-10 16:31:24,488 INFO sqlalchemy.engine.Engine [cached since 8.75e+04s ago] ('215829',)
2025-07-10 16:31:24,490 INFO sqlalchemy.engine.Engine ROLLBACK
INFO:     127.0.0.1:0 - "POST /api/hardware/verify HTTP/1.0" 200 OK
INFO:     127.0.0.1:0 - "POST /login HTTP/1.0" 200 OK
INFO:     127.0.0.1:59014 - "POST /login HTTP/1.1" 200 OK
INFO:     127.0.0.1:59024 - "GET /api/program/permissions/215829 HTTP/1.1" 200 OK
INFO:     127.0.0.1:59030 - "POST /api/program/authorize HTTP/1.1" 200 OK
INFO:     127.0.0.1:59036 - "GET /api/verify HTTP/1.1" 200 OK
INFO:     127.0.0.1:59048 - "GET /api/verify HTTP/1.1" 200 OK
2025-07-10 16:32:21,954 INFO sqlalchemy.engine.Engine BEGIN (implicit)
2025-07-10 16:32:21,955 INFO sqlalchemy.engine.Engine SELECT hardware_fingerprints.employee_id, hardware_fingerprints.fingerprint, hardware_fingerprints.registration_date 
FROM hardware_fingerprints 
WHERE hardware_fingerprints.employee_id = $1::VARCHAR
2025-07-10 16:32:21,955 INFO sqlalchemy.engine.Engine [cached since 8.755e+04s ago] ('215829',)
2025-07-10 16:32:21,956 INFO sqlalchemy.engine.Engine ROLLBACK
INFO:     127.0.0.1:0 - "POST /api/hardware/verify HTTP/1.0" 200 OK
INFO:     127.0.0.1:0 - "POST /login HTTP/1.0" 200 OK
INFO:     127.0.0.1:54154 - "POST /login HTTP/1.1" 200 OK
INFO:     127.0.0.1:54156 - "GET /api/program/permissions/215829 HTTP/1.1" 200 OK
INFO:     127.0.0.1:54170 - "POST /api/program/authorize HTTP/1.1" 200 OK
INFO:     127.0.0.1:54178 - "GET /api/verify HTTP/1.1" 200 OK
INFO:     127.0.0.1:54194 - "GET /api/verify HTTP/1.1" 200 OK
2025-07-10 16:57:44,837 INFO sqlalchemy.engine.Engine BEGIN (implicit)
2025-07-10 16:57:44,837 INFO sqlalchemy.engine.Engine SELECT hardware_fingerprints.employee_id, hardware_fingerprints.fingerprint, hardware_fingerprints.registration_date 
FROM hardware_fingerprints 
WHERE hardware_fingerprints.employee_id = $1::VARCHAR
2025-07-10 16:57:44,837 INFO sqlalchemy.engine.Engine [cached since 8.908e+04s ago] ('215829',)
2025-07-10 16:57:44,839 INFO sqlalchemy.engine.Engine ROLLBACK
INFO:     127.0.0.1:0 - "POST /api/hardware/verify HTTP/1.0" 200 OK
INFO:     127.0.0.1:0 - "POST /login HTTP/1.0" 200 OK
INFO:     127.0.0.1:52552 - "POST /login HTTP/1.1" 200 OK
INFO:     127.0.0.1:52560 - "GET /api/program/permissions/215829 HTTP/1.1" 200 OK
INFO:     127.0.0.1:52566 - "POST /api/program/authorize HTTP/1.1" 200 OK
INFO:     127.0.0.1:52578 - "GET /api/verify HTTP/1.1" 200 OK
INFO:     127.0.0.1:52584 - "GET /api/verify HTTP/1.1" 200 OK
2025-07-10 17:17:45,243 INFO sqlalchemy.engine.Engine BEGIN (implicit)
2025-07-10 17:17:45,243 INFO sqlalchemy.engine.Engine SELECT hardware_fingerprints.employee_id, hardware_fingerprints.fingerprint, hardware_fingerprints.registration_date 
FROM hardware_fingerprints 
WHERE hardware_fingerprints.employee_id = $1::VARCHAR
2025-07-10 17:17:45,243 INFO sqlalchemy.engine.Engine [cached since 9.028e+04s ago] ('215829',)
2025-07-10 17:17:45,246 INFO sqlalchemy.engine.Engine ROLLBACK
INFO:     127.0.0.1:0 - "POST /api/hardware/verify HTTP/1.0" 200 OK
INFO:     127.0.0.1:0 - "POST /login HTTP/1.0" 200 OK
INFO:     127.0.0.1:47770 - "POST /login HTTP/1.1" 200 OK
INFO:     127.0.0.1:47776 - "GET /api/program/permissions/215829 HTTP/1.1" 200 OK
INFO:     127.0.0.1:47782 - "POST /api/program/authorize HTTP/1.1" 200 OK
INFO:     127.0.0.1:47794 - "GET /api/verify HTTP/1.1" 200 OK
INFO:     127.0.0.1:47798 - "GET /api/verify HTTP/1.1" 200 OK
INFO:     127.0.0.1:44478 - "POST /api/program/authorize HTTP/1.1" 200 OK
INFO:     127.0.0.1:44482 - "GET /api/verify HTTP/1.1" 200 OK
INFO:     127.0.0.1:44498 - "GET /api/verify HTTP/1.1" 200 OK
2025-07-10 17:31:54,024 INFO sqlalchemy.engine.Engine BEGIN (implicit)
2025-07-10 17:31:54,024 INFO sqlalchemy.engine.Engine SELECT hardware_fingerprints.employee_id, hardware_fingerprints.fingerprint, hardware_fingerprints.registration_date 
FROM hardware_fingerprints 
WHERE hardware_fingerprints.employee_id = $1::VARCHAR
2025-07-10 17:31:54,026 INFO sqlalchemy.engine.Engine [cached since 9.113e+04s ago] ('215829',)
2025-07-10 17:31:54,029 INFO sqlalchemy.engine.Engine ROLLBACK
INFO:     127.0.0.1:0 - "POST /api/hardware/verify HTTP/1.0" 200 OK
INFO:     127.0.0.1:0 - "POST /login HTTP/1.0" 200 OK
INFO:     127.0.0.1:57292 - "POST /login HTTP/1.1" 200 OK
INFO:     127.0.0.1:57300 - "GET /api/program/permissions/215829 HTTP/1.1" 200 OK
INFO:     127.0.0.1:57314 - "POST /api/program/authorize HTTP/1.1" 200 OK
INFO:     127.0.0.1:57328 - "GET /api/verify HTTP/1.1" 200 OK
INFO:     127.0.0.1:57340 - "GET /api/verify HTTP/1.1" 200 OK
INFO:     127.0.0.1:48436 - "POST /api/program/authorize HTTP/1.1" 200 OK
INFO:     127.0.0.1:48448 - "GET /api/verify HTTP/1.1" 200 OK
INFO:     127.0.0.1:48462 - "GET /api/verify HTTP/1.1" 200 OK
2025-07-10 18:17:13,486 INFO sqlalchemy.engine.Engine BEGIN (implicit)
2025-07-10 18:17:13,486 INFO sqlalchemy.engine.Engine SELECT hardware_fingerprints.employee_id, hardware_fingerprints.fingerprint, hardware_fingerprints.registration_date 
FROM hardware_fingerprints 
WHERE hardware_fingerprints.employee_id = $1::VARCHAR
2025-07-10 18:17:13,486 INFO sqlalchemy.engine.Engine [cached since 9.385e+04s ago] ('215829',)
2025-07-10 18:17:13,488 INFO sqlalchemy.engine.Engine ROLLBACK
INFO:     127.0.0.1:0 - "POST /api/hardware/verify HTTP/1.0" 200 OK
INFO:     127.0.0.1:0 - "POST /login HTTP/1.0" 200 OK
INFO:     127.0.0.1:58294 - "POST /login HTTP/1.1" 200 OK
INFO:     127.0.0.1:58300 - "GET /api/program/permissions/215829 HTTP/1.1" 200 OK
INFO:     127.0.0.1:58306 - "POST /api/program/authorize HTTP/1.1" 200 OK
INFO:     127.0.0.1:58314 - "GET /api/verify HTTP/1.1" 200 OK
INFO:     127.0.0.1:58328 - "GET /api/verify HTTP/1.1" 200 OK
2025-07-10 18:29:16,725 INFO sqlalchemy.engine.Engine BEGIN (implicit)
2025-07-10 18:29:16,725 INFO sqlalchemy.engine.Engine SELECT hardware_fingerprints.employee_id, hardware_fingerprints.fingerprint, hardware_fingerprints.registration_date 
FROM hardware_fingerprints 
WHERE hardware_fingerprints.employee_id = $1::VARCHAR
2025-07-10 18:29:16,725 INFO sqlalchemy.engine.Engine [cached since 9.457e+04s ago] ('215829',)
2025-07-10 18:29:16,727 INFO sqlalchemy.engine.Engine ROLLBACK
INFO:     127.0.0.1:0 - "POST /api/hardware/verify HTTP/1.0" 200 OK
INFO:     127.0.0.1:0 - "POST /login HTTP/1.0" 200 OK
INFO:     127.0.0.1:51978 - "POST /login HTTP/1.1" 200 OK
INFO:     127.0.0.1:51988 - "GET /api/program/permissions/215829 HTTP/1.1" 200 OK
INFO:     127.0.0.1:51998 - "POST /api/program/authorize HTTP/1.1" 200 OK
INFO:     127.0.0.1:52006 - "GET /api/verify HTTP/1.1" 200 OK
INFO:     127.0.0.1:52012 - "GET /api/verify HTTP/1.1" 200 OK
INFO:     127.0.0.1:37200 - "POST /api/program/authorize HTTP/1.1" 200 OK
INFO:     127.0.0.1:37208 - "GET /api/verify HTTP/1.1" 200 OK
INFO:     127.0.0.1:37220 - "GET /api/verify HTTP/1.1" 200 OK
INFO:     127.0.0.1:46192 - "GET /api/verify HTTP/1.1" 401 Unauthorized
INFO:     127.0.0.1:50066 - "GET /api/verify HTTP/1.1" 401 Unauthorized
2025-07-10 18:40:12,699 INFO sqlalchemy.engine.Engine BEGIN (implicit)
2025-07-10 18:40:12,699 INFO sqlalchemy.engine.Engine SELECT hardware_fingerprints.employee_id, hardware_fingerprints.fingerprint, hardware_fingerprints.registration_date 
FROM hardware_fingerprints 
WHERE hardware_fingerprints.employee_id = $1::VARCHAR
2025-07-10 18:40:12,700 INFO sqlalchemy.engine.Engine [cached since 9.523e+04s ago] ('215829',)
2025-07-10 18:40:12,703 INFO sqlalchemy.engine.Engine ROLLBACK
INFO:     127.0.0.1:0 - "POST /api/hardware/verify HTTP/1.0" 200 OK
INFO:     127.0.0.1:0 - "POST /login HTTP/1.0" 200 OK
INFO:     127.0.0.1:35560 - "POST /login HTTP/1.1" 200 OK
INFO:     127.0.0.1:35566 - "GET /api/program/permissions/215829 HTTP/1.1" 200 OK
INFO:     127.0.0.1:36864 - "POST /api/program/authorize HTTP/1.1" 200 OK
INFO:     127.0.0.1:36868 - "GET /api/verify HTTP/1.1" 200 OK
INFO:     127.0.0.1:36878 - "GET /api/verify HTTP/1.1" 200 OK
2025-07-10 18:40:59,769 INFO sqlalchemy.engine.Engine BEGIN (implicit)
2025-07-10 18:40:59,770 INFO sqlalchemy.engine.Engine SELECT hardware_fingerprints.employee_id, hardware_fingerprints.fingerprint, hardware_fingerprints.registration_date 
FROM hardware_fingerprints 
WHERE hardware_fingerprints.employee_id = $1::VARCHAR
2025-07-10 18:40:59,770 INFO sqlalchemy.engine.Engine [cached since 9.527e+04s ago] ('215829',)
2025-07-10 18:40:59,773 INFO sqlalchemy.engine.Engine ROLLBACK
INFO:     127.0.0.1:0 - "POST /api/hardware/verify HTTP/1.0" 200 OK
INFO:     127.0.0.1:0 - "POST /login HTTP/1.0" 200 OK
INFO:     127.0.0.1:50724 - "POST /login HTTP/1.1" 200 OK
INFO:     127.0.0.1:50734 - "GET /api/program/permissions/215829 HTTP/1.1" 200 OK
INFO:     127.0.0.1:50742 - "POST /api/program/authorize HTTP/1.1" 200 OK
INFO:     127.0.0.1:50756 - "GET /api/verify HTTP/1.1" 200 OK
INFO:     127.0.0.1:50766 - "GET /api/verify HTTP/1.1" 200 OK
2025-07-10 18:48:00,689 INFO sqlalchemy.engine.Engine BEGIN (implicit)
2025-07-10 18:48:00,689 INFO sqlalchemy.engine.Engine SELECT hardware_fingerprints.employee_id, hardware_fingerprints.fingerprint, hardware_fingerprints.registration_date 
FROM hardware_fingerprints 
WHERE hardware_fingerprints.employee_id = $1::VARCHAR
2025-07-10 18:48:00,690 INFO sqlalchemy.engine.Engine [cached since 9.569e+04s ago] ('215829',)
2025-07-10 18:48:00,691 INFO sqlalchemy.engine.Engine ROLLBACK
INFO:     127.0.0.1:0 - "POST /api/hardware/verify HTTP/1.0" 200 OK
INFO:     127.0.0.1:0 - "POST /login HTTP/1.0" 200 OK
INFO:     127.0.0.1:43602 - "POST /login HTTP/1.1" 200 OK
INFO:     127.0.0.1:43616 - "GET /api/program/permissions/215829 HTTP/1.1" 200 OK
INFO:     127.0.0.1:43628 - "POST /api/program/authorize HTTP/1.1" 200 OK
INFO:     127.0.0.1:43634 - "GET /api/verify HTTP/1.1" 200 OK
INFO:     127.0.0.1:43640 - "GET /api/verify HTTP/1.1" 200 OK
INFO:     127.0.0.1:35484 - "POST /api/program/authorize HTTP/1.1" 200 OK
INFO:     127.0.0.1:35492 - "GET /api/verify HTTP/1.1" 200 OK
INFO:     127.0.0.1:35496 - "GET /api/verify HTTP/1.1" 200 OK
2025-07-10 18:53:42,071 INFO sqlalchemy.engine.Engine BEGIN (implicit)
2025-07-10 18:53:42,071 INFO sqlalchemy.engine.Engine SELECT hardware_fingerprints.employee_id, hardware_fingerprints.fingerprint, hardware_fingerprints.registration_date 
FROM hardware_fingerprints 
WHERE hardware_fingerprints.employee_id = $1::VARCHAR
2025-07-10 18:53:42,071 INFO sqlalchemy.engine.Engine [cached since 9.603e+04s ago] ('215829',)
2025-07-10 18:53:42,072 INFO sqlalchemy.engine.Engine ROLLBACK
INFO:     127.0.0.1:0 - "POST /api/hardware/verify HTTP/1.0" 200 OK
INFO:     127.0.0.1:0 - "POST /login HTTP/1.0" 200 OK
INFO:     127.0.0.1:43280 - "POST /login HTTP/1.1" 200 OK
INFO:     127.0.0.1:43290 - "GET /api/program/permissions/215829 HTTP/1.1" 200 OK
INFO:     127.0.0.1:43304 - "POST /api/program/authorize HTTP/1.1" 200 OK
INFO:     127.0.0.1:43312 - "GET /api/verify HTTP/1.1" 200 OK
INFO:     127.0.0.1:43322 - "GET /api/verify HTTP/1.1" 200 OK
2025-07-10 18:54:19,949 INFO sqlalchemy.engine.Engine BEGIN (implicit)
2025-07-10 18:54:19,950 INFO sqlalchemy.engine.Engine SELECT hardware_fingerprints.employee_id, hardware_fingerprints.fingerprint, hardware_fingerprints.registration_date 
FROM hardware_fingerprints 
WHERE hardware_fingerprints.employee_id = $1::VARCHAR
2025-07-10 18:54:19,950 INFO sqlalchemy.engine.Engine [cached since 9.607e+04s ago] ('215829',)
2025-07-10 18:54:19,951 INFO sqlalchemy.engine.Engine ROLLBACK
INFO:     127.0.0.1:0 - "POST /api/hardware/verify HTTP/1.0" 200 OK
INFO:     127.0.0.1:0 - "POST /login HTTP/1.0" 200 OK
INFO:     127.0.0.1:58058 - "POST /login HTTP/1.1" 200 OK
INFO:     127.0.0.1:58072 - "GET /api/program/permissions/215829 HTTP/1.1" 200 OK
INFO:     127.0.0.1:58078 - "POST /api/program/authorize HTTP/1.1" 200 OK
INFO:     127.0.0.1:58084 - "GET /api/verify HTTP/1.1" 200 OK
INFO:     127.0.0.1:58094 - "GET /api/verify HTTP/1.1" 200 OK
INFO:     127.0.0.1:42326 - "POST /api/program/authorize HTTP/1.1" 200 OK
INFO:     127.0.0.1:42330 - "GET /api/verify HTTP/1.1" 200 OK
INFO:     127.0.0.1:42346 - "GET /api/verify HTTP/1.1" 200 OK
2025-07-11 08:24:18,889 INFO sqlalchemy.engine.Engine BEGIN (implicit)
2025-07-11 08:24:18,889 INFO sqlalchemy.engine.Engine SELECT hardware_fingerprints.employee_id, hardware_fingerprints.fingerprint, hardware_fingerprints.registration_date 
FROM hardware_fingerprints 
WHERE hardware_fingerprints.employee_id = $1::VARCHAR
2025-07-11 08:24:18,889 INFO sqlalchemy.engine.Engine [cached since 1.447e+05s ago] ('215829',)
2025-07-11 08:24:18,890 INFO sqlalchemy.engine.Engine ROLLBACK
INFO:     127.0.0.1:0 - "POST /api/hardware/verify HTTP/1.0" 200 OK
INFO:     127.0.0.1:0 - "POST /login HTTP/1.0" 200 OK
INFO:     127.0.0.1:53644 - "POST /login HTTP/1.1" 200 OK
INFO:     127.0.0.1:53650 - "GET /api/program/permissions/215829 HTTP/1.1" 200 OK
INFO:     127.0.0.1:53654 - "POST /api/program/authorize HTTP/1.1" 200 OK
INFO:     127.0.0.1:53662 - "GET /api/verify HTTP/1.1" 200 OK
INFO:     127.0.0.1:53678 - "GET /api/verify HTTP/1.1" 200 OK
2025-07-11 08:25:48,165 INFO sqlalchemy.engine.Engine BEGIN (implicit)
2025-07-11 08:25:48,165 INFO sqlalchemy.engine.Engine SELECT hardware_fingerprints.employee_id, hardware_fingerprints.fingerprint, hardware_fingerprints.registration_date 
FROM hardware_fingerprints 
WHERE hardware_fingerprints.employee_id = $1::VARCHAR
2025-07-11 08:25:48,166 INFO sqlalchemy.engine.Engine [cached since 1.448e+05s ago] ('215829',)
2025-07-11 08:25:48,169 INFO sqlalchemy.engine.Engine ROLLBACK
INFO:     127.0.0.1:0 - "POST /api/hardware/verify HTTP/1.0" 200 OK
INFO:     127.0.0.1:0 - "POST /login HTTP/1.0" 200 OK
INFO:     127.0.0.1:33110 - "POST /login HTTP/1.1" 200 OK
INFO:     127.0.0.1:33116 - "GET /api/program/permissions/215829 HTTP/1.1" 200 OK
INFO:     127.0.0.1:33122 - "POST /api/program/authorize HTTP/1.1" 200 OK
INFO:     127.0.0.1:33128 - "GET /api/verify HTTP/1.1" 200 OK
INFO:     127.0.0.1:33142 - "GET /api/verify HTTP/1.1" 200 OK
2025-07-11 08:27:09,734 INFO sqlalchemy.engine.Engine BEGIN (implicit)
2025-07-11 08:27:09,734 INFO sqlalchemy.engine.Engine SELECT hardware_fingerprints.employee_id, hardware_fingerprints.fingerprint, hardware_fingerprints.registration_date 
FROM hardware_fingerprints 
WHERE hardware_fingerprints.employee_id = $1::VARCHAR
2025-07-11 08:27:09,734 INFO sqlalchemy.engine.Engine [cached since 1.448e+05s ago] ('215829',)
2025-07-11 08:27:09,736 INFO sqlalchemy.engine.Engine ROLLBACK
INFO:     127.0.0.1:0 - "POST /api/hardware/verify HTTP/1.0" 200 OK
INFO:     127.0.0.1:0 - "POST /login HTTP/1.0" 200 OK
INFO:     127.0.0.1:35046 - "POST /login HTTP/1.1" 200 OK
INFO:     127.0.0.1:35050 - "GET /api/program/permissions/215829 HTTP/1.1" 200 OK
INFO:     127.0.0.1:35062 - "POST /api/program/authorize HTTP/1.1" 200 OK
INFO:     127.0.0.1:35068 - "GET /api/verify HTTP/1.1" 200 OK
INFO:     127.0.0.1:35080 - "GET /api/verify HTTP/1.1" 200 OK
2025-07-11 08:27:41,471 INFO sqlalchemy.engine.Engine BEGIN (implicit)
2025-07-11 08:27:41,471 INFO sqlalchemy.engine.Engine SELECT hardware_fingerprints.employee_id, hardware_fingerprints.fingerprint, hardware_fingerprints.registration_date 
FROM hardware_fingerprints 
WHERE hardware_fingerprints.employee_id = $1::VARCHAR
2025-07-11 08:27:41,471 INFO sqlalchemy.engine.Engine [cached since 1.449e+05s ago] ('215829',)
2025-07-11 08:27:41,473 INFO sqlalchemy.engine.Engine ROLLBACK
INFO:     127.0.0.1:0 - "POST /api/hardware/verify HTTP/1.0" 200 OK
INFO:     127.0.0.1:0 - "POST /login HTTP/1.0" 200 OK
INFO:     127.0.0.1:39964 - "POST /login HTTP/1.1" 200 OK
INFO:     127.0.0.1:39976 - "GET /api/program/permissions/215829 HTTP/1.1" 200 OK
INFO:     127.0.0.1:39986 - "POST /api/program/authorize HTTP/1.1" 200 OK
INFO:     127.0.0.1:40000 - "GET /api/verify HTTP/1.1" 200 OK
INFO:     127.0.0.1:40002 - "GET /api/verify HTTP/1.1" 200 OK
2025-07-11 08:31:14,167 INFO sqlalchemy.engine.Engine BEGIN (implicit)
2025-07-11 08:31:14,167 INFO sqlalchemy.engine.Engine SELECT hardware_fingerprints.employee_id, hardware_fingerprints.fingerprint, hardware_fingerprints.registration_date 
FROM hardware_fingerprints 
WHERE hardware_fingerprints.employee_id = $1::VARCHAR
2025-07-11 08:31:14,167 INFO sqlalchemy.engine.Engine [cached since 1.451e+05s ago] ('215829',)
2025-07-11 08:31:14,169 INFO sqlalchemy.engine.Engine ROLLBACK
INFO:     127.0.0.1:0 - "POST /api/hardware/verify HTTP/1.0" 200 OK
INFO:     127.0.0.1:0 - "POST /login HTTP/1.0" 200 OK
INFO:     127.0.0.1:39504 - "POST /login HTTP/1.1" 200 OK
INFO:     127.0.0.1:39508 - "GET /api/program/permissions/215829 HTTP/1.1" 200 OK
INFO:     127.0.0.1:39516 - "POST /api/program/authorize HTTP/1.1" 200 OK
INFO:     127.0.0.1:39528 - "GET /api/verify HTTP/1.1" 200 OK
INFO:     127.0.0.1:39534 - "GET /api/verify HTTP/1.1" 200 OK
2025-07-11 08:33:20,889 INFO sqlalchemy.engine.Engine BEGIN (implicit)
2025-07-11 08:33:20,889 INFO sqlalchemy.engine.Engine SELECT hardware_fingerprints.employee_id, hardware_fingerprints.fingerprint, hardware_fingerprints.registration_date 
FROM hardware_fingerprints 
WHERE hardware_fingerprints.employee_id = $1::VARCHAR
2025-07-11 08:33:20,889 INFO sqlalchemy.engine.Engine [cached since 1.452e+05s ago] ('215829',)
2025-07-11 08:33:20,891 INFO sqlalchemy.engine.Engine ROLLBACK
INFO:     127.0.0.1:0 - "POST /api/hardware/verify HTTP/1.0" 200 OK
INFO:     127.0.0.1:0 - "POST /login HTTP/1.0" 200 OK
INFO:     127.0.0.1:55104 - "POST /login HTTP/1.1" 200 OK
INFO:     127.0.0.1:55116 - "GET /api/program/permissions/215829 HTTP/1.1" 200 OK
INFO:     127.0.0.1:55130 - "POST /api/program/authorize HTTP/1.1" 200 OK
INFO:     127.0.0.1:55142 - "GET /api/verify HTTP/1.1" 200 OK
INFO:     127.0.0.1:55152 - "GET /api/verify HTTP/1.1" 200 OK
2025-07-11 08:34:59,975 INFO sqlalchemy.engine.Engine BEGIN (implicit)
2025-07-11 08:34:59,975 INFO sqlalchemy.engine.Engine SELECT hardware_fingerprints.employee_id, hardware_fingerprints.fingerprint, hardware_fingerprints.registration_date 
FROM hardware_fingerprints 
WHERE hardware_fingerprints.employee_id = $1::VARCHAR
2025-07-11 08:34:59,976 INFO sqlalchemy.engine.Engine [cached since 1.453e+05s ago] ('215829',)
2025-07-11 08:34:59,978 INFO sqlalchemy.engine.Engine ROLLBACK
INFO:     127.0.0.1:0 - "POST /api/hardware/verify HTTP/1.0" 200 OK
INFO:     127.0.0.1:0 - "POST /login HTTP/1.0" 200 OK
INFO:     127.0.0.1:41942 - "POST /login HTTP/1.1" 200 OK
INFO:     127.0.0.1:41952 - "GET /api/program/permissions/215829 HTTP/1.1" 200 OK
INFO:     127.0.0.1:41968 - "POST /api/program/authorize HTTP/1.1" 200 OK
INFO:     127.0.0.1:41972 - "GET /api/verify HTTP/1.1" 200 OK
INFO:     127.0.0.1:41980 - "GET /api/verify HTTP/1.1" 200 OK
2025-07-11 08:35:31,875 INFO sqlalchemy.engine.Engine BEGIN (implicit)
2025-07-11 08:35:31,875 INFO sqlalchemy.engine.Engine SELECT hardware_fingerprints.employee_id, hardware_fingerprints.fingerprint, hardware_fingerprints.registration_date 
FROM hardware_fingerprints 
WHERE hardware_fingerprints.employee_id = $1::VARCHAR
2025-07-11 08:35:31,876 INFO sqlalchemy.engine.Engine [cached since 1.453e+05s ago] ('215829',)
2025-07-11 08:35:31,877 INFO sqlalchemy.engine.Engine ROLLBACK
INFO:     127.0.0.1:0 - "POST /api/hardware/verify HTTP/1.0" 200 OK
INFO:     127.0.0.1:0 - "POST /login HTTP/1.0" 200 OK
INFO:     127.0.0.1:60750 - "POST /login HTTP/1.1" 200 OK
INFO:     127.0.0.1:60766 - "GET /api/program/permissions/215829 HTTP/1.1" 200 OK
INFO:     127.0.0.1:60782 - "POST /api/program/authorize HTTP/1.1" 200 OK
INFO:     127.0.0.1:60794 - "GET /api/verify HTTP/1.1" 200 OK
INFO:     127.0.0.1:60810 - "GET /api/verify HTTP/1.1" 200 OK
INFO:     127.0.0.1:49092 - "POST /api/program/authorize HTTP/1.1" 200 OK
INFO:     127.0.0.1:49098 - "GET /api/verify HTTP/1.1" 200 OK
INFO:     127.0.0.1:49104 - "GET /api/verify HTTP/1.1" 200 OK
2025-07-11 09:09:05,754 INFO sqlalchemy.engine.Engine BEGIN (implicit)
2025-07-11 09:09:05,755 INFO sqlalchemy.engine.Engine SELECT hardware_fingerprints.employee_id, hardware_fingerprints.fingerprint, hardware_fingerprints.registration_date 
FROM hardware_fingerprints 
WHERE hardware_fingerprints.employee_id = $1::VARCHAR
2025-07-11 09:09:05,755 INFO sqlalchemy.engine.Engine [cached since 1.474e+05s ago] ('215829',)
2025-07-11 09:09:05,758 INFO sqlalchemy.engine.Engine ROLLBACK
INFO:     127.0.0.1:0 - "POST /api/hardware/verify HTTP/1.0" 200 OK
INFO:     127.0.0.1:0 - "POST /login HTTP/1.0" 200 OK
INFO:     127.0.0.1:48636 - "POST /login HTTP/1.1" 200 OK
INFO:     127.0.0.1:48652 - "GET /api/program/permissions/215829 HTTP/1.1" 200 OK
INFO:     127.0.0.1:48660 - "POST /api/program/authorize HTTP/1.1" 200 OK
INFO:     127.0.0.1:48662 - "GET /api/verify HTTP/1.1" 200 OK
INFO:     127.0.0.1:48678 - "GET /api/verify HTTP/1.1" 200 OK
2025-07-11 09:13:38,372 INFO sqlalchemy.engine.Engine BEGIN (implicit)
2025-07-11 09:13:38,373 INFO sqlalchemy.engine.Engine SELECT hardware_fingerprints.employee_id, hardware_fingerprints.fingerprint, hardware_fingerprints.registration_date 
FROM hardware_fingerprints 
WHERE hardware_fingerprints.employee_id = $1::VARCHAR
2025-07-11 09:13:38,373 INFO sqlalchemy.engine.Engine [cached since 1.476e+05s ago] ('215829',)
2025-07-11 09:13:38,374 INFO sqlalchemy.engine.Engine ROLLBACK
INFO:     127.0.0.1:0 - "POST /api/hardware/verify HTTP/1.0" 200 OK
INFO:     127.0.0.1:0 - "POST /login HTTP/1.0" 200 OK
INFO:     127.0.0.1:46486 - "POST /login HTTP/1.1" 200 OK
INFO:     127.0.0.1:46488 - "GET /api/program/permissions/215829 HTTP/1.1" 200 OK
INFO:     127.0.0.1:46500 - "POST /api/program/authorize HTTP/1.1" 200 OK
INFO:     127.0.0.1:46508 - "GET /api/verify HTTP/1.1" 200 OK
INFO:     127.0.0.1:46516 - "GET /api/verify HTTP/1.1" 200 OK
2025-07-11 10:21:28,875 INFO sqlalchemy.engine.Engine BEGIN (implicit)
2025-07-11 10:21:28,875 INFO sqlalchemy.engine.Engine SELECT hardware_fingerprints.employee_id, hardware_fingerprints.fingerprint, hardware_fingerprints.registration_date 
FROM hardware_fingerprints 
WHERE hardware_fingerprints.employee_id = $1::VARCHAR
2025-07-11 10:21:28,875 INFO sqlalchemy.engine.Engine [cached since 1.517e+05s ago] ('215829',)
2025-07-11 10:21:28,877 INFO sqlalchemy.engine.Engine ROLLBACK
INFO:     127.0.0.1:0 - "POST /api/hardware/verify HTTP/1.0" 200 OK
INFO:     127.0.0.1:0 - "POST /login HTTP/1.0" 200 OK
INFO:     127.0.0.1:47774 - "POST /login HTTP/1.1" 200 OK
INFO:     127.0.0.1:47776 - "GET /api/program/permissions/215829 HTTP/1.1" 200 OK
INFO:     127.0.0.1:47780 - "POST /api/program/authorize HTTP/1.1" 200 OK
INFO:     127.0.0.1:47792 - "GET /api/verify HTTP/1.1" 200 OK
INFO:     127.0.0.1:47802 - "GET /api/verify HTTP/1.1" 200 OK
2025-07-11 11:53:36,514 INFO sqlalchemy.engine.Engine BEGIN (implicit)
2025-07-11 11:53:36,514 INFO sqlalchemy.engine.Engine SELECT hardware_fingerprints.employee_id, hardware_fingerprints.fingerprint, hardware_fingerprints.registration_date 
FROM hardware_fingerprints 
WHERE hardware_fingerprints.employee_id = $1::VARCHAR
2025-07-11 11:53:36,514 INFO sqlalchemy.engine.Engine [cached since 1.572e+05s ago] ('215829',)
2025-07-11 11:53:36,518 INFO sqlalchemy.engine.Engine ROLLBACK
INFO:     127.0.0.1:0 - "POST /api/hardware/verify HTTP/1.0" 200 OK
INFO:     127.0.0.1:0 - "POST /login HTTP/1.0" 200 OK
INFO:     127.0.0.1:51398 - "POST /login HTTP/1.1" 200 OK
INFO:     127.0.0.1:51414 - "GET /api/program/permissions/215829 HTTP/1.1" 200 OK
INFO:     127.0.0.1:51418 - "POST /api/program/authorize HTTP/1.1" 200 OK
INFO:     127.0.0.1:51422 - "GET /api/verify HTTP/1.1" 200 OK
INFO:     127.0.0.1:51436 - "GET /api/verify HTTP/1.1" 200 OK
INFO:     127.0.0.1:56928 - "POST /api/program/authorize HTTP/1.1" 200 OK
INFO:     127.0.0.1:56930 - "GET /api/verify HTTP/1.1" 200 OK
INFO:     127.0.0.1:56942 - "GET /api/verify HTTP/1.1" 200 OK
2025-07-11 12:00:59,044 INFO sqlalchemy.engine.Engine BEGIN (implicit)
2025-07-11 12:00:59,044 INFO sqlalchemy.engine.Engine SELECT hardware_fingerprints.employee_id, hardware_fingerprints.fingerprint, hardware_fingerprints.registration_date 
FROM hardware_fingerprints 
WHERE hardware_fingerprints.employee_id = $1::VARCHAR
2025-07-11 12:00:59,044 INFO sqlalchemy.engine.Engine [cached since 1.577e+05s ago] ('215829',)
2025-07-11 12:00:59,046 INFO sqlalchemy.engine.Engine ROLLBACK
INFO:     127.0.0.1:0 - "POST /api/hardware/verify HTTP/1.0" 200 OK
INFO:     127.0.0.1:0 - "POST /login HTTP/1.0" 200 OK
INFO:     127.0.0.1:49654 - "POST /login HTTP/1.1" 200 OK
INFO:     127.0.0.1:49660 - "GET /api/program/permissions/215829 HTTP/1.1" 200 OK
INFO:     127.0.0.1:49664 - "POST /api/program/authorize HTTP/1.1" 200 OK
INFO:     127.0.0.1:49672 - "GET /api/verify HTTP/1.1" 200 OK
INFO:     127.0.0.1:49680 - "GET /api/verify HTTP/1.1" 200 OK
2025-07-11 12:01:41,887 INFO sqlalchemy.engine.Engine BEGIN (implicit)
2025-07-11 12:01:41,887 INFO sqlalchemy.engine.Engine SELECT hardware_fingerprints.employee_id, hardware_fingerprints.fingerprint, hardware_fingerprints.registration_date 
FROM hardware_fingerprints 
WHERE hardware_fingerprints.employee_id = $1::VARCHAR
2025-07-11 12:01:41,888 INFO sqlalchemy.engine.Engine [cached since 1.577e+05s ago] ('215829',)
2025-07-11 12:01:41,889 INFO sqlalchemy.engine.Engine ROLLBACK
INFO:     127.0.0.1:0 - "POST /api/hardware/verify HTTP/1.0" 200 OK
INFO:     127.0.0.1:0 - "POST /login HTTP/1.0" 200 OK
INFO:     127.0.0.1:36046 - "POST /login HTTP/1.1" 200 OK
INFO:     127.0.0.1:36048 - "GET /api/program/permissions/215829 HTTP/1.1" 200 OK
INFO:     127.0.0.1:36052 - "POST /api/program/authorize HTTP/1.1" 200 OK
INFO:     127.0.0.1:36064 - "GET /api/verify HTTP/1.1" 200 OK
INFO:     127.0.0.1:36072 - "GET /api/verify HTTP/1.1" 200 OK
2025-07-11 12:10:34,346 INFO sqlalchemy.engine.Engine BEGIN (implicit)
2025-07-11 12:10:34,346 INFO sqlalchemy.engine.Engine SELECT hardware_fingerprints.employee_id, hardware_fingerprints.fingerprint, hardware_fingerprints.registration_date 
FROM hardware_fingerprints 
WHERE hardware_fingerprints.employee_id = $1::VARCHAR
2025-07-11 12:10:34,347 INFO sqlalchemy.engine.Engine [cached since 1.582e+05s ago] ('215829',)
2025-07-11 12:10:34,348 INFO sqlalchemy.engine.Engine ROLLBACK
INFO:     127.0.0.1:0 - "POST /api/hardware/verify HTTP/1.0" 200 OK
INFO:     127.0.0.1:0 - "POST /login HTTP/1.0" 200 OK
INFO:     127.0.0.1:51788 - "POST /login HTTP/1.1" 200 OK
INFO:     127.0.0.1:51796 - "GET /api/program/permissions/215829 HTTP/1.1" 200 OK
INFO:     127.0.0.1:51802 - "POST /api/program/authorize HTTP/1.1" 200 OK
INFO:     127.0.0.1:51804 - "GET /api/verify HTTP/1.1" 200 OK
INFO:     127.0.0.1:51808 - "GET /api/verify HTTP/1.1" 200 OK
2025-07-11 12:12:34,174 INFO sqlalchemy.engine.Engine BEGIN (implicit)
2025-07-11 12:12:34,174 INFO sqlalchemy.engine.Engine SELECT hardware_fingerprints.employee_id, hardware_fingerprints.fingerprint, hardware_fingerprints.registration_date 
FROM hardware_fingerprints 
WHERE hardware_fingerprints.employee_id = $1::VARCHAR
2025-07-11 12:12:34,174 INFO sqlalchemy.engine.Engine [cached since 1.584e+05s ago] ('215829',)
2025-07-11 12:12:34,176 INFO sqlalchemy.engine.Engine ROLLBACK
INFO:     127.0.0.1:0 - "POST /api/hardware/verify HTTP/1.0" 200 OK
INFO:     127.0.0.1:0 - "POST /login HTTP/1.0" 200 OK
INFO:     127.0.0.1:46142 - "POST /login HTTP/1.1" 200 OK
INFO:     127.0.0.1:46158 - "GET /api/program/permissions/215829 HTTP/1.1" 200 OK
INFO:     127.0.0.1:46160 - "POST /api/program/authorize HTTP/1.1" 200 OK
INFO:     127.0.0.1:46176 - "GET /api/verify HTTP/1.1" 200 OK
INFO:     127.0.0.1:46186 - "GET /api/verify HTTP/1.1" 200 OK
2025-07-11 12:14:35,958 INFO sqlalchemy.engine.Engine BEGIN (implicit)
2025-07-11 12:14:35,959 INFO sqlalchemy.engine.Engine SELECT hardware_fingerprints.employee_id, hardware_fingerprints.fingerprint, hardware_fingerprints.registration_date 
FROM hardware_fingerprints 
WHERE hardware_fingerprints.employee_id = $1::VARCHAR
2025-07-11 12:14:35,959 INFO sqlalchemy.engine.Engine [cached since 1.585e+05s ago] ('215829',)
2025-07-11 12:14:35,960 INFO sqlalchemy.engine.Engine ROLLBACK
INFO:     127.0.0.1:0 - "POST /api/hardware/verify HTTP/1.0" 200 OK
INFO:     127.0.0.1:0 - "POST /login HTTP/1.0" 200 OK
INFO:     127.0.0.1:33376 - "POST /login HTTP/1.1" 200 OK
INFO:     127.0.0.1:33378 - "GET /api/program/permissions/215829 HTTP/1.1" 200 OK
INFO:     127.0.0.1:33386 - "POST /api/program/authorize HTTP/1.1" 200 OK
INFO:     127.0.0.1:33390 - "GET /api/verify HTTP/1.1" 200 OK
INFO:     127.0.0.1:33400 - "GET /api/verify HTTP/1.1" 200 OK
2025-07-11 13:03:39,302 INFO sqlalchemy.engine.Engine BEGIN (implicit)
2025-07-11 13:03:39,302 INFO sqlalchemy.engine.Engine SELECT hardware_fingerprints.employee_id, hardware_fingerprints.fingerprint, hardware_fingerprints.registration_date 
FROM hardware_fingerprints 
WHERE hardware_fingerprints.employee_id = $1::VARCHAR
2025-07-11 13:03:39,302 INFO sqlalchemy.engine.Engine [cached since 1.614e+05s ago] ('215829',)
2025-07-11 13:03:39,305 INFO sqlalchemy.engine.Engine ROLLBACK
INFO:     127.0.0.1:0 - "POST /api/hardware/verify HTTP/1.0" 200 OK
INFO:     127.0.0.1:0 - "POST /login HTTP/1.0" 200 OK
INFO:     127.0.0.1:46548 - "POST /login HTTP/1.1" 200 OK
INFO:     127.0.0.1:46554 - "GET /api/program/permissions/215829 HTTP/1.1" 200 OK
INFO:     127.0.0.1:46568 - "POST /api/program/authorize HTTP/1.1" 200 OK
INFO:     127.0.0.1:46570 - "GET /api/verify HTTP/1.1" 200 OK
INFO:     127.0.0.1:46586 - "GET /api/verify HTTP/1.1" 200 OK
INFO:     127.0.0.1:57474 - "GET /api/verify HTTP/1.1" 401 Unauthorized
2025-07-11 13:17:03,042 INFO sqlalchemy.engine.Engine BEGIN (implicit)
2025-07-11 13:17:03,042 INFO sqlalchemy.engine.Engine SELECT hardware_fingerprints.employee_id, hardware_fingerprints.fingerprint, hardware_fingerprints.registration_date 
FROM hardware_fingerprints 
WHERE hardware_fingerprints.employee_id = $1::VARCHAR
2025-07-11 13:17:03,042 INFO sqlalchemy.engine.Engine [cached since 1.622e+05s ago] ('215829',)
2025-07-11 13:17:03,043 INFO sqlalchemy.engine.Engine ROLLBACK
INFO:     127.0.0.1:0 - "POST /api/hardware/verify HTTP/1.0" 200 OK
INFO:     127.0.0.1:0 - "POST /login HTTP/1.0" 200 OK
INFO:     127.0.0.1:59990 - "POST /login HTTP/1.1" 200 OK
INFO:     127.0.0.1:60000 - "GET /api/program/permissions/215829 HTTP/1.1" 200 OK
INFO:     127.0.0.1:49210 - "POST /api/program/authorize HTTP/1.1" 200 OK
INFO:     127.0.0.1:49226 - "GET /api/verify HTTP/1.1" 200 OK
INFO:     127.0.0.1:49228 - "GET /api/verify HTTP/1.1" 200 OK
2025-07-11 13:19:24,345 INFO sqlalchemy.engine.Engine BEGIN (implicit)
2025-07-11 13:19:24,345 INFO sqlalchemy.engine.Engine SELECT hardware_fingerprints.employee_id, hardware_fingerprints.fingerprint, hardware_fingerprints.registration_date 
FROM hardware_fingerprints 
WHERE hardware_fingerprints.employee_id = $1::VARCHAR
2025-07-11 13:19:24,345 INFO sqlalchemy.engine.Engine [cached since 1.624e+05s ago] ('215829',)
2025-07-11 13:19:24,347 INFO sqlalchemy.engine.Engine ROLLBACK
INFO:     127.0.0.1:0 - "POST /api/hardware/verify HTTP/1.0" 200 OK
INFO:     127.0.0.1:0 - "POST /login HTTP/1.0" 200 OK
INFO:     127.0.0.1:34366 - "POST /login HTTP/1.1" 200 OK
INFO:     127.0.0.1:34372 - "GET /api/program/permissions/215829 HTTP/1.1" 200 OK
INFO:     127.0.0.1:34386 - "POST /api/program/authorize HTTP/1.1" 200 OK
INFO:     127.0.0.1:34390 - "GET /api/verify HTTP/1.1" 200 OK
INFO:     127.0.0.1:34392 - "GET /api/verify HTTP/1.1" 200 OK
2025-07-11 13:24:14,789 INFO sqlalchemy.engine.Engine BEGIN (implicit)
2025-07-11 13:24:14,789 INFO sqlalchemy.engine.Engine SELECT hardware_fingerprints.employee_id, hardware_fingerprints.fingerprint, hardware_fingerprints.registration_date 
FROM hardware_fingerprints 
WHERE hardware_fingerprints.employee_id = $1::VARCHAR
2025-07-11 13:24:14,789 INFO sqlalchemy.engine.Engine [cached since 1.627e+05s ago] ('215829',)
2025-07-11 13:24:14,791 INFO sqlalchemy.engine.Engine ROLLBACK
INFO:     127.0.0.1:0 - "POST /api/hardware/verify HTTP/1.0" 200 OK
INFO:     127.0.0.1:0 - "POST /login HTTP/1.0" 200 OK
INFO:     127.0.0.1:46588 - "POST /login HTTP/1.1" 200 OK
INFO:     127.0.0.1:46598 - "GET /api/program/permissions/215829 HTTP/1.1" 200 OK
INFO:     127.0.0.1:46602 - "POST /api/program/authorize HTTP/1.1" 200 OK
INFO:     127.0.0.1:46612 - "GET /api/verify HTTP/1.1" 200 OK
INFO:     127.0.0.1:46624 - "GET /api/verify HTTP/1.1" 200 OK
2025-07-11 13:27:52,157 INFO sqlalchemy.engine.Engine BEGIN (implicit)
2025-07-11 13:27:52,158 INFO sqlalchemy.engine.Engine SELECT hardware_fingerprints.employee_id, hardware_fingerprints.fingerprint, hardware_fingerprints.registration_date 
FROM hardware_fingerprints 
WHERE hardware_fingerprints.employee_id = $1::VARCHAR
2025-07-11 13:27:52,158 INFO sqlalchemy.engine.Engine [cached since 1.629e+05s ago] ('215829',)
2025-07-11 13:27:52,159 INFO sqlalchemy.engine.Engine ROLLBACK
INFO:     127.0.0.1:0 - "POST /api/hardware/verify HTTP/1.0" 200 OK
INFO:     127.0.0.1:0 - "POST /login HTTP/1.0" 200 OK
INFO:     127.0.0.1:42480 - "POST /login HTTP/1.1" 200 OK
INFO:     127.0.0.1:42494 - "GET /api/program/permissions/215829 HTTP/1.1" 200 OK
INFO:     127.0.0.1:42504 - "POST /api/program/authorize HTTP/1.1" 200 OK
INFO:     127.0.0.1:42518 - "GET /api/verify HTTP/1.1" 200 OK
INFO:     127.0.0.1:42520 - "GET /api/verify HTTP/1.1" 200 OK
2025-07-11 13:32:53,177 INFO sqlalchemy.engine.Engine BEGIN (implicit)
2025-07-11 13:32:53,177 INFO sqlalchemy.engine.Engine SELECT hardware_fingerprints.employee_id, hardware_fingerprints.fingerprint, hardware_fingerprints.registration_date 
FROM hardware_fingerprints 
WHERE hardware_fingerprints.employee_id = $1::VARCHAR
2025-07-11 13:32:53,177 INFO sqlalchemy.engine.Engine [cached since 1.632e+05s ago] ('215829',)
2025-07-11 13:32:53,180 INFO sqlalchemy.engine.Engine ROLLBACK
INFO:     127.0.0.1:0 - "POST /api/hardware/verify HTTP/1.0" 200 OK
INFO:     127.0.0.1:0 - "POST /login HTTP/1.0" 200 OK
INFO:     127.0.0.1:58240 - "POST /login HTTP/1.1" 200 OK
INFO:     127.0.0.1:58246 - "GET /api/program/permissions/215829 HTTP/1.1" 200 OK
INFO:     127.0.0.1:56024 - "POST /api/program/authorize HTTP/1.1" 200 OK
INFO:     127.0.0.1:56034 - "GET /api/verify HTTP/1.1" 200 OK
INFO:     127.0.0.1:56050 - "GET /api/verify HTTP/1.1" 200 OK
INFO:     127.0.0.1:44994 - "POST /api/program/authorize HTTP/1.1" 200 OK
INFO:     127.0.0.1:45002 - "GET /api/verify HTTP/1.1" 200 OK
INFO:     127.0.0.1:45008 - "GET /api/verify HTTP/1.1" 200 OK
2025-07-11 13:41:00,459 INFO sqlalchemy.engine.Engine BEGIN (implicit)
2025-07-11 13:41:00,460 INFO sqlalchemy.engine.Engine SELECT hardware_fingerprints.employee_id, hardware_fingerprints.fingerprint, hardware_fingerprints.registration_date 
FROM hardware_fingerprints 
WHERE hardware_fingerprints.employee_id = $1::VARCHAR
2025-07-11 13:41:00,460 INFO sqlalchemy.engine.Engine [cached since 1.637e+05s ago] ('215829',)
2025-07-11 13:41:00,464 INFO sqlalchemy.engine.Engine ROLLBACK
INFO:     127.0.0.1:0 - "POST /api/hardware/verify HTTP/1.0" 200 OK
INFO:     127.0.0.1:0 - "POST /login HTTP/1.0" 200 OK
INFO:     127.0.0.1:51948 - "POST /login HTTP/1.1" 200 OK
INFO:     127.0.0.1:51962 - "GET /api/program/permissions/215829 HTTP/1.1" 200 OK
INFO:     127.0.0.1:51970 - "POST /api/program/authorize HTTP/1.1" 200 OK
INFO:     127.0.0.1:51984 - "GET /api/verify HTTP/1.1" 200 OK
INFO:     127.0.0.1:51990 - "GET /api/verify HTTP/1.1" 200 OK
2025-07-11 13:44:51,809 INFO sqlalchemy.engine.Engine BEGIN (implicit)
2025-07-11 13:44:51,810 INFO sqlalchemy.engine.Engine SELECT hardware_fingerprints.employee_id, hardware_fingerprints.fingerprint, hardware_fingerprints.registration_date 
FROM hardware_fingerprints 
WHERE hardware_fingerprints.employee_id = $1::VARCHAR
2025-07-11 13:44:51,810 INFO sqlalchemy.engine.Engine [cached since 1.639e+05s ago] ('215829',)
2025-07-11 13:44:51,814 INFO sqlalchemy.engine.Engine ROLLBACK
INFO:     127.0.0.1:0 - "POST /api/hardware/verify HTTP/1.0" 200 OK
INFO:     127.0.0.1:0 - "POST /login HTTP/1.0" 200 OK
INFO:     127.0.0.1:36280 - "POST /login HTTP/1.1" 200 OK
INFO:     127.0.0.1:36294 - "GET /api/program/permissions/215829 HTTP/1.1" 200 OK
INFO:     127.0.0.1:36304 - "POST /api/program/authorize HTTP/1.1" 200 OK
INFO:     127.0.0.1:36318 - "GET /api/verify HTTP/1.1" 200 OK
INFO:     127.0.0.1:36328 - "GET /api/verify HTTP/1.1" 200 OK
2025-07-11 13:50:35,280 INFO sqlalchemy.engine.Engine BEGIN (implicit)
2025-07-11 13:50:35,280 INFO sqlalchemy.engine.Engine SELECT hardware_fingerprints.employee_id, hardware_fingerprints.fingerprint, hardware_fingerprints.registration_date 
FROM hardware_fingerprints 
WHERE hardware_fingerprints.employee_id = $1::VARCHAR
2025-07-11 13:50:35,281 INFO sqlalchemy.engine.Engine [cached since 1.642e+05s ago] ('215829',)
2025-07-11 13:50:35,284 INFO sqlalchemy.engine.Engine ROLLBACK
INFO:     127.0.0.1:0 - "POST /api/hardware/verify HTTP/1.0" 200 OK
INFO:     127.0.0.1:0 - "POST /login HTTP/1.0" 200 OK
INFO:     127.0.0.1:54336 - "POST /login HTTP/1.1" 200 OK
INFO:     127.0.0.1:54338 - "GET /api/program/permissions/215829 HTTP/1.1" 200 OK
INFO:     127.0.0.1:54344 - "POST /api/program/authorize HTTP/1.1" 200 OK
INFO:     127.0.0.1:54352 - "GET /api/verify HTTP/1.1" 200 OK
INFO:     127.0.0.1:54364 - "GET /api/verify HTTP/1.1" 200 OK
2025-07-11 13:56:23,014 INFO sqlalchemy.engine.Engine BEGIN (implicit)
2025-07-11 13:56:23,014 INFO sqlalchemy.engine.Engine SELECT hardware_fingerprints.employee_id, hardware_fingerprints.fingerprint, hardware_fingerprints.registration_date 
FROM hardware_fingerprints 
WHERE hardware_fingerprints.employee_id = $1::VARCHAR
2025-07-11 13:56:23,015 INFO sqlalchemy.engine.Engine [cached since 1.646e+05s ago] ('215829',)
2025-07-11 13:56:23,018 INFO sqlalchemy.engine.Engine ROLLBACK
INFO:     127.0.0.1:0 - "POST /api/hardware/verify HTTP/1.0" 200 OK
INFO:     127.0.0.1:0 - "POST /login HTTP/1.0" 200 OK
INFO:     127.0.0.1:40826 - "POST /login HTTP/1.1" 200 OK
INFO:     127.0.0.1:40840 - "GET /api/program/permissions/215829 HTTP/1.1" 200 OK
INFO:     127.0.0.1:58280 - "POST /api/program/authorize HTTP/1.1" 200 OK
INFO:     127.0.0.1:58294 - "GET /api/verify HTTP/1.1" 200 OK
INFO:     127.0.0.1:58306 - "GET /api/verify HTTP/1.1" 200 OK
INFO:     127.0.0.1:50422 - "POST /api/program/authorize HTTP/1.1" 200 OK
INFO:     127.0.0.1:50426 - "GET /api/verify HTTP/1.1" 200 OK
INFO:     127.0.0.1:50434 - "GET /api/verify HTTP/1.1" 200 OK
INFO:     127.0.0.1:52702 - "POST /api/program/authorize HTTP/1.1" 200 OK
INFO:     127.0.0.1:52706 - "GET /api/verify HTTP/1.1" 200 OK
INFO:     127.0.0.1:52712 - "GET /api/verify HTTP/1.1" 200 OK
