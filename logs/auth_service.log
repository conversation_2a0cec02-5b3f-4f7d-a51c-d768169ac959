INFO:     Will watch for changes in these directories: ['/home/<USER>/Documents/testF/2025/ser25/server0613/MySuiteDatebase2402newuigood-yolo5-3/server3/app']
INFO:     Uvicorn running on http://0.0.0.0:8006 (Press CTRL+C to quit)
INFO:     Started reloader process [2455637] using WatchFiles
============================================================
🚀 Starting MySuite Auth Microservice
============================================================
📁 Working Directory: /home/<USER>/Documents/testF/2025/ser25/server0613/MySuiteDatebase2402newuigood-yolo5-3/server3
🌐 Service Port: 8006
📝 API Documentation: http://localhost:8006/docs
🔍 Health Check: http://localhost:8006/health
============================================================
INFO:     Started server process [2455642]
INFO:     Waiting for application startup.
=== Auth Service Starting Up ===
2025-07-15 13:18:04,125 INFO sqlalchemy.engine.Engine select pg_catalog.version()
2025-07-15 13:18:04,125 INFO sqlalchemy.engine.Engine [raw sql] ()
2025-07-15 13:18:04,129 INFO sqlalchemy.engine.Engine select current_schema()
2025-07-15 13:18:04,129 INFO sqlalchemy.engine.Engine [raw sql] ()
2025-07-15 13:18:04,132 INFO sqlalchemy.engine.Engine show standard_conforming_strings
2025-07-15 13:18:04,132 INFO sqlalchemy.engine.Engine [raw sql] ()
2025-07-15 13:18:04,134 INFO sqlalchemy.engine.Engine BEGIN (implicit)
2025-07-15 13:18:04,137 INFO sqlalchemy.engine.Engine SELECT pg_catalog.pg_class.relname 
FROM pg_catalog.pg_class JOIN pg_catalog.pg_namespace ON pg_catalog.pg_namespace.oid = pg_catalog.pg_class.relnamespace 
WHERE pg_catalog.pg_class.relname = $1::VARCHAR AND pg_catalog.pg_class.relkind = ANY (ARRAY[$2::VARCHAR, $3::VARCHAR, $4::VARCHAR, $5::VARCHAR, $6::VARCHAR]) AND pg_catalog.pg_table_is_visible(pg_catalog.pg_class.oid) AND pg_catalog.pg_namespace.nspname != $7::VARCHAR
2025-07-15 13:18:04,137 INFO sqlalchemy.engine.Engine [generated in 0.00021s] ('users', 'r', 'p', 'f', 'v', 'm', 'pg_catalog')
2025-07-15 13:18:04,143 INFO sqlalchemy.engine.Engine SELECT pg_catalog.pg_class.relname 
FROM pg_catalog.pg_class JOIN pg_catalog.pg_namespace ON pg_catalog.pg_namespace.oid = pg_catalog.pg_class.relnamespace 
WHERE pg_catalog.pg_class.relname = $1::VARCHAR AND pg_catalog.pg_class.relkind = ANY (ARRAY[$2::VARCHAR, $3::VARCHAR, $4::VARCHAR, $5::VARCHAR, $6::VARCHAR]) AND pg_catalog.pg_table_is_visible(pg_catalog.pg_class.oid) AND pg_catalog.pg_namespace.nspname != $7::VARCHAR
2025-07-15 13:18:04,143 INFO sqlalchemy.engine.Engine [cached since 0.006872s ago] ('hardware_fingerprints', 'r', 'p', 'f', 'v', 'm', 'pg_catalog')
2025-07-15 13:18:04,144 INFO sqlalchemy.engine.Engine COMMIT
INFO:     Application startup complete.
✅ Database for auth service initialized!
🚀 Auth Service startup complete.
INFO:     127.0.0.1:37702 - "GET /health HTTP/1.1" 200 OK
INFO:     127.0.0.1:37706 - "GET /health HTTP/1.1" 200 OK
2025-07-15 13:18:29,464 INFO sqlalchemy.engine.Engine BEGIN (implicit)
2025-07-15 13:18:29,466 INFO sqlalchemy.engine.Engine SELECT hardware_fingerprints.employee_id, hardware_fingerprints.fingerprint, hardware_fingerprints.registration_date 
FROM hardware_fingerprints 
WHERE hardware_fingerprints.employee_id = $1::VARCHAR
2025-07-15 13:18:29,466 INFO sqlalchemy.engine.Engine [generated in 0.00011s] ('215829',)
2025-07-15 13:18:29,471 INFO sqlalchemy.engine.Engine ROLLBACK
INFO:     127.0.0.1:0 - "POST /api/hardware/verify HTTP/1.0" 200 OK
INFO:     127.0.0.1:0 - "POST /login HTTP/1.0" 200 OK
INFO:     127.0.0.1:39182 - "POST /login HTTP/1.1" 200 OK
INFO:     127.0.0.1:39192 - "GET /api/program/permissions/215829 HTTP/1.1" 200 OK
INFO:     127.0.0.1:39194 - "POST /api/program/authorize HTTP/1.1" 200 OK
INFO:     127.0.0.1:39202 - "GET /api/verify HTTP/1.1" 200 OK
INFO:     127.0.0.1:39214 - "GET /api/verify HTTP/1.1" 200 OK
2025-07-15 13:50:39,515 INFO sqlalchemy.engine.Engine BEGIN (implicit)
2025-07-15 13:50:39,516 INFO sqlalchemy.engine.Engine SELECT hardware_fingerprints.employee_id, hardware_fingerprints.fingerprint, hardware_fingerprints.registration_date 
FROM hardware_fingerprints 
WHERE hardware_fingerprints.employee_id = $1::VARCHAR
2025-07-15 13:50:39,516 INFO sqlalchemy.engine.Engine [cached since 1930s ago] ('215829',)
2025-07-15 13:50:39,520 INFO sqlalchemy.engine.Engine ROLLBACK
INFO:     127.0.0.1:0 - "POST /api/hardware/verify HTTP/1.0" 200 OK
INFO:     127.0.0.1:0 - "POST /login HTTP/1.0" 200 OK
INFO:     127.0.0.1:33930 - "POST /login HTTP/1.1" 200 OK
INFO:     127.0.0.1:33944 - "GET /api/program/permissions/215829 HTTP/1.1" 200 OK
INFO:     127.0.0.1:33952 - "POST /api/program/authorize HTTP/1.1" 200 OK
INFO:     127.0.0.1:33956 - "GET /api/verify HTTP/1.1" 200 OK
INFO:     127.0.0.1:33972 - "GET /api/verify HTTP/1.1" 200 OK
2025-07-15 13:51:47,977 INFO sqlalchemy.engine.Engine BEGIN (implicit)
2025-07-15 13:51:47,977 INFO sqlalchemy.engine.Engine SELECT hardware_fingerprints.employee_id, hardware_fingerprints.fingerprint, hardware_fingerprints.registration_date 
FROM hardware_fingerprints 
WHERE hardware_fingerprints.employee_id = $1::VARCHAR
2025-07-15 13:51:47,977 INFO sqlalchemy.engine.Engine [cached since 1999s ago] ('215829',)
2025-07-15 13:51:47,979 INFO sqlalchemy.engine.Engine ROLLBACK
INFO:     127.0.0.1:0 - "POST /api/hardware/verify HTTP/1.0" 200 OK
INFO:     127.0.0.1:0 - "POST /login HTTP/1.0" 200 OK
INFO:     127.0.0.1:46040 - "POST /login HTTP/1.1" 200 OK
INFO:     127.0.0.1:46042 - "GET /api/program/permissions/215829 HTTP/1.1" 200 OK
INFO:     127.0.0.1:46050 - "POST /api/program/authorize HTTP/1.1" 200 OK
INFO:     127.0.0.1:46054 - "GET /api/verify HTTP/1.1" 200 OK
INFO:     127.0.0.1:46058 - "GET /api/verify HTTP/1.1" 200 OK
2025-07-15 14:06:05,472 INFO sqlalchemy.engine.Engine BEGIN (implicit)
2025-07-15 14:06:05,472 INFO sqlalchemy.engine.Engine SELECT hardware_fingerprints.employee_id, hardware_fingerprints.fingerprint, hardware_fingerprints.registration_date 
FROM hardware_fingerprints 
WHERE hardware_fingerprints.employee_id = $1::VARCHAR
2025-07-15 14:06:05,472 INFO sqlalchemy.engine.Engine [cached since 2856s ago] ('215829',)
2025-07-15 14:06:05,474 INFO sqlalchemy.engine.Engine ROLLBACK
INFO:     127.0.0.1:0 - "POST /api/hardware/verify HTTP/1.0" 200 OK
INFO:     127.0.0.1:0 - "POST /login HTTP/1.0" 200 OK
INFO:     127.0.0.1:59848 - "POST /login HTTP/1.1" 200 OK
INFO:     127.0.0.1:59854 - "GET /api/program/permissions/215829 HTTP/1.1" 200 OK
INFO:     127.0.0.1:59868 - "POST /api/program/authorize HTTP/1.1" 200 OK
INFO:     127.0.0.1:59880 - "GET /api/verify HTTP/1.1" 200 OK
INFO:     127.0.0.1:59888 - "GET /api/verify HTTP/1.1" 200 OK
2025-07-15 14:15:16,196 INFO sqlalchemy.engine.Engine BEGIN (implicit)
2025-07-15 14:15:16,196 INFO sqlalchemy.engine.Engine SELECT hardware_fingerprints.employee_id, hardware_fingerprints.fingerprint, hardware_fingerprints.registration_date 
FROM hardware_fingerprints 
WHERE hardware_fingerprints.employee_id = $1::VARCHAR
2025-07-15 14:15:16,197 INFO sqlalchemy.engine.Engine [cached since 3407s ago] ('215829',)
2025-07-15 14:15:16,198 INFO sqlalchemy.engine.Engine ROLLBACK
INFO:     127.0.0.1:0 - "POST /api/hardware/verify HTTP/1.0" 200 OK
INFO:     127.0.0.1:0 - "POST /login HTTP/1.0" 200 OK
INFO:     127.0.0.1:48686 - "POST /login HTTP/1.1" 200 OK
INFO:     127.0.0.1:48698 - "GET /api/program/permissions/215829 HTTP/1.1" 200 OK
INFO:     127.0.0.1:48708 - "POST /api/program/authorize HTTP/1.1" 200 OK
INFO:     127.0.0.1:48724 - "GET /api/verify HTTP/1.1" 200 OK
INFO:     127.0.0.1:48738 - "GET /api/verify HTTP/1.1" 200 OK
2025-07-15 14:28:45,160 INFO sqlalchemy.engine.Engine BEGIN (implicit)
2025-07-15 14:28:45,160 INFO sqlalchemy.engine.Engine SELECT hardware_fingerprints.employee_id, hardware_fingerprints.fingerprint, hardware_fingerprints.registration_date 
FROM hardware_fingerprints 
WHERE hardware_fingerprints.employee_id = $1::VARCHAR
2025-07-15 14:28:45,160 INFO sqlalchemy.engine.Engine [cached since 4216s ago] ('215829',)
2025-07-15 14:28:45,162 INFO sqlalchemy.engine.Engine ROLLBACK
INFO:     127.0.0.1:0 - "POST /api/hardware/verify HTTP/1.0" 200 OK
INFO:     127.0.0.1:0 - "POST /login HTTP/1.0" 200 OK
INFO:     127.0.0.1:51156 - "POST /login HTTP/1.1" 200 OK
INFO:     127.0.0.1:51162 - "GET /api/program/permissions/215829 HTTP/1.1" 200 OK
INFO:     127.0.0.1:51168 - "POST /api/program/authorize HTTP/1.1" 200 OK
INFO:     127.0.0.1:51170 - "GET /api/verify HTTP/1.1" 200 OK
INFO:     127.0.0.1:51186 - "GET /api/verify HTTP/1.1" 200 OK
2025-07-15 14:35:13,474 INFO sqlalchemy.engine.Engine BEGIN (implicit)
2025-07-15 14:35:13,475 INFO sqlalchemy.engine.Engine SELECT hardware_fingerprints.employee_id, hardware_fingerprints.fingerprint, hardware_fingerprints.registration_date 
FROM hardware_fingerprints 
WHERE hardware_fingerprints.employee_id = $1::VARCHAR
2025-07-15 14:35:13,475 INFO sqlalchemy.engine.Engine [cached since 4604s ago] ('215829',)
2025-07-15 14:35:13,478 INFO sqlalchemy.engine.Engine ROLLBACK
INFO:     127.0.0.1:0 - "POST /api/hardware/verify HTTP/1.0" 200 OK
INFO:     127.0.0.1:0 - "POST /login HTTP/1.0" 200 OK
INFO:     127.0.0.1:51534 - "POST /login HTTP/1.1" 200 OK
INFO:     127.0.0.1:51536 - "GET /api/program/permissions/215829 HTTP/1.1" 200 OK
INFO:     127.0.0.1:51538 - "POST /api/program/authorize HTTP/1.1" 200 OK
INFO:     127.0.0.1:51546 - "GET /api/verify HTTP/1.1" 200 OK
INFO:     127.0.0.1:51554 - "GET /api/verify HTTP/1.1" 200 OK
INFO:     127.0.0.1:53282 - "POST /api/program/authorize HTTP/1.1" 200 OK
INFO:     127.0.0.1:53290 - "GET /api/verify HTTP/1.1" 200 OK
INFO:     127.0.0.1:53304 - "GET /api/verify HTTP/1.1" 200 OK
2025-07-15 16:12:31,297 INFO sqlalchemy.engine.Engine BEGIN (implicit)
2025-07-15 16:12:31,297 INFO sqlalchemy.engine.Engine SELECT hardware_fingerprints.employee_id, hardware_fingerprints.fingerprint, hardware_fingerprints.registration_date 
FROM hardware_fingerprints 
WHERE hardware_fingerprints.employee_id = $1::VARCHAR
2025-07-15 16:12:31,297 INFO sqlalchemy.engine.Engine [cached since 1.044e+04s ago] ('215829',)
2025-07-15 16:12:31,302 INFO sqlalchemy.engine.Engine ROLLBACK
INFO:     127.0.0.1:0 - "POST /api/hardware/verify HTTP/1.0" 200 OK
INFO:     127.0.0.1:0 - "POST /login HTTP/1.0" 200 OK
INFO:     127.0.0.1:36068 - "POST /login HTTP/1.1" 200 OK
INFO:     127.0.0.1:36074 - "GET /api/program/permissions/215829 HTTP/1.1" 200 OK
INFO:     127.0.0.1:40262 - "POST /api/program/authorize HTTP/1.1" 200 OK
INFO:     127.0.0.1:40270 - "GET /api/verify HTTP/1.1" 200 OK
INFO:     127.0.0.1:40280 - "GET /api/verify HTTP/1.1" 200 OK
2025-07-15 16:23:14,053 INFO sqlalchemy.engine.Engine BEGIN (implicit)
2025-07-15 16:23:14,054 INFO sqlalchemy.engine.Engine SELECT hardware_fingerprints.employee_id, hardware_fingerprints.fingerprint, hardware_fingerprints.registration_date 
FROM hardware_fingerprints 
WHERE hardware_fingerprints.employee_id = $1::VARCHAR
2025-07-15 16:23:14,054 INFO sqlalchemy.engine.Engine [cached since 1.108e+04s ago] ('215829',)
2025-07-15 16:23:14,056 INFO sqlalchemy.engine.Engine ROLLBACK
INFO:     127.0.0.1:0 - "POST /api/hardware/verify HTTP/1.0" 200 OK
INFO:     127.0.0.1:0 - "POST /login HTTP/1.0" 200 OK
INFO:     127.0.0.1:41300 - "POST /login HTTP/1.1" 200 OK
INFO:     127.0.0.1:41306 - "GET /api/program/permissions/215829 HTTP/1.1" 200 OK
INFO:     127.0.0.1:41308 - "POST /api/program/authorize HTTP/1.1" 200 OK
INFO:     127.0.0.1:41318 - "GET /api/verify HTTP/1.1" 200 OK
INFO:     127.0.0.1:41334 - "GET /api/verify HTTP/1.1" 200 OK
INFO:     127.0.0.1:33288 - "POST /api/program/authorize HTTP/1.1" 200 OK
INFO:     127.0.0.1:33294 - "GET /api/verify HTTP/1.1" 200 OK
INFO:     127.0.0.1:33310 - "GET /api/verify HTTP/1.1" 200 OK
2025-07-15 16:49:25,831 INFO sqlalchemy.engine.Engine BEGIN (implicit)
2025-07-15 16:49:25,832 INFO sqlalchemy.engine.Engine SELECT hardware_fingerprints.employee_id, hardware_fingerprints.fingerprint, hardware_fingerprints.registration_date 
FROM hardware_fingerprints 
WHERE hardware_fingerprints.employee_id = $1::VARCHAR
2025-07-15 16:49:25,832 INFO sqlalchemy.engine.Engine [cached since 1.266e+04s ago] ('215829',)
2025-07-15 16:49:25,835 INFO sqlalchemy.engine.Engine ROLLBACK
INFO:     127.0.0.1:0 - "POST /api/hardware/verify HTTP/1.0" 200 OK
INFO:     127.0.0.1:0 - "POST /login HTTP/1.0" 200 OK
INFO:     127.0.0.1:44318 - "POST /login HTTP/1.1" 200 OK
INFO:     127.0.0.1:44332 - "GET /api/program/permissions/215829 HTTP/1.1" 200 OK
INFO:     127.0.0.1:44346 - "POST /api/program/authorize HTTP/1.1" 200 OK
INFO:     127.0.0.1:44348 - "GET /api/verify HTTP/1.1" 200 OK
INFO:     127.0.0.1:44358 - "GET /api/verify HTTP/1.1" 200 OK
INFO:     127.0.0.1:54978 - "POST /api/program/authorize HTTP/1.1" 200 OK
INFO:     127.0.0.1:54986 - "GET /api/verify HTTP/1.1" 200 OK
INFO:     127.0.0.1:54992 - "GET /api/verify HTTP/1.1" 200 OK
2025-07-15 17:00:13,127 INFO sqlalchemy.engine.Engine BEGIN (implicit)
2025-07-15 17:00:13,128 INFO sqlalchemy.engine.Engine SELECT hardware_fingerprints.employee_id, hardware_fingerprints.fingerprint, hardware_fingerprints.registration_date 
FROM hardware_fingerprints 
WHERE hardware_fingerprints.employee_id = $1::VARCHAR
2025-07-15 17:00:13,128 INFO sqlalchemy.engine.Engine [cached since 1.33e+04s ago] ('215829',)
2025-07-15 17:00:13,129 INFO sqlalchemy.engine.Engine ROLLBACK
INFO:     127.0.0.1:0 - "POST /api/hardware/verify HTTP/1.0" 200 OK
INFO:     127.0.0.1:0 - "POST /login HTTP/1.0" 200 OK
INFO:     127.0.0.1:42234 - "POST /login HTTP/1.1" 200 OK
INFO:     127.0.0.1:42246 - "GET /api/program/permissions/215829 HTTP/1.1" 200 OK
INFO:     127.0.0.1:55828 - "POST /api/program/authorize HTTP/1.1" 200 OK
INFO:     127.0.0.1:55832 - "GET /api/verify HTTP/1.1" 200 OK
INFO:     127.0.0.1:55848 - "GET /api/verify HTTP/1.1" 200 OK
2025-07-15 17:08:14,061 INFO sqlalchemy.engine.Engine BEGIN (implicit)
2025-07-15 17:08:14,062 INFO sqlalchemy.engine.Engine SELECT hardware_fingerprints.employee_id, hardware_fingerprints.fingerprint, hardware_fingerprints.registration_date 
FROM hardware_fingerprints 
WHERE hardware_fingerprints.employee_id = $1::VARCHAR
2025-07-15 17:08:14,062 INFO sqlalchemy.engine.Engine [cached since 1.378e+04s ago] ('215829',)
2025-07-15 17:08:14,064 INFO sqlalchemy.engine.Engine ROLLBACK
INFO:     127.0.0.1:0 - "POST /api/hardware/verify HTTP/1.0" 200 OK
INFO:     127.0.0.1:0 - "POST /login HTTP/1.0" 200 OK
INFO:     127.0.0.1:44696 - "POST /login HTTP/1.1" 200 OK
INFO:     127.0.0.1:44710 - "GET /api/program/permissions/215829 HTTP/1.1" 200 OK
INFO:     127.0.0.1:44724 - "POST /api/program/authorize HTTP/1.1" 200 OK
INFO:     127.0.0.1:44738 - "GET /api/verify HTTP/1.1" 200 OK
INFO:     127.0.0.1:44746 - "GET /api/verify HTTP/1.1" 200 OK
2025-07-15 17:10:42,142 INFO sqlalchemy.engine.Engine BEGIN (implicit)
2025-07-15 17:10:42,143 INFO sqlalchemy.engine.Engine SELECT hardware_fingerprints.employee_id, hardware_fingerprints.fingerprint, hardware_fingerprints.registration_date 
FROM hardware_fingerprints 
WHERE hardware_fingerprints.employee_id = $1::VARCHAR
2025-07-15 17:10:42,143 INFO sqlalchemy.engine.Engine [cached since 1.393e+04s ago] ('215829',)
2025-07-15 17:10:42,147 INFO sqlalchemy.engine.Engine ROLLBACK
INFO:     127.0.0.1:0 - "POST /api/hardware/verify HTTP/1.0" 200 OK
INFO:     127.0.0.1:0 - "POST /login HTTP/1.0" 200 OK
INFO:     127.0.0.1:60158 - "POST /login HTTP/1.1" 200 OK
INFO:     127.0.0.1:60166 - "GET /api/program/permissions/215829 HTTP/1.1" 200 OK
INFO:     127.0.0.1:60182 - "POST /api/program/authorize HTTP/1.1" 200 OK
INFO:     127.0.0.1:60188 - "GET /api/verify HTTP/1.1" 200 OK
INFO:     127.0.0.1:60204 - "GET /api/verify HTTP/1.1" 200 OK
INFO:     127.0.0.1:50296 - "POST /api/program/authorize HTTP/1.1" 200 OK
INFO:     127.0.0.1:50300 - "GET /api/verify HTTP/1.1" 200 OK
INFO:     127.0.0.1:50316 - "GET /api/verify HTTP/1.1" 200 OK
2025-07-15 17:14:46,420 INFO sqlalchemy.engine.Engine BEGIN (implicit)
2025-07-15 17:14:46,420 INFO sqlalchemy.engine.Engine SELECT hardware_fingerprints.employee_id, hardware_fingerprints.fingerprint, hardware_fingerprints.registration_date 
FROM hardware_fingerprints 
WHERE hardware_fingerprints.employee_id = $1::VARCHAR
2025-07-15 17:14:46,420 INFO sqlalchemy.engine.Engine [cached since 1.418e+04s ago] ('215829',)
2025-07-15 17:14:46,422 INFO sqlalchemy.engine.Engine ROLLBACK
INFO:     127.0.0.1:0 - "POST /api/hardware/verify HTTP/1.0" 200 OK
INFO:     127.0.0.1:0 - "POST /login HTTP/1.0" 200 OK
INFO:     127.0.0.1:59106 - "POST /login HTTP/1.1" 200 OK
INFO:     127.0.0.1:59120 - "GET /api/program/permissions/215829 HTTP/1.1" 200 OK
INFO:     127.0.0.1:59130 - "POST /api/program/authorize HTTP/1.1" 200 OK
INFO:     127.0.0.1:59144 - "GET /api/verify HTTP/1.1" 200 OK
INFO:     127.0.0.1:59150 - "GET /api/verify HTTP/1.1" 200 OK
2025-07-15 17:21:59,061 INFO sqlalchemy.engine.Engine BEGIN (implicit)
2025-07-15 17:21:59,062 INFO sqlalchemy.engine.Engine SELECT hardware_fingerprints.employee_id, hardware_fingerprints.fingerprint, hardware_fingerprints.registration_date 
FROM hardware_fingerprints 
WHERE hardware_fingerprints.employee_id = $1::VARCHAR
2025-07-15 17:21:59,062 INFO sqlalchemy.engine.Engine [cached since 1.461e+04s ago] ('215829',)
2025-07-15 17:21:59,066 INFO sqlalchemy.engine.Engine ROLLBACK
INFO:     127.0.0.1:0 - "POST /api/hardware/verify HTTP/1.0" 200 OK
INFO:     127.0.0.1:0 - "POST /login HTTP/1.0" 200 OK
INFO:     127.0.0.1:43936 - "POST /login HTTP/1.1" 200 OK
INFO:     127.0.0.1:43940 - "GET /api/program/permissions/215829 HTTP/1.1" 200 OK
INFO:     127.0.0.1:43948 - "POST /api/program/authorize HTTP/1.1" 200 OK
INFO:     127.0.0.1:43962 - "GET /api/verify HTTP/1.1" 200 OK
INFO:     127.0.0.1:43968 - "GET /api/verify HTTP/1.1" 200 OK
INFO:     127.0.0.1:53074 - "POST /api/program/authorize HTTP/1.1" 200 OK
INFO:     127.0.0.1:53082 - "GET /api/verify HTTP/1.1" 200 OK
INFO:     127.0.0.1:53090 - "GET /api/verify HTTP/1.1" 200 OK
2025-07-16 07:48:05,983 INFO sqlalchemy.engine.Engine BEGIN (implicit)
2025-07-16 07:48:05,983 INFO sqlalchemy.engine.Engine SELECT hardware_fingerprints.employee_id, hardware_fingerprints.fingerprint, hardware_fingerprints.registration_date 
FROM hardware_fingerprints 
WHERE hardware_fingerprints.employee_id = $1::VARCHAR
2025-07-16 07:48:05,983 INFO sqlalchemy.engine.Engine [cached since 6.658e+04s ago] ('215829',)
2025-07-16 07:48:05,986 INFO sqlalchemy.engine.Engine ROLLBACK
INFO:     127.0.0.1:0 - "POST /api/hardware/verify HTTP/1.0" 200 OK
INFO:     127.0.0.1:0 - "POST /login HTTP/1.0" 200 OK
INFO:     127.0.0.1:54764 - "POST /login HTTP/1.1" 200 OK
INFO:     127.0.0.1:54774 - "GET /api/program/permissions/215829 HTTP/1.1" 200 OK
INFO:     127.0.0.1:54784 - "POST /api/program/authorize HTTP/1.1" 200 OK
INFO:     127.0.0.1:54798 - "GET /api/verify HTTP/1.1" 200 OK
INFO:     127.0.0.1:54804 - "GET /api/verify HTTP/1.1" 200 OK
