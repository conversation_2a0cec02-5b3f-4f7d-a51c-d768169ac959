INFO:     Started server process [1841685]
INFO:     Waiting for application startup.
2025-07-09 16:12:49,015 - app.main - INFO - Starting MySuite Video Monitoring Microservice...
2025-07-09 16:12:49,015 - app.core.services.video_service - INFO - Initializing video service with camera index: 1
2025-07-09 16:12:49,015 - app.core.services.video_service - INFO - Trying backend: 200
[ WARN:0@1.089] global cap_v4l.cpp:913 open VIDEOIO(V4L2:/dev/video1): can't open camera by index
[ WARN:0@1.089] global cap.cpp:478 open VIDEOIO(V4L2): backend is generally available but can't be used to capture by index
2025-07-09 16:12:49,015 - app.core.services.video_service - ERROR - Failed to open camera with backend 200
2025-07-09 16:12:49,015 - app.core.services.video_service - INFO - Trying backend: 1800
2025-07-09 16:12:49,019 - app.core.services.video_service - ERROR - Failed to open camera with backend 1800
2025-07-09 16:12:49,019 - app.core.services.video_service - INFO - Trying backend: 0
[ WARN:0@1.092] global cap_v4l.cpp:913 open VIDEOIO(V4L2:/dev/video1): can't open camera by index
2025-07-09 16:12:49,021 - app.core.services.video_service - ERROR - Failed to open camera with backend 0
2025-07-09 16:12:49,021 - app.core.services.video_service - ERROR - All camera backends failed
2025-07-09 16:12:49,022 - app.core.services.video_service - ERROR - Failed to initialize video service
2025-07-09 16:12:49,022 - app.main - WARNING - Failed to initialize video service - camera may not be available
2025-07-09 16:12:49,022 - app.main - INFO - Background tasks started
2025-07-09 16:12:49,022 - app.main - INFO - Video Monitoring Microservice started successfully on port 8007
2025-07-09 16:12:49,022 - app.routers.video_websocket - INFO - Starting video broadcast task
INFO:     Application startup complete.
INFO:     Uvicorn running on http://0.0.0.0:8007 (Press CTRL+C to quit)
Video Monitoring Service Configuration validated successfully
Service will run on port: 8007
Camera index: 1
Video resolution: 640x480
Starting MySuite Video Monitoring Microservice...
Version: 1.0.0
Host: 0.0.0.0
Port: 8007
Camera Index: 1
Video Resolution: 640x480
Video FPS: 30
--------------------------------------------------
INFO:     127.0.0.1:46662 - "GET /health HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:56908 - "WebSocket /ws/video" [accepted]
2025-07-10 17:34:38,828 - app.core.services.video_service - INFO - Client 333a235c-f64f-48f1-a794-9298552e0db6 connected. Total clients: 1
2025-07-10 17:34:38,828 - app.routers.video_websocket - INFO - Video WebSocket client 333a235c-f64f-48f1-a794-9298552e0db6 connected
INFO:     connection open
INFO:     127.0.0.1:56920 - "GET /api/video/yolo/status HTTP/1.1" 200 OK
2025-07-10 17:34:41,796 - app.routers.video_api - INFO - Restarting camera via API request
2025-07-10 17:34:41,796 - app.core.services.video_service - INFO - Restarting camera...
2025-07-10 17:34:41,796 - app.core.services.video_service - INFO - Video capture stopped
2025-07-10 17:34:41,796 - app.core.services.video_service - INFO - Initializing video service with camera index: 1
2025-07-10 17:34:41,796 - app.core.services.video_service - INFO - Trying backend: 200
[ WARN:0@91313.869] global cap_v4l.cpp:913 open VIDEOIO(V4L2:/dev/video1): can't open camera by index
[ WARN:0@91313.869] global cap.cpp:478 open VIDEOIO(V4L2): backend is generally available but can't be used to capture by index
2025-07-10 17:34:41,796 - app.core.services.video_service - ERROR - Failed to open camera with backend 200
2025-07-10 17:34:41,796 - app.core.services.video_service - INFO - Trying backend: 1800
2025-07-10 17:34:41,796 - app.core.services.video_service - ERROR - Failed to open camera with backend 1800
2025-07-10 17:34:41,796 - app.core.services.video_service - INFO - Trying backend: 0
[ WARN:0@91313.869] global cap_v4l.cpp:913 open VIDEOIO(V4L2:/dev/video1): can't open camera by index
2025-07-10 17:34:41,796 - app.core.services.video_service - ERROR - Failed to open camera with backend 0
2025-07-10 17:34:41,796 - app.core.services.video_service - ERROR - All camera backends failed
2025-07-10 17:34:41,796 - app.core.services.video_service - ERROR - Failed to initialize video service
2025-07-10 17:34:41,796 - app.core.services.video_service - ERROR - Failed to restart camera
INFO:     127.0.0.1:56936 - "POST /api/video/camera/restart HTTP/1.1" 200 OK
2025-07-10 17:34:53,973 - app.core.services.video_service - INFO - Client 333a235c-f64f-48f1-a794-9298552e0db6 disconnected. Total clients: 0
2025-07-10 17:34:53,973 - app.routers.video_websocket - INFO - Video WebSocket client 333a235c-f64f-48f1-a794-9298552e0db6 disconnected
INFO:     connection closed
