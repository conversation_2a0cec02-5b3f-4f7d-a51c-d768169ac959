INFO:     Started server process [2455736]
INFO:     Waiting for application startup.
2025-07-15 13:18:07,885 - app.main - INFO - Starting MySuite Video Monitoring Microservice...
2025-07-15 13:18:07,885 - app.core.services.video_service - INFO - Initializing video service with camera index: 1
2025-07-15 13:18:07,885 - app.core.services.video_service - INFO - Trying backend: 200
[ WARN:0@1.069] global cap_v4l.cpp:913 open VIDEOIO(V4L2:/dev/video1): can't open camera by index
[ WARN:0@1.069] global cap.cpp:478 open VIDEOIO(V4L2): backend is generally available but can't be used to capture by index
2025-07-15 13:18:07,885 - app.core.services.video_service - ERROR - Failed to open camera with backend 200
2025-07-15 13:18:07,885 - app.core.services.video_service - INFO - Trying backend: 1800
2025-07-15 13:18:07,888 - app.core.services.video_service - ERROR - Failed to open camera with backend 1800
2025-07-15 13:18:07,889 - app.core.services.video_service - INFO - Trying backend: 0
[ WARN:0@1.072] global cap_v4l.cpp:913 open VIDEOIO(V4L2:/dev/video1): can't open camera by index
2025-07-15 13:18:07,892 - app.core.services.video_service - ERROR - Failed to open camera with backend 0
2025-07-15 13:18:07,893 - app.core.services.video_service - ERROR - All camera backends failed
2025-07-15 13:18:07,893 - app.core.services.video_service - ERROR - Failed to initialize video service
2025-07-15 13:18:07,893 - app.main - WARNING - Failed to initialize video service - camera may not be available
2025-07-15 13:18:07,893 - app.main - INFO - Background tasks started
2025-07-15 13:18:07,893 - app.main - INFO - Video Monitoring Microservice started successfully on port 8007
2025-07-15 13:18:07,893 - app.routers.video_websocket - INFO - Starting video broadcast task
INFO:     Application startup complete.
INFO:     Uvicorn running on http://0.0.0.0:8007 (Press CTRL+C to quit)
Video Monitoring Service Configuration validated successfully
Service will run on port: 8007
Camera index: 1
Video resolution: 640x480
Starting MySuite Video Monitoring Microservice...
Version: 1.0.0
Host: 0.0.0.0
Port: 8007
Camera Index: 1
Video Resolution: 640x480
Video FPS: 30
--------------------------------------------------
INFO:     127.0.0.1:41336 - "GET /health HTTP/1.1" 404 Not Found
