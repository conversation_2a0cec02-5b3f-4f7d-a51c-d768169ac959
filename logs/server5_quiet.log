2025-06-27 11:37:40,246 - ERROR - Server5启动失败: 'ConfigSelector' object has no attribute 'get_config'
Traceback (most recent call last):
  File "/home/<USER>/Documents/testF/2025/ser25/server0613/MySuiteDatebase2402newuigood-yolo5-3/server5/start_ubuntu_remote_quiet.py", line 48, in start_server5_quiet
    config = config_selector.get_config()
             ^^^^^^^^^^^^^^^^^^^^^^^^^^
AttributeError: 'ConfigSelector' object has no attribute 'get_config'. Did you mean: 'load_config'?
2025-06-27 11:37:40,246 - __main__ - ERROR - Server5启动失败: 'ConfigSelector' object has no attribute 'get_config'
Traceback (most recent call last):
  File "/home/<USER>/Documents/testF/2025/ser25/server0613/MySuiteDatebase2402newuigood-yolo5-3/server5/start_ubuntu_remote_quiet.py", line 48, in start_server5_quiet
    config = config_selector.get_config()
             ^^^^^^^^^^^^^^^^^^^^^^^^^^
AttributeError: 'ConfigSelector' object has no attribute 'get_config'. Did you mean: 'load_config'?
2025-06-27 11:48:38,266 - ERROR - Server5启动失败: 'ConfigSelector' object has no attribute 'get_config'
Traceback (most recent call last):
  File "/home/<USER>/Documents/testF/2025/ser25/server0613/MySuiteDatebase2402newuigood-yolo5-3/server5/start_ubuntu_remote_quiet.py", line 48, in start_server5_quiet
    config = config_selector.get_config()
             ^^^^^^^^^^^^^^^^^^^^^^^^^^
AttributeError: 'ConfigSelector' object has no attribute 'get_config'. Did you mean: 'load_config'?
2025-06-27 11:48:38,266 - __main__ - ERROR - Server5启动失败: 'ConfigSelector' object has no attribute 'get_config'
Traceback (most recent call last):
  File "/home/<USER>/Documents/testF/2025/ser25/server0613/MySuiteDatebase2402newuigood-yolo5-3/server5/start_ubuntu_remote_quiet.py", line 48, in start_server5_quiet
    config = config_selector.get_config()
             ^^^^^^^^^^^^^^^^^^^^^^^^^^
AttributeError: 'ConfigSelector' object has no attribute 'get_config'. Did you mean: 'load_config'?
2025-06-27 12:06:37,928 - ERROR - Server5启动失败: 'ConfigSelector' object has no attribute 'get_config'
Traceback (most recent call last):
  File "/home/<USER>/Documents/testF/2025/ser25/server0613/MySuiteDatebase2402newuigood-yolo5-3/server5/start_ubuntu_remote_quiet.py", line 48, in start_server5_quiet
    config = config_selector.get_config()
             ^^^^^^^^^^^^^^^^^^^^^^^^^^
AttributeError: 'ConfigSelector' object has no attribute 'get_config'. Did you mean: 'load_config'?
2025-06-27 12:06:37,928 - __main__ - ERROR - Server5启动失败: 'ConfigSelector' object has no attribute 'get_config'
Traceback (most recent call last):
  File "/home/<USER>/Documents/testF/2025/ser25/server0613/MySuiteDatebase2402newuigood-yolo5-3/server5/start_ubuntu_remote_quiet.py", line 48, in start_server5_quiet
    config = config_selector.get_config()
             ^^^^^^^^^^^^^^^^^^^^^^^^^^
AttributeError: 'ConfigSelector' object has no attribute 'get_config'. Did you mean: 'load_config'?
2025-06-27 12:30:18,982 - ERROR - 监听通知时出错: 'Connection' object has no attribute 'connection'
2025-06-27 12:30:18,982 - app.database.postgresql_client - ERROR - 监听通知时出错: 'Connection' object has no attribute 'connection'
2025-06-27 12:30:18,994 - ERROR - 命令执行失败: 
                CREATE TABLE IF NOT EXISTS entries_202506 
                PARTITION OF entries
   ... 错误: パーティション"entries_202506"はパーティション"entries_2025_06"と重複があります
2025-06-27 12:30:18,994 - app.database.postgresql_client - ERROR - 命令执行失败: 
                CREATE TABLE IF NOT EXISTS entries_202506 
                PARTITION OF entries
   ... 错误: パーティション"entries_202506"はパーティション"entries_2025_06"と重複があります
2025-06-27 12:30:18,995 - ERROR - ❌ 分区创建失败 202506: パーティション"entries_202506"はパーティション"entries_2025_06"と重複があります
2025-06-27 12:30:18,995 - app.database.postgresql_client - ERROR - ❌ 分区创建失败 202506: パーティション"entries_202506"はパーティション"entries_2025_06"と重複があります
2025-06-27 12:30:18,996 - ERROR - ❌ 创建分区失败 202506: パーティション"entries_202506"はパーティション"entries_2025_06"と重複があります
2025-06-27 12:30:18,996 - app.services.f1_listener - ERROR - ❌ 创建分区失败 202506: パーティション"entries_202506"はパーティション"entries_2025_06"と重複があります
2025-06-27 12:30:19,470 - ERROR - 单行查询失败: 行に対応するパーティションがリレーション"entries"に見つかりません
DETAIL:  失敗した行のパーティションキーは(entry_date) = (null)を含みます。
2025-06-27 12:30:19,470 - app.database.postgresql_client - ERROR - 单行查询失败: 行に対応するパーティションがリレーション"entries"に見つかりません
DETAIL:  失敗した行のパーティションキーは(entry_date) = (null)を含みます。
2025-06-27 12:30:19,471 - ERROR - ❌ 创建新记录失败: 行に対応するパーティションがリレーション"entries"に見つかりません
DETAIL:  失敗した行のパーティションキーは(entry_date) = (null)を含みます。
2025-06-27 12:30:19,471 - app.services.f3_data_puller - ERROR - ❌ 创建新记录失败: 行に対応するパーティションがリレーション"entries"に見つかりません
DETAIL:  失敗した行のパーティションキーは(entry_date) = (null)を含みます。
2025-06-27 12:30:19,471 - ERROR - ❌ 处理增量记录失败: 行に対応するパーティションがリレーション"entries"に見つかりません
DETAIL:  失敗した行のパーティションキーは(entry_date) = (null)を含みます。, 记录: {'id': 1, 'name': '张三', 'department': '技术部', 'position': '工程师', 'status': '在职'}
2025-06-27 12:30:19,471 - app.services.f3_data_puller - ERROR - ❌ 处理增量记录失败: 行に対応するパーティションがリレーション"entries"に見つかりません
DETAIL:  失敗した行のパーティションキーは(entry_date) = (null)を含みます。, 记录: {'id': 1, 'name': '张三', 'department': '技术部', 'position': '工程师', 'status': '在职'}
2025-06-27 12:30:19,474 - ERROR - 单行查询失败: 行に対応するパーティションがリレーション"entries"に見つかりません
DETAIL:  失敗した行のパーティションキーは(entry_date) = (null)を含みます。
2025-06-27 12:30:19,474 - app.database.postgresql_client - ERROR - 单行查询失败: 行に対応するパーティションがリレーション"entries"に見つかりません
DETAIL:  失敗した行のパーティションキーは(entry_date) = (null)を含みます。
2025-06-27 12:30:19,475 - ERROR - ❌ 创建新记录失败: 行に対応するパーティションがリレーション"entries"に見つかりません
DETAIL:  失敗した行のパーティションキーは(entry_date) = (null)を含みます。
2025-06-27 12:30:19,475 - app.services.f3_data_puller - ERROR - ❌ 创建新记录失败: 行に対応するパーティションがリレーション"entries"に見つかりません
DETAIL:  失敗した行のパーティションキーは(entry_date) = (null)を含みます。
2025-06-27 12:30:19,475 - ERROR - ❌ 处理增量记录失败: 行に対応するパーティションがリレーション"entries"に見つかりません
DETAIL:  失敗した行のパーティションキーは(entry_date) = (null)を含みます。, 记录: {'id': 2, 'name': '李四', 'department': '销售部', 'position': '销售员', 'status': '在职'}
2025-06-27 12:30:19,475 - app.services.f3_data_puller - ERROR - ❌ 处理增量记录失败: 行に対応するパーティションがリレーション"entries"に見つかりません
DETAIL:  失敗した行のパーティションキーは(entry_date) = (null)を含みます。, 记录: {'id': 2, 'name': '李四', 'department': '销售部', 'position': '销售员', 'status': '在职'}
2025-06-27 12:30:19,477 - ERROR - 单行查询失败: 行に対応するパーティションがリレーション"entries"に見つかりません
DETAIL:  失敗した行のパーティションキーは(entry_date) = (null)を含みます。
2025-06-27 12:30:19,477 - app.database.postgresql_client - ERROR - 单行查询失败: 行に対応するパーティションがリレーション"entries"に見つかりません
DETAIL:  失敗した行のパーティションキーは(entry_date) = (null)を含みます。
2025-06-27 12:30:19,478 - ERROR - ❌ 创建新记录失败: 行に対応するパーティションがリレーション"entries"に見つかりません
DETAIL:  失敗した行のパーティションキーは(entry_date) = (null)を含みます。
2025-06-27 12:30:19,478 - app.services.f3_data_puller - ERROR - ❌ 创建新记录失败: 行に対応するパーティションがリレーション"entries"に見つかりません
DETAIL:  失敗した行のパーティションキーは(entry_date) = (null)を含みます。
2025-06-27 12:30:19,478 - ERROR - ❌ 处理增量记录失败: 行に対応するパーティションがリレーション"entries"に見つかりません
DETAIL:  失敗した行のパーティションキーは(entry_date) = (null)を含みます。, 记录: {'id': 3, 'name': '王五', 'department': '人事部', 'position': 'HR', 'status': '离职'}
2025-06-27 12:30:19,478 - app.services.f3_data_puller - ERROR - ❌ 处理增量记录失败: 行に対応するパーティションがリレーション"entries"に見つかりません
DETAIL:  失敗した行のパーティションキーは(entry_date) = (null)を含みます。, 记录: {'id': 3, 'name': '王五', 'department': '人事部', 'position': 'HR', 'status': '离职'}
2025-06-27 12:30:19,480 - ERROR - ❌ 设置最后拉取时间失败 f3_data_puller: Invalid input of type: 'datetime'. Convert to a bytes, string, int or float first.
2025-06-27 12:30:19,480 - app.database.redis_client - ERROR - ❌ 设置最后拉取时间失败 f3_data_puller: Invalid input of type: 'datetime'. Convert to a bytes, string, int or float first.
2025-06-27 12:30:19,919 - ERROR - 查询执行失败: 
                SELECT * FROM entries 
                WHERE external_id IS NOT NULL 
             ... 错误: 列"updated_at"は存在しません
2025-06-27 12:30:19,919 - app.database.postgresql_client - ERROR - 查询执行失败: 
                SELECT * FROM entries 
                WHERE external_id IS NOT NULL 
             ... 错误: 列"updated_at"は存在しません
2025-06-27 12:30:19,920 - ERROR - ❌ 数据一致性检查失败: 列"updated_at"は存在しません
2025-06-27 12:30:19,920 - app.services.f5_bulk_sync - ERROR - ❌ 数据一致性检查失败: 列"updated_at"は存在しません
2025-06-27 12:32:15,299 - ERROR - ❌ 设置最后拉取时间失败 f3_data_puller: Invalid input of type: 'datetime'. Convert to a bytes, string, int or float first.
2025-06-27 12:32:15,299 - app.database.redis_client - ERROR - ❌ 设置最后拉取时间失败 f3_data_puller: Invalid input of type: 'datetime'. Convert to a bytes, string, int or float first.
2025-06-27 12:32:19,888 - ERROR - 监听通知时出错: 'Connection' object has no attribute 'connection'
2025-06-27 12:32:19,888 - app.database.postgresql_client - ERROR - 监听通知时出错: 'Connection' object has no attribute 'connection'
2025-06-27 12:32:19,901 - ERROR - 命令执行失败: 
                CREATE TABLE IF NOT EXISTS entries_202506 
                PARTITION OF entries
   ... 错误: パーティション"entries_202506"はパーティション"entries_2025_06"と重複があります
2025-06-27 12:32:19,901 - app.database.postgresql_client - ERROR - 命令执行失败: 
                CREATE TABLE IF NOT EXISTS entries_202506 
                PARTITION OF entries
   ... 错误: パーティション"entries_202506"はパーティション"entries_2025_06"と重複があります
2025-06-27 12:32:19,902 - ERROR - ❌ 分区创建失败 202506: パーティション"entries_202506"はパーティション"entries_2025_06"と重複があります
2025-06-27 12:32:19,902 - app.database.postgresql_client - ERROR - ❌ 分区创建失败 202506: パーティション"entries_202506"はパーティション"entries_2025_06"と重複があります
2025-06-27 12:32:19,902 - ERROR - ❌ 创建分区失败 202506: パーティション"entries_202506"はパーティション"entries_2025_06"と重複があります
2025-06-27 12:32:19,902 - app.services.f1_listener - ERROR - ❌ 创建分区失败 202506: パーティション"entries_202506"はパーティション"entries_2025_06"と重複があります
2025-06-27 12:32:20,463 - ERROR - 单行查询失败: 行に対応するパーティションがリレーション"entries"に見つかりません
DETAIL:  失敗した行のパーティションキーは(entry_date) = (null)を含みます。
2025-06-27 12:32:20,463 - app.database.postgresql_client - ERROR - 单行查询失败: 行に対応するパーティションがリレーション"entries"に見つかりません
DETAIL:  失敗した行のパーティションキーは(entry_date) = (null)を含みます。
2025-06-27 12:32:20,465 - ERROR - ❌ 创建新记录失败: 行に対応するパーティションがリレーション"entries"に見つかりません
DETAIL:  失敗した行のパーティションキーは(entry_date) = (null)を含みます。
2025-06-27 12:32:20,465 - app.services.f3_data_puller - ERROR - ❌ 创建新记录失败: 行に対応するパーティションがリレーション"entries"に見つかりません
DETAIL:  失敗した行のパーティションキーは(entry_date) = (null)を含みます。
2025-06-27 12:32:20,466 - ERROR - ❌ 处理增量记录失败: 行に対応するパーティションがリレーション"entries"に見つかりません
DETAIL:  失敗した行のパーティションキーは(entry_date) = (null)を含みます。, 记录: {'id': 1, 'name': '张三', 'department': '技术部', 'position': '工程师', 'status': '在职'}
2025-06-27 12:32:20,466 - app.services.f3_data_puller - ERROR - ❌ 处理增量记录失败: 行に対応するパーティションがリレーション"entries"に見つかりません
DETAIL:  失敗した行のパーティションキーは(entry_date) = (null)を含みます。, 记录: {'id': 1, 'name': '张三', 'department': '技术部', 'position': '工程师', 'status': '在职'}
2025-06-27 12:32:20,471 - ERROR - 单行查询失败: 行に対応するパーティションがリレーション"entries"に見つかりません
DETAIL:  失敗した行のパーティションキーは(entry_date) = (null)を含みます。
2025-06-27 12:32:20,471 - app.database.postgresql_client - ERROR - 单行查询失败: 行に対応するパーティションがリレーション"entries"に見つかりません
DETAIL:  失敗した行のパーティションキーは(entry_date) = (null)を含みます。
2025-06-27 12:32:20,472 - ERROR - ❌ 创建新记录失败: 行に対応するパーティションがリレーション"entries"に見つかりません
DETAIL:  失敗した行のパーティションキーは(entry_date) = (null)を含みます。
2025-06-27 12:32:20,472 - app.services.f3_data_puller - ERROR - ❌ 创建新记录失败: 行に対応するパーティションがリレーション"entries"に見つかりません
DETAIL:  失敗した行のパーティションキーは(entry_date) = (null)を含みます。
2025-06-27 12:32:20,473 - ERROR - ❌ 处理增量记录失败: 行に対応するパーティションがリレーション"entries"に見つかりません
DETAIL:  失敗した行のパーティションキーは(entry_date) = (null)を含みます。, 记录: {'id': 2, 'name': '李四', 'department': '销售部', 'position': '销售员', 'status': '在职'}
2025-06-27 12:32:20,473 - app.services.f3_data_puller - ERROR - ❌ 处理增量记录失败: 行に対応するパーティションがリレーション"entries"に見つかりません
DETAIL:  失敗した行のパーティションキーは(entry_date) = (null)を含みます。, 记录: {'id': 2, 'name': '李四', 'department': '销售部', 'position': '销售员', 'status': '在职'}
2025-06-27 12:32:20,476 - ERROR - 单行查询失败: 行に対応するパーティションがリレーション"entries"に見つかりません
DETAIL:  失敗した行のパーティションキーは(entry_date) = (null)を含みます。
2025-06-27 12:32:20,476 - app.database.postgresql_client - ERROR - 单行查询失败: 行に対応するパーティションがリレーション"entries"に見つかりません
DETAIL:  失敗した行のパーティションキーは(entry_date) = (null)を含みます。
2025-06-27 12:32:20,477 - ERROR - ❌ 创建新记录失败: 行に対応するパーティションがリレーション"entries"に見つかりません
DETAIL:  失敗した行のパーティションキーは(entry_date) = (null)を含みます。
2025-06-27 12:32:20,477 - app.services.f3_data_puller - ERROR - ❌ 创建新记录失败: 行に対応するパーティションがリレーション"entries"に見つかりません
DETAIL:  失敗した行のパーティションキーは(entry_date) = (null)を含みます。
2025-06-27 12:32:20,477 - ERROR - ❌ 处理增量记录失败: 行に対応するパーティションがリレーション"entries"に見つかりません
DETAIL:  失敗した行のパーティションキーは(entry_date) = (null)を含みます。, 记录: {'id': 3, 'name': '王五', 'department': '人事部', 'position': 'HR', 'status': '离职'}
2025-06-27 12:32:20,477 - app.services.f3_data_puller - ERROR - ❌ 处理增量记录失败: 行に対応するパーティションがリレーション"entries"に見つかりません
DETAIL:  失敗した行のパーティションキーは(entry_date) = (null)を含みます。, 记录: {'id': 3, 'name': '王五', 'department': '人事部', 'position': 'HR', 'status': '离职'}
2025-06-27 12:32:20,479 - ERROR - ❌ 设置最后拉取时间失败 f3_data_puller: Invalid input of type: 'datetime'. Convert to a bytes, string, int or float first.
2025-06-27 12:32:20,479 - app.database.redis_client - ERROR - ❌ 设置最后拉取时间失败 f3_data_puller: Invalid input of type: 'datetime'. Convert to a bytes, string, int or float first.
2025-06-27 12:32:21,054 - ERROR - 查询执行失败: 
                SELECT * FROM entries 
                WHERE external_id IS NOT NULL 
             ... 错误: 列"updated_at"は存在しません
2025-06-27 12:32:21,054 - app.database.postgresql_client - ERROR - 查询执行失败: 
                SELECT * FROM entries 
                WHERE external_id IS NOT NULL 
             ... 错误: 列"updated_at"は存在しません
2025-06-27 12:32:21,055 - ERROR - ❌ 数据一致性检查失败: 列"updated_at"は存在しません
2025-06-27 12:32:21,055 - app.services.f5_bulk_sync - ERROR - ❌ 数据一致性检查失败: 列"updated_at"は存在しません
2025-06-27 12:37:20,628 - ERROR - 单行查询失败: 行に対応するパーティションがリレーション"entries"に見つかりません
DETAIL:  失敗した行のパーティションキーは(entry_date) = (null)を含みます。
2025-06-27 12:37:20,628 - app.database.postgresql_client - ERROR - 单行查询失败: 行に対応するパーティションがリレーション"entries"に見つかりません
DETAIL:  失敗した行のパーティションキーは(entry_date) = (null)を含みます。
2025-06-27 12:37:20,629 - ERROR - ❌ 创建新记录失败: 行に対応するパーティションがリレーション"entries"に見つかりません
DETAIL:  失敗した行のパーティションキーは(entry_date) = (null)を含みます。
2025-06-27 12:37:20,629 - app.services.f3_data_puller - ERROR - ❌ 创建新记录失败: 行に対応するパーティションがリレーション"entries"に見つかりません
DETAIL:  失敗した行のパーティションキーは(entry_date) = (null)を含みます。
2025-06-27 12:37:20,629 - ERROR - ❌ 处理增量记录失败: 行に対応するパーティションがリレーション"entries"に見つかりません
DETAIL:  失敗した行のパーティションキーは(entry_date) = (null)を含みます。, 记录: {'id': 1, 'name': '张三', 'department': '技术部', 'position': '工程师', 'status': '在职'}
2025-06-27 12:37:20,629 - app.services.f3_data_puller - ERROR - ❌ 处理增量记录失败: 行に対応するパーティションがリレーション"entries"に見つかりません
DETAIL:  失敗した行のパーティションキーは(entry_date) = (null)を含みます。, 记录: {'id': 1, 'name': '张三', 'department': '技术部', 'position': '工程师', 'status': '在职'}
2025-06-27 12:37:20,632 - ERROR - 单行查询失败: 行に対応するパーティションがリレーション"entries"に見つかりません
DETAIL:  失敗した行のパーティションキーは(entry_date) = (null)を含みます。
2025-06-27 12:37:20,632 - app.database.postgresql_client - ERROR - 单行查询失败: 行に対応するパーティションがリレーション"entries"に見つかりません
DETAIL:  失敗した行のパーティションキーは(entry_date) = (null)を含みます。
2025-06-27 12:37:20,633 - ERROR - ❌ 创建新记录失败: 行に対応するパーティションがリレーション"entries"に見つかりません
DETAIL:  失敗した行のパーティションキーは(entry_date) = (null)を含みます。
2025-06-27 12:37:20,633 - app.services.f3_data_puller - ERROR - ❌ 创建新记录失败: 行に対応するパーティションがリレーション"entries"に見つかりません
DETAIL:  失敗した行のパーティションキーは(entry_date) = (null)を含みます。
2025-06-27 12:37:20,633 - ERROR - ❌ 处理增量记录失败: 行に対応するパーティションがリレーション"entries"に見つかりません
DETAIL:  失敗した行のパーティションキーは(entry_date) = (null)を含みます。, 记录: {'id': 2, 'name': '李四', 'department': '销售部', 'position': '销售员', 'status': '在职'}
2025-06-27 12:37:20,633 - app.services.f3_data_puller - ERROR - ❌ 处理增量记录失败: 行に対応するパーティションがリレーション"entries"に見つかりません
DETAIL:  失敗した行のパーティションキーは(entry_date) = (null)を含みます。, 记录: {'id': 2, 'name': '李四', 'department': '销售部', 'position': '销售员', 'status': '在职'}
2025-06-27 12:37:20,635 - ERROR - 单行查询失败: 行に対応するパーティションがリレーション"entries"に見つかりません
DETAIL:  失敗した行のパーティションキーは(entry_date) = (null)を含みます。
2025-06-27 12:37:20,635 - app.database.postgresql_client - ERROR - 单行查询失败: 行に対応するパーティションがリレーション"entries"に見つかりません
DETAIL:  失敗した行のパーティションキーは(entry_date) = (null)を含みます。
2025-06-27 12:37:20,636 - ERROR - ❌ 创建新记录失败: 行に対応するパーティションがリレーション"entries"に見つかりません
DETAIL:  失敗した行のパーティションキーは(entry_date) = (null)を含みます。
2025-06-27 12:37:20,636 - app.services.f3_data_puller - ERROR - ❌ 创建新记录失败: 行に対応するパーティションがリレーション"entries"に見つかりません
DETAIL:  失敗した行のパーティションキーは(entry_date) = (null)を含みます。
2025-06-27 12:37:20,636 - ERROR - ❌ 处理增量记录失败: 行に対応するパーティションがリレーション"entries"に見つかりません
DETAIL:  失敗した行のパーティションキーは(entry_date) = (null)を含みます。, 记录: {'id': 3, 'name': '王五', 'department': '人事部', 'position': 'HR', 'status': '离职'}
2025-06-27 12:37:20,636 - app.services.f3_data_puller - ERROR - ❌ 处理增量记录失败: 行に対応するパーティションがリレーション"entries"に見つかりません
DETAIL:  失敗した行のパーティションキーは(entry_date) = (null)を含みます。, 记录: {'id': 3, 'name': '王五', 'department': '人事部', 'position': 'HR', 'status': '离职'}
2025-06-27 12:37:20,638 - ERROR - ❌ 设置最后拉取时间失败 f3_data_puller: Invalid input of type: 'datetime'. Convert to a bytes, string, int or float first.
2025-06-27 12:37:20,638 - app.database.redis_client - ERROR - ❌ 设置最后拉取时间失败 f3_data_puller: Invalid input of type: 'datetime'. Convert to a bytes, string, int or float first.
2025-06-27 12:42:20,777 - ERROR - 单行查询失败: 行に対応するパーティションがリレーション"entries"に見つかりません
DETAIL:  失敗した行のパーティションキーは(entry_date) = (null)を含みます。
2025-06-27 12:42:20,777 - app.database.postgresql_client - ERROR - 单行查询失败: 行に対応するパーティションがリレーション"entries"に見つかりません
DETAIL:  失敗した行のパーティションキーは(entry_date) = (null)を含みます。
2025-06-27 12:42:20,779 - ERROR - ❌ 创建新记录失败: 行に対応するパーティションがリレーション"entries"に見つかりません
DETAIL:  失敗した行のパーティションキーは(entry_date) = (null)を含みます。
2025-06-27 12:42:20,779 - app.services.f3_data_puller - ERROR - ❌ 创建新记录失败: 行に対応するパーティションがリレーション"entries"に見つかりません
DETAIL:  失敗した行のパーティションキーは(entry_date) = (null)を含みます。
2025-06-27 12:42:20,779 - ERROR - ❌ 处理增量记录失败: 行に対応するパーティションがリレーション"entries"に見つかりません
DETAIL:  失敗した行のパーティションキーは(entry_date) = (null)を含みます。, 记录: {'id': 1, 'name': '张三', 'department': '技术部', 'position': '工程师', 'status': '在职'}
2025-06-27 12:42:20,779 - app.services.f3_data_puller - ERROR - ❌ 处理增量记录失败: 行に対応するパーティションがリレーション"entries"に見つかりません
DETAIL:  失敗した行のパーティションキーは(entry_date) = (null)を含みます。, 记录: {'id': 1, 'name': '张三', 'department': '技术部', 'position': '工程师', 'status': '在职'}
2025-06-27 12:42:20,781 - ERROR - 单行查询失败: 行に対応するパーティションがリレーション"entries"に見つかりません
DETAIL:  失敗した行のパーティションキーは(entry_date) = (null)を含みます。
2025-06-27 12:42:20,781 - app.database.postgresql_client - ERROR - 单行查询失败: 行に対応するパーティションがリレーション"entries"に見つかりません
DETAIL:  失敗した行のパーティションキーは(entry_date) = (null)を含みます。
2025-06-27 12:42:20,782 - ERROR - ❌ 创建新记录失败: 行に対応するパーティションがリレーション"entries"に見つかりません
DETAIL:  失敗した行のパーティションキーは(entry_date) = (null)を含みます。
2025-06-27 12:42:20,782 - app.services.f3_data_puller - ERROR - ❌ 创建新记录失败: 行に対応するパーティションがリレーション"entries"に見つかりません
DETAIL:  失敗した行のパーティションキーは(entry_date) = (null)を含みます。
2025-06-27 12:42:20,782 - ERROR - ❌ 处理增量记录失败: 行に対応するパーティションがリレーション"entries"に見つかりません
DETAIL:  失敗した行のパーティションキーは(entry_date) = (null)を含みます。, 记录: {'id': 2, 'name': '李四', 'department': '销售部', 'position': '销售员', 'status': '在职'}
2025-06-27 12:42:20,782 - app.services.f3_data_puller - ERROR - ❌ 处理增量记录失败: 行に対応するパーティションがリレーション"entries"に見つかりません
DETAIL:  失敗した行のパーティションキーは(entry_date) = (null)を含みます。, 记录: {'id': 2, 'name': '李四', 'department': '销售部', 'position': '销售员', 'status': '在职'}
2025-06-27 12:42:20,784 - ERROR - 单行查询失败: 行に対応するパーティションがリレーション"entries"に見つかりません
DETAIL:  失敗した行のパーティションキーは(entry_date) = (null)を含みます。
2025-06-27 12:42:20,784 - app.database.postgresql_client - ERROR - 单行查询失败: 行に対応するパーティションがリレーション"entries"に見つかりません
DETAIL:  失敗した行のパーティションキーは(entry_date) = (null)を含みます。
2025-06-27 12:42:20,785 - ERROR - ❌ 创建新记录失败: 行に対応するパーティションがリレーション"entries"に見つかりません
DETAIL:  失敗した行のパーティションキーは(entry_date) = (null)を含みます。
2025-06-27 12:42:20,785 - app.services.f3_data_puller - ERROR - ❌ 创建新记录失败: 行に対応するパーティションがリレーション"entries"に見つかりません
DETAIL:  失敗した行のパーティションキーは(entry_date) = (null)を含みます。
2025-06-27 12:42:20,785 - ERROR - ❌ 处理增量记录失败: 行に対応するパーティションがリレーション"entries"に見つかりません
DETAIL:  失敗した行のパーティションキーは(entry_date) = (null)を含みます。, 记录: {'id': 3, 'name': '王五', 'department': '人事部', 'position': 'HR', 'status': '离职'}
2025-06-27 12:42:20,785 - app.services.f3_data_puller - ERROR - ❌ 处理增量记录失败: 行に対応するパーティションがリレーション"entries"に見つかりません
DETAIL:  失敗した行のパーティションキーは(entry_date) = (null)を含みます。, 记录: {'id': 3, 'name': '王五', 'department': '人事部', 'position': 'HR', 'status': '离职'}
2025-06-27 12:42:20,787 - ERROR - ❌ 设置最后拉取时间失败 f3_data_puller: Invalid input of type: 'datetime'. Convert to a bytes, string, int or float first.
2025-06-27 12:42:20,787 - app.database.redis_client - ERROR - ❌ 设置最后拉取时间失败 f3_data_puller: Invalid input of type: 'datetime'. Convert to a bytes, string, int or float first.
2025-06-27 12:47:20,923 - ERROR - 单行查询失败: 行に対応するパーティションがリレーション"entries"に見つかりません
DETAIL:  失敗した行のパーティションキーは(entry_date) = (null)を含みます。
2025-06-27 12:47:20,923 - app.database.postgresql_client - ERROR - 单行查询失败: 行に対応するパーティションがリレーション"entries"に見つかりません
DETAIL:  失敗した行のパーティションキーは(entry_date) = (null)を含みます。
2025-06-27 12:47:20,924 - ERROR - ❌ 创建新记录失败: 行に対応するパーティションがリレーション"entries"に見つかりません
DETAIL:  失敗した行のパーティションキーは(entry_date) = (null)を含みます。
2025-06-27 12:47:20,924 - app.services.f3_data_puller - ERROR - ❌ 创建新记录失败: 行に対応するパーティションがリレーション"entries"に見つかりません
DETAIL:  失敗した行のパーティションキーは(entry_date) = (null)を含みます。
2025-06-27 12:47:20,924 - ERROR - ❌ 处理增量记录失败: 行に対応するパーティションがリレーション"entries"に見つかりません
DETAIL:  失敗した行のパーティションキーは(entry_date) = (null)を含みます。, 记录: {'id': 1, 'name': '张三', 'department': '技术部', 'position': '工程师', 'status': '在职'}
2025-06-27 12:47:20,924 - app.services.f3_data_puller - ERROR - ❌ 处理增量记录失败: 行に対応するパーティションがリレーション"entries"に見つかりません
DETAIL:  失敗した行のパーティションキーは(entry_date) = (null)を含みます。, 记录: {'id': 1, 'name': '张三', 'department': '技术部', 'position': '工程师', 'status': '在职'}
2025-06-27 12:47:20,927 - ERROR - 单行查询失败: 行に対応するパーティションがリレーション"entries"に見つかりません
DETAIL:  失敗した行のパーティションキーは(entry_date) = (null)を含みます。
2025-06-27 12:47:20,927 - app.database.postgresql_client - ERROR - 单行查询失败: 行に対応するパーティションがリレーション"entries"に見つかりません
DETAIL:  失敗した行のパーティションキーは(entry_date) = (null)を含みます。
2025-06-27 12:47:20,928 - ERROR - ❌ 创建新记录失败: 行に対応するパーティションがリレーション"entries"に見つかりません
DETAIL:  失敗した行のパーティションキーは(entry_date) = (null)を含みます。
2025-06-27 12:47:20,928 - app.services.f3_data_puller - ERROR - ❌ 创建新记录失败: 行に対応するパーティションがリレーション"entries"に見つかりません
DETAIL:  失敗した行のパーティションキーは(entry_date) = (null)を含みます。
2025-06-27 12:47:20,928 - ERROR - ❌ 处理增量记录失败: 行に対応するパーティションがリレーション"entries"に見つかりません
DETAIL:  失敗した行のパーティションキーは(entry_date) = (null)を含みます。, 记录: {'id': 2, 'name': '李四', 'department': '销售部', 'position': '销售员', 'status': '在职'}
2025-06-27 12:47:20,928 - app.services.f3_data_puller - ERROR - ❌ 处理增量记录失败: 行に対応するパーティションがリレーション"entries"に見つかりません
DETAIL:  失敗した行のパーティションキーは(entry_date) = (null)を含みます。, 记录: {'id': 2, 'name': '李四', 'department': '销售部', 'position': '销售员', 'status': '在职'}
2025-06-27 12:47:20,930 - ERROR - 单行查询失败: 行に対応するパーティションがリレーション"entries"に見つかりません
DETAIL:  失敗した行のパーティションキーは(entry_date) = (null)を含みます。
2025-06-27 12:47:20,930 - app.database.postgresql_client - ERROR - 单行查询失败: 行に対応するパーティションがリレーション"entries"に見つかりません
DETAIL:  失敗した行のパーティションキーは(entry_date) = (null)を含みます。
2025-06-27 12:47:20,931 - ERROR - ❌ 创建新记录失败: 行に対応するパーティションがリレーション"entries"に見つかりません
DETAIL:  失敗した行のパーティションキーは(entry_date) = (null)を含みます。
2025-06-27 12:47:20,931 - app.services.f3_data_puller - ERROR - ❌ 创建新记录失败: 行に対応するパーティションがリレーション"entries"に見つかりません
DETAIL:  失敗した行のパーティションキーは(entry_date) = (null)を含みます。
2025-06-27 12:47:20,931 - ERROR - ❌ 处理增量记录失败: 行に対応するパーティションがリレーション"entries"に見つかりません
DETAIL:  失敗した行のパーティションキーは(entry_date) = (null)を含みます。, 记录: {'id': 3, 'name': '王五', 'department': '人事部', 'position': 'HR', 'status': '离职'}
2025-06-27 12:47:20,931 - app.services.f3_data_puller - ERROR - ❌ 处理增量记录失败: 行に対応するパーティションがリレーション"entries"に見つかりません
DETAIL:  失敗した行のパーティションキーは(entry_date) = (null)を含みます。, 记录: {'id': 3, 'name': '王五', 'department': '人事部', 'position': 'HR', 'status': '离职'}
2025-06-27 12:47:20,932 - ERROR - ❌ 设置最后拉取时间失败 f3_data_puller: Invalid input of type: 'datetime'. Convert to a bytes, string, int or float first.
2025-06-27 12:47:20,932 - app.database.redis_client - ERROR - ❌ 设置最后拉取时间失败 f3_data_puller: Invalid input of type: 'datetime'. Convert to a bytes, string, int or float first.
2025-06-27 13:03:13,969 - ERROR - 监听通知时出错: 'Connection' object has no attribute 'connection'
2025-06-27 13:03:13,969 - app.database.postgresql_client - ERROR - 监听通知时出错: 'Connection' object has no attribute 'connection'
2025-06-27 13:03:13,987 - ERROR - 命令执行失败: 
                CREATE TABLE IF NOT EXISTS entries_202506 
                PARTITION OF entries
   ... 错误: パーティション"entries_202506"はパーティション"entries_2025_06"と重複があります
2025-06-27 13:03:13,987 - app.database.postgresql_client - ERROR - 命令执行失败: 
                CREATE TABLE IF NOT EXISTS entries_202506 
                PARTITION OF entries
   ... 错误: パーティション"entries_202506"はパーティション"entries_2025_06"と重複があります
2025-06-27 13:03:13,988 - ERROR - ❌ 分区创建失败 202506: パーティション"entries_202506"はパーティション"entries_2025_06"と重複があります
2025-06-27 13:03:13,988 - app.database.postgresql_client - ERROR - ❌ 分区创建失败 202506: パーティション"entries_202506"はパーティション"entries_2025_06"と重複があります
2025-06-27 13:03:13,989 - ERROR - ❌ 创建分区失败 202506: パーティション"entries_202506"はパーティション"entries_2025_06"と重複があります
2025-06-27 13:03:13,989 - app.services.f1_listener - ERROR - ❌ 创建分区失败 202506: パーティション"entries_202506"はパーティション"entries_2025_06"と重複があります
2025-06-27 13:04:37,334 - ERROR - ❌ 创建entry失败: 'OperationHandlerService' object has no attribute 'handle_insert'
2025-06-27 13:04:37,334 - app.routers.entries_api - ERROR - ❌ 创建entry失败: 'OperationHandlerService' object has no attribute 'handle_insert'
2025-06-27 13:04:37,825 - ERROR - 查询执行失败: 
            SELECT entry_date, 
                   SUM(duration) as total_duration,
           ... 错误: 列"duration"は存在しません
2025-06-27 13:04:37,825 - app.database.postgresql_client - ERROR - 查询执行失败: 
            SELECT entry_date, 
                   SUM(duration) as total_duration,
           ... 错误: 列"duration"は存在しません
2025-06-27 13:04:37,827 - ERROR - ❌ 获取图表数据失败: 列"duration"は存在しません
2025-06-27 13:04:37,827 - app.routers.entries_api - ERROR - ❌ 获取图表数据失败: 列"duration"は存在しません
2025-06-27 13:23:15,824 - ERROR - ❌ 设置最后拉取时间失败 f3_data_puller: Invalid input of type: 'datetime'. Convert to a bytes, string, int or float first.
2025-06-27 13:23:15,824 - app.database.redis_client - ERROR - ❌ 设置最后拉取时间失败 f3_data_puller: Invalid input of type: 'datetime'. Convert to a bytes, string, int or float first.
2025-06-27 13:30:36,654 - ERROR - 监听通知时出错: 'Connection' object has no attribute 'notifies'
2025-06-27 13:30:36,654 - app.database.postgresql_client - ERROR - 监听通知时出错: 'Connection' object has no attribute 'notifies'
2025-06-27 13:30:36,675 - ERROR - 命令执行失败: 
                CREATE TABLE IF NOT EXISTS entries_2025_07 
                PARTITION OF entries
  ... 错误: パーティション"entries_2025_07"はパーティション"entries_202507"と重複があります
2025-06-27 13:30:36,675 - app.database.postgresql_client - ERROR - 命令执行失败: 
                CREATE TABLE IF NOT EXISTS entries_2025_07 
                PARTITION OF entries
  ... 错误: パーティション"entries_2025_07"はパーティション"entries_202507"と重複があります
2025-06-27 13:30:36,677 - ERROR - ❌ 分区创建失败 202507: パーティション"entries_2025_07"はパーティション"entries_202507"と重複があります
2025-06-27 13:30:36,677 - app.database.postgresql_client - ERROR - ❌ 分区创建失败 202507: パーティション"entries_2025_07"はパーティション"entries_202507"と重複があります
2025-06-27 13:30:36,677 - ERROR - ❌ 创建分区失败 202507: パーティション"entries_2025_07"はパーティション"entries_202507"と重複があります
2025-06-27 13:30:36,677 - app.services.f1_listener - ERROR - ❌ 创建分区失败 202507: パーティション"entries_2025_07"はパーティション"entries_202507"と重複があります
2025-06-27 13:30:46,082 - ERROR - ❌ 设置最后拉取时间失败 f3_data_puller: Invalid input of type: 'datetime'. Convert to a bytes, string, int or float first.
2025-06-27 13:30:46,082 - app.database.redis_client - ERROR - ❌ 设置最后拉取时间失败 f3_data_puller: Invalid input of type: 'datetime'. Convert to a bytes, string, int or float first.
2025-06-27 13:31:14,586 - ERROR - 监听通知时出错: 'Connection' object has no attribute 'notifies'
2025-06-27 13:31:14,586 - app.database.postgresql_client - ERROR - 监听通知时出错: 'Connection' object has no attribute 'notifies'
2025-06-27 13:31:39,998 - ERROR - 监听通知时出错: 'Connection' object has no attribute 'notifies'
2025-06-27 13:31:39,998 - app.database.postgresql_client - ERROR - 监听通知时出错: 'Connection' object has no attribute 'notifies'
2025-06-27 13:46:51,466 - ERROR - ❌ Server6请求失败: POST http://************:8009/mdb/query - Connection timeout to host http://************:8009/mdb/query
2025-06-27 13:46:51,466 - app.utils.server6_client - ERROR - ❌ Server6请求失败: POST http://************:8009/mdb/query - Connection timeout to host http://************:8009/mdb/query
2025-06-27 13:46:51,466 - ERROR - ❌ 查询MDB数据失败: Server6请求失败: Connection timeout to host http://************:8009/mdb/query
2025-06-27 13:46:51,466 - app.utils.server6_client - ERROR - ❌ 查询MDB数据失败: Server6请求失败: Connection timeout to host http://************:8009/mdb/query
2025-06-27 13:46:51,467 - ERROR - ❌ 获取增量数据失败: Server6请求失败: Connection timeout to host http://************:8009/mdb/query
2025-06-27 13:46:51,467 - app.utils.server6_client - ERROR - ❌ 获取增量数据失败: Server6请求失败: Connection timeout to host http://************:8009/mdb/query
2025-06-27 13:52:02,466 - ERROR - ❌ Server6请求失败: POST http://************:8009/mdb/query - Connection timeout to host http://************:8009/mdb/query
2025-06-27 13:52:02,466 - app.utils.server6_client - ERROR - ❌ Server6请求失败: POST http://************:8009/mdb/query - Connection timeout to host http://************:8009/mdb/query
2025-06-27 13:52:02,467 - ERROR - ❌ 查询MDB数据失败: Server6请求失败: Connection timeout to host http://************:8009/mdb/query
2025-06-27 13:52:02,467 - app.utils.server6_client - ERROR - ❌ 查询MDB数据失败: Server6请求失败: Connection timeout to host http://************:8009/mdb/query
2025-06-27 13:52:02,467 - ERROR - ❌ 获取增量数据失败: Server6请求失败: Connection timeout to host http://************:8009/mdb/query
2025-06-27 13:52:02,467 - app.utils.server6_client - ERROR - ❌ 获取增量数据失败: Server6请求失败: Connection timeout to host http://************:8009/mdb/query
2025-06-27 13:57:13,467 - ERROR - ❌ Server6请求失败: POST http://************:8009/mdb/query - Connection timeout to host http://************:8009/mdb/query
2025-06-27 13:57:13,467 - app.utils.server6_client - ERROR - ❌ Server6请求失败: POST http://************:8009/mdb/query - Connection timeout to host http://************:8009/mdb/query
2025-06-27 13:57:13,467 - ERROR - ❌ 查询MDB数据失败: Server6请求失败: Connection timeout to host http://************:8009/mdb/query
2025-06-27 13:57:13,467 - app.utils.server6_client - ERROR - ❌ 查询MDB数据失败: Server6请求失败: Connection timeout to host http://************:8009/mdb/query
2025-06-27 13:57:13,467 - ERROR - ❌ 获取增量数据失败: Server6请求失败: Connection timeout to host http://************:8009/mdb/query
2025-06-27 13:57:13,467 - app.utils.server6_client - ERROR - ❌ 获取增量数据失败: Server6请求失败: Connection timeout to host http://************:8009/mdb/query
2025-06-27 14:02:24,466 - ERROR - ❌ Server6请求失败: POST http://************:8009/mdb/query - Connection timeout to host http://************:8009/mdb/query
2025-06-27 14:02:24,466 - app.utils.server6_client - ERROR - ❌ Server6请求失败: POST http://************:8009/mdb/query - Connection timeout to host http://************:8009/mdb/query
2025-06-27 14:02:24,467 - ERROR - ❌ 查询MDB数据失败: Server6请求失败: Connection timeout to host http://************:8009/mdb/query
2025-06-27 14:02:24,467 - app.utils.server6_client - ERROR - ❌ 查询MDB数据失败: Server6请求失败: Connection timeout to host http://************:8009/mdb/query
2025-06-27 14:02:24,467 - ERROR - ❌ 获取增量数据失败: Server6请求失败: Connection timeout to host http://************:8009/mdb/query
2025-06-27 14:02:24,467 - app.utils.server6_client - ERROR - ❌ 获取增量数据失败: Server6请求失败: Connection timeout to host http://************:8009/mdb/query
2025-06-27 14:07:35,466 - ERROR - ❌ Server6请求失败: POST http://************:8009/mdb/query - Connection timeout to host http://************:8009/mdb/query
2025-06-27 14:07:35,466 - app.utils.server6_client - ERROR - ❌ Server6请求失败: POST http://************:8009/mdb/query - Connection timeout to host http://************:8009/mdb/query
2025-06-27 14:07:35,466 - ERROR - ❌ 查询MDB数据失败: Server6请求失败: Connection timeout to host http://************:8009/mdb/query
2025-06-27 14:07:35,466 - app.utils.server6_client - ERROR - ❌ 查询MDB数据失败: Server6请求失败: Connection timeout to host http://************:8009/mdb/query
2025-06-27 14:07:35,466 - ERROR - ❌ 获取增量数据失败: Server6请求失败: Connection timeout to host http://************:8009/mdb/query
2025-06-27 14:07:35,466 - app.utils.server6_client - ERROR - ❌ 获取增量数据失败: Server6请求失败: Connection timeout to host http://************:8009/mdb/query
2025-06-27 14:12:46,466 - ERROR - ❌ Server6请求失败: POST http://************:8009/mdb/query - Connection timeout to host http://************:8009/mdb/query
2025-06-27 14:12:46,466 - app.utils.server6_client - ERROR - ❌ Server6请求失败: POST http://************:8009/mdb/query - Connection timeout to host http://************:8009/mdb/query
2025-06-27 14:12:46,467 - ERROR - ❌ 查询MDB数据失败: Server6请求失败: Connection timeout to host http://************:8009/mdb/query
2025-06-27 14:12:46,467 - app.utils.server6_client - ERROR - ❌ 查询MDB数据失败: Server6请求失败: Connection timeout to host http://************:8009/mdb/query
2025-06-27 14:12:46,467 - ERROR - ❌ 获取增量数据失败: Server6请求失败: Connection timeout to host http://************:8009/mdb/query
2025-06-27 14:12:46,467 - app.utils.server6_client - ERROR - ❌ 获取增量数据失败: Server6请求失败: Connection timeout to host http://************:8009/mdb/query
2025-06-27 14:17:57,467 - ERROR - ❌ Server6请求失败: POST http://************:8009/mdb/query - Connection timeout to host http://************:8009/mdb/query
2025-06-27 14:17:57,467 - app.utils.server6_client - ERROR - ❌ Server6请求失败: POST http://************:8009/mdb/query - Connection timeout to host http://************:8009/mdb/query
2025-06-27 14:17:57,467 - ERROR - ❌ 查询MDB数据失败: Server6请求失败: Connection timeout to host http://************:8009/mdb/query
2025-06-27 14:17:57,467 - app.utils.server6_client - ERROR - ❌ 查询MDB数据失败: Server6请求失败: Connection timeout to host http://************:8009/mdb/query
2025-06-27 14:17:57,468 - ERROR - ❌ 获取增量数据失败: Server6请求失败: Connection timeout to host http://************:8009/mdb/query
2025-06-27 14:17:57,468 - app.utils.server6_client - ERROR - ❌ 获取增量数据失败: Server6请求失败: Connection timeout to host http://************:8009/mdb/query
2025-06-27 14:37:57,324 - ERROR - Server5启动失败: No module named 'server6'
2025-06-27 14:37:57,324 - root - ERROR - Server5启动失败: No module named 'server6'
2025-06-27 14:37:57,324 - ERROR - Server5启动失败: No module named 'server6'
2025-06-27 14:37:57,324 - root - ERROR - Server5启动失败: No module named 'server6'
2025-06-27 14:49:35,649 - ERROR - Server5启动失败: cannot import name 'BULK_SYNC_DAYS' from 'config.config' (unknown location)
2025-06-27 14:49:35,649 - root - ERROR - Server5启动失败: cannot import name 'BULK_SYNC_DAYS' from 'config.config' (unknown location)
2025-06-27 14:49:35,649 - ERROR - Server5启动失败: cannot import name 'BULK_SYNC_DAYS' from 'config.config' (unknown location)
2025-06-27 14:49:35,649 - root - ERROR - Server5启动失败: cannot import name 'BULK_SYNC_DAYS' from 'config.config' (unknown location)
2025-06-27 14:53:32,887 - ERROR - 监听通知时出错: 'Connection' object has no attribute 'notifies'
2025-06-27 14:53:32,887 - app.database.postgresql_client - ERROR - 监听通知时出错: 'Connection' object has no attribute 'notifies'
2025-06-27 14:53:33,327 - ERROR - ❌ 设置最后拉取时间失败 f3_data_puller: Invalid input of type: 'datetime'. Convert to a bytes, string, int or float first.
2025-06-27 14:53:33,327 - app.database.redis_client - ERROR - ❌ 设置最后拉取时间失败 f3_data_puller: Invalid input of type: 'datetime'. Convert to a bytes, string, int or float first.
2025-06-27 14:58:33,447 - ERROR - ❌ 设置最后拉取时间失败 f3_data_puller: Invalid input of type: 'datetime'. Convert to a bytes, string, int or float first.
2025-06-27 14:58:33,447 - app.database.redis_client - ERROR - ❌ 设置最后拉取时间失败 f3_data_puller: Invalid input of type: 'datetime'. Convert to a bytes, string, int or float first.
2025-06-27 15:01:03,905 - ERROR - ❌ 设置最后拉取时间失败 f3_data_puller: Invalid input of type: 'datetime'. Convert to a bytes, string, int or float first.
2025-06-27 15:01:03,905 - app.database.redis_client - ERROR - ❌ 设置最后拉取时间失败 f3_data_puller: Invalid input of type: 'datetime'. Convert to a bytes, string, int or float first.
2025-06-27 15:01:14,586 - ERROR - Task was destroyed but it is pending!
task: <Task pending name='Task-202' coro=<Connection._cancel() running at /home/<USER>/miniconda3/envs/my_suite_unified/lib/python3.12/site-packages/asyncpg/connection.py:1646> wait_for=<Future pending cb=[BaseSelectorEventLoop._sock_write_done(6, handle=<Handle BaseS...3.93', 5432))>)(), Task.task_wakeup()]>>
2025-06-27 15:01:14,586 - asyncio - ERROR - Task was destroyed but it is pending!
task: <Task pending name='Task-202' coro=<Connection._cancel() running at /home/<USER>/miniconda3/envs/my_suite_unified/lib/python3.12/site-packages/asyncpg/connection.py:1646> wait_for=<Future pending cb=[BaseSelectorEventLoop._sock_write_done(6, handle=<Handle BaseS...3.93', 5432))>)(), Task.task_wakeup()]>>
2025-06-27 15:02:16,183 - ERROR - Task was destroyed but it is pending!
task: <Task pending name='Task-202' coro=<Connection._cancel() running at /home/<USER>/miniconda3/envs/my_suite_unified/lib/python3.12/site-packages/asyncpg/connection.py:1646> wait_for=<Future pending cb=[BaseSelectorEventLoop._sock_write_done(6, handle=<Handle BaseS...3.93', 5432))>)(), Task.task_wakeup()]>>
2025-06-27 15:02:16,183 - asyncio - ERROR - Task was destroyed but it is pending!
task: <Task pending name='Task-202' coro=<Connection._cancel() running at /home/<USER>/miniconda3/envs/my_suite_unified/lib/python3.12/site-packages/asyncpg/connection.py:1646> wait_for=<Future pending cb=[BaseSelectorEventLoop._sock_write_done(6, handle=<Handle BaseS...3.93', 5432))>)(), Task.task_wakeup()]>>
2025-06-30 07:39:28,711 - ERROR - Server5启动失败: cannot import name 'UserSyncService' from 'app.services' (/home/<USER>/Documents/testF/2025/ser25/server0613/MySuiteDatebase2402newuigood-yolo5-3/server5/app/services/__init__.py)
2025-06-30 07:39:28,711 - root - ERROR - Server5启动失败: cannot import name 'UserSyncService' from 'app.services' (/home/<USER>/Documents/testF/2025/ser25/server0613/MySuiteDatebase2402newuigood-yolo5-3/server5/app/services/__init__.py)
2025-06-30 07:39:28,712 - ERROR - Server5启动失败: cannot import name 'UserSyncService' from 'app.services' (/home/<USER>/Documents/testF/2025/ser25/server0613/MySuiteDatebase2402newuigood-yolo5-3/server5/app/services/__init__.py)
2025-06-30 07:39:28,712 - root - ERROR - Server5启动失败: cannot import name 'UserSyncService' from 'app.services' (/home/<USER>/Documents/testF/2025/ser25/server0613/MySuiteDatebase2402newuigood-yolo5-3/server5/app/services/__init__.py)
2025-06-30 12:43:43,083 - ERROR - ❌ f6专属ID同步器启动失败: 'UserSyncService' object has no attribute '_sync_worker_loop'
2025-06-30 12:43:43,083 - app.services.f6_user_sync - ERROR - ❌ f6专属ID同步器启动失败: 'UserSyncService' object has no attribute '_sync_worker_loop'
2025-06-30 12:43:43,083 - ERROR - f6_user_sync 启动失败
2025-06-30 12:43:43,083 - app.main - ERROR - f6_user_sync 启动失败
2025-06-30 13:11:12,269 - ERROR - ❌ Server6请求失败: PUT http://************:8009/mdb/entries/602568 - Status: 404, Message: Not Found. Request Body: {"entry_id": 8812, "created_ts": "2025-06-30", "日付": "2025-06-26", "id": 8812, "external_id": 602568, "ts": "2025-06-30", "従業員ｺｰﾄﾞ": "215829", "機種": "", "号機": "", "工場製番": "", "工事番号": "24585", "ﾕﾆｯﾄ番号": "", "区分": 3, "項目": 7, "時間": 9.0, "所属ｺｰﾄﾞ": "131"}
2025-06-30 13:11:12,269 - app.utils.server6_client - ERROR - ❌ Server6请求失败: PUT http://************:8009/mdb/entries/602568 - Status: 404, Message: Not Found. Request Body: {"entry_id": 8812, "created_ts": "2025-06-30", "日付": "2025-06-26", "id": 8812, "external_id": 602568, "ts": "2025-06-30", "従業員ｺｰﾄﾞ": "215829", "機種": "", "号機": "", "工場製番": "", "工事番号": "24585", "ﾕﾆｯﾄ番号": "", "区分": 3, "項目": 7, "時間": 9.0, "所属ｺｰﾄﾞ": "131"}
2025-06-30 13:11:12,270 - ERROR - ❌ Server6请求失败: PUT http://************:8009/mdb/entries/602568 - Status: 404, Message: Not Found. Request Body: {"entry_id": 8812, "created_ts": "2025-06-30", "日付": "2025-06-26", "id": 8812, "external_id": 602568, "ts": "2025-06-30", "従業員ｺｰﾄﾞ": "215829", "機種": "", "号機": "", "工場製番": "", "工事番号": "24585", "ﾕﾆｯﾄ番号": "", "区分": 3, "項目": 7, "時間": 9.0, "所属ｺｰﾄﾞ": "131"}
2025-06-30 13:11:12,270 - app.utils.server6_client - ERROR - ❌ Server6请求失败: PUT http://************:8009/mdb/entries/602568 - Status: 404, Message: Not Found. Request Body: {"entry_id": 8812, "created_ts": "2025-06-30", "日付": "2025-06-26", "id": 8812, "external_id": 602568, "ts": "2025-06-30", "従業員ｺｰﾄﾞ": "215829", "機種": "", "号機": "", "工場製番": "", "工事番号": "24585", "ﾕﾆｯﾄ番号": "", "区分": 3, "項目": 7, "時間": 9.0, "所属ｺｰﾄﾞ": "131"}
2025-06-30 13:11:12,270 - ERROR - ❌ Server6请求失败: PUT http://************:8009/mdb/entries/602568 - Status: 404, Message: Not Found. Request Body: {"entry_id": 8812, "created_ts": "2025-06-30", "日付": "2025-06-26", "id": 8812, "external_id": 602568, "ts": "2025-06-30", "従業員ｺｰﾄﾞ": "215829", "機種": "", "号機": "", "工場製番": "", "工事番号": "24585", "ﾕﾆｯﾄ番号": "", "区分": 3, "項目": 7, "時間": 9.0, "所属ｺｰﾄﾞ": "131"}
2025-06-30 13:11:12,270 - app.utils.server6_client - ERROR - ❌ Server6请求失败: PUT http://************:8009/mdb/entries/602568 - Status: 404, Message: Not Found. Request Body: {"entry_id": 8812, "created_ts": "2025-06-30", "日付": "2025-06-26", "id": 8812, "external_id": 602568, "ts": "2025-06-30", "従業員ｺｰﾄﾞ": "215829", "機種": "", "号機": "", "工場製番": "", "工事番号": "24585", "ﾕﾆｯﾄ番号": "", "区分": 3, "項目": 7, "時間": 9.0, "所属ｺｰﾄﾞ": "131"}
2025-06-30 13:11:12,271 - ERROR - ❌ Server6请求失败: PUT http://************:8009/mdb/entries/602568 - Status: 404, Message: Not Found. Request Body: {"entry_id": 8812, "created_ts": "2025-06-30", "日付": "2025-06-26", "id": 8812, "external_id": 602568, "ts": "2025-06-30", "従業員ｺｰﾄﾞ": "215829", "機種": "", "号機": "", "工場製番": "", "工事番号": "24585", "ﾕﾆｯﾄ番号": "", "区分": 3, "項目": 7, "時間": 9.0, "所属ｺｰﾄﾞ": "131"}
2025-06-30 13:11:12,271 - app.utils.server6_client - ERROR - ❌ Server6请求失败: PUT http://************:8009/mdb/entries/602568 - Status: 404, Message: Not Found. Request Body: {"entry_id": 8812, "created_ts": "2025-06-30", "日付": "2025-06-26", "id": 8812, "external_id": 602568, "ts": "2025-06-30", "従業員ｺｰﾄﾞ": "215829", "機種": "", "号機": "", "工場製番": "", "工事番号": "24585", "ﾕﾆｯﾄ番号": "", "区分": 3, "項目": 7, "時間": 9.0, "所属ｺｰﾄﾞ": "131"}
2025-06-30 13:11:12,274 - ERROR - ❌ UPDATE操作失败: Server6请求返回错误 404 (URL: http://************:8009/mdb/entries/602568, Employee: N/A): Not Found
Traceback (most recent call last):
  File "/home/<USER>/Documents/testF/2025/ser25/server0613/MySuiteDatebase2402newuigood-yolo5-3/server5/app/utils/server6_client.py", line 78, in _make_request
    response.raise_for_status()  # 如果状态码是 4xx 或 5xx，则抛出异常
    ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/miniconda3/envs/my_suite_unified/lib/python3.12/site-packages/aiohttp/client_reqrep.py", line 629, in raise_for_status
    raise ClientResponseError(
aiohttp.client_exceptions.ClientResponseError: 404, message='Not Found', url='http://************:8009/mdb/entries/602568'

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "/home/<USER>/Documents/testF/2025/ser25/server0613/MySuiteDatebase2402newuigood-yolo5-3/server5/app/services/f2_push_writer.py", line 272, in _handle_update_operation
    response = await self.server6_client.update_entry(external_id, entry_data)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Documents/testF/2025/ser25/server0613/MySuiteDatebase2402newuigood-yolo5-3/server5/app/utils/server6_client.py", line 249, in update_entry
    return await self._make_request('PUT', f'/mdb/entries/{external_id}', json_data=serializable_data)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Documents/testF/2025/ser25/server0613/MySuiteDatebase2402newuigood-yolo5-3/server5/app/utils/server6_client.py", line 94, in _make_request
    raise Exception(f"Server6请求返回错误 {e.status} (URL: {url}, Employee: {json_data.get('employee_id', 'N/A')}): {e.message}") from e
Exception: Server6请求返回错误 404 (URL: http://************:8009/mdb/entries/602568, Employee: N/A): Not Found
2025-06-30 13:11:12,274 - app.services.f2_push_writer - ERROR - ❌ UPDATE操作失败: Server6请求返回错误 404 (URL: http://************:8009/mdb/entries/602568, Employee: N/A): Not Found
Traceback (most recent call last):
  File "/home/<USER>/Documents/testF/2025/ser25/server0613/MySuiteDatebase2402newuigood-yolo5-3/server5/app/utils/server6_client.py", line 78, in _make_request
    response.raise_for_status()  # 如果状态码是 4xx 或 5xx，则抛出异常
    ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/miniconda3/envs/my_suite_unified/lib/python3.12/site-packages/aiohttp/client_reqrep.py", line 629, in raise_for_status
    raise ClientResponseError(
aiohttp.client_exceptions.ClientResponseError: 404, message='Not Found', url='http://************:8009/mdb/entries/602568'

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "/home/<USER>/Documents/testF/2025/ser25/server0613/MySuiteDatebase2402newuigood-yolo5-3/server5/app/services/f2_push_writer.py", line 272, in _handle_update_operation
    response = await self.server6_client.update_entry(external_id, entry_data)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Documents/testF/2025/ser25/server0613/MySuiteDatebase2402newuigood-yolo5-3/server5/app/utils/server6_client.py", line 249, in update_entry
    return await self._make_request('PUT', f'/mdb/entries/{external_id}', json_data=serializable_data)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Documents/testF/2025/ser25/server0613/MySuiteDatebase2402newuigood-yolo5-3/server5/app/utils/server6_client.py", line 94, in _make_request
    raise Exception(f"Server6请求返回错误 {e.status} (URL: {url}, Employee: {json_data.get('employee_id', 'N/A')}): {e.message}") from e
Exception: Server6请求返回错误 404 (URL: http://************:8009/mdb/entries/602568, Employee: N/A): Not Found
2025-06-30 13:11:12,280 - ERROR - ❌ 推送作业失败 7: Server6请求返回错误 404 (URL: http://************:8009/mdb/entries/602568, Employee: N/A): Not Found
2025-06-30 13:11:12,280 - app.services.f2_push_writer - ERROR - ❌ 推送作业失败 7: Server6请求返回错误 404 (URL: http://************:8009/mdb/entries/602568, Employee: N/A): Not Found
2025-06-30 13:11:12,280 - ERROR - ❌ 推送任务处理失败: Server6请求返回错误 404 (URL: http://************:8009/mdb/entries/602568, Employee: N/A): Not Found
2025-06-30 13:11:12,280 - app.services.f2_push_writer - ERROR - ❌ 推送任务处理失败: Server6请求返回错误 404 (URL: http://************:8009/mdb/entries/602568, Employee: N/A): Not Found
2025-06-30 13:11:12,283 - ERROR - ❌ UPDATE操作失败: Server6请求返回错误 404 (URL: http://************:8009/mdb/entries/602568, Employee: N/A): Not Found
Traceback (most recent call last):
  File "/home/<USER>/Documents/testF/2025/ser25/server0613/MySuiteDatebase2402newuigood-yolo5-3/server5/app/utils/server6_client.py", line 78, in _make_request
    response.raise_for_status()  # 如果状态码是 4xx 或 5xx，则抛出异常
    ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/miniconda3/envs/my_suite_unified/lib/python3.12/site-packages/aiohttp/client_reqrep.py", line 629, in raise_for_status
    raise ClientResponseError(
aiohttp.client_exceptions.ClientResponseError: 404, message='Not Found', url='http://************:8009/mdb/entries/602568'

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "/home/<USER>/Documents/testF/2025/ser25/server0613/MySuiteDatebase2402newuigood-yolo5-3/server5/app/services/f2_push_writer.py", line 272, in _handle_update_operation
    response = await self.server6_client.update_entry(external_id, entry_data)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Documents/testF/2025/ser25/server0613/MySuiteDatebase2402newuigood-yolo5-3/server5/app/utils/server6_client.py", line 249, in update_entry
    return await self._make_request('PUT', f'/mdb/entries/{external_id}', json_data=serializable_data)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Documents/testF/2025/ser25/server0613/MySuiteDatebase2402newuigood-yolo5-3/server5/app/utils/server6_client.py", line 94, in _make_request
    raise Exception(f"Server6请求返回错误 {e.status} (URL: {url}, Employee: {json_data.get('employee_id', 'N/A')}): {e.message}") from e
Exception: Server6请求返回错误 404 (URL: http://************:8009/mdb/entries/602568, Employee: N/A): Not Found
2025-06-30 13:11:12,283 - app.services.f2_push_writer - ERROR - ❌ UPDATE操作失败: Server6请求返回错误 404 (URL: http://************:8009/mdb/entries/602568, Employee: N/A): Not Found
Traceback (most recent call last):
  File "/home/<USER>/Documents/testF/2025/ser25/server0613/MySuiteDatebase2402newuigood-yolo5-3/server5/app/utils/server6_client.py", line 78, in _make_request
    response.raise_for_status()  # 如果状态码是 4xx 或 5xx，则抛出异常
    ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/miniconda3/envs/my_suite_unified/lib/python3.12/site-packages/aiohttp/client_reqrep.py", line 629, in raise_for_status
    raise ClientResponseError(
aiohttp.client_exceptions.ClientResponseError: 404, message='Not Found', url='http://************:8009/mdb/entries/602568'

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "/home/<USER>/Documents/testF/2025/ser25/server0613/MySuiteDatebase2402newuigood-yolo5-3/server5/app/services/f2_push_writer.py", line 272, in _handle_update_operation
    response = await self.server6_client.update_entry(external_id, entry_data)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Documents/testF/2025/ser25/server0613/MySuiteDatebase2402newuigood-yolo5-3/server5/app/utils/server6_client.py", line 249, in update_entry
    return await self._make_request('PUT', f'/mdb/entries/{external_id}', json_data=serializable_data)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Documents/testF/2025/ser25/server0613/MySuiteDatebase2402newuigood-yolo5-3/server5/app/utils/server6_client.py", line 94, in _make_request
    raise Exception(f"Server6请求返回错误 {e.status} (URL: {url}, Employee: {json_data.get('employee_id', 'N/A')}): {e.message}") from e
Exception: Server6请求返回错误 404 (URL: http://************:8009/mdb/entries/602568, Employee: N/A): Not Found
2025-06-30 13:11:12,285 - ERROR - ❌ UPDATE操作失败: Server6请求返回错误 404 (URL: http://************:8009/mdb/entries/602568, Employee: N/A): Not Found
Traceback (most recent call last):
  File "/home/<USER>/Documents/testF/2025/ser25/server0613/MySuiteDatebase2402newuigood-yolo5-3/server5/app/utils/server6_client.py", line 78, in _make_request
    response.raise_for_status()  # 如果状态码是 4xx 或 5xx，则抛出异常
    ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/miniconda3/envs/my_suite_unified/lib/python3.12/site-packages/aiohttp/client_reqrep.py", line 629, in raise_for_status
    raise ClientResponseError(
aiohttp.client_exceptions.ClientResponseError: 404, message='Not Found', url='http://************:8009/mdb/entries/602568'

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "/home/<USER>/Documents/testF/2025/ser25/server0613/MySuiteDatebase2402newuigood-yolo5-3/server5/app/services/f2_push_writer.py", line 272, in _handle_update_operation
    response = await self.server6_client.update_entry(external_id, entry_data)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Documents/testF/2025/ser25/server0613/MySuiteDatebase2402newuigood-yolo5-3/server5/app/utils/server6_client.py", line 249, in update_entry
    return await self._make_request('PUT', f'/mdb/entries/{external_id}', json_data=serializable_data)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Documents/testF/2025/ser25/server0613/MySuiteDatebase2402newuigood-yolo5-3/server5/app/utils/server6_client.py", line 94, in _make_request
    raise Exception(f"Server6请求返回错误 {e.status} (URL: {url}, Employee: {json_data.get('employee_id', 'N/A')}): {e.message}") from e
Exception: Server6请求返回错误 404 (URL: http://************:8009/mdb/entries/602568, Employee: N/A): Not Found
2025-06-30 13:11:12,285 - app.services.f2_push_writer - ERROR - ❌ UPDATE操作失败: Server6请求返回错误 404 (URL: http://************:8009/mdb/entries/602568, Employee: N/A): Not Found
Traceback (most recent call last):
  File "/home/<USER>/Documents/testF/2025/ser25/server0613/MySuiteDatebase2402newuigood-yolo5-3/server5/app/utils/server6_client.py", line 78, in _make_request
    response.raise_for_status()  # 如果状态码是 4xx 或 5xx，则抛出异常
    ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/miniconda3/envs/my_suite_unified/lib/python3.12/site-packages/aiohttp/client_reqrep.py", line 629, in raise_for_status
    raise ClientResponseError(
aiohttp.client_exceptions.ClientResponseError: 404, message='Not Found', url='http://************:8009/mdb/entries/602568'

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "/home/<USER>/Documents/testF/2025/ser25/server0613/MySuiteDatebase2402newuigood-yolo5-3/server5/app/services/f2_push_writer.py", line 272, in _handle_update_operation
    response = await self.server6_client.update_entry(external_id, entry_data)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Documents/testF/2025/ser25/server0613/MySuiteDatebase2402newuigood-yolo5-3/server5/app/utils/server6_client.py", line 249, in update_entry
    return await self._make_request('PUT', f'/mdb/entries/{external_id}', json_data=serializable_data)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Documents/testF/2025/ser25/server0613/MySuiteDatebase2402newuigood-yolo5-3/server5/app/utils/server6_client.py", line 94, in _make_request
    raise Exception(f"Server6请求返回错误 {e.status} (URL: {url}, Employee: {json_data.get('employee_id', 'N/A')}): {e.message}") from e
Exception: Server6请求返回错误 404 (URL: http://************:8009/mdb/entries/602568, Employee: N/A): Not Found
2025-06-30 13:11:12,286 - ERROR - ❌ UPDATE操作失败: Server6请求返回错误 404 (URL: http://************:8009/mdb/entries/602568, Employee: N/A): Not Found
Traceback (most recent call last):
  File "/home/<USER>/Documents/testF/2025/ser25/server0613/MySuiteDatebase2402newuigood-yolo5-3/server5/app/utils/server6_client.py", line 78, in _make_request
    response.raise_for_status()  # 如果状态码是 4xx 或 5xx，则抛出异常
    ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/miniconda3/envs/my_suite_unified/lib/python3.12/site-packages/aiohttp/client_reqrep.py", line 629, in raise_for_status
    raise ClientResponseError(
aiohttp.client_exceptions.ClientResponseError: 404, message='Not Found', url='http://************:8009/mdb/entries/602568'

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "/home/<USER>/Documents/testF/2025/ser25/server0613/MySuiteDatebase2402newuigood-yolo5-3/server5/app/services/f2_push_writer.py", line 272, in _handle_update_operation
    response = await self.server6_client.update_entry(external_id, entry_data)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Documents/testF/2025/ser25/server0613/MySuiteDatebase2402newuigood-yolo5-3/server5/app/utils/server6_client.py", line 249, in update_entry
    return await self._make_request('PUT', f'/mdb/entries/{external_id}', json_data=serializable_data)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Documents/testF/2025/ser25/server0613/MySuiteDatebase2402newuigood-yolo5-3/server5/app/utils/server6_client.py", line 94, in _make_request
    raise Exception(f"Server6请求返回错误 {e.status} (URL: {url}, Employee: {json_data.get('employee_id', 'N/A')}): {e.message}") from e
Exception: Server6请求返回错误 404 (URL: http://************:8009/mdb/entries/602568, Employee: N/A): Not Found
2025-06-30 13:11:12,286 - app.services.f2_push_writer - ERROR - ❌ UPDATE操作失败: Server6请求返回错误 404 (URL: http://************:8009/mdb/entries/602568, Employee: N/A): Not Found
Traceback (most recent call last):
  File "/home/<USER>/Documents/testF/2025/ser25/server0613/MySuiteDatebase2402newuigood-yolo5-3/server5/app/utils/server6_client.py", line 78, in _make_request
    response.raise_for_status()  # 如果状态码是 4xx 或 5xx，则抛出异常
    ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/miniconda3/envs/my_suite_unified/lib/python3.12/site-packages/aiohttp/client_reqrep.py", line 629, in raise_for_status
    raise ClientResponseError(
aiohttp.client_exceptions.ClientResponseError: 404, message='Not Found', url='http://************:8009/mdb/entries/602568'

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "/home/<USER>/Documents/testF/2025/ser25/server0613/MySuiteDatebase2402newuigood-yolo5-3/server5/app/services/f2_push_writer.py", line 272, in _handle_update_operation
    response = await self.server6_client.update_entry(external_id, entry_data)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Documents/testF/2025/ser25/server0613/MySuiteDatebase2402newuigood-yolo5-3/server5/app/utils/server6_client.py", line 249, in update_entry
    return await self._make_request('PUT', f'/mdb/entries/{external_id}', json_data=serializable_data)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Documents/testF/2025/ser25/server0613/MySuiteDatebase2402newuigood-yolo5-3/server5/app/utils/server6_client.py", line 94, in _make_request
    raise Exception(f"Server6请求返回错误 {e.status} (URL: {url}, Employee: {json_data.get('employee_id', 'N/A')}): {e.message}") from e
Exception: Server6请求返回错误 404 (URL: http://************:8009/mdb/entries/602568, Employee: N/A): Not Found
2025-06-30 13:11:12,287 - ERROR - ❌ 推送作业失败 8: Server6请求返回错误 404 (URL: http://************:8009/mdb/entries/602568, Employee: N/A): Not Found
2025-06-30 13:11:12,287 - app.services.f2_push_writer - ERROR - ❌ 推送作业失败 8: Server6请求返回错误 404 (URL: http://************:8009/mdb/entries/602568, Employee: N/A): Not Found
2025-06-30 13:11:12,287 - ERROR - ❌ 推送任务处理失败: Server6请求返回错误 404 (URL: http://************:8009/mdb/entries/602568, Employee: N/A): Not Found
2025-06-30 13:11:12,287 - app.services.f2_push_writer - ERROR - ❌ 推送任务处理失败: Server6请求返回错误 404 (URL: http://************:8009/mdb/entries/602568, Employee: N/A): Not Found
2025-06-30 13:11:12,288 - ERROR - ❌ 推送作业失败 9: Server6请求返回错误 404 (URL: http://************:8009/mdb/entries/602568, Employee: N/A): Not Found
2025-06-30 13:11:12,288 - app.services.f2_push_writer - ERROR - ❌ 推送作业失败 9: Server6请求返回错误 404 (URL: http://************:8009/mdb/entries/602568, Employee: N/A): Not Found
2025-06-30 13:11:12,288 - ERROR - ❌ 推送任务处理失败: Server6请求返回错误 404 (URL: http://************:8009/mdb/entries/602568, Employee: N/A): Not Found
2025-06-30 13:11:12,288 - app.services.f2_push_writer - ERROR - ❌ 推送任务处理失败: Server6请求返回错误 404 (URL: http://************:8009/mdb/entries/602568, Employee: N/A): Not Found
2025-06-30 13:11:12,289 - ERROR - ❌ 推送作业失败 10: Server6请求返回错误 404 (URL: http://************:8009/mdb/entries/602568, Employee: N/A): Not Found
2025-06-30 13:11:12,289 - app.services.f2_push_writer - ERROR - ❌ 推送作业失败 10: Server6请求返回错误 404 (URL: http://************:8009/mdb/entries/602568, Employee: N/A): Not Found
2025-06-30 13:11:12,289 - ERROR - ❌ 推送任务处理失败: Server6请求返回错误 404 (URL: http://************:8009/mdb/entries/602568, Employee: N/A): Not Found
2025-06-30 13:11:12,289 - app.services.f2_push_writer - ERROR - ❌ 推送任务处理失败: Server6请求返回错误 404 (URL: http://************:8009/mdb/entries/602568, Employee: N/A): Not Found
2025-06-30 13:11:35,232 - ERROR - ❌ Server6请求失败: PUT http://************:8009/mdb/entries/600557 - Status: 404, Message: Not Found. Request Body: {"entry_id": 384, "created_ts": "2025-06-30", "日付": "2025-05-31", "id": 384, "external_id": 600557, "ts": "2025-06-30", "従業員ｺｰﾄﾞ": "215236", "機種": "HPX150", "号機": "101", "工場製番": "HA0491", "工事番号": "", "ﾕﾆｯﾄ番号": "8000", "区分": 0, "項目": 7, "時間": 9.5, "所属ｺｰﾄﾞ": "131"}
2025-06-30 13:11:35,232 - app.utils.server6_client - ERROR - ❌ Server6请求失败: PUT http://************:8009/mdb/entries/600557 - Status: 404, Message: Not Found. Request Body: {"entry_id": 384, "created_ts": "2025-06-30", "日付": "2025-05-31", "id": 384, "external_id": 600557, "ts": "2025-06-30", "従業員ｺｰﾄﾞ": "215236", "機種": "HPX150", "号機": "101", "工場製番": "HA0491", "工事番号": "", "ﾕﾆｯﾄ番号": "8000", "区分": 0, "項目": 7, "時間": 9.5, "所属ｺｰﾄﾞ": "131"}
2025-06-30 13:11:35,233 - ERROR - ❌ Server6请求失败: PUT http://************:8009/mdb/entries/600557 - Status: 404, Message: Not Found. Request Body: {"entry_id": 384, "created_ts": "2025-06-30", "日付": "2025-05-31", "id": 384, "external_id": 600557, "ts": "2025-06-30", "従業員ｺｰﾄﾞ": "215236", "機種": "HPX150", "号機": "101", "工場製番": "HA0491", "工事番号": "", "ﾕﾆｯﾄ番号": "8000", "区分": 0, "項目": 7, "時間": 9.5, "所属ｺｰﾄﾞ": "131"}
2025-06-30 13:11:35,233 - app.utils.server6_client - ERROR - ❌ Server6请求失败: PUT http://************:8009/mdb/entries/600557 - Status: 404, Message: Not Found. Request Body: {"entry_id": 384, "created_ts": "2025-06-30", "日付": "2025-05-31", "id": 384, "external_id": 600557, "ts": "2025-06-30", "従業員ｺｰﾄﾞ": "215236", "機種": "HPX150", "号機": "101", "工場製番": "HA0491", "工事番号": "", "ﾕﾆｯﾄ番号": "8000", "区分": 0, "項目": 7, "時間": 9.5, "所属ｺｰﾄﾞ": "131"}
2025-06-30 13:11:35,234 - ERROR - ❌ UPDATE操作失败: Server6请求返回错误 404 (URL: http://************:8009/mdb/entries/600557, Employee: N/A): Not Found
Traceback (most recent call last):
  File "/home/<USER>/Documents/testF/2025/ser25/server0613/MySuiteDatebase2402newuigood-yolo5-3/server5/app/utils/server6_client.py", line 78, in _make_request
    response.raise_for_status()  # 如果状态码是 4xx 或 5xx，则抛出异常
    ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/miniconda3/envs/my_suite_unified/lib/python3.12/site-packages/aiohttp/client_reqrep.py", line 629, in raise_for_status
    raise ClientResponseError(
aiohttp.client_exceptions.ClientResponseError: 404, message='Not Found', url='http://************:8009/mdb/entries/600557'

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "/home/<USER>/Documents/testF/2025/ser25/server0613/MySuiteDatebase2402newuigood-yolo5-3/server5/app/services/f2_push_writer.py", line 272, in _handle_update_operation
    response = await self.server6_client.update_entry(external_id, entry_data)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Documents/testF/2025/ser25/server0613/MySuiteDatebase2402newuigood-yolo5-3/server5/app/utils/server6_client.py", line 249, in update_entry
    return await self._make_request('PUT', f'/mdb/entries/{external_id}', json_data=serializable_data)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Documents/testF/2025/ser25/server0613/MySuiteDatebase2402newuigood-yolo5-3/server5/app/utils/server6_client.py", line 94, in _make_request
    raise Exception(f"Server6请求返回错误 {e.status} (URL: {url}, Employee: {json_data.get('employee_id', 'N/A')}): {e.message}") from e
Exception: Server6请求返回错误 404 (URL: http://************:8009/mdb/entries/600557, Employee: N/A): Not Found
2025-06-30 13:11:35,234 - app.services.f2_push_writer - ERROR - ❌ UPDATE操作失败: Server6请求返回错误 404 (URL: http://************:8009/mdb/entries/600557, Employee: N/A): Not Found
Traceback (most recent call last):
  File "/home/<USER>/Documents/testF/2025/ser25/server0613/MySuiteDatebase2402newuigood-yolo5-3/server5/app/utils/server6_client.py", line 78, in _make_request
    response.raise_for_status()  # 如果状态码是 4xx 或 5xx，则抛出异常
    ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/miniconda3/envs/my_suite_unified/lib/python3.12/site-packages/aiohttp/client_reqrep.py", line 629, in raise_for_status
    raise ClientResponseError(
aiohttp.client_exceptions.ClientResponseError: 404, message='Not Found', url='http://************:8009/mdb/entries/600557'

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "/home/<USER>/Documents/testF/2025/ser25/server0613/MySuiteDatebase2402newuigood-yolo5-3/server5/app/services/f2_push_writer.py", line 272, in _handle_update_operation
    response = await self.server6_client.update_entry(external_id, entry_data)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Documents/testF/2025/ser25/server0613/MySuiteDatebase2402newuigood-yolo5-3/server5/app/utils/server6_client.py", line 249, in update_entry
    return await self._make_request('PUT', f'/mdb/entries/{external_id}', json_data=serializable_data)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Documents/testF/2025/ser25/server0613/MySuiteDatebase2402newuigood-yolo5-3/server5/app/utils/server6_client.py", line 94, in _make_request
    raise Exception(f"Server6请求返回错误 {e.status} (URL: {url}, Employee: {json_data.get('employee_id', 'N/A')}): {e.message}") from e
Exception: Server6请求返回错误 404 (URL: http://************:8009/mdb/entries/600557, Employee: N/A): Not Found
2025-06-30 13:11:35,235 - ERROR - ❌ UPDATE操作失败: Server6请求返回错误 404 (URL: http://************:8009/mdb/entries/600557, Employee: N/A): Not Found
Traceback (most recent call last):
  File "/home/<USER>/Documents/testF/2025/ser25/server0613/MySuiteDatebase2402newuigood-yolo5-3/server5/app/utils/server6_client.py", line 78, in _make_request
    response.raise_for_status()  # 如果状态码是 4xx 或 5xx，则抛出异常
    ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/miniconda3/envs/my_suite_unified/lib/python3.12/site-packages/aiohttp/client_reqrep.py", line 629, in raise_for_status
    raise ClientResponseError(
aiohttp.client_exceptions.ClientResponseError: 404, message='Not Found', url='http://************:8009/mdb/entries/600557'

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "/home/<USER>/Documents/testF/2025/ser25/server0613/MySuiteDatebase2402newuigood-yolo5-3/server5/app/services/f2_push_writer.py", line 272, in _handle_update_operation
    response = await self.server6_client.update_entry(external_id, entry_data)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Documents/testF/2025/ser25/server0613/MySuiteDatebase2402newuigood-yolo5-3/server5/app/utils/server6_client.py", line 249, in update_entry
    return await self._make_request('PUT', f'/mdb/entries/{external_id}', json_data=serializable_data)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Documents/testF/2025/ser25/server0613/MySuiteDatebase2402newuigood-yolo5-3/server5/app/utils/server6_client.py", line 94, in _make_request
    raise Exception(f"Server6请求返回错误 {e.status} (URL: {url}, Employee: {json_data.get('employee_id', 'N/A')}): {e.message}") from e
Exception: Server6请求返回错误 404 (URL: http://************:8009/mdb/entries/600557, Employee: N/A): Not Found
2025-06-30 13:11:35,235 - app.services.f2_push_writer - ERROR - ❌ UPDATE操作失败: Server6请求返回错误 404 (URL: http://************:8009/mdb/entries/600557, Employee: N/A): Not Found
Traceback (most recent call last):
  File "/home/<USER>/Documents/testF/2025/ser25/server0613/MySuiteDatebase2402newuigood-yolo5-3/server5/app/utils/server6_client.py", line 78, in _make_request
    response.raise_for_status()  # 如果状态码是 4xx 或 5xx，则抛出异常
    ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/miniconda3/envs/my_suite_unified/lib/python3.12/site-packages/aiohttp/client_reqrep.py", line 629, in raise_for_status
    raise ClientResponseError(
aiohttp.client_exceptions.ClientResponseError: 404, message='Not Found', url='http://************:8009/mdb/entries/600557'

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "/home/<USER>/Documents/testF/2025/ser25/server0613/MySuiteDatebase2402newuigood-yolo5-3/server5/app/services/f2_push_writer.py", line 272, in _handle_update_operation
    response = await self.server6_client.update_entry(external_id, entry_data)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Documents/testF/2025/ser25/server0613/MySuiteDatebase2402newuigood-yolo5-3/server5/app/utils/server6_client.py", line 249, in update_entry
    return await self._make_request('PUT', f'/mdb/entries/{external_id}', json_data=serializable_data)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Documents/testF/2025/ser25/server0613/MySuiteDatebase2402newuigood-yolo5-3/server5/app/utils/server6_client.py", line 94, in _make_request
    raise Exception(f"Server6请求返回错误 {e.status} (URL: {url}, Employee: {json_data.get('employee_id', 'N/A')}): {e.message}") from e
Exception: Server6请求返回错误 404 (URL: http://************:8009/mdb/entries/600557, Employee: N/A): Not Found
2025-06-30 13:11:35,237 - ERROR - ❌ 推送作业失败 19: Server6请求返回错误 404 (URL: http://************:8009/mdb/entries/600557, Employee: N/A): Not Found
2025-06-30 13:11:35,237 - app.services.f2_push_writer - ERROR - ❌ 推送作业失败 19: Server6请求返回错误 404 (URL: http://************:8009/mdb/entries/600557, Employee: N/A): Not Found
2025-06-30 13:11:35,237 - ERROR - ❌ 推送任务处理失败: Server6请求返回错误 404 (URL: http://************:8009/mdb/entries/600557, Employee: N/A): Not Found
2025-06-30 13:11:35,237 - app.services.f2_push_writer - ERROR - ❌ 推送任务处理失败: Server6请求返回错误 404 (URL: http://************:8009/mdb/entries/600557, Employee: N/A): Not Found
2025-06-30 13:11:35,238 - ERROR - ❌ 推送作业失败 20: Server6请求返回错误 404 (URL: http://************:8009/mdb/entries/600557, Employee: N/A): Not Found
2025-06-30 13:11:35,238 - app.services.f2_push_writer - ERROR - ❌ 推送作业失败 20: Server6请求返回错误 404 (URL: http://************:8009/mdb/entries/600557, Employee: N/A): Not Found
2025-06-30 13:11:35,238 - ERROR - ❌ 推送任务处理失败: Server6请求返回错误 404 (URL: http://************:8009/mdb/entries/600557, Employee: N/A): Not Found
2025-06-30 13:11:35,238 - app.services.f2_push_writer - ERROR - ❌ 推送任务处理失败: Server6请求返回错误 404 (URL: http://************:8009/mdb/entries/600557, Employee: N/A): Not Found
2025-06-30 13:11:38,684 - ERROR - ❌ Server6请求失败: PUT http://************:8009/mdb/entries/602001 - Status: 404, Message: Not Found. Request Body: {"entry_id": 5273, "created_ts": "2025-06-30", "日付": "2025-06-18", "id": 5273, "external_id": 602001, "ts": "2025-06-30", "従業員ｺｰﾄﾞ": "272204", "機種": "", "号機": "", "工場製番": "", "工事番号": "", "ﾕﾆｯﾄ番号": "", "区分": 9, "項目": 1, "時間": 4.0, "所属ｺｰﾄﾞ": "111"}
2025-06-30 13:11:38,684 - app.utils.server6_client - ERROR - ❌ Server6请求失败: PUT http://************:8009/mdb/entries/602001 - Status: 404, Message: Not Found. Request Body: {"entry_id": 5273, "created_ts": "2025-06-30", "日付": "2025-06-18", "id": 5273, "external_id": 602001, "ts": "2025-06-30", "従業員ｺｰﾄﾞ": "272204", "機種": "", "号機": "", "工場製番": "", "工事番号": "", "ﾕﾆｯﾄ番号": "", "区分": 9, "項目": 1, "時間": 4.0, "所属ｺｰﾄﾞ": "111"}
2025-06-30 13:11:38,685 - ERROR - ❌ Server6请求失败: PUT http://************:8009/mdb/entries/602001 - Status: 404, Message: Not Found. Request Body: {"entry_id": 5273, "created_ts": "2025-06-30", "日付": "2025-06-18", "id": 5273, "external_id": 602001, "ts": "2025-06-30", "従業員ｺｰﾄﾞ": "272204", "機種": "", "号機": "", "工場製番": "", "工事番号": "", "ﾕﾆｯﾄ番号": "", "区分": 9, "項目": 1, "時間": 4.0, "所属ｺｰﾄﾞ": "111"}
2025-06-30 13:11:38,685 - app.utils.server6_client - ERROR - ❌ Server6请求失败: PUT http://************:8009/mdb/entries/602001 - Status: 404, Message: Not Found. Request Body: {"entry_id": 5273, "created_ts": "2025-06-30", "日付": "2025-06-18", "id": 5273, "external_id": 602001, "ts": "2025-06-30", "従業員ｺｰﾄﾞ": "272204", "機種": "", "号機": "", "工場製番": "", "工事番号": "", "ﾕﾆｯﾄ番号": "", "区分": 9, "項目": 1, "時間": 4.0, "所属ｺｰﾄﾞ": "111"}
2025-06-30 13:11:38,686 - ERROR - ❌ Server6请求失败: PUT http://************:8009/mdb/entries/602001 - Status: 404, Message: Not Found. Request Body: {"entry_id": 5273, "created_ts": "2025-06-30", "日付": "2025-06-18", "id": 5273, "external_id": 602001, "ts": "2025-06-30", "従業員ｺｰﾄﾞ": "272204", "機種": "", "号機": "", "工場製番": "", "工事番号": "", "ﾕﾆｯﾄ番号": "", "区分": 9, "項目": 1, "時間": 4.0, "所属ｺｰﾄﾞ": "111"}
2025-06-30 13:11:38,686 - app.utils.server6_client - ERROR - ❌ Server6请求失败: PUT http://************:8009/mdb/entries/602001 - Status: 404, Message: Not Found. Request Body: {"entry_id": 5273, "created_ts": "2025-06-30", "日付": "2025-06-18", "id": 5273, "external_id": 602001, "ts": "2025-06-30", "従業員ｺｰﾄﾞ": "272204", "機種": "", "号機": "", "工場製番": "", "工事番号": "", "ﾕﾆｯﾄ番号": "", "区分": 9, "項目": 1, "時間": 4.0, "所属ｺｰﾄﾞ": "111"}
2025-06-30 13:11:38,686 - ERROR - ❌ UPDATE操作失败: Server6请求返回错误 404 (URL: http://************:8009/mdb/entries/602001, Employee: N/A): Not Found
Traceback (most recent call last):
  File "/home/<USER>/Documents/testF/2025/ser25/server0613/MySuiteDatebase2402newuigood-yolo5-3/server5/app/utils/server6_client.py", line 78, in _make_request
    response.raise_for_status()  # 如果状态码是 4xx 或 5xx，则抛出异常
    ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/miniconda3/envs/my_suite_unified/lib/python3.12/site-packages/aiohttp/client_reqrep.py", line 629, in raise_for_status
    raise ClientResponseError(
aiohttp.client_exceptions.ClientResponseError: 404, message='Not Found', url='http://************:8009/mdb/entries/602001'

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "/home/<USER>/Documents/testF/2025/ser25/server0613/MySuiteDatebase2402newuigood-yolo5-3/server5/app/services/f2_push_writer.py", line 272, in _handle_update_operation
    response = await self.server6_client.update_entry(external_id, entry_data)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Documents/testF/2025/ser25/server0613/MySuiteDatebase2402newuigood-yolo5-3/server5/app/utils/server6_client.py", line 249, in update_entry
    return await self._make_request('PUT', f'/mdb/entries/{external_id}', json_data=serializable_data)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Documents/testF/2025/ser25/server0613/MySuiteDatebase2402newuigood-yolo5-3/server5/app/utils/server6_client.py", line 94, in _make_request
    raise Exception(f"Server6请求返回错误 {e.status} (URL: {url}, Employee: {json_data.get('employee_id', 'N/A')}): {e.message}") from e
Exception: Server6请求返回错误 404 (URL: http://************:8009/mdb/entries/602001, Employee: N/A): Not Found
2025-06-30 13:11:38,686 - app.services.f2_push_writer - ERROR - ❌ UPDATE操作失败: Server6请求返回错误 404 (URL: http://************:8009/mdb/entries/602001, Employee: N/A): Not Found
Traceback (most recent call last):
  File "/home/<USER>/Documents/testF/2025/ser25/server0613/MySuiteDatebase2402newuigood-yolo5-3/server5/app/utils/server6_client.py", line 78, in _make_request
    response.raise_for_status()  # 如果状态码是 4xx 或 5xx，则抛出异常
    ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/miniconda3/envs/my_suite_unified/lib/python3.12/site-packages/aiohttp/client_reqrep.py", line 629, in raise_for_status
    raise ClientResponseError(
aiohttp.client_exceptions.ClientResponseError: 404, message='Not Found', url='http://************:8009/mdb/entries/602001'

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "/home/<USER>/Documents/testF/2025/ser25/server0613/MySuiteDatebase2402newuigood-yolo5-3/server5/app/services/f2_push_writer.py", line 272, in _handle_update_operation
    response = await self.server6_client.update_entry(external_id, entry_data)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Documents/testF/2025/ser25/server0613/MySuiteDatebase2402newuigood-yolo5-3/server5/app/utils/server6_client.py", line 249, in update_entry
    return await self._make_request('PUT', f'/mdb/entries/{external_id}', json_data=serializable_data)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Documents/testF/2025/ser25/server0613/MySuiteDatebase2402newuigood-yolo5-3/server5/app/utils/server6_client.py", line 94, in _make_request
    raise Exception(f"Server6请求返回错误 {e.status} (URL: {url}, Employee: {json_data.get('employee_id', 'N/A')}): {e.message}") from e
Exception: Server6请求返回错误 404 (URL: http://************:8009/mdb/entries/602001, Employee: N/A): Not Found
2025-06-30 13:11:38,687 - ERROR - ❌ UPDATE操作失败: Server6请求返回错误 404 (URL: http://************:8009/mdb/entries/602001, Employee: N/A): Not Found
Traceback (most recent call last):
  File "/home/<USER>/Documents/testF/2025/ser25/server0613/MySuiteDatebase2402newuigood-yolo5-3/server5/app/utils/server6_client.py", line 78, in _make_request
    response.raise_for_status()  # 如果状态码是 4xx 或 5xx，则抛出异常
    ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/miniconda3/envs/my_suite_unified/lib/python3.12/site-packages/aiohttp/client_reqrep.py", line 629, in raise_for_status
    raise ClientResponseError(
aiohttp.client_exceptions.ClientResponseError: 404, message='Not Found', url='http://************:8009/mdb/entries/602001'

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "/home/<USER>/Documents/testF/2025/ser25/server0613/MySuiteDatebase2402newuigood-yolo5-3/server5/app/services/f2_push_writer.py", line 272, in _handle_update_operation
    response = await self.server6_client.update_entry(external_id, entry_data)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Documents/testF/2025/ser25/server0613/MySuiteDatebase2402newuigood-yolo5-3/server5/app/utils/server6_client.py", line 249, in update_entry
    return await self._make_request('PUT', f'/mdb/entries/{external_id}', json_data=serializable_data)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Documents/testF/2025/ser25/server0613/MySuiteDatebase2402newuigood-yolo5-3/server5/app/utils/server6_client.py", line 94, in _make_request
    raise Exception(f"Server6请求返回错误 {e.status} (URL: {url}, Employee: {json_data.get('employee_id', 'N/A')}): {e.message}") from e
Exception: Server6请求返回错误 404 (URL: http://************:8009/mdb/entries/602001, Employee: N/A): Not Found
2025-06-30 13:11:38,687 - app.services.f2_push_writer - ERROR - ❌ UPDATE操作失败: Server6请求返回错误 404 (URL: http://************:8009/mdb/entries/602001, Employee: N/A): Not Found
Traceback (most recent call last):
  File "/home/<USER>/Documents/testF/2025/ser25/server0613/MySuiteDatebase2402newuigood-yolo5-3/server5/app/utils/server6_client.py", line 78, in _make_request
    response.raise_for_status()  # 如果状态码是 4xx 或 5xx，则抛出异常
    ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/miniconda3/envs/my_suite_unified/lib/python3.12/site-packages/aiohttp/client_reqrep.py", line 629, in raise_for_status
    raise ClientResponseError(
aiohttp.client_exceptions.ClientResponseError: 404, message='Not Found', url='http://************:8009/mdb/entries/602001'

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "/home/<USER>/Documents/testF/2025/ser25/server0613/MySuiteDatebase2402newuigood-yolo5-3/server5/app/services/f2_push_writer.py", line 272, in _handle_update_operation
    response = await self.server6_client.update_entry(external_id, entry_data)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Documents/testF/2025/ser25/server0613/MySuiteDatebase2402newuigood-yolo5-3/server5/app/utils/server6_client.py", line 249, in update_entry
    return await self._make_request('PUT', f'/mdb/entries/{external_id}', json_data=serializable_data)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Documents/testF/2025/ser25/server0613/MySuiteDatebase2402newuigood-yolo5-3/server5/app/utils/server6_client.py", line 94, in _make_request
    raise Exception(f"Server6请求返回错误 {e.status} (URL: {url}, Employee: {json_data.get('employee_id', 'N/A')}): {e.message}") from e
Exception: Server6请求返回错误 404 (URL: http://************:8009/mdb/entries/602001, Employee: N/A): Not Found
2025-06-30 13:11:38,688 - ERROR - ❌ UPDATE操作失败: Server6请求返回错误 404 (URL: http://************:8009/mdb/entries/602001, Employee: N/A): Not Found
Traceback (most recent call last):
  File "/home/<USER>/Documents/testF/2025/ser25/server0613/MySuiteDatebase2402newuigood-yolo5-3/server5/app/utils/server6_client.py", line 78, in _make_request
    response.raise_for_status()  # 如果状态码是 4xx 或 5xx，则抛出异常
    ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/miniconda3/envs/my_suite_unified/lib/python3.12/site-packages/aiohttp/client_reqrep.py", line 629, in raise_for_status
    raise ClientResponseError(
aiohttp.client_exceptions.ClientResponseError: 404, message='Not Found', url='http://************:8009/mdb/entries/602001'

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "/home/<USER>/Documents/testF/2025/ser25/server0613/MySuiteDatebase2402newuigood-yolo5-3/server5/app/services/f2_push_writer.py", line 272, in _handle_update_operation
    response = await self.server6_client.update_entry(external_id, entry_data)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Documents/testF/2025/ser25/server0613/MySuiteDatebase2402newuigood-yolo5-3/server5/app/utils/server6_client.py", line 249, in update_entry
    return await self._make_request('PUT', f'/mdb/entries/{external_id}', json_data=serializable_data)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Documents/testF/2025/ser25/server0613/MySuiteDatebase2402newuigood-yolo5-3/server5/app/utils/server6_client.py", line 94, in _make_request
    raise Exception(f"Server6请求返回错误 {e.status} (URL: {url}, Employee: {json_data.get('employee_id', 'N/A')}): {e.message}") from e
Exception: Server6请求返回错误 404 (URL: http://************:8009/mdb/entries/602001, Employee: N/A): Not Found
2025-06-30 13:11:38,688 - app.services.f2_push_writer - ERROR - ❌ UPDATE操作失败: Server6请求返回错误 404 (URL: http://************:8009/mdb/entries/602001, Employee: N/A): Not Found
Traceback (most recent call last):
  File "/home/<USER>/Documents/testF/2025/ser25/server0613/MySuiteDatebase2402newuigood-yolo5-3/server5/app/utils/server6_client.py", line 78, in _make_request
    response.raise_for_status()  # 如果状态码是 4xx 或 5xx，则抛出异常
    ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/miniconda3/envs/my_suite_unified/lib/python3.12/site-packages/aiohttp/client_reqrep.py", line 629, in raise_for_status
    raise ClientResponseError(
aiohttp.client_exceptions.ClientResponseError: 404, message='Not Found', url='http://************:8009/mdb/entries/602001'

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "/home/<USER>/Documents/testF/2025/ser25/server0613/MySuiteDatebase2402newuigood-yolo5-3/server5/app/services/f2_push_writer.py", line 272, in _handle_update_operation
    response = await self.server6_client.update_entry(external_id, entry_data)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Documents/testF/2025/ser25/server0613/MySuiteDatebase2402newuigood-yolo5-3/server5/app/utils/server6_client.py", line 249, in update_entry
    return await self._make_request('PUT', f'/mdb/entries/{external_id}', json_data=serializable_data)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Documents/testF/2025/ser25/server0613/MySuiteDatebase2402newuigood-yolo5-3/server5/app/utils/server6_client.py", line 94, in _make_request
    raise Exception(f"Server6请求返回错误 {e.status} (URL: {url}, Employee: {json_data.get('employee_id', 'N/A')}): {e.message}") from e
Exception: Server6请求返回错误 404 (URL: http://************:8009/mdb/entries/602001, Employee: N/A): Not Found
2025-06-30 13:11:38,689 - ERROR - ❌ 推送作业失败 23: Server6请求返回错误 404 (URL: http://************:8009/mdb/entries/602001, Employee: N/A): Not Found
2025-06-30 13:11:38,689 - app.services.f2_push_writer - ERROR - ❌ 推送作业失败 23: Server6请求返回错误 404 (URL: http://************:8009/mdb/entries/602001, Employee: N/A): Not Found
2025-06-30 13:11:38,689 - ERROR - ❌ 推送任务处理失败: Server6请求返回错误 404 (URL: http://************:8009/mdb/entries/602001, Employee: N/A): Not Found
2025-06-30 13:11:38,689 - app.services.f2_push_writer - ERROR - ❌ 推送任务处理失败: Server6请求返回错误 404 (URL: http://************:8009/mdb/entries/602001, Employee: N/A): Not Found
2025-06-30 13:11:38,690 - ERROR - ❌ 推送作业失败 21: Server6请求返回错误 404 (URL: http://************:8009/mdb/entries/602001, Employee: N/A): Not Found
2025-06-30 13:11:38,690 - app.services.f2_push_writer - ERROR - ❌ 推送作业失败 21: Server6请求返回错误 404 (URL: http://************:8009/mdb/entries/602001, Employee: N/A): Not Found
2025-06-30 13:11:38,690 - ERROR - ❌ 推送任务处理失败: Server6请求返回错误 404 (URL: http://************:8009/mdb/entries/602001, Employee: N/A): Not Found
2025-06-30 13:11:38,690 - app.services.f2_push_writer - ERROR - ❌ 推送任务处理失败: Server6请求返回错误 404 (URL: http://************:8009/mdb/entries/602001, Employee: N/A): Not Found
2025-06-30 13:11:38,690 - ERROR - ❌ 推送作业失败 22: Server6请求返回错误 404 (URL: http://************:8009/mdb/entries/602001, Employee: N/A): Not Found
2025-06-30 13:11:38,690 - app.services.f2_push_writer - ERROR - ❌ 推送作业失败 22: Server6请求返回错误 404 (URL: http://************:8009/mdb/entries/602001, Employee: N/A): Not Found
2025-06-30 13:11:38,690 - ERROR - ❌ 推送任务处理失败: Server6请求返回错误 404 (URL: http://************:8009/mdb/entries/602001, Employee: N/A): Not Found
2025-06-30 13:11:38,690 - app.services.f2_push_writer - ERROR - ❌ 推送任务处理失败: Server6请求返回错误 404 (URL: http://************:8009/mdb/entries/602001, Employee: N/A): Not Found
2025-06-30 13:11:38,701 - ERROR - ❌ Server6请求失败: PUT http://************:8009/mdb/entries/602002 - Status: 404, Message: Not Found. Request Body: {"entry_id": 5274, "created_ts": "2025-06-30", "日付": "2025-06-19", "id": 5274, "external_id": 602002, "ts": "2025-06-30", "従業員ｺｰﾄﾞ": "272204", "機種": "", "号機": "", "工場製番": "", "工事番号": "", "ﾕﾆｯﾄ番号": "", "区分": 9, "項目": 1, "時間": 2.5, "所属ｺｰﾄﾞ": "111"}
2025-06-30 13:11:38,701 - app.utils.server6_client - ERROR - ❌ Server6请求失败: PUT http://************:8009/mdb/entries/602002 - Status: 404, Message: Not Found. Request Body: {"entry_id": 5274, "created_ts": "2025-06-30", "日付": "2025-06-19", "id": 5274, "external_id": 602002, "ts": "2025-06-30", "従業員ｺｰﾄﾞ": "272204", "機種": "", "号機": "", "工場製番": "", "工事番号": "", "ﾕﾆｯﾄ番号": "", "区分": 9, "項目": 1, "時間": 2.5, "所属ｺｰﾄﾞ": "111"}
2025-06-30 13:11:38,703 - ERROR - ❌ UPDATE操作失败: Server6请求返回错误 404 (URL: http://************:8009/mdb/entries/602002, Employee: N/A): Not Found
Traceback (most recent call last):
  File "/home/<USER>/Documents/testF/2025/ser25/server0613/MySuiteDatebase2402newuigood-yolo5-3/server5/app/utils/server6_client.py", line 78, in _make_request
    response.raise_for_status()  # 如果状态码是 4xx 或 5xx，则抛出异常
    ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/miniconda3/envs/my_suite_unified/lib/python3.12/site-packages/aiohttp/client_reqrep.py", line 629, in raise_for_status
    raise ClientResponseError(
aiohttp.client_exceptions.ClientResponseError: 404, message='Not Found', url='http://************:8009/mdb/entries/602002'

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "/home/<USER>/Documents/testF/2025/ser25/server0613/MySuiteDatebase2402newuigood-yolo5-3/server5/app/services/f2_push_writer.py", line 272, in _handle_update_operation
    response = await self.server6_client.update_entry(external_id, entry_data)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Documents/testF/2025/ser25/server0613/MySuiteDatebase2402newuigood-yolo5-3/server5/app/utils/server6_client.py", line 249, in update_entry
    return await self._make_request('PUT', f'/mdb/entries/{external_id}', json_data=serializable_data)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Documents/testF/2025/ser25/server0613/MySuiteDatebase2402newuigood-yolo5-3/server5/app/utils/server6_client.py", line 94, in _make_request
    raise Exception(f"Server6请求返回错误 {e.status} (URL: {url}, Employee: {json_data.get('employee_id', 'N/A')}): {e.message}") from e
Exception: Server6请求返回错误 404 (URL: http://************:8009/mdb/entries/602002, Employee: N/A): Not Found
2025-06-30 13:11:38,703 - app.services.f2_push_writer - ERROR - ❌ UPDATE操作失败: Server6请求返回错误 404 (URL: http://************:8009/mdb/entries/602002, Employee: N/A): Not Found
Traceback (most recent call last):
  File "/home/<USER>/Documents/testF/2025/ser25/server0613/MySuiteDatebase2402newuigood-yolo5-3/server5/app/utils/server6_client.py", line 78, in _make_request
    response.raise_for_status()  # 如果状态码是 4xx 或 5xx，则抛出异常
    ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/miniconda3/envs/my_suite_unified/lib/python3.12/site-packages/aiohttp/client_reqrep.py", line 629, in raise_for_status
    raise ClientResponseError(
aiohttp.client_exceptions.ClientResponseError: 404, message='Not Found', url='http://************:8009/mdb/entries/602002'

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "/home/<USER>/Documents/testF/2025/ser25/server0613/MySuiteDatebase2402newuigood-yolo5-3/server5/app/services/f2_push_writer.py", line 272, in _handle_update_operation
    response = await self.server6_client.update_entry(external_id, entry_data)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Documents/testF/2025/ser25/server0613/MySuiteDatebase2402newuigood-yolo5-3/server5/app/utils/server6_client.py", line 249, in update_entry
    return await self._make_request('PUT', f'/mdb/entries/{external_id}', json_data=serializable_data)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Documents/testF/2025/ser25/server0613/MySuiteDatebase2402newuigood-yolo5-3/server5/app/utils/server6_client.py", line 94, in _make_request
    raise Exception(f"Server6请求返回错误 {e.status} (URL: {url}, Employee: {json_data.get('employee_id', 'N/A')}): {e.message}") from e
Exception: Server6请求返回错误 404 (URL: http://************:8009/mdb/entries/602002, Employee: N/A): Not Found
2025-06-30 13:11:38,704 - ERROR - ❌ Server6请求失败: PUT http://************:8009/mdb/entries/602002 - Status: 404, Message: Not Found. Request Body: {"entry_id": 5274, "created_ts": "2025-06-30", "日付": "2025-06-19", "id": 5274, "external_id": 602002, "ts": "2025-06-30", "従業員ｺｰﾄﾞ": "272204", "機種": "", "号機": "", "工場製番": "", "工事番号": "", "ﾕﾆｯﾄ番号": "", "区分": 9, "項目": 1, "時間": 2.5, "所属ｺｰﾄﾞ": "111"}
2025-06-30 13:11:38,704 - app.utils.server6_client - ERROR - ❌ Server6请求失败: PUT http://************:8009/mdb/entries/602002 - Status: 404, Message: Not Found. Request Body: {"entry_id": 5274, "created_ts": "2025-06-30", "日付": "2025-06-19", "id": 5274, "external_id": 602002, "ts": "2025-06-30", "従業員ｺｰﾄﾞ": "272204", "機種": "", "号機": "", "工場製番": "", "工事番号": "", "ﾕﾆｯﾄ番号": "", "区分": 9, "項目": 1, "時間": 2.5, "所属ｺｰﾄﾞ": "111"}
2025-06-30 13:11:38,705 - ERROR - ❌ Server6请求失败: PUT http://************:8009/mdb/entries/602002 - Status: 404, Message: Not Found. Request Body: {"entry_id": 5274, "created_ts": "2025-06-30", "日付": "2025-06-19", "id": 5274, "external_id": 602002, "ts": "2025-06-30", "従業員ｺｰﾄﾞ": "272204", "機種": "", "号機": "", "工場製番": "", "工事番号": "", "ﾕﾆｯﾄ番号": "", "区分": 9, "項目": 1, "時間": 2.5, "所属ｺｰﾄﾞ": "111"}
2025-06-30 13:11:38,705 - app.utils.server6_client - ERROR - ❌ Server6请求失败: PUT http://************:8009/mdb/entries/602002 - Status: 404, Message: Not Found. Request Body: {"entry_id": 5274, "created_ts": "2025-06-30", "日付": "2025-06-19", "id": 5274, "external_id": 602002, "ts": "2025-06-30", "従業員ｺｰﾄﾞ": "272204", "機種": "", "号機": "", "工場製番": "", "工事番号": "", "ﾕﾆｯﾄ番号": "", "区分": 9, "項目": 1, "時間": 2.5, "所属ｺｰﾄﾞ": "111"}
2025-06-30 13:11:38,705 - ERROR - ❌ 推送作业失败 24: Server6请求返回错误 404 (URL: http://************:8009/mdb/entries/602002, Employee: N/A): Not Found
2025-06-30 13:11:38,705 - app.services.f2_push_writer - ERROR - ❌ 推送作业失败 24: Server6请求返回错误 404 (URL: http://************:8009/mdb/entries/602002, Employee: N/A): Not Found
2025-06-30 13:11:38,705 - ERROR - ❌ 推送任务处理失败: Server6请求返回错误 404 (URL: http://************:8009/mdb/entries/602002, Employee: N/A): Not Found
2025-06-30 13:11:38,705 - app.services.f2_push_writer - ERROR - ❌ 推送任务处理失败: Server6请求返回错误 404 (URL: http://************:8009/mdb/entries/602002, Employee: N/A): Not Found
2025-06-30 13:11:38,706 - ERROR - ❌ UPDATE操作失败: Server6请求返回错误 404 (URL: http://************:8009/mdb/entries/602002, Employee: N/A): Not Found
Traceback (most recent call last):
  File "/home/<USER>/Documents/testF/2025/ser25/server0613/MySuiteDatebase2402newuigood-yolo5-3/server5/app/utils/server6_client.py", line 78, in _make_request
    response.raise_for_status()  # 如果状态码是 4xx 或 5xx，则抛出异常
    ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/miniconda3/envs/my_suite_unified/lib/python3.12/site-packages/aiohttp/client_reqrep.py", line 629, in raise_for_status
    raise ClientResponseError(
aiohttp.client_exceptions.ClientResponseError: 404, message='Not Found', url='http://************:8009/mdb/entries/602002'

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "/home/<USER>/Documents/testF/2025/ser25/server0613/MySuiteDatebase2402newuigood-yolo5-3/server5/app/services/f2_push_writer.py", line 272, in _handle_update_operation
    response = await self.server6_client.update_entry(external_id, entry_data)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Documents/testF/2025/ser25/server0613/MySuiteDatebase2402newuigood-yolo5-3/server5/app/utils/server6_client.py", line 249, in update_entry
    return await self._make_request('PUT', f'/mdb/entries/{external_id}', json_data=serializable_data)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Documents/testF/2025/ser25/server0613/MySuiteDatebase2402newuigood-yolo5-3/server5/app/utils/server6_client.py", line 94, in _make_request
    raise Exception(f"Server6请求返回错误 {e.status} (URL: {url}, Employee: {json_data.get('employee_id', 'N/A')}): {e.message}") from e
Exception: Server6请求返回错误 404 (URL: http://************:8009/mdb/entries/602002, Employee: N/A): Not Found
2025-06-30 13:11:38,706 - app.services.f2_push_writer - ERROR - ❌ UPDATE操作失败: Server6请求返回错误 404 (URL: http://************:8009/mdb/entries/602002, Employee: N/A): Not Found
Traceback (most recent call last):
  File "/home/<USER>/Documents/testF/2025/ser25/server0613/MySuiteDatebase2402newuigood-yolo5-3/server5/app/utils/server6_client.py", line 78, in _make_request
    response.raise_for_status()  # 如果状态码是 4xx 或 5xx，则抛出异常
    ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/miniconda3/envs/my_suite_unified/lib/python3.12/site-packages/aiohttp/client_reqrep.py", line 629, in raise_for_status
    raise ClientResponseError(
aiohttp.client_exceptions.ClientResponseError: 404, message='Not Found', url='http://************:8009/mdb/entries/602002'

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "/home/<USER>/Documents/testF/2025/ser25/server0613/MySuiteDatebase2402newuigood-yolo5-3/server5/app/services/f2_push_writer.py", line 272, in _handle_update_operation
    response = await self.server6_client.update_entry(external_id, entry_data)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Documents/testF/2025/ser25/server0613/MySuiteDatebase2402newuigood-yolo5-3/server5/app/utils/server6_client.py", line 249, in update_entry
    return await self._make_request('PUT', f'/mdb/entries/{external_id}', json_data=serializable_data)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Documents/testF/2025/ser25/server0613/MySuiteDatebase2402newuigood-yolo5-3/server5/app/utils/server6_client.py", line 94, in _make_request
    raise Exception(f"Server6请求返回错误 {e.status} (URL: {url}, Employee: {json_data.get('employee_id', 'N/A')}): {e.message}") from e
Exception: Server6请求返回错误 404 (URL: http://************:8009/mdb/entries/602002, Employee: N/A): Not Found
2025-06-30 13:11:38,707 - ERROR - ❌ UPDATE操作失败: Server6请求返回错误 404 (URL: http://************:8009/mdb/entries/602002, Employee: N/A): Not Found
Traceback (most recent call last):
  File "/home/<USER>/Documents/testF/2025/ser25/server0613/MySuiteDatebase2402newuigood-yolo5-3/server5/app/utils/server6_client.py", line 78, in _make_request
    response.raise_for_status()  # 如果状态码是 4xx 或 5xx，则抛出异常
    ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/miniconda3/envs/my_suite_unified/lib/python3.12/site-packages/aiohttp/client_reqrep.py", line 629, in raise_for_status
    raise ClientResponseError(
aiohttp.client_exceptions.ClientResponseError: 404, message='Not Found', url='http://************:8009/mdb/entries/602002'

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "/home/<USER>/Documents/testF/2025/ser25/server0613/MySuiteDatebase2402newuigood-yolo5-3/server5/app/services/f2_push_writer.py", line 272, in _handle_update_operation
    response = await self.server6_client.update_entry(external_id, entry_data)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Documents/testF/2025/ser25/server0613/MySuiteDatebase2402newuigood-yolo5-3/server5/app/utils/server6_client.py", line 249, in update_entry
    return await self._make_request('PUT', f'/mdb/entries/{external_id}', json_data=serializable_data)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Documents/testF/2025/ser25/server0613/MySuiteDatebase2402newuigood-yolo5-3/server5/app/utils/server6_client.py", line 94, in _make_request
    raise Exception(f"Server6请求返回错误 {e.status} (URL: {url}, Employee: {json_data.get('employee_id', 'N/A')}): {e.message}") from e
Exception: Server6请求返回错误 404 (URL: http://************:8009/mdb/entries/602002, Employee: N/A): Not Found
2025-06-30 13:11:38,707 - app.services.f2_push_writer - ERROR - ❌ UPDATE操作失败: Server6请求返回错误 404 (URL: http://************:8009/mdb/entries/602002, Employee: N/A): Not Found
Traceback (most recent call last):
  File "/home/<USER>/Documents/testF/2025/ser25/server0613/MySuiteDatebase2402newuigood-yolo5-3/server5/app/utils/server6_client.py", line 78, in _make_request
    response.raise_for_status()  # 如果状态码是 4xx 或 5xx，则抛出异常
    ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/miniconda3/envs/my_suite_unified/lib/python3.12/site-packages/aiohttp/client_reqrep.py", line 629, in raise_for_status
    raise ClientResponseError(
aiohttp.client_exceptions.ClientResponseError: 404, message='Not Found', url='http://************:8009/mdb/entries/602002'

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "/home/<USER>/Documents/testF/2025/ser25/server0613/MySuiteDatebase2402newuigood-yolo5-3/server5/app/services/f2_push_writer.py", line 272, in _handle_update_operation
    response = await self.server6_client.update_entry(external_id, entry_data)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Documents/testF/2025/ser25/server0613/MySuiteDatebase2402newuigood-yolo5-3/server5/app/utils/server6_client.py", line 249, in update_entry
    return await self._make_request('PUT', f'/mdb/entries/{external_id}', json_data=serializable_data)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Documents/testF/2025/ser25/server0613/MySuiteDatebase2402newuigood-yolo5-3/server5/app/utils/server6_client.py", line 94, in _make_request
    raise Exception(f"Server6请求返回错误 {e.status} (URL: {url}, Employee: {json_data.get('employee_id', 'N/A')}): {e.message}") from e
Exception: Server6请求返回错误 404 (URL: http://************:8009/mdb/entries/602002, Employee: N/A): Not Found
2025-06-30 13:11:38,708 - ERROR - ❌ 推送作业失败 25: Server6请求返回错误 404 (URL: http://************:8009/mdb/entries/602002, Employee: N/A): Not Found
2025-06-30 13:11:38,708 - app.services.f2_push_writer - ERROR - ❌ 推送作业失败 25: Server6请求返回错误 404 (URL: http://************:8009/mdb/entries/602002, Employee: N/A): Not Found
2025-06-30 13:11:38,708 - ERROR - ❌ 推送任务处理失败: Server6请求返回错误 404 (URL: http://************:8009/mdb/entries/602002, Employee: N/A): Not Found
2025-06-30 13:11:38,708 - app.services.f2_push_writer - ERROR - ❌ 推送任务处理失败: Server6请求返回错误 404 (URL: http://************:8009/mdb/entries/602002, Employee: N/A): Not Found
2025-06-30 13:11:38,710 - ERROR - ❌ 推送作业失败 26: Server6请求返回错误 404 (URL: http://************:8009/mdb/entries/602002, Employee: N/A): Not Found
2025-06-30 13:11:38,710 - app.services.f2_push_writer - ERROR - ❌ 推送作业失败 26: Server6请求返回错误 404 (URL: http://************:8009/mdb/entries/602002, Employee: N/A): Not Found
2025-06-30 13:11:38,710 - ERROR - ❌ 推送任务处理失败: Server6请求返回错误 404 (URL: http://************:8009/mdb/entries/602002, Employee: N/A): Not Found
2025-06-30 13:11:38,710 - app.services.f2_push_writer - ERROR - ❌ 推送任务处理失败: Server6请求返回错误 404 (URL: http://************:8009/mdb/entries/602002, Employee: N/A): Not Found
2025-06-30 13:11:38,716 - ERROR - ❌ Server6请求失败: PUT http://************:8009/mdb/entries/602003 - Status: 404, Message: Not Found. Request Body: {"entry_id": 5275, "created_ts": "2025-06-30", "日付": "2025-06-20", "id": 5275, "external_id": 602003, "ts": "2025-06-30", "従業員ｺｰﾄﾞ": "272204", "機種": "", "号機": "", "工場製番": "", "工事番号": "", "ﾕﾆｯﾄ番号": "", "区分": 9, "項目": 1, "時間": 3.0, "所属ｺｰﾄﾞ": "111"}
2025-06-30 13:11:38,716 - app.utils.server6_client - ERROR - ❌ Server6请求失败: PUT http://************:8009/mdb/entries/602003 - Status: 404, Message: Not Found. Request Body: {"entry_id": 5275, "created_ts": "2025-06-30", "日付": "2025-06-20", "id": 5275, "external_id": 602003, "ts": "2025-06-30", "従業員ｺｰﾄﾞ": "272204", "機種": "", "号機": "", "工場製番": "", "工事番号": "", "ﾕﾆｯﾄ番号": "", "区分": 9, "項目": 1, "時間": 3.0, "所属ｺｰﾄﾞ": "111"}
2025-06-30 13:11:38,718 - ERROR - ❌ Server6请求失败: PUT http://************:8009/mdb/entries/602003 - Status: 404, Message: Not Found. Request Body: {"entry_id": 5275, "created_ts": "2025-06-30", "日付": "2025-06-20", "id": 5275, "external_id": 602003, "ts": "2025-06-30", "従業員ｺｰﾄﾞ": "272204", "機種": "", "号機": "", "工場製番": "", "工事番号": "", "ﾕﾆｯﾄ番号": "", "区分": 9, "項目": 1, "時間": 3.0, "所属ｺｰﾄﾞ": "111"}
2025-06-30 13:11:38,718 - app.utils.server6_client - ERROR - ❌ Server6请求失败: PUT http://************:8009/mdb/entries/602003 - Status: 404, Message: Not Found. Request Body: {"entry_id": 5275, "created_ts": "2025-06-30", "日付": "2025-06-20", "id": 5275, "external_id": 602003, "ts": "2025-06-30", "従業員ｺｰﾄﾞ": "272204", "機種": "", "号機": "", "工場製番": "", "工事番号": "", "ﾕﾆｯﾄ番号": "", "区分": 9, "項目": 1, "時間": 3.0, "所属ｺｰﾄﾞ": "111"}
2025-06-30 13:11:38,718 - ERROR - ❌ UPDATE操作失败: Server6请求返回错误 404 (URL: http://************:8009/mdb/entries/602003, Employee: N/A): Not Found
Traceback (most recent call last):
  File "/home/<USER>/Documents/testF/2025/ser25/server0613/MySuiteDatebase2402newuigood-yolo5-3/server5/app/utils/server6_client.py", line 78, in _make_request
    response.raise_for_status()  # 如果状态码是 4xx 或 5xx，则抛出异常
    ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/miniconda3/envs/my_suite_unified/lib/python3.12/site-packages/aiohttp/client_reqrep.py", line 629, in raise_for_status
    raise ClientResponseError(
aiohttp.client_exceptions.ClientResponseError: 404, message='Not Found', url='http://************:8009/mdb/entries/602003'

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "/home/<USER>/Documents/testF/2025/ser25/server0613/MySuiteDatebase2402newuigood-yolo5-3/server5/app/services/f2_push_writer.py", line 272, in _handle_update_operation
    response = await self.server6_client.update_entry(external_id, entry_data)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Documents/testF/2025/ser25/server0613/MySuiteDatebase2402newuigood-yolo5-3/server5/app/utils/server6_client.py", line 249, in update_entry
    return await self._make_request('PUT', f'/mdb/entries/{external_id}', json_data=serializable_data)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Documents/testF/2025/ser25/server0613/MySuiteDatebase2402newuigood-yolo5-3/server5/app/utils/server6_client.py", line 94, in _make_request
    raise Exception(f"Server6请求返回错误 {e.status} (URL: {url}, Employee: {json_data.get('employee_id', 'N/A')}): {e.message}") from e
Exception: Server6请求返回错误 404 (URL: http://************:8009/mdb/entries/602003, Employee: N/A): Not Found
2025-06-30 13:11:38,718 - app.services.f2_push_writer - ERROR - ❌ UPDATE操作失败: Server6请求返回错误 404 (URL: http://************:8009/mdb/entries/602003, Employee: N/A): Not Found
Traceback (most recent call last):
  File "/home/<USER>/Documents/testF/2025/ser25/server0613/MySuiteDatebase2402newuigood-yolo5-3/server5/app/utils/server6_client.py", line 78, in _make_request
    response.raise_for_status()  # 如果状态码是 4xx 或 5xx，则抛出异常
    ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/miniconda3/envs/my_suite_unified/lib/python3.12/site-packages/aiohttp/client_reqrep.py", line 629, in raise_for_status
    raise ClientResponseError(
aiohttp.client_exceptions.ClientResponseError: 404, message='Not Found', url='http://************:8009/mdb/entries/602003'

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "/home/<USER>/Documents/testF/2025/ser25/server0613/MySuiteDatebase2402newuigood-yolo5-3/server5/app/services/f2_push_writer.py", line 272, in _handle_update_operation
    response = await self.server6_client.update_entry(external_id, entry_data)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Documents/testF/2025/ser25/server0613/MySuiteDatebase2402newuigood-yolo5-3/server5/app/utils/server6_client.py", line 249, in update_entry
    return await self._make_request('PUT', f'/mdb/entries/{external_id}', json_data=serializable_data)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Documents/testF/2025/ser25/server0613/MySuiteDatebase2402newuigood-yolo5-3/server5/app/utils/server6_client.py", line 94, in _make_request
    raise Exception(f"Server6请求返回错误 {e.status} (URL: {url}, Employee: {json_data.get('employee_id', 'N/A')}): {e.message}") from e
Exception: Server6请求返回错误 404 (URL: http://************:8009/mdb/entries/602003, Employee: N/A): Not Found
2025-06-30 13:11:38,719 - ERROR - ❌ Server6请求失败: PUT http://************:8009/mdb/entries/602003 - Status: 404, Message: Not Found. Request Body: {"entry_id": 5275, "created_ts": "2025-06-30", "日付": "2025-06-20", "id": 5275, "external_id": 602003, "ts": "2025-06-30", "従業員ｺｰﾄﾞ": "272204", "機種": "", "号機": "", "工場製番": "", "工事番号": "", "ﾕﾆｯﾄ番号": "", "区分": 9, "項目": 1, "時間": 3.0, "所属ｺｰﾄﾞ": "111"}
2025-06-30 13:11:38,719 - app.utils.server6_client - ERROR - ❌ Server6请求失败: PUT http://************:8009/mdb/entries/602003 - Status: 404, Message: Not Found. Request Body: {"entry_id": 5275, "created_ts": "2025-06-30", "日付": "2025-06-20", "id": 5275, "external_id": 602003, "ts": "2025-06-30", "従業員ｺｰﾄﾞ": "272204", "機種": "", "号機": "", "工場製番": "", "工事番号": "", "ﾕﾆｯﾄ番号": "", "区分": 9, "項目": 1, "時間": 3.0, "所属ｺｰﾄﾞ": "111"}
2025-06-30 13:11:38,719 - ERROR - ❌ UPDATE操作失败: Server6请求返回错误 404 (URL: http://************:8009/mdb/entries/602003, Employee: N/A): Not Found
Traceback (most recent call last):
  File "/home/<USER>/Documents/testF/2025/ser25/server0613/MySuiteDatebase2402newuigood-yolo5-3/server5/app/utils/server6_client.py", line 78, in _make_request
    response.raise_for_status()  # 如果状态码是 4xx 或 5xx，则抛出异常
    ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/miniconda3/envs/my_suite_unified/lib/python3.12/site-packages/aiohttp/client_reqrep.py", line 629, in raise_for_status
    raise ClientResponseError(
aiohttp.client_exceptions.ClientResponseError: 404, message='Not Found', url='http://************:8009/mdb/entries/602003'

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "/home/<USER>/Documents/testF/2025/ser25/server0613/MySuiteDatebase2402newuigood-yolo5-3/server5/app/services/f2_push_writer.py", line 272, in _handle_update_operation
    response = await self.server6_client.update_entry(external_id, entry_data)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Documents/testF/2025/ser25/server0613/MySuiteDatebase2402newuigood-yolo5-3/server5/app/utils/server6_client.py", line 249, in update_entry
    return await self._make_request('PUT', f'/mdb/entries/{external_id}', json_data=serializable_data)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Documents/testF/2025/ser25/server0613/MySuiteDatebase2402newuigood-yolo5-3/server5/app/utils/server6_client.py", line 94, in _make_request
    raise Exception(f"Server6请求返回错误 {e.status} (URL: {url}, Employee: {json_data.get('employee_id', 'N/A')}): {e.message}") from e
Exception: Server6请求返回错误 404 (URL: http://************:8009/mdb/entries/602003, Employee: N/A): Not Found
2025-06-30 13:11:38,719 - app.services.f2_push_writer - ERROR - ❌ UPDATE操作失败: Server6请求返回错误 404 (URL: http://************:8009/mdb/entries/602003, Employee: N/A): Not Found
Traceback (most recent call last):
  File "/home/<USER>/Documents/testF/2025/ser25/server0613/MySuiteDatebase2402newuigood-yolo5-3/server5/app/utils/server6_client.py", line 78, in _make_request
    response.raise_for_status()  # 如果状态码是 4xx 或 5xx，则抛出异常
    ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/miniconda3/envs/my_suite_unified/lib/python3.12/site-packages/aiohttp/client_reqrep.py", line 629, in raise_for_status
    raise ClientResponseError(
aiohttp.client_exceptions.ClientResponseError: 404, message='Not Found', url='http://************:8009/mdb/entries/602003'

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "/home/<USER>/Documents/testF/2025/ser25/server0613/MySuiteDatebase2402newuigood-yolo5-3/server5/app/services/f2_push_writer.py", line 272, in _handle_update_operation
    response = await self.server6_client.update_entry(external_id, entry_data)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Documents/testF/2025/ser25/server0613/MySuiteDatebase2402newuigood-yolo5-3/server5/app/utils/server6_client.py", line 249, in update_entry
    return await self._make_request('PUT', f'/mdb/entries/{external_id}', json_data=serializable_data)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Documents/testF/2025/ser25/server0613/MySuiteDatebase2402newuigood-yolo5-3/server5/app/utils/server6_client.py", line 94, in _make_request
    raise Exception(f"Server6请求返回错误 {e.status} (URL: {url}, Employee: {json_data.get('employee_id', 'N/A')}): {e.message}") from e
Exception: Server6请求返回错误 404 (URL: http://************:8009/mdb/entries/602003, Employee: N/A): Not Found
2025-06-30 13:11:38,720 - ERROR - ❌ 推送作业失败 27: Server6请求返回错误 404 (URL: http://************:8009/mdb/entries/602003, Employee: N/A): Not Found
2025-06-30 13:11:38,720 - app.services.f2_push_writer - ERROR - ❌ 推送作业失败 27: Server6请求返回错误 404 (URL: http://************:8009/mdb/entries/602003, Employee: N/A): Not Found
2025-06-30 13:11:38,720 - ERROR - ❌ 推送任务处理失败: Server6请求返回错误 404 (URL: http://************:8009/mdb/entries/602003, Employee: N/A): Not Found
2025-06-30 13:11:38,720 - app.services.f2_push_writer - ERROR - ❌ 推送任务处理失败: Server6请求返回错误 404 (URL: http://************:8009/mdb/entries/602003, Employee: N/A): Not Found
2025-06-30 13:11:38,721 - ERROR - ❌ 推送作业失败 28: Server6请求返回错误 404 (URL: http://************:8009/mdb/entries/602003, Employee: N/A): Not Found
2025-06-30 13:11:38,721 - app.services.f2_push_writer - ERROR - ❌ 推送作业失败 28: Server6请求返回错误 404 (URL: http://************:8009/mdb/entries/602003, Employee: N/A): Not Found
2025-06-30 13:11:38,722 - ERROR - ❌ 推送任务处理失败: Server6请求返回错误 404 (URL: http://************:8009/mdb/entries/602003, Employee: N/A): Not Found
2025-06-30 13:11:38,722 - app.services.f2_push_writer - ERROR - ❌ 推送任务处理失败: Server6请求返回错误 404 (URL: http://************:8009/mdb/entries/602003, Employee: N/A): Not Found
2025-06-30 13:11:38,722 - ERROR - ❌ UPDATE操作失败: Server6请求返回错误 404 (URL: http://************:8009/mdb/entries/602003, Employee: N/A): Not Found
Traceback (most recent call last):
  File "/home/<USER>/Documents/testF/2025/ser25/server0613/MySuiteDatebase2402newuigood-yolo5-3/server5/app/utils/server6_client.py", line 78, in _make_request
    response.raise_for_status()  # 如果状态码是 4xx 或 5xx，则抛出异常
    ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/miniconda3/envs/my_suite_unified/lib/python3.12/site-packages/aiohttp/client_reqrep.py", line 629, in raise_for_status
    raise ClientResponseError(
aiohttp.client_exceptions.ClientResponseError: 404, message='Not Found', url='http://************:8009/mdb/entries/602003'

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "/home/<USER>/Documents/testF/2025/ser25/server0613/MySuiteDatebase2402newuigood-yolo5-3/server5/app/services/f2_push_writer.py", line 272, in _handle_update_operation
    response = await self.server6_client.update_entry(external_id, entry_data)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Documents/testF/2025/ser25/server0613/MySuiteDatebase2402newuigood-yolo5-3/server5/app/utils/server6_client.py", line 249, in update_entry
    return await self._make_request('PUT', f'/mdb/entries/{external_id}', json_data=serializable_data)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Documents/testF/2025/ser25/server0613/MySuiteDatebase2402newuigood-yolo5-3/server5/app/utils/server6_client.py", line 94, in _make_request
    raise Exception(f"Server6请求返回错误 {e.status} (URL: {url}, Employee: {json_data.get('employee_id', 'N/A')}): {e.message}") from e
Exception: Server6请求返回错误 404 (URL: http://************:8009/mdb/entries/602003, Employee: N/A): Not Found
2025-06-30 13:11:38,722 - app.services.f2_push_writer - ERROR - ❌ UPDATE操作失败: Server6请求返回错误 404 (URL: http://************:8009/mdb/entries/602003, Employee: N/A): Not Found
Traceback (most recent call last):
  File "/home/<USER>/Documents/testF/2025/ser25/server0613/MySuiteDatebase2402newuigood-yolo5-3/server5/app/utils/server6_client.py", line 78, in _make_request
    response.raise_for_status()  # 如果状态码是 4xx 或 5xx，则抛出异常
    ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/miniconda3/envs/my_suite_unified/lib/python3.12/site-packages/aiohttp/client_reqrep.py", line 629, in raise_for_status
    raise ClientResponseError(
aiohttp.client_exceptions.ClientResponseError: 404, message='Not Found', url='http://************:8009/mdb/entries/602003'

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "/home/<USER>/Documents/testF/2025/ser25/server0613/MySuiteDatebase2402newuigood-yolo5-3/server5/app/services/f2_push_writer.py", line 272, in _handle_update_operation
    response = await self.server6_client.update_entry(external_id, entry_data)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Documents/testF/2025/ser25/server0613/MySuiteDatebase2402newuigood-yolo5-3/server5/app/utils/server6_client.py", line 249, in update_entry
    return await self._make_request('PUT', f'/mdb/entries/{external_id}', json_data=serializable_data)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Documents/testF/2025/ser25/server0613/MySuiteDatebase2402newuigood-yolo5-3/server5/app/utils/server6_client.py", line 94, in _make_request
    raise Exception(f"Server6请求返回错误 {e.status} (URL: {url}, Employee: {json_data.get('employee_id', 'N/A')}): {e.message}") from e
Exception: Server6请求返回错误 404 (URL: http://************:8009/mdb/entries/602003, Employee: N/A): Not Found
2025-06-30 13:11:38,725 - ERROR - ❌ 推送作业失败 29: Server6请求返回错误 404 (URL: http://************:8009/mdb/entries/602003, Employee: N/A): Not Found
2025-06-30 13:11:38,725 - app.services.f2_push_writer - ERROR - ❌ 推送作业失败 29: Server6请求返回错误 404 (URL: http://************:8009/mdb/entries/602003, Employee: N/A): Not Found
2025-06-30 13:11:38,725 - ERROR - ❌ 推送任务处理失败: Server6请求返回错误 404 (URL: http://************:8009/mdb/entries/602003, Employee: N/A): Not Found
2025-06-30 13:11:38,725 - app.services.f2_push_writer - ERROR - ❌ 推送任务处理失败: Server6请求返回错误 404 (URL: http://************:8009/mdb/entries/602003, Employee: N/A): Not Found
2025-06-30 13:11:38,733 - ERROR - ❌ Server6请求失败: PUT http://************:8009/mdb/entries/602008 - Status: 404, Message: Not Found. Request Body: {"entry_id": 4930, "created_ts": "2025-06-30", "日付": "2025-06-04", "id": 4930, "external_id": 602008, "ts": "2025-06-30", "従業員ｺｰﾄﾞ": "272204", "機種": "", "号機": "", "工場製番": "", "工事番号": "", "ﾕﾆｯﾄ番号": "", "区分": 4, "項目": 0, "時間": 1.0, "所属ｺｰﾄﾞ": "111"}
2025-06-30 13:11:38,733 - app.utils.server6_client - ERROR - ❌ Server6请求失败: PUT http://************:8009/mdb/entries/602008 - Status: 404, Message: Not Found. Request Body: {"entry_id": 4930, "created_ts": "2025-06-30", "日付": "2025-06-04", "id": 4930, "external_id": 602008, "ts": "2025-06-30", "従業員ｺｰﾄﾞ": "272204", "機種": "", "号機": "", "工場製番": "", "工事番号": "", "ﾕﾆｯﾄ番号": "", "区分": 4, "項目": 0, "時間": 1.0, "所属ｺｰﾄﾞ": "111"}
2025-06-30 13:11:38,735 - ERROR - ❌ UPDATE操作失败: Server6请求返回错误 404 (URL: http://************:8009/mdb/entries/602008, Employee: N/A): Not Found
Traceback (most recent call last):
  File "/home/<USER>/Documents/testF/2025/ser25/server0613/MySuiteDatebase2402newuigood-yolo5-3/server5/app/utils/server6_client.py", line 78, in _make_request
    response.raise_for_status()  # 如果状态码是 4xx 或 5xx，则抛出异常
    ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/miniconda3/envs/my_suite_unified/lib/python3.12/site-packages/aiohttp/client_reqrep.py", line 629, in raise_for_status
    raise ClientResponseError(
aiohttp.client_exceptions.ClientResponseError: 404, message='Not Found', url='http://************:8009/mdb/entries/602008'

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "/home/<USER>/Documents/testF/2025/ser25/server0613/MySuiteDatebase2402newuigood-yolo5-3/server5/app/services/f2_push_writer.py", line 272, in _handle_update_operation
    response = await self.server6_client.update_entry(external_id, entry_data)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Documents/testF/2025/ser25/server0613/MySuiteDatebase2402newuigood-yolo5-3/server5/app/utils/server6_client.py", line 249, in update_entry
    return await self._make_request('PUT', f'/mdb/entries/{external_id}', json_data=serializable_data)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Documents/testF/2025/ser25/server0613/MySuiteDatebase2402newuigood-yolo5-3/server5/app/utils/server6_client.py", line 94, in _make_request
    raise Exception(f"Server6请求返回错误 {e.status} (URL: {url}, Employee: {json_data.get('employee_id', 'N/A')}): {e.message}") from e
Exception: Server6请求返回错误 404 (URL: http://************:8009/mdb/entries/602008, Employee: N/A): Not Found
2025-06-30 13:11:38,735 - app.services.f2_push_writer - ERROR - ❌ UPDATE操作失败: Server6请求返回错误 404 (URL: http://************:8009/mdb/entries/602008, Employee: N/A): Not Found
Traceback (most recent call last):
  File "/home/<USER>/Documents/testF/2025/ser25/server0613/MySuiteDatebase2402newuigood-yolo5-3/server5/app/utils/server6_client.py", line 78, in _make_request
    response.raise_for_status()  # 如果状态码是 4xx 或 5xx，则抛出异常
    ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/miniconda3/envs/my_suite_unified/lib/python3.12/site-packages/aiohttp/client_reqrep.py", line 629, in raise_for_status
    raise ClientResponseError(
aiohttp.client_exceptions.ClientResponseError: 404, message='Not Found', url='http://************:8009/mdb/entries/602008'

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "/home/<USER>/Documents/testF/2025/ser25/server0613/MySuiteDatebase2402newuigood-yolo5-3/server5/app/services/f2_push_writer.py", line 272, in _handle_update_operation
    response = await self.server6_client.update_entry(external_id, entry_data)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Documents/testF/2025/ser25/server0613/MySuiteDatebase2402newuigood-yolo5-3/server5/app/utils/server6_client.py", line 249, in update_entry
    return await self._make_request('PUT', f'/mdb/entries/{external_id}', json_data=serializable_data)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Documents/testF/2025/ser25/server0613/MySuiteDatebase2402newuigood-yolo5-3/server5/app/utils/server6_client.py", line 94, in _make_request
    raise Exception(f"Server6请求返回错误 {e.status} (URL: {url}, Employee: {json_data.get('employee_id', 'N/A')}): {e.message}") from e
Exception: Server6请求返回错误 404 (URL: http://************:8009/mdb/entries/602008, Employee: N/A): Not Found
2025-06-30 13:11:38,736 - ERROR - ❌ Server6请求失败: PUT http://************:8009/mdb/entries/602008 - Status: 404, Message: Not Found. Request Body: {"entry_id": 4930, "created_ts": "2025-06-30", "日付": "2025-06-04", "id": 4930, "external_id": 602008, "ts": "2025-06-30", "従業員ｺｰﾄﾞ": "272204", "機種": "", "号機": "", "工場製番": "", "工事番号": "", "ﾕﾆｯﾄ番号": "", "区分": 4, "項目": 0, "時間": 1.0, "所属ｺｰﾄﾞ": "111"}
2025-06-30 13:11:38,736 - app.utils.server6_client - ERROR - ❌ Server6请求失败: PUT http://************:8009/mdb/entries/602008 - Status: 404, Message: Not Found. Request Body: {"entry_id": 4930, "created_ts": "2025-06-30", "日付": "2025-06-04", "id": 4930, "external_id": 602008, "ts": "2025-06-30", "従業員ｺｰﾄﾞ": "272204", "機種": "", "号機": "", "工場製番": "", "工事番号": "", "ﾕﾆｯﾄ番号": "", "区分": 4, "項目": 0, "時間": 1.0, "所属ｺｰﾄﾞ": "111"}
2025-06-30 13:11:38,737 - ERROR - ❌ Server6请求失败: PUT http://************:8009/mdb/entries/602008 - Status: 404, Message: Not Found. Request Body: {"entry_id": 4930, "created_ts": "2025-06-30", "日付": "2025-06-04", "id": 4930, "external_id": 602008, "ts": "2025-06-30", "従業員ｺｰﾄﾞ": "272204", "機種": "", "号機": "", "工場製番": "", "工事番号": "", "ﾕﾆｯﾄ番号": "", "区分": 4, "項目": 0, "時間": 1.0, "所属ｺｰﾄﾞ": "111"}
2025-06-30 13:11:38,737 - app.utils.server6_client - ERROR - ❌ Server6请求失败: PUT http://************:8009/mdb/entries/602008 - Status: 404, Message: Not Found. Request Body: {"entry_id": 4930, "created_ts": "2025-06-30", "日付": "2025-06-04", "id": 4930, "external_id": 602008, "ts": "2025-06-30", "従業員ｺｰﾄﾞ": "272204", "機種": "", "号機": "", "工場製番": "", "工事番号": "", "ﾕﾆｯﾄ番号": "", "区分": 4, "項目": 0, "時間": 1.0, "所属ｺｰﾄﾞ": "111"}
2025-06-30 13:11:38,737 - ERROR - ❌ 推送作业失败 30: Server6请求返回错误 404 (URL: http://************:8009/mdb/entries/602008, Employee: N/A): Not Found
2025-06-30 13:11:38,737 - app.services.f2_push_writer - ERROR - ❌ 推送作业失败 30: Server6请求返回错误 404 (URL: http://************:8009/mdb/entries/602008, Employee: N/A): Not Found
2025-06-30 13:11:38,737 - ERROR - ❌ 推送任务处理失败: Server6请求返回错误 404 (URL: http://************:8009/mdb/entries/602008, Employee: N/A): Not Found
2025-06-30 13:11:38,737 - app.services.f2_push_writer - ERROR - ❌ 推送任务处理失败: Server6请求返回错误 404 (URL: http://************:8009/mdb/entries/602008, Employee: N/A): Not Found
2025-06-30 13:11:38,738 - ERROR - ❌ UPDATE操作失败: Server6请求返回错误 404 (URL: http://************:8009/mdb/entries/602008, Employee: N/A): Not Found
Traceback (most recent call last):
  File "/home/<USER>/Documents/testF/2025/ser25/server0613/MySuiteDatebase2402newuigood-yolo5-3/server5/app/utils/server6_client.py", line 78, in _make_request
    response.raise_for_status()  # 如果状态码是 4xx 或 5xx，则抛出异常
    ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/miniconda3/envs/my_suite_unified/lib/python3.12/site-packages/aiohttp/client_reqrep.py", line 629, in raise_for_status
    raise ClientResponseError(
aiohttp.client_exceptions.ClientResponseError: 404, message='Not Found', url='http://************:8009/mdb/entries/602008'

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "/home/<USER>/Documents/testF/2025/ser25/server0613/MySuiteDatebase2402newuigood-yolo5-3/server5/app/services/f2_push_writer.py", line 272, in _handle_update_operation
    response = await self.server6_client.update_entry(external_id, entry_data)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Documents/testF/2025/ser25/server0613/MySuiteDatebase2402newuigood-yolo5-3/server5/app/utils/server6_client.py", line 249, in update_entry
    return await self._make_request('PUT', f'/mdb/entries/{external_id}', json_data=serializable_data)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Documents/testF/2025/ser25/server0613/MySuiteDatebase2402newuigood-yolo5-3/server5/app/utils/server6_client.py", line 94, in _make_request
    raise Exception(f"Server6请求返回错误 {e.status} (URL: {url}, Employee: {json_data.get('employee_id', 'N/A')}): {e.message}") from e
Exception: Server6请求返回错误 404 (URL: http://************:8009/mdb/entries/602008, Employee: N/A): Not Found
2025-06-30 13:11:38,738 - app.services.f2_push_writer - ERROR - ❌ UPDATE操作失败: Server6请求返回错误 404 (URL: http://************:8009/mdb/entries/602008, Employee: N/A): Not Found
Traceback (most recent call last):
  File "/home/<USER>/Documents/testF/2025/ser25/server0613/MySuiteDatebase2402newuigood-yolo5-3/server5/app/utils/server6_client.py", line 78, in _make_request
    response.raise_for_status()  # 如果状态码是 4xx 或 5xx，则抛出异常
    ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/miniconda3/envs/my_suite_unified/lib/python3.12/site-packages/aiohttp/client_reqrep.py", line 629, in raise_for_status
    raise ClientResponseError(
aiohttp.client_exceptions.ClientResponseError: 404, message='Not Found', url='http://************:8009/mdb/entries/602008'

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "/home/<USER>/Documents/testF/2025/ser25/server0613/MySuiteDatebase2402newuigood-yolo5-3/server5/app/services/f2_push_writer.py", line 272, in _handle_update_operation
    response = await self.server6_client.update_entry(external_id, entry_data)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Documents/testF/2025/ser25/server0613/MySuiteDatebase2402newuigood-yolo5-3/server5/app/utils/server6_client.py", line 249, in update_entry
    return await self._make_request('PUT', f'/mdb/entries/{external_id}', json_data=serializable_data)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Documents/testF/2025/ser25/server0613/MySuiteDatebase2402newuigood-yolo5-3/server5/app/utils/server6_client.py", line 94, in _make_request
    raise Exception(f"Server6请求返回错误 {e.status} (URL: {url}, Employee: {json_data.get('employee_id', 'N/A')}): {e.message}") from e
Exception: Server6请求返回错误 404 (URL: http://************:8009/mdb/entries/602008, Employee: N/A): Not Found
2025-06-30 13:11:38,740 - ERROR - ❌ UPDATE操作失败: Server6请求返回错误 404 (URL: http://************:8009/mdb/entries/602008, Employee: N/A): Not Found
Traceback (most recent call last):
  File "/home/<USER>/Documents/testF/2025/ser25/server0613/MySuiteDatebase2402newuigood-yolo5-3/server5/app/utils/server6_client.py", line 78, in _make_request
    response.raise_for_status()  # 如果状态码是 4xx 或 5xx，则抛出异常
    ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/miniconda3/envs/my_suite_unified/lib/python3.12/site-packages/aiohttp/client_reqrep.py", line 629, in raise_for_status
    raise ClientResponseError(
aiohttp.client_exceptions.ClientResponseError: 404, message='Not Found', url='http://************:8009/mdb/entries/602008'

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "/home/<USER>/Documents/testF/2025/ser25/server0613/MySuiteDatebase2402newuigood-yolo5-3/server5/app/services/f2_push_writer.py", line 272, in _handle_update_operation
    response = await self.server6_client.update_entry(external_id, entry_data)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Documents/testF/2025/ser25/server0613/MySuiteDatebase2402newuigood-yolo5-3/server5/app/utils/server6_client.py", line 249, in update_entry
    return await self._make_request('PUT', f'/mdb/entries/{external_id}', json_data=serializable_data)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Documents/testF/2025/ser25/server0613/MySuiteDatebase2402newuigood-yolo5-3/server5/app/utils/server6_client.py", line 94, in _make_request
    raise Exception(f"Server6请求返回错误 {e.status} (URL: {url}, Employee: {json_data.get('employee_id', 'N/A')}): {e.message}") from e
Exception: Server6请求返回错误 404 (URL: http://************:8009/mdb/entries/602008, Employee: N/A): Not Found
2025-06-30 13:11:38,740 - app.services.f2_push_writer - ERROR - ❌ UPDATE操作失败: Server6请求返回错误 404 (URL: http://************:8009/mdb/entries/602008, Employee: N/A): Not Found
Traceback (most recent call last):
  File "/home/<USER>/Documents/testF/2025/ser25/server0613/MySuiteDatebase2402newuigood-yolo5-3/server5/app/utils/server6_client.py", line 78, in _make_request
    response.raise_for_status()  # 如果状态码是 4xx 或 5xx，则抛出异常
    ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/miniconda3/envs/my_suite_unified/lib/python3.12/site-packages/aiohttp/client_reqrep.py", line 629, in raise_for_status
    raise ClientResponseError(
aiohttp.client_exceptions.ClientResponseError: 404, message='Not Found', url='http://************:8009/mdb/entries/602008'

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "/home/<USER>/Documents/testF/2025/ser25/server0613/MySuiteDatebase2402newuigood-yolo5-3/server5/app/services/f2_push_writer.py", line 272, in _handle_update_operation
    response = await self.server6_client.update_entry(external_id, entry_data)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Documents/testF/2025/ser25/server0613/MySuiteDatebase2402newuigood-yolo5-3/server5/app/utils/server6_client.py", line 249, in update_entry
    return await self._make_request('PUT', f'/mdb/entries/{external_id}', json_data=serializable_data)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Documents/testF/2025/ser25/server0613/MySuiteDatebase2402newuigood-yolo5-3/server5/app/utils/server6_client.py", line 94, in _make_request
    raise Exception(f"Server6请求返回错误 {e.status} (URL: {url}, Employee: {json_data.get('employee_id', 'N/A')}): {e.message}") from e
Exception: Server6请求返回错误 404 (URL: http://************:8009/mdb/entries/602008, Employee: N/A): Not Found
2025-06-30 13:11:38,741 - ERROR - ❌ 推送作业失败 31: Server6请求返回错误 404 (URL: http://************:8009/mdb/entries/602008, Employee: N/A): Not Found
2025-06-30 13:11:38,741 - app.services.f2_push_writer - ERROR - ❌ 推送作业失败 31: Server6请求返回错误 404 (URL: http://************:8009/mdb/entries/602008, Employee: N/A): Not Found
2025-06-30 13:11:38,741 - ERROR - ❌ 推送任务处理失败: Server6请求返回错误 404 (URL: http://************:8009/mdb/entries/602008, Employee: N/A): Not Found
2025-06-30 13:11:38,741 - app.services.f2_push_writer - ERROR - ❌ 推送任务处理失败: Server6请求返回错误 404 (URL: http://************:8009/mdb/entries/602008, Employee: N/A): Not Found
2025-06-30 13:11:38,742 - ERROR - ❌ 推送作业失败 32: Server6请求返回错误 404 (URL: http://************:8009/mdb/entries/602008, Employee: N/A): Not Found
2025-06-30 13:11:38,742 - app.services.f2_push_writer - ERROR - ❌ 推送作业失败 32: Server6请求返回错误 404 (URL: http://************:8009/mdb/entries/602008, Employee: N/A): Not Found
2025-06-30 13:11:38,742 - ERROR - ❌ 推送任务处理失败: Server6请求返回错误 404 (URL: http://************:8009/mdb/entries/602008, Employee: N/A): Not Found
2025-06-30 13:11:38,742 - app.services.f2_push_writer - ERROR - ❌ 推送任务处理失败: Server6请求返回错误 404 (URL: http://************:8009/mdb/entries/602008, Employee: N/A): Not Found
2025-06-30 13:11:38,750 - ERROR - ❌ Server6请求失败: PUT http://************:8009/mdb/entries/602009 - Status: 404, Message: Not Found. Request Body: {"entry_id": 4931, "created_ts": "2025-06-30", "日付": "2025-06-05", "id": 4931, "external_id": 602009, "ts": "2025-06-30", "従業員ｺｰﾄﾞ": "272204", "機種": "", "号機": "", "工場製番": "", "工事番号": "", "ﾕﾆｯﾄ番号": "", "区分": 4, "項目": 0, "時間": 2.5, "所属ｺｰﾄﾞ": "111"}
2025-06-30 13:11:38,750 - app.utils.server6_client - ERROR - ❌ Server6请求失败: PUT http://************:8009/mdb/entries/602009 - Status: 404, Message: Not Found. Request Body: {"entry_id": 4931, "created_ts": "2025-06-30", "日付": "2025-06-05", "id": 4931, "external_id": 602009, "ts": "2025-06-30", "従業員ｺｰﾄﾞ": "272204", "機種": "", "号機": "", "工場製番": "", "工事番号": "", "ﾕﾆｯﾄ番号": "", "区分": 4, "項目": 0, "時間": 2.5, "所属ｺｰﾄﾞ": "111"}
2025-06-30 13:11:38,752 - ERROR - ❌ Server6请求失败: PUT http://************:8009/mdb/entries/602009 - Status: 404, Message: Not Found. Request Body: {"entry_id": 4931, "created_ts": "2025-06-30", "日付": "2025-06-05", "id": 4931, "external_id": 602009, "ts": "2025-06-30", "従業員ｺｰﾄﾞ": "272204", "機種": "", "号機": "", "工場製番": "", "工事番号": "", "ﾕﾆｯﾄ番号": "", "区分": 4, "項目": 0, "時間": 2.5, "所属ｺｰﾄﾞ": "111"}
2025-06-30 13:11:38,752 - app.utils.server6_client - ERROR - ❌ Server6请求失败: PUT http://************:8009/mdb/entries/602009 - Status: 404, Message: Not Found. Request Body: {"entry_id": 4931, "created_ts": "2025-06-30", "日付": "2025-06-05", "id": 4931, "external_id": 602009, "ts": "2025-06-30", "従業員ｺｰﾄﾞ": "272204", "機種": "", "号機": "", "工場製番": "", "工事番号": "", "ﾕﾆｯﾄ番号": "", "区分": 4, "項目": 0, "時間": 2.5, "所属ｺｰﾄﾞ": "111"}
2025-06-30 13:11:38,752 - ERROR - ❌ UPDATE操作失败: Server6请求返回错误 404 (URL: http://************:8009/mdb/entries/602009, Employee: N/A): Not Found
Traceback (most recent call last):
  File "/home/<USER>/Documents/testF/2025/ser25/server0613/MySuiteDatebase2402newuigood-yolo5-3/server5/app/utils/server6_client.py", line 78, in _make_request
    response.raise_for_status()  # 如果状态码是 4xx 或 5xx，则抛出异常
    ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/miniconda3/envs/my_suite_unified/lib/python3.12/site-packages/aiohttp/client_reqrep.py", line 629, in raise_for_status
    raise ClientResponseError(
aiohttp.client_exceptions.ClientResponseError: 404, message='Not Found', url='http://************:8009/mdb/entries/602009'

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "/home/<USER>/Documents/testF/2025/ser25/server0613/MySuiteDatebase2402newuigood-yolo5-3/server5/app/services/f2_push_writer.py", line 272, in _handle_update_operation
    response = await self.server6_client.update_entry(external_id, entry_data)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Documents/testF/2025/ser25/server0613/MySuiteDatebase2402newuigood-yolo5-3/server5/app/utils/server6_client.py", line 249, in update_entry
    return await self._make_request('PUT', f'/mdb/entries/{external_id}', json_data=serializable_data)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Documents/testF/2025/ser25/server0613/MySuiteDatebase2402newuigood-yolo5-3/server5/app/utils/server6_client.py", line 94, in _make_request
    raise Exception(f"Server6请求返回错误 {e.status} (URL: {url}, Employee: {json_data.get('employee_id', 'N/A')}): {e.message}") from e
Exception: Server6请求返回错误 404 (URL: http://************:8009/mdb/entries/602009, Employee: N/A): Not Found
2025-06-30 13:11:38,752 - app.services.f2_push_writer - ERROR - ❌ UPDATE操作失败: Server6请求返回错误 404 (URL: http://************:8009/mdb/entries/602009, Employee: N/A): Not Found
Traceback (most recent call last):
  File "/home/<USER>/Documents/testF/2025/ser25/server0613/MySuiteDatebase2402newuigood-yolo5-3/server5/app/utils/server6_client.py", line 78, in _make_request
    response.raise_for_status()  # 如果状态码是 4xx 或 5xx，则抛出异常
    ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/miniconda3/envs/my_suite_unified/lib/python3.12/site-packages/aiohttp/client_reqrep.py", line 629, in raise_for_status
    raise ClientResponseError(
aiohttp.client_exceptions.ClientResponseError: 404, message='Not Found', url='http://************:8009/mdb/entries/602009'

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "/home/<USER>/Documents/testF/2025/ser25/server0613/MySuiteDatebase2402newuigood-yolo5-3/server5/app/services/f2_push_writer.py", line 272, in _handle_update_operation
    response = await self.server6_client.update_entry(external_id, entry_data)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Documents/testF/2025/ser25/server0613/MySuiteDatebase2402newuigood-yolo5-3/server5/app/utils/server6_client.py", line 249, in update_entry
    return await self._make_request('PUT', f'/mdb/entries/{external_id}', json_data=serializable_data)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Documents/testF/2025/ser25/server0613/MySuiteDatebase2402newuigood-yolo5-3/server5/app/utils/server6_client.py", line 94, in _make_request
    raise Exception(f"Server6请求返回错误 {e.status} (URL: {url}, Employee: {json_data.get('employee_id', 'N/A')}): {e.message}") from e
Exception: Server6请求返回错误 404 (URL: http://************:8009/mdb/entries/602009, Employee: N/A): Not Found
2025-06-30 13:11:38,753 - ERROR - ❌ Server6请求失败: PUT http://************:8009/mdb/entries/602009 - Status: 404, Message: Not Found. Request Body: {"entry_id": 4931, "created_ts": "2025-06-30", "日付": "2025-06-05", "id": 4931, "external_id": 602009, "ts": "2025-06-30", "従業員ｺｰﾄﾞ": "272204", "機種": "", "号機": "", "工場製番": "", "工事番号": "", "ﾕﾆｯﾄ番号": "", "区分": 4, "項目": 0, "時間": 2.5, "所属ｺｰﾄﾞ": "111"}
2025-06-30 13:11:38,753 - app.utils.server6_client - ERROR - ❌ Server6请求失败: PUT http://************:8009/mdb/entries/602009 - Status: 404, Message: Not Found. Request Body: {"entry_id": 4931, "created_ts": "2025-06-30", "日付": "2025-06-05", "id": 4931, "external_id": 602009, "ts": "2025-06-30", "従業員ｺｰﾄﾞ": "272204", "機種": "", "号機": "", "工場製番": "", "工事番号": "", "ﾕﾆｯﾄ番号": "", "区分": 4, "項目": 0, "時間": 2.5, "所属ｺｰﾄﾞ": "111"}
2025-06-30 13:11:38,754 - ERROR - ❌ UPDATE操作失败: Server6请求返回错误 404 (URL: http://************:8009/mdb/entries/602009, Employee: N/A): Not Found
Traceback (most recent call last):
  File "/home/<USER>/Documents/testF/2025/ser25/server0613/MySuiteDatebase2402newuigood-yolo5-3/server5/app/utils/server6_client.py", line 78, in _make_request
    response.raise_for_status()  # 如果状态码是 4xx 或 5xx，则抛出异常
    ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/miniconda3/envs/my_suite_unified/lib/python3.12/site-packages/aiohttp/client_reqrep.py", line 629, in raise_for_status
    raise ClientResponseError(
aiohttp.client_exceptions.ClientResponseError: 404, message='Not Found', url='http://************:8009/mdb/entries/602009'

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "/home/<USER>/Documents/testF/2025/ser25/server0613/MySuiteDatebase2402newuigood-yolo5-3/server5/app/services/f2_push_writer.py", line 272, in _handle_update_operation
    response = await self.server6_client.update_entry(external_id, entry_data)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Documents/testF/2025/ser25/server0613/MySuiteDatebase2402newuigood-yolo5-3/server5/app/utils/server6_client.py", line 249, in update_entry
    return await self._make_request('PUT', f'/mdb/entries/{external_id}', json_data=serializable_data)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Documents/testF/2025/ser25/server0613/MySuiteDatebase2402newuigood-yolo5-3/server5/app/utils/server6_client.py", line 94, in _make_request
    raise Exception(f"Server6请求返回错误 {e.status} (URL: {url}, Employee: {json_data.get('employee_id', 'N/A')}): {e.message}") from e
Exception: Server6请求返回错误 404 (URL: http://************:8009/mdb/entries/602009, Employee: N/A): Not Found
2025-06-30 13:11:38,754 - app.services.f2_push_writer - ERROR - ❌ UPDATE操作失败: Server6请求返回错误 404 (URL: http://************:8009/mdb/entries/602009, Employee: N/A): Not Found
Traceback (most recent call last):
  File "/home/<USER>/Documents/testF/2025/ser25/server0613/MySuiteDatebase2402newuigood-yolo5-3/server5/app/utils/server6_client.py", line 78, in _make_request
    response.raise_for_status()  # 如果状态码是 4xx 或 5xx，则抛出异常
    ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/miniconda3/envs/my_suite_unified/lib/python3.12/site-packages/aiohttp/client_reqrep.py", line 629, in raise_for_status
    raise ClientResponseError(
aiohttp.client_exceptions.ClientResponseError: 404, message='Not Found', url='http://************:8009/mdb/entries/602009'

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "/home/<USER>/Documents/testF/2025/ser25/server0613/MySuiteDatebase2402newuigood-yolo5-3/server5/app/services/f2_push_writer.py", line 272, in _handle_update_operation
    response = await self.server6_client.update_entry(external_id, entry_data)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Documents/testF/2025/ser25/server0613/MySuiteDatebase2402newuigood-yolo5-3/server5/app/utils/server6_client.py", line 249, in update_entry
    return await self._make_request('PUT', f'/mdb/entries/{external_id}', json_data=serializable_data)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Documents/testF/2025/ser25/server0613/MySuiteDatebase2402newuigood-yolo5-3/server5/app/utils/server6_client.py", line 94, in _make_request
    raise Exception(f"Server6请求返回错误 {e.status} (URL: {url}, Employee: {json_data.get('employee_id', 'N/A')}): {e.message}") from e
Exception: Server6请求返回错误 404 (URL: http://************:8009/mdb/entries/602009, Employee: N/A): Not Found
2025-06-30 13:11:38,755 - ERROR - ❌ Server6请求失败: PUT http://************:8009/mdb/entries/602009 - Status: 404, Message: Not Found. Request Body: {"entry_id": 4931, "created_ts": "2025-06-30", "日付": "2025-06-05", "id": 4931, "external_id": 602009, "ts": "2025-06-30", "従業員ｺｰﾄﾞ": "272204", "機種": "", "号機": "", "工場製番": "", "工事番号": "", "ﾕﾆｯﾄ番号": "", "区分": 4, "項目": 0, "時間": 2.5, "所属ｺｰﾄﾞ": "111"}
2025-06-30 13:11:38,755 - app.utils.server6_client - ERROR - ❌ Server6请求失败: PUT http://************:8009/mdb/entries/602009 - Status: 404, Message: Not Found. Request Body: {"entry_id": 4931, "created_ts": "2025-06-30", "日付": "2025-06-05", "id": 4931, "external_id": 602009, "ts": "2025-06-30", "従業員ｺｰﾄﾞ": "272204", "機種": "", "号機": "", "工場製番": "", "工事番号": "", "ﾕﾆｯﾄ番号": "", "区分": 4, "項目": 0, "時間": 2.5, "所属ｺｰﾄﾞ": "111"}
2025-06-30 13:11:38,755 - ERROR - ❌ 推送作业失败 34: Server6请求返回错误 404 (URL: http://************:8009/mdb/entries/602009, Employee: N/A): Not Found
2025-06-30 13:11:38,755 - app.services.f2_push_writer - ERROR - ❌ 推送作业失败 34: Server6请求返回错误 404 (URL: http://************:8009/mdb/entries/602009, Employee: N/A): Not Found
2025-06-30 13:11:38,755 - ERROR - ❌ 推送任务处理失败: Server6请求返回错误 404 (URL: http://************:8009/mdb/entries/602009, Employee: N/A): Not Found
2025-06-30 13:11:38,755 - app.services.f2_push_writer - ERROR - ❌ 推送任务处理失败: Server6请求返回错误 404 (URL: http://************:8009/mdb/entries/602009, Employee: N/A): Not Found
2025-06-30 13:11:38,756 - ERROR - ❌ UPDATE操作失败: Server6请求返回错误 404 (URL: http://************:8009/mdb/entries/602009, Employee: N/A): Not Found
Traceback (most recent call last):
  File "/home/<USER>/Documents/testF/2025/ser25/server0613/MySuiteDatebase2402newuigood-yolo5-3/server5/app/utils/server6_client.py", line 78, in _make_request
    response.raise_for_status()  # 如果状态码是 4xx 或 5xx，则抛出异常
    ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/miniconda3/envs/my_suite_unified/lib/python3.12/site-packages/aiohttp/client_reqrep.py", line 629, in raise_for_status
    raise ClientResponseError(
aiohttp.client_exceptions.ClientResponseError: 404, message='Not Found', url='http://************:8009/mdb/entries/602009'

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "/home/<USER>/Documents/testF/2025/ser25/server0613/MySuiteDatebase2402newuigood-yolo5-3/server5/app/services/f2_push_writer.py", line 272, in _handle_update_operation
    response = await self.server6_client.update_entry(external_id, entry_data)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Documents/testF/2025/ser25/server0613/MySuiteDatebase2402newuigood-yolo5-3/server5/app/utils/server6_client.py", line 249, in update_entry
    return await self._make_request('PUT', f'/mdb/entries/{external_id}', json_data=serializable_data)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Documents/testF/2025/ser25/server0613/MySuiteDatebase2402newuigood-yolo5-3/server5/app/utils/server6_client.py", line 94, in _make_request
    raise Exception(f"Server6请求返回错误 {e.status} (URL: {url}, Employee: {json_data.get('employee_id', 'N/A')}): {e.message}") from e
Exception: Server6请求返回错误 404 (URL: http://************:8009/mdb/entries/602009, Employee: N/A): Not Found
2025-06-30 13:11:38,756 - app.services.f2_push_writer - ERROR - ❌ UPDATE操作失败: Server6请求返回错误 404 (URL: http://************:8009/mdb/entries/602009, Employee: N/A): Not Found
Traceback (most recent call last):
  File "/home/<USER>/Documents/testF/2025/ser25/server0613/MySuiteDatebase2402newuigood-yolo5-3/server5/app/utils/server6_client.py", line 78, in _make_request
    response.raise_for_status()  # 如果状态码是 4xx 或 5xx，则抛出异常
    ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/miniconda3/envs/my_suite_unified/lib/python3.12/site-packages/aiohttp/client_reqrep.py", line 629, in raise_for_status
    raise ClientResponseError(
aiohttp.client_exceptions.ClientResponseError: 404, message='Not Found', url='http://************:8009/mdb/entries/602009'

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "/home/<USER>/Documents/testF/2025/ser25/server0613/MySuiteDatebase2402newuigood-yolo5-3/server5/app/services/f2_push_writer.py", line 272, in _handle_update_operation
    response = await self.server6_client.update_entry(external_id, entry_data)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Documents/testF/2025/ser25/server0613/MySuiteDatebase2402newuigood-yolo5-3/server5/app/utils/server6_client.py", line 249, in update_entry
    return await self._make_request('PUT', f'/mdb/entries/{external_id}', json_data=serializable_data)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Documents/testF/2025/ser25/server0613/MySuiteDatebase2402newuigood-yolo5-3/server5/app/utils/server6_client.py", line 94, in _make_request
    raise Exception(f"Server6请求返回错误 {e.status} (URL: {url}, Employee: {json_data.get('employee_id', 'N/A')}): {e.message}") from e
Exception: Server6请求返回错误 404 (URL: http://************:8009/mdb/entries/602009, Employee: N/A): Not Found
2025-06-30 13:11:38,758 - ERROR - ❌ UPDATE操作失败: Server6请求返回错误 404 (URL: http://************:8009/mdb/entries/602009, Employee: N/A): Not Found
Traceback (most recent call last):
  File "/home/<USER>/Documents/testF/2025/ser25/server0613/MySuiteDatebase2402newuigood-yolo5-3/server5/app/utils/server6_client.py", line 78, in _make_request
    response.raise_for_status()  # 如果状态码是 4xx 或 5xx，则抛出异常
    ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/miniconda3/envs/my_suite_unified/lib/python3.12/site-packages/aiohttp/client_reqrep.py", line 629, in raise_for_status
    raise ClientResponseError(
aiohttp.client_exceptions.ClientResponseError: 404, message='Not Found', url='http://************:8009/mdb/entries/602009'

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "/home/<USER>/Documents/testF/2025/ser25/server0613/MySuiteDatebase2402newuigood-yolo5-3/server5/app/services/f2_push_writer.py", line 272, in _handle_update_operation
    response = await self.server6_client.update_entry(external_id, entry_data)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Documents/testF/2025/ser25/server0613/MySuiteDatebase2402newuigood-yolo5-3/server5/app/utils/server6_client.py", line 249, in update_entry
    return await self._make_request('PUT', f'/mdb/entries/{external_id}', json_data=serializable_data)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Documents/testF/2025/ser25/server0613/MySuiteDatebase2402newuigood-yolo5-3/server5/app/utils/server6_client.py", line 94, in _make_request
    raise Exception(f"Server6请求返回错误 {e.status} (URL: {url}, Employee: {json_data.get('employee_id', 'N/A')}): {e.message}") from e
Exception: Server6请求返回错误 404 (URL: http://************:8009/mdb/entries/602009, Employee: N/A): Not Found
2025-06-30 13:11:38,758 - app.services.f2_push_writer - ERROR - ❌ UPDATE操作失败: Server6请求返回错误 404 (URL: http://************:8009/mdb/entries/602009, Employee: N/A): Not Found
Traceback (most recent call last):
  File "/home/<USER>/Documents/testF/2025/ser25/server0613/MySuiteDatebase2402newuigood-yolo5-3/server5/app/utils/server6_client.py", line 78, in _make_request
    response.raise_for_status()  # 如果状态码是 4xx 或 5xx，则抛出异常
    ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/miniconda3/envs/my_suite_unified/lib/python3.12/site-packages/aiohttp/client_reqrep.py", line 629, in raise_for_status
    raise ClientResponseError(
aiohttp.client_exceptions.ClientResponseError: 404, message='Not Found', url='http://************:8009/mdb/entries/602009'

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "/home/<USER>/Documents/testF/2025/ser25/server0613/MySuiteDatebase2402newuigood-yolo5-3/server5/app/services/f2_push_writer.py", line 272, in _handle_update_operation
    response = await self.server6_client.update_entry(external_id, entry_data)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Documents/testF/2025/ser25/server0613/MySuiteDatebase2402newuigood-yolo5-3/server5/app/utils/server6_client.py", line 249, in update_entry
    return await self._make_request('PUT', f'/mdb/entries/{external_id}', json_data=serializable_data)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Documents/testF/2025/ser25/server0613/MySuiteDatebase2402newuigood-yolo5-3/server5/app/utils/server6_client.py", line 94, in _make_request
    raise Exception(f"Server6请求返回错误 {e.status} (URL: {url}, Employee: {json_data.get('employee_id', 'N/A')}): {e.message}") from e
Exception: Server6请求返回错误 404 (URL: http://************:8009/mdb/entries/602009, Employee: N/A): Not Found
2025-06-30 13:11:38,759 - ERROR - ❌ 推送作业失败 33: Server6请求返回错误 404 (URL: http://************:8009/mdb/entries/602009, Employee: N/A): Not Found
2025-06-30 13:11:38,759 - app.services.f2_push_writer - ERROR - ❌ 推送作业失败 33: Server6请求返回错误 404 (URL: http://************:8009/mdb/entries/602009, Employee: N/A): Not Found
2025-06-30 13:11:38,759 - ERROR - ❌ 推送任务处理失败: Server6请求返回错误 404 (URL: http://************:8009/mdb/entries/602009, Employee: N/A): Not Found
2025-06-30 13:11:38,759 - app.services.f2_push_writer - ERROR - ❌ 推送任务处理失败: Server6请求返回错误 404 (URL: http://************:8009/mdb/entries/602009, Employee: N/A): Not Found
2025-06-30 13:11:38,759 - ERROR - ❌ 推送作业失败 35: Server6请求返回错误 404 (URL: http://************:8009/mdb/entries/602009, Employee: N/A): Not Found
2025-06-30 13:11:38,759 - app.services.f2_push_writer - ERROR - ❌ 推送作业失败 35: Server6请求返回错误 404 (URL: http://************:8009/mdb/entries/602009, Employee: N/A): Not Found
2025-06-30 13:11:38,760 - ERROR - ❌ 推送任务处理失败: Server6请求返回错误 404 (URL: http://************:8009/mdb/entries/602009, Employee: N/A): Not Found
2025-06-30 13:11:38,760 - app.services.f2_push_writer - ERROR - ❌ 推送任务处理失败: Server6请求返回错误 404 (URL: http://************:8009/mdb/entries/602009, Employee: N/A): Not Found
2025-06-30 13:11:38,760 - ERROR - ❌ 推送作业失败 36: Server6请求返回错误 404 (URL: http://************:8009/mdb/entries/602009, Employee: N/A): Not Found
2025-06-30 13:11:38,760 - app.services.f2_push_writer - ERROR - ❌ 推送作业失败 36: Server6请求返回错误 404 (URL: http://************:8009/mdb/entries/602009, Employee: N/A): Not Found
2025-06-30 13:11:38,760 - ERROR - ❌ 推送任务处理失败: Server6请求返回错误 404 (URL: http://************:8009/mdb/entries/602009, Employee: N/A): Not Found
2025-06-30 13:11:38,760 - app.services.f2_push_writer - ERROR - ❌ 推送任务处理失败: Server6请求返回错误 404 (URL: http://************:8009/mdb/entries/602009, Employee: N/A): Not Found
2025-06-30 13:11:38,767 - ERROR - ❌ Server6请求失败: PUT http://************:8009/mdb/entries/602010 - Status: 404, Message: Not Found. Request Body: {"entry_id": 4932, "created_ts": "2025-06-30", "日付": "2025-06-05", "id": 4932, "external_id": 602010, "ts": "2025-06-30", "従業員ｺｰﾄﾞ": "272204", "機種": "", "号機": "", "工場製番": "", "工事番号": "", "ﾕﾆｯﾄ番号": "", "区分": 4, "項目": 0, "時間": 1.5, "所属ｺｰﾄﾞ": "111"}
2025-06-30 13:11:38,767 - app.utils.server6_client - ERROR - ❌ Server6请求失败: PUT http://************:8009/mdb/entries/602010 - Status: 404, Message: Not Found. Request Body: {"entry_id": 4932, "created_ts": "2025-06-30", "日付": "2025-06-05", "id": 4932, "external_id": 602010, "ts": "2025-06-30", "従業員ｺｰﾄﾞ": "272204", "機種": "", "号機": "", "工場製番": "", "工事番号": "", "ﾕﾆｯﾄ番号": "", "区分": 4, "項目": 0, "時間": 1.5, "所属ｺｰﾄﾞ": "111"}
2025-06-30 13:11:38,769 - ERROR - ❌ UPDATE操作失败: Server6请求返回错误 404 (URL: http://************:8009/mdb/entries/602010, Employee: N/A): Not Found
Traceback (most recent call last):
  File "/home/<USER>/Documents/testF/2025/ser25/server0613/MySuiteDatebase2402newuigood-yolo5-3/server5/app/utils/server6_client.py", line 78, in _make_request
    response.raise_for_status()  # 如果状态码是 4xx 或 5xx，则抛出异常
    ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/miniconda3/envs/my_suite_unified/lib/python3.12/site-packages/aiohttp/client_reqrep.py", line 629, in raise_for_status
    raise ClientResponseError(
aiohttp.client_exceptions.ClientResponseError: 404, message='Not Found', url='http://************:8009/mdb/entries/602010'

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "/home/<USER>/Documents/testF/2025/ser25/server0613/MySuiteDatebase2402newuigood-yolo5-3/server5/app/services/f2_push_writer.py", line 272, in _handle_update_operation
    response = await self.server6_client.update_entry(external_id, entry_data)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Documents/testF/2025/ser25/server0613/MySuiteDatebase2402newuigood-yolo5-3/server5/app/utils/server6_client.py", line 249, in update_entry
    return await self._make_request('PUT', f'/mdb/entries/{external_id}', json_data=serializable_data)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Documents/testF/2025/ser25/server0613/MySuiteDatebase2402newuigood-yolo5-3/server5/app/utils/server6_client.py", line 94, in _make_request
    raise Exception(f"Server6请求返回错误 {e.status} (URL: {url}, Employee: {json_data.get('employee_id', 'N/A')}): {e.message}") from e
Exception: Server6请求返回错误 404 (URL: http://************:8009/mdb/entries/602010, Employee: N/A): Not Found
2025-06-30 13:11:38,769 - app.services.f2_push_writer - ERROR - ❌ UPDATE操作失败: Server6请求返回错误 404 (URL: http://************:8009/mdb/entries/602010, Employee: N/A): Not Found
Traceback (most recent call last):
  File "/home/<USER>/Documents/testF/2025/ser25/server0613/MySuiteDatebase2402newuigood-yolo5-3/server5/app/utils/server6_client.py", line 78, in _make_request
    response.raise_for_status()  # 如果状态码是 4xx 或 5xx，则抛出异常
    ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/miniconda3/envs/my_suite_unified/lib/python3.12/site-packages/aiohttp/client_reqrep.py", line 629, in raise_for_status
    raise ClientResponseError(
aiohttp.client_exceptions.ClientResponseError: 404, message='Not Found', url='http://************:8009/mdb/entries/602010'

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "/home/<USER>/Documents/testF/2025/ser25/server0613/MySuiteDatebase2402newuigood-yolo5-3/server5/app/services/f2_push_writer.py", line 272, in _handle_update_operation
    response = await self.server6_client.update_entry(external_id, entry_data)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Documents/testF/2025/ser25/server0613/MySuiteDatebase2402newuigood-yolo5-3/server5/app/utils/server6_client.py", line 249, in update_entry
    return await self._make_request('PUT', f'/mdb/entries/{external_id}', json_data=serializable_data)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Documents/testF/2025/ser25/server0613/MySuiteDatebase2402newuigood-yolo5-3/server5/app/utils/server6_client.py", line 94, in _make_request
    raise Exception(f"Server6请求返回错误 {e.status} (URL: {url}, Employee: {json_data.get('employee_id', 'N/A')}): {e.message}") from e
Exception: Server6请求返回错误 404 (URL: http://************:8009/mdb/entries/602010, Employee: N/A): Not Found
2025-06-30 13:11:38,771 - ERROR - ❌ Server6请求失败: PUT http://************:8009/mdb/entries/602010 - Status: 404, Message: Not Found. Request Body: {"entry_id": 4932, "created_ts": "2025-06-30", "日付": "2025-06-05", "id": 4932, "external_id": 602010, "ts": "2025-06-30", "従業員ｺｰﾄﾞ": "272204", "機種": "", "号機": "", "工場製番": "", "工事番号": "", "ﾕﾆｯﾄ番号": "", "区分": 4, "項目": 0, "時間": 1.5, "所属ｺｰﾄﾞ": "111"}
2025-06-30 13:11:38,771 - app.utils.server6_client - ERROR - ❌ Server6请求失败: PUT http://************:8009/mdb/entries/602010 - Status: 404, Message: Not Found. Request Body: {"entry_id": 4932, "created_ts": "2025-06-30", "日付": "2025-06-05", "id": 4932, "external_id": 602010, "ts": "2025-06-30", "従業員ｺｰﾄﾞ": "272204", "機種": "", "号機": "", "工場製番": "", "工事番号": "", "ﾕﾆｯﾄ番号": "", "区分": 4, "項目": 0, "時間": 1.5, "所属ｺｰﾄﾞ": "111"}
2025-06-30 13:11:38,772 - ERROR - ❌ Server6请求失败: PUT http://************:8009/mdb/entries/602010 - Status: 404, Message: Not Found. Request Body: {"entry_id": 4932, "created_ts": "2025-06-30", "日付": "2025-06-05", "id": 4932, "external_id": 602010, "ts": "2025-06-30", "従業員ｺｰﾄﾞ": "272204", "機種": "", "号機": "", "工場製番": "", "工事番号": "", "ﾕﾆｯﾄ番号": "", "区分": 4, "項目": 0, "時間": 1.5, "所属ｺｰﾄﾞ": "111"}
2025-06-30 13:11:38,772 - app.utils.server6_client - ERROR - ❌ Server6请求失败: PUT http://************:8009/mdb/entries/602010 - Status: 404, Message: Not Found. Request Body: {"entry_id": 4932, "created_ts": "2025-06-30", "日付": "2025-06-05", "id": 4932, "external_id": 602010, "ts": "2025-06-30", "従業員ｺｰﾄﾞ": "272204", "機種": "", "号機": "", "工場製番": "", "工事番号": "", "ﾕﾆｯﾄ番号": "", "区分": 4, "項目": 0, "時間": 1.5, "所属ｺｰﾄﾞ": "111"}
2025-06-30 13:11:38,772 - ERROR - ❌ 推送作业失败 37: Server6请求返回错误 404 (URL: http://************:8009/mdb/entries/602010, Employee: N/A): Not Found
2025-06-30 13:11:38,772 - app.services.f2_push_writer - ERROR - ❌ 推送作业失败 37: Server6请求返回错误 404 (URL: http://************:8009/mdb/entries/602010, Employee: N/A): Not Found
2025-06-30 13:11:38,772 - ERROR - ❌ 推送任务处理失败: Server6请求返回错误 404 (URL: http://************:8009/mdb/entries/602010, Employee: N/A): Not Found
2025-06-30 13:11:38,772 - app.services.f2_push_writer - ERROR - ❌ 推送任务处理失败: Server6请求返回错误 404 (URL: http://************:8009/mdb/entries/602010, Employee: N/A): Not Found
2025-06-30 13:11:38,773 - ERROR - ❌ UPDATE操作失败: Server6请求返回错误 404 (URL: http://************:8009/mdb/entries/602010, Employee: N/A): Not Found
Traceback (most recent call last):
  File "/home/<USER>/Documents/testF/2025/ser25/server0613/MySuiteDatebase2402newuigood-yolo5-3/server5/app/utils/server6_client.py", line 78, in _make_request
    response.raise_for_status()  # 如果状态码是 4xx 或 5xx，则抛出异常
    ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/miniconda3/envs/my_suite_unified/lib/python3.12/site-packages/aiohttp/client_reqrep.py", line 629, in raise_for_status
    raise ClientResponseError(
aiohttp.client_exceptions.ClientResponseError: 404, message='Not Found', url='http://************:8009/mdb/entries/602010'

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "/home/<USER>/Documents/testF/2025/ser25/server0613/MySuiteDatebase2402newuigood-yolo5-3/server5/app/services/f2_push_writer.py", line 272, in _handle_update_operation
    response = await self.server6_client.update_entry(external_id, entry_data)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Documents/testF/2025/ser25/server0613/MySuiteDatebase2402newuigood-yolo5-3/server5/app/utils/server6_client.py", line 249, in update_entry
    return await self._make_request('PUT', f'/mdb/entries/{external_id}', json_data=serializable_data)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Documents/testF/2025/ser25/server0613/MySuiteDatebase2402newuigood-yolo5-3/server5/app/utils/server6_client.py", line 94, in _make_request
    raise Exception(f"Server6请求返回错误 {e.status} (URL: {url}, Employee: {json_data.get('employee_id', 'N/A')}): {e.message}") from e
Exception: Server6请求返回错误 404 (URL: http://************:8009/mdb/entries/602010, Employee: N/A): Not Found
2025-06-30 13:11:38,773 - app.services.f2_push_writer - ERROR - ❌ UPDATE操作失败: Server6请求返回错误 404 (URL: http://************:8009/mdb/entries/602010, Employee: N/A): Not Found
Traceback (most recent call last):
  File "/home/<USER>/Documents/testF/2025/ser25/server0613/MySuiteDatebase2402newuigood-yolo5-3/server5/app/utils/server6_client.py", line 78, in _make_request
    response.raise_for_status()  # 如果状态码是 4xx 或 5xx，则抛出异常
    ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/miniconda3/envs/my_suite_unified/lib/python3.12/site-packages/aiohttp/client_reqrep.py", line 629, in raise_for_status
    raise ClientResponseError(
aiohttp.client_exceptions.ClientResponseError: 404, message='Not Found', url='http://************:8009/mdb/entries/602010'

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "/home/<USER>/Documents/testF/2025/ser25/server0613/MySuiteDatebase2402newuigood-yolo5-3/server5/app/services/f2_push_writer.py", line 272, in _handle_update_operation
    response = await self.server6_client.update_entry(external_id, entry_data)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Documents/testF/2025/ser25/server0613/MySuiteDatebase2402newuigood-yolo5-3/server5/app/utils/server6_client.py", line 249, in update_entry
    return await self._make_request('PUT', f'/mdb/entries/{external_id}', json_data=serializable_data)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Documents/testF/2025/ser25/server0613/MySuiteDatebase2402newuigood-yolo5-3/server5/app/utils/server6_client.py", line 94, in _make_request
    raise Exception(f"Server6请求返回错误 {e.status} (URL: {url}, Employee: {json_data.get('employee_id', 'N/A')}): {e.message}") from e
Exception: Server6请求返回错误 404 (URL: http://************:8009/mdb/entries/602010, Employee: N/A): Not Found
2025-06-30 13:11:38,773 - ERROR - ❌ Server6请求失败: PUT http://************:8009/mdb/entries/602010 - Status: 404, Message: Not Found. Request Body: {"entry_id": 4932, "created_ts": "2025-06-30", "日付": "2025-06-05", "id": 4932, "external_id": 602010, "ts": "2025-06-30", "従業員ｺｰﾄﾞ": "272204", "機種": "", "号機": "", "工場製番": "", "工事番号": "", "ﾕﾆｯﾄ番号": "", "区分": 4, "項目": 0, "時間": 1.5, "所属ｺｰﾄﾞ": "111"}
2025-06-30 13:11:38,773 - app.utils.server6_client - ERROR - ❌ Server6请求失败: PUT http://************:8009/mdb/entries/602010 - Status: 404, Message: Not Found. Request Body: {"entry_id": 4932, "created_ts": "2025-06-30", "日付": "2025-06-05", "id": 4932, "external_id": 602010, "ts": "2025-06-30", "従業員ｺｰﾄﾞ": "272204", "機種": "", "号機": "", "工場製番": "", "工事番号": "", "ﾕﾆｯﾄ番号": "", "区分": 4, "項目": 0, "時間": 1.5, "所属ｺｰﾄﾞ": "111"}
2025-06-30 13:11:38,774 - ERROR - ❌ UPDATE操作失败: Server6请求返回错误 404 (URL: http://************:8009/mdb/entries/602010, Employee: N/A): Not Found
Traceback (most recent call last):
  File "/home/<USER>/Documents/testF/2025/ser25/server0613/MySuiteDatebase2402newuigood-yolo5-3/server5/app/utils/server6_client.py", line 78, in _make_request
    response.raise_for_status()  # 如果状态码是 4xx 或 5xx，则抛出异常
    ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/miniconda3/envs/my_suite_unified/lib/python3.12/site-packages/aiohttp/client_reqrep.py", line 629, in raise_for_status
    raise ClientResponseError(
aiohttp.client_exceptions.ClientResponseError: 404, message='Not Found', url='http://************:8009/mdb/entries/602010'

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "/home/<USER>/Documents/testF/2025/ser25/server0613/MySuiteDatebase2402newuigood-yolo5-3/server5/app/services/f2_push_writer.py", line 272, in _handle_update_operation
    response = await self.server6_client.update_entry(external_id, entry_data)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Documents/testF/2025/ser25/server0613/MySuiteDatebase2402newuigood-yolo5-3/server5/app/utils/server6_client.py", line 249, in update_entry
    return await self._make_request('PUT', f'/mdb/entries/{external_id}', json_data=serializable_data)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Documents/testF/2025/ser25/server0613/MySuiteDatebase2402newuigood-yolo5-3/server5/app/utils/server6_client.py", line 94, in _make_request
    raise Exception(f"Server6请求返回错误 {e.status} (URL: {url}, Employee: {json_data.get('employee_id', 'N/A')}): {e.message}") from e
Exception: Server6请求返回错误 404 (URL: http://************:8009/mdb/entries/602010, Employee: N/A): Not Found
2025-06-30 13:11:38,774 - app.services.f2_push_writer - ERROR - ❌ UPDATE操作失败: Server6请求返回错误 404 (URL: http://************:8009/mdb/entries/602010, Employee: N/A): Not Found
Traceback (most recent call last):
  File "/home/<USER>/Documents/testF/2025/ser25/server0613/MySuiteDatebase2402newuigood-yolo5-3/server5/app/utils/server6_client.py", line 78, in _make_request
    response.raise_for_status()  # 如果状态码是 4xx 或 5xx，则抛出异常
    ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/miniconda3/envs/my_suite_unified/lib/python3.12/site-packages/aiohttp/client_reqrep.py", line 629, in raise_for_status
    raise ClientResponseError(
aiohttp.client_exceptions.ClientResponseError: 404, message='Not Found', url='http://************:8009/mdb/entries/602010'

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "/home/<USER>/Documents/testF/2025/ser25/server0613/MySuiteDatebase2402newuigood-yolo5-3/server5/app/services/f2_push_writer.py", line 272, in _handle_update_operation
    response = await self.server6_client.update_entry(external_id, entry_data)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Documents/testF/2025/ser25/server0613/MySuiteDatebase2402newuigood-yolo5-3/server5/app/utils/server6_client.py", line 249, in update_entry
    return await self._make_request('PUT', f'/mdb/entries/{external_id}', json_data=serializable_data)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Documents/testF/2025/ser25/server0613/MySuiteDatebase2402newuigood-yolo5-3/server5/app/utils/server6_client.py", line 94, in _make_request
    raise Exception(f"Server6请求返回错误 {e.status} (URL: {url}, Employee: {json_data.get('employee_id', 'N/A')}): {e.message}") from e
Exception: Server6请求返回错误 404 (URL: http://************:8009/mdb/entries/602010, Employee: N/A): Not Found
2025-06-30 13:11:38,775 - ERROR - ❌ 推送作业失败 38: Server6请求返回错误 404 (URL: http://************:8009/mdb/entries/602010, Employee: N/A): Not Found
2025-06-30 13:11:38,775 - app.services.f2_push_writer - ERROR - ❌ 推送作业失败 38: Server6请求返回错误 404 (URL: http://************:8009/mdb/entries/602010, Employee: N/A): Not Found
2025-06-30 13:11:38,775 - ERROR - ❌ 推送任务处理失败: Server6请求返回错误 404 (URL: http://************:8009/mdb/entries/602010, Employee: N/A): Not Found
2025-06-30 13:11:38,775 - app.services.f2_push_writer - ERROR - ❌ 推送任务处理失败: Server6请求返回错误 404 (URL: http://************:8009/mdb/entries/602010, Employee: N/A): Not Found
2025-06-30 13:11:38,775 - ERROR - ❌ UPDATE操作失败: Server6请求返回错误 404 (URL: http://************:8009/mdb/entries/602010, Employee: N/A): Not Found
Traceback (most recent call last):
  File "/home/<USER>/Documents/testF/2025/ser25/server0613/MySuiteDatebase2402newuigood-yolo5-3/server5/app/utils/server6_client.py", line 78, in _make_request
    response.raise_for_status()  # 如果状态码是 4xx 或 5xx，则抛出异常
    ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/miniconda3/envs/my_suite_unified/lib/python3.12/site-packages/aiohttp/client_reqrep.py", line 629, in raise_for_status
    raise ClientResponseError(
aiohttp.client_exceptions.ClientResponseError: 404, message='Not Found', url='http://************:8009/mdb/entries/602010'

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "/home/<USER>/Documents/testF/2025/ser25/server0613/MySuiteDatebase2402newuigood-yolo5-3/server5/app/services/f2_push_writer.py", line 272, in _handle_update_operation
    response = await self.server6_client.update_entry(external_id, entry_data)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Documents/testF/2025/ser25/server0613/MySuiteDatebase2402newuigood-yolo5-3/server5/app/utils/server6_client.py", line 249, in update_entry
    return await self._make_request('PUT', f'/mdb/entries/{external_id}', json_data=serializable_data)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Documents/testF/2025/ser25/server0613/MySuiteDatebase2402newuigood-yolo5-3/server5/app/utils/server6_client.py", line 94, in _make_request
    raise Exception(f"Server6请求返回错误 {e.status} (URL: {url}, Employee: {json_data.get('employee_id', 'N/A')}): {e.message}") from e
Exception: Server6请求返回错误 404 (URL: http://************:8009/mdb/entries/602010, Employee: N/A): Not Found
2025-06-30 13:11:38,775 - app.services.f2_push_writer - ERROR - ❌ UPDATE操作失败: Server6请求返回错误 404 (URL: http://************:8009/mdb/entries/602010, Employee: N/A): Not Found
Traceback (most recent call last):
  File "/home/<USER>/Documents/testF/2025/ser25/server0613/MySuiteDatebase2402newuigood-yolo5-3/server5/app/utils/server6_client.py", line 78, in _make_request
    response.raise_for_status()  # 如果状态码是 4xx 或 5xx，则抛出异常
    ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/miniconda3/envs/my_suite_unified/lib/python3.12/site-packages/aiohttp/client_reqrep.py", line 629, in raise_for_status
    raise ClientResponseError(
aiohttp.client_exceptions.ClientResponseError: 404, message='Not Found', url='http://************:8009/mdb/entries/602010'

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "/home/<USER>/Documents/testF/2025/ser25/server0613/MySuiteDatebase2402newuigood-yolo5-3/server5/app/services/f2_push_writer.py", line 272, in _handle_update_operation
    response = await self.server6_client.update_entry(external_id, entry_data)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Documents/testF/2025/ser25/server0613/MySuiteDatebase2402newuigood-yolo5-3/server5/app/utils/server6_client.py", line 249, in update_entry
    return await self._make_request('PUT', f'/mdb/entries/{external_id}', json_data=serializable_data)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Documents/testF/2025/ser25/server0613/MySuiteDatebase2402newuigood-yolo5-3/server5/app/utils/server6_client.py", line 94, in _make_request
    raise Exception(f"Server6请求返回错误 {e.status} (URL: {url}, Employee: {json_data.get('employee_id', 'N/A')}): {e.message}") from e
Exception: Server6请求返回错误 404 (URL: http://************:8009/mdb/entries/602010, Employee: N/A): Not Found
2025-06-30 13:11:38,777 - ERROR - ❌ 推送作业失败 40: Server6请求返回错误 404 (URL: http://************:8009/mdb/entries/602010, Employee: N/A): Not Found
2025-06-30 13:11:38,777 - app.services.f2_push_writer - ERROR - ❌ 推送作业失败 40: Server6请求返回错误 404 (URL: http://************:8009/mdb/entries/602010, Employee: N/A): Not Found
2025-06-30 13:11:38,777 - ERROR - ❌ 推送任务处理失败: Server6请求返回错误 404 (URL: http://************:8009/mdb/entries/602010, Employee: N/A): Not Found
2025-06-30 13:11:38,777 - app.services.f2_push_writer - ERROR - ❌ 推送任务处理失败: Server6请求返回错误 404 (URL: http://************:8009/mdb/entries/602010, Employee: N/A): Not Found
2025-06-30 13:11:38,778 - ERROR - ❌ 推送作业失败 39: Server6请求返回错误 404 (URL: http://************:8009/mdb/entries/602010, Employee: N/A): Not Found
2025-06-30 13:11:38,778 - app.services.f2_push_writer - ERROR - ❌ 推送作业失败 39: Server6请求返回错误 404 (URL: http://************:8009/mdb/entries/602010, Employee: N/A): Not Found
2025-06-30 13:11:38,778 - ERROR - ❌ 推送任务处理失败: Server6请求返回错误 404 (URL: http://************:8009/mdb/entries/602010, Employee: N/A): Not Found
2025-06-30 13:11:38,778 - app.services.f2_push_writer - ERROR - ❌ 推送任务处理失败: Server6请求返回错误 404 (URL: http://************:8009/mdb/entries/602010, Employee: N/A): Not Found
2025-06-30 13:11:38,783 - ERROR - ❌ Server6请求失败: PUT http://************:8009/mdb/entries/602011 - Status: 404, Message: Not Found. Request Body: {"entry_id": 4933, "created_ts": "2025-06-30", "日付": "2025-06-06", "id": 4933, "external_id": 602011, "ts": "2025-06-30", "従業員ｺｰﾄﾞ": "272204", "機種": "", "号機": "", "工場製番": "", "工事番号": "", "ﾕﾆｯﾄ番号": "", "区分": 4, "項目": 0, "時間": 1.0, "所属ｺｰﾄﾞ": "111"}
2025-06-30 13:11:38,783 - app.utils.server6_client - ERROR - ❌ Server6请求失败: PUT http://************:8009/mdb/entries/602011 - Status: 404, Message: Not Found. Request Body: {"entry_id": 4933, "created_ts": "2025-06-30", "日付": "2025-06-06", "id": 4933, "external_id": 602011, "ts": "2025-06-30", "従業員ｺｰﾄﾞ": "272204", "機種": "", "号機": "", "工場製番": "", "工事番号": "", "ﾕﾆｯﾄ番号": "", "区分": 4, "項目": 0, "時間": 1.0, "所属ｺｰﾄﾞ": "111"}
2025-06-30 13:11:38,786 - ERROR - ❌ UPDATE操作失败: Server6请求返回错误 404 (URL: http://************:8009/mdb/entries/602011, Employee: N/A): Not Found
Traceback (most recent call last):
  File "/home/<USER>/Documents/testF/2025/ser25/server0613/MySuiteDatebase2402newuigood-yolo5-3/server5/app/utils/server6_client.py", line 78, in _make_request
    response.raise_for_status()  # 如果状态码是 4xx 或 5xx，则抛出异常
    ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/miniconda3/envs/my_suite_unified/lib/python3.12/site-packages/aiohttp/client_reqrep.py", line 629, in raise_for_status
    raise ClientResponseError(
aiohttp.client_exceptions.ClientResponseError: 404, message='Not Found', url='http://************:8009/mdb/entries/602011'

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "/home/<USER>/Documents/testF/2025/ser25/server0613/MySuiteDatebase2402newuigood-yolo5-3/server5/app/services/f2_push_writer.py", line 272, in _handle_update_operation
    response = await self.server6_client.update_entry(external_id, entry_data)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Documents/testF/2025/ser25/server0613/MySuiteDatebase2402newuigood-yolo5-3/server5/app/utils/server6_client.py", line 249, in update_entry
    return await self._make_request('PUT', f'/mdb/entries/{external_id}', json_data=serializable_data)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Documents/testF/2025/ser25/server0613/MySuiteDatebase2402newuigood-yolo5-3/server5/app/utils/server6_client.py", line 94, in _make_request
    raise Exception(f"Server6请求返回错误 {e.status} (URL: {url}, Employee: {json_data.get('employee_id', 'N/A')}): {e.message}") from e
Exception: Server6请求返回错误 404 (URL: http://************:8009/mdb/entries/602011, Employee: N/A): Not Found
2025-06-30 13:11:38,786 - app.services.f2_push_writer - ERROR - ❌ UPDATE操作失败: Server6请求返回错误 404 (URL: http://************:8009/mdb/entries/602011, Employee: N/A): Not Found
Traceback (most recent call last):
  File "/home/<USER>/Documents/testF/2025/ser25/server0613/MySuiteDatebase2402newuigood-yolo5-3/server5/app/utils/server6_client.py", line 78, in _make_request
    response.raise_for_status()  # 如果状态码是 4xx 或 5xx，则抛出异常
    ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/miniconda3/envs/my_suite_unified/lib/python3.12/site-packages/aiohttp/client_reqrep.py", line 629, in raise_for_status
    raise ClientResponseError(
aiohttp.client_exceptions.ClientResponseError: 404, message='Not Found', url='http://************:8009/mdb/entries/602011'

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "/home/<USER>/Documents/testF/2025/ser25/server0613/MySuiteDatebase2402newuigood-yolo5-3/server5/app/services/f2_push_writer.py", line 272, in _handle_update_operation
    response = await self.server6_client.update_entry(external_id, entry_data)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Documents/testF/2025/ser25/server0613/MySuiteDatebase2402newuigood-yolo5-3/server5/app/utils/server6_client.py", line 249, in update_entry
    return await self._make_request('PUT', f'/mdb/entries/{external_id}', json_data=serializable_data)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Documents/testF/2025/ser25/server0613/MySuiteDatebase2402newuigood-yolo5-3/server5/app/utils/server6_client.py", line 94, in _make_request
    raise Exception(f"Server6请求返回错误 {e.status} (URL: {url}, Employee: {json_data.get('employee_id', 'N/A')}): {e.message}") from e
Exception: Server6请求返回错误 404 (URL: http://************:8009/mdb/entries/602011, Employee: N/A): Not Found
2025-06-30 13:11:38,787 - ERROR - ❌ Server6请求失败: PUT http://************:8009/mdb/entries/602011 - Status: 404, Message: Not Found. Request Body: {"entry_id": 4933, "created_ts": "2025-06-30", "日付": "2025-06-06", "id": 4933, "external_id": 602011, "ts": "2025-06-30", "従業員ｺｰﾄﾞ": "272204", "機種": "", "号機": "", "工場製番": "", "工事番号": "", "ﾕﾆｯﾄ番号": "", "区分": 4, "項目": 0, "時間": 1.0, "所属ｺｰﾄﾞ": "111"}
2025-06-30 13:11:38,787 - app.utils.server6_client - ERROR - ❌ Server6请求失败: PUT http://************:8009/mdb/entries/602011 - Status: 404, Message: Not Found. Request Body: {"entry_id": 4933, "created_ts": "2025-06-30", "日付": "2025-06-06", "id": 4933, "external_id": 602011, "ts": "2025-06-30", "従業員ｺｰﾄﾞ": "272204", "機種": "", "号機": "", "工場製番": "", "工事番号": "", "ﾕﾆｯﾄ番号": "", "区分": 4, "項目": 0, "時間": 1.0, "所属ｺｰﾄﾞ": "111"}
2025-06-30 13:11:38,788 - ERROR - ❌ 推送作业失败 41: Server6请求返回错误 404 (URL: http://************:8009/mdb/entries/602011, Employee: N/A): Not Found
2025-06-30 13:11:38,788 - app.services.f2_push_writer - ERROR - ❌ 推送作业失败 41: Server6请求返回错误 404 (URL: http://************:8009/mdb/entries/602011, Employee: N/A): Not Found
2025-06-30 13:11:38,788 - ERROR - ❌ 推送任务处理失败: Server6请求返回错误 404 (URL: http://************:8009/mdb/entries/602011, Employee: N/A): Not Found
2025-06-30 13:11:38,788 - app.services.f2_push_writer - ERROR - ❌ 推送任务处理失败: Server6请求返回错误 404 (URL: http://************:8009/mdb/entries/602011, Employee: N/A): Not Found
2025-06-30 13:11:38,789 - ERROR - ❌ UPDATE操作失败: Server6请求返回错误 404 (URL: http://************:8009/mdb/entries/602011, Employee: N/A): Not Found
Traceback (most recent call last):
  File "/home/<USER>/Documents/testF/2025/ser25/server0613/MySuiteDatebase2402newuigood-yolo5-3/server5/app/utils/server6_client.py", line 78, in _make_request
    response.raise_for_status()  # 如果状态码是 4xx 或 5xx，则抛出异常
    ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/miniconda3/envs/my_suite_unified/lib/python3.12/site-packages/aiohttp/client_reqrep.py", line 629, in raise_for_status
    raise ClientResponseError(
aiohttp.client_exceptions.ClientResponseError: 404, message='Not Found', url='http://************:8009/mdb/entries/602011'

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "/home/<USER>/Documents/testF/2025/ser25/server0613/MySuiteDatebase2402newuigood-yolo5-3/server5/app/services/f2_push_writer.py", line 272, in _handle_update_operation
    response = await self.server6_client.update_entry(external_id, entry_data)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Documents/testF/2025/ser25/server0613/MySuiteDatebase2402newuigood-yolo5-3/server5/app/utils/server6_client.py", line 249, in update_entry
    return await self._make_request('PUT', f'/mdb/entries/{external_id}', json_data=serializable_data)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Documents/testF/2025/ser25/server0613/MySuiteDatebase2402newuigood-yolo5-3/server5/app/utils/server6_client.py", line 94, in _make_request
    raise Exception(f"Server6请求返回错误 {e.status} (URL: {url}, Employee: {json_data.get('employee_id', 'N/A')}): {e.message}") from e
Exception: Server6请求返回错误 404 (URL: http://************:8009/mdb/entries/602011, Employee: N/A): Not Found
2025-06-30 13:11:38,789 - app.services.f2_push_writer - ERROR - ❌ UPDATE操作失败: Server6请求返回错误 404 (URL: http://************:8009/mdb/entries/602011, Employee: N/A): Not Found
Traceback (most recent call last):
  File "/home/<USER>/Documents/testF/2025/ser25/server0613/MySuiteDatebase2402newuigood-yolo5-3/server5/app/utils/server6_client.py", line 78, in _make_request
    response.raise_for_status()  # 如果状态码是 4xx 或 5xx，则抛出异常
    ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/miniconda3/envs/my_suite_unified/lib/python3.12/site-packages/aiohttp/client_reqrep.py", line 629, in raise_for_status
    raise ClientResponseError(
aiohttp.client_exceptions.ClientResponseError: 404, message='Not Found', url='http://************:8009/mdb/entries/602011'

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "/home/<USER>/Documents/testF/2025/ser25/server0613/MySuiteDatebase2402newuigood-yolo5-3/server5/app/services/f2_push_writer.py", line 272, in _handle_update_operation
    response = await self.server6_client.update_entry(external_id, entry_data)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Documents/testF/2025/ser25/server0613/MySuiteDatebase2402newuigood-yolo5-3/server5/app/utils/server6_client.py", line 249, in update_entry
    return await self._make_request('PUT', f'/mdb/entries/{external_id}', json_data=serializable_data)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Documents/testF/2025/ser25/server0613/MySuiteDatebase2402newuigood-yolo5-3/server5/app/utils/server6_client.py", line 94, in _make_request
    raise Exception(f"Server6请求返回错误 {e.status} (URL: {url}, Employee: {json_data.get('employee_id', 'N/A')}): {e.message}") from e
Exception: Server6请求返回错误 404 (URL: http://************:8009/mdb/entries/602011, Employee: N/A): Not Found
2025-06-30 13:11:38,790 - ERROR - ❌ Server6请求失败: PUT http://************:8009/mdb/entries/602011 - Status: 404, Message: Not Found. Request Body: {"entry_id": 4933, "created_ts": "2025-06-30", "日付": "2025-06-06", "id": 4933, "external_id": 602011, "ts": "2025-06-30", "従業員ｺｰﾄﾞ": "272204", "機種": "", "号機": "", "工場製番": "", "工事番号": "", "ﾕﾆｯﾄ番号": "", "区分": 4, "項目": 0, "時間": 1.0, "所属ｺｰﾄﾞ": "111"}
2025-06-30 13:11:38,790 - app.utils.server6_client - ERROR - ❌ Server6请求失败: PUT http://************:8009/mdb/entries/602011 - Status: 404, Message: Not Found. Request Body: {"entry_id": 4933, "created_ts": "2025-06-30", "日付": "2025-06-06", "id": 4933, "external_id": 602011, "ts": "2025-06-30", "従業員ｺｰﾄﾞ": "272204", "機種": "", "号機": "", "工場製番": "", "工事番号": "", "ﾕﾆｯﾄ番号": "", "区分": 4, "項目": 0, "時間": 1.0, "所属ｺｰﾄﾞ": "111"}
2025-06-30 13:11:38,791 - ERROR - ❌ Server6请求失败: PUT http://************:8009/mdb/entries/602011 - Status: 404, Message: Not Found. Request Body: {"entry_id": 4933, "created_ts": "2025-06-30", "日付": "2025-06-06", "id": 4933, "external_id": 602011, "ts": "2025-06-30", "従業員ｺｰﾄﾞ": "272204", "機種": "", "号機": "", "工場製番": "", "工事番号": "", "ﾕﾆｯﾄ番号": "", "区分": 4, "項目": 0, "時間": 1.0, "所属ｺｰﾄﾞ": "111"}
2025-06-30 13:11:38,791 - app.utils.server6_client - ERROR - ❌ Server6请求失败: PUT http://************:8009/mdb/entries/602011 - Status: 404, Message: Not Found. Request Body: {"entry_id": 4933, "created_ts": "2025-06-30", "日付": "2025-06-06", "id": 4933, "external_id": 602011, "ts": "2025-06-30", "従業員ｺｰﾄﾞ": "272204", "機種": "", "号機": "", "工場製番": "", "工事番号": "", "ﾕﾆｯﾄ番号": "", "区分": 4, "項目": 0, "時間": 1.0, "所属ｺｰﾄﾞ": "111"}
2025-06-30 13:11:38,792 - ERROR - ❌ 推送作业失败 42: Server6请求返回错误 404 (URL: http://************:8009/mdb/entries/602011, Employee: N/A): Not Found
2025-06-30 13:11:38,792 - app.services.f2_push_writer - ERROR - ❌ 推送作业失败 42: Server6请求返回错误 404 (URL: http://************:8009/mdb/entries/602011, Employee: N/A): Not Found
2025-06-30 13:11:38,792 - ERROR - ❌ 推送任务处理失败: Server6请求返回错误 404 (URL: http://************:8009/mdb/entries/602011, Employee: N/A): Not Found
2025-06-30 13:11:38,792 - app.services.f2_push_writer - ERROR - ❌ 推送任务处理失败: Server6请求返回错误 404 (URL: http://************:8009/mdb/entries/602011, Employee: N/A): Not Found
2025-06-30 13:11:38,792 - ERROR - ❌ UPDATE操作失败: Server6请求返回错误 404 (URL: http://************:8009/mdb/entries/602011, Employee: N/A): Not Found
Traceback (most recent call last):
  File "/home/<USER>/Documents/testF/2025/ser25/server0613/MySuiteDatebase2402newuigood-yolo5-3/server5/app/utils/server6_client.py", line 78, in _make_request
    response.raise_for_status()  # 如果状态码是 4xx 或 5xx，则抛出异常
    ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/miniconda3/envs/my_suite_unified/lib/python3.12/site-packages/aiohttp/client_reqrep.py", line 629, in raise_for_status
    raise ClientResponseError(
aiohttp.client_exceptions.ClientResponseError: 404, message='Not Found', url='http://************:8009/mdb/entries/602011'

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "/home/<USER>/Documents/testF/2025/ser25/server0613/MySuiteDatebase2402newuigood-yolo5-3/server5/app/services/f2_push_writer.py", line 272, in _handle_update_operation
    response = await self.server6_client.update_entry(external_id, entry_data)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Documents/testF/2025/ser25/server0613/MySuiteDatebase2402newuigood-yolo5-3/server5/app/utils/server6_client.py", line 249, in update_entry
    return await self._make_request('PUT', f'/mdb/entries/{external_id}', json_data=serializable_data)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Documents/testF/2025/ser25/server0613/MySuiteDatebase2402newuigood-yolo5-3/server5/app/utils/server6_client.py", line 94, in _make_request
    raise Exception(f"Server6请求返回错误 {e.status} (URL: {url}, Employee: {json_data.get('employee_id', 'N/A')}): {e.message}") from e
Exception: Server6请求返回错误 404 (URL: http://************:8009/mdb/entries/602011, Employee: N/A): Not Found
2025-06-30 13:11:38,792 - app.services.f2_push_writer - ERROR - ❌ UPDATE操作失败: Server6请求返回错误 404 (URL: http://************:8009/mdb/entries/602011, Employee: N/A): Not Found
Traceback (most recent call last):
  File "/home/<USER>/Documents/testF/2025/ser25/server0613/MySuiteDatebase2402newuigood-yolo5-3/server5/app/utils/server6_client.py", line 78, in _make_request
    response.raise_for_status()  # 如果状态码是 4xx 或 5xx，则抛出异常
    ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/miniconda3/envs/my_suite_unified/lib/python3.12/site-packages/aiohttp/client_reqrep.py", line 629, in raise_for_status
    raise ClientResponseError(
aiohttp.client_exceptions.ClientResponseError: 404, message='Not Found', url='http://************:8009/mdb/entries/602011'

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "/home/<USER>/Documents/testF/2025/ser25/server0613/MySuiteDatebase2402newuigood-yolo5-3/server5/app/services/f2_push_writer.py", line 272, in _handle_update_operation
    response = await self.server6_client.update_entry(external_id, entry_data)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Documents/testF/2025/ser25/server0613/MySuiteDatebase2402newuigood-yolo5-3/server5/app/utils/server6_client.py", line 249, in update_entry
    return await self._make_request('PUT', f'/mdb/entries/{external_id}', json_data=serializable_data)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Documents/testF/2025/ser25/server0613/MySuiteDatebase2402newuigood-yolo5-3/server5/app/utils/server6_client.py", line 94, in _make_request
    raise Exception(f"Server6请求返回错误 {e.status} (URL: {url}, Employee: {json_data.get('employee_id', 'N/A')}): {e.message}") from e
Exception: Server6请求返回错误 404 (URL: http://************:8009/mdb/entries/602011, Employee: N/A): Not Found
2025-06-30 13:11:38,793 - ERROR - ❌ UPDATE操作失败: Server6请求返回错误 404 (URL: http://************:8009/mdb/entries/602011, Employee: N/A): Not Found
Traceback (most recent call last):
  File "/home/<USER>/Documents/testF/2025/ser25/server0613/MySuiteDatebase2402newuigood-yolo5-3/server5/app/utils/server6_client.py", line 78, in _make_request
    response.raise_for_status()  # 如果状态码是 4xx 或 5xx，则抛出异常
    ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/miniconda3/envs/my_suite_unified/lib/python3.12/site-packages/aiohttp/client_reqrep.py", line 629, in raise_for_status
    raise ClientResponseError(
aiohttp.client_exceptions.ClientResponseError: 404, message='Not Found', url='http://************:8009/mdb/entries/602011'

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "/home/<USER>/Documents/testF/2025/ser25/server0613/MySuiteDatebase2402newuigood-yolo5-3/server5/app/services/f2_push_writer.py", line 272, in _handle_update_operation
    response = await self.server6_client.update_entry(external_id, entry_data)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Documents/testF/2025/ser25/server0613/MySuiteDatebase2402newuigood-yolo5-3/server5/app/utils/server6_client.py", line 249, in update_entry
    return await self._make_request('PUT', f'/mdb/entries/{external_id}', json_data=serializable_data)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Documents/testF/2025/ser25/server0613/MySuiteDatebase2402newuigood-yolo5-3/server5/app/utils/server6_client.py", line 94, in _make_request
    raise Exception(f"Server6请求返回错误 {e.status} (URL: {url}, Employee: {json_data.get('employee_id', 'N/A')}): {e.message}") from e
Exception: Server6请求返回错误 404 (URL: http://************:8009/mdb/entries/602011, Employee: N/A): Not Found
2025-06-30 13:11:38,793 - app.services.f2_push_writer - ERROR - ❌ UPDATE操作失败: Server6请求返回错误 404 (URL: http://************:8009/mdb/entries/602011, Employee: N/A): Not Found
Traceback (most recent call last):
  File "/home/<USER>/Documents/testF/2025/ser25/server0613/MySuiteDatebase2402newuigood-yolo5-3/server5/app/utils/server6_client.py", line 78, in _make_request
    response.raise_for_status()  # 如果状态码是 4xx 或 5xx，则抛出异常
    ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/miniconda3/envs/my_suite_unified/lib/python3.12/site-packages/aiohttp/client_reqrep.py", line 629, in raise_for_status
    raise ClientResponseError(
aiohttp.client_exceptions.ClientResponseError: 404, message='Not Found', url='http://************:8009/mdb/entries/602011'

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "/home/<USER>/Documents/testF/2025/ser25/server0613/MySuiteDatebase2402newuigood-yolo5-3/server5/app/services/f2_push_writer.py", line 272, in _handle_update_operation
    response = await self.server6_client.update_entry(external_id, entry_data)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Documents/testF/2025/ser25/server0613/MySuiteDatebase2402newuigood-yolo5-3/server5/app/utils/server6_client.py", line 249, in update_entry
    return await self._make_request('PUT', f'/mdb/entries/{external_id}', json_data=serializable_data)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Documents/testF/2025/ser25/server0613/MySuiteDatebase2402newuigood-yolo5-3/server5/app/utils/server6_client.py", line 94, in _make_request
    raise Exception(f"Server6请求返回错误 {e.status} (URL: {url}, Employee: {json_data.get('employee_id', 'N/A')}): {e.message}") from e
Exception: Server6请求返回错误 404 (URL: http://************:8009/mdb/entries/602011, Employee: N/A): Not Found
2025-06-30 13:11:38,794 - ERROR - ❌ 推送作业失败 43: Server6请求返回错误 404 (URL: http://************:8009/mdb/entries/602011, Employee: N/A): Not Found
2025-06-30 13:11:38,794 - app.services.f2_push_writer - ERROR - ❌ 推送作业失败 43: Server6请求返回错误 404 (URL: http://************:8009/mdb/entries/602011, Employee: N/A): Not Found
2025-06-30 13:11:38,794 - ERROR - ❌ 推送任务处理失败: Server6请求返回错误 404 (URL: http://************:8009/mdb/entries/602011, Employee: N/A): Not Found
2025-06-30 13:11:38,794 - app.services.f2_push_writer - ERROR - ❌ 推送任务处理失败: Server6请求返回错误 404 (URL: http://************:8009/mdb/entries/602011, Employee: N/A): Not Found
2025-06-30 13:11:38,796 - ERROR - ❌ 推送作业失败 44: Server6请求返回错误 404 (URL: http://************:8009/mdb/entries/602011, Employee: N/A): Not Found
2025-06-30 13:11:38,796 - app.services.f2_push_writer - ERROR - ❌ 推送作业失败 44: Server6请求返回错误 404 (URL: http://************:8009/mdb/entries/602011, Employee: N/A): Not Found
2025-06-30 13:11:38,796 - ERROR - ❌ 推送任务处理失败: Server6请求返回错误 404 (URL: http://************:8009/mdb/entries/602011, Employee: N/A): Not Found
2025-06-30 13:11:38,796 - app.services.f2_push_writer - ERROR - ❌ 推送任务处理失败: Server6请求返回错误 404 (URL: http://************:8009/mdb/entries/602011, Employee: N/A): Not Found
2025-06-30 13:11:38,798 - ERROR - ❌ Server6请求失败: PUT http://************:8009/mdb/entries/602012 - Status: 404, Message: Not Found. Request Body: {"entry_id": 4934, "created_ts": "2025-06-30", "日付": "2025-06-06", "id": 4934, "external_id": 602012, "ts": "2025-06-30", "従業員ｺｰﾄﾞ": "272204", "機種": "", "号機": "", "工場製番": "", "工事番号": "", "ﾕﾆｯﾄ番号": "", "区分": 4, "項目": 0, "時間": 5.0, "所属ｺｰﾄﾞ": "111"}
2025-06-30 13:11:38,798 - app.utils.server6_client - ERROR - ❌ Server6请求失败: PUT http://************:8009/mdb/entries/602012 - Status: 404, Message: Not Found. Request Body: {"entry_id": 4934, "created_ts": "2025-06-30", "日付": "2025-06-06", "id": 4934, "external_id": 602012, "ts": "2025-06-30", "従業員ｺｰﾄﾞ": "272204", "機種": "", "号機": "", "工場製番": "", "工事番号": "", "ﾕﾆｯﾄ番号": "", "区分": 4, "項目": 0, "時間": 5.0, "所属ｺｰﾄﾞ": "111"}
2025-06-30 13:11:38,800 - ERROR - ❌ UPDATE操作失败: Server6请求返回错误 404 (URL: http://************:8009/mdb/entries/602012, Employee: N/A): Not Found
Traceback (most recent call last):
  File "/home/<USER>/Documents/testF/2025/ser25/server0613/MySuiteDatebase2402newuigood-yolo5-3/server5/app/utils/server6_client.py", line 78, in _make_request
    response.raise_for_status()  # 如果状态码是 4xx 或 5xx，则抛出异常
    ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/miniconda3/envs/my_suite_unified/lib/python3.12/site-packages/aiohttp/client_reqrep.py", line 629, in raise_for_status
    raise ClientResponseError(
aiohttp.client_exceptions.ClientResponseError: 404, message='Not Found', url='http://************:8009/mdb/entries/602012'

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "/home/<USER>/Documents/testF/2025/ser25/server0613/MySuiteDatebase2402newuigood-yolo5-3/server5/app/services/f2_push_writer.py", line 272, in _handle_update_operation
    response = await self.server6_client.update_entry(external_id, entry_data)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Documents/testF/2025/ser25/server0613/MySuiteDatebase2402newuigood-yolo5-3/server5/app/utils/server6_client.py", line 249, in update_entry
    return await self._make_request('PUT', f'/mdb/entries/{external_id}', json_data=serializable_data)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Documents/testF/2025/ser25/server0613/MySuiteDatebase2402newuigood-yolo5-3/server5/app/utils/server6_client.py", line 94, in _make_request
    raise Exception(f"Server6请求返回错误 {e.status} (URL: {url}, Employee: {json_data.get('employee_id', 'N/A')}): {e.message}") from e
Exception: Server6请求返回错误 404 (URL: http://************:8009/mdb/entries/602012, Employee: N/A): Not Found
2025-06-30 13:11:38,800 - app.services.f2_push_writer - ERROR - ❌ UPDATE操作失败: Server6请求返回错误 404 (URL: http://************:8009/mdb/entries/602012, Employee: N/A): Not Found
Traceback (most recent call last):
  File "/home/<USER>/Documents/testF/2025/ser25/server0613/MySuiteDatebase2402newuigood-yolo5-3/server5/app/utils/server6_client.py", line 78, in _make_request
    response.raise_for_status()  # 如果状态码是 4xx 或 5xx，则抛出异常
    ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/miniconda3/envs/my_suite_unified/lib/python3.12/site-packages/aiohttp/client_reqrep.py", line 629, in raise_for_status
    raise ClientResponseError(
aiohttp.client_exceptions.ClientResponseError: 404, message='Not Found', url='http://************:8009/mdb/entries/602012'

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "/home/<USER>/Documents/testF/2025/ser25/server0613/MySuiteDatebase2402newuigood-yolo5-3/server5/app/services/f2_push_writer.py", line 272, in _handle_update_operation
    response = await self.server6_client.update_entry(external_id, entry_data)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Documents/testF/2025/ser25/server0613/MySuiteDatebase2402newuigood-yolo5-3/server5/app/utils/server6_client.py", line 249, in update_entry
    return await self._make_request('PUT', f'/mdb/entries/{external_id}', json_data=serializable_data)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Documents/testF/2025/ser25/server0613/MySuiteDatebase2402newuigood-yolo5-3/server5/app/utils/server6_client.py", line 94, in _make_request
    raise Exception(f"Server6请求返回错误 {e.status} (URL: {url}, Employee: {json_data.get('employee_id', 'N/A')}): {e.message}") from e
Exception: Server6请求返回错误 404 (URL: http://************:8009/mdb/entries/602012, Employee: N/A): Not Found
2025-06-30 13:11:38,802 - ERROR - ❌ 推送作业失败 45: Server6请求返回错误 404 (URL: http://************:8009/mdb/entries/602012, Employee: N/A): Not Found
2025-06-30 13:11:38,802 - app.services.f2_push_writer - ERROR - ❌ 推送作业失败 45: Server6请求返回错误 404 (URL: http://************:8009/mdb/entries/602012, Employee: N/A): Not Found
2025-06-30 13:11:38,802 - ERROR - ❌ 推送任务处理失败: Server6请求返回错误 404 (URL: http://************:8009/mdb/entries/602012, Employee: N/A): Not Found
2025-06-30 13:11:38,802 - app.services.f2_push_writer - ERROR - ❌ 推送任务处理失败: Server6请求返回错误 404 (URL: http://************:8009/mdb/entries/602012, Employee: N/A): Not Found
2025-06-30 13:11:38,803 - ERROR - ❌ Server6请求失败: PUT http://************:8009/mdb/entries/602012 - Status: 404, Message: Not Found. Request Body: {"entry_id": 4934, "created_ts": "2025-06-30", "日付": "2025-06-06", "id": 4934, "external_id": 602012, "ts": "2025-06-30", "従業員ｺｰﾄﾞ": "272204", "機種": "", "号機": "", "工場製番": "", "工事番号": "", "ﾕﾆｯﾄ番号": "", "区分": 4, "項目": 0, "時間": 5.0, "所属ｺｰﾄﾞ": "111"}
2025-06-30 13:11:38,803 - app.utils.server6_client - ERROR - ❌ Server6请求失败: PUT http://************:8009/mdb/entries/602012 - Status: 404, Message: Not Found. Request Body: {"entry_id": 4934, "created_ts": "2025-06-30", "日付": "2025-06-06", "id": 4934, "external_id": 602012, "ts": "2025-06-30", "従業員ｺｰﾄﾞ": "272204", "機種": "", "号機": "", "工場製番": "", "工事番号": "", "ﾕﾆｯﾄ番号": "", "区分": 4, "項目": 0, "時間": 5.0, "所属ｺｰﾄﾞ": "111"}
2025-06-30 13:11:38,806 - ERROR - ❌ UPDATE操作失败: Server6请求返回错误 404 (URL: http://************:8009/mdb/entries/602012, Employee: N/A): Not Found
Traceback (most recent call last):
  File "/home/<USER>/Documents/testF/2025/ser25/server0613/MySuiteDatebase2402newuigood-yolo5-3/server5/app/utils/server6_client.py", line 78, in _make_request
    response.raise_for_status()  # 如果状态码是 4xx 或 5xx，则抛出异常
    ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/miniconda3/envs/my_suite_unified/lib/python3.12/site-packages/aiohttp/client_reqrep.py", line 629, in raise_for_status
    raise ClientResponseError(
aiohttp.client_exceptions.ClientResponseError: 404, message='Not Found', url='http://************:8009/mdb/entries/602012'

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "/home/<USER>/Documents/testF/2025/ser25/server0613/MySuiteDatebase2402newuigood-yolo5-3/server5/app/services/f2_push_writer.py", line 272, in _handle_update_operation
    response = await self.server6_client.update_entry(external_id, entry_data)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Documents/testF/2025/ser25/server0613/MySuiteDatebase2402newuigood-yolo5-3/server5/app/utils/server6_client.py", line 249, in update_entry
    return await self._make_request('PUT', f'/mdb/entries/{external_id}', json_data=serializable_data)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Documents/testF/2025/ser25/server0613/MySuiteDatebase2402newuigood-yolo5-3/server5/app/utils/server6_client.py", line 94, in _make_request
    raise Exception(f"Server6请求返回错误 {e.status} (URL: {url}, Employee: {json_data.get('employee_id', 'N/A')}): {e.message}") from e
Exception: Server6请求返回错误 404 (URL: http://************:8009/mdb/entries/602012, Employee: N/A): Not Found
2025-06-30 13:11:38,806 - app.services.f2_push_writer - ERROR - ❌ UPDATE操作失败: Server6请求返回错误 404 (URL: http://************:8009/mdb/entries/602012, Employee: N/A): Not Found
Traceback (most recent call last):
  File "/home/<USER>/Documents/testF/2025/ser25/server0613/MySuiteDatebase2402newuigood-yolo5-3/server5/app/utils/server6_client.py", line 78, in _make_request
    response.raise_for_status()  # 如果状态码是 4xx 或 5xx，则抛出异常
    ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/miniconda3/envs/my_suite_unified/lib/python3.12/site-packages/aiohttp/client_reqrep.py", line 629, in raise_for_status
    raise ClientResponseError(
aiohttp.client_exceptions.ClientResponseError: 404, message='Not Found', url='http://************:8009/mdb/entries/602012'

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "/home/<USER>/Documents/testF/2025/ser25/server0613/MySuiteDatebase2402newuigood-yolo5-3/server5/app/services/f2_push_writer.py", line 272, in _handle_update_operation
    response = await self.server6_client.update_entry(external_id, entry_data)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Documents/testF/2025/ser25/server0613/MySuiteDatebase2402newuigood-yolo5-3/server5/app/utils/server6_client.py", line 249, in update_entry
    return await self._make_request('PUT', f'/mdb/entries/{external_id}', json_data=serializable_data)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Documents/testF/2025/ser25/server0613/MySuiteDatebase2402newuigood-yolo5-3/server5/app/utils/server6_client.py", line 94, in _make_request
    raise Exception(f"Server6请求返回错误 {e.status} (URL: {url}, Employee: {json_data.get('employee_id', 'N/A')}): {e.message}") from e
Exception: Server6请求返回错误 404 (URL: http://************:8009/mdb/entries/602012, Employee: N/A): Not Found
2025-06-30 13:11:38,807 - ERROR - ❌ 推送作业失败 46: Server6请求返回错误 404 (URL: http://************:8009/mdb/entries/602012, Employee: N/A): Not Found
2025-06-30 13:11:38,807 - app.services.f2_push_writer - ERROR - ❌ 推送作业失败 46: Server6请求返回错误 404 (URL: http://************:8009/mdb/entries/602012, Employee: N/A): Not Found
2025-06-30 13:11:38,807 - ERROR - ❌ 推送任务处理失败: Server6请求返回错误 404 (URL: http://************:8009/mdb/entries/602012, Employee: N/A): Not Found
2025-06-30 13:11:38,807 - app.services.f2_push_writer - ERROR - ❌ 推送任务处理失败: Server6请求返回错误 404 (URL: http://************:8009/mdb/entries/602012, Employee: N/A): Not Found
2025-06-30 13:11:38,808 - ERROR - ❌ Server6请求失败: PUT http://************:8009/mdb/entries/602012 - Status: 404, Message: Not Found. Request Body: {"entry_id": 4934, "created_ts": "2025-06-30", "日付": "2025-06-06", "id": 4934, "external_id": 602012, "ts": "2025-06-30", "従業員ｺｰﾄﾞ": "272204", "機種": "", "号機": "", "工場製番": "", "工事番号": "", "ﾕﾆｯﾄ番号": "", "区分": 4, "項目": 0, "時間": 5.0, "所属ｺｰﾄﾞ": "111"}
2025-06-30 13:11:38,808 - app.utils.server6_client - ERROR - ❌ Server6请求失败: PUT http://************:8009/mdb/entries/602012 - Status: 404, Message: Not Found. Request Body: {"entry_id": 4934, "created_ts": "2025-06-30", "日付": "2025-06-06", "id": 4934, "external_id": 602012, "ts": "2025-06-30", "従業員ｺｰﾄﾞ": "272204", "機種": "", "号機": "", "工場製番": "", "工事番号": "", "ﾕﾆｯﾄ番号": "", "区分": 4, "項目": 0, "時間": 5.0, "所属ｺｰﾄﾞ": "111"}
2025-06-30 13:11:38,809 - ERROR - ❌ Server6请求失败: PUT http://************:8009/mdb/entries/602012 - Status: 404, Message: Not Found. Request Body: {"entry_id": 4934, "created_ts": "2025-06-30", "日付": "2025-06-06", "id": 4934, "external_id": 602012, "ts": "2025-06-30", "従業員ｺｰﾄﾞ": "272204", "機種": "", "号機": "", "工場製番": "", "工事番号": "", "ﾕﾆｯﾄ番号": "", "区分": 4, "項目": 0, "時間": 5.0, "所属ｺｰﾄﾞ": "111"}
2025-06-30 13:11:38,809 - app.utils.server6_client - ERROR - ❌ Server6请求失败: PUT http://************:8009/mdb/entries/602012 - Status: 404, Message: Not Found. Request Body: {"entry_id": 4934, "created_ts": "2025-06-30", "日付": "2025-06-06", "id": 4934, "external_id": 602012, "ts": "2025-06-30", "従業員ｺｰﾄﾞ": "272204", "機種": "", "号機": "", "工場製番": "", "工事番号": "", "ﾕﾆｯﾄ番号": "", "区分": 4, "項目": 0, "時間": 5.0, "所属ｺｰﾄﾞ": "111"}
2025-06-30 13:11:38,810 - ERROR - ❌ UPDATE操作失败: Server6请求返回错误 404 (URL: http://************:8009/mdb/entries/602012, Employee: N/A): Not Found
Traceback (most recent call last):
  File "/home/<USER>/Documents/testF/2025/ser25/server0613/MySuiteDatebase2402newuigood-yolo5-3/server5/app/utils/server6_client.py", line 78, in _make_request
    response.raise_for_status()  # 如果状态码是 4xx 或 5xx，则抛出异常
    ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/miniconda3/envs/my_suite_unified/lib/python3.12/site-packages/aiohttp/client_reqrep.py", line 629, in raise_for_status
    raise ClientResponseError(
aiohttp.client_exceptions.ClientResponseError: 404, message='Not Found', url='http://************:8009/mdb/entries/602012'

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "/home/<USER>/Documents/testF/2025/ser25/server0613/MySuiteDatebase2402newuigood-yolo5-3/server5/app/services/f2_push_writer.py", line 272, in _handle_update_operation
    response = await self.server6_client.update_entry(external_id, entry_data)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Documents/testF/2025/ser25/server0613/MySuiteDatebase2402newuigood-yolo5-3/server5/app/utils/server6_client.py", line 249, in update_entry
    return await self._make_request('PUT', f'/mdb/entries/{external_id}', json_data=serializable_data)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Documents/testF/2025/ser25/server0613/MySuiteDatebase2402newuigood-yolo5-3/server5/app/utils/server6_client.py", line 94, in _make_request
    raise Exception(f"Server6请求返回错误 {e.status} (URL: {url}, Employee: {json_data.get('employee_id', 'N/A')}): {e.message}") from e
Exception: Server6请求返回错误 404 (URL: http://************:8009/mdb/entries/602012, Employee: N/A): Not Found
2025-06-30 13:11:38,810 - app.services.f2_push_writer - ERROR - ❌ UPDATE操作失败: Server6请求返回错误 404 (URL: http://************:8009/mdb/entries/602012, Employee: N/A): Not Found
Traceback (most recent call last):
  File "/home/<USER>/Documents/testF/2025/ser25/server0613/MySuiteDatebase2402newuigood-yolo5-3/server5/app/utils/server6_client.py", line 78, in _make_request
    response.raise_for_status()  # 如果状态码是 4xx 或 5xx，则抛出异常
    ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/miniconda3/envs/my_suite_unified/lib/python3.12/site-packages/aiohttp/client_reqrep.py", line 629, in raise_for_status
    raise ClientResponseError(
aiohttp.client_exceptions.ClientResponseError: 404, message='Not Found', url='http://************:8009/mdb/entries/602012'

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "/home/<USER>/Documents/testF/2025/ser25/server0613/MySuiteDatebase2402newuigood-yolo5-3/server5/app/services/f2_push_writer.py", line 272, in _handle_update_operation
    response = await self.server6_client.update_entry(external_id, entry_data)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Documents/testF/2025/ser25/server0613/MySuiteDatebase2402newuigood-yolo5-3/server5/app/utils/server6_client.py", line 249, in update_entry
    return await self._make_request('PUT', f'/mdb/entries/{external_id}', json_data=serializable_data)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Documents/testF/2025/ser25/server0613/MySuiteDatebase2402newuigood-yolo5-3/server5/app/utils/server6_client.py", line 94, in _make_request
    raise Exception(f"Server6请求返回错误 {e.status} (URL: {url}, Employee: {json_data.get('employee_id', 'N/A')}): {e.message}") from e
Exception: Server6请求返回错误 404 (URL: http://************:8009/mdb/entries/602012, Employee: N/A): Not Found
2025-06-30 13:11:38,811 - ERROR - ❌ UPDATE操作失败: Server6请求返回错误 404 (URL: http://************:8009/mdb/entries/602012, Employee: N/A): Not Found
Traceback (most recent call last):
  File "/home/<USER>/Documents/testF/2025/ser25/server0613/MySuiteDatebase2402newuigood-yolo5-3/server5/app/utils/server6_client.py", line 78, in _make_request
    response.raise_for_status()  # 如果状态码是 4xx 或 5xx，则抛出异常
    ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/miniconda3/envs/my_suite_unified/lib/python3.12/site-packages/aiohttp/client_reqrep.py", line 629, in raise_for_status
    raise ClientResponseError(
aiohttp.client_exceptions.ClientResponseError: 404, message='Not Found', url='http://************:8009/mdb/entries/602012'

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "/home/<USER>/Documents/testF/2025/ser25/server0613/MySuiteDatebase2402newuigood-yolo5-3/server5/app/services/f2_push_writer.py", line 272, in _handle_update_operation
    response = await self.server6_client.update_entry(external_id, entry_data)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Documents/testF/2025/ser25/server0613/MySuiteDatebase2402newuigood-yolo5-3/server5/app/utils/server6_client.py", line 249, in update_entry
    return await self._make_request('PUT', f'/mdb/entries/{external_id}', json_data=serializable_data)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Documents/testF/2025/ser25/server0613/MySuiteDatebase2402newuigood-yolo5-3/server5/app/utils/server6_client.py", line 94, in _make_request
    raise Exception(f"Server6请求返回错误 {e.status} (URL: {url}, Employee: {json_data.get('employee_id', 'N/A')}): {e.message}") from e
Exception: Server6请求返回错误 404 (URL: http://************:8009/mdb/entries/602012, Employee: N/A): Not Found
2025-06-30 13:11:38,811 - app.services.f2_push_writer - ERROR - ❌ UPDATE操作失败: Server6请求返回错误 404 (URL: http://************:8009/mdb/entries/602012, Employee: N/A): Not Found
Traceback (most recent call last):
  File "/home/<USER>/Documents/testF/2025/ser25/server0613/MySuiteDatebase2402newuigood-yolo5-3/server5/app/utils/server6_client.py", line 78, in _make_request
    response.raise_for_status()  # 如果状态码是 4xx 或 5xx，则抛出异常
    ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/miniconda3/envs/my_suite_unified/lib/python3.12/site-packages/aiohttp/client_reqrep.py", line 629, in raise_for_status
    raise ClientResponseError(
aiohttp.client_exceptions.ClientResponseError: 404, message='Not Found', url='http://************:8009/mdb/entries/602012'

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "/home/<USER>/Documents/testF/2025/ser25/server0613/MySuiteDatebase2402newuigood-yolo5-3/server5/app/services/f2_push_writer.py", line 272, in _handle_update_operation
    response = await self.server6_client.update_entry(external_id, entry_data)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Documents/testF/2025/ser25/server0613/MySuiteDatebase2402newuigood-yolo5-3/server5/app/utils/server6_client.py", line 249, in update_entry
    return await self._make_request('PUT', f'/mdb/entries/{external_id}', json_data=serializable_data)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Documents/testF/2025/ser25/server0613/MySuiteDatebase2402newuigood-yolo5-3/server5/app/utils/server6_client.py", line 94, in _make_request
    raise Exception(f"Server6请求返回错误 {e.status} (URL: {url}, Employee: {json_data.get('employee_id', 'N/A')}): {e.message}") from e
Exception: Server6请求返回错误 404 (URL: http://************:8009/mdb/entries/602012, Employee: N/A): Not Found
2025-06-30 13:11:38,813 - ERROR - ❌ 推送作业失败 48: Server6请求返回错误 404 (URL: http://************:8009/mdb/entries/602012, Employee: N/A): Not Found
2025-06-30 13:11:38,813 - app.services.f2_push_writer - ERROR - ❌ 推送作业失败 48: Server6请求返回错误 404 (URL: http://************:8009/mdb/entries/602012, Employee: N/A): Not Found
2025-06-30 13:11:38,813 - ERROR - ❌ 推送任务处理失败: Server6请求返回错误 404 (URL: http://************:8009/mdb/entries/602012, Employee: N/A): Not Found
2025-06-30 13:11:38,813 - app.services.f2_push_writer - ERROR - ❌ 推送任务处理失败: Server6请求返回错误 404 (URL: http://************:8009/mdb/entries/602012, Employee: N/A): Not Found
2025-06-30 13:11:38,814 - ERROR - ❌ 推送作业失败 47: Server6请求返回错误 404 (URL: http://************:8009/mdb/entries/602012, Employee: N/A): Not Found
2025-06-30 13:11:38,814 - app.services.f2_push_writer - ERROR - ❌ 推送作业失败 47: Server6请求返回错误 404 (URL: http://************:8009/mdb/entries/602012, Employee: N/A): Not Found
2025-06-30 13:11:38,814 - ERROR - ❌ 推送任务处理失败: Server6请求返回错误 404 (URL: http://************:8009/mdb/entries/602012, Employee: N/A): Not Found
2025-06-30 13:11:38,814 - app.services.f2_push_writer - ERROR - ❌ 推送任务处理失败: Server6请求返回错误 404 (URL: http://************:8009/mdb/entries/602012, Employee: N/A): Not Found
2025-06-30 13:12:05,549 - ERROR - ❌ Server6请求异常: POST http://************:8009/mdb/entries/insert - Server disconnected
Traceback (most recent call last):
  File "/home/<USER>/Documents/testF/2025/ser25/server0613/MySuiteDatebase2402newuigood-yolo5-3/server5/app/utils/server6_client.py", line 71, in _make_request
    async with self.session.request(method, url, json=json_data) as response:
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/miniconda3/envs/my_suite_unified/lib/python3.12/site-packages/aiohttp/client.py", line 1482, in __aenter__
    self._resp: _RetType = await self._coro
                           ^^^^^^^^^^^^^^^^
  File "/home/<USER>/miniconda3/envs/my_suite_unified/lib/python3.12/site-packages/aiohttp/client.py", line 770, in _request
    resp = await handler(req)
           ^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/miniconda3/envs/my_suite_unified/lib/python3.12/site-packages/aiohttp/client.py", line 748, in _connect_and_send_request
    await resp.start(conn)
  File "/home/<USER>/miniconda3/envs/my_suite_unified/lib/python3.12/site-packages/aiohttp/client_reqrep.py", line 532, in start
    message, payload = await protocol.read()  # type: ignore[union-attr]
                       ^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/miniconda3/envs/my_suite_unified/lib/python3.12/site-packages/aiohttp/streams.py", line 672, in read
    await self._waiter
aiohttp.client_exceptions.ServerDisconnectedError: Server disconnected
2025-06-30 13:12:05,549 - app.utils.server6_client - ERROR - ❌ Server6请求异常: POST http://************:8009/mdb/entries/insert - Server disconnected
Traceback (most recent call last):
  File "/home/<USER>/Documents/testF/2025/ser25/server0613/MySuiteDatebase2402newuigood-yolo5-3/server5/app/utils/server6_client.py", line 71, in _make_request
    async with self.session.request(method, url, json=json_data) as response:
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/miniconda3/envs/my_suite_unified/lib/python3.12/site-packages/aiohttp/client.py", line 1482, in __aenter__
    self._resp: _RetType = await self._coro
                           ^^^^^^^^^^^^^^^^
  File "/home/<USER>/miniconda3/envs/my_suite_unified/lib/python3.12/site-packages/aiohttp/client.py", line 770, in _request
    resp = await handler(req)
           ^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/miniconda3/envs/my_suite_unified/lib/python3.12/site-packages/aiohttp/client.py", line 748, in _connect_and_send_request
    await resp.start(conn)
  File "/home/<USER>/miniconda3/envs/my_suite_unified/lib/python3.12/site-packages/aiohttp/client_reqrep.py", line 532, in start
    message, payload = await protocol.read()  # type: ignore[union-attr]
                       ^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/miniconda3/envs/my_suite_unified/lib/python3.12/site-packages/aiohttp/streams.py", line 672, in read
    await self._waiter
aiohttp.client_exceptions.ServerDisconnectedError: Server disconnected
2025-06-30 13:12:05,550 - ERROR - ❌ Server6请求异常: POST http://************:8009/mdb/entries/insert - Server disconnected
Traceback (most recent call last):
  File "/home/<USER>/Documents/testF/2025/ser25/server0613/MySuiteDatebase2402newuigood-yolo5-3/server5/app/utils/server6_client.py", line 71, in _make_request
    async with self.session.request(method, url, json=json_data) as response:
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/miniconda3/envs/my_suite_unified/lib/python3.12/site-packages/aiohttp/client.py", line 1482, in __aenter__
    self._resp: _RetType = await self._coro
                           ^^^^^^^^^^^^^^^^
  File "/home/<USER>/miniconda3/envs/my_suite_unified/lib/python3.12/site-packages/aiohttp/client.py", line 770, in _request
    resp = await handler(req)
           ^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/miniconda3/envs/my_suite_unified/lib/python3.12/site-packages/aiohttp/client.py", line 748, in _connect_and_send_request
    await resp.start(conn)
  File "/home/<USER>/miniconda3/envs/my_suite_unified/lib/python3.12/site-packages/aiohttp/client_reqrep.py", line 532, in start
    message, payload = await protocol.read()  # type: ignore[union-attr]
                       ^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/miniconda3/envs/my_suite_unified/lib/python3.12/site-packages/aiohttp/streams.py", line 672, in read
    await self._waiter
aiohttp.client_exceptions.ServerDisconnectedError: Server disconnected
2025-06-30 13:12:05,550 - app.utils.server6_client - ERROR - ❌ Server6请求异常: POST http://************:8009/mdb/entries/insert - Server disconnected
Traceback (most recent call last):
  File "/home/<USER>/Documents/testF/2025/ser25/server0613/MySuiteDatebase2402newuigood-yolo5-3/server5/app/utils/server6_client.py", line 71, in _make_request
    async with self.session.request(method, url, json=json_data) as response:
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/miniconda3/envs/my_suite_unified/lib/python3.12/site-packages/aiohttp/client.py", line 1482, in __aenter__
    self._resp: _RetType = await self._coro
                           ^^^^^^^^^^^^^^^^
  File "/home/<USER>/miniconda3/envs/my_suite_unified/lib/python3.12/site-packages/aiohttp/client.py", line 770, in _request
    resp = await handler(req)
           ^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/miniconda3/envs/my_suite_unified/lib/python3.12/site-packages/aiohttp/client.py", line 748, in _connect_and_send_request
    await resp.start(conn)
  File "/home/<USER>/miniconda3/envs/my_suite_unified/lib/python3.12/site-packages/aiohttp/client_reqrep.py", line 532, in start
    message, payload = await protocol.read()  # type: ignore[union-attr]
                       ^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/miniconda3/envs/my_suite_unified/lib/python3.12/site-packages/aiohttp/streams.py", line 672, in read
    await self._waiter
aiohttp.client_exceptions.ServerDisconnectedError: Server disconnected
2025-06-30 13:12:05,565 - ERROR - ❌ INSERT操作失败: Server disconnected
Traceback (most recent call last):
  File "/home/<USER>/Documents/testF/2025/ser25/server0613/MySuiteDatebase2402newuigood-yolo5-3/server5/app/services/f2_push_writer.py", line 231, in _handle_insert_operation
    response = await self.server6_client.insert_entry(mdb_data)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Documents/testF/2025/ser25/server0613/MySuiteDatebase2402newuigood-yolo5-3/server5/app/utils/server6_client.py", line 202, in insert_entry
    return await self._make_request('POST', '/mdb/entries/insert', json_data=serializable_data)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Documents/testF/2025/ser25/server0613/MySuiteDatebase2402newuigood-yolo5-3/server5/app/utils/server6_client.py", line 71, in _make_request
    async with self.session.request(method, url, json=json_data) as response:
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/miniconda3/envs/my_suite_unified/lib/python3.12/site-packages/aiohttp/client.py", line 1482, in __aenter__
    self._resp: _RetType = await self._coro
                           ^^^^^^^^^^^^^^^^
  File "/home/<USER>/miniconda3/envs/my_suite_unified/lib/python3.12/site-packages/aiohttp/client.py", line 770, in _request
    resp = await handler(req)
           ^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/miniconda3/envs/my_suite_unified/lib/python3.12/site-packages/aiohttp/client.py", line 748, in _connect_and_send_request
    await resp.start(conn)
  File "/home/<USER>/miniconda3/envs/my_suite_unified/lib/python3.12/site-packages/aiohttp/client_reqrep.py", line 532, in start
    message, payload = await protocol.read()  # type: ignore[union-attr]
                       ^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/miniconda3/envs/my_suite_unified/lib/python3.12/site-packages/aiohttp/streams.py", line 672, in read
    await self._waiter
aiohttp.client_exceptions.ServerDisconnectedError: Server disconnected
2025-06-30 13:12:05,565 - app.services.f2_push_writer - ERROR - ❌ INSERT操作失败: Server disconnected
Traceback (most recent call last):
  File "/home/<USER>/Documents/testF/2025/ser25/server0613/MySuiteDatebase2402newuigood-yolo5-3/server5/app/services/f2_push_writer.py", line 231, in _handle_insert_operation
    response = await self.server6_client.insert_entry(mdb_data)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Documents/testF/2025/ser25/server0613/MySuiteDatebase2402newuigood-yolo5-3/server5/app/utils/server6_client.py", line 202, in insert_entry
    return await self._make_request('POST', '/mdb/entries/insert', json_data=serializable_data)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Documents/testF/2025/ser25/server0613/MySuiteDatebase2402newuigood-yolo5-3/server5/app/utils/server6_client.py", line 71, in _make_request
    async with self.session.request(method, url, json=json_data) as response:
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/miniconda3/envs/my_suite_unified/lib/python3.12/site-packages/aiohttp/client.py", line 1482, in __aenter__
    self._resp: _RetType = await self._coro
                           ^^^^^^^^^^^^^^^^
  File "/home/<USER>/miniconda3/envs/my_suite_unified/lib/python3.12/site-packages/aiohttp/client.py", line 770, in _request
    resp = await handler(req)
           ^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/miniconda3/envs/my_suite_unified/lib/python3.12/site-packages/aiohttp/client.py", line 748, in _connect_and_send_request
    await resp.start(conn)
  File "/home/<USER>/miniconda3/envs/my_suite_unified/lib/python3.12/site-packages/aiohttp/client_reqrep.py", line 532, in start
    message, payload = await protocol.read()  # type: ignore[union-attr]
                       ^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/miniconda3/envs/my_suite_unified/lib/python3.12/site-packages/aiohttp/streams.py", line 672, in read
    await self._waiter
aiohttp.client_exceptions.ServerDisconnectedError: Server disconnected
2025-06-30 13:12:05,567 - ERROR - ❌ INSERT操作失败: Server disconnected
Traceback (most recent call last):
  File "/home/<USER>/Documents/testF/2025/ser25/server0613/MySuiteDatebase2402newuigood-yolo5-3/server5/app/services/f2_push_writer.py", line 231, in _handle_insert_operation
    response = await self.server6_client.insert_entry(mdb_data)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Documents/testF/2025/ser25/server0613/MySuiteDatebase2402newuigood-yolo5-3/server5/app/utils/server6_client.py", line 202, in insert_entry
    return await self._make_request('POST', '/mdb/entries/insert', json_data=serializable_data)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Documents/testF/2025/ser25/server0613/MySuiteDatebase2402newuigood-yolo5-3/server5/app/utils/server6_client.py", line 71, in _make_request
    async with self.session.request(method, url, json=json_data) as response:
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/miniconda3/envs/my_suite_unified/lib/python3.12/site-packages/aiohttp/client.py", line 1482, in __aenter__
    self._resp: _RetType = await self._coro
                           ^^^^^^^^^^^^^^^^
  File "/home/<USER>/miniconda3/envs/my_suite_unified/lib/python3.12/site-packages/aiohttp/client.py", line 770, in _request
    resp = await handler(req)
           ^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/miniconda3/envs/my_suite_unified/lib/python3.12/site-packages/aiohttp/client.py", line 748, in _connect_and_send_request
    await resp.start(conn)
  File "/home/<USER>/miniconda3/envs/my_suite_unified/lib/python3.12/site-packages/aiohttp/client_reqrep.py", line 532, in start
    message, payload = await protocol.read()  # type: ignore[union-attr]
                       ^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/miniconda3/envs/my_suite_unified/lib/python3.12/site-packages/aiohttp/streams.py", line 672, in read
    await self._waiter
aiohttp.client_exceptions.ServerDisconnectedError: Server disconnected
2025-06-30 13:12:05,567 - app.services.f2_push_writer - ERROR - ❌ INSERT操作失败: Server disconnected
Traceback (most recent call last):
  File "/home/<USER>/Documents/testF/2025/ser25/server0613/MySuiteDatebase2402newuigood-yolo5-3/server5/app/services/f2_push_writer.py", line 231, in _handle_insert_operation
    response = await self.server6_client.insert_entry(mdb_data)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Documents/testF/2025/ser25/server0613/MySuiteDatebase2402newuigood-yolo5-3/server5/app/utils/server6_client.py", line 202, in insert_entry
    return await self._make_request('POST', '/mdb/entries/insert', json_data=serializable_data)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Documents/testF/2025/ser25/server0613/MySuiteDatebase2402newuigood-yolo5-3/server5/app/utils/server6_client.py", line 71, in _make_request
    async with self.session.request(method, url, json=json_data) as response:
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/miniconda3/envs/my_suite_unified/lib/python3.12/site-packages/aiohttp/client.py", line 1482, in __aenter__
    self._resp: _RetType = await self._coro
                           ^^^^^^^^^^^^^^^^
  File "/home/<USER>/miniconda3/envs/my_suite_unified/lib/python3.12/site-packages/aiohttp/client.py", line 770, in _request
    resp = await handler(req)
           ^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/miniconda3/envs/my_suite_unified/lib/python3.12/site-packages/aiohttp/client.py", line 748, in _connect_and_send_request
    await resp.start(conn)
  File "/home/<USER>/miniconda3/envs/my_suite_unified/lib/python3.12/site-packages/aiohttp/client_reqrep.py", line 532, in start
    message, payload = await protocol.read()  # type: ignore[union-attr]
                       ^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/miniconda3/envs/my_suite_unified/lib/python3.12/site-packages/aiohttp/streams.py", line 672, in read
    await self._waiter
aiohttp.client_exceptions.ServerDisconnectedError: Server disconnected
2025-06-30 13:12:05,568 - ERROR - ❌ 推送作业失败 60: Server disconnected
2025-06-30 13:12:05,568 - app.services.f2_push_writer - ERROR - ❌ 推送作业失败 60: Server disconnected
2025-06-30 13:12:05,568 - ERROR - ❌ 推送任务处理失败: Server disconnected
2025-06-30 13:12:05,568 - app.services.f2_push_writer - ERROR - ❌ 推送任务处理失败: Server disconnected
2025-06-30 13:12:05,570 - ERROR - ❌ 推送作业失败 61: Server disconnected
2025-06-30 13:12:05,570 - app.services.f2_push_writer - ERROR - ❌ 推送作业失败 61: Server disconnected
2025-06-30 13:12:05,570 - ERROR - ❌ 推送任务处理失败: Server disconnected
2025-06-30 13:12:05,570 - app.services.f2_push_writer - ERROR - ❌ 推送任务处理失败: Server disconnected
2025-06-30 13:12:06,025 - ERROR - ❌ Server6请求异常: POST http://************:8009/mdb/entries/insert - [Errno 104] Connection reset by peer
Traceback (most recent call last):
  File "/home/<USER>/miniconda3/envs/my_suite_unified/lib/python3.12/asyncio/selector_events.py", line 1013, in _read_ready__data_received
    data = self._sock.recv(self.max_size)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
ConnectionResetError: [Errno 104] Connection reset by peer

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "/home/<USER>/Documents/testF/2025/ser25/server0613/MySuiteDatebase2402newuigood-yolo5-3/server5/app/utils/server6_client.py", line 71, in _make_request
    async with self.session.request(method, url, json=json_data) as response:
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/miniconda3/envs/my_suite_unified/lib/python3.12/site-packages/aiohttp/client.py", line 1482, in __aenter__
    self._resp: _RetType = await self._coro
                           ^^^^^^^^^^^^^^^^
  File "/home/<USER>/miniconda3/envs/my_suite_unified/lib/python3.12/site-packages/aiohttp/client.py", line 770, in _request
    resp = await handler(req)
           ^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/miniconda3/envs/my_suite_unified/lib/python3.12/site-packages/aiohttp/client.py", line 748, in _connect_and_send_request
    await resp.start(conn)
  File "/home/<USER>/miniconda3/envs/my_suite_unified/lib/python3.12/site-packages/aiohttp/client_reqrep.py", line 532, in start
    message, payload = await protocol.read()  # type: ignore[union-attr]
                       ^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/miniconda3/envs/my_suite_unified/lib/python3.12/site-packages/aiohttp/streams.py", line 672, in read
    await self._waiter
aiohttp.client_exceptions.ClientOSError: [Errno 104] Connection reset by peer
2025-06-30 13:12:06,025 - app.utils.server6_client - ERROR - ❌ Server6请求异常: POST http://************:8009/mdb/entries/insert - [Errno 104] Connection reset by peer
Traceback (most recent call last):
  File "/home/<USER>/miniconda3/envs/my_suite_unified/lib/python3.12/asyncio/selector_events.py", line 1013, in _read_ready__data_received
    data = self._sock.recv(self.max_size)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
ConnectionResetError: [Errno 104] Connection reset by peer

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "/home/<USER>/Documents/testF/2025/ser25/server0613/MySuiteDatebase2402newuigood-yolo5-3/server5/app/utils/server6_client.py", line 71, in _make_request
    async with self.session.request(method, url, json=json_data) as response:
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/miniconda3/envs/my_suite_unified/lib/python3.12/site-packages/aiohttp/client.py", line 1482, in __aenter__
    self._resp: _RetType = await self._coro
                           ^^^^^^^^^^^^^^^^
  File "/home/<USER>/miniconda3/envs/my_suite_unified/lib/python3.12/site-packages/aiohttp/client.py", line 770, in _request
    resp = await handler(req)
           ^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/miniconda3/envs/my_suite_unified/lib/python3.12/site-packages/aiohttp/client.py", line 748, in _connect_and_send_request
    await resp.start(conn)
  File "/home/<USER>/miniconda3/envs/my_suite_unified/lib/python3.12/site-packages/aiohttp/client_reqrep.py", line 532, in start
    message, payload = await protocol.read()  # type: ignore[union-attr]
                       ^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/miniconda3/envs/my_suite_unified/lib/python3.12/site-packages/aiohttp/streams.py", line 672, in read
    await self._waiter
aiohttp.client_exceptions.ClientOSError: [Errno 104] Connection reset by peer
2025-06-30 13:12:06,028 - ERROR - ❌ INSERT操作失败: [Errno 104] Connection reset by peer
Traceback (most recent call last):
  File "/home/<USER>/miniconda3/envs/my_suite_unified/lib/python3.12/asyncio/selector_events.py", line 1013, in _read_ready__data_received
    data = self._sock.recv(self.max_size)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
ConnectionResetError: [Errno 104] Connection reset by peer

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "/home/<USER>/Documents/testF/2025/ser25/server0613/MySuiteDatebase2402newuigood-yolo5-3/server5/app/services/f2_push_writer.py", line 231, in _handle_insert_operation
    response = await self.server6_client.insert_entry(mdb_data)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Documents/testF/2025/ser25/server0613/MySuiteDatebase2402newuigood-yolo5-3/server5/app/utils/server6_client.py", line 202, in insert_entry
    return await self._make_request('POST', '/mdb/entries/insert', json_data=serializable_data)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Documents/testF/2025/ser25/server0613/MySuiteDatebase2402newuigood-yolo5-3/server5/app/utils/server6_client.py", line 71, in _make_request
    async with self.session.request(method, url, json=json_data) as response:
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/miniconda3/envs/my_suite_unified/lib/python3.12/site-packages/aiohttp/client.py", line 1482, in __aenter__
    self._resp: _RetType = await self._coro
                           ^^^^^^^^^^^^^^^^
  File "/home/<USER>/miniconda3/envs/my_suite_unified/lib/python3.12/site-packages/aiohttp/client.py", line 770, in _request
    resp = await handler(req)
           ^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/miniconda3/envs/my_suite_unified/lib/python3.12/site-packages/aiohttp/client.py", line 748, in _connect_and_send_request
    await resp.start(conn)
  File "/home/<USER>/miniconda3/envs/my_suite_unified/lib/python3.12/site-packages/aiohttp/client_reqrep.py", line 532, in start
    message, payload = await protocol.read()  # type: ignore[union-attr]
                       ^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/miniconda3/envs/my_suite_unified/lib/python3.12/site-packages/aiohttp/streams.py", line 672, in read
    await self._waiter
aiohttp.client_exceptions.ClientOSError: [Errno 104] Connection reset by peer
2025-06-30 13:12:06,028 - app.services.f2_push_writer - ERROR - ❌ INSERT操作失败: [Errno 104] Connection reset by peer
Traceback (most recent call last):
  File "/home/<USER>/miniconda3/envs/my_suite_unified/lib/python3.12/asyncio/selector_events.py", line 1013, in _read_ready__data_received
    data = self._sock.recv(self.max_size)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
ConnectionResetError: [Errno 104] Connection reset by peer

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "/home/<USER>/Documents/testF/2025/ser25/server0613/MySuiteDatebase2402newuigood-yolo5-3/server5/app/services/f2_push_writer.py", line 231, in _handle_insert_operation
    response = await self.server6_client.insert_entry(mdb_data)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Documents/testF/2025/ser25/server0613/MySuiteDatebase2402newuigood-yolo5-3/server5/app/utils/server6_client.py", line 202, in insert_entry
    return await self._make_request('POST', '/mdb/entries/insert', json_data=serializable_data)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Documents/testF/2025/ser25/server0613/MySuiteDatebase2402newuigood-yolo5-3/server5/app/utils/server6_client.py", line 71, in _make_request
    async with self.session.request(method, url, json=json_data) as response:
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/miniconda3/envs/my_suite_unified/lib/python3.12/site-packages/aiohttp/client.py", line 1482, in __aenter__
    self._resp: _RetType = await self._coro
                           ^^^^^^^^^^^^^^^^
  File "/home/<USER>/miniconda3/envs/my_suite_unified/lib/python3.12/site-packages/aiohttp/client.py", line 770, in _request
    resp = await handler(req)
           ^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/miniconda3/envs/my_suite_unified/lib/python3.12/site-packages/aiohttp/client.py", line 748, in _connect_and_send_request
    await resp.start(conn)
  File "/home/<USER>/miniconda3/envs/my_suite_unified/lib/python3.12/site-packages/aiohttp/client_reqrep.py", line 532, in start
    message, payload = await protocol.read()  # type: ignore[union-attr]
                       ^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/miniconda3/envs/my_suite_unified/lib/python3.12/site-packages/aiohttp/streams.py", line 672, in read
    await self._waiter
aiohttp.client_exceptions.ClientOSError: [Errno 104] Connection reset by peer
2025-06-30 13:12:06,030 - ERROR - ❌ 推送作业失败 59: [Errno 104] Connection reset by peer
2025-06-30 13:12:06,030 - app.services.f2_push_writer - ERROR - ❌ 推送作业失败 59: [Errno 104] Connection reset by peer
2025-06-30 13:12:06,030 - ERROR - ❌ 推送任务处理失败: [Errno 104] Connection reset by peer
2025-06-30 13:12:06,030 - app.services.f2_push_writer - ERROR - ❌ 推送任务处理失败: [Errno 104] Connection reset by peer
2025-06-30 13:12:18,611 - ERROR - Task was destroyed but it is pending!
task: <Task pending name='Task-4109' coro=<Connection._cancel() running at /home/<USER>/miniconda3/envs/my_suite_unified/lib/python3.12/site-packages/asyncpg/connection.py:1646> wait_for=<Future pending cb=[BaseSelectorEventLoop._sock_write_done(6, handle=<Handle BaseS...3.93', 5432))>)(), Task.task_wakeup()]>>
2025-06-30 13:12:18,611 - asyncio - ERROR - Task was destroyed but it is pending!
task: <Task pending name='Task-4109' coro=<Connection._cancel() running at /home/<USER>/miniconda3/envs/my_suite_unified/lib/python3.12/site-packages/asyncpg/connection.py:1646> wait_for=<Future pending cb=[BaseSelectorEventLoop._sock_write_done(6, handle=<Handle BaseS...3.93', 5432))>)(), Task.task_wakeup()]>>
2025-06-30 14:33:41,465 - WARNING - ⏰ Server6请求超时 (尝试 1/3): POST http://************:8009/mdb/entries/query，2秒后重试...
2025-06-30 14:33:41,465 - app.utils.server6_client - WARNING - ⏰ Server6请求超时 (尝试 1/3): POST http://************:8009/mdb/entries/query，2秒后重试...
2025-06-30 14:33:42,466 - WARNING - ⏰ Server6请求超时 (尝试 1/3): POST http://************:8009/mdb/entries/query，2秒后重试...
2025-06-30 14:33:42,466 - app.utils.server6_client - WARNING - ⏰ Server6请求超时 (尝试 1/3): POST http://************:8009/mdb/entries/query，2秒后重试...
2025-06-30 14:33:48,473 - ERROR - Task was destroyed but it is pending!
task: <Task pending name='Task-198' coro=<Connection._cancel() running at /home/<USER>/miniconda3/envs/my_suite_unified/lib/python3.12/site-packages/asyncpg/connection.py:1646> wait_for=<Future pending cb=[BaseSelectorEventLoop._sock_write_done(6, handle=<Handle BaseS...3.93', 5432))>)(), Task.task_wakeup()]>>
2025-06-30 14:33:48,473 - asyncio - ERROR - Task was destroyed but it is pending!
task: <Task pending name='Task-198' coro=<Connection._cancel() running at /home/<USER>/miniconda3/envs/my_suite_unified/lib/python3.12/site-packages/asyncpg/connection.py:1646> wait_for=<Future pending cb=[BaseSelectorEventLoop._sock_write_done(6, handle=<Handle BaseS...3.93', 5432))>)(), Task.task_wakeup()]>>
2025-06-30 14:35:42,506 - ERROR - Task was destroyed but it is pending!
task: <Task pending name='Task-200' coro=<Connection._cancel() running at /home/<USER>/miniconda3/envs/my_suite_unified/lib/python3.12/site-packages/asyncpg/connection.py:1646> wait_for=<Future pending cb=[Task.task_wakeup()]>>
2025-06-30 14:35:42,506 - asyncio - ERROR - Task was destroyed but it is pending!
task: <Task pending name='Task-200' coro=<Connection._cancel() running at /home/<USER>/miniconda3/envs/my_suite_unified/lib/python3.12/site-packages/asyncpg/connection.py:1646> wait_for=<Future pending cb=[Task.task_wakeup()]>>
2025-07-01 07:36:23,953 - ERROR - ❌ Server6请求异常: POST http://************:8009/mdb/entries/query - [Errno 104] Connection reset by peer
Traceback (most recent call last):
  File "/home/<USER>/miniconda3/envs/my_suite_unified/lib/python3.12/asyncio/selector_events.py", line 1013, in _read_ready__data_received
    data = self._sock.recv(self.max_size)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
ConnectionResetError: [Errno 104] Connection reset by peer

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "/home/<USER>/Documents/testF/2025/ser25/server0613/MySuiteDatebase2402newuigood-yolo5-3/server5/app/utils/server6_client.py", line 71, in _make_request
    async with self.session.request(method, url, json=json_data) as response:
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/miniconda3/envs/my_suite_unified/lib/python3.12/site-packages/aiohttp/client.py", line 1482, in __aenter__
    self._resp: _RetType = await self._coro
                           ^^^^^^^^^^^^^^^^
  File "/home/<USER>/miniconda3/envs/my_suite_unified/lib/python3.12/site-packages/aiohttp/client.py", line 770, in _request
    resp = await handler(req)
           ^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/miniconda3/envs/my_suite_unified/lib/python3.12/site-packages/aiohttp/client.py", line 748, in _connect_and_send_request
    await resp.start(conn)
  File "/home/<USER>/miniconda3/envs/my_suite_unified/lib/python3.12/site-packages/aiohttp/client_reqrep.py", line 532, in start
    message, payload = await protocol.read()  # type: ignore[union-attr]
                       ^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/miniconda3/envs/my_suite_unified/lib/python3.12/site-packages/aiohttp/streams.py", line 672, in read
    await self._waiter
aiohttp.client_exceptions.ClientOSError: [Errno 104] Connection reset by peer
2025-07-01 07:36:23,953 - app.utils.server6_client - ERROR - ❌ Server6请求异常: POST http://************:8009/mdb/entries/query - [Errno 104] Connection reset by peer
Traceback (most recent call last):
  File "/home/<USER>/miniconda3/envs/my_suite_unified/lib/python3.12/asyncio/selector_events.py", line 1013, in _read_ready__data_received
    data = self._sock.recv(self.max_size)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
ConnectionResetError: [Errno 104] Connection reset by peer

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "/home/<USER>/Documents/testF/2025/ser25/server0613/MySuiteDatebase2402newuigood-yolo5-3/server5/app/utils/server6_client.py", line 71, in _make_request
    async with self.session.request(method, url, json=json_data) as response:
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/miniconda3/envs/my_suite_unified/lib/python3.12/site-packages/aiohttp/client.py", line 1482, in __aenter__
    self._resp: _RetType = await self._coro
                           ^^^^^^^^^^^^^^^^
  File "/home/<USER>/miniconda3/envs/my_suite_unified/lib/python3.12/site-packages/aiohttp/client.py", line 770, in _request
    resp = await handler(req)
           ^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/miniconda3/envs/my_suite_unified/lib/python3.12/site-packages/aiohttp/client.py", line 748, in _connect_and_send_request
    await resp.start(conn)
  File "/home/<USER>/miniconda3/envs/my_suite_unified/lib/python3.12/site-packages/aiohttp/client_reqrep.py", line 532, in start
    message, payload = await protocol.read()  # type: ignore[union-attr]
                       ^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/miniconda3/envs/my_suite_unified/lib/python3.12/site-packages/aiohttp/streams.py", line 672, in read
    await self._waiter
aiohttp.client_exceptions.ClientOSError: [Errno 104] Connection reset by peer
2025-07-01 07:36:23,958 - ERROR - 为员工 215753 批量拉取数据失败: [Errno 104] Connection reset by peer
2025-07-01 07:36:23,958 - app.services.f5_bulk_sync - ERROR - 为员工 215753 批量拉取数据失败: [Errno 104] Connection reset by peer
2025-07-01 07:36:23,958 - ERROR - ❌ Server6请求异常: POST http://************:8009/mdb/entries/query - [Errno 104] Connection reset by peer
Traceback (most recent call last):
  File "/home/<USER>/miniconda3/envs/my_suite_unified/lib/python3.12/asyncio/selector_events.py", line 1013, in _read_ready__data_received
    data = self._sock.recv(self.max_size)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
ConnectionResetError: [Errno 104] Connection reset by peer

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "/home/<USER>/Documents/testF/2025/ser25/server0613/MySuiteDatebase2402newuigood-yolo5-3/server5/app/utils/server6_client.py", line 71, in _make_request
    async with self.session.request(method, url, json=json_data) as response:
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/miniconda3/envs/my_suite_unified/lib/python3.12/site-packages/aiohttp/client.py", line 1482, in __aenter__
    self._resp: _RetType = await self._coro
                           ^^^^^^^^^^^^^^^^
  File "/home/<USER>/miniconda3/envs/my_suite_unified/lib/python3.12/site-packages/aiohttp/client.py", line 770, in _request
    resp = await handler(req)
           ^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/miniconda3/envs/my_suite_unified/lib/python3.12/site-packages/aiohttp/client.py", line 748, in _connect_and_send_request
    await resp.start(conn)
  File "/home/<USER>/miniconda3/envs/my_suite_unified/lib/python3.12/site-packages/aiohttp/client_reqrep.py", line 532, in start
    message, payload = await protocol.read()  # type: ignore[union-attr]
                       ^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/miniconda3/envs/my_suite_unified/lib/python3.12/site-packages/aiohttp/streams.py", line 672, in read
    await self._waiter
aiohttp.client_exceptions.ClientOSError: [Errno 104] Connection reset by peer
2025-07-01 07:36:23,958 - app.utils.server6_client - ERROR - ❌ Server6请求异常: POST http://************:8009/mdb/entries/query - [Errno 104] Connection reset by peer
Traceback (most recent call last):
  File "/home/<USER>/miniconda3/envs/my_suite_unified/lib/python3.12/asyncio/selector_events.py", line 1013, in _read_ready__data_received
    data = self._sock.recv(self.max_size)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
ConnectionResetError: [Errno 104] Connection reset by peer

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "/home/<USER>/Documents/testF/2025/ser25/server0613/MySuiteDatebase2402newuigood-yolo5-3/server5/app/utils/server6_client.py", line 71, in _make_request
    async with self.session.request(method, url, json=json_data) as response:
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/miniconda3/envs/my_suite_unified/lib/python3.12/site-packages/aiohttp/client.py", line 1482, in __aenter__
    self._resp: _RetType = await self._coro
                           ^^^^^^^^^^^^^^^^
  File "/home/<USER>/miniconda3/envs/my_suite_unified/lib/python3.12/site-packages/aiohttp/client.py", line 770, in _request
    resp = await handler(req)
           ^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/miniconda3/envs/my_suite_unified/lib/python3.12/site-packages/aiohttp/client.py", line 748, in _connect_and_send_request
    await resp.start(conn)
  File "/home/<USER>/miniconda3/envs/my_suite_unified/lib/python3.12/site-packages/aiohttp/client_reqrep.py", line 532, in start
    message, payload = await protocol.read()  # type: ignore[union-attr]
                       ^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/miniconda3/envs/my_suite_unified/lib/python3.12/site-packages/aiohttp/streams.py", line 672, in read
    await self._waiter
aiohttp.client_exceptions.ClientOSError: [Errno 104] Connection reset by peer
2025-07-01 07:36:23,959 - ERROR - 为员工 215753 拉取数据失败: [Errno 104] Connection reset by peer
2025-07-01 07:36:23,959 - app.services.f3_data_puller - ERROR - 为员工 215753 拉取数据失败: [Errno 104] Connection reset by peer
2025-07-01 07:36:43,799 - ERROR - ❌ Server6请求异常: POST http://************:8009/mdb/entries/query - [Errno 104] Connection reset by peer
Traceback (most recent call last):
  File "/home/<USER>/miniconda3/envs/my_suite_unified/lib/python3.12/asyncio/selector_events.py", line 1013, in _read_ready__data_received
    data = self._sock.recv(self.max_size)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
ConnectionResetError: [Errno 104] Connection reset by peer

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "/home/<USER>/Documents/testF/2025/ser25/server0613/MySuiteDatebase2402newuigood-yolo5-3/server5/app/utils/server6_client.py", line 71, in _make_request
    async with self.session.request(method, url, json=json_data) as response:
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/miniconda3/envs/my_suite_unified/lib/python3.12/site-packages/aiohttp/client.py", line 1482, in __aenter__
    self._resp: _RetType = await self._coro
                           ^^^^^^^^^^^^^^^^
  File "/home/<USER>/miniconda3/envs/my_suite_unified/lib/python3.12/site-packages/aiohttp/client.py", line 770, in _request
    resp = await handler(req)
           ^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/miniconda3/envs/my_suite_unified/lib/python3.12/site-packages/aiohttp/client.py", line 748, in _connect_and_send_request
    await resp.start(conn)
  File "/home/<USER>/miniconda3/envs/my_suite_unified/lib/python3.12/site-packages/aiohttp/client_reqrep.py", line 532, in start
    message, payload = await protocol.read()  # type: ignore[union-attr]
                       ^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/miniconda3/envs/my_suite_unified/lib/python3.12/site-packages/aiohttp/streams.py", line 672, in read
    await self._waiter
aiohttp.client_exceptions.ClientOSError: [Errno 104] Connection reset by peer
2025-07-01 07:36:43,799 - app.utils.server6_client - ERROR - ❌ Server6请求异常: POST http://************:8009/mdb/entries/query - [Errno 104] Connection reset by peer
Traceback (most recent call last):
  File "/home/<USER>/miniconda3/envs/my_suite_unified/lib/python3.12/asyncio/selector_events.py", line 1013, in _read_ready__data_received
    data = self._sock.recv(self.max_size)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
ConnectionResetError: [Errno 104] Connection reset by peer

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "/home/<USER>/Documents/testF/2025/ser25/server0613/MySuiteDatebase2402newuigood-yolo5-3/server5/app/utils/server6_client.py", line 71, in _make_request
    async with self.session.request(method, url, json=json_data) as response:
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/miniconda3/envs/my_suite_unified/lib/python3.12/site-packages/aiohttp/client.py", line 1482, in __aenter__
    self._resp: _RetType = await self._coro
                           ^^^^^^^^^^^^^^^^
  File "/home/<USER>/miniconda3/envs/my_suite_unified/lib/python3.12/site-packages/aiohttp/client.py", line 770, in _request
    resp = await handler(req)
           ^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/miniconda3/envs/my_suite_unified/lib/python3.12/site-packages/aiohttp/client.py", line 748, in _connect_and_send_request
    await resp.start(conn)
  File "/home/<USER>/miniconda3/envs/my_suite_unified/lib/python3.12/site-packages/aiohttp/client_reqrep.py", line 532, in start
    message, payload = await protocol.read()  # type: ignore[union-attr]
                       ^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/miniconda3/envs/my_suite_unified/lib/python3.12/site-packages/aiohttp/streams.py", line 672, in read
    await self._waiter
aiohttp.client_exceptions.ClientOSError: [Errno 104] Connection reset by peer
2025-07-01 07:36:43,799 - ERROR - 为员工 953563 拉取数据失败: [Errno 104] Connection reset by peer
2025-07-01 07:36:43,799 - app.services.f3_data_puller - ERROR - 为员工 953563 拉取数据失败: [Errno 104] Connection reset by peer
2025-07-01 07:36:43,800 - ERROR - ❌ Server6请求异常: POST http://************:8009/mdb/entries/query - [Errno 104] Connection reset by peer
Traceback (most recent call last):
  File "/home/<USER>/miniconda3/envs/my_suite_unified/lib/python3.12/asyncio/selector_events.py", line 1013, in _read_ready__data_received
    data = self._sock.recv(self.max_size)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
ConnectionResetError: [Errno 104] Connection reset by peer

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "/home/<USER>/Documents/testF/2025/ser25/server0613/MySuiteDatebase2402newuigood-yolo5-3/server5/app/utils/server6_client.py", line 71, in _make_request
    async with self.session.request(method, url, json=json_data) as response:
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/miniconda3/envs/my_suite_unified/lib/python3.12/site-packages/aiohttp/client.py", line 1482, in __aenter__
    self._resp: _RetType = await self._coro
                           ^^^^^^^^^^^^^^^^
  File "/home/<USER>/miniconda3/envs/my_suite_unified/lib/python3.12/site-packages/aiohttp/client.py", line 770, in _request
    resp = await handler(req)
           ^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/miniconda3/envs/my_suite_unified/lib/python3.12/site-packages/aiohttp/client.py", line 748, in _connect_and_send_request
    await resp.start(conn)
  File "/home/<USER>/miniconda3/envs/my_suite_unified/lib/python3.12/site-packages/aiohttp/client_reqrep.py", line 532, in start
    message, payload = await protocol.read()  # type: ignore[union-attr]
                       ^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/miniconda3/envs/my_suite_unified/lib/python3.12/site-packages/aiohttp/streams.py", line 672, in read
    await self._waiter
aiohttp.client_exceptions.ClientOSError: [Errno 104] Connection reset by peer
2025-07-01 07:36:43,800 - app.utils.server6_client - ERROR - ❌ Server6请求异常: POST http://************:8009/mdb/entries/query - [Errno 104] Connection reset by peer
Traceback (most recent call last):
  File "/home/<USER>/miniconda3/envs/my_suite_unified/lib/python3.12/asyncio/selector_events.py", line 1013, in _read_ready__data_received
    data = self._sock.recv(self.max_size)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
ConnectionResetError: [Errno 104] Connection reset by peer

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "/home/<USER>/Documents/testF/2025/ser25/server0613/MySuiteDatebase2402newuigood-yolo5-3/server5/app/utils/server6_client.py", line 71, in _make_request
    async with self.session.request(method, url, json=json_data) as response:
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/miniconda3/envs/my_suite_unified/lib/python3.12/site-packages/aiohttp/client.py", line 1482, in __aenter__
    self._resp: _RetType = await self._coro
                           ^^^^^^^^^^^^^^^^
  File "/home/<USER>/miniconda3/envs/my_suite_unified/lib/python3.12/site-packages/aiohttp/client.py", line 770, in _request
    resp = await handler(req)
           ^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/miniconda3/envs/my_suite_unified/lib/python3.12/site-packages/aiohttp/client.py", line 748, in _connect_and_send_request
    await resp.start(conn)
  File "/home/<USER>/miniconda3/envs/my_suite_unified/lib/python3.12/site-packages/aiohttp/client_reqrep.py", line 532, in start
    message, payload = await protocol.read()  # type: ignore[union-attr]
                       ^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/miniconda3/envs/my_suite_unified/lib/python3.12/site-packages/aiohttp/streams.py", line 672, in read
    await self._waiter
aiohttp.client_exceptions.ClientOSError: [Errno 104] Connection reset by peer
2025-07-01 07:36:43,801 - ERROR - 为员工 953563 批量拉取数据失败: [Errno 104] Connection reset by peer
2025-07-01 07:36:43,801 - app.services.f5_bulk_sync - ERROR - 为员工 953563 批量拉取数据失败: [Errno 104] Connection reset by peer
2025-07-04 17:26:40,536 - ERROR - ❌ 推送工作线程异常 worker_2: 列q.retry_countは存在しません
2025-07-04 17:26:40,536 - app.services.f2_push_writer - ERROR - ❌ 推送工作线程异常 worker_2: 列q.retry_countは存在しません
2025-07-04 17:26:40,538 - ERROR - ❌ 推送工作线程异常 worker_1: 列q.retry_countは存在しません
2025-07-04 17:26:40,538 - app.services.f2_push_writer - ERROR - ❌ 推送工作线程异常 worker_1: 列q.retry_countは存在しません
2025-07-04 17:26:40,656 - ERROR - ❌ 推送工作线程异常 worker_3: 列q.retry_countは存在しません
2025-07-04 17:26:40,656 - app.services.f2_push_writer - ERROR - ❌ 推送工作线程异常 worker_3: 列q.retry_countは存在しません
2025-07-04 17:26:40,692 - ERROR - ❌ 推送工作线程异常 worker_4: 列q.retry_countは存在しません
2025-07-04 17:26:40,692 - app.services.f2_push_writer - ERROR - ❌ 推送工作线程异常 worker_4: 列q.retry_countは存在しません
2025-07-04 17:26:45,548 - ERROR - ❌ 推送工作线程异常 worker_2: 列q.retry_countは存在しません
2025-07-04 17:26:45,548 - app.services.f2_push_writer - ERROR - ❌ 推送工作线程异常 worker_2: 列q.retry_countは存在しません
2025-07-04 17:26:45,549 - ERROR - ❌ 推送工作线程异常 worker_1: 列q.retry_countは存在しません
2025-07-04 17:26:45,549 - app.services.f2_push_writer - ERROR - ❌ 推送工作线程异常 worker_1: 列q.retry_countは存在しません
2025-07-04 17:26:45,663 - ERROR - ❌ 推送工作线程异常 worker_3: 列q.retry_countは存在しません
2025-07-04 17:26:45,663 - app.services.f2_push_writer - ERROR - ❌ 推送工作线程异常 worker_3: 列q.retry_countは存在しません
2025-07-04 17:26:45,699 - ERROR - ❌ 推送工作线程异常 worker_4: 列q.retry_countは存在しません
2025-07-04 17:26:45,699 - app.services.f2_push_writer - ERROR - ❌ 推送工作线程异常 worker_4: 列q.retry_countは存在しません
2025-07-04 17:26:50,559 - ERROR - ❌ 推送工作线程异常 worker_1: 列q.retry_countは存在しません
2025-07-04 17:26:50,559 - app.services.f2_push_writer - ERROR - ❌ 推送工作线程异常 worker_1: 列q.retry_countは存在しません
2025-07-04 17:26:50,559 - ERROR - ❌ 推送工作线程异常 worker_2: 列q.retry_countは存在しません
2025-07-04 17:26:50,559 - app.services.f2_push_writer - ERROR - ❌ 推送工作线程异常 worker_2: 列q.retry_countは存在しません
2025-07-04 17:26:50,670 - ERROR - ❌ 推送工作线程异常 worker_3: 列q.retry_countは存在しません
2025-07-04 17:26:50,670 - app.services.f2_push_writer - ERROR - ❌ 推送工作线程异常 worker_3: 列q.retry_countは存在しません
2025-07-04 17:26:50,706 - ERROR - ❌ 推送工作线程异常 worker_4: 列q.retry_countは存在しません
2025-07-04 17:26:50,706 - app.services.f2_push_writer - ERROR - ❌ 推送工作线程异常 worker_4: 列q.retry_countは存在しません
2025-07-04 17:26:55,572 - ERROR - ❌ 推送工作线程异常 worker_2: 列q.retry_countは存在しません
2025-07-04 17:26:55,572 - app.services.f2_push_writer - ERROR - ❌ 推送工作线程异常 worker_2: 列q.retry_countは存在しません
2025-07-04 17:26:55,572 - ERROR - ❌ 推送工作线程异常 worker_1: 列q.retry_countは存在しません
2025-07-04 17:26:55,572 - app.services.f2_push_writer - ERROR - ❌ 推送工作线程异常 worker_1: 列q.retry_countは存在しません
2025-07-04 17:26:55,680 - ERROR - ❌ 推送工作线程异常 worker_3: 列q.retry_countは存在しません
2025-07-04 17:26:55,680 - app.services.f2_push_writer - ERROR - ❌ 推送工作线程异常 worker_3: 列q.retry_countは存在しません
2025-07-04 17:26:55,714 - ERROR - ❌ 推送工作线程异常 worker_4: 列q.retry_countは存在しません
2025-07-04 17:26:55,714 - app.services.f2_push_writer - ERROR - ❌ 推送工作线程异常 worker_4: 列q.retry_countは存在しません
2025-07-04 17:27:00,583 - ERROR - ❌ 推送工作线程异常 worker_1: 列q.retry_countは存在しません
2025-07-04 17:27:00,583 - app.services.f2_push_writer - ERROR - ❌ 推送工作线程异常 worker_1: 列q.retry_countは存在しません
2025-07-04 17:27:00,583 - ERROR - ❌ 推送工作线程异常 worker_2: 列q.retry_countは存在しません
2025-07-04 17:27:00,583 - app.services.f2_push_writer - ERROR - ❌ 推送工作线程异常 worker_2: 列q.retry_countは存在しません
2025-07-04 17:27:00,689 - ERROR - ❌ 推送工作线程异常 worker_3: 列q.retry_countは存在しません
2025-07-04 17:27:00,689 - app.services.f2_push_writer - ERROR - ❌ 推送工作线程异常 worker_3: 列q.retry_countは存在しません
2025-07-04 17:27:00,739 - ERROR - ❌ 推送工作线程异常 worker_4: 列q.retry_countは存在しません
2025-07-04 17:27:00,739 - app.services.f2_push_writer - ERROR - ❌ 推送工作线程异常 worker_4: 列q.retry_countは存在しません
2025-07-04 17:27:05,589 - ERROR - ❌ 推送工作线程异常 worker_2: 列q.retry_countは存在しません
2025-07-04 17:27:05,589 - app.services.f2_push_writer - ERROR - ❌ 推送工作线程异常 worker_2: 列q.retry_countは存在しません
2025-07-04 17:27:05,590 - ERROR - ❌ 推送工作线程异常 worker_1: 列q.retry_countは存在しません
2025-07-04 17:27:05,590 - app.services.f2_push_writer - ERROR - ❌ 推送工作线程异常 worker_1: 列q.retry_countは存在しません
2025-07-04 17:27:05,697 - ERROR - ❌ 推送工作线程异常 worker_3: 列q.retry_countは存在しません
2025-07-04 17:27:05,697 - app.services.f2_push_writer - ERROR - ❌ 推送工作线程异常 worker_3: 列q.retry_countは存在しません
2025-07-04 17:27:05,746 - ERROR - ❌ 推送工作线程异常 worker_4: 列q.retry_countは存在しません
2025-07-04 17:27:05,746 - app.services.f2_push_writer - ERROR - ❌ 推送工作线程异常 worker_4: 列q.retry_countは存在しません
2025-07-04 17:27:10,601 - ERROR - ❌ 推送工作线程异常 worker_1: 列q.retry_countは存在しません
2025-07-04 17:27:10,601 - app.services.f2_push_writer - ERROR - ❌ 推送工作线程异常 worker_1: 列q.retry_countは存在しません
2025-07-04 17:27:10,602 - ERROR - ❌ 推送工作线程异常 worker_2: 列q.retry_countは存在しません
2025-07-04 17:27:10,602 - app.services.f2_push_writer - ERROR - ❌ 推送工作线程异常 worker_2: 列q.retry_countは存在しません
2025-07-04 17:27:10,704 - ERROR - ❌ 推送工作线程异常 worker_3: 列q.retry_countは存在しません
2025-07-04 17:27:10,704 - app.services.f2_push_writer - ERROR - ❌ 推送工作线程异常 worker_3: 列q.retry_countは存在しません
2025-07-04 17:27:10,752 - ERROR - ❌ 推送工作线程异常 worker_4: 列q.retry_countは存在しません
2025-07-04 17:27:10,752 - app.services.f2_push_writer - ERROR - ❌ 推送工作线程异常 worker_4: 列q.retry_countは存在しません
2025-07-04 17:27:15,612 - ERROR - ❌ 推送工作线程异常 worker_1: 列q.retry_countは存在しません
2025-07-04 17:27:15,612 - app.services.f2_push_writer - ERROR - ❌ 推送工作线程异常 worker_1: 列q.retry_countは存在しません
2025-07-04 17:27:15,612 - ERROR - ❌ 推送工作线程异常 worker_2: 列q.retry_countは存在しません
2025-07-04 17:27:15,612 - app.services.f2_push_writer - ERROR - ❌ 推送工作线程异常 worker_2: 列q.retry_countは存在しません
2025-07-04 17:27:15,711 - ERROR - ❌ 推送工作线程异常 worker_3: 列q.retry_countは存在しません
2025-07-04 17:27:15,711 - app.services.f2_push_writer - ERROR - ❌ 推送工作线程异常 worker_3: 列q.retry_countは存在しません
2025-07-04 17:27:15,759 - ERROR - ❌ 推送工作线程异常 worker_4: 列q.retry_countは存在しません
2025-07-04 17:27:15,759 - app.services.f2_push_writer - ERROR - ❌ 推送工作线程异常 worker_4: 列q.retry_countは存在しません
2025-07-04 17:27:20,619 - ERROR - ❌ 推送工作线程异常 worker_2: 列q.retry_countは存在しません
2025-07-04 17:27:20,619 - app.services.f2_push_writer - ERROR - ❌ 推送工作线程异常 worker_2: 列q.retry_countは存在しません
2025-07-04 17:27:20,619 - ERROR - ❌ 推送工作线程异常 worker_1: 列q.retry_countは存在しません
2025-07-04 17:27:20,619 - app.services.f2_push_writer - ERROR - ❌ 推送工作线程异常 worker_1: 列q.retry_countは存在しません
2025-07-04 17:27:20,717 - ERROR - ❌ 推送工作线程异常 worker_3: 列q.retry_countは存在しません
2025-07-04 17:27:20,717 - app.services.f2_push_writer - ERROR - ❌ 推送工作线程异常 worker_3: 列q.retry_countは存在しません
2025-07-04 17:27:20,764 - ERROR - ❌ 推送工作线程异常 worker_4: 列q.retry_countは存在しません
2025-07-04 17:27:20,764 - app.services.f2_push_writer - ERROR - ❌ 推送工作线程异常 worker_4: 列q.retry_countは存在しません
2025-07-04 17:31:24,573 - ERROR - ❌ 推送工作线程异常 worker_2: 列q.retry_countは存在しません
2025-07-04 17:31:24,573 - app.services.f2_push_writer - ERROR - ❌ 推送工作线程异常 worker_2: 列q.retry_countは存在しません
2025-07-04 17:31:24,573 - ERROR - ❌ 推送工作线程异常 worker_1: 列q.retry_countは存在しません
2025-07-04 17:31:24,573 - app.services.f2_push_writer - ERROR - ❌ 推送工作线程异常 worker_1: 列q.retry_countは存在しません
2025-07-04 17:31:24,793 - ERROR - ❌ 推送工作线程异常 worker_4: 列q.retry_countは存在しません
2025-07-04 17:31:24,793 - app.services.f2_push_writer - ERROR - ❌ 推送工作线程异常 worker_4: 列q.retry_countは存在しません
2025-07-04 17:31:24,797 - ERROR - ❌ 推送工作线程异常 worker_3: 列q.retry_countは存在しません
2025-07-04 17:31:24,797 - app.services.f2_push_writer - ERROR - ❌ 推送工作线程异常 worker_3: 列q.retry_countは存在しません
2025-07-04 17:31:29,583 - ERROR - ❌ 推送工作线程异常 worker_1: 列q.retry_countは存在しません
2025-07-04 17:31:29,583 - app.services.f2_push_writer - ERROR - ❌ 推送工作线程异常 worker_1: 列q.retry_countは存在しません
2025-07-04 17:31:29,584 - ERROR - ❌ 推送工作线程异常 worker_2: 列q.retry_countは存在しません
2025-07-04 17:31:29,584 - app.services.f2_push_writer - ERROR - ❌ 推送工作线程异常 worker_2: 列q.retry_countは存在しません
2025-07-04 17:31:29,805 - ERROR - ❌ 推送工作线程异常 worker_4: 列q.retry_countは存在しません
2025-07-04 17:31:29,805 - app.services.f2_push_writer - ERROR - ❌ 推送工作线程异常 worker_4: 列q.retry_countは存在しません
2025-07-04 17:31:29,807 - ERROR - ❌ 推送工作线程异常 worker_3: 列q.retry_countは存在しません
2025-07-04 17:31:29,807 - app.services.f2_push_writer - ERROR - ❌ 推送工作线程异常 worker_3: 列q.retry_countは存在しません
