#!/usr/bin/env python3
# server5-2/verify_timeprotab_setup.py
# 2025/07/03 + 16:25 + 验证timeprotab表设置和功能

import asyncio
import asyncpg
import sys
from pathlib import Path
from datetime import datetime

sys.path.append(str(Path(__file__).parent))
from config.config import DATABASE_URL

async def verify_setup():
    """验证timeprotab表的完整设置"""
    print("🔍 2025/07/03 + 16:25 + 开始验证timeprotab表设置...")
    
    try:
        conn = await asyncpg.connect(DATABASE_URL)
        try:
            # 1. 检查主表是否存在
            print("\n1️⃣ 2025/07/03 + 16:25 + 检查主表...")
            main_table_exists = await conn.fetchval("""
                SELECT EXISTS (
                    SELECT 1 FROM information_schema.tables 
                    WHERE table_name = 'timeprotab' AND table_schema = 'public'
                )
            """)
            
            if main_table_exists:
                print("✅ 2025/07/03 + 16:25 + 主表timeprotab存在")
            else:
                print("❌ 2025/07/03 + 16:25 + 主表timeprotab不存在")
                return False
            
            # 2. 检查分区
            print("\n2️⃣ 2025/07/03 + 16:25 + 检查分区...")
            partitions = await conn.fetch("""
                SELECT tablename 
                FROM pg_tables 
                WHERE tablename LIKE 'timeprotab_%'
                ORDER BY tablename
            """)
            
            if partitions:
                print(f"✅ 2025/07/03 + 16:25 + 找到 {len(partitions)} 个分区:")
                for partition in partitions:
                    print(f"  - {partition['tablename']}")
            else:
                print("⚠️ 2025/07/03 + 16:25 + 未找到分区")
            
            # 3. 检查索引
            print("\n3️⃣ 2025/07/03 + 16:25 + 检查索引...")
            indexes = await conn.fetch("""
                SELECT indexname, tablename
                FROM pg_indexes 
                WHERE tablename LIKE 'timeprotab%'
                ORDER BY tablename, indexname
            """)
            
            if indexes:
                print(f"✅ 2025/07/03 + 16:25 + 找到 {len(indexes)} 个索引:")
                for index in indexes:
                    print(f"  - {index['indexname']} (表: {index['tablename']})")
            else:
                print("⚠️ 2025/07/03 + 16:25 + 未找到索引")
            
            # 4. 检查触发器
            print("\n4️⃣ 2025/07/03 + 16:25 + 检查触发器...")
            triggers = await conn.fetch("""
                SELECT trigger_name, event_manipulation, action_statement
                FROM information_schema.triggers 
                WHERE event_object_table = 'timeprotab'
            """)
            
            if triggers:
                print(f"✅ 2025/07/03 + 16:25 + 找到 {len(triggers)} 个触发器:")
                for trigger in triggers:
                    print(f"  - {trigger['trigger_name']} ({trigger['event_manipulation']})")
            else:
                print("⚠️ 2025/07/03 + 16:25 + 未找到触发器")
            
            # 5. 检查测试数据
            print("\n5️⃣ 2025/07/03 + 16:25 + 检查测试数据...")
            test_data = await conn.fetch("""
                SELECT employee_id, 日付, 星期, 勤務区分, 出勤時刻, 退勤時刻, コメント
                FROM timeprotab 
                WHERE employee_id = '215829'
                ORDER BY 日付 DESC
                LIMIT 5
            """)
            
            if test_data:
                print(f"✅ 2025/07/03 + 16:25 + 找到 {len(test_data)} 条测试数据:")
                for record in test_data:
                    print(f"  - {record['employee_id']} | {record['日付']} | {record['星期']} | {record['勤務区分']} | {record['出勤時刻']}-{record['退勤時刻']}")
            else:
                print("⚠️ 2025/07/03 + 16:25 + 未找到测试数据")
            
            # 6. 测试查询性能
            print("\n6️⃣ 2025/07/03 + 16:25 + 测试查询性能...")
            
            # 测试按员工ID查询
            start_time = asyncio.get_event_loop().time()
            employee_data = await conn.fetch("""
                SELECT COUNT(*) as count
                FROM timeprotab 
                WHERE employee_id = '215829'
            """)
            query_time = (asyncio.get_event_loop().time() - start_time) * 1000
            print(f"✅ 2025/07/03 + 16:25 + 按员工ID查询: {employee_data[0]['count']} 条记录，耗时 {query_time:.2f}ms")
            
            # 测试按日期范围查询
            start_time = asyncio.get_event_loop().time()
            date_range_data = await conn.fetch("""
                SELECT COUNT(*) as count
                FROM timeprotab 
                WHERE 日付 >= '2025-07-01' AND 日付 <= '2025-07-31'
            """)
            query_time = (asyncio.get_event_loop().time() - start_time) * 1000
            print(f"✅ 2025/07/03 + 16:25 + 按日期范围查询: {date_range_data[0]['count']} 条记录，耗时 {query_time:.2f}ms")
            
            # 7. 测试分区查询
            print("\n7️⃣ 2025/07/03 + 16:25 + 测试分区查询...")
            partition_data = await conn.fetch("""
                SELECT 
                    schemaname,
                    tablename,
                    partitiontablename,
                    partitionrangestart,
                    partitionrangeend
                FROM pg_partition_tables 
                WHERE tablename = 'timeprotab'
                ORDER BY partitionrangestart
            """)
            
            if partition_data:
                print(f"✅ 2025/07/03 + 16:25 + 分区信息:")
                for partition in partition_data:
                    print(f"  - {partition['partitiontablename']}: {partition['partitionrangestart']} ~ {partition['partitionrangeend']}")
            else:
                # 尝试另一种方式查询分区信息
                partition_info = await conn.fetch("""
                    SELECT 
                        parent.relname as table_name,
                        child.relname as partition_name,
                        pg_get_expr(child.relpartbound, child.oid) as partition_expression
                    FROM pg_inherits
                    JOIN pg_class parent ON pg_inherits.inhparent = parent.oid
                    JOIN pg_class child ON pg_inherits.inhrelid = child.oid
                    WHERE parent.relname = 'timeprotab'
                    ORDER BY child.relname
                """)
                
                if partition_info:
                    print(f"✅ 2025/07/03 + 16:25 + 分区信息 (通过继承关系):")
                    for partition in partition_info:
                        print(f"  - {partition['partition_name']}: {partition['partition_expression']}")
                else:
                    print("⚠️ 2025/07/03 + 16:25 + 未找到分区信息")
            
            print("\n🎉 2025/07/03 + 16:25 + 验证完成！timeprotab表设置正确")
            return True
            
        finally:
            await conn.close()
            
    except Exception as e:
        print(f"❌ 2025/07/03 + 16:25 + 验证失败: {e}")
        return False

if __name__ == "__main__":
    success = asyncio.run(verify_setup())
    sys.exit(0 if success else 1) 