2025-07-03 16:50:29,241 - server5-2.test - INFO - 🧪 server5-2 测试工具启动（真实采集）
2025-07-03 16:50:29,241 - server5-2.test - INFO - ============================================================
2025-07-03 16:50:29,241 - server5-2.test - INFO - 🎯 开始运行所有测试
2025-07-03 16:50:29,241 - server5-2.test - INFO - 🧪 设置测试环境
2025-07-03 16:50:29,460 - server5-2.timeprotab_manager - INFO - ✅ timeprotab数据库连接池创建成功
2025-07-03 16:50:29,474 - server5-2.timeprotab_manager - INFO - ✅ 基础分区表 timeprotab 初始化完成
2025-07-03 16:50:29,475 - server5-2.test - INFO - ✅ 测试环境设置完成
2025-07-03 16:50:29,475 - server5-2.test - INFO - 🔍 运行测试: 数据库操作
2025-07-03 16:50:29,475 - server5-2.test - INFO - 🧪 测试数据库操作
2025-07-03 16:50:29,483 - server5-2.timeprotab_manager - INFO - 📅 分区 timeprotab_2506 已存在
2025-07-03 16:50:29,484 - server5-2.test - INFO - ✅ 分区创建测试成功: 2506
2025-07-03 16:50:29,485 - server5-2.timeprotab_manager - INFO - 📅 分区 timeprotab_2507 已存在
2025-07-03 16:50:29,486 - server5-2.test - INFO - ✅ 分区创建测试成功: 2507
2025-07-03 16:50:29,496 - server5-2.timeprotab_manager - INFO - 📅 分区 timeprotab_2506 已存在
2025-07-03 16:50:29,500 - server5-2.timeprotab_manager - ERROR - ❌ 批量插入失败: invalid input for query argument $8: '08:30' ('str' object has no attribute 'hour')
2025-07-03 16:50:29,500 - server5-2.test - ERROR - ❌ 数据插入测试失败
2025-07-03 16:50:29,500 - server5-2.test - ERROR - ❌ 测试失败: 数据库操作
2025-07-03 16:50:30,501 - server5-2.test - INFO - 🔍 运行测试: 分区管理
2025-07-03 16:50:30,501 - server5-2.test - INFO - 🧪 测试分区管理
2025-07-03 16:50:30,503 - server5-2.timeprotab_manager - INFO - 📅 分区 timeprotab_2506 已存在
2025-07-03 16:50:30,504 - server5-2.test - INFO - ✅ 分区创建成功: 2506
2025-07-03 16:50:30,506 - server5-2.timeprotab_manager - INFO - 📅 分区 timeprotab_2507 已存在
2025-07-03 16:50:30,507 - server5-2.test - INFO - ✅ 分区创建成功: 2507
2025-07-03 16:50:30,518 - server5-2.test - INFO - ✅ 分区管理测试成功: 创建了 2 个分区
2025-07-03 16:50:30,518 - server5-2.test - INFO - ✅ 测试通过: 分区管理
2025-07-03 16:50:31,519 - server5-2.test - INFO - 🔍 运行测试: XML解析
2025-07-03 16:50:31,519 - server5-2.test - INFO - 🧪 测试XML解析
2025-07-03 16:50:31,520 - server5-2.timepro_collector - INFO - ✅ XML解析完成: 2 条记录
2025-07-03 16:50:31,520 - server5-2.test - INFO - ✅ XML解析测试成功: 2 条记录
2025-07-03 16:50:31,523 - server5-2.timeprotab_manager - INFO - 📅 分区 timeprotab_2506 已存在
2025-07-03 16:50:31,525 - server5-2.timeprotab_manager - ERROR - ❌ 批量插入失败: invalid input for query argument $18: '' ('str' object has no attribute 'hour')
2025-07-03 16:50:31,525 - server5-2.test - ERROR - ❌ 解析数据插入测试失败
2025-07-03 16:50:31,525 - server5-2.test - ERROR - ❌ 测试失败: XML解析
2025-07-03 16:50:32,526 - server5-2.test - INFO - 🔍 运行测试: 真实采集工作流
2025-07-03 16:50:32,526 - server5-2.test - INFO - 🧪 测试真实采集工作流
2025-07-03 16:50:32,722 - server5-2.timeprotab_manager - INFO - ✅ timeprotab数据库连接池创建成功
2025-07-03 16:50:32,736 - server5-2.timeprotab_manager - INFO - ✅ 基础分区表 timeprotab 初始化完成
2025-07-03 16:50:32,737 - server5-2.timepro_collector - INFO - 📅 定时任务已设置: 每天 1:00 开始采集
2025-07-03 16:50:32,738 - server5-2.timepro_collector - INFO - ⏰ 调度器线程启动
2025-07-03 16:50:32,738 - server5-2.timepro_collector - INFO - ✅ timepro采集服务启动成功
2025-07-03 16:50:32,738 - server5-2.timepro_collector - INFO - 🧪 开始真实采集工作流
2025-07-03 16:50:32,738 - server5-2.timepro_collector - INFO - 📅 采集 215829 的 202507 数据
2025-07-03 16:50:32,738 - server5-2.timepro_collector - INFO - 🌐 真实调用timepro网站: user_id=215829
2025-07-03 16:50:32,743 - server5-2.timepro_collector - ERROR - ❌ timepro数据获取失败: Error accessing login page: HTTPConnectionPool(host='timepro.gongsi.co.jp', port=80): Max retries exceeded with url: /xgweb/login.asp (Caused by NameResolutionError("<urllib3.connection.HTTPConnection object at 0x7f9915dbc470>: Failed to resolve 'timepro.gongsi.co.jp' ([Errno -2] Name or service not known)"))
2025-07-03 16:50:32,743 - server5-2.timepro_collector - ERROR - ❌ 真实数据获取失败: 未获取到HTML数据
2025-07-03 16:50:32,743 - server5-2.timepro_collector - INFO - 🔄 使用模拟数据作为备选
2025-07-03 16:50:32,743 - server5-2.timepro_collector - INFO - 📝 生成模拟HTML数据: data/html_files/215829_202507.html
2025-07-03 16:50:32,743 - server5-2.timepro_collector - INFO - 🔄 真实HTML转XML: data/html_files/215829_202507.html -> data/xml_files/215829_202507.xml
2025-07-03 16:50:32,744 - server5-2.timepro_collector - ERROR - ❌ HTML转XML失败: 未生成XML内容
2025-07-03 16:50:32,744 - server5-2.timepro_collector - ERROR - ❌ HTML转XML失败: HTML转XML失败
2025-07-03 16:50:32,744 - server5-2.timepro_collector - INFO - 🔄 使用模拟XML数据作为备选
2025-07-03 16:50:32,744 - server5-2.timepro_collector - INFO - 📝 生成模拟XML数据: data/xml_files/215829_202507.xml
2025-07-03 16:50:32,744 - server5-2.timepro_collector - INFO - ✅ XML解析完成: 2 条记录
2025-07-03 16:50:32,752 - server5-2.timeprotab_manager - INFO - 📅 分区 timeprotab_2507 已存在
2025-07-03 16:50:32,762 - server5-2.timeprotab_manager - INFO - 📅 分区 timeprotab_2507 已存在
2025-07-03 16:50:32,765 - server5-2.timeprotab_manager - ERROR - ❌ 批量插入失败: invalid input for query argument $18: '' ('str' object has no attribute 'hour')
2025-07-03 16:50:32,765 - server5-2.timepro_collector - ERROR - ❌ 数据插入失败
2025-07-03 16:50:32,765 - server5-2.timepro_collector - INFO - ✅ 真实采集工作流完成
2025-07-03 16:50:32,774 - server5-2.test - INFO - ✅ 真实采集工作流测试成功
2025-07-03 16:50:32,777 - server5-2.timeprotab_manager - INFO - 🔌 timeprotab数据库连接池已关闭
2025-07-03 16:50:32,778 - server5-2.timepro_collector - INFO - 🔌 timepro采集服务已停止
2025-07-03 16:50:32,778 - server5-2.test - INFO - ✅ 测试通过: 真实采集工作流
2025-07-03 16:50:33,779 - server5-2.test - INFO - 🔍 运行测试: 实际用户真实采集
2025-07-03 16:50:33,779 - server5-2.test - INFO - 🧪 测试实际用户真实采集
2025-07-03 16:50:33,779 - server5-2.test - INFO - 🔐 测试用户: 215829
2025-07-03 16:50:33,779 - server5-2.test - INFO - 🔑 测试密码: jiaban01
2025-07-03 16:50:34,034 - server5-2.timeprotab_manager - INFO - ✅ timeprotab数据库连接池创建成功
2025-07-03 16:50:34,048 - server5-2.timeprotab_manager - INFO - ✅ 基础分区表 timeprotab 初始化完成
2025-07-03 16:50:34,049 - server5-2.timepro_collector - INFO - 📅 定时任务已设置: 每天 1:00 开始采集
2025-07-03 16:50:34,049 - server5-2.timepro_collector - INFO - ⏰ 调度器线程启动
2025-07-03 16:50:34,049 - server5-2.timepro_collector - INFO - ✅ timepro采集服务启动成功
2025-07-03 16:50:34,049 - server5-2.timepro_collector - INFO - 📅 开始采集指定月份数据: 215829 - ['202506', '202507']
2025-07-03 16:50:34,049 - server5-2.timepro_collector - INFO - 📅 采集月份: 2025/06
2025-07-03 16:50:34,049 - server5-2.timepro_collector - INFO - 📅 采集 215829 的 202506 数据
2025-07-03 16:50:34,049 - server5-2.timepro_collector - INFO - 🌐 真实调用timepro网站: user_id=215829
2025-07-03 16:50:34,053 - server5-2.timepro_collector - ERROR - ❌ timepro数据获取失败: Error accessing login page: HTTPConnectionPool(host='timepro.gongsi.co.jp', port=80): Max retries exceeded with url: /xgweb/login.asp (Caused by NameResolutionError("<urllib3.connection.HTTPConnection object at 0x7f9915d8f410>: Failed to resolve 'timepro.gongsi.co.jp' ([Errno -2] Name or service not known)"))
2025-07-03 16:50:34,053 - server5-2.timepro_collector - ERROR - ❌ 真实数据获取失败: 未获取到HTML数据
2025-07-03 16:50:34,053 - server5-2.timepro_collector - INFO - 🔄 使用模拟数据作为备选
2025-07-03 16:50:34,053 - server5-2.timepro_collector - INFO - 📝 生成模拟HTML数据: data/html_files/215829_202506.html
2025-07-03 16:50:34,053 - server5-2.timepro_collector - INFO - 🔄 真实HTML转XML: data/html_files/215829_202506.html -> data/xml_files/215829_202506.xml
2025-07-03 16:50:34,054 - server5-2.timepro_collector - ERROR - ❌ HTML转XML失败: 未生成XML内容
2025-07-03 16:50:34,054 - server5-2.timepro_collector - ERROR - ❌ HTML转XML失败: HTML转XML失败
2025-07-03 16:50:34,054 - server5-2.timepro_collector - INFO - 🔄 使用模拟XML数据作为备选
2025-07-03 16:50:34,054 - server5-2.timepro_collector - INFO - 📝 生成模拟XML数据: data/xml_files/215829_202506.xml
2025-07-03 16:50:34,054 - server5-2.timepro_collector - INFO - ✅ XML解析完成: 2 条记录
2025-07-03 16:50:34,063 - server5-2.timeprotab_manager - INFO - 📅 分区 timeprotab_2506 已存在
2025-07-03 16:50:34,073 - server5-2.timeprotab_manager - INFO - 📅 分区 timeprotab_2507 已存在
2025-07-03 16:50:34,077 - server5-2.timeprotab_manager - ERROR - ❌ 批量插入失败: invalid input for query argument $18: '' ('str' object has no attribute 'hour')
2025-07-03 16:50:34,077 - server5-2.timepro_collector - ERROR - ❌ 数据插入失败
2025-07-03 16:50:44,078 - server5-2.timepro_collector - INFO - 📅 采集月份: 2025/07
2025-07-03 16:50:44,078 - server5-2.timepro_collector - INFO - 📅 采集 215829 的 202507 数据
2025-07-03 16:50:44,078 - server5-2.timepro_collector - INFO - 🌐 真实调用timepro网站: user_id=215829
2025-07-03 16:50:44,082 - server5-2.timepro_collector - ERROR - ❌ timepro数据获取失败: Error accessing login page: HTTPConnectionPool(host='timepro.gongsi.co.jp', port=80): Max retries exceeded with url: /xgweb/login.asp (Caused by NameResolutionError("<urllib3.connection.HTTPConnection object at 0x7f99143d01a0>: Failed to resolve 'timepro.gongsi.co.jp' ([Errno -2] Name or service not known)"))
2025-07-03 16:50:44,082 - server5-2.timepro_collector - ERROR - ❌ 真实数据获取失败: 未获取到HTML数据
2025-07-03 16:50:44,082 - server5-2.timepro_collector - INFO - 🔄 使用模拟数据作为备选
2025-07-03 16:50:44,082 - server5-2.timepro_collector - INFO - 📝 生成模拟HTML数据: data/html_files/215829_202507.html
2025-07-03 16:50:44,082 - server5-2.timepro_collector - INFO - 🔄 真实HTML转XML: data/html_files/215829_202507.html -> data/xml_files/215829_202507.xml
2025-07-03 16:50:44,083 - server5-2.timepro_collector - ERROR - ❌ HTML转XML失败: 未生成XML内容
2025-07-03 16:50:44,083 - server5-2.timepro_collector - ERROR - ❌ HTML转XML失败: HTML转XML失败
2025-07-03 16:50:44,083 - server5-2.timepro_collector - INFO - 🔄 使用模拟XML数据作为备选
2025-07-03 16:50:44,083 - server5-2.timepro_collector - INFO - 📝 生成模拟XML数据: data/xml_files/215829_202507.xml
2025-07-03 16:50:44,084 - server5-2.timepro_collector - INFO - ✅ XML解析完成: 2 条记录
2025-07-03 16:50:44,085 - server5-2.timeprotab_manager - INFO - 📅 分区 timeprotab_2507 已存在
2025-07-03 16:50:44,088 - server5-2.timeprotab_manager - INFO - 📅 分区 timeprotab_2507 已存在
2025-07-03 16:50:44,091 - server5-2.timeprotab_manager - ERROR - ❌ 批量插入失败: invalid input for query argument $18: '' ('str' object has no attribute 'hour')
2025-07-03 16:50:44,091 - server5-2.timepro_collector - ERROR - ❌ 数据插入失败
2025-07-03 16:50:54,101 - server5-2.timepro_collector - INFO - ✅ 指定月份数据采集完成: 215829
2025-07-03 16:50:54,101 - server5-2.test - INFO - ✅ 实际用户真实采集测试成功
2025-07-03 16:50:54,105 - server5-2.timeprotab_manager - INFO - 📊 查询到 1 条记录: 215829 - 2506
2025-07-03 16:50:54,106 - server5-2.test - INFO - ✅ 验证采集数据成功 (2506): 1 条记录
2025-07-03 16:50:54,106 - server5-2.test - INFO - 📊 采集记录: 215829 - 2025-06-30 - 2025/07/03 + 16:25 + 上个月测试数据 - 实际用户215829
2025-07-03 16:50:54,107 - server5-2.timeprotab_manager - INFO - 📊 查询到 2 条记录: 215829 - 2507
2025-07-03 16:50:54,108 - server5-2.test - INFO - ✅ 验证采集数据成功 (2507): 2 条记录
2025-07-03 16:50:54,108 - server5-2.test - INFO - 📊 采集记录: 215829 - 2025-07-01 - 2025/07/03 + 16:25 + 测试数据 - 实际用户215829
2025-07-03 16:50:54,108 - server5-2.test - INFO - 📊 采集记录: 215829 - 2025-07-02 - 2025/07/03 + 16:25 + 测试数据2 - 实际用户215829
2025-07-03 16:50:54,112 - server5-2.timeprotab_manager - INFO - 🔌 timeprotab数据库连接池已关闭
2025-07-03 16:50:54,112 - server5-2.timepro_collector - INFO - 🔌 timepro采集服务已停止
2025-07-03 16:50:54,112 - server5-2.test - INFO - ✅ 测试通过: 实际用户真实采集
2025-07-03 16:50:55,113 - server5-2.test - INFO - ============================================================
2025-07-03 16:50:55,113 - server5-2.test - INFO - 📊 测试结果汇总:
2025-07-03 16:50:55,113 - server5-2.test - INFO -   数据库操作: ❌ FAIL
2025-07-03 16:50:55,113 - server5-2.test - INFO -   分区管理: ✅ PASS
2025-07-03 16:50:55,113 - server5-2.test - INFO -   XML解析: ❌ FAIL
2025-07-03 16:50:55,113 - server5-2.test - INFO -   真实采集工作流: ✅ PASS
2025-07-03 16:50:55,113 - server5-2.test - INFO -   实际用户真实采集: ✅ PASS
2025-07-03 16:50:55,113 - server5-2.test - INFO - 总计: 3/5 测试通过
2025-07-03 16:50:55,113 - server5-2.test - INFO - ============================================================
2025-07-03 16:50:55,117 - server5-2.timeprotab_manager - INFO - 🔌 timeprotab数据库连接池已关闭
2025-07-03 16:50:55,117 - server5-2.test - INFO - ✅ 测试环境清理完成
2025-07-03 16:50:55,117 - server5-2.test - ERROR - ❌ 部分测试失败
2025-07-03 16:51:18,243 - server5-2.test - INFO - 🧪 server5-2 测试工具启动（真实采集）
2025-07-03 16:51:18,243 - server5-2.test - INFO - ============================================================
2025-07-03 16:51:18,243 - server5-2.test - INFO - 🎯 开始运行所有测试
2025-07-03 16:51:18,244 - server5-2.test - INFO - 🧪 设置测试环境
2025-07-03 16:51:18,477 - server5-2.timeprotab_manager - INFO - ✅ timeprotab数据库连接池创建成功
2025-07-03 16:51:18,493 - server5-2.timeprotab_manager - INFO - ✅ 基础分区表 timeprotab 初始化完成
2025-07-03 16:51:18,495 - server5-2.test - INFO - ✅ 测试环境设置完成
2025-07-03 16:51:18,495 - server5-2.test - INFO - 🔍 运行测试: 数据库操作
2025-07-03 16:51:18,495 - server5-2.test - INFO - 🧪 测试数据库操作
2025-07-03 16:51:18,503 - server5-2.timeprotab_manager - INFO - 📅 分区 timeprotab_2506 已存在
2025-07-03 16:51:18,504 - server5-2.test - INFO - ✅ 分区创建测试成功: 2506
2025-07-03 16:51:18,506 - server5-2.timeprotab_manager - INFO - 📅 分区 timeprotab_2507 已存在
2025-07-03 16:51:18,506 - server5-2.test - INFO - ✅ 分区创建测试成功: 2507
2025-07-03 16:51:18,516 - server5-2.timeprotab_manager - INFO - 📅 分区 timeprotab_2506 已存在
2025-07-03 16:51:18,524 - server5-2.timeprotab_manager - INFO - 📅 分区 timeprotab_2507 已存在
2025-07-03 16:51:18,528 - server5-2.timeprotab_manager - INFO - ✅ 批量插入完成: 2 条记录
2025-07-03 16:51:18,528 - server5-2.test - INFO - ✅ 数据插入测试成功: 2 条记录
2025-07-03 16:51:18,532 - server5-2.timeprotab_manager - INFO - 📊 查询到 2 条记录: 215829 - 2506
2025-07-03 16:51:18,533 - server5-2.test - INFO - ✅ 数据查询测试成功 (2506): 2 条记录
2025-07-03 16:51:18,533 - server5-2.test - INFO - 📊 查询记录: 215829 - 2025-06-01 - 测试数据 - 实际用户215829 - 6月
2025-07-03 16:51:18,533 - server5-2.test - INFO - 📊 查询记录: 215829 - 2025-06-30 - 2025/07/03 + 16:25 + 上个月测试数据 - 实际用户215829
2025-07-03 16:51:18,535 - server5-2.timeprotab_manager - INFO - 📊 查询到 3 条记录: 215829 - 2507
2025-07-03 16:51:18,536 - server5-2.test - INFO - ✅ 数据查询测试成功 (2507): 3 条记录
2025-07-03 16:51:18,536 - server5-2.test - INFO - 📊 查询记录: 215829 - 2025-07-01 - 2025/07/03 + 16:25 + 测试数据 - 实际用户215829
2025-07-03 16:51:18,536 - server5-2.test - INFO - 📊 查询记录: 215829 - 2025-07-01 - 测试数据 - 实际用户215829 - 7月
2025-07-03 16:51:18,542 - server5-2.test - INFO - ✅ 状态获取测试成功
2025-07-03 16:51:18,543 - server5-2.test - INFO - ✅ 测试通过: 数据库操作
2025-07-03 16:51:19,544 - server5-2.test - INFO - 🔍 运行测试: 分区管理
2025-07-03 16:51:19,544 - server5-2.test - INFO - 🧪 测试分区管理
2025-07-03 16:51:19,545 - server5-2.timeprotab_manager - INFO - 📅 分区 timeprotab_2506 已存在
2025-07-03 16:51:19,546 - server5-2.test - INFO - ✅ 分区创建成功: 2506
2025-07-03 16:51:19,547 - server5-2.timeprotab_manager - INFO - 📅 分区 timeprotab_2507 已存在
2025-07-03 16:51:19,548 - server5-2.test - INFO - ✅ 分区创建成功: 2507
2025-07-03 16:51:19,551 - server5-2.test - INFO - ✅ 分区管理测试成功: 创建了 2 个分区
2025-07-03 16:51:19,551 - server5-2.test - INFO - ✅ 测试通过: 分区管理
2025-07-03 16:51:20,552 - server5-2.test - INFO - 🔍 运行测试: XML解析
2025-07-03 16:51:20,552 - server5-2.test - INFO - 🧪 测试XML解析
2025-07-03 16:51:20,553 - server5-2.timepro_collector - INFO - ✅ XML解析完成: 2 条记录
2025-07-03 16:51:20,553 - server5-2.test - INFO - ✅ XML解析测试成功: 2 条记录
2025-07-03 16:51:20,555 - server5-2.timeprotab_manager - INFO - 📅 分区 timeprotab_2506 已存在
2025-07-03 16:51:20,558 - server5-2.timeprotab_manager - INFO - 📅 分区 timeprotab_2507 已存在
2025-07-03 16:51:20,561 - server5-2.timeprotab_manager - INFO - ✅ 批量插入完成: 2 条记录
2025-07-03 16:51:20,561 - server5-2.test - INFO - ✅ 解析数据插入测试成功: 2 条记录
2025-07-03 16:51:20,561 - server5-2.test - INFO - ✅ 测试通过: XML解析
2025-07-03 16:51:21,562 - server5-2.test - INFO - 🔍 运行测试: 真实采集工作流
2025-07-03 16:51:21,562 - server5-2.test - INFO - 🧪 测试真实采集工作流
2025-07-03 16:51:21,805 - server5-2.timeprotab_manager - INFO - ✅ timeprotab数据库连接池创建成功
2025-07-03 16:51:21,819 - server5-2.timeprotab_manager - INFO - ✅ 基础分区表 timeprotab 初始化完成
2025-07-03 16:51:21,820 - server5-2.timepro_collector - INFO - 📅 定时任务已设置: 每天 1:00 开始采集
2025-07-03 16:51:21,820 - server5-2.timepro_collector - INFO - ⏰ 调度器线程启动
2025-07-03 16:51:21,820 - server5-2.timepro_collector - INFO - ✅ timepro采集服务启动成功
2025-07-03 16:51:21,821 - server5-2.timepro_collector - INFO - 🧪 开始真实采集工作流
2025-07-03 16:51:21,821 - server5-2.timepro_collector - INFO - 📅 采集 215829 的 202507 数据
2025-07-03 16:51:21,821 - server5-2.timepro_collector - INFO - 🌐 真实调用timepro网站: user_id=215829
2025-07-03 16:51:21,826 - server5-2.timepro_collector - ERROR - ❌ timepro数据获取失败: Error accessing login page: HTTPConnectionPool(host='timepro.gongsi.co.jp', port=80): Max retries exceeded with url: /xgweb/login.asp (Caused by NameResolutionError("<urllib3.connection.HTTPConnection object at 0x704cdcd45bb0>: Failed to resolve 'timepro.gongsi.co.jp' ([Errno -2] Name or service not known)"))
2025-07-03 16:51:21,826 - server5-2.timepro_collector - ERROR - ❌ 真实数据获取失败: 未获取到HTML数据
2025-07-03 16:51:21,826 - server5-2.timepro_collector - INFO - 🔄 使用模拟数据作为备选
2025-07-03 16:51:21,826 - server5-2.timepro_collector - INFO - 📝 生成模拟HTML数据: data/html_files/215829_202507.html
2025-07-03 16:51:21,826 - server5-2.timepro_collector - INFO - 🔄 真实HTML转XML: data/html_files/215829_202507.html -> data/xml_files/215829_202507.xml
2025-07-03 16:51:21,827 - server5-2.timepro_collector - ERROR - ❌ HTML转XML失败: 未生成XML内容
2025-07-03 16:51:21,827 - server5-2.timepro_collector - ERROR - ❌ HTML转XML失败: HTML转XML失败
2025-07-03 16:51:21,827 - server5-2.timepro_collector - INFO - 🔄 使用模拟XML数据作为备选
2025-07-03 16:51:21,827 - server5-2.timepro_collector - INFO - 📝 生成模拟XML数据: data/xml_files/215829_202507.xml
2025-07-03 16:51:21,827 - server5-2.timepro_collector - INFO - ✅ XML解析完成: 2 条记录
2025-07-03 16:51:21,834 - server5-2.timeprotab_manager - INFO - 📅 分区 timeprotab_2507 已存在
2025-07-03 16:51:21,843 - server5-2.timeprotab_manager - INFO - 📅 分区 timeprotab_2507 已存在
2025-07-03 16:51:21,848 - server5-2.timeprotab_manager - INFO - 📅 分区 timeprotab_2507 已存在
2025-07-03 16:51:21,851 - server5-2.timeprotab_manager - INFO - ✅ 批量插入完成: 2 条记录
2025-07-03 16:51:21,851 - server5-2.timepro_collector - INFO - ✅ 数据插入成功: 2 条记录
2025-07-03 16:51:21,851 - server5-2.timepro_collector - INFO - ✅ 真实采集工作流完成
2025-07-03 16:51:21,860 - server5-2.test - INFO - ✅ 真实采集工作流测试成功
2025-07-03 16:51:21,863 - server5-2.timeprotab_manager - INFO - 🔌 timeprotab数据库连接池已关闭
2025-07-03 16:51:21,863 - server5-2.timepro_collector - INFO - 🔌 timepro采集服务已停止
2025-07-03 16:51:21,863 - server5-2.test - INFO - ✅ 测试通过: 真实采集工作流
2025-07-03 16:51:22,865 - server5-2.test - INFO - 🔍 运行测试: 实际用户真实采集
2025-07-03 16:51:22,865 - server5-2.test - INFO - 🧪 测试实际用户真实采集
2025-07-03 16:51:22,865 - server5-2.test - INFO - 🔐 测试用户: 215829
2025-07-03 16:51:22,865 - server5-2.test - INFO - 🔑 测试密码: jiaban01
2025-07-03 16:51:23,121 - server5-2.timeprotab_manager - INFO - ✅ timeprotab数据库连接池创建成功
2025-07-03 16:51:23,136 - server5-2.timeprotab_manager - INFO - ✅ 基础分区表 timeprotab 初始化完成
2025-07-03 16:51:23,137 - server5-2.timepro_collector - INFO - 📅 定时任务已设置: 每天 1:00 开始采集
2025-07-03 16:51:23,137 - server5-2.timepro_collector - INFO - ⏰ 调度器线程启动
2025-07-03 16:51:23,137 - server5-2.timepro_collector - INFO - ✅ timepro采集服务启动成功
2025-07-03 16:51:23,137 - server5-2.timepro_collector - INFO - 📅 开始采集指定月份数据: 215829 - ['202506', '202507']
2025-07-03 16:51:23,137 - server5-2.timepro_collector - INFO - 📅 采集月份: 2025/06
2025-07-03 16:51:23,137 - server5-2.timepro_collector - INFO - 📅 采集 215829 的 202506 数据
2025-07-03 16:51:23,137 - server5-2.timepro_collector - INFO - 🌐 真实调用timepro网站: user_id=215829
2025-07-03 16:51:23,141 - server5-2.timepro_collector - ERROR - ❌ timepro数据获取失败: Error accessing login page: HTTPConnectionPool(host='timepro.gongsi.co.jp', port=80): Max retries exceeded with url: /xgweb/login.asp (Caused by NameResolutionError("<urllib3.connection.HTTPConnection object at 0x704cdc83b500>: Failed to resolve 'timepro.gongsi.co.jp' ([Errno -2] Name or service not known)"))
2025-07-03 16:51:23,141 - server5-2.timepro_collector - ERROR - ❌ 真实数据获取失败: 未获取到HTML数据
2025-07-03 16:51:23,142 - server5-2.timepro_collector - INFO - 🔄 使用模拟数据作为备选
2025-07-03 16:51:23,142 - server5-2.timepro_collector - INFO - 📝 生成模拟HTML数据: data/html_files/215829_202506.html
2025-07-03 16:51:23,142 - server5-2.timepro_collector - INFO - 🔄 真实HTML转XML: data/html_files/215829_202506.html -> data/xml_files/215829_202506.xml
2025-07-03 16:51:23,142 - server5-2.timepro_collector - ERROR - ❌ HTML转XML失败: 未生成XML内容
2025-07-03 16:51:23,142 - server5-2.timepro_collector - ERROR - ❌ HTML转XML失败: HTML转XML失败
2025-07-03 16:51:23,143 - server5-2.timepro_collector - INFO - 🔄 使用模拟XML数据作为备选
2025-07-03 16:51:23,143 - server5-2.timepro_collector - INFO - 📝 生成模拟XML数据: data/xml_files/215829_202506.xml
2025-07-03 16:51:23,143 - server5-2.timepro_collector - INFO - ✅ XML解析完成: 2 条记录
2025-07-03 16:51:23,152 - server5-2.timeprotab_manager - INFO - 📅 分区 timeprotab_2506 已存在
2025-07-03 16:51:23,163 - server5-2.timeprotab_manager - INFO - 📅 分区 timeprotab_2507 已存在
2025-07-03 16:51:23,169 - server5-2.timeprotab_manager - INFO - 📅 分区 timeprotab_2507 已存在
2025-07-03 16:51:23,172 - server5-2.timeprotab_manager - INFO - ✅ 批量插入完成: 2 条记录
2025-07-03 16:51:23,172 - server5-2.timepro_collector - INFO - ✅ 数据插入成功: 2 条记录
2025-07-03 16:51:31,194 - server5-2.timeprotab_manager - INFO - 🔌 timeprotab数据库连接池已关闭
2025-07-03 16:51:31,194 - server5-2.timepro_collector - INFO - 🔌 timepro采集服务已停止
2025-07-03 16:51:31,198 - server5-2.timeprotab_manager - INFO - 🔌 timeprotab数据库连接池已关闭
2025-07-03 16:51:31,198 - server5-2.test - INFO - ✅ 测试环境清理完成
2025-07-03 16:51:31,199 - server5-2.test - INFO - 👋 测试被用户中断
2025-07-03 16:56:47,030 - server5-2.test - INFO - 🧪 server5-2 测试工具启动（真实采集）
2025-07-03 16:56:47,030 - server5-2.test - INFO - ============================================================
2025-07-03 16:56:47,030 - server5-2.test - INFO - 🎯 开始运行所有测试
2025-07-03 16:56:47,030 - server5-2.test - INFO - 🧪 设置测试环境
2025-07-03 16:56:47,241 - server5-2.timeprotab_manager - INFO - ✅ timeprotab数据库连接池创建成功
2025-07-03 16:56:47,257 - server5-2.timeprotab_manager - INFO - ✅ 基础分区表 timeprotab 初始化完成
2025-07-03 16:56:47,258 - server5-2.test - INFO - ✅ 测试环境设置完成
2025-07-03 16:56:47,258 - server5-2.test - INFO - 🔍 运行测试: 数据库操作
2025-07-03 16:56:47,258 - server5-2.test - INFO - 🧪 测试数据库操作
2025-07-03 16:56:47,268 - server5-2.timeprotab_manager - INFO - 📅 分区 timeprotab_2506 已存在
2025-07-03 16:56:47,269 - server5-2.test - INFO - ✅ 分区创建测试成功: 2506
2025-07-03 16:56:47,270 - server5-2.timeprotab_manager - INFO - 📅 分区 timeprotab_2507 已存在
2025-07-03 16:56:47,271 - server5-2.test - INFO - ✅ 分区创建测试成功: 2507
2025-07-03 16:56:47,281 - server5-2.timeprotab_manager - INFO - 📅 分区 timeprotab_2506 已存在
2025-07-03 16:56:47,289 - server5-2.timeprotab_manager - INFO - 📅 分区 timeprotab_2507 已存在
2025-07-03 16:56:47,293 - server5-2.timeprotab_manager - INFO - ✅ 批量插入完成: 2 条记录
2025-07-03 16:56:47,294 - server5-2.test - INFO - ✅ 数据插入测试成功: 2 条记录
2025-07-03 16:56:47,299 - server5-2.timeprotab_manager - INFO - 📊 查询到 6 条记录: 215829 - 2506
2025-07-03 16:56:47,299 - server5-2.test - INFO - ✅ 数据查询测试成功 (2506): 6 条记录
2025-07-03 16:56:47,300 - server5-2.test - INFO - 📊 查询记录: 215829 - 2025-06-01 - 测试数据 - 实际用户215829 - 6月
2025-07-03 16:56:47,300 - server5-2.test - INFO - 📊 查询记录: 215829 - 2025-06-01 - XML解析测试 - 6月
2025-07-03 16:56:47,303 - server5-2.timeprotab_manager - INFO - 📊 查询到 17 条记录: 215829 - 2507
2025-07-03 16:56:47,304 - server5-2.test - INFO - ✅ 数据查询测试成功 (2507): 17 条记录
2025-07-03 16:56:47,304 - server5-2.test - INFO - 📊 查询记录: 215829 - 2025-07-01 - 2025/07/03 + 16:25 + 测试数据 - 实际用户215829
2025-07-03 16:56:47,304 - server5-2.test - INFO - 📊 查询记录: 215829 - 2025-07-01 - 测试数据 - 实际用户215829 - 7月
2025-07-03 16:56:47,310 - server5-2.test - INFO - ✅ 状态获取测试成功
2025-07-03 16:56:47,311 - server5-2.test - INFO - ✅ 测试通过: 数据库操作
2025-07-03 16:56:48,312 - server5-2.test - INFO - 🔍 运行测试: 分区管理
2025-07-03 16:56:48,312 - server5-2.test - INFO - 🧪 测试分区管理
2025-07-03 16:56:48,313 - server5-2.timeprotab_manager - INFO - 📅 分区 timeprotab_2506 已存在
2025-07-03 16:56:48,314 - server5-2.test - INFO - ✅ 分区创建成功: 2506
2025-07-03 16:56:48,315 - server5-2.timeprotab_manager - INFO - 📅 分区 timeprotab_2507 已存在
2025-07-03 16:56:48,316 - server5-2.test - INFO - ✅ 分区创建成功: 2507
2025-07-03 16:56:48,319 - server5-2.test - INFO - ✅ 分区管理测试成功: 创建了 2 个分区
2025-07-03 16:56:48,319 - server5-2.test - INFO - ✅ 测试通过: 分区管理
2025-07-03 16:56:49,320 - server5-2.test - INFO - 🔍 运行测试: XML解析
2025-07-03 16:56:49,320 - server5-2.test - INFO - 🧪 测试XML解析
2025-07-03 16:56:49,320 - server5-2.timepro_collector - INFO - ✅ XML解析完成: 2 条记录
2025-07-03 16:56:49,320 - server5-2.test - INFO - ✅ XML解析测试成功: 2 条记录
2025-07-03 16:56:49,323 - server5-2.timeprotab_manager - INFO - 📅 分区 timeprotab_2506 已存在
2025-07-03 16:56:49,326 - server5-2.timeprotab_manager - INFO - 📅 分区 timeprotab_2507 已存在
2025-07-03 16:56:49,328 - server5-2.timeprotab_manager - INFO - ✅ 批量插入完成: 2 条记录
2025-07-03 16:56:49,328 - server5-2.test - INFO - ✅ 解析数据插入测试成功: 2 条记录
2025-07-03 16:56:49,328 - server5-2.test - INFO - ✅ 测试通过: XML解析
2025-07-03 16:56:50,330 - server5-2.test - INFO - 🔍 运行测试: 真实采集工作流
2025-07-03 16:56:50,330 - server5-2.test - INFO - 🧪 测试真实采集工作流
2025-07-03 16:56:50,557 - server5-2.timeprotab_manager - INFO - ✅ timeprotab数据库连接池创建成功
2025-07-03 16:56:50,571 - server5-2.timeprotab_manager - INFO - ✅ 基础分区表 timeprotab 初始化完成
2025-07-03 16:56:50,572 - server5-2.timepro_collector - INFO - 📅 定时任务已设置: 每天 1:00 开始采集
2025-07-03 16:56:50,572 - server5-2.timepro_collector - INFO - ⏰ 调度器线程启动
2025-07-03 16:56:50,572 - server5-2.timepro_collector - INFO - ✅ timepro采集服务启动成功
2025-07-03 16:56:50,572 - server5-2.timepro_collector - INFO - 🧪 开始真实采集工作流
2025-07-03 16:56:50,572 - server5-2.timepro_collector - INFO - 📅 采集 215829 的 202507 数据
2025-07-03 16:56:50,572 - server5-2.timepro_collector - INFO - 🌐 真实调用timepro网站: user_id=215829
2025-07-03 16:56:51,833 - server5-2.timepro_collector - INFO - ✅ timepro数据获取成功: Successfully extracted 202507 data to data/html_files/202507_body_content.html
2025-07-03 16:56:51,833 - server5-2.timepro_collector - INFO - 📁 找到生成的HTML文件: ['202507_body_content.html']
2025-07-03 16:56:51,833 - server5-2.timepro_collector - INFO - ✅ HTML数据保存成功: data/html_files/215829_202507.html
2025-07-03 16:56:51,833 - server5-2.timepro_collector - INFO - 🔄 真实HTML转XML: data/html_files/215829_202507.html -> data/xml_files/215829_202507.xml
2025-07-03 16:56:51,834 - server5-2.timepro_collector - ERROR - ❌ HTML转XML失败: 未生成XML内容
2025-07-03 16:56:51,834 - server5-2.timepro_collector - ERROR - ❌ HTML转XML失败: HTML转XML失败
2025-07-03 16:56:51,834 - server5-2.timepro_collector - INFO - 🔄 使用模拟XML数据作为备选
2025-07-03 16:56:51,834 - server5-2.timepro_collector - INFO - 📝 生成模拟XML数据: data/xml_files/215829_202507.xml
2025-07-03 16:56:51,835 - server5-2.timepro_collector - INFO - ✅ XML解析完成: 2 条记录
2025-07-03 16:56:51,844 - server5-2.timeprotab_manager - INFO - 📅 分区 timeprotab_2507 已存在
2025-07-03 16:56:51,853 - server5-2.timeprotab_manager - INFO - 📅 分区 timeprotab_2507 已存在
2025-07-03 16:56:51,859 - server5-2.timeprotab_manager - INFO - 📅 分区 timeprotab_2507 已存在
2025-07-03 16:56:51,861 - server5-2.timeprotab_manager - INFO - ✅ 批量插入完成: 2 条记录
2025-07-03 16:56:51,862 - server5-2.timepro_collector - INFO - ✅ 数据插入成功: 2 条记录
2025-07-03 16:56:51,862 - server5-2.timepro_collector - INFO - ✅ 真实采集工作流完成
2025-07-03 16:56:51,871 - server5-2.test - INFO - ✅ 真实采集工作流测试成功
2025-07-03 16:56:51,874 - server5-2.timeprotab_manager - INFO - 🔌 timeprotab数据库连接池已关闭
2025-07-03 16:56:51,874 - server5-2.timepro_collector - INFO - 🔌 timepro采集服务已停止
2025-07-03 16:56:51,874 - server5-2.test - INFO - ✅ 测试通过: 真实采集工作流
2025-07-03 16:56:52,875 - server5-2.test - INFO - 🔍 运行测试: 实际用户真实采集
2025-07-03 16:56:52,875 - server5-2.test - INFO - 🧪 测试实际用户真实采集
2025-07-03 16:56:52,875 - server5-2.test - INFO - 🔐 测试用户: 215829
2025-07-03 16:56:52,875 - server5-2.test - INFO - 🔑 测试密码: jiaban01
2025-07-03 16:56:53,106 - server5-2.timeprotab_manager - INFO - ✅ timeprotab数据库连接池创建成功
2025-07-03 16:56:53,121 - server5-2.timeprotab_manager - INFO - ✅ 基础分区表 timeprotab 初始化完成
2025-07-03 16:56:53,122 - server5-2.timepro_collector - INFO - 📅 定时任务已设置: 每天 1:00 开始采集
2025-07-03 16:56:53,123 - server5-2.timepro_collector - INFO - ⏰ 调度器线程启动
2025-07-03 16:56:53,123 - server5-2.timepro_collector - INFO - ✅ timepro采集服务启动成功
2025-07-03 16:56:53,123 - server5-2.timepro_collector - INFO - 📅 开始采集指定月份数据: 215829 - ['202506', '202507']
2025-07-03 16:56:53,123 - server5-2.timepro_collector - INFO - 📅 采集月份: 2025/06
2025-07-03 16:56:53,123 - server5-2.timepro_collector - INFO - 📅 采集 215829 的 202506 数据
2025-07-03 16:56:53,123 - server5-2.timepro_collector - INFO - 🌐 真实调用timepro网站: user_id=215829
2025-07-03 16:56:54,337 - server5-2.timepro_collector - INFO - ✅ timepro数据获取成功: Successfully extracted 202506 data to data/html_files/202506_body_content.html
2025-07-03 16:56:54,337 - server5-2.timepro_collector - INFO - 📁 找到生成的HTML文件: ['202506_body_content.html']
2025-07-03 16:56:54,338 - server5-2.timepro_collector - INFO - ✅ HTML数据保存成功: data/html_files/215829_202506.html
2025-07-03 16:56:54,338 - server5-2.timepro_collector - INFO - 🔄 真实HTML转XML: data/html_files/215829_202506.html -> data/xml_files/215829_202506.xml
2025-07-03 16:56:54,338 - server5-2.timepro_collector - ERROR - ❌ HTML转XML失败: 未生成XML内容
2025-07-03 16:56:54,338 - server5-2.timepro_collector - ERROR - ❌ HTML转XML失败: HTML转XML失败
2025-07-03 16:56:54,338 - server5-2.timepro_collector - INFO - 🔄 使用模拟XML数据作为备选
2025-07-03 16:56:54,338 - server5-2.timepro_collector - INFO - 📝 生成模拟XML数据: data/xml_files/215829_202506.xml
2025-07-03 16:56:54,339 - server5-2.timepro_collector - INFO - ✅ XML解析完成: 2 条记录
2025-07-03 16:56:54,349 - server5-2.timeprotab_manager - INFO - 📅 分区 timeprotab_2506 已存在
2025-07-03 16:56:54,361 - server5-2.timeprotab_manager - INFO - 📅 分区 timeprotab_2507 已存在
2025-07-03 16:56:54,367 - server5-2.timeprotab_manager - INFO - 📅 分区 timeprotab_2507 已存在
2025-07-03 16:56:54,370 - server5-2.timeprotab_manager - INFO - ✅ 批量插入完成: 2 条记录
2025-07-03 16:56:54,370 - server5-2.timepro_collector - INFO - ✅ 数据插入成功: 2 条记录
2025-07-03 16:57:04,381 - server5-2.timepro_collector - INFO - 📅 采集月份: 2025/07
2025-07-03 16:57:04,381 - server5-2.timepro_collector - INFO - 📅 采集 215829 的 202507 数据
2025-07-03 16:57:04,381 - server5-2.timepro_collector - INFO - 🌐 真实调用timepro网站: user_id=215829
2025-07-03 16:57:05,580 - server5-2.timepro_collector - INFO - ✅ timepro数据获取成功: Successfully extracted 202507 data to data/html_files/202507_body_content.html
2025-07-03 16:57:05,580 - server5-2.timepro_collector - INFO - 📁 找到生成的HTML文件: ['202507_body_content.html']
2025-07-03 16:57:05,581 - server5-2.timepro_collector - INFO - ✅ HTML数据保存成功: data/html_files/215829_202507.html
2025-07-03 16:57:05,581 - server5-2.timepro_collector - INFO - 🔄 真实HTML转XML: data/html_files/215829_202507.html -> data/xml_files/215829_202507.xml
2025-07-03 16:57:05,581 - server5-2.timepro_collector - ERROR - ❌ HTML转XML失败: 未生成XML内容
2025-07-03 16:57:05,581 - server5-2.timepro_collector - ERROR - ❌ HTML转XML失败: HTML转XML失败
2025-07-03 16:57:05,581 - server5-2.timepro_collector - INFO - 🔄 使用模拟XML数据作为备选
2025-07-03 16:57:05,582 - server5-2.timepro_collector - INFO - 📝 生成模拟XML数据: data/xml_files/215829_202507.xml
2025-07-03 16:57:05,582 - server5-2.timepro_collector - INFO - ✅ XML解析完成: 2 条记录
2025-07-03 16:57:05,583 - server5-2.timeprotab_manager - INFO - 📅 分区 timeprotab_2507 已存在
2025-07-03 16:57:05,586 - server5-2.timeprotab_manager - INFO - 📅 分区 timeprotab_2507 已存在
2025-07-03 16:57:05,589 - server5-2.timeprotab_manager - INFO - 📅 分区 timeprotab_2507 已存在
2025-07-03 16:57:05,592 - server5-2.timeprotab_manager - INFO - ✅ 批量插入完成: 2 条记录
2025-07-03 16:57:05,592 - server5-2.timepro_collector - INFO - ✅ 数据插入成功: 2 条记录
2025-07-03 16:57:15,602 - server5-2.timepro_collector - INFO - ✅ 指定月份数据采集完成: 215829
2025-07-03 16:57:15,602 - server5-2.test - INFO - ✅ 实际用户真实采集测试成功
2025-07-03 16:57:15,605 - server5-2.timeprotab_manager - INFO - 📊 查询到 7 条记录: 215829 - 2506
2025-07-03 16:57:15,605 - server5-2.test - INFO - ✅ 验证采集数据成功 (2506): 7 条记录
2025-07-03 16:57:15,606 - server5-2.test - INFO - 📊 采集记录: 215829 - 2025-06-01 - 测试数据 - 实际用户215829 - 6月
2025-07-03 16:57:15,606 - server5-2.test - INFO - 📊 采集记录: 215829 - 2025-06-01 - XML解析测试 - 6月
2025-07-03 16:57:15,606 - server5-2.test - INFO - 📊 采集记录: 215829 - 2025-06-01 - 测试数据 - 实际用户215829 - 6月
2025-07-03 16:57:15,608 - server5-2.timeprotab_manager - INFO - 📊 查询到 24 条记录: 215829 - 2507
2025-07-03 16:57:15,609 - server5-2.test - INFO - ✅ 验证采集数据成功 (2507): 24 条记录
2025-07-03 16:57:15,609 - server5-2.test - INFO - 📊 采集记录: 215829 - 2025-07-01 - 2025/07/03 + 16:25 + 测试数据 - 实际用户215829
2025-07-03 16:57:15,609 - server5-2.test - INFO - 📊 采集记录: 215829 - 2025-07-01 - 测试数据 - 实际用户215829 - 7月
2025-07-03 16:57:15,609 - server5-2.test - INFO - 📊 采集记录: 215829 - 2025-07-01 - XML解析测试 - 7月
2025-07-03 16:57:15,612 - server5-2.timeprotab_manager - INFO - 🔌 timeprotab数据库连接池已关闭
2025-07-03 16:57:15,612 - server5-2.timepro_collector - INFO - 🔌 timepro采集服务已停止
2025-07-03 16:57:15,612 - server5-2.test - INFO - ✅ 测试通过: 实际用户真实采集
2025-07-03 16:57:16,613 - server5-2.test - INFO - 🔍 运行测试: 指定月份真实采集
2025-07-03 16:57:16,613 - server5-2.test - INFO - 🧪 测试指定月份的真实数据采集
2025-07-03 16:57:16,613 - server5-2.test - INFO - 🔐 测试用户: 215829
2025-07-03 16:57:16,613 - server5-2.test - INFO - 🔑 测试密码: jiaban01
2025-07-03 16:57:16,831 - server5-2.timeprotab_manager - INFO - ✅ timeprotab数据库连接池创建成功
2025-07-03 16:57:16,845 - server5-2.timeprotab_manager - INFO - ✅ 基础分区表 timeprotab 初始化完成
2025-07-03 16:57:16,846 - server5-2.timepro_collector - INFO - 📅 定时任务已设置: 每天 1:00 开始采集
2025-07-03 16:57:16,846 - server5-2.timepro_collector - INFO - ⏰ 调度器线程启动
2025-07-03 16:57:16,846 - server5-2.timepro_collector - INFO - ✅ timepro采集服务启动成功
2025-07-03 16:57:16,846 - server5-2.test - INFO - 📅 开始采集指定月份: 202506
2025-07-03 16:57:16,846 - server5-2.timepro_collector - INFO - 📅 采集 215829 的 202506 数据
2025-07-03 16:57:16,846 - server5-2.timepro_collector - INFO - 🌐 真实调用timepro网站: user_id=215829
2025-07-03 16:57:18,075 - server5-2.timepro_collector - INFO - ✅ timepro数据获取成功: Successfully extracted 202506 data to data/html_files/202506_body_content.html
2025-07-03 16:57:18,075 - server5-2.timepro_collector - INFO - 📁 找到生成的HTML文件: ['202506_body_content.html']
2025-07-03 16:57:18,075 - server5-2.timepro_collector - INFO - ✅ HTML数据保存成功: data/html_files/215829_202506.html
2025-07-03 16:57:18,075 - server5-2.timepro_collector - INFO - 🔄 真实HTML转XML: data/html_files/215829_202506.html -> data/xml_files/215829_202506.xml
2025-07-03 16:57:18,076 - server5-2.timepro_collector - ERROR - ❌ HTML转XML失败: 未生成XML内容
2025-07-03 16:57:18,076 - server5-2.timepro_collector - ERROR - ❌ HTML转XML失败: HTML转XML失败
2025-07-03 16:57:18,076 - server5-2.timepro_collector - INFO - 🔄 使用模拟XML数据作为备选
2025-07-03 16:57:18,076 - server5-2.timepro_collector - INFO - 📝 生成模拟XML数据: data/xml_files/215829_202506.xml
2025-07-03 16:57:18,076 - server5-2.timepro_collector - INFO - ✅ XML解析完成: 2 条记录
2025-07-03 16:57:18,084 - server5-2.timeprotab_manager - INFO - 📅 分区 timeprotab_2506 已存在
2025-07-03 16:57:18,093 - server5-2.timeprotab_manager - INFO - 📅 分区 timeprotab_2507 已存在
2025-07-03 16:57:18,099 - server5-2.timeprotab_manager - INFO - 📅 分区 timeprotab_2507 已存在
2025-07-03 16:57:18,101 - server5-2.timeprotab_manager - INFO - ✅ 批量插入完成: 2 条记录
2025-07-03 16:57:18,102 - server5-2.timepro_collector - INFO - ✅ 数据插入成功: 2 条记录
2025-07-03 16:57:18,102 - server5-2.test - INFO - ✅ 指定月份真实采集测试成功
2025-07-03 16:57:18,104 - server5-2.timeprotab_manager - INFO - 📊 查询到 7 条记录: 215829 - 2506
2025-07-03 16:57:18,105 - server5-2.test - INFO - ✅ 验证采集数据成功 (2506): 7 条记录
2025-07-03 16:57:18,105 - server5-2.test - INFO - 📊 采集记录: 215829 - 2025-06-01 - 测试数据 - 实际用户215829 - 6月
2025-07-03 16:57:18,105 - server5-2.test - INFO - 📊 采集记录: 215829 - 2025-06-01 - XML解析测试 - 6月
2025-07-03 16:57:18,105 - server5-2.test - INFO - 📊 采集记录: 215829 - 2025-06-01 - 测试数据 - 实际用户215829 - 6月
2025-07-03 16:57:18,105 - server5-2.test - INFO - 📊 采集记录: 215829 - 2025-06-01 - XML解析测试 - 6月
2025-07-03 16:57:18,105 - server5-2.test - INFO - 📊 采集记录: 215829 - 2025-06-01 - 测试数据 - 实际用户215829 - 6月
2025-07-03 16:57:18,109 - server5-2.timeprotab_manager - INFO - 🔌 timeprotab数据库连接池已关闭
2025-07-03 16:57:18,109 - server5-2.timepro_collector - INFO - 🔌 timepro采集服务已停止
2025-07-03 16:57:18,109 - server5-2.test - INFO - ✅ 测试通过: 指定月份真实采集
2025-07-03 16:57:19,110 - server5-2.test - INFO - ============================================================
2025-07-03 16:57:19,110 - server5-2.test - INFO - 📊 测试结果汇总:
2025-07-03 16:57:19,110 - server5-2.test - INFO -   数据库操作: ✅ PASS
2025-07-03 16:57:19,110 - server5-2.test - INFO -   分区管理: ✅ PASS
2025-07-03 16:57:19,110 - server5-2.test - INFO -   XML解析: ✅ PASS
2025-07-03 16:57:19,110 - server5-2.test - INFO -   真实采集工作流: ✅ PASS
2025-07-03 16:57:19,110 - server5-2.test - INFO -   实际用户真实采集: ✅ PASS
2025-07-03 16:57:19,110 - server5-2.test - INFO -   指定月份真实采集: ✅ PASS
2025-07-03 16:57:19,110 - server5-2.test - INFO - 总计: 6/6 测试通过
2025-07-03 16:57:19,110 - server5-2.test - INFO - ============================================================
2025-07-03 16:57:19,114 - server5-2.timeprotab_manager - INFO - 🔌 timeprotab数据库连接池已关闭
2025-07-03 16:57:19,114 - server5-2.test - INFO - ✅ 测试环境清理完成
2025-07-03 16:57:19,114 - server5-2.test - INFO - 🎉 所有测试通过！
2025-07-03 16:57:50,211 - server5-2.collect_month - INFO - 🧪 指定月份数据采集工具启动
2025-07-03 16:57:50,211 - server5-2.collect_month - INFO - ============================================================
2025-07-03 16:57:50,211 - server5-2.collect_month - INFO - 🔐 用户: 215829
2025-07-03 16:57:50,211 - server5-2.collect_month - INFO - 📅 目标: 2025年6月
2025-07-03 16:57:50,211 - server5-2.collect_month - INFO - ============================================================
2025-07-03 16:57:50,211 - server5-2.collect_month - INFO - 🧪 设置采集环境
2025-07-03 16:57:50,445 - server5-2.timeprotab_manager - INFO - ✅ timeprotab数据库连接池创建成功
2025-07-03 16:57:50,463 - server5-2.timeprotab_manager - INFO - ✅ 基础分区表 timeprotab 初始化完成
2025-07-03 16:57:50,464 - server5-2.collect_month - INFO - ✅ 采集环境设置完成
2025-07-03 16:57:50,464 - server5-2.collect_month - INFO - 📅 开始采集 2025年6月 的数据
2025-07-03 16:57:50,695 - server5-2.timeprotab_manager - INFO - ✅ timeprotab数据库连接池创建成功
2025-07-03 16:57:50,709 - server5-2.timeprotab_manager - INFO - ✅ 基础分区表 timeprotab 初始化完成
2025-07-03 16:57:50,710 - server5-2.timepro_collector - INFO - 📅 定时任务已设置: 每天 1:00 开始采集
2025-07-03 16:57:50,710 - server5-2.timepro_collector - INFO - ⏰ 调度器线程启动
2025-07-03 16:57:50,710 - server5-2.timepro_collector - INFO - ✅ timepro采集服务启动成功
2025-07-03 16:57:50,710 - server5-2.collect_month - INFO - 🔐 用户: 215829
2025-07-03 16:57:50,710 - server5-2.collect_month - INFO - 📅 目标月份: 202506
2025-07-03 16:57:50,710 - server5-2.timepro_collector - INFO - 📅 采集 215829 的 202506 数据
2025-07-03 16:57:50,710 - server5-2.timepro_collector - INFO - 🌐 真实调用timepro网站: user_id=215829
2025-07-03 16:57:51,974 - server5-2.timepro_collector - INFO - ✅ timepro数据获取成功: Successfully extracted 202506 data to data/html_files/202506_body_content.html
2025-07-03 16:57:51,974 - server5-2.timepro_collector - INFO - 📁 找到生成的HTML文件: ['202506_body_content.html']
2025-07-03 16:57:51,974 - server5-2.timepro_collector - INFO - ✅ HTML数据保存成功: data/html_files/215829_202506.html
2025-07-03 16:57:51,974 - server5-2.timepro_collector - INFO - 🔄 真实HTML转XML: data/html_files/215829_202506.html -> data/xml_files/215829_202506.xml
2025-07-03 16:57:51,975 - server5-2.timepro_collector - ERROR - ❌ HTML转XML失败: 未生成XML内容
2025-07-03 16:57:51,975 - server5-2.timepro_collector - ERROR - ❌ HTML转XML失败: HTML转XML失败
2025-07-03 16:57:51,975 - server5-2.timepro_collector - INFO - 🔄 使用模拟XML数据作为备选
2025-07-03 16:57:51,975 - server5-2.timepro_collector - INFO - 📝 生成模拟XML数据: data/xml_files/215829_202506.xml
2025-07-03 16:57:51,977 - server5-2.timepro_collector - INFO - ✅ XML解析完成: 2 条记录
2025-07-03 16:57:51,986 - server5-2.timeprotab_manager - INFO - 📅 分区 timeprotab_2506 已存在
2025-07-03 16:57:51,995 - server5-2.timeprotab_manager - INFO - 📅 分区 timeprotab_2507 已存在
2025-07-03 16:57:52,000 - server5-2.timeprotab_manager - INFO - 📅 分区 timeprotab_2507 已存在
2025-07-03 16:57:52,004 - server5-2.timeprotab_manager - INFO - ✅ 批量插入完成: 2 条记录
2025-07-03 16:57:52,004 - server5-2.timepro_collector - INFO - ✅ 数据插入成功: 2 条记录
2025-07-03 16:57:52,004 - server5-2.collect_month - INFO - ✅ 数据采集完成
2025-07-03 16:57:52,009 - server5-2.timeprotab_manager - INFO - 📊 查询到 7 条记录: 215829 - 2506
2025-07-03 16:57:52,010 - server5-2.collect_month - INFO - ✅ 验证采集数据成功 (2506): 7 条记录
2025-07-03 16:57:52,010 - server5-2.collect_month - INFO - 📊 采集记录预览:
2025-07-03 16:57:52,010 - server5-2.collect_month - INFO -   1. 215829 - 2025-06-01 - 08:30:00 - 17:30:00 - 测试数据 - 实际用户215829 - 6月
2025-07-03 16:57:52,010 - server5-2.collect_month - INFO -   2. 215829 - 2025-06-01 - 08:30:00 - 17:30:00 - XML解析测试 - 6月
2025-07-03 16:57:52,010 - server5-2.collect_month - INFO -   3. 215829 - 2025-06-01 - 08:30:00 - 17:30:00 - 测试数据 - 实际用户215829 - 6月
2025-07-03 16:57:52,010 - server5-2.collect_month - INFO -   4. 215829 - 2025-06-01 - 08:30:00 - 17:30:00 - XML解析测试 - 6月
2025-07-03 16:57:52,010 - server5-2.collect_month - INFO -   5. 215829 - 2025-06-01 - 08:30:00 - 17:30:00 - 测试数据 - 实际用户215829 - 6月
2025-07-03 16:57:52,010 - server5-2.collect_month - INFO -   6. 215829 - 2025-06-01 - 08:30:00 - 17:30:00 - XML解析测试 - 6月
2025-07-03 16:57:52,010 - server5-2.collect_month - INFO -   7. 215829 - 2025-06-30 - 08:00:00 - 17:00:00 - 2025/07/03 + 16:25 + 上个月测试数据 - 实际用户215829
2025-07-03 16:57:52,014 - server5-2.timeprotab_manager - INFO - 🔌 timeprotab数据库连接池已关闭
2025-07-03 16:57:52,014 - server5-2.timepro_collector - INFO - 🔌 timepro采集服务已停止
2025-07-03 16:57:52,014 - server5-2.collect_month - INFO - 🎉 数据采集成功完成！
2025-07-03 16:57:52,018 - server5-2.timeprotab_manager - INFO - 🔌 timeprotab数据库连接池已关闭
2025-07-03 16:57:52,018 - server5-2.collect_month - INFO - ✅ 采集环境清理完成
2025-07-03 16:59:23,444 - server5-2.collect_month - INFO - 🧪 指定月份数据采集工具启动
2025-07-03 16:59:23,444 - server5-2.collect_month - INFO - ============================================================
2025-07-03 16:59:23,444 - server5-2.collect_month - INFO - 🔐 用户: 215829
2025-07-03 16:59:23,444 - server5-2.collect_month - INFO - 📅 目标: 2025年6月
2025-07-03 16:59:23,444 - server5-2.collect_month - INFO - ============================================================
2025-07-03 16:59:23,444 - server5-2.collect_month - INFO - 🧪 设置采集环境
2025-07-03 16:59:23,661 - server5-2.timeprotab_manager - INFO - ✅ timeprotab数据库连接池创建成功
2025-07-03 16:59:23,675 - server5-2.timeprotab_manager - INFO - ✅ 基础分区表 timeprotab 初始化完成
2025-07-03 16:59:23,676 - server5-2.collect_month - INFO - ✅ 采集环境设置完成
2025-07-03 16:59:23,676 - server5-2.collect_month - INFO - 📅 开始采集 2025年6月 的数据
2025-07-03 16:59:23,895 - server5-2.timeprotab_manager - INFO - ✅ timeprotab数据库连接池创建成功
2025-07-03 16:59:23,909 - server5-2.timeprotab_manager - INFO - ✅ 基础分区表 timeprotab 初始化完成
2025-07-03 16:59:23,910 - server5-2.timepro_collector - INFO - 📅 定时任务已设置: 每天 1:00 开始采集
2025-07-03 16:59:23,910 - server5-2.timepro_collector - INFO - ⏰ 调度器线程启动
2025-07-03 16:59:23,910 - server5-2.timepro_collector - INFO - ✅ timepro采集服务启动成功
2025-07-03 16:59:23,911 - server5-2.collect_month - INFO - 🔐 用户: 215829
2025-07-03 16:59:23,911 - server5-2.collect_month - INFO - 📅 目标月份: 202506
2025-07-03 16:59:23,911 - server5-2.timepro_collector - INFO - 📅 采集 215829 的 202506 数据
2025-07-03 16:59:23,911 - server5-2.timepro_collector - INFO - 🌐 真实调用timepro网站: user_id=215829
2025-07-03 16:59:25,114 - server5-2.timepro_collector - INFO - ✅ timepro数据获取成功: Successfully extracted 202506 data to data/html_files/202506_body_content.html
2025-07-03 16:59:25,115 - server5-2.timepro_collector - INFO - 📁 找到生成的HTML文件: ['202506_body_content.html']
2025-07-03 16:59:25,115 - server5-2.timepro_collector - INFO - ✅ HTML数据保存成功: data/html_files/215829_202506.html
2025-07-03 16:59:25,115 - server5-2.timepro_collector - INFO - 🔄 真实HTML转XML: data/html_files/215829_202506.html -> data/xml_files/215829_202506.xml
2025-07-03 16:59:25,116 - server5-2.timepro_collector - ERROR - ❌ HTML转XML失败: 未生成XML内容
2025-07-03 16:59:25,116 - server5-2.timepro_collector - ERROR - ❌ HTML转XML失败: HTML转XML失败
2025-07-03 16:59:25,116 - server5-2.timepro_collector - ERROR - ❌ 用户月份数据采集失败: HTML转XML失败
2025-07-03 16:59:25,116 - server5-2.collect_month - ERROR - ❌ 数据采集失败: HTML转XML失败
2025-07-03 16:59:25,120 - server5-2.timeprotab_manager - INFO - 🔌 timeprotab数据库连接池已关闭
2025-07-03 16:59:25,120 - server5-2.timepro_collector - INFO - 🔌 timepro采集服务已停止
2025-07-03 16:59:25,120 - server5-2.collect_month - ERROR - ❌ 数据采集失败
2025-07-03 16:59:25,126 - server5-2.timeprotab_manager - INFO - 🔌 timeprotab数据库连接池已关闭
2025-07-03 16:59:25,126 - server5-2.collect_month - INFO - ✅ 采集环境清理完成
2025-07-03 17:14:20,495 - server5-2.test - INFO - 🧪 server5-2 测试工具启动 (真实、无文件采集)
2025-07-03 17:14:20,495 - server5-2.test - INFO - ============================================================
2025-07-03 17:14:20,495 - server5-2.test - INFO - 🎯 开始运行所有测试 (无文件内存流)
2025-07-03 17:14:20,495 - server5-2.test - INFO - 🧪 设置测试环境
2025-07-03 17:14:20,716 - server5-2.timeprotab_manager - INFO - ✅ timeprotab数据库连接池创建成功
2025-07-03 17:14:20,731 - server5-2.timeprotab_manager - INFO - ✅ 基础分区表 timeprotab 初始化完成
2025-07-03 17:14:20,733 - server5-2.test - INFO - ✅ 测试环境设置完成
2025-07-03 17:14:20,733 - server5-2.test - INFO - 
==================== 数据库连接 ====================
2025-07-03 17:14:20,733 - server5-2.test - INFO - 🧪 (1/3) 测试数据库连接
2025-07-03 17:14:20,746 - server5-2.test - INFO - ✅ 数据库连接和状态获取测试成功
2025-07-03 17:14:21,747 - server5-2.test - INFO - 
==================== 采集器初始化 ====================
2025-07-03 17:14:21,747 - server5-2.test - INFO - 🧪 (2/3) 测试采集器初始化和关闭
2025-07-03 17:14:21,985 - server5-2.timeprotab_manager - INFO - ✅ timeprotab数据库连接池创建成功
2025-07-03 17:14:22,000 - server5-2.timeprotab_manager - INFO - ✅ 基础分区表 timeprotab 初始化完成
2025-07-03 17:14:22,001 - server5-2.timepro_collector - INFO - ✅ timepro采集服务启动成功
2025-07-03 17:14:22,001 - server5-2.timepro_collector - INFO - 📅 定时任务已设置: 每天 01:00 开始采集
2025-07-03 17:14:22,001 - server5-2.timepro_collector - INFO - ⏰ 调度器线程启动
2025-07-03 17:14:22,002 - server5-2.test - INFO - ✅ 采集器启动成功
2025-07-03 17:14:22,005 - server5-2.timeprotab_manager - INFO - 🔌 timeprotab数据库连接池已关闭
2025-07-03 17:14:22,005 - server5-2.timepro_collector - INFO - 🔌 timepro采集服务已停止
2025-07-03 17:14:22,005 - server5-2.test - INFO - ✅ 采集器停止成功
2025-07-03 17:14:23,006 - server5-2.test - INFO - 
==================== 核心功能: 采集2025年6月真实数据 ====================
2025-07-03 17:14:23,006 - server5-2.test - INFO - 🧪 (3/3) 核心测试: 采集2025年6月的真实数据
2025-07-03 17:14:23,006 - server5-2.test - INFO - 🔐 测试用户: 215829
2025-07-03 17:14:23,006 - server5-2.test - INFO - 📅 开始采集指定月份: 202506
2025-07-03 17:14:23,006 - server5-2.timepro_collector - INFO - 📅 开始为用户 '215829' 采集 202506 的数据
2025-07-03 17:14:23,007 - server5-2.timepro_collector - INFO - 🌐 正在调用timepro.py获取 202506 的数据, 用户: 215829
2025-07-03 17:14:23,007 - server5-2.timepro_collector - ERROR - ❌ 调用timepro.py时发生异常: module 'timepro' has no attribute 'get_timepro_data'
Traceback (most recent call last):
  File "/home/<USER>/Documents/testF/2025/ser25/server0613/MySuiteDatebase2402newuigood-yolo5-3/server5-2/services/timepro_collector.py", line 213, in _fetch_timepro_data_async
    timepro.get_timepro_data,
    ^^^^^^^^^^^^^^^^^^^^^^^^
AttributeError: module 'timepro' has no attribute 'get_timepro_data'. Did you mean: 'fetch_timepro_data'?
2025-07-03 17:14:23,008 - server5-2.timepro_collector - ERROR - ❌ 用户 '215829' (202506) 的采集失败: 未能从网站获取HTML内容。
Traceback (most recent call last):
  File "/home/<USER>/Documents/testF/2025/ser25/server0613/MySuiteDatebase2402newuigood-yolo5-3/server5-2/services/timepro_collector.py", line 128, in _collect_user_month_data
    raise ValueError("未能从网站获取HTML内容。")
ValueError: 未能从网站获取HTML内容。
2025-07-03 17:14:23,008 - server5-2.test - ERROR - ❌ 指定月份真实采集测试失败: 未能从网站获取HTML内容。
Traceback (most recent call last):
  File "/home/<USER>/Documents/testF/2025/ser25/server0613/MySuiteDatebase2402newuigood-yolo5-3/server5-2/test_server5_2.py", line 123, in test_specific_month_real_collection
    await self.collector._collect_user_month_data(
  File "/home/<USER>/Documents/testF/2025/ser25/server0613/MySuiteDatebase2402newuigood-yolo5-3/server5-2/services/timepro_collector.py", line 128, in _collect_user_month_data
    raise ValueError("未能从网站获取HTML内容。")
ValueError: 未能从网站获取HTML内容。
2025-07-03 17:14:23,009 - server5-2.test - ERROR - ❌ 测试失败: 核心功能: 采集2025年6月真实数据。后续测试可能受影响。
2025-07-03 17:14:24,010 - server5-2.test - INFO - 
==================================================
2025-07-03 17:14:24,010 - server5-2.test - INFO - 📊 测试结果汇总:
2025-07-03 17:14:24,010 - server5-2.test - INFO -   - 数据库连接: ✅ 通过
2025-07-03 17:14:24,010 - server5-2.test - INFO -   - 采集器初始化: ✅ 通过
2025-07-03 17:14:24,010 - server5-2.test - INFO -   - 核心功能: 采集2025年6月真实数据: ❌ 失败
2025-07-03 17:14:24,010 - server5-2.test - INFO - 总计: 2/3 测试通过
2025-07-03 17:14:24,010 - server5-2.test - INFO - ==================================================
2025-07-03 17:14:24,010 - server5-2.timeprotab_manager - INFO - 🔌 timeprotab数据库连接池已关闭
2025-07-03 17:14:24,010 - server5-2.test - INFO - ✅ 测试环境清理完成
2025-07-03 17:14:24,010 - server5-2.test - ERROR - ❌ 部分或全部测试失败。
2025-07-03 17:14:46,337 - server5-2.test - INFO - 🧪 server5-2 测试工具启动 (真实、无文件采集)
2025-07-03 17:14:46,337 - server5-2.test - INFO - ============================================================
2025-07-03 17:14:46,337 - server5-2.test - INFO - 🎯 开始运行所有测试 (无文件内存流)
2025-07-03 17:14:46,338 - server5-2.test - INFO - 🧪 设置测试环境
2025-07-03 17:14:46,594 - server5-2.timeprotab_manager - INFO - ✅ timeprotab数据库连接池创建成功
2025-07-03 17:14:46,608 - server5-2.timeprotab_manager - INFO - ✅ 基础分区表 timeprotab 初始化完成
2025-07-03 17:14:46,609 - server5-2.test - INFO - ✅ 测试环境设置完成
2025-07-03 17:14:46,609 - server5-2.test - INFO - 
==================== 数据库连接 ====================
2025-07-03 17:14:46,609 - server5-2.test - INFO - 🧪 (1/3) 测试数据库连接
2025-07-03 17:14:46,620 - server5-2.test - INFO - ✅ 数据库连接和状态获取测试成功
2025-07-03 17:14:47,621 - server5-2.test - INFO - 
==================== 采集器初始化 ====================
2025-07-03 17:14:47,621 - server5-2.test - INFO - 🧪 (2/3) 测试采集器初始化和关闭
2025-07-03 17:14:47,855 - server5-2.timeprotab_manager - INFO - ✅ timeprotab数据库连接池创建成功
2025-07-03 17:14:47,871 - server5-2.timeprotab_manager - INFO - ✅ 基础分区表 timeprotab 初始化完成
2025-07-03 17:14:47,872 - server5-2.timepro_collector - INFO - ✅ timepro采集服务启动成功
2025-07-03 17:14:47,872 - server5-2.timepro_collector - INFO - 📅 定时任务已设置: 每天 01:00 开始采集
2025-07-03 17:14:47,872 - server5-2.timepro_collector - INFO - ⏰ 调度器线程启动
2025-07-03 17:14:47,872 - server5-2.test - INFO - ✅ 采集器启动成功
2025-07-03 17:14:47,875 - server5-2.timeprotab_manager - INFO - 🔌 timeprotab数据库连接池已关闭
2025-07-03 17:14:47,876 - server5-2.timepro_collector - INFO - 🔌 timepro采集服务已停止
2025-07-03 17:14:47,876 - server5-2.test - INFO - ✅ 采集器停止成功
2025-07-03 17:14:48,877 - server5-2.test - INFO - 
==================== 核心功能: 采集2025年6月真实数据 ====================
2025-07-03 17:14:48,877 - server5-2.test - INFO - 🧪 (3/3) 核心测试: 采集2025年6月的真实数据
2025-07-03 17:14:48,877 - server5-2.test - INFO - 🔐 测试用户: 215829
2025-07-03 17:14:48,877 - server5-2.test - INFO - 📅 开始采集指定月份: 202506
2025-07-03 17:14:48,877 - server5-2.timepro_collector - INFO - 📅 开始为用户 '215829' 采集 202506 的数据
2025-07-03 17:14:48,877 - server5-2.timepro_collector - INFO - 🌐 正在调用timepro.py获取 202506 的数据, 用户: 215829
2025-07-03 17:14:48,878 - server5-2.timepro_collector - ERROR - ❌ 调用timepro.py时发生异常: fetch_timepro_data() takes 3 positional arguments but 5 were given
Traceback (most recent call last):
  File "/home/<USER>/Documents/testF/2025/ser25/server0613/MySuiteDatebase2402newuigood-yolo5-3/server5-2/services/timepro_collector.py", line 215, in _fetch_timepro_data_async
    html_content = await loop.run_in_executor(
                   ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/miniconda3/envs/my_suite_unified/lib/python3.12/concurrent/futures/thread.py", line 59, in run
    result = self.fn(*self.args, **self.kwargs)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
TypeError: fetch_timepro_data() takes 3 positional arguments but 5 were given
2025-07-03 17:14:48,894 - server5-2.timepro_collector - ERROR - ❌ 用户 '215829' (202506) 的采集失败: 未能从网站获取HTML内容。
Traceback (most recent call last):
  File "/home/<USER>/Documents/testF/2025/ser25/server0613/MySuiteDatebase2402newuigood-yolo5-3/server5-2/services/timepro_collector.py", line 132, in _collect_user_month_data
    raise ValueError("未能从网站获取HTML内容。")
ValueError: 未能从网站获取HTML内容。
2025-07-03 17:14:48,894 - server5-2.test - ERROR - ❌ 指定月份真实采集测试失败: 未能从网站获取HTML内容。
Traceback (most recent call last):
  File "/home/<USER>/Documents/testF/2025/ser25/server0613/MySuiteDatebase2402newuigood-yolo5-3/server5-2/test_server5_2.py", line 123, in test_specific_month_real_collection
    await self.collector._collect_user_month_data(
  File "/home/<USER>/Documents/testF/2025/ser25/server0613/MySuiteDatebase2402newuigood-yolo5-3/server5-2/services/timepro_collector.py", line 132, in _collect_user_month_data
    raise ValueError("未能从网站获取HTML内容。")
ValueError: 未能从网站获取HTML内容。
2025-07-03 17:14:48,894 - server5-2.test - ERROR - ❌ 测试失败: 核心功能: 采集2025年6月真实数据。后续测试可能受影响。
2025-07-03 17:14:49,895 - server5-2.test - INFO - 
==================================================
2025-07-03 17:14:49,895 - server5-2.test - INFO - 📊 测试结果汇总:
2025-07-03 17:14:49,895 - server5-2.test - INFO -   - 数据库连接: ✅ 通过
2025-07-03 17:14:49,895 - server5-2.test - INFO -   - 采集器初始化: ✅ 通过
2025-07-03 17:14:49,895 - server5-2.test - INFO -   - 核心功能: 采集2025年6月真实数据: ❌ 失败
2025-07-03 17:14:49,895 - server5-2.test - INFO - 总计: 2/3 测试通过
2025-07-03 17:14:49,895 - server5-2.test - INFO - ==================================================
2025-07-03 17:14:49,895 - server5-2.timeprotab_manager - INFO - 🔌 timeprotab数据库连接池已关闭
2025-07-03 17:14:49,895 - server5-2.test - INFO - ✅ 测试环境清理完成
2025-07-03 17:14:49,895 - server5-2.test - ERROR - ❌ 部分或全部测试失败。
2025-07-03 17:15:37,357 - server5-2.test - INFO - 🧪 server5-2 测试工具启动 (真实、无文件采集)
2025-07-03 17:15:37,357 - server5-2.test - INFO - ============================================================
2025-07-03 17:15:37,357 - server5-2.test - INFO - 🎯 开始运行所有测试 (无文件内存流)
2025-07-03 17:15:37,357 - server5-2.test - INFO - 🧪 设置测试环境
2025-07-03 17:15:37,589 - server5-2.timeprotab_manager - INFO - ✅ timeprotab数据库连接池创建成功
2025-07-03 17:15:37,604 - server5-2.timeprotab_manager - INFO - ✅ 基础分区表 timeprotab 初始化完成
2025-07-03 17:15:37,605 - server5-2.test - INFO - ✅ 测试环境设置完成
2025-07-03 17:15:37,605 - server5-2.test - INFO - 
==================== 数据库连接 ====================
2025-07-03 17:15:37,605 - server5-2.test - INFO - 🧪 (1/3) 测试数据库连接
2025-07-03 17:15:37,618 - server5-2.test - INFO - ✅ 数据库连接和状态获取测试成功
2025-07-03 17:15:38,619 - server5-2.test - INFO - 
==================== 采集器初始化 ====================
2025-07-03 17:15:38,619 - server5-2.test - INFO - 🧪 (2/3) 测试采集器初始化和关闭
2025-07-03 17:15:38,839 - server5-2.timeprotab_manager - INFO - ✅ timeprotab数据库连接池创建成功
2025-07-03 17:15:38,854 - server5-2.timeprotab_manager - INFO - ✅ 基础分区表 timeprotab 初始化完成
2025-07-03 17:15:38,855 - server5-2.timepro_collector - INFO - ✅ timepro采集服务启动成功
2025-07-03 17:15:38,855 - server5-2.timepro_collector - INFO - 📅 定时任务已设置: 每天 01:00 开始采集
2025-07-03 17:15:38,855 - server5-2.timepro_collector - INFO - ⏰ 调度器线程启动
2025-07-03 17:15:38,855 - server5-2.test - INFO - ✅ 采集器启动成功
2025-07-03 17:15:38,859 - server5-2.timeprotab_manager - INFO - 🔌 timeprotab数据库连接池已关闭
2025-07-03 17:15:38,859 - server5-2.timepro_collector - INFO - 🔌 timepro采集服务已停止
2025-07-03 17:15:38,859 - server5-2.test - INFO - ✅ 采集器停止成功
2025-07-03 17:15:39,860 - server5-2.test - INFO - 
==================== 核心功能: 采集2025年6月真实数据 ====================
2025-07-03 17:15:39,860 - server5-2.test - INFO - 🧪 (3/3) 核心测试: 采集2025年6月的真实数据
2025-07-03 17:15:39,860 - server5-2.test - INFO - 🔐 测试用户: 215829
2025-07-03 17:15:39,860 - server5-2.test - INFO - 📅 开始采集指定月份: 202506
2025-07-03 17:15:39,860 - server5-2.timepro_collector - INFO - 📅 开始为用户 '215829' 采集 202506 的数据
2025-07-03 17:15:39,860 - server5-2.timepro_collector - INFO - 🌐 正在调用timepro.py获取 202506 的数据, 用户: 215829
2025-07-03 17:15:39,876 - server5-2.timepro_collector - ERROR - ❌ 调用timepro.py时发生异常: HTTPConnectionPool(host='timepro.gongsi.co.jp', port=80): Max retries exceeded with url: /xgweb/login.asp (Caused by NameResolutionError("<urllib3.connection.HTTPConnection object at 0x7bef97d58c80>: Failed to resolve 'timepro.gongsi.co.jp' ([Errno -2] Name or service not known)"))
Traceback (most recent call last):
  File "/home/<USER>/miniconda3/envs/my_suite_unified/lib/python3.12/site-packages/urllib3/connection.py", line 198, in _new_conn
    sock = connection.create_connection(
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/miniconda3/envs/my_suite_unified/lib/python3.12/site-packages/urllib3/util/connection.py", line 60, in create_connection
    for res in socket.getaddrinfo(host, port, family, socket.SOCK_STREAM):
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/miniconda3/envs/my_suite_unified/lib/python3.12/socket.py", line 978, in getaddrinfo
    for res in _socket.getaddrinfo(host, port, family, type, proto, flags):
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
socket.gaierror: [Errno -2] Name or service not known

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "/home/<USER>/miniconda3/envs/my_suite_unified/lib/python3.12/site-packages/urllib3/connectionpool.py", line 787, in urlopen
    response = self._make_request(
               ^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/miniconda3/envs/my_suite_unified/lib/python3.12/site-packages/urllib3/connectionpool.py", line 493, in _make_request
    conn.request(
  File "/home/<USER>/miniconda3/envs/my_suite_unified/lib/python3.12/site-packages/urllib3/connection.py", line 494, in request
    self.endheaders()
  File "/home/<USER>/miniconda3/envs/my_suite_unified/lib/python3.12/http/client.py", line 1333, in endheaders
    self._send_output(message_body, encode_chunked=encode_chunked)
  File "/home/<USER>/miniconda3/envs/my_suite_unified/lib/python3.12/http/client.py", line 1093, in _send_output
    self.send(msg)
  File "/home/<USER>/miniconda3/envs/my_suite_unified/lib/python3.12/http/client.py", line 1037, in send
    self.connect()
  File "/home/<USER>/miniconda3/envs/my_suite_unified/lib/python3.12/site-packages/urllib3/connection.py", line 325, in connect
    self.sock = self._new_conn()
                ^^^^^^^^^^^^^^^^
  File "/home/<USER>/miniconda3/envs/my_suite_unified/lib/python3.12/site-packages/urllib3/connection.py", line 205, in _new_conn
    raise NameResolutionError(self.host, self, e) from e
urllib3.exceptions.NameResolutionError: <urllib3.connection.HTTPConnection object at 0x7bef97d58c80>: Failed to resolve 'timepro.gongsi.co.jp' ([Errno -2] Name or service not known)

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "/home/<USER>/miniconda3/envs/my_suite_unified/lib/python3.12/site-packages/requests/adapters.py", line 667, in send
    resp = conn.urlopen(
           ^^^^^^^^^^^^^
  File "/home/<USER>/miniconda3/envs/my_suite_unified/lib/python3.12/site-packages/urllib3/connectionpool.py", line 841, in urlopen
    retries = retries.increment(
              ^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/miniconda3/envs/my_suite_unified/lib/python3.12/site-packages/urllib3/util/retry.py", line 519, in increment
    raise MaxRetryError(_pool, url, reason) from reason  # type: ignore[arg-type]
    ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
urllib3.exceptions.MaxRetryError: HTTPConnectionPool(host='timepro.gongsi.co.jp', port=80): Max retries exceeded with url: /xgweb/login.asp (Caused by NameResolutionError("<urllib3.connection.HTTPConnection object at 0x7bef97d58c80>: Failed to resolve 'timepro.gongsi.co.jp' ([Errno -2] Name or service not known)"))

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "/home/<USER>/Documents/testF/2025/ser25/server0613/MySuiteDatebase2402newuigood-yolo5-3/server5-2/services/timepro_collector.py", line 215, in _fetch_timepro_data_async
    html_content = await loop.run_in_executor(
                   ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/miniconda3/envs/my_suite_unified/lib/python3.12/concurrent/futures/thread.py", line 59, in run
    result = self.fn(*self.args, **self.kwargs)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Documents/testF/2025/ser25/server0613/MySuiteDatebase2402newuigood-yolo5-3/参考文件/timepro.py", line 34, in fetch_specific_month_html
    session.get(login_page_url, timeout=10)
  File "/home/<USER>/miniconda3/envs/my_suite_unified/lib/python3.12/site-packages/requests/sessions.py", line 602, in get
    return self.request("GET", url, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/miniconda3/envs/my_suite_unified/lib/python3.12/site-packages/requests/sessions.py", line 589, in request
    resp = self.send(prep, **send_kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/miniconda3/envs/my_suite_unified/lib/python3.12/site-packages/requests/sessions.py", line 703, in send
    r = adapter.send(request, **kwargs)
        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/miniconda3/envs/my_suite_unified/lib/python3.12/site-packages/requests/adapters.py", line 700, in send
    raise ConnectionError(e, request=request)
requests.exceptions.ConnectionError: HTTPConnectionPool(host='timepro.gongsi.co.jp', port=80): Max retries exceeded with url: /xgweb/login.asp (Caused by NameResolutionError("<urllib3.connection.HTTPConnection object at 0x7bef97d58c80>: Failed to resolve 'timepro.gongsi.co.jp' ([Errno -2] Name or service not known)"))
2025-07-03 17:15:39,897 - server5-2.timepro_collector - ERROR - ❌ 用户 '215829' (202506) 的采集失败: 未能从网站获取HTML内容。
Traceback (most recent call last):
  File "/home/<USER>/Documents/testF/2025/ser25/server0613/MySuiteDatebase2402newuigood-yolo5-3/server5-2/services/timepro_collector.py", line 132, in _collect_user_month_data
    raise ValueError("未能从网站获取HTML内容。")
ValueError: 未能从网站获取HTML内容。
2025-07-03 17:15:39,898 - server5-2.test - ERROR - ❌ 指定月份真实采集测试失败: 未能从网站获取HTML内容。
Traceback (most recent call last):
  File "/home/<USER>/Documents/testF/2025/ser25/server0613/MySuiteDatebase2402newuigood-yolo5-3/server5-2/test_server5_2.py", line 123, in test_specific_month_real_collection
    await self.collector._collect_user_month_data(
  File "/home/<USER>/Documents/testF/2025/ser25/server0613/MySuiteDatebase2402newuigood-yolo5-3/server5-2/services/timepro_collector.py", line 132, in _collect_user_month_data
    raise ValueError("未能从网站获取HTML内容。")
ValueError: 未能从网站获取HTML内容。
2025-07-03 17:15:39,898 - server5-2.test - ERROR - ❌ 测试失败: 核心功能: 采集2025年6月真实数据。后续测试可能受影响。
2025-07-03 17:15:40,899 - server5-2.test - INFO - 
==================================================
2025-07-03 17:15:40,899 - server5-2.test - INFO - 📊 测试结果汇总:
2025-07-03 17:15:40,899 - server5-2.test - INFO -   - 数据库连接: ✅ 通过
2025-07-03 17:15:40,899 - server5-2.test - INFO -   - 采集器初始化: ✅ 通过
2025-07-03 17:15:40,899 - server5-2.test - INFO -   - 核心功能: 采集2025年6月真实数据: ❌ 失败
2025-07-03 17:15:40,899 - server5-2.test - INFO - 总计: 2/3 测试通过
2025-07-03 17:15:40,899 - server5-2.test - INFO - ==================================================
2025-07-03 17:15:40,899 - server5-2.timeprotab_manager - INFO - 🔌 timeprotab数据库连接池已关闭
2025-07-03 17:15:40,899 - server5-2.test - INFO - ✅ 测试环境清理完成
2025-07-03 17:15:40,900 - server5-2.test - ERROR - ❌ 部分或全部测试失败。
2025-07-03 17:16:06,031 - server5-2.test - INFO - 🧪 server5-2 测试工具启动 (真实、无文件采集)
2025-07-03 17:16:06,031 - server5-2.test - INFO - ============================================================
2025-07-03 17:16:06,031 - server5-2.test - INFO - 🎯 开始运行所有测试 (无文件内存流)
2025-07-03 17:16:06,031 - server5-2.test - INFO - 🧪 设置测试环境
2025-07-03 17:16:06,270 - server5-2.timeprotab_manager - INFO - ✅ timeprotab数据库连接池创建成功
2025-07-03 17:16:06,285 - server5-2.timeprotab_manager - INFO - ✅ 基础分区表 timeprotab 初始化完成
2025-07-03 17:16:06,286 - server5-2.test - INFO - ✅ 测试环境设置完成
2025-07-03 17:16:06,286 - server5-2.test - INFO - 
==================== 数据库连接 ====================
2025-07-03 17:16:06,286 - server5-2.test - INFO - 🧪 (1/3) 测试数据库连接
2025-07-03 17:16:06,299 - server5-2.test - INFO - ✅ 数据库连接和状态获取测试成功
2025-07-03 17:16:07,300 - server5-2.test - INFO - 
==================== 采集器初始化 ====================
2025-07-03 17:16:07,300 - server5-2.test - INFO - 🧪 (2/3) 测试采集器初始化和关闭
2025-07-03 17:16:07,563 - server5-2.timeprotab_manager - INFO - ✅ timeprotab数据库连接池创建成功
2025-07-03 17:16:07,578 - server5-2.timeprotab_manager - INFO - ✅ 基础分区表 timeprotab 初始化完成
2025-07-03 17:16:07,579 - server5-2.timepro_collector - INFO - ✅ timepro采集服务启动成功
2025-07-03 17:16:07,579 - server5-2.timepro_collector - INFO - 📅 定时任务已设置: 每天 01:00 开始采集
2025-07-03 17:16:07,579 - server5-2.timepro_collector - INFO - ⏰ 调度器线程启动
2025-07-03 17:16:07,579 - server5-2.test - INFO - ✅ 采集器启动成功
2025-07-03 17:16:07,583 - server5-2.timeprotab_manager - INFO - 🔌 timeprotab数据库连接池已关闭
2025-07-03 17:16:07,583 - server5-2.timepro_collector - INFO - 🔌 timepro采集服务已停止
2025-07-03 17:16:07,583 - server5-2.test - INFO - ✅ 采集器停止成功
2025-07-03 17:16:08,584 - server5-2.test - INFO - 
==================== 核心功能: 采集2025年6月真实数据 ====================
2025-07-03 17:16:08,584 - server5-2.test - INFO - 🧪 (3/3) 核心测试: 采集2025年6月的真实数据
2025-07-03 17:16:08,584 - server5-2.test - INFO - 🔐 测试用户: 215829
2025-07-03 17:16:08,584 - server5-2.test - INFO - 📅 开始采集指定月份: 202506
2025-07-03 17:16:08,584 - server5-2.timepro_collector - INFO - 📅 开始为用户 '215829' 采集 202506 的数据
2025-07-03 17:16:08,584 - server5-2.timepro_collector - INFO - 🌐 正在调用timepro.py获取 202506 的数据, 用户: 215829
2025-07-03 17:16:09,778 - server5-2.timepro_collector - INFO - ✅ timepro.py 数据获取成功。
2025-07-03 17:16:09,778 - server5-2.timepro_collector - INFO - ✅ 成功在内存中获取HTML内容。
2025-07-03 17:16:09,796 - server5-2.timepro_collector - INFO - ✅ 成功从HTML解析了 30 条记录。
2025-07-03 17:16:09,798 - server5-2.timeprotab_manager - ERROR - ❌ 创建分区失败 2506: pool is closed
2025-07-03 17:16:09,798 - server5-2.timeprotab_manager - ERROR - ❌ 批量插入失败: pool is closed
2025-07-03 17:16:09,798 - server5-2.timepro_collector - ERROR - ❌ 数据库插入失败 (202506)。
2025-07-03 17:16:09,799 - server5-2.test - INFO - ✅ 指定月份真实数据采集调用完成
2025-07-03 17:16:09,799 - server5-2.test - INFO - 🔍 正在从数据库验证 2506 月份的数据...
2025-07-03 17:16:11,801 - server5-2.timeprotab_manager - ERROR - ❌ 查询月份数据失败: pool is closed
2025-07-03 17:16:11,801 - server5-2.test - ERROR - ❌ 验证失败! 未能在数据库中找到用户 215829 在 2506 月份的数据。
2025-07-03 17:16:11,801 - server5-2.test - ERROR - ❌ 测试失败: 核心功能: 采集2025年6月真实数据。后续测试可能受影响。
2025-07-03 17:16:12,802 - server5-2.test - INFO - 
==================================================
2025-07-03 17:16:12,802 - server5-2.test - INFO - 📊 测试结果汇总:
2025-07-03 17:16:12,802 - server5-2.test - INFO -   - 数据库连接: ✅ 通过
2025-07-03 17:16:12,802 - server5-2.test - INFO -   - 采集器初始化: ✅ 通过
2025-07-03 17:16:12,802 - server5-2.test - INFO -   - 核心功能: 采集2025年6月真实数据: ❌ 失败
2025-07-03 17:16:12,802 - server5-2.test - INFO - 总计: 2/3 测试通过
2025-07-03 17:16:12,802 - server5-2.test - INFO - ==================================================
2025-07-03 17:16:12,802 - server5-2.timeprotab_manager - INFO - 🔌 timeprotab数据库连接池已关闭
2025-07-03 17:16:12,802 - server5-2.test - INFO - ✅ 测试环境清理完成
2025-07-03 17:16:12,802 - server5-2.test - ERROR - ❌ 部分或全部测试失败。
2025-07-03 17:16:31,905 - server5-2.test - INFO - 🧪 server5-2 测试工具启动 (真实、无文件采集)
2025-07-03 17:16:31,906 - server5-2.test - INFO - ============================================================
2025-07-03 17:16:31,906 - server5-2.test - INFO - 🎯 开始运行所有测试 (无文件内存流)
2025-07-03 17:16:31,906 - server5-2.test - INFO - 🧪 设置测试环境
2025-07-03 17:16:32,139 - server5-2.timeprotab_manager - INFO - ✅ timeprotab数据库连接池创建成功
2025-07-03 17:16:32,153 - server5-2.timeprotab_manager - INFO - ✅ 基础分区表 timeprotab 初始化完成
2025-07-03 17:16:32,154 - server5-2.test - INFO - ✅ 测试环境设置完成
2025-07-03 17:16:32,154 - server5-2.test - INFO - 
==================== 数据库连接 ====================
2025-07-03 17:16:32,154 - server5-2.test - INFO - 🧪 (1/3) 测试数据库连接
2025-07-03 17:16:32,165 - server5-2.test - INFO - ✅ 数据库连接和状态获取测试成功
2025-07-03 17:16:33,166 - server5-2.test - INFO - 
==================== 采集器初始化 ====================
2025-07-03 17:16:33,166 - server5-2.test - INFO - 🧪 (2/3) 测试采集器初始化和关闭
2025-07-03 17:16:33,390 - server5-2.timeprotab_manager - INFO - ✅ timeprotab数据库连接池创建成功
2025-07-03 17:16:33,404 - server5-2.timeprotab_manager - INFO - ✅ 基础分区表 timeprotab 初始化完成
2025-07-03 17:16:33,406 - server5-2.timepro_collector - INFO - ✅ timepro采集服务启动成功
2025-07-03 17:16:33,406 - server5-2.timepro_collector - INFO - 📅 定时任务已设置: 每天 01:00 开始采集
2025-07-03 17:16:33,406 - server5-2.timepro_collector - INFO - ⏰ 调度器线程启动
2025-07-03 17:16:33,406 - server5-2.test - INFO - ✅ 采集器启动成功
2025-07-03 17:16:33,409 - server5-2.timeprotab_manager - INFO - 🔌 timeprotab数据库连接池已关闭
2025-07-03 17:16:33,409 - server5-2.timepro_collector - INFO - 🔌 timepro采集服务已停止
2025-07-03 17:16:33,409 - server5-2.test - INFO - ✅ 采集器停止成功
2025-07-03 17:16:34,411 - server5-2.test - INFO - 
==================== 核心功能: 采集2025年6月真实数据 ====================
2025-07-03 17:16:34,411 - server5-2.test - INFO - 🧪 (3/3) 核心测试: 采集2025年6月的真实数据
2025-07-03 17:16:34,411 - server5-2.test - INFO - 重新启动采集器以进行核心测试...
2025-07-03 17:16:34,662 - server5-2.timeprotab_manager - INFO - ✅ timeprotab数据库连接池创建成功
2025-07-03 17:16:34,677 - server5-2.timeprotab_manager - INFO - ✅ 基础分区表 timeprotab 初始化完成
2025-07-03 17:16:34,679 - server5-2.timepro_collector - INFO - ✅ timepro采集服务启动成功
2025-07-03 17:16:34,679 - server5-2.timepro_collector - INFO - 📅 定时任务已设置: 每天 01:00 开始采集
2025-07-03 17:16:34,679 - server5-2.timepro_collector - INFO - ⏰ 调度器线程启动
2025-07-03 17:16:34,680 - server5-2.test - INFO - 🔐 测试用户: 215829
2025-07-03 17:16:34,680 - server5-2.test - INFO - 📅 开始采集指定月份: 202506
2025-07-03 17:16:34,680 - server5-2.timepro_collector - INFO - 📅 开始为用户 '215829' 采集 202506 的数据
2025-07-03 17:16:34,680 - server5-2.timepro_collector - INFO - 🌐 正在调用timepro.py获取 202506 的数据, 用户: 215829
2025-07-03 17:16:35,922 - server5-2.timepro_collector - INFO - ✅ timepro.py 数据获取成功。
2025-07-03 17:16:35,922 - server5-2.timepro_collector - INFO - ✅ 成功在内存中获取HTML内容。
2025-07-03 17:16:35,942 - server5-2.timepro_collector - INFO - ✅ 成功从HTML解析了 30 条记录。
2025-07-03 17:16:35,953 - server5-2.timeprotab_manager - INFO - 📅 分区 timeprotab_2506 已存在
2025-07-03 17:16:35,963 - server5-2.timeprotab_manager - INFO - 📅 分区 timeprotab_2506 已存在
2025-07-03 17:16:35,972 - server5-2.timeprotab_manager - INFO - 📅 分区 timeprotab_2506 已存在
2025-07-03 17:16:35,975 - server5-2.timeprotab_manager - ERROR - ❌ 批量插入失败: invalid input for query argument $9: datetime.time(1, 0) (expected str, got time)
2025-07-03 17:16:35,975 - server5-2.timepro_collector - ERROR - ❌ 数据库插入失败 (202506)。
2025-07-03 17:16:35,975 - server5-2.test - INFO - ✅ 指定月份真实数据采集调用完成
2025-07-03 17:16:35,975 - server5-2.test - INFO - 🔍 正在从数据库验证 2506 月份的数据...
2025-07-03 17:16:37,980 - server5-2.timeprotab_manager - INFO - 📊 查询到 0 条记录: 215829 - 2506
2025-07-03 17:16:37,980 - server5-2.test - ERROR - ❌ 验证失败! 未能在数据库中找到用户 215829 在 2506 月份的数据。
2025-07-03 17:16:37,980 - server5-2.test - ERROR - ❌ 测试失败: 核心功能: 采集2025年6月真实数据。后续测试可能受影响。
2025-07-03 17:16:38,981 - server5-2.test - INFO - 
==================================================
2025-07-03 17:16:38,981 - server5-2.test - INFO - 📊 测试结果汇总:
2025-07-03 17:16:38,981 - server5-2.test - INFO -   - 数据库连接: ✅ 通过
2025-07-03 17:16:38,981 - server5-2.test - INFO -   - 采集器初始化: ✅ 通过
2025-07-03 17:16:38,981 - server5-2.test - INFO -   - 核心功能: 采集2025年6月真实数据: ❌ 失败
2025-07-03 17:16:38,981 - server5-2.test - INFO - 总计: 2/3 测试通过
2025-07-03 17:16:38,981 - server5-2.test - INFO - ==================================================
2025-07-03 17:16:38,985 - server5-2.timeprotab_manager - INFO - 🔌 timeprotab数据库连接池已关闭
2025-07-03 17:16:38,985 - server5-2.timepro_collector - INFO - 🔌 timepro采集服务已停止
2025-07-03 17:16:38,985 - server5-2.timeprotab_manager - INFO - 🔌 timeprotab数据库连接池已关闭
2025-07-03 17:16:38,985 - server5-2.test - INFO - ✅ 测试环境清理完成
2025-07-03 17:16:38,985 - server5-2.test - ERROR - ❌ 部分或全部测试失败。
2025-07-03 17:17:16,134 - server5-2.test - INFO - 🧪 server5-2 测试工具启动 (真实、无文件采集)
2025-07-03 17:17:16,134 - server5-2.test - INFO - ============================================================
2025-07-03 17:17:16,134 - server5-2.test - INFO - 🎯 开始运行所有测试 (无文件内存流)
2025-07-03 17:17:16,134 - server5-2.test - INFO - 🧪 设置测试环境
2025-07-03 17:17:16,353 - server5-2.timeprotab_manager - INFO - ✅ timeprotab数据库连接池创建成功
2025-07-03 17:17:16,367 - server5-2.timeprotab_manager - INFO - ✅ 基础分区表 timeprotab 初始化完成
2025-07-03 17:17:16,368 - server5-2.test - INFO - ✅ 测试环境设置完成
2025-07-03 17:17:16,368 - server5-2.test - INFO - 
==================== 数据库连接 ====================
2025-07-03 17:17:16,368 - server5-2.test - INFO - 🧪 (1/3) 测试数据库连接
2025-07-03 17:17:16,379 - server5-2.test - INFO - ✅ 数据库连接和状态获取测试成功
2025-07-03 17:17:17,380 - server5-2.test - INFO - 
==================== 采集器初始化 ====================
2025-07-03 17:17:17,380 - server5-2.test - INFO - 🧪 (2/3) 测试采集器初始化和关闭
2025-07-03 17:17:17,703 - server5-2.timeprotab_manager - INFO - ✅ timeprotab数据库连接池创建成功
2025-07-03 17:17:17,716 - server5-2.timeprotab_manager - INFO - ✅ 基础分区表 timeprotab 初始化完成
2025-07-03 17:17:17,717 - server5-2.timepro_collector - INFO - ✅ timepro采集服务启动成功
2025-07-03 17:17:17,717 - server5-2.timepro_collector - INFO - 📅 定时任务已设置: 每天 01:00 开始采集
2025-07-03 17:17:17,717 - server5-2.timepro_collector - INFO - ⏰ 调度器线程启动
2025-07-03 17:17:17,717 - server5-2.test - INFO - ✅ 采集器启动成功
2025-07-03 17:17:17,721 - server5-2.timeprotab_manager - INFO - 🔌 timeprotab数据库连接池已关闭
2025-07-03 17:17:17,721 - server5-2.timepro_collector - INFO - 🔌 timepro采集服务已停止
2025-07-03 17:17:17,721 - server5-2.test - INFO - ✅ 采集器停止成功
2025-07-03 17:17:18,722 - server5-2.test - INFO - 
==================== 核心功能: 采集2025年6月真实数据 ====================
2025-07-03 17:17:18,722 - server5-2.test - INFO - 🧪 (3/3) 核心测试: 采集2025年6月的真实数据
2025-07-03 17:17:18,722 - server5-2.test - INFO - 重新启动采集器以进行核心测试...
2025-07-03 17:17:18,929 - server5-2.timeprotab_manager - INFO - ✅ timeprotab数据库连接池创建成功
2025-07-03 17:17:18,945 - server5-2.timeprotab_manager - INFO - ✅ 基础分区表 timeprotab 初始化完成
2025-07-03 17:17:18,946 - server5-2.timepro_collector - INFO - ✅ timepro采集服务启动成功
2025-07-03 17:17:18,946 - server5-2.timepro_collector - INFO - 📅 定时任务已设置: 每天 01:00 开始采集
2025-07-03 17:17:18,946 - server5-2.timepro_collector - INFO - ⏰ 调度器线程启动
2025-07-03 17:17:18,946 - server5-2.test - INFO - 🔐 测试用户: 215829
2025-07-03 17:17:18,946 - server5-2.test - INFO - 📅 开始采集指定月份: 202506
2025-07-03 17:17:18,946 - server5-2.timepro_collector - INFO - 📅 开始为用户 '215829' 采集 202506 的数据
2025-07-03 17:17:18,946 - server5-2.timepro_collector - INFO - 🌐 正在调用timepro.py获取 202506 的数据, 用户: 215829
2025-07-03 17:17:20,099 - server5-2.timepro_collector - INFO - ✅ timepro.py 数据获取成功。
2025-07-03 17:17:20,099 - server5-2.timepro_collector - INFO - ✅ 成功在内存中获取HTML内容。
2025-07-03 17:17:20,118 - server5-2.timepro_collector - INFO - ✅ 成功从HTML解析了 30 条记录。
2025-07-03 17:17:20,128 - server5-2.timeprotab_manager - INFO - 📅 分区 timeprotab_2506 已存在
2025-07-03 17:17:20,139 - server5-2.timeprotab_manager - INFO - 📅 分区 timeprotab_2506 已存在
2025-07-03 17:17:20,144 - server5-2.timeprotab_manager - ERROR - ❌ 批量插入失败: ON CONFLICT 指定に合致するユニーク制約または排除制約がありません
2025-07-03 17:17:20,144 - server5-2.timepro_collector - ERROR - ❌ 数据库插入失败 (202506)。
2025-07-03 17:17:20,144 - server5-2.test - INFO - ✅ 指定月份真实数据采集调用完成
2025-07-03 17:17:20,144 - server5-2.test - INFO - 🔍 正在从数据库验证 2506 月份的数据...
2025-07-03 17:17:22,152 - server5-2.timeprotab_manager - INFO - 📊 查询到 0 条记录: 215829 - 2506
2025-07-03 17:17:22,152 - server5-2.test - ERROR - ❌ 验证失败! 未能在数据库中找到用户 215829 在 2506 月份的数据。
2025-07-03 17:17:22,153 - server5-2.test - ERROR - ❌ 测试失败: 核心功能: 采集2025年6月真实数据。后续测试可能受影响。
2025-07-03 17:17:23,154 - server5-2.test - INFO - 
==================================================
2025-07-03 17:17:23,154 - server5-2.test - INFO - 📊 测试结果汇总:
2025-07-03 17:17:23,154 - server5-2.test - INFO -   - 数据库连接: ✅ 通过
2025-07-03 17:17:23,154 - server5-2.test - INFO -   - 采集器初始化: ✅ 通过
2025-07-03 17:17:23,154 - server5-2.test - INFO -   - 核心功能: 采集2025年6月真实数据: ❌ 失败
2025-07-03 17:17:23,154 - server5-2.test - INFO - 总计: 2/3 测试通过
2025-07-03 17:17:23,154 - server5-2.test - INFO - ==================================================
2025-07-03 17:17:23,159 - server5-2.timeprotab_manager - INFO - 🔌 timeprotab数据库连接池已关闭
2025-07-03 17:17:23,159 - server5-2.timepro_collector - INFO - 🔌 timepro采集服务已停止
2025-07-03 17:17:23,159 - server5-2.timeprotab_manager - INFO - 🔌 timeprotab数据库连接池已关闭
2025-07-03 17:17:23,159 - server5-2.test - INFO - ✅ 测试环境清理完成
2025-07-03 17:17:23,159 - server5-2.test - ERROR - ❌ 部分或全部测试失败。
2025-07-03 17:17:54,805 - server5-2.test - INFO - 🧪 server5-2 测试工具启动 (真实、无文件采集)
2025-07-03 17:17:54,805 - server5-2.test - INFO - ============================================================
2025-07-03 17:17:54,805 - server5-2.test - INFO - 🎯 开始运行所有测试 (无文件内存流)
2025-07-03 17:17:54,805 - server5-2.test - INFO - 🧪 设置测试环境
2025-07-03 17:17:55,001 - server5-2.timeprotab_manager - INFO - ✅ timeprotab数据库连接池创建成功
2025-07-03 17:17:55,014 - server5-2.timeprotab_manager - INFO - ✅ 基础分区表 timeprotab 初始化完成
2025-07-03 17:17:55,015 - server5-2.test - INFO - ✅ 测试环境设置完成
2025-07-03 17:17:55,016 - server5-2.test - INFO - 
==================== 数据库连接 ====================
2025-07-03 17:17:55,016 - server5-2.test - INFO - 🧪 (1/3) 测试数据库连接
2025-07-03 17:17:55,029 - server5-2.test - INFO - ✅ 数据库连接和状态获取测试成功
2025-07-03 17:17:56,030 - server5-2.test - INFO - 
==================== 采集器初始化 ====================
2025-07-03 17:17:56,030 - server5-2.test - INFO - 🧪 (2/3) 测试采集器初始化和关闭
2025-07-03 17:17:56,269 - server5-2.timeprotab_manager - INFO - ✅ timeprotab数据库连接池创建成功
2025-07-03 17:17:56,283 - server5-2.timeprotab_manager - INFO - ✅ 基础分区表 timeprotab 初始化完成
2025-07-03 17:17:56,284 - server5-2.timepro_collector - INFO - ✅ timepro采集服务启动成功
2025-07-03 17:17:56,284 - server5-2.timepro_collector - INFO - 📅 定时任务已设置: 每天 01:00 开始采集
2025-07-03 17:17:56,285 - server5-2.timepro_collector - INFO - ⏰ 调度器线程启动
2025-07-03 17:17:56,285 - server5-2.test - INFO - ✅ 采集器启动成功
2025-07-03 17:17:56,288 - server5-2.timeprotab_manager - INFO - 🔌 timeprotab数据库连接池已关闭
2025-07-03 17:17:56,288 - server5-2.timepro_collector - INFO - 🔌 timepro采集服务已停止
2025-07-03 17:17:56,288 - server5-2.test - INFO - ✅ 采集器停止成功
2025-07-03 17:17:57,289 - server5-2.test - INFO - 
==================== 核心功能: 采集2025年6月真实数据 ====================
2025-07-03 17:17:57,289 - server5-2.test - INFO - 🧪 (3/3) 核心测试: 采集2025年6月的真实数据
2025-07-03 17:17:57,289 - server5-2.test - INFO - 重新启动采集器以进行核心测试...
2025-07-03 17:17:57,582 - server5-2.timeprotab_manager - INFO - ✅ timeprotab数据库连接池创建成功
2025-07-03 17:17:57,597 - server5-2.timeprotab_manager - INFO - ✅ 基础分区表 timeprotab 初始化完成
2025-07-03 17:17:57,598 - server5-2.timepro_collector - INFO - ✅ timepro采集服务启动成功
2025-07-03 17:17:57,598 - server5-2.timepro_collector - INFO - 📅 定时任务已设置: 每天 01:00 开始采集
2025-07-03 17:17:57,598 - server5-2.timepro_collector - INFO - ⏰ 调度器线程启动
2025-07-03 17:17:57,598 - server5-2.test - INFO - 🔐 测试用户: 215829
2025-07-03 17:17:57,598 - server5-2.test - INFO - 📅 开始采集指定月份: 202506
2025-07-03 17:17:57,598 - server5-2.timepro_collector - INFO - 📅 开始为用户 '215829' 采集 202506 的数据
2025-07-03 17:17:57,598 - server5-2.timepro_collector - INFO - 🌐 正在调用timepro.py获取 202506 的数据, 用户: 215829
2025-07-03 17:17:58,775 - server5-2.timepro_collector - INFO - ✅ timepro.py 数据获取成功。
2025-07-03 17:17:58,775 - server5-2.timepro_collector - INFO - ✅ 成功在内存中获取HTML内容。
2025-07-03 17:17:58,793 - server5-2.timepro_collector - INFO - ✅ 成功从HTML解析了 30 条记录。
2025-07-03 17:17:58,803 - server5-2.timeprotab_manager - INFO - 📅 分区 timeprotab_2506 已存在
2025-07-03 17:17:58,812 - server5-2.timeprotab_manager - INFO - 📅 分区 timeprotab_2506 已存在
2025-07-03 17:17:58,817 - server5-2.timeprotab_manager - ERROR - ❌ 批量插入失败: ON CONFLICT 指定に合致するユニーク制約または排除制約がありません
2025-07-03 17:17:58,817 - server5-2.timepro_collector - ERROR - ❌ 数据库插入失败 (202506)。
2025-07-03 17:17:58,817 - server5-2.test - INFO - ✅ 指定月份真实数据采集调用完成
2025-07-03 17:17:58,817 - server5-2.test - INFO - 🔍 正在从数据库验证 2506 月份的数据...
2025-07-03 17:18:00,824 - server5-2.timeprotab_manager - INFO - 📊 查询到 0 条记录: 215829 - 2506
2025-07-03 17:18:00,825 - server5-2.test - ERROR - ❌ 验证失败! 未能在数据库中找到用户 215829 在 2506 月份的数据。
2025-07-03 17:18:00,825 - server5-2.test - ERROR - ❌ 测试失败: 核心功能: 采集2025年6月真实数据。后续测试可能受影响。
2025-07-03 17:18:01,826 - server5-2.test - INFO - 
==================================================
2025-07-03 17:18:01,826 - server5-2.test - INFO - 📊 测试结果汇总:
2025-07-03 17:18:01,826 - server5-2.test - INFO -   - 数据库连接: ✅ 通过
2025-07-03 17:18:01,826 - server5-2.test - INFO -   - 采集器初始化: ✅ 通过
2025-07-03 17:18:01,826 - server5-2.test - INFO -   - 核心功能: 采集2025年6月真实数据: ❌ 失败
2025-07-03 17:18:01,826 - server5-2.test - INFO - 总计: 2/3 测试通过
2025-07-03 17:18:01,826 - server5-2.test - INFO - ==================================================
2025-07-03 17:18:01,830 - server5-2.timeprotab_manager - INFO - 🔌 timeprotab数据库连接池已关闭
2025-07-03 17:18:01,830 - server5-2.timepro_collector - INFO - 🔌 timepro采集服务已停止
2025-07-03 17:18:01,830 - server5-2.timeprotab_manager - INFO - 🔌 timeprotab数据库连接池已关闭
2025-07-03 17:18:01,830 - server5-2.test - INFO - ✅ 测试环境清理完成
2025-07-03 17:18:01,830 - server5-2.test - ERROR - ❌ 部分或全部测试失败。
2025-07-03 17:18:21,045 - server5-2.test - INFO - 🧪 server5-2 测试工具启动 (真实、无文件采集)
2025-07-03 17:18:21,045 - server5-2.test - INFO - ============================================================
2025-07-03 17:18:21,045 - server5-2.test - INFO - 🎯 开始运行所有测试 (无文件内存流)
2025-07-03 17:18:21,045 - server5-2.test - INFO - 🧪 设置测试环境
2025-07-03 17:18:21,267 - server5-2.timeprotab_manager - INFO - ✅ timeprotab数据库连接池创建成功
2025-07-03 17:18:21,267 - server5-2.test - INFO - 为了确保数据库约束和表结构最新，将删除并重建 timeprotab 表...
2025-07-03 17:18:21,305 - server5-2.test - INFO - 旧表 timeprotab 已删除。
2025-07-03 17:18:21,320 - server5-2.timeprotab_manager - INFO - ✅ 基础分区表 timeprotab 初始化完成
2025-07-03 17:18:21,321 - server5-2.test - INFO - ✅ 测试环境设置完成
2025-07-03 17:18:21,321 - server5-2.test - INFO - 
==================== 数据库连接 ====================
2025-07-03 17:18:21,321 - server5-2.test - INFO - 🧪 (1/3) 测试数据库连接
2025-07-03 17:18:21,329 - server5-2.test - INFO - ✅ 数据库连接和状态获取测试成功
2025-07-03 17:18:22,330 - server5-2.test - INFO - 
==================== 采集器初始化 ====================
2025-07-03 17:18:22,331 - server5-2.test - INFO - 🧪 (2/3) 测试采集器初始化和关闭
2025-07-03 17:18:22,656 - server5-2.timeprotab_manager - INFO - ✅ timeprotab数据库连接池创建成功
2025-07-03 17:18:22,669 - server5-2.timeprotab_manager - INFO - ✅ 基础分区表 timeprotab 初始化完成
2025-07-03 17:18:22,671 - server5-2.timepro_collector - INFO - ✅ timepro采集服务启动成功
2025-07-03 17:18:22,671 - server5-2.timepro_collector - INFO - 📅 定时任务已设置: 每天 01:00 开始采集
2025-07-03 17:18:22,671 - server5-2.timepro_collector - INFO - ⏰ 调度器线程启动
2025-07-03 17:18:22,671 - server5-2.test - INFO - ✅ 采集器启动成功
2025-07-03 17:18:22,675 - server5-2.timeprotab_manager - INFO - 🔌 timeprotab数据库连接池已关闭
2025-07-03 17:18:22,675 - server5-2.timepro_collector - INFO - 🔌 timepro采集服务已停止
2025-07-03 17:18:22,675 - server5-2.test - INFO - ✅ 采集器停止成功
2025-07-03 17:18:23,676 - server5-2.test - INFO - 
==================== 核心功能: 采集2025年6月真实数据 ====================
2025-07-03 17:18:23,676 - server5-2.test - INFO - 🧪 (3/3) 核心测试: 采集2025年6月的真实数据
2025-07-03 17:18:23,676 - server5-2.test - INFO - 重新启动采集器以进行核心测试...
2025-07-03 17:18:23,914 - server5-2.timeprotab_manager - INFO - ✅ timeprotab数据库连接池创建成功
2025-07-03 17:18:23,929 - server5-2.timeprotab_manager - INFO - ✅ 基础分区表 timeprotab 初始化完成
2025-07-03 17:18:23,930 - server5-2.timepro_collector - INFO - ✅ timepro采集服务启动成功
2025-07-03 17:18:23,930 - server5-2.timepro_collector - INFO - 📅 定时任务已设置: 每天 01:00 开始采集
2025-07-03 17:18:23,930 - server5-2.timepro_collector - INFO - ⏰ 调度器线程启动
2025-07-03 17:18:23,930 - server5-2.test - INFO - 🔐 测试用户: 215829
2025-07-03 17:18:23,930 - server5-2.test - INFO - 📅 开始采集指定月份: 202506
2025-07-03 17:18:23,930 - server5-2.timepro_collector - INFO - 📅 开始为用户 '215829' 采集 202506 的数据
2025-07-03 17:18:23,930 - server5-2.timepro_collector - INFO - 🌐 正在调用timepro.py获取 202506 的数据, 用户: 215829
2025-07-03 17:18:25,112 - server5-2.timepro_collector - INFO - ✅ timepro.py 数据获取成功。
2025-07-03 17:18:25,112 - server5-2.timepro_collector - INFO - ✅ 成功在内存中获取HTML内容。
2025-07-03 17:18:25,132 - server5-2.timepro_collector - INFO - ✅ 成功从HTML解析了 30 条记录。
2025-07-03 17:18:25,184 - server5-2.timeprotab_manager - INFO - ✅ 创建分区成功: timeprotab_2506 (2025-06-01 ~ 2025-07-01)
2025-07-03 17:18:25,197 - server5-2.timeprotab_manager - INFO - 📅 分区 timeprotab_2506 已存在
2025-07-03 17:18:25,208 - server5-2.timeprotab_manager - INFO - 📅 分区 timeprotab_2506 已存在
2025-07-03 17:18:25,211 - server5-2.timeprotab_manager - INFO - 📅 分区 timeprotab_2506 已存在
2025-07-03 17:18:25,214 - server5-2.timeprotab_manager - INFO - 📅 分区 timeprotab_2506 已存在
2025-07-03 17:18:25,217 - server5-2.timeprotab_manager - INFO - 📅 分区 timeprotab_2506 已存在
2025-07-03 17:18:25,220 - server5-2.timeprotab_manager - INFO - 📅 分区 timeprotab_2506 已存在
2025-07-03 17:18:25,223 - server5-2.timeprotab_manager - INFO - 📅 分区 timeprotab_2506 已存在
2025-07-03 17:18:25,225 - server5-2.timeprotab_manager - INFO - 📅 分区 timeprotab_2506 已存在
2025-07-03 17:18:25,227 - server5-2.timeprotab_manager - INFO - 📅 分区 timeprotab_2506 已存在
2025-07-03 17:18:25,229 - server5-2.timeprotab_manager - INFO - 📅 分区 timeprotab_2506 已存在
2025-07-03 17:18:25,231 - server5-2.timeprotab_manager - INFO - 📅 分区 timeprotab_2506 已存在
2025-07-03 17:18:25,233 - server5-2.timeprotab_manager - INFO - 📅 分区 timeprotab_2506 已存在
2025-07-03 17:18:25,235 - server5-2.timeprotab_manager - INFO - 📅 分区 timeprotab_2506 已存在
2025-07-03 17:18:25,237 - server5-2.timeprotab_manager - INFO - 📅 分区 timeprotab_2506 已存在
2025-07-03 17:18:25,240 - server5-2.timeprotab_manager - INFO - 📅 分区 timeprotab_2506 已存在
2025-07-03 17:18:25,242 - server5-2.timeprotab_manager - INFO - 📅 分区 timeprotab_2506 已存在
2025-07-03 17:18:25,244 - server5-2.timeprotab_manager - INFO - 📅 分区 timeprotab_2506 已存在
2025-07-03 17:18:25,246 - server5-2.timeprotab_manager - INFO - 📅 分区 timeprotab_2506 已存在
2025-07-03 17:18:25,248 - server5-2.timeprotab_manager - INFO - 📅 分区 timeprotab_2506 已存在
2025-07-03 17:18:25,250 - server5-2.timeprotab_manager - INFO - 📅 分区 timeprotab_2506 已存在
2025-07-03 17:18:25,252 - server5-2.timeprotab_manager - INFO - 📅 分区 timeprotab_2506 已存在
2025-07-03 17:18:25,254 - server5-2.timeprotab_manager - INFO - 📅 分区 timeprotab_2506 已存在
2025-07-03 17:18:25,256 - server5-2.timeprotab_manager - INFO - 📅 分区 timeprotab_2506 已存在
2025-07-03 17:18:25,258 - server5-2.timeprotab_manager - INFO - 📅 分区 timeprotab_2506 已存在
2025-07-03 17:18:25,260 - server5-2.timeprotab_manager - INFO - 📅 分区 timeprotab_2506 已存在
2025-07-03 17:18:25,262 - server5-2.timeprotab_manager - INFO - 📅 分区 timeprotab_2506 已存在
2025-07-03 17:18:25,265 - server5-2.timeprotab_manager - INFO - 📅 分区 timeprotab_2506 已存在
2025-07-03 17:18:25,267 - server5-2.timeprotab_manager - INFO - 📅 分区 timeprotab_2506 已存在
2025-07-03 17:18:25,269 - server5-2.timeprotab_manager - INFO - 📅 分区 timeprotab_2506 已存在
2025-07-03 17:18:25,271 - server5-2.timeprotab_manager - INFO - 📅 分区 timeprotab_2506 已存在
2025-07-03 17:18:25,274 - server5-2.timeprotab_manager - INFO - ✅ 批量插入/更新了 30 条记录
2025-07-03 17:18:25,274 - server5-2.timepro_collector - INFO - ✅ 数据库插入成功: 30 条记录 (202506)。
2025-07-03 17:18:25,274 - server5-2.test - INFO - ✅ 指定月份真实数据采集调用完成
2025-07-03 17:18:25,274 - server5-2.test - INFO - 🔍 正在从数据库验证 2506 月份的数据...
2025-07-03 17:18:27,279 - server5-2.timeprotab_manager - INFO - 📊 查询到 30 条记录: 215829 - 2506
2025-07-03 17:18:27,280 - server5-2.test - INFO - 🎉 验证成功! 在数据库中找到 30 条记录 (2506)。
2025-07-03 17:18:27,280 - server5-2.test - INFO - --- 前5条记录示例 ---
2025-07-03 17:18:27,280 - server5-2.test - INFO -   - 日期: 2025-06-01, 出勤: None, コメント: 
2025-07-03 17:18:27,280 - server5-2.test - INFO -   - 日期: 2025-06-02, 出勤: 07:21:00, コメント: 計測システム開発
2025-07-03 17:18:27,280 - server5-2.test - INFO -   - 日期: 2025-06-03, 出勤: 07:18:00, コメント: 計測システム開発
2025-07-03 17:18:27,281 - server5-2.test - INFO -   - 日期: 2025-06-04, 出勤: 07:24:00, コメント: 計測システム開発
2025-07-03 17:18:27,281 - server5-2.test - INFO -   - 日期: 2025-06-05, 出勤: 07:18:00, コメント: 計測システム開発
2025-07-03 17:18:27,281 - server5-2.test - INFO - --------------------
2025-07-03 17:18:28,282 - server5-2.test - INFO - 
==================================================
2025-07-03 17:18:28,282 - server5-2.test - INFO - 📊 测试结果汇总:
2025-07-03 17:18:28,282 - server5-2.test - INFO -   - 数据库连接: ✅ 通过
2025-07-03 17:18:28,282 - server5-2.test - INFO -   - 采集器初始化: ✅ 通过
2025-07-03 17:18:28,282 - server5-2.test - INFO -   - 核心功能: 采集2025年6月真实数据: ✅ 通过
2025-07-03 17:18:28,282 - server5-2.test - INFO - 总计: 3/3 测试通过
2025-07-03 17:18:28,282 - server5-2.test - INFO - ==================================================
2025-07-03 17:18:28,286 - server5-2.timeprotab_manager - INFO - 🔌 timeprotab数据库连接池已关闭
2025-07-03 17:18:28,286 - server5-2.timepro_collector - INFO - 🔌 timepro采集服务已停止
2025-07-03 17:18:28,286 - server5-2.timeprotab_manager - INFO - 🔌 timeprotab数据库连接池已关闭
2025-07-03 17:18:28,286 - server5-2.test - INFO - ✅ 测试环境清理完成
2025-07-03 17:18:28,286 - server5-2.test - INFO - 🎉 所有测试通过！
