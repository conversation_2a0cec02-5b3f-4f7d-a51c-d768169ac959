# server5-2/services/html_parser.py
# 2025/07/03 + 17:30 + 新增，完全在内存中操作的HTML解析器
import logging
from bs4 import BeautifulSoup
from typing import List, Dict, Optional

logger = logging.getLogger(__name__)

def parse_html_to_records(html_content: str) -> Optional[List[Dict]]:
    """
    从timepro网站的HTML内容中解析表格数据。
    此函数完全在内存中操作，不读写文件。

    Args:
        html_content (str): HTML内容的字符串。

    Returns:
        一个包含记录字典的列表。如果解析失败，则返回None。
    """
    if not html_content:
        logger.error("HTML内容为空，无法解析。")
        return None

    try:
        soup = BeautifulSoup(html_content, 'lxml')

        # 从CmbYM下拉菜单中提取所选的年份和月份
        selected_year_month = None
        cmb_ym_select = soup.find('select', {'id': 'CmbYM'})
        if cmb_ym_select:
            selected_option = cmb_ym_select.find('option', selected=True)
            if selected_option and selected_option.has_attr('value'):
                value_str = selected_option['value']
                if len(value_str) == 6 and value_str.isdigit():
                    selected_year_month = value_str  # 格式: YYYYMM
        
        if not selected_year_month:
            logger.error("在HTML中找不到选定的年份/月份 ('CmbYM')。")
            return None
        
        selected_year = selected_year_month[:4]

        # 数据表的表头
        headers = [
            "日付", "星期", "ｶﾚﾝﾀﾞ", "不在", "勤務区分", "事由",
            "出勤時刻", "ＭＣ_出勤", "退勤時刻", "ＭＣ_退勤", "所定時間", "早出残業",
            "内深夜残業", "遅刻早退", "休出時間", "出張残業", "外出時間",
            "戻り時間", "コメント"
        ]
        
        # 查找主数据表 - 关键是找到包含"日付"标题的表
        main_data_table = None
        all_tables = soup.find_all('table')
        for table in all_tables:
            header_row = table.find('tr', class_='ap_tr_title')
            if header_row and header_row.find(lambda tag: tag.name == 'td' and '日付' in tag.get_text(strip=True)):
                main_data_table = table
                break
                
        if not main_data_table:
            logger.error("在HTML中找不到主数据表（包含'日付'表头）。")
            return None

        records = []
        # 遍历表中的数据行
        for row in main_data_table.find_all('tr', class_='ap_tr_base'):
            cells = row.find_all('td')
            
            if len(cells) >= 19:
                record_dict = {header: cells[i].get_text(strip=True) for i, header in enumerate(headers)}

                # 重建完整日期
                if record_dict["日付"] and '/' in record_dict["日付"]:
                    record_dict["日付"] = f"{selected_year}/{record_dict['日付']}"
                
                records.append(record_dict)
            elif cells:
                logger.warning(f"跳过包含 {len(cells)} 个单元格的行，预期为19。内容: {[c.get_text(strip=True) for c in cells]}")

        if not records:
            logger.warning("找到了HTML表，但未能提取任何数据记录。")
            return None
            
        logger.info(f"成功从HTML中解析了 {len(records)} 条记录。")
        return records

    except Exception as e:
        logger.error(f"解析HTML时发生意外错误: {e}", exc_info=True)
        return None 