# server5-2/services/timepro_collector.py
# 2025/07/03 + 17:30 + 重构为完全无文件的内存处理流程
import asyncio
import logging
import time as time_module # 2025/07/03 + 17:30 + 修正命名空间冲突
from datetime import datetime, time, timedelta
from pathlib import Path
from typing import Dict, List, Optional

import schedule
from threading import Thread

# 2025/07/03 + 17:30 + 修正导入错误的配置变量名
from config.config import TIMEPRO_USERS, DATA_STORAGE
from database.timeprotab_manager import TimeprotabManager
# 2025/07/03 + 17:30 + 导入新的内存HTML解析器
from .html_parser import parse_html_to_records

# 2025/07/03 + 17:30 + 适应timepro.py文件位置移动，并清理无用导入
from . import timepro

logger = logging.getLogger("server5-2.timepro_collector")

class TimeproCollector:
    """
    负责从timepro网站采集数据，并存入数据库。
    特点:
    - 定时执行: 每天凌晨1点开始采集。
    - 内存处理: HTML解析完全在内存中进行，不生成中间文件。
    - 数据库存储: 解析后的数据直接存入PostgreSQL。
    - 错误处理: 包含重试和延迟机制，确保稳定性。
    """
    def __init__(self, db_manager: Optional[TimeprotabManager] = None):
        """
        初始化采集器。
        2025/07/04 + 12:30 - 修正：确保db_manager在未提供时严格为None。
        """
        self.user_configs = TIMEPRO_USERS
        self.db_manager = db_manager  # 仅赋值，不创建实例
        self.is_running = False
        self.scheduler_thread = None
        self.loop = None
        self.collection_stats = {
            "total_collections": 0,
            "last_collection_time": None,
            "successful_collections": 0,
            "failed_collections": 0,
        }
        logger.info("TimeproCollector instance created.")

    async def start(self):
        """启动采集服务"""
        if not self.is_running:
            self.is_running = True
            logger.info("🚀 启动 timepro采集服务...")
            
            self.loop = asyncio.get_running_loop()

            # 2025/07/04 + 12:30 - 修正：确保db_manager在启动时已连接
            if self.db_manager is None:
                logger.info("内部创建并连接新的DB Manager实例。")
                self.db_manager = TimeprotabManager()
                await self.db_manager.connect()
            elif not self.db_manager.is_connected():
                logger.warning("外部传入的DB Manager未连接，正在尝试连接...")
                await self.db_manager.connect()

            # 初始化数据库主分区表
            await self.db_manager.initialize_base_table()
            
            # 设置定时任务
            schedule.every().day.at("07:59").do(self._run_scheduled_collection)
            
            self.scheduler_thread = Thread(target=self._run_scheduler, daemon=True)
            self.scheduler_thread.start()
            
            logger.info("✅ timepro采集服务启动成功")
            logger.info(f"📅 定时任务已设置: 每天 07:51 开始采集")
            logger.info(f"⏰ 调度器线程启动")

    async def stop(self):
        """停止采集服务"""
        if self.is_running:
            self.is_running = False
            schedule.clear()
            if self.scheduler_thread and self.scheduler_thread.is_alive():
                # 调度器线程是守护线程，会自动退出
                pass
            await self.db_manager.disconnect()
            logger.info("🔌 timepro采集服务已停止")

    def _run_scheduler(self):
        """运行调度器的循环"""
        while self.is_running:
            schedule.run_pending()
            # 2025/07/03 + 17:30 + 使用修正后的time_module
            time_module.sleep(1)

    def _run_scheduled_collection(self):
        """
        运行计划中的采集任务。
        此方法在独立的调度器线程中被调用，
        因此需要使用线程安全的方式将协程提交到主事件循环。
        """
        if self.loop and self.is_running:
            logger.info("⏰ 调度器线程: 检测到定时任务，准备提交到主事件循环...")
            # 2025/07/04 + 11:30 - 使用线程安全的方式在主循环上运行协程
            future = asyncio.run_coroutine_threadsafe(
                self.collect_all_users_current_and_last_month(), 
                self.loop
            )
            try:
                # 等待任务完成，可以设置超时
                future.result(timeout=600) # 10分钟超时
                logger.info("✅ 定时采集任务在主事件循环中执行完毕。")
            except Exception as e:
                logger.error(f"❌ 定时采集任务在主事件循环中执行时发生错误: {e}", exc_info=True)
        else:
            logger.warning("⚠️ 调度器触发，但事件循环未就绪或服务已停止，跳过任务。")

    async def collect_all_users_current_and_last_month(self):
        """为所有配置的用户采集当月和上个月的数据"""
        logger.info("🚀 开始所有用户的月度数据采集")
        
        now = datetime.now()
        current_month = now.replace(day=1)
        last_month = (current_month - timedelta(days=1)).replace(day=1)

        # 2025/07/04 + 10:00 - 筛选出需要执行定时采集的用户
        scheduled_users = [user for user in self.user_configs if user.get("scheduled_collection", False)]

        if not scheduled_users:
            logger.info("ℹ️ 没有配置为自动采集的用户，跳过本次定时任务。")
            return
        
        logger.info(f"👥 本次自动采集任务将为 {len(scheduled_users)} 个用户执行。")

        for user in scheduled_users:
            try:
                logger.info(f"👤 开始为用户 {user['employee_id']} 采集")
                
                # 采集当月数据
                await self._collect_user_month_data(
                    user["user_id"], user["password"], user["employee_id"], current_month
                )
                
                # 采集上个月数据
                await self._collect_user_month_data(
                    user["user_id"], user["password"], user["employee_id"], last_month
                )
                
            except Exception as e:
                logger.error(f"❌ 用户 {user['employee_id']} 的数据采集失败: {e}")
            finally:
                # 2025/07/04 + 10:00 - 按要求将间隔调整为10秒
                logger.info(f"⏳ 用户 {user['employee_id']} 采集完成，等待10秒...")
                await asyncio.sleep(10)
        
        logger.info("✅ 所有用户月度数据采集完成")

    async def _collect_user_month_data(self, user_id: str, password: str, employee_id: str, target_month: datetime):
        """
        为指定用户采集指定月份的数据，完全在内存中处理。
        """
        month_str = target_month.strftime("%Y%m")
        logger.info(f"📅 开始为用户 '{employee_id}' 采集 {month_str} 的数据")

        try:
            # 1. 从网站获取HTML内容
            html_content = await self._fetch_timepro_data_async(
                user_id, password, target_month
            )
            if not html_content:
                raise ValueError("未能从网站获取HTML内容。")
            
            logger.info("✅ 成功在内存中获取HTML内容。")

            # 2. 直接将HTML内容解析为记录
            loop = asyncio.get_running_loop()
            records_from_html = await loop.run_in_executor(
                None, parse_html_to_records, html_content
            )
            if not records_from_html:
                raise ValueError("未能从HTML内容中解析出记录。")
            
            logger.info(f"✅ 成功从HTML解析了 {len(records_from_html)} 条记录。")

            # 3. 数据清洗和类型转换
            final_records = self._clean_and_prepare_records(records_from_html, employee_id)
            if not final_records:
                logger.warning("处理后没有可插入的记录。")
                self.collection_stats["failed_collections"] += 1
                return

            # 4. 将记录插入数据库
            month_code = target_month.strftime("%y%m")
            await self.db_manager.create_partition(month_code)
            inserted_count = await self.db_manager.insert_timeprotab_data(final_records)

            if inserted_count > 0:
                logger.info(f"✅ 数据库插入成功: {inserted_count} 条记录 ({month_str})。")
                self.collection_stats["successful_collections"] += 1
            else:
                logger.error(f"❌ 数据库插入失败 ({month_str})。")
                self.collection_stats["failed_collections"] += 1

        except Exception as e:
            logger.error(f"❌ 用户 '{employee_id}' ({month_str}) 的采集失败: {e}", exc_info=True)
            self.collection_stats["failed_collections"] += 1
            raise
        finally:
            self.collection_stats["total_collections"] += 1
            self.collection_stats["last_collection_time"] = datetime.now()

    def _clean_and_prepare_records(self, records: List[Dict], employee_id: str) -> List[Dict]:
        """对解析出的记录进行数据清洗和类型转换"""
        prepared_records = []
        for record_data in records:
            record_data['employee_id'] = employee_id
            
            # 日期转换
            date_str = record_data.get("日付")
            if date_str:
                try:
                    record_data["日付"] = datetime.strptime(date_str, "%Y/%m/%d").date()
                except (ValueError, TypeError):
                    logger.warning(f"无法解析日期 '{date_str}'，将设为None。")
                    record_data["日付"] = None
            
            # 时间转换
            for key in ["出勤時刻", "退勤時刻", "所定時間", "早出残業", "内深夜残業", "遅刻早退", "休出時間", "出張残業", "外出時間", "戻り時間", "ＭＣ_出勤", "ＭＣ_退勤"]:
                time_str = record_data.get(key)
                if time_str and time_str.strip() not in ['----', '']:
                    try:
                        time_str_cleaned = time_str.replace('当', '').strip()
                        parts = time_str_cleaned.split(':')
                        if len(parts) == 2:
                            record_data[key] = time(int(parts[0]), int(parts[1]))
                        else:
                            record_data[key] = None
                    except (ValueError, TypeError):
                        logger.warning(f"无法解析时间 '{time_str}' (键: {key})，将设为None。")
                        record_data[key] = None
                else:
                    record_data[key] = None
            
            prepared_records.append(record_data)
        return prepared_records

    async def _fetch_timepro_data_async(self, user_id: str, password: str, target_month: datetime) -> Optional[str]:
        """异步调用timepro.py从网站获取HTML内容"""
        logger.info(f"🌐 正在调用timepro.py获取 {target_month.strftime('%Y%m')} 的数据, 用户: {user_id}")
        
        loop = asyncio.get_running_loop()
        try:
            # 使用新的、支持指定月份并返回HTML的函数
            html_content = await loop.run_in_executor(
                None,  # 使用默认的ThreadPoolExecutor
                timepro.fetch_specific_month_html,
                user_id,
                password,
                target_month.year,
                target_month.month
            )
            
            if html_content:
                logger.info("✅ timepro.py 数据获取成功。")
                return html_content
            else:
                logger.error(f"❌ timepro.py 数据获取失败，未返回HTML内容。")
                return None
        except Exception as e:
            logger.error(f"❌ 调用timepro.py时发生异常: {e}", exc_info=True)
            return None

    # ... (其他辅助函数，如get_status)
    async def get_status(self) -> Dict:
        """获取采集器当前状态"""
        db_status = await self.db_manager.get_status() if self.db_manager else {}
        return {
            "is_running": self.is_running,
            "user_configs_count": len(self.user_configs),
            "collection_stats": self.collection_stats,
            "database_status": db_status
        } 