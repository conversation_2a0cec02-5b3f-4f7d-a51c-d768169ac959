import requests
import os
from bs4 import BeautifulSoup
from typing import Optional

# 2025/07/03 + 17:30 + 移除对status的依赖，直接使用正确的URL
# import status

# 使用正确的URL
BASE_URL = "http://timepro.mst1.mitsuiseiki.co.jp"

# Initialize Session
session = requests.Session()

def fetch_specific_month_html(login_id: str, password_val: str, year: int, month: int) -> Optional[str]:
    """
    登录TimePro，导航到指定年月的月度报告，并返回页面HTML内容。
    此函数在内存中操作，不写入文件。

    Args:
        login_id (str): 员工登录ID。
        password_val (str): 员工密码。
        year (int): 目标年份。
        month (int): 目标月份。

    Returns:
        Optional[str]: 成功则返回HTML内容字符串，否则返回None。
    """
    # 此处省略了登录和导航到月度报告页面的详细步骤，
    # 假设这些步骤与 fetch_timepro_data 中的逻辑相同。
    # 为保持简洁，我们直接进入获取特定月份数据的步骤。
    
    session = requests.Session() # 为确保线程安全和状态隔离，每次调用都创建新会话

    # Step 1 & 2: 登录 (与原函数相同)
    login_page_url = f"{BASE_URL}/xgweb/login.asp"
    session.get(login_page_url, timeout=10)
    login_payload = {'PAGESTATUS': 'REFER', 'ERRBACK': '', 'LoginID': login_id, 'PassWord': password_val, 'PROSESS': 'REFER'}
    headers_post = {"User-Agent": "Mozilla/5.0", "Content-Type": "application/x-www-form-urlencoded", "Referer": login_page_url}
    login_response = session.post(f"{BASE_URL}/xgweb/Login.asp", data=login_payload, headers=headers_post, allow_redirects=False, timeout=10)
    if not (login_response.status_code == 302 and "frame.asp" in login_response.headers.get("location", "")):
        print("Login failed.")
        return None

    # Step 3: 导航到月度报告页面
    frame_url = f"{BASE_URL}/xgweb/frame.asp"
    session.get(frame_url, headers={"Referer": login_page_url}, timeout=10)
    monthly_report_params = {'Title': '%8FA%8B%C6%8FT%95%F1%81E%8C%8E%95%F1', 'MainMenuNo': '1', 'SubMenuNo': '5', 'OptionMenuNo': '0', 'Arg0': '11', 'Option4': '1', 'Option5': '1', 'OpeLevel': '1', 'MenuFlg': '1'}
    monthly_report_url = f"{BASE_URL}/xgweb/page/Xaw1500.asp"
    monthly_report_response = session.get(monthly_report_url, params=monthly_report_params, headers={"Referer": frame_url}, timeout=10)
    
    # Step 4: 获取指定月份数据
    year_month_str = f"{year}{month:02d}"
    personal_info_payload = {'YmdStart': '0', 'YmdEnd': '0', 'YYYYMM': year_month_str, 'EmpCode': f'*{login_id}', 'Outsflg': '', 'RetURL': ''}
    personal_info_params = {'AddEmpFlg': '1', 'MainMenuNo': '1', 'SubMenuNo': '5', 'Arg0': '11', 'ReLoad': '1', 'Option0': '0', 'Option4': '1', 'Option5': '1'}
    personal_info_url = f"{BASE_URL}/xgweb/page/Xaw1500.asp"

    try:
        response = session.post(personal_info_url, params=personal_info_params, data=personal_info_payload, headers={"Content-Type": "application/x-www-form-urlencoded", "Referer": monthly_report_response.url}, timeout=10)
        if response.status_code == 200:
            page_content = response.content.decode("shift_jis", errors='ignore')
            soup = BeautifulSoup(page_content, 'html.parser')
            if soup.body:
                # 成功获取，返回body部分的HTML字符串
                return str(soup.body)
            else:
                print(f"找不到 {year_month_str} 页面的 <body> 标签。")
                return None
        else:
            print(f"获取 {year_month_str} 数据失败。状态码: {response.status_code}")
            return None
    except requests.exceptions.RequestException as e:
        print(f"提取 {year_month_str} 数据时发生错误: {e}")
        return None


def fetch_timepro_data(login_id: str, password_val: str, output_dir: str):
    """
    Logs into TimePro, navigates to the monthly report for a specific employee,
    and saves the HTML content of the employee's personal page.

    Args:
        login_id (str): The employee's login ID.
        password_val (str): The employee's password.
        output_dir (str): The directory where 'body_content.html' will be saved.
    
    Returns:
        bool: True if successful, False otherwise.
        str: A message indicating the outcome.
    """
    global session # Use the global session

    # Ensure output directory exists
    if not os.path.exists(output_dir):
        os.makedirs(output_dir)
    output_filename = os.path.join(output_dir, "body_content.html")
       # html_input_path = 'F:/2025/tensors/testconda/test1 (3rd copy)/tome/2/link/body_content2.html' 

    # Re-initialize session for each call to ensure clean state, or manage session state carefully.
    # For simplicity in this refactoring, we'll use the global session but a fresh one per call might be more robust.
    # session = requests.Session() # Option: new session per call

    # Step one: Visit login page to get initial cookies
    login_page_url = f"{BASE_URL}/xgweb/login.asp"
    try:
        session.get(login_page_url, timeout=10)
    except requests.exceptions.RequestException as e:
        return False, f"Error accessing login page: {e}"

    # Step two: Submit login information
    login_payload = {
        'PAGESTATUS': 'REFER',
        'ERRBACK': '',
        'LoginID': login_id,
        'PassWord': password_val,
        'PROSESS': 'REFER'
    }

    headers_post = {
        "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64)",
        "Content-Type": "application/x-www-form-urlencoded",
        "Referer": login_page_url
    }

    try:
        login_response = session.post(
            f"{BASE_URL}/xgweb/Login.asp",
            data=login_payload,
            headers=headers_post,
            allow_redirects=False,
            timeout=10
        )
    except requests.exceptions.RequestException as e:
        return False, f"Error during login POST: {e}"

    if not (login_response.status_code == 302 and "frame.asp" in login_response.headers.get("location", "")):
        return False, "Login failed. Please check credentials or system status."

    # print("✅ Login successful, proceeding to main page")

    # Enter main page
    frame_url = f"{BASE_URL}/xgweb/frame.asp"
    try:
        session.get(frame_url, headers={"Referer": login_page_url}, timeout=10)
    except requests.exceptions.RequestException as e:
        return False, f"Error accessing frame page: {e}"

    # Step three: Click "Monthly Report" to go to interface C
    monthly_report_params = {
        'Title': '%8FA%8B%C6%8FT%95%F1%81E%8C%8E%95%F1', # Monthly Report / Weekly Report
        'MainMenuNo': '1',
        'SubMenuNo': '5',
        'OptionMenuNo': '0',
        'Arg0': '11',
        'Option4': '1',
        'Option5': '1',
        'OpeLevel': '1',
        'MenuFlg': '1'
    }
    monthly_report_url = f"{BASE_URL}/xgweb/page/Xaw1500.asp"
    try:
        monthly_report_response = session.get(
            monthly_report_url,
            params=monthly_report_params,
            headers={"Referer": frame_url},
            timeout=10
        )
    except requests.exceptions.RequestException as e:
        return False, f"Error accessing monthly report page: {e}"

    if monthly_report_response.status_code != 200:
        return False, "Could not access monthly report page."
    # print("✅ Accessed monthly report page (Interface C)")

    # Step four: Open employee selection window (Interface D)
    emp_select_params = {
        'MainMenuNo': '1',
        'SubMenuNo': '5',
        'ProgramID': 'Xa1500',
        'OptionNo': '11',
        'OperatorID': login_id, # Use login_id
        'Multiple': '50',
        'SystemType': '1',
        'Retire': '2',
        'TaxRegulation': '1',
        'Insurance': '1',
        'BonusSim': '1',
        'Report': '1',
        'Enter': '1',
        'DepMode': '1',
        'Route': '0',
        'YYYYMM': '0',
        'AllFlg': '0',
        'EmpSelWidth': '760',
        'EmpSelHeight': '515',
        'WindowTitle': '%u5C31%u696D%u9031%u5831%u30FB%u6708%u5831' # Attendance Weekly/Monthly Report
    }
    emp_select_url = f"{BASE_URL}/xgweb/page/XgwSelectempfrm.asp"
    try:
        emp_select_response = session.get(
            emp_select_url,
            params=emp_select_params,
            headers={"Referer": monthly_report_response.url},
            timeout=10
        )
    except requests.exceptions.RequestException as e:
        return False, f"Error opening employee selection window: {e}"

    if emp_select_response.status_code != 200:
        return False, "Could not open employee selection window."
    # print("✅ Opened employee selection window (Interface D)")

    # Step five: Select employee and go to final personal page (Interface E)
    # Assuming YYYYMM should be current or a relevant month. For simplicity, let's use a fixed one or make it a parameter.
    # For now, using a placeholder or last used value from original script.
    # A more robust solution might involve getting the current year/month.
    
        
    # 修改点1: 获取当前月份和上个月份的日期
    from datetime import datetime, timedelta

    current_year_month = datetime.now().strftime("%Y%m")  # 当前月份
    last_month = datetime.now().replace(day=1) - timedelta(days=1)  # 上个月的最后一天
    last_year_month = last_month.strftime("%Y%m")  # 上个月份


    # 修改点3: 同时提取当月和上个月的信息
    fetch_and_save(session, current_year_month, login_id, monthly_report_response, 
                os.path.join(output_dir, f"{current_year_month}_body_content.html"))

    fetch_and_save(session, last_year_month, login_id, monthly_report_response, 
                os.path.join(output_dir, f"{last_year_month}_body_content.html"))

    return True, f"{current_year_month} と {last_year_month} のデータを抽出して保存しました。"

    #return True, f"提取并保存了 {current_year_month} 和 {last_year_month} 的数据"

    if personal_info_response.status_code == 200:
        # print("Successfully fetched personal info page data")
        try:
            personal_page_content = personal_info_response.content.decode("shift_jis", errors='ignore')
            soup = BeautifulSoup(personal_page_content, 'html.parser')
            if soup.body:
                with open(output_filename, "w", encoding="utf-8") as f:
                    f.write(soup.body.prettify())
                return True, f"Body content saved to {output_filename}"
            else:
                return False, "Could not find a <body> tag in the HTML content."
        except Exception as e:
            return False, f"Error processing or saving personal page content: {e}"
    else:
        return False, f"Failed to get personal info page data. Status: {personal_info_response.status_code}"
# 修改点2: 新增函数，专门用来提取指定月份的信息并保存到指定文件
def fetch_and_save(session, year_month, login_id, monthly_report_response, output_filename):
    personal_info_payload = {
        'YmdStart': '0',
        'YmdEnd': '0',
        'YYYYMM': year_month,
        'EmpCode': f'*{login_id}',
        'Outsflg': '',
        'RetURL': ''
    }
    personal_info_params = {
        'AddEmpFlg': '1',
        'MainMenuNo': '1',
        'SubMenuNo': '5',
        'Arg0': '11',
        'ReLoad': '1',
        'Option0': '0',
        'Option4': '1',
        'Option5': '1'
    }
    personal_info_url = f"{BASE_URL}/xgweb/page/Xaw1500.asp"

    try:
        response = session.post(
            personal_info_url,
            params=personal_info_params,
            data=personal_info_payload,
            headers={
                "Content-Type": "application/x-www-form-urlencoded",
                "Referer": monthly_report_response.url
            },
            timeout=10
        )
        if response.status_code == 200:
            page_content = response.content.decode("shift_jis", errors='ignore')
            soup = BeautifulSoup(page_content, 'html.parser')
            if soup.body:
                with open(output_filename, "w", encoding="utf-8") as f:
                    f.write(soup.body.prettify())
                print(f"✅ 成功提取{year_month}的数据到 {output_filename}")
            else:
                print(f"❌ {year_month}的数据提取失败，找不到<body>标签")
        else:
            print(f"❌ 请求失败，状态码: {response.status_code}")
    except requests.exceptions.RequestException as e:
        print(f"❌ 提取{year_month}数据时发生错误: {e}")


if __name__ == "__main__":
    # Example usage:
    test_login_id = "123456"  # Replace with a valid test ID
    test_password = "pass123"    # Replace with a valid test password
    test_output_dir = "timepro_test_output"
    
    print(f"Attempting to fetch data for ID: {test_login_id} into dir: {test_output_dir}")
    success, message = fetch_timepro_data(test_login_id, test_password, test_output_dir)
    
    if success:
        print(f"✅ Success: {message}")
    else:
        print(f"❌ Failure: {message}")

