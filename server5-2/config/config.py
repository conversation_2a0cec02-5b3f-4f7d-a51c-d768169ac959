# server5-2/config/config.py
# 2025/07/03 + 16:30 + timepro数据采集服务配置（真实采集配置）

import os
from pathlib import Path
import logging

# 多平台数据库配置
DB_HOST = os.getenv("DB_HOST", "************")
DB_PORT = int(os.getenv("DB_PORT", "5432"))
DB_NAME = os.getenv("DB_NAME", "imdb") 
DB_USER = os.getenv("DB_USER", "postgres")
DB_PASS = os.getenv("DB_PASS", "pojiami0602")

# 构建数据库连接URL
DATABASE_URL = f"postgresql://{DB_USER}:{DB_PASS}@{DB_HOST}:{DB_PORT}/{DB_NAME}"

# timepro网站配置
TIMEPRO_CONFIG = {
    "base_url":  "http://timepro.mst1.mitsuiseiki.co.jp",  # 实际的timepro网站URL
    "timeout": 30,
    "retry_delay": 5,  # 操作间隔秒数
    "max_retries": 3
}

# 用户账户配置 - 从用户注册数据集获取
TIMEPRO_USERS = [
    {
        "user_id": "215829",  # 使用员工ID作为网站登录ID
        "password": "jiaban01",  # 使用实际密码
        "employee_id": "215829",
        "description": "主要采集用户 - 从用户注册数据集获取",
        "scheduled_collection": True  # 参与每日自动采集
    },
    {
        "user_id": "200001",  # 备用用户ID
        "password": "password002",  # 备用密码
        "employee_id": "200001",
        "description": "备用采集用户",
        "scheduled_collection": False # 不参与每日自动采集
    },
    {
        "user_id": "testuser",
        "password": "testpassword",
        "employee_id": "999999",
        "description": "用于测试的用户",
        "scheduled_collection": True  # 参与每日自动采集
    },
    {
        "user_id": "another_user",
        "password": "another_password",
        "employee_id": "123456",
        "description": "另一个业务用户",
        "scheduled_collection": False # 不参与每日自动采集
    }
]

# 任务调度配置
SCHEDULE_CONFIG = {
    "daily_hour": 1,  # 凌晨1点开始
    "user_interval_minutes": 30,  # 用户间隔30分钟
    "operation_delay_seconds": 10,  # 操作间延迟10秒
    "error_retry_delay_minutes": 60  # 错误后重试延迟1小时
}

# 文件存储配置
DATA_STORAGE = {
    "html_dir": Path("data/html_files"),
    "xml_dir": Path("data/xml_files"), 
    "log_dir": Path("logs"),
    "backup_dir": Path("data/backup")
}

# 确保目录存在
for dir_path in DATA_STORAGE.values():
    dir_path.mkdir(parents=True, exist_ok=True)

# timeprotab分区表配置
TIMEPROTAB_CONFIG = {
    "base_table_name": "timeprotab",
    "partition_format": "timeprotab_{month_code}",  # timeprotab_2505
    "auto_create_partitions": True,
    "retention_months": 24  # 保留24个月的数据
}

# 日志配置
LOGGING_CONFIG = {
    "version": 1,
    "disable_existing_loggers": False,
    "formatters": {
        "detailed": {
            "format": "%(asctime)s - %(name)s - %(levelname)s - %(message)s"
        }
    },
    "handlers": {
        "console": {
            "class": "logging.StreamHandler",
            "level": "INFO",
            "formatter": "detailed"
        },
        "file": {
            "class": "logging.FileHandler",
            "filename": "logs/server5-2.log",
            "level": "DEBUG", 
            "formatter": "detailed"
        }
    },
    "loggers": {
        "server5-2": {
            "level": "DEBUG",
            "handlers": ["console", "file"],
            "propagate": False
        }
    }
} 