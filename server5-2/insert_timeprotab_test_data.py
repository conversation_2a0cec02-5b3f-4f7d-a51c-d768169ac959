#!/usr/bin/env python3
# server5-2/insert_timeprotab_test_data.py
# 2025/07/03 + 16:25 + 插入测试数据到timeprotab表

import asyncio
import asyncpg
import sys
from pathlib import Path
from datetime import datetime, date

sys.path.append(str(Path(__file__).parent))
from config.config import DATABASE_URL

TEST_DATA = [
    {
        "employee_id": "215829",
        "日付": "2025-07-01",
        "星期": "火",
        "ｶﾚﾝﾀﾞ": "平日",
        "不在": "",
        "勤務区分": "平常勤務-8-0",
        "事由": "",
        "出勤時刻": "08:30",
        "ＭＣ_出勤": "",
        "退勤時刻": "17:30",
        "ＭＣ_退勤": "",
        "所定時間": "8:00",
        "早出残業": "0:00",
        "内深夜残業": "0:00",
        "遅刻早退": "0:00",
        "休出時間": "0:00",
        "出張残業": "0:00",
        "外出時間": None,
        "戻り時間": None,
        "コメント": "2025/07/03 + 16:25 + 测试数据 - 实际用户215829"
    },
    {
        "employee_id": "215829",
        "日付": "2025-07-02",
        "星期": "水",
        "ｶﾚﾝﾀﾞ": "平日",
        "不在": "",
        "勤務区分": "平常勤務-8-0",
        "事由": "",
        "出勤時刻": "09:00",
        "ＭＣ_出勤": "",
        "退勤時刻": "18:00",
        "ＭＣ_退勤": "",
        "所定時間": "8:00",
        "早出残業": "0:00",
        "内深夜残業": "0:00",
        "遅刻早退": "0:00",
        "休出時間": "0:00",
        "出張残業": "0:00",
        "外出時間": None,
        "戻り時間": None,
        "コメント": "2025/07/03 + 16:25 + 测试数据2 - 实际用户215829"
    },
    {
        "employee_id": "215829",
        "日付": "2025-06-30",
        "星期": "月",
        "ｶﾚﾝﾀﾞ": "平日",
        "不在": "",
        "勤務区分": "平常勤務-8-0",
        "事由": "",
        "出勤時刻": "08:00",
        "ＭＣ_出勤": "",
        "退勤時刻": "17:00",
        "ＭＣ_退勤": "",
        "所定時間": "8:00",
        "早出残業": "0:00",
        "内深夜残業": "0:00",
        "遅刻早退": "0:00",
        "休出時間": "0:00",
        "出張残業": "0:00",
        "外出時間": None,
        "戻り時間": None,
        "コメント": "2025/07/03 + 16:25 + 上个月测试数据 - 实际用户215829"
    }
]

async def insert_test_data():
    """插入测试数据"""
    print("🟢 2025/07/03 + 16:25 + 开始插入测试数据...")
    
    try:
        conn = await asyncpg.connect(DATABASE_URL)
        try:
            inserted_count = 0
            
            for record in TEST_DATA:
                # 构建INSERT语句
                columns = list(record.keys())
                placeholders = [f"${i+1}" for i in range(len(columns))]
                
                sql = f"""
                INSERT INTO timeprotab ({', '.join(columns)})
                VALUES ({', '.join(placeholders)})
                ON CONFLICT (id, 日付) DO NOTHING;
                """
                
                # 准备数据
                values = []
                for col in columns:
                    value = record[col]
                    if isinstance(value, str) and value == "":
                        value = None
                    elif col in ["日付"] and isinstance(value, str):
                        value = datetime.strptime(value, "%Y-%m-%d").date()
                    elif col in ["出勤時刻", "退勤時刻", "外出時間", "戻り時間"] and value:
                        if isinstance(value, str):
                            value = datetime.strptime(value, "%H:%M").time()
                    values.append(value)
                
                # 执行插入
                result = await conn.execute(sql, *values)
                if "INSERT" in result:
                    inserted_count += 1
                    print(f"  ✅ 2025/07/03 + 16:25 + 插入记录: {record['employee_id']} - {record['日付']}")
                else:
                    print(f"  ⚠️ 2025/07/03 + 16:25 + 记录已存在: {record['employee_id']} - {record['日付']}")
            
            print(f"✅ 2025/07/03 + 16:25 + 测试数据插入完成，成功插入 {inserted_count} 条记录")
            
            # 验证数据
            count = await conn.fetchval("SELECT COUNT(*) FROM timeprotab WHERE employee_id = $1", "215829")
            print(f"📊 2025/07/03 + 16:25 + 验证：employee_id=215829 的记录总数: {count}")
            
        finally:
            await conn.close()
    except Exception as e:
        print(f"❌ 2025/07/03 + 16:25 + 插入测试数据失败: {e}")
        return False
    return True

if __name__ == "__main__":
    asyncio.run(insert_test_data()) 