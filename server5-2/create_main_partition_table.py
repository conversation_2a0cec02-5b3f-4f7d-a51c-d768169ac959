#!/usr/bin/env python3
# server5-2/create_main_partition_table.py
# 2025/07/03 + 16:20 + 创建主分区表timeprotab

import asyncio
import asyncpg
import sys
from pathlib import Path

sys.path.append(str(Path(__file__).parent))
from config.config import DATABASE_URL

CREATE_MAIN_TABLE_SQL = '''
CREATE TABLE IF NOT EXISTS timeprotab (
    id SERIAL,
    employee_id VARCHAR(20) NOT NULL,
    日付 DATE NOT NULL,
    星期 VARCHAR(10),
    ｶﾚﾝﾀﾞ VARCHAR(20),
    不在 VARCHAR(50),
    勤務区分 VARCHAR(50),
    事由 VARCHAR(100),
    出勤時刻 TIME,
    ＭＣ_出勤 VARCHAR(10),
    退勤時刻 TIME,
    ＭＣ_退勤 VARCHAR(10),
    所定時間 VARCHAR(20),
    早出残業 VARCHAR(20),
    内深夜残業 VARCHAR(20),
    遅刻早退 VARCHAR(20),
    休出時間 VARCHAR(20),
    出張残業 VARCHAR(20),
    外出時間 TIME,
    戻り時間 TIME,
    コメント TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    PRIMARY KEY (id, 日付)
) PARTITION BY RANGE (日付);
'''

async def main():
    print("🟢 2025/07/03 + 16:20 + 开始创建主分区表timeprotab...")
    try:
        conn = await asyncpg.connect(DATABASE_URL)
        try:
            await conn.execute(CREATE_MAIN_TABLE_SQL)
            print("✅ 2025/07/03 + 16:20 + 主分区表timeprotab创建成功")
        finally:
            await conn.close()
    except Exception as e:
        print(f"❌ 2025/07/03 + 16:20 + 创建主分区表失败: {e}")
        sys.exit(1)

if __name__ == "__main__":
    asyncio.run(main()) 