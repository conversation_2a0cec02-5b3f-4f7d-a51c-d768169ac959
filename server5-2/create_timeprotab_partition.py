#!/usr/bin/env python3
# server5-2/create_timeprotab_partition.py
# 2025/07/03 + 16:20 + 创建指定月份分区

import asyncio
import asyncpg
import sys
from pathlib import Path
from datetime import datetime

sys.path.append(str(Path(__file__).parent))
from config.config import DATABASE_URL

async def create_partition(month_code: str):
    if len(month_code) != 4 or not month_code.isdigit():
        print(f"❌ 2025/07/03 + 16:20 + 无效的月份代码: {month_code}")
        return False
    year = 2000 + int(month_code[:2])
    month = int(month_code[2:])
    start_date = datetime(year, month, 1).date()
    if month == 12:
        end_date = datetime(year + 1, 1, 1).date()
    else:
        end_date = datetime(year, month + 1, 1).date()
    partition_name = f"timeprotab_{month_code}"
    sql = f"""
    CREATE TABLE IF NOT EXISTS {partition_name} PARTITION OF timeprotab
    FOR VALUES FROM ('{start_date}') TO ('{end_date}');
    """
    print(f"🟢 2025/07/03 + 16:20 + 创建分区: {partition_name} ({start_date} ~ {end_date})")
    try:
        conn = await asyncpg.connect(DATABASE_URL)
        try:
            await conn.execute(sql)
            print(f"✅ 2025/07/03 + 16:20 + 分区 {partition_name} 创建成功")
        finally:
            await conn.close()
    except Exception as e:
        print(f"❌ 2025/07/03 + 16:20 + 分区 {partition_name} 创建失败: {e}")
        sys.exit(1)

if __name__ == "__main__":
    if len(sys.argv) != 2:
        print("用法: python create_timeprotab_partition.py <月份代码>")
        print("示例: python create_timeprotab_partition.py 2506")
        sys.exit(1)
    asyncio.run(create_partition(sys.argv[1])) 