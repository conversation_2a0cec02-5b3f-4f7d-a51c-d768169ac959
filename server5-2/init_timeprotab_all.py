#!/usr/bin/env python3
# server5-2/init_timeprotab_all.py
# 2025/07/03 + 16:25 + 主控脚本：按顺序执行所有timeprotab初始化步骤

import asyncio
import sys
import subprocess
from pathlib import Path

def run_script(script_name: str, description: str, args: list = None):
    """运行指定的Python脚本"""
    print(f"\n{'='*60}")
    print(f"🟢 2025/07/03 + 16:25 + {description}")
    print(f"{'='*60}")
    
    script_path = Path(__file__).parent / script_name
    if not script_path.exists():
        print(f"❌ 2025/07/03 + 16:25 + 脚本不存在: {script_path}")
        return False
    
    cmd = [sys.executable, str(script_path)]
    if args:
        cmd.extend(args)
    
    try:
        result = subprocess.run(cmd, capture_output=True, text=True, cwd=Path(__file__).parent)
        
        # 输出脚本的执行结果
        if result.stdout:
            print(result.stdout)
        if result.stderr:
            print(f"⚠️ 2025/07/03 + 16:25 + 脚本警告: {result.stderr}")
        
        if result.returncode == 0:
            print(f"✅ 2025/07/03 + 16:25 + {description} 执行成功")
            return True
        else:
            print(f"❌ 2025/07/03 + 16:25 + {description} 执行失败 (返回码: {result.returncode})")
            return False
            
    except Exception as e:
        print(f"❌ 2025/07/03 + 16:25 + 执行脚本异常: {e}")
        return False

def main():
    """主函数：按顺序执行所有步骤"""
    print("🚀 2025/07/03 + 16:25 + 开始执行timeprotab完整初始化流程")
    print("=" * 80)
    
    steps = [
        ("create_main_partition_table.py", "创建主分区表timeprotab"),
        ("create_timeprotab_partition.py", "创建6月分区 (timeprotab_2506)", ["2506"]),
        ("create_timeprotab_partition.py", "创建7月分区 (timeprotab_2507)", ["2507"]),
        ("create_timeprotab_triggers.py", "创建触发器函数和触发器"),
        ("create_timeprotab_indexes.py", "为所有分区创建索引"),
        ("insert_timeprotab_test_data.py", "插入测试数据"),
    ]
    
    success_count = 0
    total_steps = len(steps)
    
    for i, (script, description, *args) in enumerate(steps, 1):
        print(f"\n📋 2025/07/03 + 16:25 + 步骤 {i}/{total_steps}: {description}")
        
        if run_script(script, description, args[0] if args else None):
            success_count += 1
        else:
            print(f"❌ 2025/07/03 + 16:25 + 步骤 {i} 失败，是否继续？(y/n): ", end="")
            try:
                response = input().strip().lower()
                if response != 'y':
                    print("🛑 2025/07/03 + 16:25 + 用户选择停止执行")
                    break
            except KeyboardInterrupt:
                print("\n🛑 2025/07/03 + 16:25 + 用户中断执行")
                break
    
    # 输出最终结果
    print(f"\n{'='*80}")
    print(f"📊 2025/07/03 + 16:25 + 执行结果汇总")
    print(f"{'='*80}")
    print(f"✅ 成功步骤: {success_count}/{total_steps}")
    print(f"❌ 失败步骤: {total_steps - success_count}")
    
    if success_count == total_steps:
        print("🎉 2025/07/03 + 16:25 + 所有步骤执行成功！timeprotab初始化完成")
        return 0
    else:
        print("⚠️ 2025/07/03 + 16:25 + 部分步骤失败，请检查错误信息")
        return 1

if __name__ == "__main__":
    try:
        exit_code = main()
        sys.exit(exit_code)
    except KeyboardInterrupt:
        print("\n👋 2025/07/03 + 16:25 + 用户中断执行")
        sys.exit(0)
    except Exception as e:
        print(f"❌ 2025/07/03 + 16:25 + 程序异常: {e}")
        sys.exit(1) 