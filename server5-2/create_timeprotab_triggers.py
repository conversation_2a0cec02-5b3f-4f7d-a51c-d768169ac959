#!/usr/bin/env python3
# server5-2/create_timeprotab_triggers.py
# 2025/07/03 + 16:25 + 为timeprotab表创建触发器

import asyncio
import asyncpg
import sys
from pathlib import Path

sys.path.append(str(Path(__file__).parent))
from config.config import DATABASE_URL

CREATE_TRIGGER_FUNCTION_SQL = '''
CREATE OR REPLACE FUNCTION update_timeprotab_updated_at()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = CURRENT_TIMESTAMP;
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;
'''

CREATE_TRIGGER_SQL = '''
DROP TRIGGER IF EXISTS trigger_update_timeprotab_updated_at ON timeprotab;
CREATE TRIGGER trigger_update_timeprotab_updated_at
    BEFORE UPDATE ON timeprotab
    FOR EACH ROW
    EXECUTE FUNCTION update_timeprotab_updated_at();
'''

async def create_triggers():
    """创建触发器函数和触发器"""
    print("🟢 2025/07/03 + 16:25 + 开始创建触发器...")
    
    try:
        conn = await asyncpg.connect(DATABASE_URL)
        try:
            # 创建触发器函数
            print("🟢 2025/07/03 + 16:25 + 创建触发器函数...")
            await conn.execute(CREATE_TRIGGER_FUNCTION_SQL)
            print("✅ 2025/07/03 + 16:25 + 触发器函数创建成功")
            
            # 创建触发器
            print("🟢 2025/07/03 + 16:25 + 创建触发器...")
            await conn.execute(CREATE_TRIGGER_SQL)
            print("✅ 2025/07/03 + 16:25 + 触发器创建成功")
            
            print("✅ 2025/07/03 + 16:25 + 所有触发器创建完成")
            
        finally:
            await conn.close()
    except Exception as e:
        print(f"❌ 2025/07/03 + 16:25 + 创建触发器失败: {e}")
        return False
    return True

if __name__ == "__main__":
    asyncio.run(create_triggers()) 