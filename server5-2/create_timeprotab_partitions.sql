-- server5-2/create_timeprotab_partitions.sql
-- 2025/07/03 + 16:10 + 创建timeprotab分区表SQL脚本

-- 2025/07/03 + 16:10 + 连接到正确的数据库
-- 注意：此命令需要在psql客户端中执行，在Python脚本中会自动连接到配置的数据库

-- 2025/07/03 + 16:10 + 创建基础分区表
CREATE TABLE IF NOT EXISTS timeprotab (
    id SERIAL,
    employee_id VARCHAR(20) NOT NULL,
    日付 DATE NOT NULL,
    星期 VARCHAR(10),
    ｶﾚﾝﾀﾞ VARCHAR(20),
    不在 VARCHAR(50),
    勤務区分 VARCHAR(50),
    事由 VARCHAR(100),
    出勤時刻 TIME,
    ＭＣ_出勤 VARCHAR(10),
    退勤時刻 TIME,
    ＭＣ_退勤 VARCHAR(10),
    所定時間 VARCHAR(20),
    早出残業 VARCHAR(20),
    内深夜残業 VARCHAR(20),
    遅刻早退 VARCHAR(20),
    休出時間 VARCHAR(20),
    出張残業 VARCHAR(20),
    外出時間 TIME,
    戻り時間 TIME,
    コメント TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    PRIMARY KEY (id, 日付)
) PARTITION BY RANGE (日付);

-- 2025/07/03 + 16:10 + 创建更新时间触发器函数
CREATE OR REPLACE FUNCTION update_timeprotab_updated_at()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = CURRENT_TIMESTAMP;
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- 2025/07/03 + 16:10 + 创建触发器
DROP TRIGGER IF EXISTS trigger_update_timeprotab_updated_at ON timeprotab;
CREATE TRIGGER trigger_update_timeprotab_updated_at
    BEFORE UPDATE ON timeprotab
    FOR EACH ROW
    EXECUTE FUNCTION update_timeprotab_updated_at();

-- 2025/07/03 + 16:10 + 创建当月分区 (2025年7月)
CREATE TABLE IF NOT EXISTS timeprotab_2507 PARTITION OF timeprotab
FOR VALUES FROM ('2025-07-01') TO ('2025-08-01');

-- 2025/07/03 + 16:10 + 创建上个月分区 (2025年6月)
CREATE TABLE IF NOT EXISTS timeprotab_2506 PARTITION OF timeprotab
FOR VALUES FROM ('2025-06-01') TO ('2025-07-01');

-- 2025/07/03 + 16:10 + 为分区创建索引
CREATE INDEX IF NOT EXISTS idx_timeprotab_2507_employee_date 
ON timeprotab_2507 (employee_id, 日付);

CREATE INDEX IF NOT EXISTS idx_timeprotab_2507_date 
ON timeprotab_2507 (日付);

CREATE INDEX IF NOT EXISTS idx_timeprotab_2506_employee_date 
ON timeprotab_2506 (employee_id, 日付);

CREATE INDEX IF NOT EXISTS idx_timeprotab_2506_date 
ON timeprotab_2506 (日付);

-- 2025/07/03 + 16:10 + 插入测试数据
INSERT INTO timeprotab (
    employee_id, 日付, 星期, ｶﾚﾝﾀﾞ, 勤務区分, 出勤時刻, 退勤時刻, 所定時間, コメント
) VALUES 
('215829', '2025-07-01', '火', '平日', '平常勤務-8-0', '08:30', '17:30', '8:00', '2025/07/03 + 16:10 + 测试数据'),
('215829', '2025-07-02', '水', '平日', '平常勤務-8-0', '09:00', '18:00', '8:00', '2025/07/03 + 16:10 + 测试数据'),
('215829', '2025-06-30', '月', '平日', '平常勤務-8-0', '08:00', '17:00', '8:00', '2025/07/03 + 16:10 + 上个月测试数据')
ON CONFLICT (id, 日付) DO NOTHING;

-- 2025/07/03 + 16:10 + 验证分区创建
SELECT 
    schemaname,
    tablename,
    partitiontablename,
    partitionrangestart,
    partitionrangeend
FROM pg_partitions 
WHERE tablename = 'timeprotab'
ORDER BY partitionrangestart;

-- 2025/07/03 + 16:10 + 验证数据插入
SELECT 
    employee_id,
    日付,
    星期,
    勤務区分,
    出勤時刻,
    退勤時刻,
    コメント
FROM timeprotab 
WHERE employee_id = '215829'
ORDER BY 日付 DESC
LIMIT 5; 