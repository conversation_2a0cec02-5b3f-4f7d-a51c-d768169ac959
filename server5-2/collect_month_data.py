# server5-2/collect_month_data.py
# 2025/07/03 + 16:30 + 指定月份数据采集工具

import asyncio
import logging
import logging.config
import sys
import argparse
from datetime import datetime
from pathlib import Path

# 添加项目路径
sys.path.append(str(Path(__file__).parent))

from config.config import LOGGING_CONFIG
from services.timepro_collector import TimeproCollector
from database.timeprotab_manager import TimeprotabManager

# 配置日志
logging.config.dictConfig(LOGGING_CONFIG)
logger = logging.getLogger("server5-2.collect_month")

class MonthDataCollector:
    """指定月份数据采集工具"""
    
    def __init__(self):
        self.collector = None
        self.db_manager = None
    
    async def setup(self):
        """设置环境"""
        try:
            logger.info("🧪 设置采集环境")
            
            # 初始化数据库管理器
            self.db_manager = TimeprotabManager()
            await self.db_manager.connect()
            await self.db_manager.initialize_base_table()
            
            # 初始化采集器
            self.collector = TimeproCollector()
            
            logger.info("✅ 采集环境设置完成")
            return True
            
        except Exception as e:
            logger.error(f"❌ 采集环境设置失败: {e}")
            return False
    
    async def cleanup(self):
        """清理环境"""
        try:
            if self.db_manager:
                await self.db_manager.disconnect()
            
            logger.info("✅ 采集环境清理完成")
            
        except Exception as e:
            logger.error(f"❌ 采集环境清理失败: {e}")
    
    async def collect_month_data(self, user_id: str, password: str, employee_id: str, year: int, month: int):
        """采集指定月份的数据"""
        try:
            logger.info(f"📅 开始采集 {year}年{month}月 的数据")
            
            # 启动采集器
            await self.collector.start()
            
            # 创建目标月份
            target_month = datetime(year, month, 1)
            month_str = target_month.strftime("%Y%m")
            
            logger.info(f"🔐 用户: {user_id}")
            logger.info(f"📅 目标月份: {month_str}")
            
            # 采集数据
            await self.collector._collect_user_month_data(
                user_id, password, employee_id, target_month
            )
            
            logger.info("✅ 数据采集完成")
            
            # 验证采集的数据
            month_code = target_month.strftime("%y%m")  # "2506"
            collected_data = await self.db_manager.get_month_data(employee_id, month_code)
            
            if collected_data:
                logger.info(f"✅ 验证采集数据成功 ({month_code}): {len(collected_data)} 条记录")
                logger.info("📊 采集记录预览:")
                for i, record in enumerate(collected_data[:10]):  # 显示前10条记录
                    logger.info(f"  {i+1}. {record.get('employee_id')} - {record.get('日付')} - {record.get('出勤時刻')} - {record.get('退勤時刻')} - {record.get('コメント')}")
                
                if len(collected_data) > 10:
                    logger.info(f"  ... 还有 {len(collected_data) - 10} 条记录")
            else:
                logger.warning(f"⚠️ 未找到采集的数据 ({month_code})")
            
            return True
            
        except Exception as e:
            logger.error(f"❌ 数据采集失败: {e}")
            return False
        finally:
            # 停止采集器
            await self.collector.stop()

async def main():
    """主函数"""
    parser = argparse.ArgumentParser(description="指定月份数据采集工具")
    parser.add_argument("--user", default="215829", help="用户ID (默认: 215829)")
    parser.add_argument("--password", default="jiaban01", help="密码 (默认: jiaban01)")
    parser.add_argument("--employee", default="215829", help="员工ID (默认: 215829)")
    parser.add_argument("--year", type=int, required=True, help="年份 (例如: 2025)")
    parser.add_argument("--month", type=int, required=True, help="月份 (1-12)")
    
    args = parser.parse_args()
    
    # 验证月份
    if not (1 <= args.month <= 12):
        logger.error("❌ 月份必须在1-12之间")
        return 1
    
    logger.info("🧪 指定月份数据采集工具启动")
    logger.info("=" * 60)
    logger.info(f"🔐 用户: {args.user}")
    logger.info(f"📅 目标: {args.year}年{args.month}月")
    logger.info("=" * 60)
    
    try:
        collector = MonthDataCollector()
        
        # 设置环境
        if not await collector.setup():
            return 1
        
        # 采集数据
        success = await collector.collect_month_data(
            args.user, args.password, args.employee, args.year, args.month
        )
        
        if success:
            logger.info("🎉 数据采集成功完成！")
            return 0
        else:
            logger.error("❌ 数据采集失败")
            return 1
            
    except Exception as e:
        logger.error(f"❌ 程序异常: {e}")
        return 1
    finally:
        await collector.cleanup()

if __name__ == "__main__":
    try:
        exit_code = asyncio.run(main())
        sys.exit(exit_code)
    except KeyboardInterrupt:
        logger.info("👋 程序被用户中断")
        sys.exit(0)
    except Exception as e:
        logger.error(f"❌ 程序异常退出: {e}")
        sys.exit(1) 