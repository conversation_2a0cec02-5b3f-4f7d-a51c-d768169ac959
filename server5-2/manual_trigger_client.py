import argparse
import json

import requests

# API终结点URL
API_URL = "http://127.0.0.1:8002/collect"

def trigger_collection(year: int, month: int):
    """
    向 server5-2 API 发送POST请求以触发数据采集。

    Args:
        year: 要采集的年份。
        month: 要采集的月份。
    """
    payload = {
        "year": year,
        "month": month,
        # 使用API模型中定义的默认测试用户
        "user_id": "215829",
        "password": "jiaban01",
        "employee_id": "215829"
    }
    
    print(f"🚀 向 {API_URL} 发送请求...")
    print(f"   请求内容: {json.dumps(payload, indent=2)}")

    try:
        # 设置一个较长的超时时间（300秒），因为数据采集可能需要一些时间
        response = requests.post(API_URL, json=payload, timeout=300)
        
        # 检查请求是否成功
        response.raise_for_status()
        
        print("✅ 请求成功!")
        print("   服务器响应:")
        # 使用 ensure_ascii=False 以正确显示中文
        print(json.dumps(response.json(), indent=2, ensure_ascii=False))

    except requests.exceptions.Timeout:
        print("❌ 请求超时。采集过程可能仍在后台运行，请稍后检查数据库或服务状态。")
    except requests.exceptions.RequestException as e:
        print(f"❌ 请求失败: {e}")
        if e.response is not None:
            try:
                print("   服务器返回的错误详情:")
                print(json.dumps(e.response.json(), indent=2, ensure_ascii=False))
            except json.JSONDecodeError:
                print(f"   无法解析的响应内容: {e.response.text}")


if __name__ == "__main__":
    parser = argparse.ArgumentParser(
        description="手动触发 server5-2 数据采集的客户端工具。",
        formatter_class=argparse.RawTextHelpFormatter
    )
    parser.add_argument(
        "--year", 
        type=int, 
        required=True, 
        help="要采集的年份, 例如: 2025"
    )
    parser.add_argument(
        "--month", 
        type=int, 
        required=True, 
        help="要采集的月份, 例如: 6"
    )
    
    args = parser.parse_args()
    
    trigger_collection(args.year, args.month) 