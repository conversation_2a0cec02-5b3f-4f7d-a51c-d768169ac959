# server5-2/test_server5_2.py
# 2025/07/03 + 17:30 + 更新测试用例以适应无文件内存处理流程

import asyncio
import logging
import logging.config
import sys
from pathlib import Path
from datetime import datetime

# 添加项目路径
sys.path.append(str(Path(__file__).parent))

from config.config import LOGGING_CONFIG
from services.timepro_collector import TimeproCollector
from database.timeprotab_manager import TimeprotabManager

# 配置日志
logging.config.dictConfig(LOGGING_CONFIG)
logger = logging.getLogger("server5-2.test")

class Server52Tester:
    """server5-2测试工具（真实、无文件采集）"""
    
    def __init__(self):
        self.db_manager = None
        # 在setup中初始化，以确保db_manager已连接
        self.collector = None
    
    async def setup(self):
        """设置测试环境"""
        try:
            logger.info("🧪 设置测试环境")
            
            # 初始化并连接数据库管理器
            self.db_manager = TimeprotabManager()
            await self.db_manager.connect()

            # 2025/07/03 + 17:30 + 修正：注释掉破坏性的删表操作
            # 以下代码用于在需要重置表结构（例如添加新约束）时强制删除并重建整个表。
            # 在正常测试中应保持注释状态，以避免删除现有分区。
            # logger.info("正在强制删除并重建 timeprotab 表以更新结构...")
            # async with self.db_manager.pool.acquire() as conn:
            #     await conn.execute("DROP TABLE IF EXISTS timeprotab CASCADE;")
            # logger.info("旧表 timeprotab 已删除。")

            await self.db_manager.initialize_base_table()
            
            # 初始化采集器，并传入已连接的db_manager
            self.collector = TimeproCollector(db_manager=self.db_manager)
            
            logger.info("✅ 测试环境设置完成")
            return True
            
        except Exception as e:
            logger.error(f"❌ 测试环境设置失败: {e}", exc_info=True)
            return False
    
    async def cleanup(self):
        """清理测试环境"""
        try:
            if self.collector and self.collector.is_running:
                await self.collector.stop()
            if self.db_manager:
                await self.db_manager.disconnect()
            
            logger.info("✅ 测试环境清理完成")
            
        except Exception as e:
            logger.error(f"❌ 测试环境清理失败: {e}", exc_info=True)
    
    async def test_database_connection(self):
        """测试数据库连接和基本状态"""
        try:
            logger.info("🧪 (1/3) 测试数据库连接")
            status = await self.db_manager.get_status()
            if status.get("status") == "healthy":
                logger.info("✅ 数据库连接和状态获取测试成功")
                return True
            else:
                logger.error(f"❌ 数据库状态获取测试失败: {status}")
                return False
        except Exception as e:
            logger.error(f"❌ 数据库连接测试失败: {e}", exc_info=True)
            return False
            
    async def test_collector_initialization(self):
        """测试采集器初始化和关闭"""
        try:
            logger.info("🧪 (2/3) 测试采集器初始化和关闭")
            
            # 启动采集器
            await self.collector.start()
            if not self.collector.is_running:
                raise RuntimeError("采集器未能成功启动")
            logger.info("✅ 采集器启动成功")
            
            # 停止采集器
            await self.collector.stop()
            if self.collector.is_running:
                raise RuntimeError("采集器未能成功停止")
            logger.info("✅ 采集器停止成功")
            
            return True
        except Exception as e:
            logger.error(f"❌ 采集器初始化测试失败: {e}", exc_info=True)
            return False

    async def test_specific_month_real_collection(self):
        """
        核心测试: 采集指定月份（2025年6月）的真实数据。
        本测试将实际访问网站，使用用户'215829'和密码'jiaban01'。
        """
        try:
            # 2025/07/03 + 17:30 + 修正测试逻辑，确保采集器和DB连接是活动的
            logger.info("🧪 (3/3) 核心测试: 采集2025年6月的真实数据")

            # 确保采集器处于运行状态
            if not self.collector or not self.collector.is_running:
                logger.info("重新启动采集器以进行核心测试...")
                self.collector = TimeproCollector(db_manager=self.db_manager)
                await self.collector.start()

            test_user = {
                "user_id": "215829",
                "password": "jiaban01",
                "employee_id": "215829",
            }
            
            logger.info(f"🔐 测试用户: {test_user['user_id']}")
            
            # 指定要采集的月份 - 2025年6月
            target_month = datetime(2025, 4, 1)
            month_str = target_month.strftime("%Y%m")
            
            logger.info(f"📅 开始采集指定月份: {month_str}")
            
            # 直接调用采集方法
            await self.collector._collect_user_month_data(
                test_user["user_id"],
                test_user["password"],
                test_user["employee_id"],
                target_month
            )
            
            logger.info("✅ 指定月份真实数据采集调用完成")
            
            # 验证采集的数据
            month_code = target_month.strftime("%y%m")  # "2506"
            logger.info(f"🔍 正在从数据库验证 {month_code} 月份的数据...")
            
            # 增加一点延迟，确保数据已提交
            await asyncio.sleep(2)
            
            collected_data = await self.db_manager.get_month_data(test_user["employee_id"], month_code)
            
            if collected_data:
                logger.info(f"🎉 验证成功! 在数据库中找到 {len(collected_data)} 条记录 ({month_code})。")
                logger.info("--- 前5条记录示例 ---")
                for record in collected_data[:5]:
                    logger.info(f"  - 日期: {record.get('日付')}, 出勤: {record.get('出勤時刻')}, コメント: {record.get('コメント')}")
                logger.info("--------------------")
                return True
            else:
                logger.error(f"❌ 验证失败! 未能在数据库中找到用户 {test_user['employee_id']} 在 {month_code} 月份的数据。")
                return False
            
        except Exception as e:
            logger.error(f"❌ 指定月份真实采集测试失败: {e}", exc_info=True)
            return False
    
    async def run_all_tests(self):
        """运行所有测试"""
        logger.info("🎯 开始运行所有测试 (无文件内存流)")
        
        # 设置测试环境
        if not await self.setup():
            logger.error("测试环境设置失败，测试中止。")
            return False
        
        try:
            # 定义测试列表
            tests = [
                ("数据库连接", self.test_database_connection),
                ("采集器初始化", self.test_collector_initialization),
                ("核心功能: 采集2025年6月真实数据", self.test_specific_month_real_collection),
            ]
            
            results = []
            for name, func in tests:
                logger.info(f"\n{'='*20} {name} {'='*20}")
                result = await func()
                results.append((name, result))
                if not result:
                    logger.error(f"❌ 测试失败: {name}。后续测试可能受影响。")
                    # 可选择在此处中断
                    # break 
                await asyncio.sleep(1) # 测试间短暂间隔
            
            # 汇总结果
            passed = sum(1 for _, r in results if r)
            total = len(results)
            
            logger.info("\n" + "=" * 50)
            logger.info("📊 测试结果汇总:")
            for name, r in results:
                status = "✅ 通过" if r else "❌ 失败"
                logger.info(f"  - {name}: {status}")
            logger.info(f"总计: {passed}/{total} 测试通过")
            logger.info("=" * 50)
            
            return passed == len(tests)
            
        finally:
            await self.cleanup()

async def main():
    logger.info("🧪 server5-2 测试工具启动 (真实、无文件采集)")
    logger.info("=" * 60)
    
    tester = Server52Tester()
    success = await tester.run_all_tests()
    
    if success:
        logger.info("🎉 所有测试通过！")
        return 0
    else:
        logger.error("❌ 部分或全部测试失败。")
        return 1

if __name__ == "__main__":
    try:
        exit_code = asyncio.run(main())
        sys.exit(exit_code)
    except KeyboardInterrupt:
        logger.info("👋 测试被用户中断")
        sys.exit(0)
    except Exception as e:
        logger.error(f"❌ 程序异常退出: {e}", exc_info=True)
        sys.exit(1) 