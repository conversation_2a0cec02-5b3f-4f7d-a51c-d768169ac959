#!/usr/bin/env python3
# server5-2/create_timeprotab_indexes.py
# 2025/07/03 + 16:25 + 为timeprotab分区表创建索引

import asyncio
import asyncpg
import sys
from pathlib import Path

sys.path.append(str(Path(__file__).parent))
from config.config import DATABASE_URL

async def create_indexes_for_partition(partition_name: str):
    """为指定分区创建索引"""
    indexes = [
        f"CREATE INDEX IF NOT EXISTS idx_{partition_name}_employee_date ON {partition_name} (employee_id, 日付);",
        f"CREATE INDEX IF NOT EXISTS idx_{partition_name}_date ON {partition_name} (日付);",
        f"CREATE INDEX IF NOT EXISTS idx_{partition_name}_employee ON {partition_name} (employee_id);"
    ]
    
    print(f"🟢 2025/07/03 + 16:25 + 为分区 {partition_name} 创建索引...")
    
    try:
        conn = await asyncpg.connect(DATABASE_URL)
        try:
            for i, index_sql in enumerate(indexes, 1):
                await conn.execute(index_sql)
                print(f"  ✅ 2025/07/03 + 16:25 + 索引 {i}/3 创建成功")
            print(f"✅ 2025/07/03 + 16:25 + 分区 {partition_name} 所有索引创建完成")
        finally:
            await conn.close()
    except Exception as e:
        print(f"❌ 2025/07/03 + 16:25 + 分区 {partition_name} 索引创建失败: {e}")
        return False
    return True

async def create_all_indexes():
    """为所有现有分区创建索引"""
    print("🟢 2025/07/03 + 16:25 + 开始为所有分区创建索引...")
    
    try:
        conn = await asyncpg.connect(DATABASE_URL)
        try:
            # 查找所有timeprotab分区
            partitions = await conn.fetch("""
                SELECT tablename 
                FROM pg_tables 
                WHERE tablename LIKE 'timeprotab_%'
                ORDER BY tablename
            """)
            
            if not partitions:
                print("⚠️ 2025/07/03 + 16:25 + 未找到任何timeprotab分区")
                return
            
            print(f"🟢 2025/07/03 + 16:25 + 找到 {len(partitions)} 个分区")
            
            for partition in partitions:
                partition_name = partition['tablename']
                await create_indexes_for_partition(partition_name)
                
        finally:
            await conn.close()
            
    except Exception as e:
        print(f"❌ 2025/07/03 + 16:25 + 创建索引失败: {e}")

if __name__ == "__main__":
    if len(sys.argv) > 1:
        # 为指定分区创建索引
        partition_name = sys.argv[1]
        asyncio.run(create_indexes_for_partition(partition_name))
    else:
        # 为所有分区创建索引
        asyncio.run(create_all_indexes()) 