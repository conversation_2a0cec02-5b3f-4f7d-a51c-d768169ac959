# server5-2/start_server5_2.py
# 2025/07/03 + 17:30 + 集成FastAPI提供外部调用接口

import asyncio
import logging
import signal
import sys
from pathlib import Path
from typing import Optional
from datetime import datetime

# 2025/07/03 + 17:30 + 新增FastAPI相关导入
import uvicorn
from fastapi import FastAPI, HTTPException
from pydantic import BaseModel

# 添加项目路径
sys.path.append(str(Path(__file__).parent))

from config.config import LOGGING_CONFIG
from services.timepro_collector import TimeproCollector

# 配置日志
logging.config.dictConfig(LOGGING_CONFIG)
logger = logging.getLogger("server5-2.main")


# --- FastAPI应用和数据模型 ---
app = FastAPI(
    title="Server5-2 Timepro Collector API",
    version="1.1.0",
    description="提供手动触发Timepro考勤数据采集的接口"
)

class CollectRequest(BaseModel):
    year: int
    month: int
    user_id: str = "215829"
    password: str = "jiaban01"
    employee_id: str = "215829"

# ----------------------------


class Server52Main:
    """server5-2主服务管理器（后台定时任务）"""
    
    def __init__(self):
        self.collector: Optional[TimeproCollector] = None
        self.is_running = False
    
    async def start(self) -> bool:
        """启动后台定时采集服务"""
        try:
            logger.info("🚀 启动后台定时采集服务...")
            self.collector = TimeproCollector()
            await self.collector.start()
            self.is_running = True
            logger.info("✅ 后台定时采集服务已成功启动，每日凌晨1点执行。")
            return True
        except Exception as e:
            logger.error(f"❌ 后台服务启动失败: {e}", exc_info=True)
            return False
    
    async def stop(self):
        """停止后台服务"""
        try:
            logger.info("🔌 停止后台服务")
            if self.collector:
                await self.collector.stop()
            self.is_running = False
        except Exception as e:
            logger.error(f"❌ 后台服务停止失败: {e}", exc_info=True)
            
    async def get_status(self) -> dict:
        """获取服务状态"""
        if not self.collector:
            return {"error": "Collector not initialized"}
        return await self.collector.get_status()
        
    async def trigger_manual_collection(self, req: CollectRequest) -> dict:
        """为API调用触发一次手动采集"""
        try:
            logger.info(f"API触发 -> 开始为用户 {req.user_id} 采集 {req.year}-{req.month} 的数据")
            if not self.collector:
                raise RuntimeError("采集服务未初始化。")
                
            target_month = datetime(req.year, req.month, 1)
            
            # 直接调用核心采集逻辑
            await self.collector._collect_user_month_data(
                req.user_id, req.password, req.employee_id, target_month
            )
            
            # 从数据库验证结果
            month_code = target_month.strftime("%y%m")
            db_manager = self.collector.db_manager
            records = await db_manager.get_month_data(req.employee_id, month_code)
            
            message = f"采集调用完成。在数据库中找到 {len(records)} 条关于 {req.year}-{req.month} 的记录。"
            logger.info(f"API触发 -> {message}")
            return {"status": "success", "message": message, "records_found": len(records)}

        except Exception as e:
            logger.error(f"❌ API触发的采集失败: {e}", exc_info=True)
            raise HTTPException(status_code=500, detail=f"采集过程中发生错误: {e}")


# --- 全局服务实例和FastAPI事件处理 ---
server_instance: Optional[Server52Main] = None

@app.on_event("startup")
async def startup_event():
    """FastAPI启动时，在后台启动定时采集服务"""
    global server_instance
    server_instance = Server52Main()
    asyncio.create_task(server_instance.start())

@app.on_event("shutdown")
async def shutdown_event():
    """FastAPI关闭时，停止后台服务"""
    global server_instance
    if server_instance:
        await server_instance.stop()

# --- API 接口 ---
@app.post("/collect", summary="手动触发指定月份的数据采集")
async def manual_collect(request: CollectRequest):
    """
    模拟UI按键，立即触发一次对特定用户和月份的数据采集。
    """
    if not server_instance:
        raise HTTPException(status_code=503, detail="服务尚未完全初始化。")
    return await server_instance.trigger_manual_collection(request)

@app.get("/status", summary="获取服务当前状态")
async def get_service_status():
    """
    返回后台采集服务的状态，包括数据库连接情况和采集统计。
    """
    if not server_instance:
        raise HTTPException(status_code=503, detail="服务尚未完全初始化。")
    return await server_instance.get_status()

# ------------------------------------

if __name__ == "__main__":
    logger.info("=" * 60)
    logger.info("🎯 启动 Server5-2 API 服务...")
    logger.info("   - 后台定时采集: 每日 01:00")
    logger.info("   - API接口: http://127.0.0.1:8002")
    logger.info("=" * 60)
    
    # 2025/07/03 + 17:30 + 使用uvicorn启动服务
    uvicorn.run(app, host="0.0.0.0", port=8002) 