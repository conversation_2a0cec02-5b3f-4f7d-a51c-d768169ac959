# server5-2: Timepro考勤数据采集服务

`server5-2` 是一个独立的、多平台的微服务，旨在自动化地从Timepro网站抓取员工考勤数据，并将其存储到PostgreSQL数据库中。该服务被设计为健壮、高效，并采用完全无文件的内存处理流程。

## 主要功能

- **自动化数据采集**: 每日凌晨1点定时启动，为所有配置的用户采集考勤数据。
- **内存处理**: 直接在内存中完成HTML解析和数据转换，不产生任何临时的HTML或XML文件，保证了高效和整洁。
- **数据库集成**: 将处理后的结构化数据直接存入PostgreSQL分区表，方便后续查询和分析。
- **增量更新**: 使用 `ON CONFLICT` 机制，智能地插入新数据或更新已有数据，确保数据一致性。
- **稳定可靠**: 内置用户间操作延迟和错误处理机制，以应对网络波动和目标网站的登录限制。
- **多平台兼容**: 可在Windows、Linux等多种操作系统上部署和运行。

## 如何使用

### 1. 环境配置

在开始之前，请确保Python环境已安装，并满足 `requirements.txt` 中的依赖。

核心配置位于 `config/config.py` 文件中，您可以根据需要进行调整：

- **数据库连接**:
  ```python
  DATABASE_URL = f"postgresql://{DB_USER}:{DB_PASS}@{DB_HOST}:{DB_PORT}/{DB_NAME}"
  ```
- **采集用户**:
  在 `TIMEPRO_USERS` 列表中添加或修改需要采集的用户名和密码。
  ```python
  TIMEPRO_USERS = [
      {
          "user_id": "215829",
          "password": "jiaban01",
          "employee_id": "215829",
          "description": "主要采集用户"
      },
      # ...可以添加更多用户
  ]
  ```

### 2. 启动服务

您可以直接运行 `start_server5_2.py` 来启动服务。服务启动后将进入后台，并根据设定的时间自动执行采集任务。

```bash
python start_server5_2.py
```
服务启动后，您将在控制台看到类似以下的日志信息，表明服务已成功启动并设置了定时任务。

```
INFO - ✅ timepro采集服务启动成功
INFO - 📅 定时任务已设置: 每天 01:00 开始采集
INFO - ⏰ 调度器线程启动
```

### 3. 手动执行与测试

如果您需要立即执行一次数据采集或进行功能验证，可以使用以下工具：

- **运行单元测试 (推荐)**:
  执行 `test_server5_2.py` 将进行一个完整的端到端测试，包括数据库连接、服务初始化以及使用测试账户 (`215829`) 采集2025年6月的真实数据并验证其是否成功入库。
  ```bash
  python test_server5_2.py
  ```

- **手动采集指定月份数据**:
  使用 `collect_month_data.py` 脚本，您可以为指定用户手动触发特定月份的数据采集。
  ```bash
  # 采集用户215829在2025年6月的数据
  python collect_month_data.py --user 215829 --password jiaban01 --year 2025 --month 6
  ```

## 技术架构

1.  **调度器 (`schedule`)**: 负责定时触发每日的数据采集任务。
2.  **采集器 (`TimeproCollector`)**: 作为服务核心，编排整个数据采集、处理和存储的流程。
3.  **HTTP客户端 (`requests`)**: 在 `timepro.py` 中用于模拟登录Timepro网站并抓取HTML页面。
4.  **HTML解析器 (`BeautifulSoup`)**: 在 `html_parser.py` 中用于从原始HTML中精准提取所需的数据表格。
5.  **数据库管理器 (`asyncpg`)**: 在 `timeprotab_manager.py` 中负责与PostgreSQL数据库的异步交互，包括创建分区、插入和更新数据。

整个流程被设计为异步、非阻塞，以实现更高的性能和资源利用率。 