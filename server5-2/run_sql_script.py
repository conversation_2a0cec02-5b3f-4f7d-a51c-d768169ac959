#!/usr/bin/env python3
# server5-2/run_sql_script.py
# 2025/07/03 + 16:10 + 运行SQL脚本创建timeprotab分区表

import asyncio
import asyncpg
import sys
from pathlib import Path

# 添加配置路径
sys.path.append(str(Path(__file__).parent))
from config.config import DATABASE_URL

async def run_sql_script():
    """2025/07/03 + 16:10 + 运行SQL脚本创建timeprotab分区表"""
    try:
        print("🚀 2025/07/03 + 16:10 + 开始运行SQL脚本创建timeprotab分区表")
        
        # 读取SQL脚本
        sql_file = Path(__file__).parent / "create_timeprotab_partitions.sql"
        if not sql_file.exists():
            print(f"❌ 2025/07/03 + 16:10 + SQL脚本文件不存在: {sql_file}")
            return False
        
        with open(sql_file, 'r', encoding='utf-8') as f:
            sql_content = f.read()
        
        # 连接数据库
        print("🔌 2025/07/03 + 16:10 + 连接数据库...")
        conn = await asyncpg.connect(DATABASE_URL)
        
        try:
            # 分割SQL语句（按分号分割，但忽略字符串内的分号）
            statements = []
            current_statement = ""
            in_string = False
            string_char = None
            
            for char in sql_content:
                if char in ["'", '"'] and (not in_string or char == string_char):
                    if not in_string:
                        in_string = True
                        string_char = char
                    else:
                        in_string = False
                        string_char = None
                
                current_statement += char
                
                if char == ';' and not in_string:
                    statements.append(current_statement.strip())
                    current_statement = ""
            
            # 执行SQL语句
            print(f"📝 2025/07/03 + 16:10 + 执行 {len(statements)} 条SQL语句")
            
            for i, statement in enumerate(statements, 1):
                if statement.strip() and not statement.strip().startswith('--'):
                    try:
                        print(f"  {i}/{len(statements)}: 执行SQL语句...")
                        await conn.execute(statement)
                        print(f"  ✅ 2025/07/03 + 16:10 + SQL语句 {i} 执行成功")
                    except Exception as e:
                        print(f"  ❌ 2025/07/03 + 16:10 + SQL语句 {i} 执行失败: {e}")
                        # 继续执行其他语句
                        continue
            
            # 验证分区表创建
            print("🔍 2025/07/03 + 16:10 + 验证分区表创建...")
            
            # 检查主表是否存在
            main_table_exists = await conn.fetchval("""
                SELECT EXISTS (
                    SELECT 1 FROM information_schema.tables 
                    WHERE table_name = 'timeprotab' AND table_schema = 'public'
                )
            """)
            
            if main_table_exists:
                print("✅ 2025/07/03 + 16:10 + timeprotab主表创建成功")
            else:
                print("❌ 2025/07/03 + 16:10 + timeprotab主表创建失败")
                return False
            
            # 检查分区是否存在
            partitions = await conn.fetch("""
                SELECT tablename 
                FROM pg_tables 
                WHERE tablename LIKE 'timeprotab_%'
                ORDER BY tablename
            """)
            
            if partitions:
                print(f"✅ 2025/07/03 + 16:10 + 创建了 {len(partitions)} 个分区:")
                for partition in partitions:
                    print(f"  - {partition['tablename']}")
            else:
                print("⚠️ 2025/07/03 + 16:10 + 未找到分区表")
            
            # 检查测试数据
            test_data_count = await conn.fetchval("""
                SELECT COUNT(*) FROM timeprotab WHERE employee_id = '215829'
            """)
            
            if test_data_count > 0:
                print(f"✅ 2025/07/03 + 16:10 + 插入了 {test_data_count} 条测试数据")
            else:
                print("⚠️ 2025/07/03 + 16:10 + 未找到测试数据")
            
            print("🎉 2025/07/03 + 16:10 + SQL脚本执行完成")
            return True
            
        finally:
            await conn.close()
            
    except Exception as e:
        print(f"❌ 2025/07/03 + 16:10 + SQL脚本执行失败: {e}")
        return False

async def main():
    """2025/07/03 + 16:10 + 主函数"""
    print("=" * 60)
    print("🧪 2025/07/03 + 16:10 + timeprotab分区表创建工具")
    print("=" * 60)
    
    success = await run_sql_script()
    
    if success:
        print("🎉 2025/07/03 + 16:10 + 所有操作成功完成")
        return 0
    else:
        print("❌ 2025/07/03 + 16:10 + 操作失败")
        return 1

if __name__ == "__main__":
    try:
        exit_code = asyncio.run(main())
        sys.exit(exit_code)
    except KeyboardInterrupt:
        print("👋 2025/07/03 + 16:10 + 操作被用户中断")
        sys.exit(0)
    except Exception as e:
        print(f"❌ 2025/07/03 + 16:10 + 程序异常退出: {e}")
        sys.exit(1) 