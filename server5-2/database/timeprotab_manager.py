# server5-2/database/timeprotab_manager.py
# 2025/07/03 + 16:30 + timeprotab分区表管理器（真实数据管理）

import asyncio
import asyncpg
import logging
from datetime import datetime, timedelta
from typing import List, Dict, Optional
from pathlib import Path
import sys

# 添加配置路径
sys.path.append(str(Path(__file__).parent.parent))
from config.config import DATABASE_URL, TIMEPROTAB_CONFIG

logger = logging.getLogger("server5-2.timeprotab_manager")

class TimeprotabManager:
    """timeprotab分区表管理器（真实数据管理）"""
    
    def __init__(self):
        self.pool: Optional[asyncpg.Pool] = None
        self.base_table = TIMEPROTAB_CONFIG["base_table_name"]
        self.partition_format = TIMEPROTAB_CONFIG["partition_format"]
        
    async def connect(self) -> bool:
        """连接数据库"""
        try:
            self.pool = await asyncpg.create_pool(
                DATABASE_URL,
                min_size=2,
                max_size=10,
                command_timeout=60
            )
            
            logger.info("✅ timeprotab数据库连接池创建成功")
            return True
            
        except Exception as e:
            logger.error(f"❌ 数据库连接失败: {e}")
            return False
    
    async def disconnect(self):
        """断开数据库连接"""
        if self.pool:
            await self.pool.close()
            logger.info("🔌 timeprotab数据库连接池已关闭")
    
    async def initialize_base_table(self):
        """初始化基础分区表"""
        try:
            async with self.pool.acquire() as conn:
                # 创建主表（如果不存在）
                await conn.execute(f"""
                    CREATE TABLE IF NOT EXISTS {self.base_table} (
                        id SERIAL,
                        employee_id VARCHAR(20) NOT NULL,
                        日付 DATE NOT NULL,
                        星期 VARCHAR(10),
                        ｶﾚﾝﾀﾞ VARCHAR(20),
                        不在 VARCHAR(50),
                        勤務区分 VARCHAR(50),
                        事由 VARCHAR(100),
                        出勤時刻 TIME,
                        ＭＣ_出勤 VARCHAR(10),
                        退勤時刻 TIME,
                        ＭＣ_退勤 VARCHAR(10),
                        所定時間 VARCHAR(20),
                        早出残業 VARCHAR(20),
                        内深夜残業 VARCHAR(20),
                        遅刻早退 VARCHAR(20),
                        休出時間 VARCHAR(20),
                        出張残業 VARCHAR(20),
                        外出時間 TIME,
                        戻り時間 TIME,
                        コメント TEXT,
                        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                        PRIMARY KEY (id, 日付),
                        UNIQUE (employee_id, 日付)
                    ) PARTITION BY RANGE (日付)
                """)
                
                # 创建更新时间触发器
                await conn.execute(f"""
                    CREATE OR REPLACE FUNCTION update_{self.base_table}_updated_at()
                    RETURNS TRIGGER AS $$
                    BEGIN
                        NEW.updated_at = CURRENT_TIMESTAMP;
                        RETURN NEW;
                    END;
                    $$ LANGUAGE plpgsql;
                """)
                
                await conn.execute(f"""
                    DROP TRIGGER IF EXISTS trigger_update_{self.base_table}_updated_at ON {self.base_table};
                    CREATE TRIGGER trigger_update_{self.base_table}_updated_at
                        BEFORE UPDATE ON {self.base_table}
                        FOR EACH ROW
                        EXECUTE FUNCTION update_{self.base_table}_updated_at();
                """)
                
                logger.info(f"✅ 基础分区表 {self.base_table} 初始化完成")
                
        except Exception as e:
            logger.error(f"❌ 初始化基础分区表失败: {e}")
            raise
    
    async def create_partition(self, month_code: str) -> bool:
        """创建月份分区"""
        try:
            # 解析月份代码 (例如: "2505" -> 2025年5月)
            if len(month_code) != 4:
                raise ValueError(f"无效的月份代码: {month_code}")
                
            year = 2000 + int(month_code[:2])
            month = int(month_code[2:])
            
            # 计算分区范围
            start_date = datetime(year, month, 1).date()
            if month == 12:
                end_date = datetime(year + 1, 1, 1).date()
            else:
                end_date = datetime(year, month + 1, 1).date()
            
            partition_name = self.partition_format.format(month_code=month_code)
            
            async with self.pool.acquire() as conn:
                # 检查分区是否已存在
                exists = await conn.fetchval("""
                    SELECT EXISTS (
                        SELECT 1 FROM information_schema.tables 
                        WHERE table_name = $1 AND table_schema = 'public'
                    )
                """, partition_name)
                
                if exists:
                    logger.info(f"📅 分区 {partition_name} 已存在")
                    return True
                
                # 创建分区
                await conn.execute(f"""
                    CREATE TABLE {partition_name} PARTITION OF {self.base_table}
                    FOR VALUES FROM ('{start_date}') TO ('{end_date}')
                """)
                
                # 创建分区索引
                await conn.execute(f"""
                    CREATE INDEX IF NOT EXISTS idx_{partition_name}_employee_date 
                    ON {partition_name} (employee_id, 日付)
                """)
                
                await conn.execute(f"""
                    CREATE INDEX IF NOT EXISTS idx_{partition_name}_date 
                    ON {partition_name} (日付)
                """)
                
                logger.info(f"✅ 创建分区成功: {partition_name} ({start_date} ~ {end_date})")
                return True
                
        except Exception as e:
            logger.error(f"❌ 创建分区失败 {month_code}: {e}")
            return False
    
    async def insert_timeprotab_data(self, records: List[Dict]) -> int:
        """批量插入timeprotab数据"""
        if not records:
            return 0
            
        try:
            inserted_count = 0
            
            async with self.pool.acquire() as conn:
                async with conn.transaction():
                    for record in records:
                        # 确保所需分区存在
                        日付 = record.get('日付')
                        if 日付:
                            if isinstance(日付, str):
                                # 解析日期字符串 "2025/05/01" -> 月份代码 "2505"
                                date_obj = datetime.strptime(日付, '%Y/%m/%d').date()
                            else:
                                date_obj = 日付
                            
                            month_code = f"{date_obj.year % 100:02d}{date_obj.month:02d}"
                            await self.create_partition(month_code)
                        
                        # 处理时间字段
                        def format_value(value, is_time_col=False):
                            if value is None:
                                return None
                            # 如果列类型应为TIME，但传入的是字符串，尝试转换
                            if is_time_col:
                                if isinstance(value, str):
                                    try:
                                        return datetime.strptime(value, '%H:%M').time()
                                    except (ValueError, TypeError):
                                        return None
                                return value # 已经是time对象
                            # 如果列类型应为VARCHAR，但传入的是time对象，转换为字符串
                            else:
                                if hasattr(value, 'strftime'): # 是time或datetime对象
                                    return value.strftime('%H:%M')
                                return str(value)
                        
                        # 插入数据
                        await conn.execute("""
                            INSERT INTO timeprotab (
                                employee_id, 日付, 星期, ｶﾚﾝﾀﾞ, 不在, 勤務区分, 事由,
                                出勤時刻, ＭＣ_出勤, 退勤時刻, ＭＣ_退勤, 所定時間,
                                早出残業, 内深夜残業, 遅刻早退, 休出時間, 出張残業,
                                外出時間, 戻り時間, コメント
                            ) VALUES (
                                $1, $2, $3, $4, $5, $6, $7, $8, $9, $10,
                                $11, $12, $13, $14, $15, $16, $17, $18, $19, $20
                            ) ON CONFLICT (employee_id, 日付) DO UPDATE SET
                                星期 = EXCLUDED.星期,
                                ｶﾚﾝﾀﾞ = EXCLUDED.ｶﾚﾝﾀﾞ,
                                不在 = EXCLUDED.不在,
                                勤務区分 = EXCLUDED.勤務区分,
                                事由 = EXCLUDED.事由,
                                出勤時刻 = EXCLUDED.出勤時刻,
                                ＭＣ_出勤 = EXCLUDED.ＭＣ_出勤,
                                退勤時刻 = EXCLUDED.退勤時刻,
                                ＭＣ_退勤 = EXCLUDED.ＭＣ_退勤,
                                所定時間 = EXCLUDED.所定時間,
                                早出残業 = EXCLUDED.早出残業,
                                内深夜残業 = EXCLUDED.内深夜残業,
                                遅刻早退 = EXCLUDED.遅刻早退,
                                休出時間 = EXCLUDED.休出時間,
                                出張残業 = EXCLUDED.出張残業,
                                外出時間 = EXCLUDED.外出時間,
                                戻り時間 = EXCLUDED.戻り時間,
                                コメント = EXCLUDED.コメント,
                                updated_at = CURRENT_TIMESTAMP
                        """, 
                            record.get('employee_id'),
                            record.get('日付'),
                            record.get('星期'),
                            record.get('ｶﾚﾝﾀﾞ'),
                            record.get('不在'),
                            record.get('勤務区分'),
                            record.get('事由'),
                            format_value(record.get('出勤時刻'), is_time_col=True),
                            format_value(record.get('ＭＣ_出勤')),
                            format_value(record.get('退勤時刻'), is_time_col=True),
                            format_value(record.get('ＭＣ_退勤')),
                            format_value(record.get('所定時間')),
                            format_value(record.get('早出残業')),
                            format_value(record.get('内深夜残業')),
                            format_value(record.get('遅刻早退')),
                            format_value(record.get('休出時間')),
                            format_value(record.get('出張残業')),
                            format_value(record.get('外出時間'), is_time_col=True),
                            format_value(record.get('戻り時間'), is_time_col=True),
                            record.get('コメント')
                        )
                        
                        inserted_count += 1
            
            logger.info(f"✅ 批量插入/更新了 {inserted_count} 条记录")
            return inserted_count
            
        except Exception as e:
            logger.error(f"❌ 批量插入失败: {e}")
            return 0
    
    async def get_month_data(self, employee_id: str, month_code: str) -> List[Dict]:
        """获取指定员工指定月份的数据"""
        try:
            # 解析月份代码
            year = 2000 + int(month_code[:2])
            month = int(month_code[2:])
            
            start_date = datetime(year, month, 1).date()
            if month == 12:
                end_date = datetime(year + 1, 1, 1).date()
            else:
                end_date = datetime(year, month + 1, 1).date()
            
            async with self.pool.acquire() as conn:
                rows = await conn.fetch("""
                    SELECT * FROM timeprotab 
                    WHERE employee_id = $1 AND 日付 >= $2 AND 日付 < $3
                    ORDER BY 日付
                """, employee_id, start_date, end_date)
                
                records = []
                for row in rows:
                    record = dict(row)
                    records.append(record)
                
                logger.info(f"📊 查询到 {len(records)} 条记录: {employee_id} - {month_code}")
                return records
                
        except Exception as e:
            logger.error(f"❌ 查询月份数据失败: {e}")
            return []
    
    async def cleanup_old_partitions(self, retention_months: int = 24):
        """清理旧分区（保留指定月数）"""
        try:
            cutoff_date = datetime.now() - timedelta(days=retention_months * 30)
            cutoff_month = cutoff_date.strftime("%y%m")
            
            async with self.pool.acquire() as conn:
                # 获取所有分区
                partitions = await conn.fetch("""
                    SELECT tablename FROM pg_tables 
                    WHERE tablename LIKE 'timeprotab_%'
                    ORDER BY tablename
                """)
                
                for partition in partitions:
                    partition_name = partition['tablename']
                    month_code = partition_name.split('_')[1]
                    
                    if month_code < cutoff_month:
                        await conn.execute(f"DROP TABLE IF EXISTS {partition_name}")
                        logger.info(f"🗑️ 删除旧分区: {partition_name}")
            
            logger.info(f"✅ 旧分区清理完成，保留 {retention_months} 个月")
            
        except Exception as e:
            logger.error(f"❌ 清理旧分区失败: {e}")
    
    async def get_status(self) -> Dict:
        """获取数据库状态"""
        try:
            async with self.pool.acquire() as conn:
                # 检查连接
                await conn.execute("SELECT 1")
                
                # 获取分区信息
                partitions = await conn.fetch("""
                    SELECT tablename FROM pg_tables 
                    WHERE tablename LIKE 'timeprotab_%'
                    ORDER BY tablename
                """)
                
                partition_list = [p['tablename'] for p in partitions]
                
                # 获取总记录数
                total_records = await conn.fetchval("SELECT COUNT(*) FROM timeprotab")
                
                return {
                    "status": "healthy",
                    "total_partitions": len(partition_list),
                    "partition_list": partition_list,
                    "total_records": total_records,
                    "base_table": self.base_table
                }
                
        except Exception as e:
            return {
                "status": "error",
                "error": str(e)
            } 