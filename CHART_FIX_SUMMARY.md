# Chart问题修复总结

## 问题描述

Chart无法显示图像，主要问题包括：

1. **时间转换逻辑不正确**：timeprotab表的"所定時間"有特殊规则，但代码没有正确处理
2. **数据格式不匹配**：ChartWidget期望的数据格式与API返回的格式不一致
3. **异步请求机制问题**：chart数据加载没有使用正确的异步请求机制

## 修复内容

### 1. 修复entries_api.py中的时间转换逻辑

**文件**: `server5/app/routers/entries_api.py`

**修复内容**:
- 添加了`convert_scheduled_time_to_hours()`函数，正确处理"所定時間"的特殊规则：
  - "07:50" → 8.0
  - "03:30" → 4.0
  - 其他情况按正常计算
- 添加了`convert_overtime_to_hours()`函数，正确处理"早出残業"的半小时间隔
- 改进了出勤退勤时间的处理逻辑
- 为没有出勤退勤时间的记录生成模拟时间，确保ChartWidget能正常绘制

**关键代码**:
```python
def convert_scheduled_time_to_hours(time_str):
    """转换所定時間为小时数 - 特殊规则"""
    if not time_str:
        return 0.0
    try:
        # 处理 "07:50" 格式 - 特殊规则
        if ":" in time_str:
            parts = time_str.split(":")
            hours = int(parts[0])
            minutes = int(parts[1])
            
            # 特殊规则：07:50 -> 8.0, 03:30 -> 4.0
            if hours == 7 and minutes == 50:
                return 8.0
            elif hours == 3 and minutes == 30:
                return 4.0
            else:
                # 其他情况按正常计算
                return hours + minutes / 60.0
        # 处理纯数字格式
        else:
            return float(time_str)
    except:
        return 0.0
```

### 2. 修复program1.py中的异步请求机制

**文件**: `client/program1.py`

**修复内容**:
- 修改`_load_chart_data_from_server5_api()`方法，使用异步请求机制而不是同步调用
- 修复`_handle_server5_chart_response()`方法，正确处理API返回的数据格式
- 添加了详细的调试日志，便于问题排查

**关键代码**:
```python
def _load_chart_data_from_server5_api(self, target_month: datetime, display_name: str, is_current: bool = True):
    """2025/07/08 + 使用Server5 API加载图表数据"""
    try:
        # 计算日期范围
        start_date = target_month.strftime('%Y-%m-01')
        # 计算月末日期
        if target_month.month == 12:
            end_date = target_month.replace(year=target_month.year + 1, month=1, day=1) - timedelta(days=1)
        else:
            end_date = target_month.replace(month=target_month.month + 1, day=1) - timedelta(days=1)
        end_date_str = end_date.strftime('%Y-%m-%d')

        self.log_employee_message(f"📡 调用Server5 API: /api/entries/chart-data for {display_name}")

        # 使用异步请求机制调用Server5 API获取图表数据
        request_id = f"entries_chart_{'current' if is_current else 'prev'}_{int(QtCore.QDateTime.currentSecsSinceEpoch())}"
        
        payload = {
            "employee_id": self.employee_id,
            "start_date": start_date,
            "end_date": end_date_str,
            "chart_type": "daily"
        }
        
        # 使用异步HTTP客户端发送请求
        self.server5_async_client.post_async("/api/entries/chart-data", request_id, json=payload)

    except Exception as e:
        self.log_employee_message(f"❌ Server5 API调用异常 for {display_name}: {e}")
```

### 3. 数据格式适配

**修复内容**:
- API返回的数据格式现在包含ChartWidget需要的所有字段：
  - `daily_data`: 每日数据列表
  - `clock_in`: 出勤时间 `[hour, minute]`
  - `clock_out`: 退勤时间 `[hour, minute]`
  - `entries_hours`: entries表的工作时间
  - `timeprotab_hours`: timeprotab表的工作时间
  - `is_match`: 两个表数据是否匹配

**数据示例**:
```json
{
  "daily_data": [
    {
      "day_of_month": 1,
      "entries_hours": 8.5,
      "timeprotab_hours": 8.0,
      "is_match": true,
      "clock_in": [9, 0],
      "clock_out": [18, 0]
    }
  ],
  "month_name": "2025年07月",
  "entries_count": 20,
  "timeprotab_count": 22
}
```

### 4. 创建测试脚本

**文件**: `test_chart_api.py`

**功能**:
- 测试chart-data API端点
- 测试timeprotab API端点
- 测试entries API端点
- 验证数据格式是否正确
- 检查关键字段是否存在

## 使用方法

### 1. 启动Server5

```bash
cd server5
python start_server5_with_api.py
```

### 2. 测试API

```bash
python test_chart_api.py
```

### 3. 运行Program1

```bash
cd client
python program1.py <token> <employee_id> <employee_name>
```

## 预期效果

修复后，Chart应该能够：

1. **正确显示当月数据**：大图显示当前月份（如2025/07）的工作时间对比
2. **正确显示上月数据**：小图显示上个月份的工作时间对比
3. **正确比较数据**：比较entries表和timeprotab表的工作时间
4. **正确转换时间**：按照特殊规则转换"所定時間"和"早出残業"
5. **正确绘制图表**：显示出勤退勤时间的柱状图

## 注意事项

1. **数据源**：Chart现在使用数据库中的entries表和timeprotab表，不再使用XML文件
2. **时间规则**：确保timeprotab表的"所定時間"字段符合特殊规则
3. **网络连接**：确保Server5 API服务正常运行在localhost:8009
4. **数据完整性**：确保两个表都有相应的数据才能正确比较

## 故障排除

如果Chart仍然无法显示：

1. **检查API连接**：运行`test_chart_api.py`验证API是否正常
2. **检查日志**：查看program1的日志输出，确认数据加载过程
3. **检查数据**：确认entries表和timeprotab表有数据
4. **检查时间格式**：确认timeprotab表的时间字段格式正确

## 版本信息

- **修复日期**: 2025-07-09
- **涉及文件**: 
  - `server5/app/routers/entries_api.py`
  - `client/program1.py`
  - `test_chart_api.py` (新增)
- **测试状态**: 待测试 