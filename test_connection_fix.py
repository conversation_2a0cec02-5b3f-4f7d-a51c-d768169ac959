#!/usr/bin/env python3
"""
简单的HTTP连接管理修复测试
验证全局连接池和session管理是否正常工作
"""

import sys
import time
import requests
from pathlib import Path

# 添加client目录到路径
sys.path.insert(0, str(Path(__file__).parent / "client"))

def test_global_http_manager():
    """测试全局HTTP管理器"""
    print("🧪 测试全局HTTP连接管理器...")
    
    try:
        # 导入修复后的HTTP管理器
        from client.program1 import HTTPClientManager, Server5APIClient
        
        # 测试1: 全局管理器单例
        print("1️⃣ 测试全局管理器单例...")
        manager1 = HTTPClientManager()
        manager2 = HTTPClientManager()
        assert manager1 is manager2, "管理器不是单例"
        print("✅ 全局管理器单例测试通过")
        
        # 测试2: Session复用
        print("2️⃣ 测试Session复用...")
        session1 = manager1.get_session("http://localhost:8009")
        session2 = manager1.get_session("http://localhost:8009")
        assert session1 is session2, "Session没有复用"
        print("✅ Session复用测试通过")
        
        # 测试3: 不同URL不同Session
        print("3️⃣ 测试不同URL不同Session...")
        session_a = manager1.get_session("http://localhost:8009")
        session_b = manager1.get_session("https://localhost")
        assert session_a is not session_b, "不同URL应该有不同Session"
        print("✅ 不同URL不同Session测试通过")
        
        # 测试4: Server5APIClient状态管理
        print("4️⃣ 测试Server5APIClient状态管理...")
        client = Server5APIClient()
        assert not client._closed, "新客户端应该未关闭"
        
        # 测试关闭状态
        client.close()
        assert client._closed, "关闭后客户端应该标记为已关闭"
        
        # 测试关闭后调用
        result = client.get_entries("test", "2025-01-01", "2025-01-31")
        assert not result["ok"], "关闭后的调用应该失败"
        assert "已关闭" in result["error"], "应该返回已关闭错误"
        print("✅ Server5APIClient状态管理测试通过")
        
        print("\n🎉 所有HTTP连接管理测试通过！")
        return True
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_session_cleanup():
    """测试Session清理"""
    print("\n🧪 测试Session清理...")
    
    try:
        from client.program1 import HTTPClientManager
        
        # 创建管理器并获取一些Session
        manager = HTTPClientManager()
        session1 = manager.get_session("http://test1.com")
        session2 = manager.get_session("http://test2.com")
        
        print(f"📊 当前Session数量: {len(manager._sessions)}")
        
        # 清理单个Session
        manager.close_session("http://test1.com")
        print(f"📊 清理后Session数量: {len(manager._sessions)}")
        
        # 清理所有Session
        manager.cleanup_all()
        print(f"📊 全部清理后Session数量: {len(manager._sessions)}")
        
        print("✅ Session清理测试通过")
        return True
        
    except Exception as e:
        print(f"❌ Session清理测试失败: {e}")
        return False

def test_connection_pool_config():
    """测试连接池配置"""
    print("\n🧪 测试连接池配置...")
    
    try:
        from client.program1 import HTTPClientManager
        
        manager = HTTPClientManager()
        session = manager.get_session("http://localhost:8009")
        
        # 检查连接池配置
        http_adapter = session.get_adapter("http://")
        
        print(f"📊 连接池配置:")
        print(f"  - Pool connections: {http_adapter.config.get('pool_connections', '未知')}")
        print(f"  - Pool max size: {http_adapter.config.get('pool_maxsize', '未知')}")
        print(f"  - Max retries: {http_adapter.config.get('max_retries', '未知')}")
        
        print("✅ 连接池配置测试通过")
        return True
        
    except Exception as e:
        print(f"❌ 连接池配置测试失败: {e}")
        return False

def test_multiple_clients():
    """测试多个客户端实例"""
    print("\n🧪 测试多个客户端实例...")
    
    try:
        from client.program1 import Server5APIClient, SimpleSyncHTTPClient, SimpleAsyncHTTPClient
        
        # 创建多个客户端
        clients = []
        for i in range(5):
            server5_client = Server5APIClient()
            sync_client = SimpleSyncHTTPClient("https://localhost")
            async_client = SimpleAsyncHTTPClient("https://localhost")
            clients.append((server5_client, sync_client, async_client))
        
        print(f"📊 创建了 {len(clients)} 组客户端")
        
        # 关闭所有客户端
        for server5_client, sync_client, async_client in clients:
            server5_client.close()
            sync_client.close()
            # async_client需要异步关闭，这里只标记
            async_client._closed = True
        
        print("✅ 多个客户端实例测试通过")
        return True
        
    except Exception as e:
        print(f"❌ 多个客户端实例测试失败: {e}")
        return False

def main():
    """主函数"""
    print("🔧 HTTP连接管理修复验证测试")
    print("=" * 50)
    
    # 检查必要文件
    client_program = Path("client/program1.py")
    if not client_program.exists():
        print(f"❌ 缺少必要文件: {client_program}")
        return False
    
    # 运行所有测试
    tests = [
        ("全局HTTP管理器", test_global_http_manager),
        ("Session清理", test_session_cleanup), 
        ("连接池配置", test_connection_pool_config),
        ("多个客户端实例", test_multiple_clients)
    ]
    
    passed = 0
    failed = 0
    
    for test_name, test_func in tests:
        print(f"\n📋 运行测试: {test_name}")
        try:
            if test_func():
                passed += 1
                print(f"✅ {test_name} 通过")
            else:
                failed += 1
                print(f"❌ {test_name} 失败")
        except Exception as e:
            failed += 1
            print(f"❌ {test_name} 异常: {e}")
    
    # 总结
    print("\n" + "=" * 50)
    print("📊 测试总结:")
    print(f"  - 通过: {passed}")
    print(f"  - 失败: {failed}")
    print(f"  - 总计: {passed + failed}")
    
    if failed == 0:
        print("\n🎉 所有测试通过！HTTP连接管理修复成功！")
        print("\n💡 修复要点:")
        print("  ✅ 实现了全局HTTP连接池管理")
        print("  ✅ Session复用避免重复创建")
        print("  ✅ 客户端状态管理和正确关闭")
        print("  ✅ 连接池配置优化")
        print("  ✅ 资源清理机制")
        
        print("\n🚀 建议:")
        print("  1. 重启Server5服务")
        print("  2. 重启客户端程序")
        print("  3. 测试多次开关客户端是否还会卡顿")
        return True
    else:
        print(f"\n⚠️ {failed} 个测试失败，请检查修复")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1) 