# 自定义日期范围数据拉取功能使用指南

## 功能概述

新增的**选项17**允许用户指定自定义的开始日期和结束日期，快速拉取指定日期范围内所有人的数据。这是对现有功能的扩展，提供了更灵活的数据拉取方式。

## 功能特点

- ✅ **自定义日期范围**: 可以指定任意开始和结束日期
- ✅ **分批次处理**: 避免超时，提高稳定性
- ✅ **全员数据拉取**: 一次性拉取所有员工的数据
- ✅ **智能批次大小**: 可自定义批次大小，建议5-10天
- ✅ **详细进度显示**: 实时显示拉取进度和统计信息
- ✅ **错误恢复**: 单个批次失败不影响整体流程

## 使用方法

### 1. 在主测试脚本中使用

```bash
cd server5
python test_f3_60days_pull.py
```

选择选项 **17**，然后按提示输入：
- 开始日期 (YYYY/MM/DD格式)
- 结束日期 (YYYY/MM/DD格式)
- 批次大小 (可选，默认7天)

### 2. 使用专用演示脚本

```bash
cd server5
python test_custom_date_range.py
```

这个脚本提供了三个示例：
- 示例1: 最近一周的数据
- 示例2: 指定月份的数据 (2025年5月)
- 示例3: 完全自定义日期范围

## 使用示例

### 示例1: 拉取最近一周数据
```
开始日期: 2025/06/20
结束日期: 2025/06/27
总天数: 8 天
批次大小: 3 天
```

### 示例2: 拉取指定月份数据
```
开始日期: 2025/05/01
结束日期: 2025/05/31
总天数: 31 天
批次大小: 5 天
```

### 示例3: 拉取特定时间段
```
开始日期: 2025/04/15
结束日期: 2025/06/15
总天数: 62 天
批次大小: 7 天
```

## 参数说明

### 日期格式
- **格式**: YYYY/MM/DD
- **示例**: 2025/06/27
- **注意**: 开始日期不能大于结束日期

### 批次大小
- **默认值**: 7天
- **建议范围**: 5-10天
- **选择原则**: 
  - 数据量小时可以用较大批次
  - 数据量大时建议用小批次避免超时
  - 网络不稳定时建议用小批次

## 输出信息

### 进度显示
```
📦 批次 1: 2025-06-20 到 2025-06-22
    ✅ 批次 1 拉取完成，耗时: 15.23 秒
    📊 拉取到记录数: 245
    💾 批次 1 同步完成，耗时: 0.08 秒
    📈 同步到PostgreSQL的记录数: 245
```

### 统计信息
```
✅ 分批次拉取完成，总耗时: 125.67 秒
📊 总计拉取记录数: 1847
📊 总计同步记录数: 1847
📊 处理批次数: 4

--- 4. 数据库统计信息 ---
  - 总记录数: 1847
  - 唯一员工数: 23
  - 最早日期: 2025-06-20
  - 最晚日期: 2025-06-27
```

## 性能优化建议

### 1. 批次大小选择
- **小数据量** (< 1000条/天): 批次大小 7-10天
- **中等数据量** (1000-3000条/天): 批次大小 5-7天
- **大数据量** (> 3000条/天): 批次大小 3-5天

### 2. 时间选择
- **避免高峰期**: 避开业务高峰期进行大批量拉取
- **分批执行**: 大范围数据可以分多次执行
- **监控资源**: 注意Server6的Access数据库状态

### 3. 错误处理
- **单个批次失败**: 会自动跳过，继续处理下一批次
- **网络超时**: 系统会自动重试
- **数据库错误**: 会记录详细错误信息

## 注意事项

### 1. 数据量考虑
- 大日期范围会产生大量数据
- 建议先测试小范围，确认无误后再处理大范围
- 注意PostgreSQL存储空间

### 2. 系统资源
- 大批量拉取会占用较多网络带宽
- Server6的Access数据库可能被占用
- 建议在系统负载较低时执行

### 3. 数据一致性
- 拉取过程中新数据可能被遗漏
- 建议在数据相对稳定时执行
- 可以多次执行确保数据完整性

## 故障排除

### 常见问题

1. **日期格式错误**
   ```
   ❌ 日期格式错误: time data '2025/06/27' does not match format '%Y/%m/%d'
   ```
   **解决**: 使用 YYYY/MM/DD 格式，如 2025/06/27

2. **开始日期大于结束日期**
   ```
   ❌ 开始日期不能大于结束日期
   ```
   **解决**: 检查日期输入顺序

3. **批次大小无效**
   ```
   ⚠️ 批次大小无效，使用默认值7天
   ```
   **解决**: 输入正整数，建议5-10

4. **超时错误**
   ```
   ❌ Server6请求最终超时
   ```
   **解决**: 减小批次大小，检查网络连接

### 日志分析

查看详细日志了解执行情况：
```bash
# 查看实时日志
tail -f logs/server5.log

# 查看错误日志
grep "ERROR" logs/server5.log
```

## 扩展功能

### 1. 编程接口
```python
from test_f3_60days_pull import test_bulk_fast_pull_custom_date_range
from datetime import date

# 拉取指定日期范围的数据 (格式: YYYY/MM/DD)
start_date = date(2025, 6, 1)
end_date = date(2025, 6, 30)
await test_bulk_fast_pull_custom_date_range(start_date, end_date, batch_size=7)
```

### 2. 自动化脚本
可以基于此功能创建自动化数据同步脚本，定期拉取指定范围的数据。

## 总结

自定义日期范围功能为数据同步提供了更大的灵活性，特别适合：
- 历史数据补全
- 特定时间段数据分析
- 数据质量检查和修复
- 定期数据备份

通过合理设置批次大小和选择合适的时间，可以高效稳定地完成大批量数据拉取任务。 