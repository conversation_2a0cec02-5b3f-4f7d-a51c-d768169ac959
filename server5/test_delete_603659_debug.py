#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试external_id=603659的删除问题
"""

import asyncio
import sys
from pathlib import Path
import logging

# 添加项目根目录到Python路径
sys.path.append(str(Path(__file__).parent.parent))

from app.database import IMDBClient
from app.utils.server6_client import Server6Client

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

class Delete603659DebugTest:
    """external_id=603659删除问题调试测试类"""
    
    def __init__(self):
        self.imdb_client = IMDBClient()
        self.server6_client = Server6Client()
        
    async def setup(self):
        """初始化连接"""
        try:
            await self.imdb_client.connect()
            await self.server6_client.connect()
            logger.info("✅ 数据库连接成功")
            return True
        except Exception as e:
            logger.error(f"❌ 数据库连接失败: {e}")
            return False
    
    async def cleanup(self):
        """清理连接"""
        await self.imdb_client.disconnect()
        await self.server6_client.disconnect()
    
    async def check_external_id_603659(self):
        """检查external_id=603659的记录状态"""
        logger.info("🔍 检查external_id=603659的记录状态")
        
        # 1. 检查entries表中是否存在
        entry_result = await self.imdb_client.fetch_one(
            "SELECT * FROM entries WHERE external_id = 603659"
        )
        
        if entry_result:
            logger.info(f"✅ entries表中存在记录: {entry_result}")
            
            # 检查source字段
            source = entry_result.get('source')
            logger.info(f"📋 记录source字段: {source}")
            
            return entry_result
        else:
            logger.info("❌ entries表中不存在external_id=603659的记录")
            return None
    
    async def check_delete_queue_items(self):
        """检查DELETE队列项"""
        logger.info("🔍 检查DELETE队列项")
        
        # 检查所有DELETE队列项
        delete_queue_items = await self.imdb_client.execute_query(
            "SELECT * FROM entries_push_queue WHERE operation = 'DELETE' ORDER BY created_ts DESC LIMIT 10"
        )
        
        if delete_queue_items:
            logger.info(f"📋 找到 {len(delete_queue_items)} 个DELETE队列项:")
            for item in delete_queue_items:
                logger.info(f"  - queue_id={item['queue_id']}, entry_id={item['entry_id']}, external_id={item['external_id']}, synced={item['synced']}")
        else:
            logger.info("❌ 未找到DELETE队列项")
        
        return delete_queue_items
    
    async def check_specific_delete_queue(self, entry_id):
        """检查特定entry_id的DELETE队列项"""
        logger.info(f"🔍 检查entry_id={entry_id}的DELETE队列项")
        
        queue_items = await self.imdb_client.execute_query(
            "SELECT * FROM entries_push_queue WHERE entry_id = $1 AND operation = 'DELETE' ORDER BY created_ts DESC",
            entry_id
        )
        
        if queue_items:
            for item in queue_items:
                logger.info(f"📋 DELETE队列项: {item}")
                return item
        else:
            logger.info(f"❌ 未找到entry_id={entry_id}的DELETE队列项")
            return None
    
    async def test_server6_delete_603659(self):
        """测试Server6删除external_id=603659"""
        logger.info("🧪 测试Server6删除external_id=603659")
        
        try:
            response = await self.server6_client.delete_entry(603659)
            logger.info(f"📤 Server6删除响应: {response}")
            
            if response.get('success'):
                logger.info("✅ Server6删除成功")
            else:
                logger.error(f"❌ Server6删除失败: {response}")
            
            return response
            
        except Exception as e:
            logger.error(f"❌ Server6删除异常: {e}")
            return None
    
    async def simulate_delete_operation(self, entry_id):
        """模拟删除操作"""
        logger.info(f"🧪 模拟删除操作: entry_id={entry_id}")
        
        try:
            # 1. 先标记为user操作
            await self.imdb_client.execute_command(
                "UPDATE entries SET source = 'user' WHERE id = $1", entry_id
            )
            logger.info(f"✅ 标记为user操作: entry_id={entry_id}")
            
            # 2. 删除记录
            await self.imdb_client.execute_command(
                "DELETE FROM entries WHERE id = $1", entry_id
            )
            logger.info(f"✅ 删除记录成功: entry_id={entry_id}")
            
            # 3. 等待触发器执行
            await asyncio.sleep(1)
            
            # 4. 检查队列项
            queue_item = await self.check_specific_delete_queue(entry_id)
            return queue_item
            
        except Exception as e:
            logger.error(f"❌ 模拟删除操作失败: {e}")
            return None
    
    async def run_debug_test(self):
        """运行调试测试"""
        logger.info("🚀 开始external_id=603659删除问题调试测试")
        logger.info("=" * 60)
        
        if not await self.setup():
            return
        
        try:
            # 1. 检查external_id=603659的状态
            entry_record = await self.check_external_id_603659()
            
            if entry_record:
                entry_id = entry_record['id']
                external_id = entry_record['external_id']
                source = entry_record['source']
                
                logger.info(f"📋 记录详情: entry_id={entry_id}, external_id={external_id}, source={source}")
                
                # 2. 检查该记录的DELETE队列项
                await self.check_specific_delete_queue(entry_id)
                
                # 3. 如果source不是user，模拟删除操作
                if source != 'user':
                    logger.info("🔄 记录source不是user，模拟删除操作")
                    queue_item = await self.simulate_delete_operation(entry_id)
                    
                    if queue_item:
                        logger.info(f"✅ 删除队列项创建成功: {queue_item}")
                        
                        # 检查队列项是否有external_id
                        if queue_item.get('external_id'):
                            logger.info(f"✅ 队列项有external_id: {queue_item['external_id']}")
                        else:
                            logger.warning("⚠️ 队列项没有external_id")
                else:
                    logger.info("ℹ️ 记录source已经是user，无需模拟删除")
                
                # 4. 测试Server6删除
                await self.test_server6_delete_603659()
            else:
                logger.info("ℹ️ 记录不存在，检查DELETE队列项")
                await self.check_delete_queue_items()
            
        finally:
            await self.cleanup()

async def main():
    """主函数"""
    test = Delete603659DebugTest()
    await test.run_debug_test()

if __name__ == "__main__":
    asyncio.run(main()) 