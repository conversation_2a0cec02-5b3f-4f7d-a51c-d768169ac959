#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
详细测试Server5更新操作完整流程
验证客户端 -> Server5 -> 触发器 -> f2 -> server6 的完整链路
使用指定的external_id: 603682
"""

import asyncio
import sys
import requests
import time
from pathlib import Path
import logging

# 添加项目路径
sys.path.append(str(Path(__file__).parent))

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

class DetailedUpdateFlowTest:
    """详细测试更新操作完整流程"""
    
    def __init__(self):
        self.server5_base_url = "http://localhost:8009"
        self.target_external_id = 603682  # 使用指定的external_id
        
    async def test_complete_update_flow(self):
        """测试完整的更新流程"""
        print("🚀 开始详细测试Server5更新操作完整流程")
        print(f"🎯 目标external_id: {self.target_external_id}")
        print("="*80)
        
        try:
            # 1. 首先获取目标记录
            print("📋 1. 获取目标记录...")
            response = requests.get(
                f"{self.server5_base_url}/api/entries/",
                params={"limit": 1000},
                timeout=10
            )
            
            if response.status_code != 200:
                print(f"❌ 获取记录失败: {response.status_code} - {response.text}")
                return False
                
            entries = response.json()
            if not entries:
                print("❌ 没有找到记录")
                return False
            
            # 查找指定的external_id
            target_entry = None
            for entry in entries:
                if entry.get('external_id') == self.target_external_id:
                    target_entry = entry
                    break
            
            if not target_entry:
                print(f"❌ 没有找到external_id={self.target_external_id}的记录")
                print(f"可用的external_id: {[e.get('external_id') for e in entries[:5]]}")
                return False
                
            internal_id = target_entry.get('id')
            external_id = target_entry.get('external_id')
            
            print(f"✅ 找到目标记录: internal_id={internal_id}, external_id={external_id}")
            print(f"📊 当前数据:")
            print(f"  - duration: {target_entry.get('duration')}")
            print(f"  - model: {target_entry.get('model')}")
            print(f"  - project_number: {target_entry.get('project_number')}")
            print(f"  - category: {target_entry.get('category')}")
            print(f"  - source: {target_entry.get('source')}")
            
            # 2. 准备更新数据
            print("\n📝 2. 准备更新数据...")
            update_data = {
                "entry_date": target_entry.get('entry_date'),
                "employee_id": target_entry.get('employee_id'),
                "duration": 8.5,  # 修改工作时间
                "project_code": "TEST_PROJECT_UPDATE",  # 修改项目代码
                "status": "TEST_STATUS_UPDATE",  # 修改状态
                "description": "详细测试更新描述",  # 修改描述
                "department": target_entry.get('department'),
                "notes": f"号机:TEST001 工场製番:TEST_FACTORY 工事番号:TEST_PROJECT ユニット番号:TEST_UNIT",
                "source": "user"
            }
            
            print(f"📊 更新数据: {update_data}")
            
            # 3. 发送更新请求
            print("\n📤 3. 发送更新请求...")
            print(f"🔗 请求URL: {self.server5_base_url}/api/entries/{external_id}")
            update_response = requests.put(
                f"{self.server5_base_url}/api/entries/{external_id}",
                json=update_data,
                timeout=10
            )
            
            if update_response.status_code != 200:
                print(f"❌ 更新失败: {update_response.status_code} - {update_response.text}")
                return False
                
            update_result = update_response.json()
            print(f"✅ 更新成功: {update_result}")
            
            # 4. 等待触发器处理
            print("\n⏳ 4. 等待触发器处理...")
            await asyncio.sleep(3)
            
            # 5. 检查entries_push_queue
            print("\n🔍 5. 检查entries_push_queue...")
            try:
                # 这里需要直接查询数据库
                import asyncpg
                from app.config import IMDB_DATABASE_URL
                
                conn = await asyncpg.connect(IMDB_DATABASE_URL)
                try:
                    queue_items = await conn.fetch(
                        "SELECT * FROM entries_push_queue WHERE entry_id = $1 ORDER BY created_at DESC LIMIT 5",
                        internal_id
                    )
                    
                    if queue_items:
                        print(f"✅ 找到 {len(queue_items)} 条队列项:")
                        for item in queue_items:
                            print(f"   - queue_id: {item['queue_id']}")
                            print(f"   - operation: {item['operation']}")
                            print(f"   - synced: {item['synced']}")
                            print(f"   - created_at: {item['created_at']}")
                            print(f"   - external_id: {item['external_id']}")
                    else:
                        print("⚠️ 没有找到队列项")
                        
                finally:
                    await conn.close()
                    
            except Exception as e:
                print(f"⚠️ 检查队列失败: {e}")
            
            # 6. 验证更新结果
            print("\n🔍 6. 验证更新结果...")
            verify_response = requests.get(
                f"{self.server5_base_url}/api/entries/{external_id}",
                timeout=10
            )
            
            if verify_response.status_code == 200:
                updated_entry = verify_response.json()
                print(f"✅ 验证成功:")
                print(f"  - duration: {updated_entry.get('duration')} (期望: 8.5)")
                print(f"  - model: {updated_entry.get('model')} (期望: 详细测试更新描述)")
                print(f"  - project_number: {updated_entry.get('project_number')} (期望: TEST_PROJECT_UPDATE)")
                print(f"  - category: {updated_entry.get('category')} (期望: TEST_STATUS_UPDATE)")
                print(f"  - source: {updated_entry.get('source')} (期望: user)")
                
                # 检查字段是否正确更新
                duration_ok = updated_entry.get('duration') == 8.5
                model_ok = updated_entry.get('model') == "详细测试更新描述"
                project_ok = updated_entry.get('project_number') == "TEST_PROJECT_UPDATE"
                category_ok = updated_entry.get('category') == "TEST_STATUS_UPDATE"
                source_ok = updated_entry.get('source') == "user"
                
                if all([duration_ok, model_ok, project_ok, category_ok, source_ok]):
                    print("🎉 所有字段更新正确！")
                else:
                    print("⚠️ 部分字段更新不正确")
                    print(f"  - duration: {'✅' if duration_ok else '❌'}")
                    print(f"  - model: {'✅' if model_ok else '❌'}")
                    print(f"  - project_number: {'✅' if project_ok else '❌'}")
                    print(f"  - category: {'✅' if category_ok else '❌'}")
                    print(f"  - source: {'✅' if source_ok else '❌'}")
            else:
                print(f"⚠️ 验证失败: {verify_response.status_code}")
            
            print("\n" + "="*80)
            print("🎉 详细更新流程测试完成！")
            return True
            
        except Exception as e:
            print(f"❌ 测试失败: {e}")
            return False

async def main():
    """主函数"""
    test = DetailedUpdateFlowTest()
    success = await test.test_complete_update_flow()
    
    if success:
        print("✅ 所有测试通过")
    else:
        print("❌ 测试失败")
    
    return success

if __name__ == "__main__":
    asyncio.run(main()) 