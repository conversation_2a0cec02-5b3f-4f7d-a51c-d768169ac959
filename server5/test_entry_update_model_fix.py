#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试EntryUpdate模型的修复
"""

from pydantic import BaseModel
from typing import Optional

def test_entry_update_model():
    """测试EntryUpdate模型"""
    
    print("EntryUpdate模型测试")
    print("="*60)
    
    # 模拟修复前的EntryUpdate模型（旧版本）
    class OldEntryUpdate(BaseModel):
        """修复前的EntryUpdate模型"""
        entry_date: Optional[str] = None
        employee_id: Optional[str] = None
        duration: Optional[float] = None
        description: Optional[str] = None
        project_code: Optional[str] = None
        department: Optional[str] = None
        overduration: Optional[float] = None
        break_hours: Optional[float] = None
        status: Optional[str] = None
        notes: Optional[str] = None
        source: Optional[str] = None
    
    # 模拟修复后的EntryUpdate模型（新版本）
    class NewEntryUpdate(BaseModel):
        """修复后的EntryUpdate模型"""
        entry_date: Optional[str] = None
        employee_id: Optional[str] = None
        duration: Optional[float] = None
        # 新字段名（客户端实际发送的）
        model: Optional[str] = None
        number: Optional[str] = None
        factory_number: Optional[str] = None
        project_number: Optional[str] = None
        unit_number: Optional[str] = None
        category: Optional[str] = None
        item: Optional[str] = None
        department: Optional[str] = None
        source: Optional[str] = None
        # 兼容旧字段名
        description: Optional[str] = None
        project_code: Optional[str] = None
        overduration: Optional[float] = None
        break_hours: Optional[float] = None
        status: Optional[str] = None
        notes: Optional[str] = None
    
    # 模拟客户端发送的数据
    client_data = {
        "entry_date": "2025-07-15",
        "employee_id": "215829",
        "duration": 0.2,
        "model": "",
        "number": "",
        "factory_number": "",
        "project_number": "",
        "unit_number": "",
        "category": "8",  # 用户输入的区分
        "item": "9",      # 用户输入的项目
        "department": "131",
        "source": "user"
    }
    
    print("1. 客户端发送的数据:")
    for field, value in client_data.items():
        print(f"   {field}: {value}")
    
    print("\n" + "="*60)
    
    print("2. 修复前的EntryUpdate模型字段:")
    old_model = OldEntryUpdate()
    for field in old_model.__fields__.keys():
        print(f"   {field}")
    
    print("\n" + "="*60)
    
    print("3. 修复后的EntryUpdate模型字段:")
    new_model = NewEntryUpdate()
    for field in new_model.__fields__.keys():
        print(f"   {field}")
    
    print("\n" + "="*60)
    
    print("4. 字段对比:")
    old_fields = set(old_model.__fields__.keys())
    new_fields = set(new_model.__fields__.keys())
    
    print("   修复前缺少的字段:")
    missing_fields = {"model", "number", "factory_number", "project_number", "unit_number", "category", "item"} - old_fields
    for field in missing_fields:
        print(f"   - {field}")
    
    print("\n   修复后新增的字段:")
    added_fields = new_fields - old_fields
    for field in added_fields:
        print(f"   - {field}")
    
    print("\n" + "="*60)
    
    print("5. 数据解析测试:")
    
    # 测试修复前的模型
    try:
        old_parsed = OldEntryUpdate(**client_data)
        print("   修复前 - 解析成功，但会忽略未知字段")
        print(f"   - category字段: {old_parsed.category if hasattr(old_parsed, 'category') else '字段不存在'}")
        print(f"   - item字段: {old_parsed.item if hasattr(old_parsed, 'item') else '字段不存在'}")
        print(f"   - model字段: {old_parsed.model if hasattr(old_parsed, 'model') else '字段不存在'}")
    except Exception as e:
        print(f"   修复前 - 解析失败: {e}")
    
    # 测试修复后的模型
    try:
        new_parsed = NewEntryUpdate(**client_data)
        print("\n   修复后 - 解析成功，所有字段都被接受")
        print(f"   - category字段: {new_parsed.category}")
        print(f"   - item字段: {new_parsed.item}")
        print(f"   - model字段: {new_parsed.model}")
    except Exception as e:
        print(f"   修复后 - 解析失败: {e}")
    
    print("\n" + "="*60)
    
    print("6. 测试结果:")
    if "category" in new_fields and "item" in new_fields and "model" in new_fields:
        print("   ✅ 修复成功：EntryUpdate模型包含所有必要字段")
    else:
        print("   ❌ 修复失败：EntryUpdate模型缺少必要字段")
    
    print("\n" + "="*60)
    
    print("7. 预期效果:")
    print("   - 客户端发送的所有字段都会被FastAPI接受")
    print("   - 字段映射逻辑会正确处理category和item字段")
    print("   - entries表中会保存正确的值：category=8, item=9")
    print("   - server6会接收到正确的值：区分=8, 項目=9")

if __name__ == "__main__":
    test_entry_update_model() 