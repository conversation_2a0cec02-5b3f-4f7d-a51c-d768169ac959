import asyncio
import asyncpg

# Database connection
PG_DATABASE_URL = "***************************************************/imdb"

async def check_triggers():
    """Check the current trigger status on the entries table"""
    conn = None
    try:
        print("=== 检查 entries 表的触发器状态 ===")
        conn = await asyncpg.connect(PG_DATABASE_URL)
        
        # 查询触发器信息
        triggers = await conn.fetch("""
            SELECT 
                trigger_name, 
                event_manipulation, 
                action_statement,
                action_timing
            FROM information_schema.triggers 
            WHERE event_object_table = 'entries'
            ORDER BY trigger_name;
        """)
        
        if triggers:
            print(f"找到 {len(triggers)} 个触发器:")
            for trigger in triggers:
                print(f"  触发器名: {trigger['trigger_name']}")
                print(f"  事件类型: {trigger['event_manipulation']}")
                print(f"  执行时机: {trigger['action_timing']}")
                print(f"  执行语句: {trigger['action_statement']}")
                print("-" * 50)
        else:
            print("❌ 没有找到 entries 表的触发器!")
        
        # 检查触发器函数是否存在
        print("\n=== 检查触发器函数 ===")
        functions = await conn.fetch("""
            SELECT 
                routine_name, 
                routine_type,
                routine_definition
            FROM information_schema.routines 
            WHERE routine_name LIKE '%entries%' 
            OR routine_name LIKE '%enqueue%'
            ORDER BY routine_name;
        """)
        
        if functions:
            print(f"找到 {len(functions)} 个相关函数:")
            for func in functions:
                print(f"  函数名: {func['routine_name']}")
                print(f"  类型: {func['routine_type']}")
                print(f"  定义: {func['routine_definition'][:200]}...")
                print("-" * 50)
        else:
            print("❌ 没有找到相关的触发器函数!")
        
    except Exception as e:
        print(f"❌ 检查触发器时出错: {e}")
    finally:
        if conn:
            await conn.close()

if __name__ == "__main__":
    asyncio.run(check_triggers()) 