#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
检查触发器状态
"""

import asyncio
import sys
from pathlib import Path

# 添加项目根目录到Python路径
sys.path.append(str(Path(__file__).parent.parent))

from app.database import IMDBClient

async def check_trigger_status():
    """检查触发器状态"""
    imdb_client = IMDBClient()
    
    try:
        await imdb_client.connect()
        
        print("🔍 检查触发器状态")
        print("=" * 60)
        
        # 1. 检查触发器函数
        print("📋 1. 检查触发器函数...")
        result = await imdb_client.execute_query("""
            SELECT pg_get_functiondef(oid) as function_def 
            FROM pg_proc 
            WHERE proname = 'trg_entries_enqueue'
        """)
        
        if result:
            print("✅ 找到触发器函数:")
            print(result[0]['function_def'])
        else:
            print("❌ 未找到触发器函数")
            return
        
        # 2. 检查触发器
        print("\n📋 2. 检查触发器...")
        trigger_result = await imdb_client.execute_query("""
            SELECT trigger_name, event_manipulation, action_statement
            FROM information_schema.triggers 
            WHERE trigger_name = 'trg_entries_enqueue'
        """)
        
        if trigger_result:
            print("✅ 找到触发器:")
            for row in trigger_result:
                print(f"  触发器名: {row['trigger_name']}")
                print(f"  事件: {row['event_manipulation']}")
                print(f"  动作: {row['action_statement']}")
        else:
            print("❌ 未找到触发器")
            return
        
        # 3. 检查entries表的source字段分布
        print("\n📋 3. 检查entries表的source字段分布...")
        source_result = await imdb_client.execute_query("""
            SELECT source, COUNT(*) as count
            FROM entries 
            GROUP BY source
            ORDER BY source
        """)
        
        if source_result:
            print("✅ source字段分布:")
            for row in source_result:
                print(f"  {row['source']}: {row['count']} 条记录")
        else:
            print("❌ 无法获取source字段分布")
        
        # 4. 检查最近的队列项
        print("\n📋 4. 检查最近的队列项...")
        queue_result = await imdb_client.execute_query("""
            SELECT queue_id, entry_id, external_id, operation, synced, created_ts
            FROM entries_push_queue 
            ORDER BY created_ts DESC 
            LIMIT 5
        """)
        
        if queue_result:
            print("✅ 最近的队列项:")
            for row in queue_result:
                print(f"  queue_id={row['queue_id']}, entry_id={row['entry_id']}, external_id={row['external_id']}, operation={row['operation']}, synced={row['synced']}")
        else:
            print("ℹ️ 没有队列项")
        
        # 5. 检查external_id=603639的记录状态
        print("\n📋 5. 检查external_id=603639的记录状态...")
        record_result = await imdb_client.execute_query("""
            SELECT id, external_id, employee_id, source, ts
            FROM entries 
            WHERE external_id = 603639
        """)
        
        if record_result:
            print("✅ 找到记录:")
            for row in record_result:
                print(f"  id={row['id']}, external_id={row['external_id']}, employee_id={row['employee_id']}, source={row['source']}")
        else:
            print("ℹ️ 未找到external_id=603639的记录（可能已被删除）")
        
        # 6. 检查是否有DELETE队列项
        print("\n📋 6. 检查DELETE队列项...")
        delete_queue_result = await imdb_client.execute_query("""
            SELECT queue_id, entry_id, external_id, operation, synced, created_ts
            FROM entries_push_queue 
            WHERE operation = 'DELETE'
            ORDER BY created_ts DESC 
            LIMIT 5
        """)
        
        if delete_queue_result:
            print("✅ 最近的DELETE队列项:")
            for row in delete_queue_result:
                print(f"  queue_id={row['queue_id']}, entry_id={row['entry_id']}, external_id={row['external_id']}, synced={row['synced']}")
        else:
            print("ℹ️ 没有DELETE队列项")
        
    except Exception as e:
        print(f"❌ 检查失败: {e}")
    finally:
        await imdb_client.disconnect()

if __name__ == "__main__":
    asyncio.run(check_trigger_status()) 