#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试客户端删除响应的实际格式
"""

import requests
import json

def test_client_delete_response():
    """测试客户端删除响应的实际格式"""
    
    print("🧪 测试客户端删除响应的实际格式")
    print("=" * 60)
    
    # 测试一个存在的记录
    test_external_id = 603659
    
    try:
        # 调用Server5删除API
        url = f"http://localhost:8009/api/entries/{test_external_id}"
        print(f"📡 请求URL: {url}")
        
        response = requests.delete(url, timeout=10)
        
        print(f"📊 HTTP状态码: {response.status_code}")
        print(f"📋 响应头: {dict(response.headers)}")
        
        # 尝试解析响应体
        try:
            response_json = response.json()
            print(f"📦 JSON响应: {json.dumps(response_json, indent=2, ensure_ascii=False)}")
            
            # 检查客户端响应处理逻辑
            message = response_json.get("message", "")
            print(f"📝 响应message字段: '{message}'")
            
            # 模拟客户端的响应处理逻辑
            if message and "删除成功" in message:
                print("✅ 客户端响应处理: 成功")
                print("✅ 应该显示: 削除操作成功")
            else:
                print("❌ 客户端响应处理: 失败")
                print(f"❌ 应该显示: 削除操作失败: {response.status_code}")
                
                # 检查其他可能的字段
                detail = response_json.get("detail", "")
                if detail:
                    print(f"📋 响应detail字段: '{detail}'")
                    
        except json.JSONDecodeError:
            print(f"❌ 响应不是有效的JSON: {response.text}")
            
    except requests.exceptions.RequestException as e:
        print(f"❌ 请求失败: {e}")

if __name__ == "__main__":
    test_client_delete_response() 