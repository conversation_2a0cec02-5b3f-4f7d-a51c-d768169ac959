#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试修复后的UPDATE操作
验证URL路径修复
"""

import asyncio
import sys
from pathlib import Path
import logging

# 添加项目路径
sys.path.append(str(Path(__file__).parent))

from app.database import IMDBClient, RedisClient, MongoDBClient
from app.services.f1_listener import ListenerService
from app.services.f2_push_writer_fixed import PushWriterServiceFixed

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

async def test_update_fix():
    """测试修复后的UPDATE操作"""
    imdb_client = IMDBClient()
    redis_client = RedisClient()
    mongo_client = MongoDBClient()
    
    try:
        await imdb_client.connect()
        await redis_client.connect()
        await mongo_client.connect()
        
        # 目标记录
        target_entry_id = 10612
        target_external_id = 602610
        
        logger.info(f"🎯 测试修复后的UPDATE操作")
        logger.info(f"   目标记录: entry_id={target_entry_id}, external_id={target_external_id}")
        
        # 获取当前记录状态
        entry_info = await imdb_client.execute_query(
            "SELECT id, employee_id, entry_date, duration, external_id, source FROM entries WHERE id = $1",
            target_entry_id
        )
        
        if entry_info:
            entry = entry_info[0]
            logger.info(f"📊 当前记录状态:")
            logger.info(f"   ID: {entry['id']}")
            logger.info(f"   员工ID: {entry['employee_id']}")
            logger.info(f"   日期: {entry['entry_date']}")
            logger.info(f"   duration: {entry['duration']}")
            logger.info(f"   external_id: {entry['external_id']}")
            logger.info(f"   source: {entry['source']}")
        
        # 清理之前的队列项
        await imdb_client.execute_command(
            "DELETE FROM entries_push_queue WHERE entry_id = $1",
            target_entry_id
        )
        
        # 启动f1和f2服务
        logger.info("🚀 启动f1和f2服务...")
        f1_service = ListenerService()
        f2_service = PushWriterServiceFixed()
        
        await f1_service.start()
        await f2_service.start()
        logger.info("✅ f1和f2服务启动成功")
        
        # 执行用户更新操作（source='user'）
        logger.info("📝 执行用户更新操作（source='user'）- 修改duration为8.00...")
        
        # 使用事务更新，设置source='user'
        async with imdb_client.pool.acquire() as conn:
            async with conn.transaction():
                await conn.execute(
                    "UPDATE entries SET duration = $1, source = 'user' WHERE id = $2",
                    8.00, target_entry_id
                )
        
        logger.info("✅ 用户更新操作完成")
        
        # 等待触发器执行和队列处理
        logger.info("⏳ 等待触发器执行和队列处理...")
        await asyncio.sleep(10)
        
        # 检查队列状态
        unsynced_items = await imdb_client.get_queue_items(synced=False, limit=10)
        logger.info(f"📋 未同步队列项: {len(unsynced_items)} 个")
        
        if unsynced_items:
            logger.info("📋 未同步队列项详情:")
            for item in unsynced_items:
                logger.info(f"    queue_id={item['queue_id']}, operation={item['operation']}, entry_id={item['entry_id']}")
        
        # 检查最终记录状态
        final_entry_info = await imdb_client.execute_query(
            "SELECT id, employee_id, entry_date, duration, external_id, source FROM entries WHERE id = $1",
            target_entry_id
        )
        
        if final_entry_info:
            final_entry = final_entry_info[0]
            logger.info(f"📊 最终记录状态:")
            logger.info(f"   ID: {final_entry['id']}")
            logger.info(f"   员工ID: {final_entry['employee_id']}")
            logger.info(f"   日期: {final_entry['entry_date']}")
            logger.info(f"   duration: {final_entry['duration']}")
            logger.info(f"   external_id: {final_entry['external_id']}")
            logger.info(f"   source: {final_entry['source']}")
        
        # 停止服务
        await f1_service.stop()
        await f2_service.stop()
        
        logger.info("🎉 测试完成！")
        
    except Exception as e:
        logger.error(f"❌ 测试过程中发生错误: {e}", exc_info=True)
    finally:
        await imdb_client.disconnect()
        await redis_client.disconnect()
        await mongo_client.disconnect()

async def main():
    """主函数"""
    await test_update_fix()

if __name__ == "__main__":
    asyncio.run(main()) 