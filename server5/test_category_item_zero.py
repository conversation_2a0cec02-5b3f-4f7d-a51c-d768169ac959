#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试category和item字段为0时的处理逻辑
验证插入和更新操作的数据流
"""

import asyncio
import sys
from pathlib import Path
sys.path.append(str(Path(__file__).parent))

from app.database import IMDBClient
from app.services.f2_push_writer import PushWriterServiceFixed
from app.utils.server6_client import Server6Client

async def test_category_item_zero():
    """测试category和item字段为0时的处理"""
    
    print("🧪 测试category和item字段为0时的处理逻辑")
    print("=" * 50)
    
    # 初始化客户端和服务
    imdb_client = IMDBClient()
    f2_service = PushWriterServiceFixed()
    server6_client = Server6Client()
    
    try:
        # 1. 连接数据库
        print("📡 步骤1: 连接数据库...")
        await imdb_client.connect()
        await f2_service.start()
        print("✅ 数据库连接成功")
        
        # 2. 插入测试数据（category=0, item=0）
        print("\n📝 步骤2: 插入测试数据（category=0, item=0）...")
        test_data = {
            'employee_id': '215829',
            'entry_date': '2025/07/15',
            'model': '',
            'number': '',
            'factory_number': '',
            'project_number': 'TEST001',
            'unit_number': '',
            'category': 0,  # 测试0值
            'item': 0,      # 测试0值
            'duration': 0.5,
            'department': '131'
        }
        
        # 插入数据到PostgreSQL
        entry_id = await imdb_client.create_entry(test_data, source='user')
        print(f"✅ 插入成功，entry_id: {entry_id}")
        
        # 3. 验证PostgreSQL中的数据
        print("\n📋 步骤3: 验证PostgreSQL中的数据...")
        entry_record = await imdb_client.fetch_one(
            "SELECT * FROM entries WHERE id = $1", entry_id
        )
        
        if entry_record:
            print(f"📊 PostgreSQL中的数据:")
            print(f"   category: {entry_record['category']} (类型: {type(entry_record['category'])})")
            print(f"   item: {entry_record['item']} (类型: {type(entry_record['item'])})")
            print(f"   source: {entry_record['source']}")
        else:
            print("❌ 无法获取插入的记录")
            return
        
        # 4. 模拟f2推送处理
        print("\n🔄 步骤4: 模拟f2推送处理...")
        
        # 准备MDB数据（模拟f2_push_writer中的逻辑）
        mdb_data = {
            'employee_id': entry_record.get('employee_id'),
            'entry_date': entry_record.get('entry_date').strftime('%Y/%m/%d') if entry_record.get('entry_date') else '2025/07/15',
            'model': entry_record.get('model') if entry_record.get('model') and entry_record.get('model').strip() else None,
            'number': entry_record.get('number') if entry_record.get('number') and entry_record.get('number').strip() else None,
            'factory_number': entry_record.get('factory_number') if entry_record.get('factory_number') and entry_record.get('factory_number').strip() else None,
            'project_number': entry_record.get('project_number') if entry_record.get('project_number') and entry_record.get('project_number').strip() else None,
            'unit_number': entry_record.get('unit_number') if entry_record.get('unit_number') and entry_record.get('unit_number').strip() else None,
            'category': str(entry_record.get('category')) if entry_record.get('category') is not None else None,  # 整数转字符串
            'item': str(entry_record.get('item')) if entry_record.get('item') is not None else None,  # 整数转字符串
            'duration': float(entry_record.get('duration', 0.0)),  # 将Decimal转换为float
            'department': entry_record.get('department') if entry_record.get('department') and entry_record.get('department').strip() else None
        }
        
        print(f"📦 准备发送给Server6的数据:")
        print(f"   category: {mdb_data['category']} (类型: {type(mdb_data['category'])})")
        print(f"   item: {mdb_data['item']} (类型: {type(mdb_data['item'])})")
        print(f"   完整数据: {mdb_data}")
        
        # 5. 测试Server6客户端处理
        print("\n📤 步骤5: 测试Server6客户端处理...")
        
        # 模拟Server6客户端的处理逻辑
        field_mapping = {
            'employee_id': '従業員ｺｰﾄﾞ',
            'entry_date': '日付',
            'model': '機種',
            'number': '号機',
            'factory_number': '工場製番',
            'project_number': '工事番号',
            'unit_number': 'ﾕﾆｯﾄ番号',
            'category': '区分',
            'item': '項目',
            'duration': '時間',
            'department': '所属ｺｰﾄﾞ'
        }
        
        serializable_data = {}
        for key, value in mdb_data.items():
            japanese_key = field_mapping.get(key, key)
            
            # 处理空值：发送None给Server6，让Server6处理NULL转换
            if value is None:
                serializable_data[japanese_key] = None
                print(f"  📤 字段 {key} 设置为 None")
                continue
            
            # 处理空字符串：发送None给Server6，让Server6处理NULL转换
            if isinstance(value, str) and value.strip() == '':
                serializable_data[japanese_key] = None
                print(f"  📤 字段 {key} (空字符串) 设置为 None")
                continue
            
            # 特殊处理：确保category和item字段发送正确的值
            if key in ['category', 'item']:
                # 确保值不为空字符串或None
                if value == "" or value is None:
                    final_value = "0"
                    print(f"  📤 字段 {key} (空值) 设置为默认值 '0'")
                else:
                    final_value = str(value)  # 确保是字符串
                    print(f"  📤 字段 {key} 设置为 '{value}'")
                
                serializable_data[japanese_key] = final_value
                continue
            
            # 其他字段直接赋值
            serializable_data[japanese_key] = value
        
        print(f"📤 最终发送给Server6的数据: {serializable_data}")
        print(f"🔍 调试 - category最终值: {serializable_data.get('区分')}, 类型: {type(serializable_data.get('区分'))}")
        print(f"🔍 调试 - item最终值: {serializable_data.get('項目')}, 类型: {type(serializable_data.get('項目'))}")
        
        # 6. 清理测试数据
        print("\n🧹 步骤6: 清理测试数据...")
        await imdb_client.execute_command("DELETE FROM entries WHERE id = $1", entry_id)
        print(f"✅ 测试数据已清理: entry_id={entry_id}")
        
        print("\n✅ 测试完成！")
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        
    finally:
        await f2_service.stop()
        await imdb_client.disconnect()

if __name__ == "__main__":
    asyncio.run(test_category_item_zero()) 