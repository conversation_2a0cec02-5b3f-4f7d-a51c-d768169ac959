#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试f2删除操作调试
"""

import asyncio
import sys
from pathlib import Path
import logging

# 添加项目根目录到Python路径
sys.path.append(str(Path(__file__).parent.parent))

from app.database import IMDBClient
from app.utils.server6_client import Server6Client
from config.config import SERVER6_CONFIG

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

class F2DeleteDebugTest:
    """f2删除操作调试测试类"""
    
    def __init__(self):
        self.imdb_client = IMDBClient()
        self.server6_client = Server6Client()
        
    async def setup(self):
        """初始化连接"""
        try:
            await self.imdb_client.connect()
            await self.server6_client.connect()
            logger.info("✅ 数据库连接成功")
            return True
        except Exception as e:
            logger.error(f"❌ 数据库连接失败: {e}")
            return False
    
    async def cleanup(self):
        """清理连接"""
        await self.imdb_client.disconnect()
        await self.server6_client.disconnect()
    
    async def check_external_id_603658(self):
        """检查external_id=603658的记录状态"""
        logger.info("🔍 检查external_id=603658的记录状态")
        
        # 1. 检查entries表中是否存在
        entry_result = await self.imdb_client.fetch_one(
            "SELECT * FROM entries WHERE external_id = 603658"
        )
        
        if entry_result:
            logger.info(f"✅ entries表中存在记录: {entry_result}")
        else:
            logger.info("❌ entries表中不存在external_id=603658的记录")
        
        # 2. 检查entries_push_queue中的DELETE任务
        queue_result = await self.imdb_client.fetch_one(
            "SELECT * FROM entries_push_queue WHERE external_id = 603658 AND operation = 'DELETE' ORDER BY created_ts DESC LIMIT 1"
        )
        
        if queue_result:
            logger.info(f"📋 找到DELETE队列项: {queue_result}")
            
            # 检查队列项状态
            queue_id = queue_result['queue_id']
            synced = queue_result['synced']
            retry_count = queue_result.get('retry_count', 0)
            last_error = queue_result.get('last_error')
            
            logger.info(f"📊 队列项状态: queue_id={queue_id}, synced={synced}, retry_count={retry_count}")
            
            if last_error:
                logger.error(f"❌ 队列项错误: {last_error}")
            
            return queue_result
        else:
            logger.warning("⚠️ 未找到external_id=603658的DELETE队列项")
            return None
    
    async def test_server6_delete_603658(self):
        """测试Server6删除external_id=603658"""
        logger.info("🧪 测试Server6删除external_id=603658")
        
        try:
            # 先检查Server6中是否存在该记录
            logger.info("🔍 检查Server6中是否存在external_id=603658")
            
            # 尝试删除
            response = await self.server6_client.delete_entry(603658)
            logger.info(f"📤 Server6删除响应: {response}")
            
            if response.get('success'):
                logger.info("✅ Server6删除成功")
            else:
                logger.error(f"❌ Server6删除失败: {response}")
            
            return response
            
        except Exception as e:
            logger.error(f"❌ Server6删除异常: {e}")
            return None
    
    async def check_f2_service_status(self):
        """检查f2服务状态"""
        logger.info("🔍 检查f2服务状态")
        
        # 检查是否有f2相关的进程
        import psutil
        f2_processes = []
        
        for proc in psutil.process_iter(['pid', 'name', 'cmdline']):
            try:
                if 'f2' in proc.info['name'] or any('f2' in str(arg) for arg in proc.info['cmdline'] or []):
                    f2_processes.append(proc.info)
            except (psutil.NoSuchProcess, psutil.AccessDenied):
                pass
        
        if f2_processes:
            logger.info(f"✅ 找到f2相关进程: {f2_processes}")
        else:
            logger.warning("⚠️ 未找到f2相关进程")
        
        # 检查Redis中的f2状态
        try:
            from app.database import RedisClient
            redis_client = RedisClient()
            await redis_client.connect()
            
            # 检查f2相关的Redis键
            f2_keys = await redis_client.get_keys("server5:f2*")
            if f2_keys:
                logger.info(f"📊 Redis中的f2键: {f2_keys}")
            else:
                logger.warning("⚠️ Redis中未找到f2相关键")
            
            await redis_client.disconnect()
            
        except Exception as e:
            logger.error(f"❌ 检查Redis状态失败: {e}")
    
    async def run_debug_test(self):
        """运行调试测试"""
        logger.info("🚀 开始f2删除操作调试测试")
        logger.info("=" * 60)
        
        if not await self.setup():
            return
        
        try:
            # 1. 检查external_id=603658的状态
            queue_item = await self.check_external_id_603658()
            
            # 2. 检查f2服务状态
            await self.check_f2_service_status()
            
            # 3. 测试Server6删除
            await self.test_server6_delete_603658()
            
            # 4. 如果有队列项，手动处理
            if queue_item and not queue_item['synced']:
                logger.info("🔄 手动处理未同步的DELETE队列项")
                await self.manual_process_delete_queue_item(queue_item)
            
        finally:
            await self.cleanup()
    
    async def manual_process_delete_queue_item(self, queue_item):
        """手动处理DELETE队列项"""
        try:
            queue_id = queue_item['queue_id']
            external_id = queue_item['external_id']
            
            logger.info(f"🔄 手动处理DELETE队列项: queue_id={queue_id}, external_id={external_id}")
            
            # 调用Server6删除
            response = await self.server6_client.delete_entry(external_id)
            
            if response.get('success'):
                # 标记队列项为已同步
                await self.imdb_client.execute_command(
                    "UPDATE entries_push_queue SET synced = TRUE WHERE queue_id = $1",
                    queue_id
                )
                logger.info(f"✅ 手动处理成功: queue_id={queue_id}")
            else:
                logger.error(f"❌ 手动处理失败: {response}")
                
        except Exception as e:
            logger.error(f"❌ 手动处理异常: {e}")

async def main():
    """主函数"""
    test = F2DeleteDebugTest()
    await test.run_debug_test()

if __name__ == "__main__":
    asyncio.run(main()) 