# Entries网关解决方案

## 🎯 问题描述

需要在Server5中增加一个网关来写入数据到PostgreSQL的entries表中，确保：
1. 数据格式正确
2. 添加source="user"属性
3. 触发同步到MDB数据库
4. 支持完整的CRUD操作

## 📋 数据流分析

### 完整数据流
```
UI输入 → Server5 HTTP API → PostgreSQL entries → 触发器 → entries_push_queue → f1监听 → f2推送 → Server6 → MDB数据库
```

### 关键组件
- **entries表**: PostgreSQL主表，存储所有记录
- **entries_push_queue表**: 队列表，存储待同步任务
- **触发器**: 自动将用户操作加入队列
- **f2推送服务**: 处理队列任务，调用Server6
- **Server6**: MDB网关，执行实际的MDB操作

## 🔧 解决方案实现

### 1. 修改f2推送服务

**文件**: `server5/app/services/f2_push_writer.py`

**关键修改**:
- 从entries表中读取完整数据
- 正确转换数据类型（特别是Decimal到float）
- 处理空值，确保Server6接收正确的NULL值
- 添加详细的日志记录

**主要改进**:
```python
# 准备MDB插入数据 - 使用从entries表中读取的实际数据
mdb_data = {
    'employee_id': entry_data['employee_id'],
    'entry_date': entry_data['entry_date'].strftime('%Y/%m/%d') if entry_data['entry_date'] else datetime.now().strftime('%Y/%m/%d'),
    'model': entry_data['model'] if entry_data['model'] else '',
    'number': entry_data['number'] if entry_data['number'] else '',
    'factory_number': entry_data['factory_number'] if entry_data['factory_number'] else '',
    'project_number': entry_data['project_number'] if entry_data['project_number'] else '',
    'unit_number': entry_data['unit_number'] if entry_data['unit_number'] else '',
    'category': entry_data['category'] if entry_data['category'] is not None else 1,
    'item': entry_data['item'] if entry_data['item'] is not None else 1,
    'duration': float(entry_data['duration']) if entry_data['duration'] is not None else 0.0,
    'department': entry_data['department'] if entry_data['department'] else ''
}
```

### 2. 创建Entries网关

**文件**: `server5/app/routers/entries_gateway.py`

**功能**:
- 专门处理UI到PostgreSQL的数据写入
- 自动转换UI数据格式为entries表格式
- 确保source="user"属性
- 提供完整的CRUD API

**API端点**:
- `POST /gateway/entries/` - 创建记录
- `PUT /gateway/entries/{entry_id}` - 更新记录
- `DELETE /gateway/entries/{entry_id}` - 删除记录
- `GET /gateway/entries/status/{entry_id}` - 查询记录状态
- `GET /gateway/entries/sync-status` - 查询同步状态

**数据转换**:
```python
def _convert_ui_to_entries_format(ui_data: Dict[str, Any]) -> Dict[str, Any]:
    """将UI数据转换为entries表格式"""
    entries_data = {}
    
    # 日期处理
    if 'entry_date' in ui_data and ui_data['entry_date']:
        date_str = ui_data['entry_date']
        if '/' in date_str:
            # 转换 YYYY/MM/DD 为 YYYY-MM-DD
            entries_data['entry_date'] = date_str.replace('/', '-')
        else:
            entries_data['entry_date'] = date_str
    
    # 字段映射
    field_mapping = {
        'employee_id': 'employee_id',
        'duration': 'duration',
        'project_code': 'project_code',
        'status': 'status',
        'description': 'description',
        'department': 'department',
        'notes': 'notes',
        'model': 'model',
        'number': 'number',
        'factory_number': 'factory_number',
        'project_number': 'project_number',
        'unit_number': 'unit_number',
        'category': 'category',
        'item': 'item'
    }
    
    for ui_field, entries_field in field_mapping.items():
        if ui_field in ui_data and ui_data[ui_field] is not None:
            entries_data[entries_field] = ui_data[ui_field]
    
    # 确保source字段为user
    entries_data['source'] = 'user'
    
    return entries_data
```

### 3. 集成到主应用

**文件**: `server5/app/main.py`

**修改**:
- 导入新的网关路由
- 注册网关路由到FastAPI应用

```python
from app.routers.entries_gateway import router as entries_gateway_router

# 添加路由
app.include_router(entries_gateway_router)
```

## 🧪 测试验证

### 1. 完整数据流测试

**文件**: `server5/test_complete_data_flow.py`

**测试内容**:
- 模拟UI输入到PostgreSQL
- 验证触发器创建队列项
- 模拟f2处理队列项
- 验证MDB同步结果

### 2. 网关功能测试

**文件**: `server5/test_entries_gateway.py`

**测试内容**:
- 网关健康检查
- 创建entries记录
- 更新entries记录
- 查询记录状态
- 查询同步状态
- 删除entries记录

## 📊 数据格式说明

### entries表结构
```sql
CREATE TABLE entries (
    id BIGINT PRIMARY KEY,
    external_id INTEGER,
    entry_date DATE NOT NULL,
    employee_id TEXT NOT NULL,
    model TEXT,
    number TEXT,
    factory_number TEXT,
    project_number TEXT,
    unit_number TEXT,
    category INTEGER,
    item INTEGER,
    duration NUMERIC,
    department TEXT,
    source CHARACTER VARYING,
    ts TIMESTAMP WITH TIME ZONE NOT NULL
);
```

### UI数据格式
```json
{
    "entry_date": "2025/06/27",
    "employee_id": "215829",
    "duration": 8.5,
    "project_code": "TEST001",
    "status": "3",
    "description": "测试项目",
    "department": "131",
    "notes": "备注信息",
    "model": "TEST_MODEL",
    "number": "TEST001",
    "factory_number": "FACTORY001",
    "project_number": "PROJECT001",
    "unit_number": "UNIT001",
    "category": 3,
    "item": 7
}
```

### Server6期望格式
```json
{
    "従業員ｺｰﾄﾞ": "215829",
    "日付": "2025-06-27",
    "機種": "TEST_MODEL",
    "号機": "TEST001",
    "工場製番": "FACTORY001",
    "工事番号": "PROJECT001",
    "ﾕﾆｯﾄ番号": "UNIT001",
    "区分": 3,
    "項目": 7,
    "時間": 8.5,
    "所属ｺｰﾄﾞ": "131"
}
```

## 🚀 使用方法

### 1. 启动服务
```bash
# 启动Server5 (完整模式)
cd server5
python start_server5_notwith_api.py

# 启动Server6 (在Windows机器上)
cd server6
python start_server6.py
```

### 2. 使用网关API
```bash
# 创建记录
curl -X POST "http://localhost:8009/gateway/entries/" \
  -H "Content-Type: application/json" \
  -d '{
    "entry_date": "2025/06/27",
    "employee_id": "215829",
    "duration": 8.5,
    "project_code": "TEST001",
    "department": "131"
  }'

# 更新记录
curl -X PUT "http://localhost:8009/gateway/entries/123" \
  -H "Content-Type: application/json" \
  -d '{
    "duration": 9.0,
    "description": "更新后的描述"
  }'

# 查询状态
curl "http://localhost:8009/gateway/entries/status/123"

# 查询同步状态
curl "http://localhost:8009/gateway/entries/sync-status"
```

### 3. 运行测试
```bash
# 测试完整数据流
python test_complete_data_flow.py

# 测试网关功能
python test_entries_gateway.py
```

## ✅ 验证要点

### 1. 数据完整性
- ✅ UI输入数据正确写入PostgreSQL
- ✅ source字段自动设置为"user"
- ✅ 触发器正确创建队列项
- ✅ f2正确读取entries表数据
- ✅ 数据格式正确转换
- ✅ Server6成功写入MDB

### 2. 同步机制
- ✅ 用户操作触发队列
- ✅ 系统操作不触发队列
- ✅ 队列项正确标记为已同步
- ✅ external_id正确回写

### 3. 错误处理
- ✅ 数据类型转换错误处理
- ✅ 空值正确处理
- ✅ 网络错误重试机制
- ✅ 详细错误日志记录

## 🔍 故障排除

### 常见问题

1. **触发器不工作**
   - 检查source字段是否为"user"
   - 验证触发器函数是否正确创建

2. **f2无法读取数据**
   - 检查entries表结构
   - 验证数据类型转换

3. **Server6连接失败**
   - 检查Server6是否在Windows机器上运行
   - 验证网络连接和端口

4. **数据格式错误**
   - 检查字段映射
   - 验证日期格式转换

### 调试命令
```bash
# 检查队列状态
psql -h 192.168.3.93 -U postgres -d imdb -c "SELECT * FROM entries_push_queue WHERE synced = FALSE;"

# 检查entries表
psql -h 192.168.3.93 -U postgres -d imdb -c "SELECT * FROM entries WHERE source = 'user' ORDER BY id DESC LIMIT 5;"

# 检查Server6状态
curl http://192.168.3.93:8019/health
```

## 📈 性能优化

1. **批量处理**: f2支持批量处理队列项
2. **连接池**: 使用数据库连接池提高性能
3. **重试机制**: 自动重试失败的同步操作
4. **监控指标**: 记录详细的性能指标

## 🎉 总结

通过实现这个Entries网关解决方案，我们成功实现了：

1. **完整的UI到MDB数据流**: UI输入 → PostgreSQL → 触发器 → 队列 → f2 → Server6 → MDB
2. **正确的数据格式转换**: 自动处理UI数据格式到entries表格式的转换
3. **可靠的同步机制**: 确保用户操作正确触发MDB同步
4. **完善的错误处理**: 提供详细的错误信息和重试机制
5. **全面的测试覆盖**: 包含完整的数据流测试和网关功能测试

这个解决方案确保了数据的一致性和可靠性，同时提供了良好的用户体验和系统可维护性。 