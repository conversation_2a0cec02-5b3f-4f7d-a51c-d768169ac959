#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
UPDATE 修复验证脚本
直接验证我们修复的 UPDATE 逻辑是否正确工作
"""

import asyncio
import asyncpg
import sys
from pathlib import Path
import aiohttp
import json
from datetime import datetime
from decimal import Decimal

# 添加项目路径
sys.path.append(str(Path(__file__).parent))

# 数据库连接
PG_DATABASE_URL = "***************************************************/imdb"
SERVER6_URL = "http://192.168.3.93:8009"
TARGET_EXTERNAL_ID = 602631

async def test_update_logic_verification():
    """验证 UPDATE 逻辑修复"""
    print("🔧 UPDATE 修复验证测试")
    print("=" * 50)
    
    conn = None
    try:
        # 1. 连接数据库
        conn = await asyncpg.connect(PG_DATABASE_URL)
        print("✅ 数据库连接成功")
        
        # 2. 创建一个新的 UPDATE 任务
        print(f"\n📝 创建 UPDATE 测试任务...")
        
        # 更新记录
        await conn.execute("""
            UPDATE public.entries
            SET
                duration = $1,
                source = 'user',
                ts = NOW()
            WHERE external_id = $2
        """, 4.5, TARGET_EXTERNAL_ID)  # 测试值：4.5
        
        print(f"   ✅ 记录已更新: external_id={TARGET_EXTERNAL_ID}, duration=4.5")
        
        # 3. 等待触发器
        await asyncio.sleep(2)
        
        # 4. 获取最新的队列项
        queue_item = await conn.fetchrow("""
            SELECT queue_id, operation, entry_id, external_id
            FROM entries_push_queue
            WHERE external_id = $1 AND operation = 'UPDATE'
            ORDER BY created_ts DESC
            LIMIT 1
        """, TARGET_EXTERNAL_ID)
        
        if not queue_item:
            print("❌ 触发器没有创建队列项")
            return False
        
        print(f"   ✅ 触发器创建队列项: queue_id={queue_item['queue_id']}")
        
        # 5. 模拟我们修复的 F2 UPDATE 逻辑
        print(f"\n🔄 模拟 F2 UPDATE 处理逻辑...")
        
        entry_id = queue_item['entry_id']
        external_id = queue_item['external_id']
        queue_id = queue_item['queue_id']
        
        # 获取完整的entry数据（模拟我们的修复代码）
        entry_data_row = await conn.fetchrow("SELECT * FROM entries WHERE id = $1", entry_id)
        entry_data = dict(entry_data_row)
        
        # 准备MDB更新数据，确保数据类型正确（这是我们的关键修复）
        mdb_data = {
            'employee_id': entry_data.get('employee_id'),
            'entry_date': entry_data.get('entry_date').strftime('%Y-%m-%d') if entry_data.get('entry_date') else None,
            'model': entry_data.get('model'),
            'number': entry_data.get('number'),
            'factory_number': entry_data.get('factory_number'),
            'project_number': entry_data.get('project_number'),
            'unit_number': entry_data.get('unit_number'),
            'category': entry_data.get('category'),
            'item': entry_data.get('item'),
            'duration': float(entry_data.get('duration', 0.0)),  # 关键修复: Decimal -> float
            'department': entry_data.get('department')
        }
        
        print(f"   📤 准备发送到 Server6 的数据:")
        print(f"   - duration: {mdb_data['duration']} ({type(mdb_data['duration']).__name__})")
        print(f"   - employee_id: {mdb_data['employee_id']}")
        print(f"   - entry_date: {mdb_data['entry_date']}")
        
        # 6. 测试 Server6 API 调用
        print(f"\n🌐 测试 Server6 API 调用...")
        
        # 转换为 Server6 期望的格式 (使用英文字段名)
        server6_payload = {
            'employee_id': mdb_data['employee_id'],
            'entry_date': mdb_data['entry_date'],
            'model': mdb_data['model'],
            'number': mdb_data['number'],
            'factory_number': mdb_data['factory_number'],
            'project_number': mdb_data['project_number'],
            'unit_number': mdb_data['unit_number'],
            'category': mdb_data['category'],
            'item': mdb_data['item'],
            'duration': mdb_data['duration'],  # 这里应该是 float 类型
            'department': mdb_data['department']
        }
        
        try:
            async with aiohttp.ClientSession() as session:
                url = f"{SERVER6_URL}/mdb/entries/update/{external_id}"
                
                print(f"   📡 调用 Server6: PUT {url}")
                print(f"   📦 Payload: {json.dumps(server6_payload, ensure_ascii=False, default=str)}")
                
                async with session.put(url, json=server6_payload, timeout=10) as response:
                    response_text = await response.text()
                    
                    if response.status == 200:
                        print(f"   ✅ Server6 API 调用成功!")
                        print(f"   📄 响应: {response_text}")
                        
                        # 7. 标记队列项为已同步（使用我们修复的 SQL）
                        await conn.execute("UPDATE entries_push_queue SET synced = TRUE WHERE queue_id = $1", queue_id)
                        print(f"   ✅ 队列项已标记为同步")
                        
                        # 8. 最终验证
                        final_status = await conn.fetchrow("SELECT synced FROM entries_push_queue WHERE queue_id = $1", queue_id)
                        final_duration = await conn.fetchrow("SELECT duration FROM entries WHERE external_id = $1", external_id)
                        
                        print(f"\n🎉 验证结果:")
                        print(f"   ✅ PostgreSQL duration: {final_duration['duration']}")
                        print(f"   ✅ 队列项 synced: {final_status['synced']}")
                        print(f"   ✅ 数据类型转换: Decimal -> float 成功")
                        print(f"   ✅ Server6 API 调用成功")
                        print(f"   ✅ SQL 语句修复成功")
                        
                        return True
                        
                    else:
                        print(f"   ❌ Server6 API 调用失败: {response.status}")
                        print(f"   📄 错误响应: {response_text}")
                        return False
                        
        except Exception as api_error:
            print(f"   ❌ Server6 API 调用异常: {api_error}")
            return False
        
    except Exception as e:
        print(f"❌ 测试过程中出错: {e}")
        import traceback
        traceback.print_exc()
        return False
    finally:
        if conn:
            await conn.close()

async def main():
    """主函数"""
    print("🧪 UPDATE 修复验证测试")
    
    success = await test_update_logic_verification()
    
    print("=" * 50)
    if success:
        print("🎉 UPDATE 修复验证成功！")
        print("💡 所有修复都正确工作:")
        print("   ✅ 数据类型转换 (Decimal -> float)")
        print("   ✅ SQL 语句修复 (移除不存在的 source 列)")
        print("   ✅ Server6 API 集成")
        print("   ✅ 队列同步逻辑")
    else:
        print("❌ UPDATE 修复验证失败")
        print("💡 需要进一步调试")
    print("=" * 50)

if __name__ == "__main__":
    asyncio.run(main()) 