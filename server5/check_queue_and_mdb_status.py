#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
检查队列状态和MDB连接问题
"""

import asyncio
import sys
from pathlib import Path
import logging

# 添加项目路径
sys.path.append(str(Path(__file__).parent))

from app.database import IMDBClient, RedisClient, MongoDBClient
from app.utils.server6_client import Server6Client

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

async def check_queue_and_mdb():
    """检查队列状态和MDB连接"""
    imdb_client = IMDBClient()
    server6_client = Server6Client()
    
    try:
        await imdb_client.connect()
        
        # 1. 检查队列状态
        logger.info("🔍 检查entries_push_queue状态...")
        
        # 检查所有队列项
        all_items = await imdb_client.execute_query(
            "SELECT * FROM entries_push_queue ORDER BY queue_id DESC LIMIT 10"
        )
        
        logger.info(f"📋 队列项总数: {len(all_items)}")
        
        if all_items:
            logger.info("📋 最近的队列项:")
            for item in all_items:
                logger.info(f"  queue_id={item['queue_id']}, operation={item['operation']}, entry_id={item['entry_id']}, synced={item['synced']}")
        else:
            logger.info("📋 队列为空")
        
        # 检查未同步的队列项
        unsynced_items = await imdb_client.execute_query(
            "SELECT * FROM entries_push_queue WHERE synced = false ORDER BY queue_id DESC"
        )
        
        logger.info(f"❌ 未同步队列项: {len(unsynced_items)}")
        
        if unsynced_items:
            logger.info("❌ 未同步队列项详情:")
            for item in unsynced_items:
                logger.info(f"  queue_id={item['queue_id']}, operation={item['operation']}, entry_id={item['entry_id']}")
        
        # 2. 检查目标记录
        target_entry_id = 10612
        target_external_id = 602610
        
        logger.info(f"🔍 检查目标记录: entry_id={target_entry_id}, external_id={target_external_id}")
        
        entry_info = await imdb_client.execute_query(
            "SELECT id, employee_id, entry_date, duration, external_id, source FROM entries WHERE id = $1",
            target_entry_id
        )
        
        if entry_info:
            entry = entry_info[0]
            logger.info(f"📊 PostgreSQL记录状态:")
            logger.info(f"   ID: {entry['id']}")
            logger.info(f"   员工ID: {entry['employee_id']}")
            logger.info(f"   日期: {entry['entry_date']}")
            logger.info(f"   duration: {entry['duration']}")
            logger.info(f"   external_id: {entry['external_id']}")
            logger.info(f"   source: {entry['source']}")
        else:
            logger.error(f"❌ 未找到PostgreSQL记录: entry_id={target_entry_id}")
        
        # 3. 检查MDB连接和记录
        logger.info("🔍 检查MDB连接和记录...")
        
        # 测试MDB连接
        test_result = await server6_client.test_mdb_connection()
        logger.info(f"🔗 MDB连接测试: {test_result}")
        
        # 尝试直接获取MDB记录
        logger.info(f"🔍 尝试获取MDB记录: external_id={target_external_id}")
        
        try:
            # 使用query_entries查询特定记录
            from datetime import date
            start_date = date(2025, 6, 26)
            end_date = date(2025, 6, 26)
            
            entries = await server6_client.query_entries(
                employee_id="215829",
                start_date=start_date,
                end_date=end_date
            )
            
            logger.info(f"📊 MDB查询结果: 找到 {len(entries)} 条记录")
            
            # 查找目标记录
            target_entry = None
            for entry in entries:
                if entry.get('external_id') == target_external_id:
                    target_entry = entry
                    break
            
            if target_entry:
                logger.info(f"✅ 找到MDB目标记录:")
                logger.info(f"   external_id: {target_entry.get('external_id')}")
                logger.info(f"   従業員ｺｰﾄﾞ: {target_entry.get('従業員ｺｰﾄﾞ')}")
                logger.info(f"   日付: {target_entry.get('日付')}")
                logger.info(f"   時間: {target_entry.get('時間')}")
                logger.info(f"   工事番号: {target_entry.get('工事番号')}")
            else:
                logger.error(f"❌ 未找到MDB目标记录: external_id={target_external_id}")
                
                # 显示所有记录的external_id
                logger.info("📋 所有MDB记录的external_id:")
                for entry in entries:
                    logger.info(f"   external_id: {entry.get('external_id')}")
                    
        except Exception as e:
            logger.error(f"❌ MDB查询异常: {e}")
        
        # 4. 尝试直接UPDATE MDB记录
        logger.info("🧪 尝试直接UPDATE MDB记录...")
        
        try:
            mdb_data = {
                '従業員ｺｰﾄﾞ': '215829',
                '日付': '2025-06-26',
                '機種': '',
                '号機': '',
                '工場製番': '',
                '工事番号': '24585',
                'ﾕﾆｯﾄ番号': '',
                '区分': 3,
                '項目': 7,
                '時間': 9.0,  # 测试值
                '所属ｺｰﾄﾞ': '131'
            }
            
            response = await server6_client.update_entry(target_external_id, mdb_data)
            logger.info(f"✅ MDB UPDATE成功: {response}")
            
        except Exception as e:
            logger.error(f"❌ MDB UPDATE失败: {e}")
        
    except Exception as e:
        logger.error(f"❌ 检查过程中发生错误: {e}", exc_info=True)
    finally:
        await imdb_client.disconnect()

async def main():
    """主函数"""
    await check_queue_and_mdb()

if __name__ == "__main__":
    asyncio.run(main()) 