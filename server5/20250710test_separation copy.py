#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Server5 分离测试脚本
测试HTTP API和微服务的完全分离
"""

import os
import sys
import time
import requests
import subprocess
import signal
from pathlib import Path

# 添加项目路径
sys.path.insert(0, str(Path(__file__).parent))

def test_http_only_mode():
    """测试HTTP-only模式"""
    print("🧪 测试 1: HTTP-only 模式")
    print("=" * 50)
    
    # 设置环境变量
    env = os.environ.copy()
    env['HTTP_ONLY_MODE'] = 'true'
    
    # 启动HTTP服务器
    print("🚀 启动HTTP-only服务器...")
    
    try:
        # 直接测试 main.py 在 HTTP-only 模式下的行为
        from app.main import HTTP_ONLY_MODE, service_manager
        
        print(f"✅ HTTP_ONLY_MODE = {HTTP_ONLY_MODE}")
        
        # 测试服务管理器
        print(f"✅ ServiceManager.http_only_mode = {service_manager.http_only_mode}")
        
        # 检查是否不会导入微服务
        if HTTP_ONLY_MODE:
            print("✅ HTTP-only模式已启用")
            print("✅ 微服务模块不会被导入")
        else:
            print("❌ HTTP-only模式未启用")
            
        print("🎉 HTTP-only模式测试通过")
        
    except Exception as e:
        print(f"❌ HTTP-only模式测试失败: {e}")
        return False
    
    return True

def test_microservices_only_mode():
    """测试纯微服务模式"""
    print("\n🧪 测试 2: 纯微服务模式")
    print("=" * 50)
    
    # 设置环境变量
    env = os.environ.copy()
    env['HTTP_ONLY_MODE'] = 'false'
    
    try:
        # 重置环境变量
        os.environ['HTTP_ONLY_MODE'] = 'false'
        
        # 重新导入来测试
        import importlib
        import app.main
        importlib.reload(app.main)
        
        print(f"✅ HTTP_ONLY_MODE = {app.main.HTTP_ONLY_MODE}")
        
        if not app.main.HTTP_ONLY_MODE:
            print("✅ 微服务模式已启用")
            print("✅ 微服务模块会被导入")
        else:
            print("❌ 微服务模式未启用")
            
        print("🎉 微服务模式测试通过")
        
    except Exception as e:
        print(f"❌ 微服务模式测试失败: {e}")
        return False
    
    return True

def test_service_imports():
    """测试服务导入分离"""
    print("\n🧪 测试 3: 服务导入分离")
    print("=" * 50)
    
    # 测试 HTTP-only 模式下的导入
    os.environ['HTTP_ONLY_MODE'] = 'true'
    
    try:
        # 重新导入测试
        import importlib
        import app.main
        importlib.reload(app.main)
        
        # 检查是否有微服务相关的属性
        if hasattr(app.main.service_manager, 'f1_listener'):
            print("❌ HTTP-only模式下仍然有微服务属性")
            return False
        else:
            print("✅ HTTP-only模式下没有微服务属性")
            
        print("🎉 服务导入分离测试通过")
        
    except Exception as e:
        print(f"❌ 服务导入分离测试失败: {e}")
        return False
    
    return True

def test_status_api():
    """测试状态API在不同模式下的响应"""
    print("\n🧪 测试 4: 状态API响应")
    print("=" * 50)
    
    try:
        # 测试 HTTP-only 模式
        os.environ['HTTP_ONLY_MODE'] = 'true'
        
        import importlib
        import app.main
        importlib.reload(app.main)
        
        # 模拟状态获取
        import asyncio
        status = asyncio.run(app.main.service_manager.get_all_status())
        
        print(f"✅ 状态响应: {status}")
        
        if status.get('mode') == 'HTTP-only':
            print("✅ HTTP-only模式状态正确")
        else:
            print("❌ HTTP-only模式状态错误")
            return False
            
        # 检查服务状态
        if 'http_api' in status.get('services', {}):
            print("✅ HTTP API服务状态存在")
        else:
            print("❌ HTTP API服务状态不存在")
            return False
            
        print("🎉 状态API测试通过")
        
    except Exception as e:
        print(f"❌ 状态API测试失败: {e}")
        return False
    
    return True

def main():
    """主测试函数"""
    print("🧪 Server5 HTTP/微服务分离测试")
    print("=" * 60)
    
    # 备份原始环境变量
    original_mode = os.environ.get('HTTP_ONLY_MODE')
    
    try:
        # 运行所有测试
        tests = [
            test_http_only_mode,
            test_microservices_only_mode,
            test_service_imports,
            test_status_api
        ]
        
        passed = 0
        total = len(tests)
        
        for test in tests:
            if test():
                passed += 1
        
        print(f"\n📊 测试结果: {passed}/{total} 通过")
        
        if passed == total:
            print("🎉 所有测试通过！HTTP/微服务分离成功！")
            
            print("\n📋 使用说明:")
            print("1. 只启动HTTP API:")
            print("   cd server5 && python start_server5_http_server.py")
            print("   - 访问: http://localhost:8009/docs")
            print("   - 健康检查: http://localhost:8009/health")
            
            print("\n2. 只启动微服务:")
            print("   cd server5 && python start_server5_notwith_api.py")
            print("   - 后台运行 f1-f4 微服务")
            
            print("\n3. 同时启动（推荐）:")
            print("   终端1: cd server5 && python start_server5_http_server.py")
            print("   终端2: cd server5 && python start_server5_notwith_api.py")
            
            return True
        else:
            print("❌ 有测试失败，请检查代码")
            return False
            
    except Exception as e:
        print(f"❌ 测试执行失败: {e}")
        return False
    
    finally:
        # 恢复原始环境变量
        if original_mode is not None:
            os.environ['HTTP_ONLY_MODE'] = original_mode
        elif 'HTTP_ONLY_MODE' in os.environ:
            del os.environ['HTTP_ONLY_MODE']

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1) 