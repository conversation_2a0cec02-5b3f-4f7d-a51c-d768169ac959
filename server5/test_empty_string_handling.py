#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试空字符串处理逻辑
验证f2_push_writer.py是否正确处理空字符串转换为NULL
"""

import asyncio
import sys
from pathlib import Path
import logging
import json
from datetime import datetime

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

def test_empty_string_conversion():
    """测试空字符串转换逻辑"""
    
    # 模拟从PostgreSQL读取的数据（包含空字符串）
    test_entry_data = {
        'employee_id': '215829',
        'entry_date': datetime.now().date(),
        'model': '',  # 空字符串
        'number': '   ',  # 只有空格的字符串
        'factory_number': None,  # NULL值
        'project_number': 'TEST',  # 正常值
        'unit_number': '',  # 空字符串
        'category': 1,  # 整数
        'item': None,  # NULL值
        'duration': 0.5,
        'department': ''  # 空字符串
    }
    
    print("🔍 测试数据:")
    for key, value in test_entry_data.items():
        print(f"  {key}: {repr(value)} (类型: {type(value).__name__})")
    
    print("\n🔄 应用f2_push_writer.py的转换逻辑:")
    
    # 应用f2_push_writer.py中的转换逻辑
    mdb_data = {
        'employee_id': test_entry_data['employee_id'],
        'entry_date': test_entry_data['entry_date'].strftime('%Y/%m/%d') if test_entry_data['entry_date'] else datetime.now().strftime('%Y/%m/%d'),
        'model': test_entry_data['model'] if test_entry_data['model'] and test_entry_data['model'].strip() else None,
        'number': test_entry_data['number'] if test_entry_data['number'] and test_entry_data['number'].strip() else None,
        'factory_number': test_entry_data['factory_number'] if test_entry_data['factory_number'] and test_entry_data['factory_number'].strip() else None,
        'project_number': test_entry_data['project_number'] if test_entry_data['project_number'] and test_entry_data['project_number'].strip() else None,
        'unit_number': test_entry_data['unit_number'] if test_entry_data['unit_number'] and test_entry_data['unit_number'].strip() else None,
        'category': str(test_entry_data['category']) if test_entry_data['category'] is not None else None,
        'item': str(test_entry_data['item']) if test_entry_data['item'] is not None else None,
        'duration': float(test_entry_data['duration']) if test_entry_data['duration'] is not None else 0.0,
        'department': test_entry_data['department'] if test_entry_data['department'] and test_entry_data['department'].strip() else None
    }
    
    print("📦 转换后的MDB数据:")
    for key, value in mdb_data.items():
        print(f"  {key}: {repr(value)} (类型: {type(value).__name__})")
    
    # 验证空字符串是否被正确转换为None
    empty_fields = []
    for key, value in mdb_data.items():
        if value == '' or (isinstance(value, str) and value.strip() == ''):
            empty_fields.append(key)
    
    if empty_fields:
        print(f"\n❌ 发现问题: 以下字段仍然是空字符串: {empty_fields}")
        return False
    else:
        print("\n✅ 所有空字符串都被正确转换为None")
        return True

def test_server6_null_string_conversion():
    """测试Server6客户端如何发送NULL字符串"""
    
    # 模拟f2发送给Server6的数据
    mdb_data = {
        'employee_id': '215829',
        'entry_date': '2025/07/15',
        'model': None,  # 这会被转换为"NULL"字符串
        'number': None,
        'factory_number': None,
        'project_number': 'TEST',
        'unit_number': None,
        'category': '1',
        'item': None,
        'duration': 0.5,
        'department': None
    }
    
    print("\n🔄 模拟Server6客户端发送数据:")
    print("📦 发送给Server6的数据:")
    for key, value in mdb_data.items():
        print(f"  {key}: {repr(value)}")
    
    # 模拟Server6客户端的NULL字符串转换
    server6_data = {}
    for key, value in mdb_data.items():
        if value is None:
            server6_data[key] = "NULL"  # 显式发送"NULL"字符串
        else:
            server6_data[key] = value
    
    print("\n📤 Server6客户端转换后的数据:")
    for key, value in server6_data.items():
        print(f"  {key}: {repr(value)}")
    
    return server6_data

if __name__ == "__main__":
    print("🧪 开始测试空字符串处理逻辑")
    print("=" * 50)
    
    # 测试1: 空字符串转换
    test1_result = test_empty_string_conversion()
    
    # 测试2: Server6 NULL字符串转换
    server6_data = test_server6_null_string_conversion()
    
    print("\n" + "=" * 50)
    print("📋 测试总结:")
    print(f"  空字符串转换测试: {'✅ 通过' if test1_result else '❌ 失败'}")
    print(f"  Server6 NULL字符串转换: ✅ 通过")
    
    if test1_result:
        print("\n✅ 结论: f2_push_writer.py的空字符串处理逻辑是正确的")
        print("   空字符串会被正确转换为None，然后Server6客户端会发送'NULL'字符串给MDB")
    else:
        print("\n❌ 结论: 需要修复f2_push_writer.py中的空字符串处理逻辑") 