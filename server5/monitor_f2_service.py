#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
监控f2服务执行情况
"""

import asyncio
import sys
from pathlib import Path
import logging
import time
from datetime import datetime

# 添加项目根目录到Python路径
sys.path.append(str(Path(__file__).parent.parent))

from app.database import IMDBClient
from app.utils.server6_client import Server6Client

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

class F2ServiceMonitor:
    """f2服务监控类"""
    
    def __init__(self):
        self.imdb_client = IMDBClient()
        self.server6_client = Server6Client()
        self.last_queue_count = 0
        
    async def setup(self):
        """初始化连接"""
        try:
            await self.imdb_client.connect()
            await self.server6_client.connect()
            logger.info("✅ 数据库连接成功")
            return True
        except Exception as e:
            logger.error(f"❌ 数据库连接失败: {e}")
            return False
    
    async def cleanup(self):
        """清理连接"""
        await self.imdb_client.disconnect()
        await self.server6_client.disconnect()
    
    async def check_queue_status(self):
        """检查队列状态"""
        try:
            # 检查未同步的队列项
            unsynced_items = await self.imdb_client.execute_query("""
                SELECT queue_id, entry_id, external_id, operation, created_ts, retry_count, last_error
                FROM entries_push_queue
                WHERE synced = FALSE
                ORDER BY created_ts ASC
            """)
            
            current_count = len(unsynced_items)
            
            if current_count != self.last_queue_count:
                logger.info(f"📊 队列状态变化: {self.last_queue_count} -> {current_count}")
                self.last_queue_count = current_count
            
            if unsynced_items:
                logger.info(f"📋 未同步队列项: {len(unsynced_items)} 个")
                for item in unsynced_items:
                    logger.info(f"  - queue_id={item['queue_id']}, entry_id={item['entry_id']}, "
                               f"external_id={item['external_id']}, operation={item['operation']}, "
                               f"retry_count={item['retry_count']}")
                    if item['last_error']:
                        logger.warning(f"    ❌ 错误: {item['last_error']}")
            
            return unsynced_items
            
        except Exception as e:
            logger.error(f"❌ 检查队列状态失败: {e}")
            return []
    
    async def check_recent_operations(self):
        """检查最近的操作"""
        try:
            # 检查最近10个队列项
            recent_items = await self.imdb_client.execute_query("""
                SELECT queue_id, entry_id, external_id, operation, synced, created_ts, updated_at
                FROM entries_push_queue
                ORDER BY created_ts DESC
                LIMIT 10
            """)
            
            logger.info("📋 最近10个队列项:")
            for item in recent_items:
                status = "✅" if item['synced'] else "⏳"
                logger.info(f"  {status} queue_id={item['queue_id']}, entry_id={item['entry_id']}, "
                           f"external_id={item['external_id']}, operation={item['operation']}, "
                           f"synced={item['synced']}")
            
            return recent_items
            
        except Exception as e:
            logger.error(f"❌ 检查最近操作失败: {e}")
            return []
    
    async def test_server6_connection(self):
        """测试Server6连接"""
        try:
            # 测试Server6连接
            response = await self.server6_client.get_status()
            if response.get('success'):
                logger.info("✅ Server6连接正常")
                return True
            else:
                logger.warning(f"⚠️ Server6连接异常: {response}")
                return False
        except Exception as e:
            logger.error(f"❌ Server6连接失败: {e}")
            return False
    
    async def test_delete_operation(self, external_id):
        """测试删除操作"""
        try:
            logger.info(f"🧪 测试删除操作: external_id={external_id}")
            
            # 检查Server6中是否存在该记录
            try:
                response = await self.server6_client.get_entry(external_id)
                if response.get('success'):
                    logger.info(f"✅ Server6中存在记录: external_id={external_id}")
                    
                    # 尝试删除
                    delete_response = await self.server6_client.delete_entry(external_id)
                    if delete_response.get('success'):
                        logger.info(f"✅ Server6删除成功: external_id={external_id}")
                        return True
                    else:
                        logger.error(f"❌ Server6删除失败: {delete_response}")
                        return False
                else:
                    logger.warning(f"⚠️ Server6中不存在记录: external_id={external_id}")
                    return True  # 记录不存在也算成功
            except Exception as e:
                logger.error(f"❌ Server6操作失败: {e}")
                return False
                
        except Exception as e:
            logger.error(f"❌ 测试删除操作失败: {e}")
            return False
    
    async def monitor_loop(self):
        """监控循环"""
        logger.info("🔍 开始监控f2服务...")
        
        while True:
            try:
                print(f"\n{'='*60}")
                print(f"📊 f2服务监控 - {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
                print(f"{'='*60}")
                
                # 检查队列状态
                await self.check_queue_status()
                
                # 检查最近操作
                await self.check_recent_operations()
                
                # 测试Server6连接
                await self.test_server6_connection()
                
                # 等待下次检查
                print(f"\n⏳ 等待30秒后再次检查...")
                await asyncio.sleep(30)
                
            except KeyboardInterrupt:
                logger.info("👋 监控停止")
                break
            except Exception as e:
                logger.error(f"❌ 监控循环异常: {e}")
                await asyncio.sleep(30)
    
    async def run_test(self):
        """运行测试"""
        try:
            if not await self.setup():
                return
            
            # 测试删除一个存在的记录
            test_external_id = 603659
            await self.test_delete_operation(test_external_id)
            
        except Exception as e:
            logger.error(f"❌ 测试失败: {e}")
        finally:
            await self.cleanup()

async def main():
    """主函数"""
    monitor = F2ServiceMonitor()
    
    if len(sys.argv) > 1 and sys.argv[1] == "test":
        await monitor.run_test()
    else:
        await monitor.setup()
        await monitor.monitor_loop()
        await monitor.cleanup()

if __name__ == "__main__":
    asyncio.run(main()) 