#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
检查MDB中external_id为602610的记录
"""

import asyncio
import sys
from pathlib import Path
import logging
from datetime import date

# 添加项目路径
sys.path.append(str(Path(__file__).parent))

from app.utils.server6_client import Server6Client

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

async def check_mdb_record():
    """检查MDB中的记录"""
    server6_client = Server6Client()
    
    try:
        # 检查external_id为602610的记录
        external_id = 602610
        employee_id = "215829"  # 员工ID
        
        logger.info(f"🔍 检查MDB中external_id为{external_id}的记录...")
        
        # 查询员工215829在2025-06-26的记录
        start_date = date(2025, 6, 26)
        end_date = date(2025, 6, 26)
        
        logger.info(f"📋 查询员工{employee_id}在{start_date}的记录...")
        entries = await server6_client.query_entries(employee_id, start_date, end_date)
        
        if entries:
            logger.info(f"✅ 找到 {len(entries)} 条记录")
            
            # 查找ID为602610的记录
            found_602610 = False
            for entry in entries:
                if str(entry.get('ID', '')).strip() == str(external_id):
                    found_602610 = True
                    logger.info(f"✅ 找到external_id={external_id}的记录:")
                    for key, value in entry.items():
                        logger.info(f"   {key}: {value}")
                    break
            
            if not found_602610:
                logger.warning(f"⚠️ 未找到external_id={external_id}的记录")
                logger.info("📋 找到的记录:")
                for i, entry in enumerate(entries):
                    logger.info(f"  记录{i+1}: ID={entry.get('ID')}, 従業員ｺｰﾄﾞ={entry.get('従業員ｺｰﾄﾞ')}, 時間={entry.get('時間')}")
        else:
            logger.warning(f"⚠️ 未找到员工{employee_id}在{start_date}的记录")
        
        # 测试MDB连接
        logger.info("🔍 测试MDB连接...")
        test_result = await server6_client.test_mdb_connection()
        logger.info(f"MDB连接测试结果: {test_result}")
        
    except Exception as e:
        logger.error(f"❌ 检查失败: {e}", exc_info=True)

async def main():
    """主函数"""
    await check_mdb_record()

if __name__ == "__main__":
    asyncio.run(main()) 