#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试UPDATE操作修复
验证UPDATE队列项能正确处理
"""

import asyncio
import sys
from pathlib import Path
import logging

# 添加项目路径
sys.path.append(str(Path(__file__).parent))

from app.database import IMDBClient, RedisClient, MongoDBClient

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

async def test_update_fix():
    """测试UPDATE操作修复"""
    imdb_client = IMDBClient()
    
    try:
        await imdb_client.connect()
        
        # 目标记录
        target_entry_id = 10612
        
        # 检查更新前的状态
        logger.info("🔍 检查更新前的状态...")
        before_info = await imdb_client.execute_query(
            "SELECT id, duration, external_id, source FROM entries WHERE id = $1",
            target_entry_id
        )
        
        if before_info:
            before_entry = before_info[0]
            logger.info(f"更新前: duration={before_entry['duration']}, source={before_entry['source']}")
        
        # 清理之前的队列项
        logger.info("🧹 清理之前的队列项...")
        await imdb_client.execute_command(
            "DELETE FROM entries_push_queue WHERE entry_id = $1",
            target_entry_id
        )
        
        # 检查清理后的队列状态
        queue_before = await imdb_client.execute_query(
            "SELECT COUNT(*) as count FROM entries_push_queue WHERE entry_id = $1",
            target_entry_id
        )
        logger.info(f"清理后队列项数量: {queue_before[0]['count']}")
        
        # 执行用户更新操作
        logger.info("📝 执行用户更新操作 - 修改duration为6.00...")
        async with imdb_client.pool.acquire() as conn:
            async with conn.transaction():
                await conn.execute(
                    "UPDATE entries SET duration = $1, source = 'user' WHERE id = $2",
                    6.00, target_entry_id
                )
        
        logger.info("✅ 用户更新操作完成")
        
        # 检查更新后的状态
        logger.info("🔍 检查更新后的状态...")
        after_info = await imdb_client.execute_query(
            "SELECT id, duration, external_id, source FROM entries WHERE id = $1",
            target_entry_id
        )
        
        if after_info:
            after_entry = after_info[0]
            logger.info(f"📊 更新后记录状态:")
            logger.info(f"   ID: {after_entry['id']}")
            logger.info(f"   duration: {after_entry['duration']}")
            logger.info(f"   external_id: {after_entry['external_id']}")
            logger.info(f"   source: {after_entry['source']}")
            
            if after_entry['duration'] == 6.00:
                logger.info("✅ 数据库更新成功！duration已修改为6.00")
            else:
                logger.error(f"❌ 数据库更新失败！duration仍然是{after_entry['duration']}")
        
        # 检查队列状态 - 修复字段名
        logger.info("🔍 检查队列状态...")
        queue_after = await imdb_client.execute_query(
            "SELECT COUNT(*) as count FROM entries_push_queue WHERE entry_id = $1",
            target_entry_id
        )
        logger.info(f"更新后队列项数量: {queue_after[0]['count']}")
        
        # 获取详细的队列项信息
        queue_items = await imdb_client.execute_query(
            "SELECT queue_id, entry_id, operation, synced, created_ts FROM entries_push_queue WHERE entry_id = $1 ORDER BY created_ts DESC LIMIT 5",
            target_entry_id
        )
        
        logger.info(f"📋 队列项详情: {len(queue_items)} 个")
        
        if queue_items:
            for item in queue_items:
                logger.info(f"队列项: queue_id={item['queue_id']}, operation={item['operation']}, synced={item['synced']}, created_ts={item['created_ts']}")
        else:
            logger.warning("⚠️ 没有找到队列项！UPDATE操作可能没有触发队列生成")
        
        # 检查触发器是否正常工作
        logger.info("🔍 检查触发器状态...")
        trigger_info = await imdb_client.execute_query(
            "SELECT trigger_name, event_manipulation, event_object_table FROM information_schema.triggers WHERE event_object_table = 'entries'"
        )
        
        logger.info(f"entries表触发器数量: {len(trigger_info)}")
        for trigger in trigger_info:
            logger.info(f"触发器: {trigger['trigger_name']} - {trigger['event_manipulation']} on {trigger['event_object_table']}")
        
    except Exception as e:
        logger.error(f"❌ 测试失败: {e}", exc_info=True)
    finally:
        await imdb_client.disconnect()

async def main():
    """主函数"""
    await test_update_fix()

if __name__ == "__main__":
    asyncio.run(main()) 