-- server5/optimize_database.sql
-- 数据库性能优化脚本
-- 为entries_api.py添加必要的索引

-- ===== ENTRIES表索引优化 =====

-- 1. 核心查询索引（employee_id + entry_date）
CREATE INDEX IF NOT EXISTS idx_entries_employee_date 
ON entries (employee_id, entry_date DESC);

-- 2. 按月查询索引（用于chart-data和months API）
CREATE INDEX IF NOT EXISTS idx_entries_employee_month 
ON entries (employee_id, DATE_TRUNC('month', entry_date));

-- 3. 主键优化（如果还没有）
CREATE INDEX IF NOT EXISTS idx_entries_id 
ON entries (id);

-- 4. 时间戳索引（用于排序）
CREATE INDEX IF NOT EXISTS idx_entries_ts 
ON entries (ts DESC);

-- ===== TIMEPROTAB表索引优化 =====

-- 1. 核心查询索引（employee_id + 日付）
CREATE INDEX IF NOT EXISTS idx_timeprotab_employee_date 
ON timeprotab (employee_id, 日付 DESC);

-- 2. 按月查询索引（用于chart-data）
CREATE INDEX IF NOT EXISTS idx_timeprotab_employee_month 
ON timeprotab (employee_id, DATE_TRUNC('month', 日付));

-- ===== 分析表统计信息 =====

-- 更新表统计信息以优化查询计划
ANALYZE entries;
ANALYZE timeprotab;

-- ===== 检查索引使用情况 =====

-- 查看索引大小和使用情况
SELECT 
    schemaname,
    tablename,
    indexname,
    pg_size_pretty(pg_relation_size(indexrelid)) AS index_size,
    idx_tup_read,
    idx_tup_fetch
FROM pg_stat_user_indexes 
WHERE tablename IN ('entries', 'timeprotab')
ORDER BY tablename, indexname;

-- ===== PostgreSQL配置优化建议 =====

-- 检查当前连接设置
SHOW max_connections;
SHOW shared_buffers;
SHOW work_mem;
SHOW maintenance_work_mem;

-- 建议配置（根据服务器内存调整）:
-- max_connections = 200
-- shared_buffers = 256MB (或可用RAM的25%)
-- work_mem = 4MB
-- maintenance_work_mem = 64MB
-- effective_cache_size = 1GB (或可用RAM的75%)

-- ===== 查询性能分析 =====

-- 分析慢查询日志
-- 在postgresql.conf中启用：
-- log_statement = 'all'
-- log_min_duration_statement = 1000  -- 记录超过1秒的查询

-- 查看最耗时的查询
SELECT 
    query,
    calls,
    total_time,
    mean_time,
    rows
FROM pg_stat_statements 
WHERE query LIKE '%entries%' OR query LIKE '%timeprotab%'
ORDER BY total_time DESC
LIMIT 10; 