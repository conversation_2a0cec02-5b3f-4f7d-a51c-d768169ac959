#!/usr/bin/env python3
"""
测试f5员工删除同步功能
调用f5的函数，从MDB中读取员工ID是215829的数据，
在指定时间段内找出并删除在MDB中已不存在的PostgreSQL记录。
"""

import asyncio
import logging
from datetime import date
import sys
from pathlib import Path

# 添加项目路径
sys.path.append(str(Path(__file__).parent))

from app.services.f5_bulk_sync import DeletionSyncService

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

class F5EmployeeDeletionSyncTest:
    """f5员工删除同步测试类"""
    
    def __init__(self):
        self.f5_service = DeletionSyncService()
        self.test_employee_id = "215829"
        self.test_start_date = date(2025, 7, 1)
        self.test_end_date = date(2025, 7, 16)
    
    async def setup(self):
        """初始化服务"""
        logger.info("🚀 初始化f5删除同步服务...")
        
        # 启动f5服务
        success = await self.f5_service.start()
        if not success:
            logger.error("❌ f5服务启动失败")
            return False
        
        logger.info("✅ f5服务启动成功")
        return True
    
    async def cleanup(self):
        """清理资源"""
        logger.info("🔌 停止f5服务...")
        await self.f5_service.stop()
        logger.info("✅ f5服务已停止")
    
    async def test_employee_deletion_sync(self):
        """测试员工删除同步功能"""
        logger.info("=" * 80)
        logger.info("🧪 开始测试f5员工删除同步功能")
        logger.info("=" * 80)
        
        logger.info(f"📋 测试参数:")
        logger.info(f"   员工ID: {self.test_employee_id}")
        logger.info(f"   开始日期: {self.test_start_date}")
        logger.info(f"   结束日期: {self.test_end_date}")
        logger.info("")
        
        try:
            # 执行员工删除同步
            logger.info("🔄 执行员工删除同步...")
            await self.f5_service.run_deletion_sync_for_employee(
                employee_id=self.test_employee_id,
                start_date=self.test_start_date,
                end_date=self.test_end_date
            )
            
            logger.info("✅ 员工删除同步执行完成")
            
        except Exception as e:
            logger.error(f"❌ 员工删除同步执行失败: {e}", exc_info=True)
            return False
        
        return True
    
    async def verify_results(self):
        """验证结果"""
        logger.info("=" * 80)
        logger.info("🔍 验证删除同步结果")
        logger.info("=" * 80)
        
        try:
            # 检查PostgreSQL中的数据
            logger.info("📊 检查PostgreSQL中的数据...")
            
            # 获取指定员工在指定时间范围内的记录
            query = """
                SELECT id, external_id, entry_date, employee_id, source, ts
                FROM entries 
                WHERE employee_id = $1
                  AND entry_date BETWEEN $2 AND $3
                ORDER BY entry_date DESC, ts DESC
            """
            
            results = await self.f5_service.imdb_client.execute_query(
                query, self.test_employee_id, self.test_start_date, self.test_end_date
            )
            
            logger.info(f"📈 PostgreSQL中找到 {len(results)} 条记录")
            
            if results:
                logger.info("📋 记录详情:")
                for i, record in enumerate(results[:5], 1):  # 只显示前5条
                    logger.info(f"   {i}. ID: {record['id']}, external_id: {record['external_id']}, "
                               f"日期: {record['entry_date']}, source: {record['source']}")
                
                if len(results) > 5:
                    logger.info(f"   ... 还有 {len(results) - 5} 条记录")
            else:
                logger.info("   📭 没有找到记录")
            
            # 检查MDB中的数据
            logger.info("")
            logger.info("📊 检查MDB中的数据...")
            
            mdb_records = await self.f5_service.server6_client.query_entries(
                employee_id=self.test_employee_id,
                start_date=self.test_start_date,
                end_date=self.test_end_date
            )
            
            if mdb_records:
                logger.info(f"📈 MDB中找到 {len(mdb_records)} 条记录")
                logger.info("📋 记录详情:")
                for i, record in enumerate(mdb_records[:5], 1):  # 只显示前5条
                    # 使用正确的字段名映射
                    external_id = record.get('ID') or record.get('external_id')
                    employee_id = record.get('従業員ｺｰﾄﾞ') or record.get('employee_id')
                    entry_date = record.get('日付') or record.get('entry_date')
                    
                    logger.info(f"   {i}. ID: {external_id}, 员工ID: {employee_id}, "
                               f"日期: {entry_date}")
                
                if len(mdb_records) > 5:
                    logger.info(f"   ... 还有 {len(mdb_records) - 5} 条记录")
            else:
                logger.info("   📭 MDB中没有找到记录")
            
            # 分析结果
            logger.info("")
            logger.info("📊 结果分析:")
            pg_count = len(results)
            mdb_count = len(mdb_records) if mdb_records else 0
            
            logger.info(f"   PostgreSQL记录数: {pg_count}")
            logger.info(f"   MDB记录数: {mdb_count}")
            
            if pg_count == mdb_count:
                logger.info("   ✅ 数据一致，删除同步成功")
            elif pg_count > mdb_count:
                logger.warning(f"   ⚠️ PostgreSQL比MDB多 {pg_count - mdb_count} 条记录")
                logger.info("   💡 可能需要再次执行删除同步")
            else:
                logger.info("   ℹ️ MDB比PostgreSQL多记录，这是正常的")
            
        except Exception as e:
            logger.error(f"❌ 验证结果失败: {e}", exc_info=True)
    
    async def run_test(self):
        """运行完整测试"""
        try:
            # 初始化
            if not await self.setup():
                return False
            
            # 执行测试
            success = await self.test_employee_deletion_sync()
            if not success:
                return False
            
            # 验证结果
            await self.verify_results()
            
            logger.info("")
            logger.info("🎉 测试完成！")
            return True
            
        except Exception as e:
            logger.error(f"❌ 测试执行失败: {e}", exc_info=True)
            return False
        finally:
            # 清理资源
            await self.cleanup()

async def main():
    """主函数"""
    logger.info("🚀 开始f5员工删除同步测试")
    logger.info("=" * 80)
    
    # 创建测试实例
    test = F5EmployeeDeletionSyncTest()
    
    # 运行测试
    success = await test.run_test()
    
    if success:
        logger.info("✅ 所有测试通过")
    else:
        logger.error("❌ 测试失败")
    
    logger.info("=" * 80)
    logger.info("🏁 测试结束")

if __name__ == "__main__":
    asyncio.run(main()) 