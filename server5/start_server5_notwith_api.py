# №02025/07/10 + Server5 微服务启动脚本（纯微服务，不包含HTTP API）
# 完全分离HTTP服务与微服务 - 只启动f1-f4微服务

import os
import sys
import asyncio
import logging
from pathlib import Path
import signal
import atexit

# 设置环境变量 - 确保不启动HTTP API
os.environ['HTTP_ONLY_MODE'] = 'false'

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.StreamHandler(sys.stdout),
        logging.FileHandler('logs/server5_services_only.log', encoding='utf-8')
    ]
)

logger = logging.getLogger(__name__)

# 添加项目路径
sys.path.insert(0, str(Path(__file__).parent))

# 全局变量用于微服务管理
services_list = []

def cleanup_on_exit():
    """退出时的清理函数"""
    global services_list
    print("\n🧹 执行微服务清理操作...")
    
    # 停止微服务
    if services_list:
        print("🛑 停止微服务...")
        asyncio.run(stop_services(services_list))
    
    print("✅ 微服务清理完成")

# 注册退出清理函数
atexit.register(cleanup_on_exit)

# 导入配置选择器并加载配置
from app.config_selector import ConfigSelector

def load_configuration():
    """加载配置"""
    try:
        selector = ConfigSelector()
        config_dict, config_name = selector.load_config()
        config_module = selector.apply_config_to_module(config_dict)
        
        # 显示配置信息
        info = selector.get_current_config_info()
        print(f"📋 配置信息:")
        print(f"  - 配置文件: {config_name}")
        print(f"  - 平台: {info['platform']}")
        print(f"  - 主机: {info['hostname']}")
        print(f"  - IP: {info['local_ip']}")
        print(f"  - 自动检测: {info['auto_detected']}")
        
        return config_module
    except Exception as e:
        print(f"❌ 配置加载失败: {e}")
        raise

# 加载配置
config_module = load_configuration()

async def run_services():
    """异步启动 f1、f2、f3、f4 微服务"""
    global services_list
    print("🚀 启动Server5微服务...")

    from app.services.f1_listener import ListenerService
    from app.services.f2_push_writer import PushWriterServiceFixed
    from app.services.f3_data_puller import DataPullerService
    from app.services.f4_operation_handler import OperationHandlerService

    # 实例化服务
    f1 = ListenerService()
    f2 = PushWriterServiceFixed()
    f3 = DataPullerService()
    f4 = OperationHandlerService()
    
    # 保存服务列表用于清理
    services_list = [f1, f2, f3, f4]

    # 启动所有服务
    results = await asyncio.gather(
        f1.start(),
        f2.start(),
        f3.start(),
        f4.start(),
        return_exceptions=True,
    )

    # 打印启动结果
    services = ["f1_listener", "f2_push_writer", "f3_data_puller", "f4_operation_handler"]
    for name, res in zip(services, results):
        status = "✅" if (isinstance(res, bool) and res) else "❌"
        print(f"{status} {name} {'启动成功' if status=='✅' else '启动失败'}")

    # 如果有任何服务启动失败，直接退出
    if not all(isinstance(r, bool) and r for r in results):
        print("❌ 有服务启动失败，请检查日志。即将退出...")
        await stop_services(services_list)
        return False

    print("🎉 所有微服务已启动")
    return True

async def stop_services(service_list):
    """停止所有服务"""
    if not service_list:
        return
    
    print("🛑 正在停止微服务...")
    stop_coros = []
    for svc in service_list:
        if hasattr(svc, "stop"):
            stop_coros.append(svc.stop())
    
    if stop_coros:
        await asyncio.gather(*stop_coros, return_exceptions=True)
    print("✅ 微服务已停止")

def signal_handler(signum, frame):
    """信号处理器"""
    print(f"\n📡 接收到信号 {signum}，正在关闭微服务...")
    cleanup_on_exit()
    sys.exit(0)

def main():
    """主函数 - 只启动微服务（不包含HTTP API）"""
    print("🔧 MySuite Server5 - 纯微服务引擎")
    print("=" * 60)
    print("🔧 模式: 微服务模式 (不启动 HTTP API)")
    print("🛠️  功能: 仅启动 f1-f4 后台微服务")
    print("🌐 HTTP API: 需要单独启动 start_server5_http_server.py")
    print("📝 包含服务: f1_listener, f2_push_writer, f3_data_puller, f4_operation_handler")
    print("=" * 60)
    
    # 注册信号处理器
    signal.signal(signal.SIGINT, signal_handler)
    signal.signal(signal.SIGTERM, signal_handler)

    try:
        print("🚀 启动Server5微服务引擎...")
        print("👋 按 Ctrl+C 退出")
        
        # 启动微服务
        success = asyncio.run(run_services())
        if not success:
            print("❌ 微服务启动失败，退出")
            return

        print("🎉 Server5微服务引擎启动成功！")
        print("🔧 微服务状态: 运行中")
        print("💻 正在处理后台任务...")
        print("⚠️  提示: 如需HTTP API，请另开终端运行: python start_server5_http_server.py")

        # 保持运行
        import time
        while True:
            time.sleep(1)

    except KeyboardInterrupt:
        print("\n👋 接收到中断信号，正在关闭微服务...")
    except Exception as e:
        print(f"❌ 微服务启动失败: {e}")
        logging.error(f"Server5微服务启动失败: {e}")
        sys.exit(1)

if __name__ == "__main__":
    main() 