#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试Server5更新操作完整流程
验证客户端 -> Server5 -> 触发器 -> f2 -> server6 的完整链路
"""

import asyncio
import sys
import requests
import time
from pathlib import Path
import logging

# 添加项目路径
sys.path.append(str(Path(__file__).parent))

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

class UpdateFlowTest:
    """测试更新操作完整流程"""
    
    def __init__(self):
        self.server5_base_url = "http://localhost:8009"
        self.test_employee_id = "215829"
        
    async def test_update_flow(self):
        """测试完整的更新流程"""
        print("🚀 开始测试Server5更新操作完整流程")
        print("="*80)
        
        try:
            # 1. 首先获取现有记录
            print("📋 1. 获取现有记录...")
            response = requests.get(
                f"{self.server5_base_url}/api/entries/",
                params={
                    "employee_id": self.test_employee_id,
                    "limit": 5
                },
                timeout=10
            )
            
            if response.status_code != 200:
                print(f"❌ 获取记录失败: {response.status_code} - {response.text}")
                return False
                
            entries = response.json()
            if not entries:
                print("❌ 没有找到测试记录")
                return False
                
            # 选择第一条记录进行更新
            test_entry = entries[0]
            entry_id = test_entry.get('id')
            external_id = test_entry.get('external_id')
            
            print(f"✅ 找到测试记录: internal_id={entry_id}, external_id={external_id}")
            
            # 2. 准备更新数据
            print("📝 2. 准备更新数据...")
            update_data = {
                "entry_date": test_entry.get('entry_date'),
                "employee_id": test_entry.get('employee_id'),
                "duration": 8.5,  # 修改工作时间
                "project_code": "TEST_PROJECT",  # 修改项目代码
                "status": "TEST_STATUS",  # 修改状态
                "description": "测试更新描述",  # 修改描述
                "department": test_entry.get('department'),
                "notes": f"号机:TEST001 工场製番:TEST_FACTORY 工事番号:TEST_PROJECT ユニット番号:TEST_UNIT",
                "source": "user"
            }
            
            print(f"📊 更新数据: {update_data}")
            
            # 3. 发送更新请求
            print("📤 3. 发送更新请求...")
            update_response = requests.put(
                f"{self.server5_base_url}/api/entries/{entry_id}",
                json=update_data,
                timeout=10
            )
            
            if update_response.status_code != 200:
                print(f"❌ 更新失败: {update_response.status_code} - {update_response.text}")
                return False
                
            update_result = update_response.json()
            print(f"✅ 更新成功: {update_result}")
            
            # 4. 等待触发器处理
            print("⏳ 4. 等待触发器处理...")
            await asyncio.sleep(2)
            
            # 5. 检查entries_push_queue
            print("🔍 5. 检查entries_push_queue...")
            try:
                # 这里需要直接查询数据库
                import asyncpg
                from app.config import IMDB_DATABASE_URL
                
                conn = await asyncpg.connect(IMDB_DATABASE_URL)
                try:
                    queue_items = await conn.fetch(
                        "SELECT * FROM entries_push_queue WHERE entry_id = $1 ORDER BY created_at DESC LIMIT 5",
                        entry_id
                    )
                    
                    if queue_items:
                        print(f"✅ 找到 {len(queue_items)} 条队列项:")
                        for item in queue_items:
                            print(f"   - queue_id: {item['queue_id']}, operation: {item['operation']}, synced: {item['synced']}")
                    else:
                        print("⚠️ 没有找到队列项")
                        
                finally:
                    await conn.close()
                    
            except Exception as e:
                print(f"⚠️ 检查队列失败: {e}")
            
            # 6. 验证更新结果
            print("🔍 6. 验证更新结果...")
            verify_response = requests.get(
                f"{self.server5_base_url}/api/entries/{entry_id}",
                timeout=10
            )
            
            if verify_response.status_code == 200:
                updated_entry = verify_response.json()
                print(f"✅ 验证成功: duration={updated_entry.get('duration')}, project_code={updated_entry.get('project_code')}")
            else:
                print(f"⚠️ 验证失败: {verify_response.status_code}")
            
            print("="*80)
            print("🎉 更新流程测试完成！")
            return True
            
        except Exception as e:
            print(f"❌ 测试失败: {e}")
            return False

async def main():
    """主函数"""
    test = UpdateFlowTest()
    success = await test.test_update_flow()
    
    if success:
        print("✅ 所有测试通过")
    else:
        print("❌ 测试失败")
    
    return success

if __name__ == "__main__":
    asyncio.run(main()) 