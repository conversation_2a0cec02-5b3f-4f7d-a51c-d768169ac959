#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
清洁环境下的完整插入流程测试
先插入数据到entries，然后启动f1和f2服务自动处理
"""

import asyncio
import sys
from pathlib import Path
from datetime import datetime, date

# 添加项目根目录到Python路径
sys.path.append(str(Path(__file__).parent))

from app.database import IMDBClient
from app.services.f1_listener import ListenerService
from app.services.f2_push_writer import PushWriterService

async def test_clean_insert_flow():
    """清洁环境下的完整插入流程测试"""
    
    print("🧪 开始清洁环境下的完整插入流程测试")
    print("=" * 60)
    
    # 初始化客户端和服务
    imdb_client = IMDBClient()
    f1_service = ListenerService()
    f2_service = PushWriterService()
    
    try:
        # 1. 连接数据库
        print("📡 步骤1: 连接数据库...")
        await imdb_client.connect()
        print("✅ PostgreSQL连接成功")
        
        # 2. 插入测试数据到entries表（external_id=NULL）
        print("\n📝 步骤2: 插入测试数据到entries表...")
        test_data = {
            'employee_id': '215829',
            'entry_date': date(2025, 6, 26),
            'model': '',
            'number': '',
            'factory_number': '',
            'project_number': '24585',
            'unit_number': '',
            'category': 3,
            'item': 7,
            'duration': 9.0,
            'department': '131'
        }
        
        # 插入数据（external_id默认为NULL）
        entry_id = await imdb_client.create_entry(test_data)
        print(f"✅ 插入成功，entry_id: {entry_id}")
        
        # 获取刚插入的记录来检查external_id
        entry_records = await imdb_client.get_user_entries('215829', 1)
        if entry_records:
            latest_entry = entry_records[0]
            print(f"   初始external_id: {latest_entry.get('external_id')} (应该为NULL)")
        
        # 3. 启动f1和f2服务
        print("\n🚀 步骤3: 启动f1和f2服务...")
        await f1_service.start()
        await f2_service.start()
        print("✅ f1和f2服务启动成功")
        
        # 4. 等待触发器自动入队
        print("\n⏳ 步骤4: 等待触发器自动入队...")
        await asyncio.sleep(3)  # 等待触发器执行
        
        # 5. 检查队列状态
        print("\n📋 步骤5: 检查队列状态...")
        queue_items = await imdb_client.get_queue_items(synced=False, limit=10)
        print(f"找到 {len(queue_items)} 个未同步队列项")
        
        # 查找我们刚插入的队列项
        our_queue_item = None
        for item in queue_items:
            if item['entry_id'] == entry_id:
                our_queue_item = item
                break
        
        if our_queue_item:
            print(f"✅ 找到我们的队列项:")
            print(f"   queue_id: {our_queue_item['queue_id']}")
            print(f"   entry_id: {our_queue_item['entry_id']}")
            print(f"   operation: {our_queue_item['operation']}")
            print(f"   created_ts: {our_queue_item['created_ts']}")
        else:
            print("❌ 未找到我们的队列项")
            return False
        
        # 6. 等待f2服务处理队列
        print("\n⏳ 步骤6: 等待f2服务处理队列...")
        print("   等待20秒让f2服务自动处理队列...")
        await asyncio.sleep(20)  # 等待f2服务处理
        
        # 7. 检查处理结果
        print("\n🔍 步骤7: 检查处理结果...")
        
        # 检查队列项是否已同步
        remaining_queue_items = await imdb_client.get_queue_items(synced=False, limit=10)
        our_queue_still_pending = any(item['entry_id'] == entry_id for item in remaining_queue_items)
        
        if not our_queue_still_pending:
            print("✅ 队列项已同步")
        else:
            print("❌ 队列项未同步")
            return False
        
        # 检查entries表中的external_id是否已更新
        entry_records = await imdb_client.get_user_entries('215829', 1)
        if entry_records:
            latest_entry = entry_records[0]
            if latest_entry.get('external_id'):
                print(f"✅ entries表external_id已更新: {latest_entry['external_id']}")
                print("🎉 完整插入流程测试成功！")
                print(f"📊 最终数据:")
                print(f"   entry_id: {latest_entry['id']}")
                print(f"   external_id: {latest_entry['external_id']}")
                print(f"   employee_id: {latest_entry['employee_id']}")
                print(f"   entry_date: {latest_entry['entry_date']}")
                print(f"   duration: {latest_entry['duration']}")
                return True
            else:
                print("❌ entries表external_id未更新")
                return False
        else:
            print("❌ 未找到entry记录")
            return False
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False
        
    finally:
        # 清理资源
        try:
            await f2_service.stop()
            await f1_service.stop()
            await imdb_client.disconnect()
        except:
            pass

if __name__ == "__main__":
    asyncio.run(test_clean_insert_flow()) 