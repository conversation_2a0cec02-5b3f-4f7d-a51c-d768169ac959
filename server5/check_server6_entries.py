# server5/check_server6_entries.py
# 安全检查Server6中的记录 - 只读操作
# 20250715 - 用于检查记录存在性和详细信息，不执行删除

import asyncio
import aiohttp
import json
import sys
from pathlib import Path
sys.path.append(str(Path(__file__).parent))

from config.config import SERVER6_CONFIG

async def check_entry_exists(session, external_id):
    """检查记录是否存在并显示详细信息"""
    try:
        url = f"{SERVER6_CONFIG['base_url']}/api/entries/{external_id}"
        async with session.get(url) as response:
            if response.status == 200:
                data = await response.json()
                print(f"✅ 找到记录 external_id={external_id}:")
                print(f"   - 员工ID: {data.get('employee_id')}")
                print(f"   - 日期: {data.get('entry_date')}")
                print(f"   - 模型: {data.get('model')}")
                print(f"   - 编号: {data.get('number')}")
                print(f"   - 工厂编号: {data.get('factory_number')}")
                print(f"   - 项目编号: {data.get('project_number')}")
                print(f"   - 单元编号: {data.get('unit_number')}")
                print(f"   - 类别: {data.get('category')}")
                print(f"   - 项目: {data.get('item')}")
                print(f"   - 部门: {data.get('department')}")
                print(f"   - 时长: {data.get('duration')}")
                return True, data
            elif response.status == 404:
                print(f"❌ 记录不存在: external_id={external_id}")
                return False, None
            else:
                print(f"⚠️ 查询失败: status={response.status}")
                return False, None
    except Exception as e:
        print(f"❌ 查询记录时出错: {e}")
        return False, None

async def get_server6_status(session):
    """获取Server6服务状态"""
    try:
        url = f"{SERVER6_CONFIG['base_url']}/health"
        async with session.get(url) as response:
            if response.status == 200:
                data = await response.json()
                print(f"✅ Server6服务正常: {data}")
                return True
            else:
                print(f"⚠️ Server6服务异常: status={response.status}")
                return False
    except Exception as e:
        print(f"❌ 检查Server6状态时出错: {e}")
        return False

async def main():
    """主函数 - 安全检查Server6记录"""
    print("🔍 Server6 记录安全检查 (只读)")
    print("=" * 60)
    
    # 测试多个external_id
    test_ids = [603668, 603641, 603640, 603639, 603638]
    
    async with aiohttp.ClientSession() as session:
        # 首先检查Server6服务状态
        print("📡 检查Server6服务状态...")
        server6_ok = await get_server6_status(session)
        
        if not server6_ok:
            print("❌ Server6服务不可用，停止检查")
            return
        
        print(f"\n📋 检查 {len(test_ids)} 个记录:")
        print("=" * 60)
        
        found_count = 0
        for i, external_id in enumerate(test_ids, 1):
            print(f"\n{i}. 检查记录: external_id={external_id}")
            print("-" * 40)
            
            # 检查记录是否存在
            exists, entry_data = await check_entry_exists(session, external_id)
            
            if exists:
                found_count += 1
                print(f"   📊 记录详情已显示")
            else:
                print(f"   ⚠️ 记录不存在，可能已被删除或从未同步")
        
        print(f"\n" + "=" * 60)
        print(f"📊 检查结果汇总:")
        print(f"   - 总检查数: {len(test_ids)}")
        print(f"   - 存在记录: {found_count}")
        print(f"   - 不存在记录: {len(test_ids) - found_count}")
        print(f"   - 存在率: {found_count/len(test_ids)*100:.1f}%")
        
        if found_count > 0:
            print(f"\n💡 建议:")
            print(f"   - 如果需要进行删除测试，请选择存在的记录")
            print(f"   - 使用 test_server6_delete_direct.py 进行安全的删除测试")
        else:
            print(f"\n💡 所有测试记录都不存在，可能原因:")
            print(f"   - 记录已被删除")
            print(f"   - 记录从未同步到Server6")
            print(f"   - external_id不正确")
    
    print("\n" + "=" * 60)
    print("🏁 安全检查完成")

if __name__ == "__main__":
    asyncio.run(main()) 