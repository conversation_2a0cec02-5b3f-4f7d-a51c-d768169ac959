#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Redis缓存清理脚本
清理与特定entry_id相关的缓存数据
"""

import asyncio
import sys
from pathlib import Path
import logging

# 添加项目路径
sys.path.append(str(Path(__file__).parent))

from app.database import RedisClient

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

async def cleanup_redis_cache(entry_id: int = None):
    """清理Redis缓存"""
    redis_client = RedisClient()
    
    try:
        await redis_client.connect()
        logger.info("✅ Redis连接成功")
        
        if entry_id:
            # 清理特定entry_id的缓存
            logger.info(f"🧹 清理entry_id={entry_id}相关的缓存...")
            
            # 清理可能的缓存键
            cache_keys = [
                f"entry:{entry_id}",
                f"entry_data:{entry_id}",
                f"entry_status:{entry_id}",
                f"entry_sync:{entry_id}"
            ]
            
            for key in cache_keys:
                if await redis_client.delete_cache(key):
                    logger.info(f"✅ 已删除缓存: {key}")
                else:
                    logger.info(f"ℹ️ 缓存不存在: {key}")
        else:
            # 清理所有entries相关缓存
            logger.info("🧹 清理所有entries相关缓存...")
            
            # 获取所有keys
            keys = await redis_client.client.keys("entry:*")
            if keys:
                for key in keys:
                    if await redis_client.delete_cache(key):
                        logger.info(f"✅ 已删除缓存: {key}")
                logger.info(f"✅ 共清理了 {len(keys)} 个缓存项")
            else:
                logger.info("ℹ️ 没有找到entries相关缓存")
            
            # 清理计数器（可选）
            logger.info("🧹 清理计数器...")
            counter_keys = [
                "server5:insert_count",
                "server5:update_count", 
                "server5:delete_count",
                "server5:update_skipped_count",
                "server5:delete_skipped_count"
            ]
            
            for key in counter_keys:
                await redis_client.client.delete(key)
                logger.info(f"✅ 已重置计数器: {key}")
        
        logger.info("🎉 Redis缓存清理完成")
        
    except Exception as e:
        logger.error(f"❌ Redis缓存清理失败: {e}")
    finally:
        await redis_client.disconnect()

if __name__ == "__main__":
    import argparse
    
    parser = argparse.ArgumentParser(description="清理Redis缓存")
    parser.add_argument("--entry-id", type=int, help="要清理的特定entry_id")
    parser.add_argument("--all", action="store_true", help="清理所有entries相关缓存")
    
    args = parser.parse_args()
    
    if args.entry_id:
        asyncio.run(cleanup_redis_cache(args.entry_id))
    elif args.all:
        asyncio.run(cleanup_redis_cache())
    else:
        print("请指定 --entry-id <id> 或 --all 参数")
        print("示例:")
        print("  python cleanup_redis_cache.py --entry-id 145172")
        print("  python cleanup_redis_cache.py --all") 