#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试完整的删除流程
"""

import asyncio
import sys
from pathlib import Path
import logging

# 添加项目根目录到Python路径
sys.path.append(str(Path(__file__).parent.parent))

from app.database import IMDBClient
from app.utils.server6_client import Server6Client

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

class DeleteFlowDebugTest:
    """删除流程调试测试类"""
    
    def __init__(self):
        self.imdb_client = IMDBClient()
        self.server6_client = Server6Client()
        
    async def setup(self):
        """初始化连接"""
        try:
            await self.imdb_client.connect()
            await self.server6_client.connect()
            logger.info("✅ 数据库连接成功")
            return True
        except Exception as e:
            logger.error(f"❌ 数据库连接失败: {e}")
            return False
    
    async def cleanup(self):
        """清理连接"""
        await self.imdb_client.disconnect()
        await self.server6_client.disconnect()
    
    async def check_queue_status(self):
        """检查队列状态"""
        logger.info("🔍 检查entries_push_queue状态")
        
        # 检查所有队列项
        queue_items = await self.imdb_client.execute_query("""
            SELECT queue_id, entry_id, external_id, operation, synced, created_ts, updated_at
            FROM entries_push_queue
            ORDER BY created_ts DESC
            LIMIT 10
        """)
        
        logger.info(f"📋 找到 {len(queue_items)} 个队列项")
        
        for item in queue_items:
            logger.info(f"  - queue_id={item['queue_id']}, entry_id={item['entry_id']}, "
                       f"external_id={item['external_id']}, operation={item['operation']}, "
                       f"synced={item['synced']}")
        
        # 检查未同步的队列项
        unsynced_items = await self.imdb_client.execute_query("""
            SELECT queue_id, entry_id, external_id, operation, created_ts
            FROM entries_push_queue
            WHERE synced = FALSE
            ORDER BY created_ts ASC
        """)
        
        logger.info(f"📋 未同步的队列项: {len(unsynced_items)} 个")
        
        for item in unsynced_items:
            logger.info(f"  - queue_id={item['queue_id']}, entry_id={item['entry_id']}, "
                       f"external_id={item['external_id']}, operation={item['operation']}")
        
        return unsynced_items
    
    async def test_delete_operation(self, external_id):
        """测试删除操作"""
        logger.info(f"🧪 测试删除操作: external_id={external_id}")
        
        # 1. 检查entries表中是否存在该记录
        entry_record = await self.imdb_client.fetch_one(
            "SELECT * FROM entries WHERE external_id = $1", external_id
        )
        
        if entry_record:
            logger.info(f"✅ 找到entries记录: id={entry_record['id']}, external_id={entry_record['external_id']}")
            
            # 2. 检查Server6中是否存在该记录
            try:
                response = await self.server6_client.get_entry(external_id)
                if response.get('success'):
                    logger.info(f"✅ Server6中存在记录: external_id={external_id}")
                else:
                    logger.warning(f"⚠️ Server6中不存在记录: external_id={external_id}")
            except Exception as e:
                logger.error(f"❌ 检查Server6记录失败: {e}")
            
            # 3. 模拟删除操作
            logger.info(f"🗑️ 模拟删除操作: external_id={external_id}")
            
            # 先标记为user操作
            await self.imdb_client.execute_command(
                "UPDATE entries SET source = 'user' WHERE external_id = $1", external_id
            )
            
            # 删除记录（触发器会处理）
            await self.imdb_client.execute_command(
                "DELETE FROM entries WHERE external_id = $1", external_id
            )
            
            logger.info(f"✅ 删除操作完成: external_id={external_id}")
            
            # 4. 等待一段时间让f2处理
            logger.info("⏳ 等待f2处理队列项...")
            await asyncio.sleep(5)
            
            # 5. 检查队列状态
            await self.check_queue_status()
            
        else:
            logger.warning(f"⚠️ entries表中不存在记录: external_id={external_id}")
    
    async def run_test(self):
        """运行测试"""
        try:
            if not await self.setup():
                return
            
            # 检查当前队列状态
            await self.check_queue_status()
            
            # 测试删除一个存在的记录
            test_external_id = 603659  # 使用您提到的external_id
            await self.test_delete_operation(test_external_id)
            
        except Exception as e:
            logger.error(f"❌ 测试失败: {e}")
        finally:
            await self.cleanup()

async def main():
    """主函数"""
    test = DeleteFlowDebugTest()
    await test.run_test()

if __name__ == "__main__":
    asyncio.run(main()) 