#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
专门测试Server6删除功能，external_id=603640
"""

import requests
import logging

logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

SERVER6_BASE_URL = "http://192.168.3.93:8019"  # 局域网Win10中的Server6
EXTERNAL_ID = 603640

def test_server6_delete_api(external_id: int):
    print(f"\n🗑️ 测试Server6删除API: external_id={external_id}")
    print("=" * 50)
    try:
        response = requests.delete(
            f"{SERVER6_BASE_URL}/mdb/entries/delete/{external_id}",
            timeout=10
        )
        if response.status_code == 200:
            data = response.json()
            print(f"✅ 删除API调用成功: {data}")
            return True
        else:
            print(f"❌ 删除API调用失败: {response.status_code}")
            print(f"错误信息: {response.text}")
            return False
    except Exception as e:
        print(f"❌ 删除API调用异常: {e}")
        return False

def main():
    print("🚀 开始Server6删除external_id=603640测试")
    test_server6_delete_api(EXTERNAL_ID)
    print("\n🏁 测试完成")

if __name__ == "__main__":
    main() 