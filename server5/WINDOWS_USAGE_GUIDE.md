# Server5 Windows使用指南

## 🎯 概览

Server5是一个MDB-PostgreSQL双向同步微服务，专为Windows 10环境设计。本指南将帮助您在Windows上正确部署和使用Server5。

## 📋 前提条件

### 1. 环境要求
- Windows 10 或更高版本
- Python 3.12
- Conda虚拟环境
- Access 2013或更高版本（用于MDB文件访问）

### 2. 依赖服务
- PostgreSQL 17.5 (运行在 ************:5432)
- MongoDB (运行在 ************:27017)
- Redis (运行在 ************:6379)
- MDB文件 (位于 D:\actest25\6.mdb)

## 🚀 快速开始

### 1. 环境设置
```bash
# 创建虚拟环境
conda create -n server5-sync python=3.12

# 激活虚拟环境
conda activate server5-sync

# 安装依赖
pip install -r requirements.txt
pip install pywin32
```

### 2. 测试选项

#### 选项A: 简化功能测试 (推荐首次使用)
```bash
# 仅测试核心功能，不启动完整服务器
python test_windows_simple.py
```

#### 选项B: MDB连接测试
```bash
# 测试MDB数据库连接
python test_windows_mdb_direct.py
```

#### 选项C: 最简服务器 (推荐生产使用) ⭐推荐⭐
```bash
# 启动最简服务器，避免复杂配置，仅提供基础API
python start_windows_minimal.py
```

#### 选项C2: 简单服务器 (推荐入门使用) 🌟新增🌟
```bash
# 启动简单服务器，无复杂配置，专注稳定运行
python start_windows_simple.py
```

#### 选项D: 完整服务器 (超级安静模式)
```bash
# 启动完整服务器，最小化日志输出，使用本地配置
python start_windows_ultra_quiet.py
```

#### 选项E: 完整服务器 (安静模式)
```bash
# 启动完整服务器，减少日志输出
python start_windows_quiet.py
```

#### 选项F: 完整服务器 (调试模式)
```bash
# 启动完整服务器，显示详细日志
python app/main.py
```

## 📊 服务器访问

当服务器启动后，可以通过以下地址访问：

- **主页**: http://localhost:8009
- **健康检查**: http://localhost:8009/health
- **状态监控**: http://localhost:8009/status
- **API文档**: http://localhost:8009/docs
- **重启服务**: http://localhost:8009/services/restart

## 🔧 配置文件

### 标准配置
- `config/config.py` - 主配置文件 (使用************远程连接)

### Windows优化配置
- `config/config_windows_local.py` - **本地配置** (使用127.0.0.1本地连接) ⭐推荐⭐
- `config_windows_quiet.py` - 减少日志输出的安静配置

## 📝 日志文件

日志文件位于 `logs/` 目录：
- `server5.log` - 主服务日志
- `server5_quiet.log` - 安静模式日志

## 🛠️ 故障排除

### 问题1: 大量循环日志输出
**原因**: 服务器包含多个后台循环任务
**解决**: 使用安静模式启动
```bash
python start_windows_quiet.py
```

### 问题2: Ctrl+C 后出现进程错误
**原因**: Windows下多进程清理问题
**解决**: 这是正常现象，服务已安全关闭

### 问题3: MDB文件访问失败
**原因**: win32com.client未安装或MDB文件不存在
**解决**: 
```bash
pip install pywin32
# 确保MDB文件存在于 D:\actest25\6.mdb
```

### 问题4: 数据库连接失败
**原因**: 远程数据库服务未启动
**解决**: 确保************上的PostgreSQL、MongoDB、Redis正在运行

## 🎮 服务控制

### 启动服务
```bash
# 最简模式 (强烈推荐，稳定可靠)
python start_windows_minimal.py

# 简单模式 (推荐入门)
python start_windows_simple.py

# 超级安静模式 (完整功能)
python start_windows_ultra_quiet.py

# 安静模式
python start_windows_quiet.py

# 调试模式
python app/main.py
```

### 停止服务
- 按 `Ctrl+C` 安全停止
- 或访问 http://localhost:8009/services/restart

### 重启服务
```bash
# 通过API重启
curl -X POST http://localhost:8009/services/restart
```

## 🏗️ 服务架构

Server5包含以下核心服务：

1. **f1_listener** - PostgreSQL监听器和分区管理
2. **f2_push_writer** - MDB数据推送和回写
3. **f3_data_puller** - MDB数据拉取器
4. **f4_operation_handler** - 客户端操作处理
5. **f5_bulk_sync** - 批量数据同步
6. **f6_user_sync** - 用户数据同步

## 🔍 监控和调试

### 查看服务状态
```bash
curl http://localhost:8009/status
```

### 查看健康状态
```bash
curl http://localhost:8009/health
```

### 手动触发分区检查
```bash
curl -X POST http://localhost:8009/services/f1/partition-check
```

## 💡 性能优化

### Windows安静模式配置
- 日志级别: WARNING
- 服务循环间隔: 优化为更长间隔
- 批处理大小: 增加以减少处理频率

### 推荐的服务间隔
- f3数据拉取: 5分钟
- f2数据推送: 1分钟  
- f5批量同步: 30分钟
- f6用户同步: 1小时

## 📞 技术支持

如果遇到问题，请：
1. 检查日志文件 `logs/server5.log`
2. 确认所有前提条件都满足
3. 使用测试脚本验证各组件功能
4. 查看API文档了解接口详情

---

*最后更新: 2025-06-26* 