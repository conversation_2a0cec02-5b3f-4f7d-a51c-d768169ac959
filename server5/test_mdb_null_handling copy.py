#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
MDB NULL值处理测试脚本
验证f2推送服务正确处理空值，让Server6生成正确的MDB SQL语句
"""

import asyncio
import sys
from pathlib import Path
import logging
from datetime import datetime, date
import asyncpg

# 添加项目路径
sys.path.append(str(Path(__file__).parent))

from app.database import IMDBClient
from app.utils.server6_client import Server6Client
from app.services.f2_push_writer import PushWriterServiceFixed

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

class MDBNullHandlingTest:
    """MDB NULL值处理测试类"""
    
    def __init__(self):
        self.imdb_client = IMDBClient()
        self.server6_client = Server6Client()
        self.push_writer = PushWriterServiceFixed()
        
        # 导入配置
        sys.path.append(str(Path(__file__).parent))
        from config.config import IMDB_HOST, IMDB_PORT, IMDB_USER, IMDB_PASS, IMDB_DB_NAME
        
        self.db_config = {
            "host": IMDB_HOST,
            "port": IMDB_PORT,
            "user": IMDB_USER,
            "password": IMDB_PASS,
            "database": IMDB_DB_NAME
        }
        
        # 测试数据 - 包含空值
        self.test_entry_data = {
            "entry_date": "2025/07/15",
            "employee_id": "215829",
            "duration": 2.0,
            "model": "",  # 空字符串
            "number": None,  # None值
            "factory_number": "   ",  # 只有空格
            "project_number": None,
            "unit_number": "",
            "category": 1,
            "item": 1,
            "department": "131"
        }
        
    async def setup_test_data(self):
        """设置测试数据到PostgreSQL"""
        try:
            # 连接到PostgreSQL
            conn = await asyncpg.connect(
                host=self.db_config["host"],
                port=self.db_config["port"],
                user=self.db_config["user"],
                password=self.db_config["password"],
                database=self.db_config["database"]
            )
            
            # 插入测试数据到entries表
            result = await conn.fetchrow("""
                INSERT INTO entries (
                    entry_date, employee_id, duration, model, number,
                    factory_number, project_number, unit_number,
                    category, item, department, source, ts
                ) VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9, $10, $11, $12, $13)
                RETURNING id
            """, 
                date(2025, 7, 15),  # entry_date
                "215829",  # employee_id
                0.2,  # duration
                "",  # model - 空字符串
                None,  # number - None
                "   ",  # factory_number - 只有空格
                None,  # project_number - None
                "",  # unit_number - 空字符串
                1,  # category
                1,  # item
                "131",  # department
                "user",  # source
                datetime.now()  # ts
            )
            
            entry_id = result['id']
            logger.info(f"✅ 测试数据已插入: entry_id={entry_id}")
            
            # 手动创建队列项
            queue_result = await conn.fetchrow("""
                INSERT INTO entries_push_queue (
                    entry_id, operation, retry_count, synced, created_ts
                ) VALUES ($1, $2, $3, $4, $5)
                RETURNING queue_id
            """,
                entry_id,  # entry_id
                "INSERT",  # operation
                0,  # retry_count
                False,  # synced
                datetime.now()  # created_ts
            )
            
            queue_id = queue_result['queue_id']
            logger.info(f"✅ 队列项已创建: queue_id={queue_id}")
            
            await conn.close()
            return entry_id, queue_id
            
        except Exception as e:
            logger.error(f"❌ 设置测试数据失败: {e}")
            raise
    
    async def test_f2_data_preparation(self, entry_id: int):
        """测试f2数据准备逻辑"""
        try:
            # 连接到PostgreSQL
            conn = await asyncpg.connect(
                host=self.db_config["host"],
                port=self.db_config["port"],
                user=self.db_config["user"],
                password=self.db_config["password"],
                database=self.db_config["database"]
            )
            
            # 获取完整的entry数据
            entry_data = await conn.fetchrow(
                "SELECT * FROM entries WHERE id = $1", entry_id
            )
            
            if not entry_data:
                raise ValueError(f"找不到entry记录: entry_id={entry_id}")
            
            # 模拟f2的数据准备逻辑
            mdb_data = {
                'employee_id': entry_data['employee_id'],
                'entry_date': entry_data['entry_date'].strftime('%Y/%m/%d') if entry_data['entry_date'] else datetime.now().strftime('%Y/%m/%d'),
                'model': entry_data['model'] if entry_data['model'] and entry_data['model'].strip() else None,
                'number': entry_data['number'] if entry_data['number'] and entry_data['number'].strip() else None,
                'factory_number': entry_data['factory_number'] if entry_data['factory_number'] and entry_data['factory_number'].strip() else None,
                'project_number': entry_data['project_number'] if entry_data['project_number'] and entry_data['project_number'].strip() else None,
                'unit_number': entry_data['unit_number'] if entry_data['unit_number'] and entry_data['unit_number'].strip() else None,
                'category': entry_data['category'] if entry_data['category'] is not None else 1,
                'item': entry_data['item'] if entry_data['item'] is not None else 1,
                'duration': float(entry_data['duration']) if entry_data['duration'] is not None else 0.0,
                'department': entry_data['department'] if entry_data['department'] and entry_data['department'].strip() else None
            }
            
            logger.info("📊 f2数据准备结果:")
            for key, value in mdb_data.items():
                status = "✅ 有值" if value is not None else "❌ NULL"
                logger.info(f"  {key}: {value} ({status})")
            
            await conn.close()
            return mdb_data
            
        except Exception as e:
            logger.error(f"❌ f2数据准备测试失败: {e}")
            raise
    
    async def test_server6_client_processing(self, mdb_data: dict):
        """测试Server6客户端的数据处理"""
        try:
            logger.info("🔍 测试Server6客户端数据处理...")
            
            # 模拟Server6客户端的insert_entry方法
            field_mapping = {
                'employee_id': '従業員ｺｰﾄﾞ',
                'entry_date': '日付',
                'model': '機種',
                'number': '号機',
                'factory_number': '工場製番',
                'project_number': '工事番号',
                'unit_number': 'ﾕﾆｯﾄ番号',
                'category': '区分',
                'item': '項目',
                'duration': '時間',
                'department': '所属ｺｰﾄﾞ'
            }
            
            # 定义必需字段
            required_fields = {
                'employee_id', 'entry_date', 'category', 'item', 'duration', 'department'
            }
            
            # 转换数据并映射字段名
            serializable_data = {}
            for key, value in mdb_data.items():
                # 映射字段名
                japanese_key = field_mapping.get(key, key)
                
                # 处理必需字段：不能跳过，确保有值
                if key in required_fields:
                    if key == 'entry_date':
                        # 处理日期格式
                        if isinstance(value, str):
                            # 将 YYYY/MM/DD 转换为 YYYY-MM-DD
                            if '/' in value:
                                serializable_data[japanese_key] = value.replace('/', '-')
                            else:
                                serializable_data[japanese_key] = value
                        else:
                            serializable_data[japanese_key] = value
                    elif key in ['category', 'item']:
                        # 确保整数类型，如果为空则使用默认值
                        if value is None or value == '':
                            serializable_data[japanese_key] = 1  # 默认值
                        else:
                            try:
                                serializable_data[japanese_key] = int(value)
                            except (ValueError, TypeError):
                                serializable_data[japanese_key] = 1  # 默认值
                    elif key == 'duration':
                        # 确保浮点数类型，如果为空则使用默认值
                        if value is None or value == '':
                            serializable_data[japanese_key] = 0.0  # 默认值
                        else:
                            try:
                                serializable_data[japanese_key] = float(value)
                            except (ValueError, TypeError):
                                serializable_data[japanese_key] = 0.0  # 默认值
                    else:
                        # 字符串字段，如果为空则使用空字符串而不是跳过
                        serializable_data[japanese_key] = value if value is not None else ""
                else:
                    # 处理可选字段：不跳过，明确发送NULL
                    if value is None:
                        serializable_data[japanese_key] = "NULL"
                        logger.info(f"  📤 可选字段 {key} 设置为 NULL")
                        continue
                    
                    if isinstance(value, str) and value.strip() == '':
                        serializable_data[japanese_key] = "NULL"
                        logger.info(f"  📤 可选字段 {key} (空字符串) 设置为 NULL")
                        continue
                    
                    # 处理日期格式
                    if isinstance(value, str) and key == 'entry_date':
                        # 如果是字符串格式的日期，转换为YYYY-MM-DD格式
                        if '/' in value:
                            # 将 YYYY/MM/DD 转换为 YYYY-MM-DD
                            serializable_data[japanese_key] = value.replace('/', '-')
                        else:
                            serializable_data[japanese_key] = value
                    else:
                        serializable_data[japanese_key] = value
            
            logger.info("📤 发送到Server6的数据:")
            for key, value in serializable_data.items():
                status = "✅ 有值" if value is not None else "❌ NULL"
                logger.info(f"  {key}: {value} ({status})")
            
            # 模拟MDB SQL语句
            logger.info("🔍 模拟MDB SQL语句:")
            sql_fields = []
            sql_values = []
            
            for japanese_key, value in serializable_data.items():
                sql_fields.append(japanese_key)
                if value is None:
                    sql_values.append("NULL")
                elif isinstance(value, str):
                    sql_values.append(f"'{value}'")
                elif isinstance(value, (int, float)):
                    sql_values.append(str(value))
                else:
                    sql_values.append(f"'{value}'")
            
            sql_statement = f"""
INSERT INTO [元作業時間]
              ({', '.join(sql_fields)})
             VALUES
              ({', '.join(sql_values)})
            """
            
            logger.info(f"📝 生成的SQL语句:\n{sql_statement}")
            
            return serializable_data
            
        except Exception as e:
            logger.error(f"❌ Server6客户端处理测试失败: {e}")
            raise
    
    async def cleanup_test_data(self, entry_id: int, queue_id: int):
        """清理测试数据"""
        try:
            # 连接到PostgreSQL
            conn = await asyncpg.connect(
                host=self.db_config["host"],
                port=self.db_config["port"],
                user=self.db_config["user"],
                password=self.db_config["password"],
                database=self.db_config["database"]
            )
            
            # 删除队列项
            await conn.execute(
                "DELETE FROM entries_push_queue WHERE queue_id = $1", queue_id
            )
            
            # 删除entry记录
            await conn.execute(
                "DELETE FROM entries WHERE id = $1", entry_id
            )
            
            await conn.close()
            logger.info("✅ 测试数据已清理")
            
        except Exception as e:
            logger.error(f"❌ 清理测试数据失败: {e}")
    
    async def run_test(self):
        """运行完整测试"""
        logger.info("🧪 开始MDB NULL值处理测试")
        logger.info("=" * 60)
        
        entry_id = None
        queue_id = None
        
        try:
            # 1. 设置测试数据
            logger.info("📝 步骤1: 设置测试数据...")
            entry_id, queue_id = await self.setup_test_data()
            
            # 2. 测试f2数据准备
            logger.info("🔧 步骤2: 测试f2数据准备逻辑...")
            mdb_data = await self.test_f2_data_preparation(entry_id)
            
            # 3. 测试Server6客户端处理
            logger.info("📡 步骤3: 测试Server6客户端数据处理...")
            await self.test_server6_client_processing(mdb_data)
            
            logger.info("✅ MDB NULL值处理测试完成")
            
        except Exception as e:
            logger.error(f"❌ 测试失败: {e}")
            raise
        finally:
            # 清理测试数据
            if entry_id and queue_id:
                await self.cleanup_test_data(entry_id, queue_id)

async def main():
    """主函数"""
    test = MDBNullHandlingTest()
    await test.run_test()

if __name__ == "__main__":
    asyncio.run(main()) 