# 🗑️ 删除操作行为说明

## 🎯 **删除操作概述**

删除操作的行为与更新操作不同，它可能产生一条或两条队列项，这取决于记录的 `source` 字段值。

## 📊 **删除操作行为分析**

### 情况1：记录的 source='user'
```
直接执行 DELETE → 触发一次DELETE触发器 → 创建一条DELETE队列项
```

**队列项数量**: 1条
**操作类型**: DELETE

### 情况2：记录的 source='system'
```
UPDATE SET source='user' → 触发UPDATE触发器 → 创建UPDATE队列项
DELETE → 触发DELETE触发器 → 创建DELETE队列项
```

**队列项数量**: 2条
**操作类型**: UPDATE + DELETE

## 🔍 **为什么会有两种行为？**

### 触发器逻辑
PostgreSQL触发器 `trg_entries_enqueue` 的逻辑：
```sql
ELSIF (TG_OP='DELETE' AND OLD.source = 'user') THEN
    INSERT INTO entries_push_queue(entry_id, external_id, operation)
    VALUES (OLD.id, OLD.external_id,'DELETE') RETURNING queue_id INTO q;
    PERFORM pg_notify('push_job', q::text);
END IF;
```

这意味着：
- 只有删除时 `OLD.source = 'user'` 才会触发DELETE队列
- 如果 `source = 'system'`，删除时不会触发队列

### 业务逻辑需求
1. **source='user' 的记录**: 已经是用户操作，直接删除即可
2. **source='system' 的记录**: 需要先同步到MDB，然后删除

## 📋 **队列项处理流程**

### 对于 source='system' 的记录：

1. **第一步 - UPDATE操作**:
   ```sql
   UPDATE entries SET source = 'user' WHERE id = $1
   ```
   - 触发UPDATE触发器
   - 创建UPDATE队列项
   - f2推送服务将数据同步到MDB

2. **第二步 - DELETE操作**:
   ```sql
   DELETE FROM entries WHERE id = $1
   ```
   - 触发DELETE触发器
   - 创建DELETE队列项
   - f2推送服务从MDB删除数据

## 🔧 **代码实现**

### 删除操作代码逻辑
```python
# 在HTTP-only模式下，直接执行删除操作
if HTTP_ONLY_MODE:
    # 如果source不是'user'，先更新为'user'，然后删除
    if existing_entry.get('source') != 'user':
        # 先更新source为'user'，这会触发一次触发器（UPDATE队列项）
        await imdb_client.execute_command(
            "UPDATE entries SET source = 'user' WHERE id = $1", actual_internal_id
        )
        # 等待一小段时间确保UPDATE触发器处理完成
        await asyncio.sleep(0.1)
    
    # 删除PostgreSQL记录，这会触发第二次触发器（DELETE队列项）
    await imdb_client.execute_command(
        "DELETE FROM entries WHERE id = $1", actual_internal_id
    )
```

## ✅ **这是正常行为**

### 为什么两条队列项是正常的？

1. **数据一致性**: 确保MDB中的数据与PostgreSQL保持一致
2. **同步完整性**: 先同步数据，再删除数据
3. **业务逻辑**: 系统需要知道哪些数据需要同步，哪些需要删除

### 与更新操作的区别

- **更新操作**: 只需要一次UPDATE，所以修复后只有一条队列项
- **删除操作**: 可能需要UPDATE+DELETE，所以可能有两条队列项

## 🧪 **验证方法**

### 1. 检查队列项
```bash
cd server5
python check_queue_duplicates.py
```

### 2. 观察日志
- source='user' 的记录：只看到一次DELETE操作
- source='system' 的记录：先看到UPDATE操作，然后看到DELETE操作

### 3. 数据库查询
```sql
-- 检查指定entry_id的队列项
SELECT queue_id, entry_id, external_id, operation, synced, created_ts
FROM entries_push_queue 
WHERE entry_id = $1 
ORDER BY created_ts DESC;
```

## 📝 **总结**

- **更新操作**: 修复后只产生一条UPDATE队列项 ✅
- **删除操作**: 可能产生一条或两条队列项，这是正常行为 ✅
  - source='user': 1条DELETE队列项
  - source='system': 1条UPDATE队列项 + 1条DELETE队列项

删除操作的两条队列项不是bug，而是业务逻辑的体现，确保数据同步的完整性。 