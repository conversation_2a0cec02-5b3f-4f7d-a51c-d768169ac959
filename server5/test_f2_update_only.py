#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
简化的F2更新测试脚本
直接测试F2服务的UPDATE处理逻辑，不依赖Server6
"""

import asyncio
import asyncpg
import sys
from pathlib import Path
from datetime import datetime
from unittest.mock import AsyncMock, MagicMock

# 添加项目路径
sys.path.append(str(Path(__file__).parent))

# 数据库连接
PG_DATABASE_URL = "***************************************************/imdb"
TARGET_EXTERNAL_ID = 602631

async def test_f2_update_logic():
    """直接测试F2的UPDATE处理逻辑"""
    print("=== F2 UPDATE 逻辑测试 ===")
    
    conn = None
    try:
        # 连接数据库
        conn = await asyncpg.connect(PG_DATABASE_URL)
        print("✅ 数据库连接成功")
        
        # 1. 查找现有的UPDATE队列项
        queue_items = await conn.fetch("""
            SELECT q.queue_id, q.operation, q.entry_id, q.external_id, q.synced
            FROM entries_push_queue q
            WHERE q.operation = 'UPDATE' AND q.synced = FALSE
            ORDER BY q.created_ts DESC
            LIMIT 5
        """)
        
        if not queue_items:
            print("❌ 没有找到未同步的UPDATE队列项")
            return False
        
        print(f"找到 {len(queue_items)} 个未同步的UPDATE队列项:")
        for item in queue_items:
            print(f"  queue_id={item['queue_id']}, entry_id={item['entry_id']}, external_id={item['external_id']}")
        
        # 选择第一个队列项进行测试
        test_item = dict(queue_items[0])
        queue_id = test_item['queue_id']
        entry_id = test_item['entry_id']
        external_id = test_item['external_id']
        
        print(f"\n🧪 测试队列项: queue_id={queue_id}, entry_id={entry_id}, external_id={external_id}")
        
        # 2. 获取完整的entry数据
        entry_data_row = await conn.fetchrow("SELECT * FROM entries WHERE id = $1", entry_id)
        if not entry_data_row:
            print(f"❌ 找不到entry记录: entry_id={entry_id}")
            return False
        
        entry_data = dict(entry_data_row)
        print(f"✅ 获取到entry数据: employee_id={entry_data.get('employee_id')}, duration={entry_data.get('duration')}")
        
        # 3. 准备MDB更新数据（模拟F2逻辑）
        mdb_data = {
            'employee_id': entry_data.get('employee_id'),
            'entry_date': entry_data.get('entry_date'),
            'model': entry_data.get('model'),
            'number': entry_data.get('number'),
            'factory_number': entry_data.get('factory_number'),
            'project_number': entry_data.get('project_number'),
            'unit_number': entry_data.get('unit_number'),
            'category': entry_data.get('category'),
            'item': entry_data.get('item'),
            'duration': float(entry_data.get('duration', 0.0)),  # 关键修复: 将Decimal转换为float
            'department': entry_data.get('department')
        }
        
        print(f"✅ 准备的MDB数据:")
        for key, value in mdb_data.items():
            print(f"  {key}: {value} ({type(value).__name__})")
        
        # 4. 模拟Server6 API调用成功
        print(f"\n🔄 模拟Server6 API调用...")
        
        # 模拟成功的响应
        mock_response = {
            'success': True,
            'message': '记录更新成功',
            'affected_rows': 1
        }
        print(f"✅ 模拟Server6响应: {mock_response}")
        
        # 5. 在事务中标记队列项为已同步（模拟F2成功逻辑）
        async with conn.transaction():
            await conn.execute(
                "UPDATE entries_push_queue SET synced = TRUE WHERE queue_id = $1",
                queue_id
            )
            print(f"✅ 队列项 {queue_id} 已标记为已同步")
        
        # 6. 验证结果
        final_status = await conn.fetchrow(
            "SELECT synced FROM entries_push_queue WHERE queue_id = $1",
            queue_id
        )
        
        if final_status and final_status['synced']:
            print(f"🎉 测试成功！队列项 {queue_id} 处理完成")
            return True
        else:
            print(f"❌ 测试失败！队列项 {queue_id} 状态未更新")
            return False
        
    except Exception as e:
        print(f"❌ 测试过程中出错: {e}")
        return False
    finally:
        if conn:
            await conn.close()

async def main():
    """主函数"""
    print("🧪 F2 UPDATE 逻辑简化测试")
    print("=" * 50)
    
    success = await test_f2_update_logic()
    
    print("=" * 50)
    if success:
        print("🎉 F2 UPDATE 逻辑测试通过！")
        print("💡 这意味着数据类型转换修复有效")
        print("💡 问题可能在于Server6连接或Server6本身")
    else:
        print("❌ F2 UPDATE 逻辑测试失败")
        print("💡 需要进一步检查数据处理逻辑")

if __name__ == "__main__":
    asyncio.run(main()) 