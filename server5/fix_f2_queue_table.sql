-- 2025 07/04 + 16：30 + 相关主题
-- 修正f2服务因缺少字段而无法启动的问题

-- 为 entries_push_queue 表添加 retry_count 和 last_error 字段

ALTER TABLE public.entries_push_queue
ADD COLUMN IF NOT EXISTS retry_count INTEGER DEFAULT 0,
ADD COLUMN IF NOT EXISTS last_error TEXT;

COMMENT ON COLUMN public.entries_push_queue.retry_count IS '记录任务失败重试的次数';
COMMENT ON COLUMN public.entries_push_queue.last_error IS '记录最后一次失败的错误信息';

-- 为新字段创建索引，以优化查询性能
CREATE INDEX IF NOT EXISTS idx_entries_push_queue_retry_count
ON public.entries_push_queue(retry_count)
WHERE synced = FALSE;

-- 重置所有现有任务的重试计数（可选，但推荐）
UPDATE public.entries_push_queue
SET retry_count = 0
WHERE retry_count IS NULL;

-- 确认修改
SELECT
    column_name,
    data_type,
    character_maximum_length,
    column_default
FROM
    information_schema.columns
WHERE
    table_schema = 'public' AND table_name = 'entries_push_queue'; 