nohup: ignoring input
🔧 加载配置: config_ubuntu_remote
🐛 MySuite Server5 - 数据同步微服务 (Ubuntu远程) 运行在调试模式
📊 PostgreSQL: ************:5432
🍃 MongoDB: ************:27017
⚡ Redis: localhost:6379
💾 ODBC可用: False
📋 配置信息:
  - 配置文件: config_ubuntu_remote
  - 平台: Linux
  - 主机: test
  - IP: *************
  - 自动检测: config_ubuntu_remote
🔧 加载配置: config_ubuntu_remote
📋 配置信息:
  - 配置文件: config_ubuntu_remote
  - 平台: Linux
  - 主机: test
  - IP: *************
  - 自动检测: config_ubuntu_remote
🌐 MySuite Server5 - 纯HTTP API服务器
============================================================
🔧 模式: HTTP-only (不启动 f1-f4 微服务)
🌐 功能: 仅提供 HTTP API 接口
🔗 微服务: 需要单独启动 start_server5_notwith_api.py
============================================================
🔍 检查端口 8009 可用性...
✅ 端口8009可用
🌐 启动纯HTTP API服务器...
📡 监听地址: http://0.0.0.0:8009
📋 API文档: http://localhost:8009/docs
🔍 健康检查: http://localhost:8009/health
⚠️  注意：此模式仅启动HTTP API，不包含f1-f4微服务
🔧 如需启动微服务，请另开终端运行: python start_server5_notwith_api.py
👋 按 Ctrl+C 退出
INFO:     Started server process [1938880]
INFO:     Waiting for application startup.
🔥 当前加载的 entries_api.py 路径: /home/<USER>/Documents/testF/2025/ser25/server0613/MySuiteDatebase2402newuigood-yolo5-3/server5/app/routers/entries_api.py
🔥 当前加载的 entries_api.py 内容版本: 20250710 - HTTP-only 模式支持
🔥🔥🔥 这是优化后的 entries_api.py！🔥🔥🔥
2025-07-10 14:24:33,045 - INFO - MySuite Server5 - 数据同步微服务 (Ubuntu远程) v1.0.0 启动中 (HTTP-only 模式)...
2025-07-10 14:24:33,045 - INFO - 🌐 HTTP-only 模式: 仅启动 HTTP API，不启动微服务
2025-07-10 14:24:33,311 - INFO - ✅ PostgreSQL连接池创建成功
2025-07-10 14:24:33,311 - INFO - ✅ HTTP-only 模式下数据库连接成功
INFO:     Application startup complete.
INFO:     Uvicorn running on http://0.0.0.0:8009 (Press CTRL+C to quit)
INFO:     127.0.0.1:55796 - "GET /health HTTP/1.1" 500 Internal Server Error
INFO:     127.0.0.1:55810 - "GET /api/timeprotab?employee_id=215829 HTTP/1.1" 307 Temporary Redirect
INFO:     127.0.0.1:38562 - "GET /api/timeprotab?employee_id=215829&year=2025&month=7 HTTP/1.1" 307 Temporary Redirect
INFO:     127.0.0.1:38568 - "GET / HTTP/1.1" 200 OK
2025-07-10 14:25:05,727 - INFO - 🔗 HTTP-only 模式: 使用 service_manager 的数据库连接
INFO:     127.0.0.1:36740 - "GET /api/entries/months?employee_id=215829 HTTP/1.1" 200 OK
INFO:     127.0.0.1:36754 - "GET /api/timeprotab/months?employee_id=215829 HTTP/1.1" 200 OK
INFO:     127.0.0.1:54918 - "GET /api/timeprotab?employee_id=215829&start_date=2025-07-01&end_date=2025-07-31&limit=5 HTTP/1.1" 307 Temporary Redirect
INFO:     127.0.0.1:54928 - "GET /api/timeprotab?employee_id=215829&start_date=2025-07-01&end_date=2025-07-31&limit=5 HTTP/1.1" 307 Temporary Redirect
INFO:     Shutting down
INFO:     Waiting for application shutdown.
2025-07-10 14:25:26,836 - INFO - 🌐 HTTP-only 模式: 断开数据库连接
2025-07-10 14:25:26,840 - INFO - 🔌 PostgreSQL连接已关闭
INFO:     Application shutdown complete.
INFO:     Finished server process [1938880]

📡 接收到信号 15，正在关闭HTTP服务...
