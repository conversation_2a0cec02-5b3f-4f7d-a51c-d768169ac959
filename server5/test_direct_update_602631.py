#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
直接测试 UPDATE 操作：external_id=602631, duration=3.0
专门测试 F2 服务的 UPDATE 处理和 Server6 同步
"""

import asyncio
import asyncpg
import sys
from pathlib import Path
from datetime import datetime
import time

# 添加项目路径
sys.path.append(str(Path(__file__).parent))

# 数据库连接
PG_DATABASE_URL = "***************************************************/imdb"
TARGET_EXTERNAL_ID = 602631
NEW_DURATION = 3.0

async def test_direct_update():
    """直接测试 UPDATE 操作并监控同步过程"""
    print("=" * 60)
    print(f"🎯 直接测试 UPDATE: external_id={TARGET_EXTERNAL_ID}, duration={NEW_DURATION}")
    print("=" * 60)
    
    conn = None
    try:
        # 连接数据库
        conn = await asyncpg.connect(PG_DATABASE_URL)
        print("✅ 数据库连接成功")
        
        # 1. 查看当前状态
        print(f"\n📋 1. 查看当前记录状态...")
        current_record = await conn.fetchrow("""
            SELECT id, external_id, employee_id, entry_date, duration, project_number, source, ts
            FROM public.entries 
            WHERE external_id = $1
        """, TARGET_EXTERNAL_ID)
        
        if not current_record:
            print(f"❌ 找不到 external_id = {TARGET_EXTERNAL_ID} 的记录")
            return False
        
        print(f"   当前记录:")
        print(f"   - PostgreSQL ID: {current_record['id']}")
        print(f"   - External ID: {current_record['external_id']}")
        print(f"   - Employee ID: {current_record['employee_id']}")
        print(f"   - Entry Date: {current_record['entry_date']}")
        print(f"   - Duration: {current_record['duration']} (当前值)")
        print(f"   - Project Number: {current_record['project_number']}")
        print(f"   - Source: {current_record['source']}")
        print(f"   - Timestamp: {current_record['ts']}")
        
        original_entry_id = current_record['id']
        
        # 2. 清理之前的队列项（可选）
        print(f"\n🧹 2. 清理之前的未同步队列项...")
        cleanup_count = await conn.execute("""
            DELETE FROM entries_push_queue 
            WHERE entry_id = $1 AND synced = FALSE
        """, original_entry_id)
        print(f"   清理了 {cleanup_count.split()[-1]} 个未同步队列项")
        
        # 3. 执行 UPDATE 操作
        print(f"\n🔄 3. 执行 UPDATE 操作: duration {current_record['duration']} -> {NEW_DURATION}")
        
        update_sql = """
            UPDATE public.entries
            SET
                duration = $1,
                source = 'user',
                ts = NOW()
            WHERE external_id = $2
            RETURNING id, external_id, duration, ts;
        """
        
        updated_record = await conn.fetchrow(update_sql, NEW_DURATION, TARGET_EXTERNAL_ID)
        
        if updated_record:
            print(f"   ✅ UPDATE 成功执行")
            print(f"   - PostgreSQL ID: {updated_record['id']}")
            print(f"   - External ID: {updated_record['external_id']}")
            print(f"   - 新 Duration: {updated_record['duration']}")
            print(f"   - 更新时间: {updated_record['ts']}")
        else:
            print("   ❌ UPDATE 执行失败")
            return False
        
        # 4. 等待触发器执行
        print(f"\n⏳ 4. 等待触发器执行...")
        await asyncio.sleep(2)
        
        # 5. 检查队列状态
        print(f"\n📋 5. 检查队列中的 UPDATE 任务...")
        queue_items = await conn.fetch("""
            SELECT queue_id, operation, entry_id, external_id, synced, created_ts
            FROM entries_push_queue
            WHERE entry_id = $1 AND operation = 'UPDATE'
            ORDER BY created_ts DESC
            LIMIT 3
        """, original_entry_id)
        
        if not queue_items:
            print("   ❌ 触发器没有创建 UPDATE 任务")
            return False
        
        print(f"   ✅ 找到 {len(queue_items)} 个 UPDATE 队列项:")
        latest_queue_item = None
        for item in queue_items:
            print(f"   - queue_id={item['queue_id']}, synced={item['synced']}, created_ts={item['created_ts']}")
            if latest_queue_item is None:
                latest_queue_item = item
        
        # 6. 监控 F2 处理过程
        print(f"\n🔍 6. 监控 F2 服务处理过程...")
        queue_id = latest_queue_item['queue_id']
        
        # 等待最多 30 秒
        max_wait_time = 30
        check_interval = 2
        checks = 0
        max_checks = max_wait_time // check_interval
        
        while checks < max_checks:
            await asyncio.sleep(check_interval)
            checks += 1
            
            # 检查队列项状态
            current_status = await conn.fetchrow("""
                SELECT synced, created_ts 
                FROM entries_push_queue 
                WHERE queue_id = $1
            """, queue_id)
            
            if current_status and current_status['synced']:
                print(f"   ✅ F2 处理完成！(等待了 {checks * check_interval} 秒)")
                print(f"   - queue_id: {queue_id}")
                print(f"   - 状态: synced = TRUE")
                break
            else:
                print(f"   ⏳ 等待中... ({checks * check_interval}/{max_wait_time}秒) synced={current_status['synced'] if current_status else 'Unknown'}")
        else:
            print(f"   ❌ F2 处理超时！(等待了 {max_wait_time} 秒)")
            print("   可能的原因:")
            print("   - F2 服务没有运行")
            print("   - Server6 连接问题")
            print("   - SQL 语句问题")
            return False
        
        # 7. 最终验证
        print(f"\n✅ 7. 最终验证...")
        final_record = await conn.fetchrow("""
            SELECT duration, ts, source
            FROM public.entries 
            WHERE external_id = $1
        """, TARGET_EXTERNAL_ID)
        
        final_queue_status = await conn.fetchrow("""
            SELECT synced 
            FROM entries_push_queue 
            WHERE queue_id = $1
        """, queue_id)
        
        print(f"   PostgreSQL 记录:")
        print(f"   - Duration: {final_record['duration']}")
        print(f"   - Source: {final_record['source']}")
        print(f"   - 最后更新: {final_record['ts']}")
        
        print(f"   队列状态:")
        print(f"   - Queue ID: {queue_id}")
        print(f"   - Synced: {final_queue_status['synced']}")
        
        # 检查是否成功
        success = (
            float(final_record['duration']) == NEW_DURATION and
            final_queue_status['synced'] == True
        )
        
        if success:
            print(f"\n🎉 测试完全成功！")
            print(f"   ✅ PostgreSQL duration 已更新为 {NEW_DURATION}")
            print(f"   ✅ 队列项已标记为 synced")
            print(f"   ✅ 数据应该已同步到 MDB")
        else:
            print(f"\n❌ 测试失败")
            
        return success
        
    except Exception as e:
        print(f"❌ 测试过程中出错: {e}")
        import traceback
        traceback.print_exc()
        return False
    finally:
        if conn:
            await conn.close()

async def main():
    """主函数"""
    print("🧪 直接 UPDATE 测试 - external_id=602631")
    
    success = await test_direct_update()
    
    print("=" * 60)
    if success:
        print("🎉 UPDATE 测试成功！")
        print("💡 F2 服务和 Server6 同步正常工作")
    else:
        print("❌ UPDATE 测试失败")
        print("💡 需要检查 F2 服务或 Server6 连接")
    print("=" * 60)

if __name__ == "__main__":
    asyncio.run(main()) 