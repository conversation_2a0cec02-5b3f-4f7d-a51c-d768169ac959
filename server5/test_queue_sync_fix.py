#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试队列同步状态修复脚本
验证队列项在处理完成后正确标记为已同步
"""

import asyncio
import sys
from pathlib import Path
from datetime import datetime, date
import logging

# 添加项目路径
sys.path.append(str(Path(__file__).parent))

from app.database import IMDBClient, RedisClient, MongoDBClient
from app.services.f1_listener import ListenerService
from app.services.f2_push_writer_fixed import PushWriterServiceFixed

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

class QueueSyncFixTest:
    """队列同步状态修复测试类"""
    
    def __init__(self):
        self.imdb_client = IMDBClient()
        self.redis_client = RedisClient()
        self.mongo_client = MongoDBClient()
        
        # 测试数据
        self.test_data = {
            'employee_id': '215829',
            'entry_date': date(2025, 6, 26),
            'model': '',
            'number': '',
            'factory_number': '',
            'project_number': '24585',
            'unit_number': '',
            'category': 3,
            'item': 7,
            'duration': 8.0,
            'department': '131'
        }
        
        # 服务实例
        self.f1_service = None
        self.f2_service = None
    
    async def start(self):
        """启动测试环境"""
        logger.info("🚀 启动测试环境...")
        
        # 连接数据库
        await self.imdb_client.connect()
        await self.redis_client.connect()
        await self.mongo_client.connect()
        
        logger.info("✅ 数据库连接成功")
    
    async def stop(self):
        """停止测试环境"""
        logger.info("🔌 停止测试环境...")
        
        if self.f1_service:
            await self.f1_service.stop()
        if self.f2_service:
            await self.f2_service.stop()
        
        await self.imdb_client.disconnect()
        await self.redis_client.disconnect()
        await self.mongo_client.disconnect()
        
        logger.info("✅ 测试环境已停止")
    
    async def cleanup_test_data(self):
        """清理测试数据"""
        logger.info("🧹 清理测试数据...")
        
        try:
            # 只删除相关队列项，不删除entries表中的数据
            await self.imdb_client.execute_command(
                "DELETE FROM entries_push_queue WHERE entry_id IN (SELECT id FROM entries WHERE employee_id = $1 AND entry_date = $2)",
                self.test_data['employee_id'], self.test_data['entry_date']
            )
            
            logger.info("✅ 测试数据清理完成（只清理队列项，保留entries数据）")
            
        except Exception as e:
            logger.error(f"❌ 清理测试数据失败: {e}")
    
    async def check_queue_status(self, description: str):
        """检查队列状态"""
        logger.info(f"📋 {description} - 检查队列状态...")
        
        # 检查未同步队列项
        unsynced_items = await self.imdb_client.get_queue_items(synced=False, limit=10)
        logger.info(f"  未同步队列项: {len(unsynced_items)} 个")
        
        # 检查已同步队列项
        synced_items = await self.imdb_client.get_queue_items(synced=True, limit=10)
        logger.info(f"  已同步队列项: {len(synced_items)} 个")
        
        # 显示队列项详情
        if unsynced_items:
            logger.info("  未同步队列项详情:")
            for item in unsynced_items:
                logger.info(f"    queue_id={item['queue_id']}, operation={item['operation']}, entry_id={item['entry_id']}")
        
        if synced_items:
            logger.info("  已同步队列项详情:")
            for item in synced_items:
                logger.info(f"    queue_id={item['queue_id']}, operation={item['operation']}, entry_id={item['entry_id']}")
        
        return len(unsynced_items), len(synced_items)
    
    async def test_queue_sync_fix(self):
        """测试队列同步状态修复"""
        logger.info("=" * 60)
        logger.info("🧪 测试队列同步状态修复: 验证队列项正确标记为已同步")
        logger.info("=" * 60)
        
        # 清理之前的数据
        await self.cleanup_test_data()
        
        # 启动f1和f2服务
        logger.info("🚀 启动f1和f2服务...")
        self.f1_service = ListenerService()
        self.f2_service = PushWriterServiceFixed()
        
        await self.f1_service.start()
        await self.f2_service.start()
        logger.info("✅ f1和f2服务启动成功")
        
        # 记录操作前的队列状态
        unsynced_before, synced_before = await self.check_queue_status("用户操作前")
        
        # 执行用户操作（source='user'）
        logger.info("📝 执行用户操作（source='user'）...")
        entry_id = await self.imdb_client.create_entry(self.test_data, source='user')
        
        if entry_id:
            logger.info(f"✅ 用户操作成功，entry_id: {entry_id}")
        else:
            logger.error("❌ 用户操作失败")
            return False
        
        # 等待触发器执行和队列处理
        logger.info("⏳ 等待触发器执行和队列处理...")
        await asyncio.sleep(12)  # 增加等待时间
        
        # 检查操作后的队列状态
        unsynced_after, synced_after = await self.check_queue_status("用户操作后")
        
        # 验证结果
        total_before = unsynced_before + synced_before
        total_after = unsynced_after + synced_after
        queue_increase = total_after - total_before
        
        logger.info(f"📊 队列项变化分析:")
        logger.info(f"  操作前总数: {total_before}")
        logger.info(f"  操作后总数: {total_after}")
        logger.info(f"  增加数量: {queue_increase}")
        logger.info(f"  未同步项: {unsynced_after}")
        logger.info(f"  已同步项: {synced_after}")
        
        # 检查entries表中的external_id是否已更新
        entry_info = await self.imdb_client.execute_query(
            "SELECT id, external_id, employee_id, entry_date, source FROM entries WHERE id = $1",
            entry_id
        )
        
        if entry_info:
            entry = entry_info[0]
            logger.info(f"📊 entries记录状态:")
            logger.info(f"   ID: {entry['id']}")
            logger.info(f"   员工ID: {entry['employee_id']}")
            logger.info(f"   日期: {entry['entry_date']}")
            logger.info(f"   external_id: {entry['external_id']}")
            logger.info(f"   source: {entry['source']}")
            
            if entry['external_id']:
                logger.info("✅ external_id已更新，MDB推送成功")
                
                # 关键验证：队列项应该被标记为已同步
                if unsynced_after == 0 and synced_after == 1:
                    logger.info("✅ 验证成功: 队列项正确标记为已同步")
                    return True
                elif unsynced_after > 0:
                    logger.error(f"❌ 验证失败: 仍有{unsynced_after}个未同步队列项")
                    return False
                else:
                    logger.warning("⚠️ 没有队列项，可能触发器没有正常工作")
                    return False
            else:
                logger.warning("⚠️ external_id未更新，MDB推送可能失败")
                return False
        else:
            logger.error("❌ 未找到entries记录")
            return False
    
    async def run_test(self):
        """运行测试"""
        logger.info("🎯 开始队列同步状态修复验证测试")
        logger.info("=" * 80)
        
        try:
            await self.start()
            
            # 测试队列同步状态修复
            test_result = await self.test_queue_sync_fix()
            
            # 输出测试结果
            logger.info("=" * 80)
            logger.info("📊 测试结果总结:")
            logger.info(f"  队列同步状态修复测试: {'✅ 通过' if test_result else '❌ 失败'}")
            
            if test_result:
                logger.info("🎉 队列同步状态修复成功！队列项正确标记为已同步")
            else:
                logger.error("❌ 队列同步状态修复失败，需要进一步检查")
            
        except Exception as e:
            logger.error(f"❌ 测试过程中发生错误: {e}", exc_info=True)
        finally:
            await self.stop()

async def main():
    """主函数"""
    tester = QueueSyncFixTest()
    await tester.run_test()

if __name__ == "__main__":
    asyncio.run(main()) 