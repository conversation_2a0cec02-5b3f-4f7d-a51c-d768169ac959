#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试Server6删除功能
验证f2_push_writer.py调用Server6删除API是否正常工作
"""

import asyncio
import sys
from pathlib import Path
import logging
import json
import requests
from datetime import datetime

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

class Server6DeleteTest:
    """Server6删除功能测试类"""
    
    def __init__(self):
        self.server6_base_url = "http://192.168.3.93:8019"  # 局域网Win10中的Server6
        
    def test_server6_health(self):
        """测试Server6健康状态"""
        print("🔍 测试Server6健康状态")
        print("=" * 50)
        
        try:
            response = requests.get(f"{self.server6_base_url}/health", timeout=10)
            if response.status_code == 200:
                data = response.json()
                print(f"✅ Server6健康检查成功: {data}")
                return True
            else:
                print(f"❌ Server6健康检查失败: {response.status_code}")
                return False
        except Exception as e:
            print(f"❌ 连接Server6失败: {e}")
            return False
    
    def test_server6_mdb_connection(self):
        """测试Server6 MDB连接"""
        print("\n🔍 测试Server6 MDB连接")
        print("=" * 50)
        
        try:
            response = requests.get(f"{self.server6_base_url}/mdb/test", timeout=10)
            if response.status_code == 200:
                data = response.json()
                print(f"✅ MDB连接测试成功: {data}")
                return True
            else:
                print(f"❌ MDB连接测试失败: {response.status_code}")
                return False
        except Exception as e:
            print(f"❌ MDB连接测试失败: {e}")
            return False
    
    def test_server6_delete_api(self, external_id: int):
        """测试Server6删除API"""
        print(f"\n🗑️ 测试Server6删除API: external_id={external_id}")
        print("=" * 50)
        
        try:
            response = requests.delete(
                f"{self.server6_base_url}/mdb/entries/delete/{external_id}",
                timeout=10
            )
            
            if response.status_code == 200:
                data = response.json()
                print(f"✅ 删除API调用成功: {data}")
                return True
            else:
                print(f"❌ 删除API调用失败: {response.status_code}")
                print(f"错误信息: {response.text}")
                return False
        except Exception as e:
            print(f"❌ 删除API调用异常: {e}")
            return False
    
    def test_server6_insert_api(self):
        """测试Server6插入API（用于创建测试数据）"""
        print("\n📝 测试Server6插入API（创建测试数据）")
        print("=" * 50)
        
        test_data = {
            "従業員ｺｰﾄﾞ": "215829",
            "日付": "2025-07-15",  # 修复：使用YYYY-MM-DD格式
            "機種": "TEST_DELETE_MODEL",
            "号機": "TEST_DELETE_001",
            "工場製番": "FACTORY_DELETE_001",
            "工事番号": "PROJECT_DELETE_001",
            "ﾕﾆｯﾄ番号": "UNIT_DELETE_001",
            "区分": "1",
            "項目": "1",
            "時間": 0.5,
            "所属ｺｰﾄﾞ": "131"
        }
        
        try:
            response = requests.post(
                f"{self.server6_base_url}/mdb/entries/insert",
                json=test_data,
                timeout=10
            )
            
            if response.status_code == 200:
                data = response.json()
                print(f"✅ 插入API调用成功: {data}")
                if data.get('success') and data.get('inserted_id'):
                    return data.get('inserted_id')
                else:
                    print("❌ 插入成功但未获取到inserted_id")
                    return None
            else:
                print(f"❌ 插入API调用失败: {response.status_code}")
                print(f"错误信息: {response.text}")
                return None
        except Exception as e:
            print(f"❌ 插入API调用异常: {e}")
            return None
    
    def run_full_test(self):
        """运行完整测试"""
        print("🧪 Server6删除功能完整测试")
        print("=" * 60)
        
        # 1. 测试健康状态
        if not self.test_server6_health():
            print("❌ 健康检查失败，停止测试")
            return
        
        # 2. 测试MDB连接
        if not self.test_server6_mdb_connection():
            print("❌ MDB连接失败，停止测试")
            return
        
        # 3. 插入测试数据
        print("\n📝 步骤3: 插入测试数据")
        external_id = self.test_server6_insert_api()
        if not external_id:
            print("❌ 插入测试数据失败，停止测试")
            return
        
        print(f"✅ 测试数据插入成功，external_id={external_id}")
        
        # 4. 等待一下确保数据写入
        import time
        time.sleep(2)
        
        # 5. 测试删除API
        print(f"\n🗑️ 步骤4: 测试删除API (external_id={external_id})")
        if self.test_server6_delete_api(external_id):
            print("✅ 删除功能测试成功！")
        else:
            print("❌ 删除功能测试失败！")
        
        print("\n" + "=" * 60)
        print("🏁 测试完成")

def main():
    """主函数"""
    print("🚀 开始Server6删除功能测试")
    
    test = Server6DeleteTest()
    test.run_full_test()

if __name__ == "__main__":
    main() 