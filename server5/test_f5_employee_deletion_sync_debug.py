#!/usr/bin/env python3
"""
调试版本的f5员工删除同步测试
添加更详细的信息来帮助分析数据逻辑问题
"""

import asyncio
import logging
from datetime import date
import sys
from pathlib import Path

# 添加项目路径
sys.path.append(str(Path(__file__).parent))

from app.services.f5_bulk_sync import DeletionSyncService

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

class F5EmployeeDeletionSyncDebugTest:
    """f5员工删除同步调试测试类"""
    
    def __init__(self):
        self.f5_service = DeletionSyncService()
        self.test_employee_id = "215829"
        self.test_start_date = date(2025, 7, 1)
        self.test_end_date = date(2025, 7, 16)
    
    async def setup(self):
        """初始化服务"""
        logger.info("🚀 初始化f5删除同步服务...")
        
        # 启动f5服务
        success = await self.f5_service.start()
        if not success:
            logger.error("❌ f5服务启动失败")
            return False
        
        logger.info("✅ f5服务启动成功")
        return True
    
    async def cleanup(self):
        """清理资源"""
        logger.info("🔌 停止f5服务...")
        await self.f5_service.stop()
        logger.info("✅ f5服务已停止")
    
    async def debug_data_comparison(self):
        """调试数据对比"""
        logger.info("=" * 80)
        logger.info("🔍 调试数据对比分析")
        logger.info("=" * 80)
        
        try:
            # 1. 获取PostgreSQL数据
            logger.info("📊 1. 获取PostgreSQL数据...")
            pg_query = """
                SELECT id, external_id, entry_date, employee_id, source, ts
                FROM entries 
                WHERE employee_id = $1
                  AND entry_date BETWEEN $2 AND $3
                ORDER BY entry_date DESC, ts DESC
            """
            
            pg_results = await self.f5_service.imdb_client.execute_query(
                pg_query, self.test_employee_id, self.test_start_date, self.test_end_date
            )
            
            logger.info(f"📈 PostgreSQL中找到 {len(pg_results)} 条记录")
            
            # 分析PostgreSQL数据
            pg_external_ids = set()
            pg_null_external_ids = 0
            
            for record in pg_results:
                external_id = record.get('external_id')
                if external_id is not None:
                    pg_external_ids.add(external_id)
                else:
                    pg_null_external_ids += 1
            
            logger.info(f"📋 PostgreSQL分析:")
            logger.info(f"   - 总记录数: {len(pg_results)}")
            logger.info(f"   - 有external_id的记录: {len(pg_external_ids)}")
            logger.info(f"   - external_id为NULL的记录: {pg_null_external_ids}")
            
            if pg_external_ids:
                logger.info(f"   - external_id列表: {sorted(list(pg_external_ids))[:10]}...")
            
            # 2. 获取MDB数据
            logger.info("")
            logger.info("📊 2. 获取MDB数据...")
            
            mdb_records = await self.f5_service.server6_client.query_entries(
                employee_id=self.test_employee_id,
                start_date=self.test_start_date,
                end_date=self.test_end_date
            )
            
            logger.info(f"📈 MDB中找到 {len(mdb_records) if mdb_records else 0} 条记录")
            
            # 分析MDB数据
            mdb_ids = set()
            mdb_null_ids = 0
            
            if mdb_records:
                for record in mdb_records:
                    mdb_id = record.get('ID')
                    if mdb_id is not None:
                        mdb_ids.add(mdb_id)
                    else:
                        mdb_null_ids += 1
                
                logger.info(f"📋 MDB分析:")
                logger.info(f"   - 总记录数: {len(mdb_records)}")
                logger.info(f"   - 有ID的记录: {len(mdb_ids)}")
                logger.info(f"   - ID为NULL的记录: {mdb_null_ids}")
                
                if mdb_ids:
                    logger.info(f"   - ID列表: {sorted(list(mdb_ids))[:10]}...")
                
                # 显示MDB记录的详细信息
                logger.info("📋 MDB记录详情:")
                for i, record in enumerate(mdb_records[:5], 1):
                    logger.info(f"   {i}. 完整记录: {record}")
            else:
                logger.warning("⚠️ MDB中没有找到任何记录")
            
            # 3. 数据对比分析
            logger.info("")
            logger.info("📊 3. 数据对比分析...")
            
            if pg_external_ids and mdb_ids:
                # 找出在PostgreSQL中存在但在MDB中不存在的ID
                ids_to_delete = pg_external_ids - mdb_ids
                # 找出在MDB中存在但在PostgreSQL中不存在的ID
                ids_to_add = mdb_ids - pg_external_ids
                # 找出两者都存在的ID
                ids_common = pg_external_ids & mdb_ids
                
                logger.info(f"📋 对比结果:")
                logger.info(f"   - 共同存在的ID: {len(ids_common)} 个")
                logger.info(f"   - 只在PostgreSQL中存在的ID: {len(ids_to_delete)} 个")
                logger.info(f"   - 只在MDB中存在的ID: {len(ids_to_add)} 个")
                
                if ids_common:
                    logger.info(f"   - 共同ID列表: {sorted(list(ids_common))[:10]}...")
                
                if ids_to_delete:
                    logger.info(f"   - 需要删除的ID列表: {sorted(list(ids_to_delete))[:10]}...")
                
                if ids_to_add:
                    logger.info(f"   - 需要添加的ID列表: {sorted(list(ids_to_add))[:10]}...")
                
                # 分析删除原因
                logger.info("")
                logger.info("🔍 删除原因分析:")
                if len(ids_to_delete) > 0:
                    logger.warning(f"⚠️ 发现 {len(ids_to_delete)} 条记录需要删除")
                    logger.info("   可能原因:")
                    logger.info("   1. 这些记录在MDB中已被手动删除")
                    logger.info("   2. 这些记录从未同步到MDB")
                    logger.info("   3. 同步过程中出现错误")
                else:
                    logger.info("✅ 没有需要删除的记录")
                
            elif not pg_external_ids:
                logger.warning("⚠️ PostgreSQL中没有有效的external_id")
                logger.info("   可能原因:")
                logger.info("   1. 这些记录从未同步到MDB")
                logger.info("   2. external_id字段为NULL")
                
            elif not mdb_ids:
                logger.warning("⚠️ MDB中没有找到任何记录")
                logger.info("   可能原因:")
                logger.info("   1. 指定时间范围内没有数据")
                logger.info("   2. 员工ID不正确")
                logger.info("   3. MDB连接问题")
            
        except Exception as e:
            logger.error(f"❌ 调试数据对比失败: {e}", exc_info=True)
    
    async def run_debug_test(self):
        """运行调试测试"""
        try:
            # 初始化
            if not await self.setup():
                return False
            
            # 执行调试分析
            await self.debug_data_comparison()
            
            logger.info("")
            logger.info("🎉 调试分析完成！")
            return True
            
        except Exception as e:
            logger.error(f"❌ 调试测试执行失败: {e}", exc_info=True)
            return False
        finally:
            # 清理资源
            await self.cleanup()

async def main():
    """主函数"""
    logger.info("🚀 开始f5员工删除同步调试测试")
    logger.info("=" * 80)
    
    # 创建测试实例
    test = F5EmployeeDeletionSyncDebugTest()
    
    # 运行调试测试
    success = await test.run_debug_test()
    
    if success:
        logger.info("✅ 调试分析完成")
    else:
        logger.error("❌ 调试分析失败")
    
    logger.info("=" * 80)
    logger.info("🏁 调试测试结束")

if __name__ == "__main__":
    asyncio.run(main()) 