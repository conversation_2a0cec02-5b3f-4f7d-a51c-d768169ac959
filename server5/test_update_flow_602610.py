import asyncio
import asyncpg
from datetime import datetime

# --- Configuration ---
# Taken from server5/config/config_ubuntu_remote.py
PG_DATABASE_URL = "***************************************************/imdb"
TARGET_EXTERNAL_ID = 602631

# New data for the update based on your request
# Note: Empty strings are converted to NULL for text fields
UPDATE_DATA = {
    "employee_id": "215829",
    "entry_date": datetime.strptime("2025/06/26", "%Y/%m/%d").date(),
    "model": None,
    "number": None,
    "factory_number": None,
    "project_number": "24585",
    "unit_number": None,
    "category": 3,
    "item": 7,
    "duration": 5,  # Target duration value
    "department": "131"
}

async def run_update_test():
    """Connects to the database, runs the update, and checks the queue."""
    conn = None
    try:
        print("--- Update Test Started ---")
        conn = await asyncpg.connect(PG_DATABASE_URL)
        print(f"✅ Connected to the database.")

        # 1. Show the record *before* the update
        print(f"\n1. Fetching current state for external_id = {TARGET_EXTERNAL_ID}...")
        original_record = await conn.fetchrow(
            "SELECT id, entry_date, duration, project_number FROM public.entries WHERE external_id = $1",
            TARGET_EXTERNAL_ID
        )
        if not original_record:
            print(f"❌ ERROR: Record with external_id = {TARGET_EXTERNAL_ID} not found in the 'imdb' database.")
            return

        print("   Current Data ->", dict(original_record))
        original_entry_id = original_record['id']

        # 2. Execute the UPDATE statement
        print(f"\n2. Executing UPDATE for external_id = {TARGET_EXTERNAL_ID}...")
        
        # Fixed SQL statement based on db_utils2.py format
        # Handle NULL values properly for text fields
        sql = """
            UPDATE public.entries
            SET
                employee_id = $1,
                entry_date = $2,
                model = $3,
                number = $4,
                factory_number = $5,
                project_number = $6,
                unit_number = $7,
                category = $8,
                item = $9,
                duration = $10,
                department = $11,
                source = 'user',
                ts = NOW()
            WHERE external_id = $12
            RETURNING id, external_id, duration;
        """
        
        # Prepare parameters with proper NULL handling
        params = [
            UPDATE_DATA["employee_id"],
            UPDATE_DATA["entry_date"],
            UPDATE_DATA["model"],  # Will be None (NULL)
            UPDATE_DATA["number"],  # Will be None (NULL)
            UPDATE_DATA["factory_number"],  # Will be None (NULL)
            UPDATE_DATA["project_number"],
            UPDATE_DATA["unit_number"],  # Will be None (NULL)
            UPDATE_DATA["category"],
            UPDATE_DATA["item"],
            UPDATE_DATA["duration"],
            UPDATE_DATA["department"],
            TARGET_EXTERNAL_ID
        ]
        
        updated_record = await conn.fetchrow(sql, *params)

        if updated_record:
            print(f"   ✅ UPDATE statement executed successfully. PG `entries.id` is {updated_record['id']}.")
            print(f"   New Duration: {updated_record['duration']}")
        else:
            print("   ❌ UPDATE statement failed to return the updated record.")
            return

        # 3. Check the push queue immediately after
        print("\n3. Checking 'entries_push_queue' for the new 'UPDATE' task...")
        # Give the trigger a moment
        await asyncio.sleep(1) 
        
        queue_job = await conn.fetchrow(
            """
            SELECT queue_id, operation, synced, created_ts 
            FROM public.entries_push_queue 
            WHERE entry_id = $1 AND operation = 'UPDATE'
            ORDER BY created_ts DESC
            LIMIT 1
            """,
            original_entry_id
        )

        if queue_job:
            print(f"   ✅ SUCCESS: Trigger fired and created a job in the queue.")
            print("   Queue Job ->", dict(queue_job))
        else:
            print(f"   ❌ FAILURE: No 'UPDATE' job found in the queue for entries.id = {original_entry_id}.")
            print("   This suggests the trigger might not be firing correctly on UPDATE.")
            return

        # 4. Wait for F2 to process the job
        wait_time = 10
        print(f"\n4. Waiting for {wait_time} seconds for the F2 service to process the job...")
        for i in range(wait_time):
            print(f"   ... {wait_time - i}", end='\r')
            await asyncio.sleep(1)
        print("\n   Wait complete. Checking sync status.")

        # 5. Check the queue again for the sync status
        final_queue_job = await conn.fetchrow(
            "SELECT synced FROM public.entries_push_queue WHERE queue_id = $1",
            queue_job['queue_id']
        )

        print("\n5. Final sync status check:")
        if final_queue_job and final_queue_job['synced']:
            print(f"   ✅✅✅ SUCCESS! The job (queue_id={queue_job['queue_id']}) was processed and marked as 'synced = TRUE'.")
            print("   The update should be reflected in the MDB database.")
        elif final_queue_job:
            print(f"   ❌❌❌ FAILURE: The job is still in the queue with 'synced = FALSE'.")
            print("   This indicates a problem with the F2 Push Writer or the Server6 API.")
            print("   Check the logs for 'f2_push_writer_fixed.py' on server5 and 'server6.log' on the Windows machine for errors.")
        else:
            print("   ❌ ERROR: The queue job seems to have disappeared, which is unexpected.")

    except asyncpg.exceptions.PostgresError as db_err:
        print(f"\n❌ A database error occurred: {db_err}")
    except Exception as e:
        print(f"\n❌ An unexpected error occurred: {e}")
    finally:
        if conn:
            await conn.close()
            print("\n--- Test Finished. Database connection closed. ---")


if __name__ == "__main__":
    # Ensure you are running this from a machine that can access the database IP.
    # The F2 service should be running on server5 to process the queue.
    asyncio.run(run_update_test()) 