#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试f2推送回写引擎的最终修复版本
使用真实员工ID和正确的数据类型
"""

import asyncio
import sys
import os
from datetime import datetime, date
from pathlib import Path

# 添加项目根目录到Python路径
sys.path.append(str(Path(__file__).parent.parent))

from app.database import IMDBClient
from app.services.f2_push_writer import PushWriterService
from app.utils.server6_client import Server6Client

async def test_f2_insert_with_real_employee():
    """测试使用真实员工ID的完整插入工作流程"""
    
    print("🧪 开始测试f2推送回写引擎 - 最终修复版本")
    print("=" * 60)
    
    # 初始化客户端
    imdb_client = IMDBClient()
    server6_client = Server6Client()
    
    try:
        # 连接数据库
        print("📡 连接数据库...")
        await imdb_client.connect()
        print("✅ PostgreSQL连接成功")
        
        # 测试Server6连接
        print("🔗 测试Server6连接...")
        status = await server6_client.get_status()
        print(f"✅ Server6状态: {status}")
        
        # 使用真实的员工ID (从之前的测试中确认存在的)
        real_employee_id = "215829"  # 真实员工ID
        
        # 准备测试数据 - 确保数据类型正确
        test_data = {
            'employee_id': real_employee_id,
            'entry_date': date(2025, 1, 27),  # 今天
            'model': 'TEST_MODEL_FIXED',
            'number': 'TEST_NUMBER_FIXED',
            'factory_number': 'TEST_FACTORY_FIXED',
            'project_number': 'TEST_PROJECT_FIXED',
            'unit_number': 'TEST_UNIT_FIXED',
            'category': 3,  # 确保为int
            'item': 7,      # 确保为int
            'duration': 2.5,  # 确保为float
            'department': 'TEST_DEPT_FIXED'
        }
        
        print(f"📝 测试数据:")
        for key, value in test_data.items():
            print(f"   {key}: {value} (类型: {type(value).__name__})")
        
        # 1. 插入到PostgreSQL
        print("\n1️⃣ 插入到PostgreSQL...")
        entry_id = await imdb_client.create_entry(test_data)
        print(f"✅ PostgreSQL插入成功: entry_id = {entry_id}")
        
        # 2. 检查队列项是否创建
        print("\n2️⃣ 检查队列项...")
        queue_items = await imdb_client.get_queue_items(synced=False, limit=5)
        print(f"📋 未同步队列项数量: {len(queue_items)}")
        
        if queue_items:
            target_item = None
            for item in queue_items:
                if item.get('entry_id') == entry_id:
                    target_item = item
                    break
            
            if target_item:
                print(f"✅ 找到目标队列项: queue_id = {target_item['queue_id']}")
                print(f"   操作类型: {target_item['operation']}")
                print(f"   队列项完整内容: {target_item}")
            else:
                print("❌ 未找到目标队列项")
                return False
        else:
            print("❌ 没有未同步的队列项")
            return False
        
        # 3. 启动f2服务
        print("\n3️⃣ 启动f2推送回写服务...")
        f2_service = PushWriterService()
        start_success = await f2_service.start()
        
        if not start_success:
            print("❌ f2服务启动失败")
            return False
        
        print("✅ f2服务启动成功")
        
        # 4. 等待处理
        print("\n4️⃣ 等待f2服务处理队列...")
        max_wait = 30  # 最多等待30秒
        wait_count = 0
        
        while wait_count < max_wait:
            # 检查队列项状态
            updated_queue_items = await imdb_client.get_queue_items(synced=True, limit=10)
            target_synced = None
            for item in updated_queue_items:
                if item.get('entry_id') == entry_id:
                    target_synced = item
                    break
            
            if target_synced:
                print(f"✅ 队列项已同步: entry_id={target_synced['entry_id']}")
                break
            
            print(f"⏳ 等待中... ({wait_count + 1}/{max_wait})")
            await asyncio.sleep(1)
            wait_count += 1
        
        if wait_count >= max_wait:
            print("❌ 等待超时，f2服务可能未正确处理")
            return False
        
        # 5. 验证结果
        print("\n5️⃣ 验证最终结果...")
        
        # 检查PostgreSQL中的记录 - 通过队列项获取
        synced_items = await imdb_client.get_queue_items(synced=True, limit=100)
        final_entry = None
        for item in synced_items:
            if item.get('entry_id') == entry_id:
                final_entry = item
                break
        
        if final_entry and final_entry.get('external_id'):
            print(f"✅ PostgreSQL记录验证成功:")
            print(f"   entry_id: {final_entry['entry_id']}")
            print(f"   external_id: {final_entry['external_id']}")
            print(f"   employee_id: {final_entry['employee_id']}")
            print(f"   category: {final_entry['category']} (类型: {type(final_entry['category']).__name__})")
            print(f"   item: {final_entry['item']} (类型: {type(final_entry['item']).__name__})")
            print(f"   duration: {final_entry['duration']} (类型: {type(final_entry['duration']).__name__})")
        else:
            print("❌ PostgreSQL记录验证失败")
            return False
        
        # 检查MDB中的记录 (通过Server6)
        print("\n6️⃣ 验证MDB中的记录...")
        try:
            # 查询今天该员工的记录
            query_data = {
                'employee_id': real_employee_id,
                'start_date': date(2025, 1, 27),
                'end_date': date(2025, 1, 27)
            }
            
            mdb_response = await server6_client.query_entries(query_data)
            
            if mdb_response.get('success') and mdb_response.get('data'):
                mdb_records = mdb_response['data']
                print(f"✅ MDB查询成功，找到 {len(mdb_records)} 条记录")
                
                # 查找我们的测试记录
                test_record = None
                for record in mdb_records:
                    if (record.get('model') == 'TEST_MODEL_FIXED' and 
                        record.get('number') == 'TEST_NUMBER_FIXED'):
                        test_record = record
                        break
                
                if test_record:
                    print(f"✅ 找到MDB测试记录:")
                    print(f"   external_id: {test_record.get('external_id')}")
                    print(f"   employee_id: {test_record.get('employee_id')}")
                    print(f"   category: {test_record.get('category')} (类型: {type(test_record.get('category')).__name__})")
                    print(f"   item: {test_record.get('item')} (类型: {type(test_record.get('item')).__name__})")
                    print(f"   duration: {test_record.get('duration')} (类型: {type(test_record.get('duration')).__name__})")
                    
                    # 验证external_id匹配
                    if test_record.get('external_id') == final_entry.get('external_id'):
                        print("✅ external_id匹配成功！")
                    else:
                        print(f"❌ external_id不匹配: MDB={test_record.get('external_id')}, PG={final_entry.get('external_id')}")
                        return False
                else:
                    print("❌ 未找到MDB测试记录")
                    return False
            else:
                print(f"❌ MDB查询失败: {mdb_response.get('message', '未知错误')}")
                return False
                
        except Exception as e:
            print(f"❌ MDB验证异常: {e}")
            return False
        
        # 6. 清理测试数据
        print("\n7️⃣ 清理测试数据...")
        
        # 删除MDB记录
        if final_entry.get('external_id'):
            delete_response = await server6_client.delete_entry(final_entry['external_id'])
            if delete_response.get('success'):
                print("✅ MDB记录删除成功")
            else:
                print(f"❌ MDB记录删除失败: {delete_response.get('message')}")
        
        # 清理PostgreSQL记录 - 使用SQL直接删除
        try:
            await imdb_client.execute_command("DELETE FROM entries WHERE id = $1", entry_id)
            print("✅ PostgreSQL记录删除成功")
        except Exception as e:
            print(f"❌ PostgreSQL记录删除失败: {e}")
        
        print("\n🎉 测试完成！f2推送回写引擎工作正常")
        return True
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False
        
    finally:
        # 清理资源
        try:
            await imdb_client.disconnect()
            await server6_client.disconnect()
        except:
            pass

if __name__ == "__main__":
    asyncio.run(test_f2_insert_with_real_employee()) 