#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试实际的删除API响应
"""

import requests
import json

def test_actual_delete_response():
    """测试实际的删除API响应"""
    
    # 测试一个存在的记录
    test_external_id = 603639  # 这个记录已经被删除了，应该返回404
    
    print(f"🧪 测试删除API响应: external_id={test_external_id}")
    print("=" * 60)
    
    try:
        # 调用Server5删除API
        url = f"http://localhost:8009/api/entries/{test_external_id}"
        print(f"📡 请求URL: {url}")
        
        response = requests.delete(url, timeout=10)
        
        print(f"📊 HTTP状态码: {response.status_code}")
        print(f"📋 响应头: {dict(response.headers)}")
        
        # 尝试解析响应体
        try:
            response_json = response.json()
            print(f"📦 JSON响应: {json.dumps(response_json, indent=2, ensure_ascii=False)}")
        except json.JSONDecodeError:
            response_text = response.text
            print(f"📄 文本响应: {response_text}")
        
        # 模拟客户端响应处理
        print(f"\n🔍 模拟客户端响应处理:")
        print("-" * 40)
        
        if response.status_code == 200:
            try:
                response_data = response.json()
                # 检查响应格式
                if response_data.get("message") and "删除成功" in response_data.get("message", ""):
                    print("✅ 客户端处理: 删除操作成功")
                else:
                    error_detail = response_data.get('detail', response_data.get('message', response_data.get('status_code', '未知错误')))
                    error_msg = f"削除操作失败: {error_detail}"
                    print(f"❌ 客户端处理: {error_msg}")
            except json.JSONDecodeError:
                print("❌ 客户端处理: 响应不是有效的JSON格式")
        elif response.status_code == 404:
            print("❌ 客户端处理: 记录不存在 (404)")
        else:
            print(f"❌ 客户端处理: HTTP错误 {response.status_code}")
            
    except requests.exceptions.RequestException as e:
        print(f"❌ 请求异常: {e}")
    except Exception as e:
        print(f"❌ 其他异常: {e}")

def test_delete_nonexistent_record():
    """测试删除不存在的记录"""
    
    print(f"\n🧪 测试删除不存在的记录")
    print("=" * 60)
    
    try:
        # 使用一个不存在的ID
        nonexistent_id = 999999
        url = f"http://localhost:8009/api/entries/{nonexistent_id}"
        print(f"📡 请求URL: {url}")
        
        response = requests.delete(url, timeout=10)
        
        print(f"📊 HTTP状态码: {response.status_code}")
        
        try:
            response_json = response.json()
            print(f"📦 JSON响应: {json.dumps(response_json, indent=2, ensure_ascii=False)}")
        except json.JSONDecodeError:
            response_text = response.text
            print(f"📄 文本响应: {response_text}")
            
    except requests.exceptions.RequestException as e:
        print(f"❌ 请求异常: {e}")
    except Exception as e:
        print(f"❌ 其他异常: {e}")

if __name__ == "__main__":
    test_actual_delete_response()
    test_delete_nonexistent_record() 