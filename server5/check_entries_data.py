#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
检查entries表中的数据，特别是category和item字段的默认值
"""

import asyncio
import sys
from pathlib import Path
sys.path.append(str(Path(__file__).parent))

from app.database import IMDBClient

async def check_entries_data():
    client = IMDBClient()
    await client.connect()
    
    # 检查最近的entries数据
    entries = await client.execute_query('''
        SELECT id, employee_id, model, category, item, source, external_id, ts
        FROM entries 
        WHERE employee_id = '215829'
        ORDER BY ts DESC 
        LIMIT 10
    ''')
    
    print('最近的entries数据:')
    for entry in entries:
        print(f'  id={entry["id"]}, model={entry["model"]}, category={entry["category"]}, item={entry["item"]}, source={entry["source"]}')
    
    # 检查category和item字段的分布
    print('\ncategory字段分布:')
    category_dist = await client.execute_query('''
        SELECT category, COUNT(*) as count
        FROM entries 
        WHERE employee_id = '215829'
        GROUP BY category
        ORDER BY count DESC
    ''')
    
    for row in category_dist:
        print(f'  category={row["category"]}: {row["count"]}条记录')
    
    print('\nitem字段分布:')
    item_dist = await client.execute_query('''
        SELECT item, COUNT(*) as count
        FROM entries 
        WHERE employee_id = '215829'
        GROUP BY item
        ORDER BY count DESC
    ''')
    
    for row in item_dist:
        print(f'  item={row["item"]}: {row["count"]}条记录')
    
    await client.disconnect()

if __name__ == "__main__":
    asyncio.run(check_entries_data()) 