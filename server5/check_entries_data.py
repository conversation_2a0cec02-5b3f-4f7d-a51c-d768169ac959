#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
检查entries表中的数据
"""

import asyncio
import sys
from pathlib import Path
import logging

# 添加项目路径
sys.path.append(str(Path(__file__).parent))

from app.database import IMDBClient

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

async def check_entries_data():
    """检查entries表中的数据"""
    imdb_client = IMDBClient()
    
    try:
        await imdb_client.connect()
        
        # 检查总记录数
        total_count = await imdb_client.fetch_one("SELECT COUNT(*) as count FROM entries")
        logger.info(f"📊 entries表总记录数: {total_count['count']}")
        
        # 检查员工215829的记录
        employee_count = await imdb_client.fetch_one(
            "SELECT COUNT(*) as count FROM entries WHERE employee_id = $1",
            '215829'
        )
        logger.info(f"📊 员工215829的记录数: {employee_count['count']}")
        
        # 获取员工215829的所有记录
        employee_records = await imdb_client.execute_query(
            "SELECT id, employee_id, entry_date, external_id, source, ts FROM entries WHERE employee_id = $1 ORDER BY ts DESC",
            '215829'
        )
        
        logger.info(f"📋 员工215829的记录详情:")
        for i, record in enumerate(employee_records, 1):
            logger.info(f"  {i}. ID={record['id']}, 日期={record['entry_date']}, external_id={record['external_id']}, source={record['source']}, ts={record['ts']}")
        
        # 检查队列项
        queue_count = await imdb_client.fetch_one("SELECT COUNT(*) as count FROM entries_push_queue")
        logger.info(f"📊 entries_push_queue表总记录数: {queue_count['count']}")
        
        # 获取队列项详情
        queue_items = await imdb_client.execute_query(
            "SELECT queue_id, entry_id, operation, synced, created_ts FROM entries_push_queue ORDER BY created_ts DESC LIMIT 10"
        )
        
        logger.info(f"📋 最近10个队列项详情:")
        for i, item in enumerate(queue_items, 1):
            logger.info(f"  {i}. queue_id={item['queue_id']}, entry_id={item['entry_id']}, operation={item['operation']}, synced={item['synced']}, created_ts={item['created_ts']}")
        
    except Exception as e:
        logger.error(f"❌ 检查数据失败: {e}")
    finally:
        await imdb_client.disconnect()

async def main():
    """主函数"""
    await check_entries_data()

if __name__ == "__main__":
    asyncio.run(main()) 