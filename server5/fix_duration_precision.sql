-- №02025/06/27 + 日本东京时间17：30 + Duration精度修复SQL脚本
-- 修复PostgreSQL中duration字段的精度问题
-- 将NUMERIC改为NUMERIC(10,2)以限制小数位数为2位

-- 1. 修复staging_entries表的duration字段精度
ALTER TABLE staging_entries 
ALTER COLUMN duration TYPE NUMERIC(10,2);

-- 2. 修复entries表的duration字段精度
ALTER TABLE entries 
ALTER COLUMN duration TYPE NUMERIC(10,2);

-- 3. 修复所有分区表的duration字段精度
-- 注意：分区表会自动继承父表的列定义，但现有数据可能需要重新处理

-- 4. 验证修复结果
-- 检查staging_entries表的列定义
SELECT 
    column_name, 
    data_type, 
    numeric_precision, 
    numeric_scale
FROM information_schema.columns 
WHERE table_name = 'staging_entries' 
  AND column_name = 'duration';

-- 检查entries表的列定义
SELECT 
    column_name, 
    data_type, 
    numeric_precision, 
    numeric_scale
FROM information_schema.columns 
WHERE table_name = 'entries' 
  AND column_name = 'duration';

-- 5. 清理现有数据中的精度问题（可选）
-- 将现有的duration值四舍五入到2位小数
UPDATE staging_entries 
SET duration = ROUND(duration, 2) 
WHERE duration IS NOT NULL;

UPDATE entries 
SET duration = ROUND(duration, 2) 
WHERE duration IS NOT NULL;

-- 6. 验证数据修复结果
SELECT 
    'staging_entries' as table_name,
    COUNT(*) as total_records,
    COUNT(CASE WHEN duration IS NOT NULL THEN 1 END) as non_null_duration,
    COUNT(CASE WHEN duration = ROUND(duration, 2) THEN 1 END) as properly_rounded
FROM staging_entries
UNION ALL
SELECT 
    'entries' as table_name,
    COUNT(*) as total_records,
    COUNT(CASE WHEN duration IS NOT NULL THEN 1 END) as non_null_duration,
    COUNT(CASE WHEN duration = ROUND(duration, 2) THEN 1 END) as properly_rounded
FROM entries; 