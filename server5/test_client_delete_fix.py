#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试客户端删除功能修复
验证Server5 entries API删除功能是否正常工作
"""

import requests
import json
import time
from datetime import datetime

# Server5配置
SERVER5_BASE_URL = "http://localhost:8009"
ENTRIES_API_BASE = f"{SERVER5_BASE_URL}/api/entries"

def test_server5_health():
    """测试Server5健康状态"""
    try:
        # 直接测试entries API作为健康检查
        response = requests.get(f"{ENTRIES_API_BASE}/?limit=1", timeout=5)
        if response.status_code == 200:
            print("✅ Server5健康检查成功")
            return True
        else:
            print(f"❌ Server5健康检查失败: {response.status_code}")
            return False
    except Exception as e:
        print(f"❌ Server5连接失败: {e}")
        return False

def test_entries_api():
    """测试entries API基本功能"""
    try:
        # 获取entries列表
        response = requests.get(f"{ENTRIES_API_BASE}/?employee_id=215829&limit=5", timeout=10)
        if response.status_code == 200:
            entries = response.json()
            print(f"✅ Entries API查询成功: {len(entries)} 条记录")
            return entries
        else:
            print(f"❌ Entries API查询失败: {response.status_code}")
            return []
    except Exception as e:
        print(f"❌ Entries API调用异常: {e}")
        return []

def test_delete_api(entry_id):
    """测试删除API"""
    try:
        print(f"🗑️ 测试删除 entry_id={entry_id}")
        
        # 直接执行删除（Server5没有单个记录的GET端点）
        delete_response = requests.delete(f"{ENTRIES_API_BASE}/{entry_id}", timeout=10)
        
        if delete_response.status_code == 200:
            result = delete_response.json()
            print(f"✅ 删除成功: {result}")
            return True
        elif delete_response.status_code == 404:
            print(f"❌ 记录不存在: entry_id={entry_id}")
            return False
        else:
            print(f"❌ 删除失败: {delete_response.status_code} - {delete_response.text}")
            return False
            
    except Exception as e:
        print(f"❌ 删除API调用异常: {e}")
        return False

def test_client_delete_simulation():
    """模拟客户端删除操作"""
    try:
        print("\n🔧 模拟客户端删除操作...")
        
        # 1. 获取可删除的记录
        entries = test_entries_api()
        if not entries:
            print("❌ 没有可用的记录进行删除测试")
            return False
        
        # 选择第一条记录进行删除测试
        test_entry = entries[0]
        entry_id = test_entry.get('id')
        
        if not entry_id:
            print("❌ 无法获取记录的ID")
            return False
        
        print(f"📋 选择测试记录: entry_id={entry_id}")
        
        # 2. 模拟客户端删除请求
        req_id = f"delete_progress_{int(time.time())}"
        print(f"🆔 请求ID: {req_id}")
        
        # 3. 执行删除
        success = test_delete_api(entry_id)
        
        if success:
            print("✅ 客户端删除模拟成功")
            return True
        else:
            print("❌ 客户端删除模拟失败")
            return False
            
    except Exception as e:
        print(f"❌ 客户端删除模拟异常: {e}")
        return False

def main():
    """主测试函数"""
    print("🚀 开始测试客户端删除功能修复")
    print("=" * 50)
    
    # 1. 检查Server5健康状态
    if not test_server5_health():
        print("❌ Server5不可用，测试终止")
        return
    
    # 2. 测试entries API
    entries = test_entries_api()
    if not entries:
        print("❌ Entries API不可用，测试终止")
        return
    
    # 3. 模拟客户端删除操作
    success = test_client_delete_simulation()
    
    print("\n" + "=" * 50)
    if success:
        print("🎉 客户端删除功能修复测试成功")
    else:
        print("💥 客户端删除功能修复测试失败")
    
    print(f"📅 测试完成时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")

if __name__ == "__main__":
    main() 