#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
№02025/06/27 + 应用触发器修复脚本
解决随机数据插入问题 - 只在用户操作时才触发队列
"""

import asyncio
import logging
import sys
from pathlib import Path

# 添加项目路径
sys.path.append(str(Path(__file__).parent))

from app.database import IMDBClient

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

async def apply_trigger_fix():
    """应用触发器修复"""
    imdb_client = IMDBClient()
    
    try:
        await imdb_client.connect()
        logger.info("✅ 数据库连接成功")
        
        # 1. 添加source字段
        logger.info("🔧 步骤1: 添加source字段到entries表...")
        await imdb_client.execute_command("""
            ALTER TABLE entries ADD COLUMN IF NOT EXISTS source VARCHAR(20) DEFAULT 'system';
        """)
        
        # 2. 为现有数据设置source值
        logger.info("🔧 步骤2: 为现有数据设置source值...")
        result = await imdb_client.execute_command("""
            UPDATE entries SET source = 'system' WHERE source IS NULL;
        """)
        logger.info(f"✅ 更新了 {result} 条记录")
        
        # 3. 修改触发器函数
        logger.info("🔧 步骤3: 修改触发器函数...")
        await imdb_client.execute_command("""
            CREATE OR REPLACE FUNCTION trg_entries_enqueue() RETURNS TRIGGER AS $$
            DECLARE q BIGINT;
            BEGIN
                -- 只在source为'user'时才触发队列
                IF (TG_OP='INSERT' AND NEW.source = 'user') THEN
                    INSERT INTO entries_push_queue(entry_id, operation)
                    VALUES (NEW.id,'INSERT') RETURNING queue_id INTO q;
                    PERFORM pg_notify('push_job', q::text);
                ELSIF (TG_OP='UPDATE' AND NEW.source = 'user') THEN
                    INSERT INTO entries_push_queue(entry_id, external_id, operation)
                    VALUES (NEW.id, NEW.external_id,'UPDATE') RETURNING queue_id INTO q;
                    PERFORM pg_notify('push_job', q::text);
                ELSIF (TG_OP='DELETE' AND OLD.source = 'user') THEN
                    INSERT INTO entries_push_queue(entry_id, external_id, operation)
                    VALUES (OLD.id, OLD.external_id,'DELETE') RETURNING queue_id INTO q;
                    PERFORM pg_notify('push_job', q::text);
                END IF;
                
                RETURN NULL;
            END;
            $$ LANGUAGE plpgsql;
        """)
        
        # 4. 重新创建触发器
        logger.info("🔧 步骤4: 重新创建触发器...")
        await imdb_client.execute_command("""
            DROP TRIGGER IF EXISTS trg_entries_enqueue ON public.entries;
        """)
        await imdb_client.execute_command("""
            CREATE TRIGGER trg_entries_enqueue
            AFTER INSERT OR UPDATE OR DELETE ON public.entries
            FOR EACH ROW EXECUTE FUNCTION trg_entries_enqueue();
        """)
        
        # 5. 验证修改结果
        logger.info("🔍 步骤5: 验证修改结果...")
        
        # 检查source字段分布
        source_stats = await imdb_client.execute_query("""
            SELECT 
                source,
                COUNT(*) as count
            FROM entries 
            GROUP BY source
            ORDER BY source;
        """)
        
        logger.info("📊 entries表的source字段分布:")
        for stat in source_stats:
            logger.info(f"   {stat['source']}: {stat['count']} 条记录")
        
        # 检查触发器状态
        trigger_info = await imdb_client.execute_query("""
            SELECT 
                trigger_name,
                event_manipulation,
                action_statement
            FROM information_schema.triggers 
            WHERE trigger_name = 'trg_entries_enqueue';
        """)
        
        if trigger_info:
            logger.info("✅ 触发器创建成功:")
            for trigger in trigger_info:
                logger.info(f"   名称: {trigger['trigger_name']}")
                logger.info(f"   事件: {trigger['event_manipulation']}")
        else:
            logger.warning("⚠️ 触发器信息未找到")
        
        logger.info("🎉 触发器修复完成！")
        logger.info("📝 现在只有source='user'的操作才会触发队列")
        
    except Exception as e:
        logger.error(f"❌ 触发器修复失败: {e}")
        raise
    finally:
        await imdb_client.disconnect()

if __name__ == "__main__":
    asyncio.run(apply_trigger_fix()) 