#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
完整数据流测试脚本
测试从UI输入到MDB同步的完整流程：
UI输入 → Server5 HTTP API → PostgreSQL entries → 触发器 → entries_push_queue → f2 → Server6 → MDB
"""

import asyncio
import sys
from pathlib import Path
import logging
from datetime import datetime, date
import asyncpg

# 添加项目路径
sys.path.append(str(Path(__file__).parent))

from app.database import IMDBClient
from app.utils.server6_client import Server6Client

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

class CompleteDataFlowTest:
    """完整数据流测试类"""
    
    def __init__(self):
        self.imdb_client = IMDBClient()
        self.server6_client = Server6Client()
        
        # 测试数据
        self.test_entry_data = {
            "entry_date": "2025/06/27",
            "employee_id": "215829",
            "duration": 8.5,
            "project_code": "TEST001",
            "status": "3",
            "description": "测试项目",
            "department": "131",
            "notes": "号机:TEST001 工场製番:SN001 工事番号:PRJ001 ユニット番号:UNIT001",
            "source": "user"
        }
    
    async def start(self):
        """启动测试环境"""
        logger.info("🚀 启动完整数据流测试...")
        
        # 连接数据库
        await self.imdb_client.connect()
        await self.server6_client.connect()
        
        logger.info("✅ 数据库连接成功")
    
    async def stop(self):
        """停止测试环境"""
        logger.info("🔌 停止测试环境...")
        
        await self.imdb_client.disconnect()
        await self.server6_client.disconnect()
        
        logger.info("✅ 测试环境已停止")
    
    async def test_step1_ui_to_postgres(self):
        """步骤1: 模拟UI输入到PostgreSQL entries表"""
        logger.info("\n📝 步骤1: 模拟UI输入到PostgreSQL entries表")
        
        try:
            # 模拟UI输入数据转换为entries格式
            entry_data = {
                "entry_date": self.test_entry_data["entry_date"].replace('/', '-'),
                "employee_id": self.test_entry_data["employee_id"],
                "duration": self.test_entry_data["duration"],
                "project_code": self.test_entry_data["project_code"],
                "status": self.test_entry_data["status"],
                "description": self.test_entry_data["description"],
                "department": self.test_entry_data["department"],
                "notes": self.test_entry_data["notes"],
                "source": "user"  # 关键：标记为用户操作
            }
            
            # 插入到entries表
            entry_id = await self.imdb_client.create_entry(entry_data, source="user")
            
            if entry_id:
                logger.info(f"✅ 成功插入entries表: entry_id={entry_id}")
                
                # 验证数据插入
                entry_record = await self.imdb_client.fetch_one(
                    "SELECT * FROM entries WHERE id = $1", entry_id
                )
                
                if entry_record:
                    logger.info(f"📋 插入的数据: {dict(entry_record)}")
                    return entry_id
                else:
                    logger.error("❌ 无法验证插入的数据")
                    return None
            else:
                logger.error("❌ entries表插入失败")
                return None
                
        except Exception as e:
            logger.error(f"❌ 步骤1失败: {e}")
            return None
    
    async def test_step2_trigger_to_queue(self):
        """步骤2: 验证触发器是否创建队列项"""
        logger.info("\n🔔 步骤2: 验证触发器是否创建队列项")
        
        try:
            # 等待触发器执行
            await asyncio.sleep(2)
            
            # 检查队列项
            queue_items = await self.imdb_client.execute_query(
                "SELECT * FROM entries_push_queue WHERE synced = FALSE ORDER BY created_ts DESC LIMIT 5"
            )
            
            if queue_items:
                logger.info(f"✅ 找到 {len(queue_items)} 个未同步的队列项")
                for item in queue_items:
                    logger.info(f"📋 队列项: {dict(item)}")
                return queue_items[0]  # 返回最新的队列项
            else:
                logger.warning("⚠️ 没有找到未同步的队列项")
                return None
                
        except Exception as e:
            logger.error(f"❌ 步骤2失败: {e}")
            return None
    
    async def test_step3_f2_processing(self):
        """步骤3: 模拟f2处理队列项"""
        logger.info("\n⚙️ 步骤3: 模拟f2处理队列项")
        
        try:
            # 获取最新的未同步队列项
            queue_item = await self.imdb_client.fetch_one(
                "SELECT * FROM entries_push_queue WHERE synced = FALSE ORDER BY created_ts DESC LIMIT 1"
            )
            
            if not queue_item:
                logger.warning("⚠️ 没有找到未同步的队列项")
                return False
            
            queue_id = queue_item['queue_id']
            entry_id = queue_item['entry_id']
            operation = queue_item['operation']
            
            logger.info(f"📋 处理队列项: queue_id={queue_id}, entry_id={entry_id}, operation={operation}")
            
            # 获取完整的entry数据
            entry_data = await self.imdb_client.fetch_one(
                "SELECT * FROM entries WHERE id = $1", entry_id
            )
            
            if not entry_data:
                logger.error(f"❌ 找不到entry记录: entry_id={entry_id}")
                return False
            
            # 准备MDB数据
            mdb_data = {
                'employee_id': entry_data['employee_id'],
                'entry_date': entry_data['entry_date'].strftime('%Y/%m/%d') if entry_data['entry_date'] else datetime.now().strftime('%Y/%m/%d'),
                'model': entry_data['model'] if entry_data['model'] else '',
                'number': entry_data['number'] if entry_data['number'] else '',
                'factory_number': entry_data['factory_number'] if entry_data['factory_number'] else '',
                'project_number': entry_data['project_number'] if entry_data['project_number'] else '',
                'unit_number': entry_data['unit_number'] if entry_data['unit_number'] else '',
                'category': entry_data['category'] if entry_data['category'] is not None else 1,
                'item': entry_data['item'] if entry_data['item'] is not None else 1,
                'duration': float(entry_data['duration']) if entry_data['duration'] is not None else 0.0,
                'department': entry_data['department'] if entry_data['department'] else ''
            }
            
            logger.info(f"📦 准备发送到Server6的数据: {mdb_data}")
            
            # 根据操作类型调用Server6
            if operation == 'INSERT':
                response = await self.server6_client.insert_entry(mdb_data)
                if response.get('success') and response.get('inserted_id'):
                    external_id = response.get('inserted_id')
                    logger.info(f"✅ Server6插入成功: external_id={external_id}")
                    
                    # 更新entries表的external_id
                    await self.imdb_client.update_external_id_system(entry_id, external_id)
                    
                    # 标记队列项为已同步
                    await self.imdb_client.execute_command(
                        "UPDATE entries_push_queue SET synced = TRUE WHERE queue_id = $1",
                        queue_id
                    )
                    
                    logger.info(f"✅ 队列项处理完成: queue_id={queue_id}")
                    return True
                else:
                    logger.error(f"❌ Server6插入失败: {response}")
                    return False
                    
            elif operation == 'UPDATE':
                external_id = queue_item.get('external_id')
                if not external_id:
                    logger.error("❌ UPDATE操作缺少external_id")
                    return False
                
                response = await self.server6_client.update_entry(external_id, mdb_data)
                if response.get('success'):
                    logger.info(f"✅ Server6更新成功: external_id={external_id}")
                    
                    # 标记队列项为已同步
                    await self.imdb_client.execute_command(
                        "UPDATE entries_push_queue SET synced = TRUE WHERE queue_id = $1",
                        queue_id
                    )
                    
                    logger.info(f"✅ 队列项处理完成: queue_id={queue_id}")
                    return True
                else:
                    logger.error(f"❌ Server6更新失败: {response}")
                    return False
            
            else:
                logger.error(f"❌ 不支持的操作类型: {operation}")
                return False
                
        except Exception as e:
            logger.error(f"❌ 步骤3失败: {e}")
            return False
    
    async def test_step4_verify_mdb_sync(self):
        """步骤4: 验证MDB同步结果"""
        logger.info("\n🔍 步骤4: 验证MDB同步结果")
        
        try:
            # 等待同步完成
            await asyncio.sleep(3)
            
            # 检查队列项状态
            synced_items = await self.imdb_client.execute_query(
                "SELECT * FROM entries_push_queue WHERE synced = TRUE ORDER BY created_ts DESC LIMIT 5"
            )
            
            if synced_items:
                logger.info(f"✅ 找到 {len(synced_items)} 个已同步的队列项")
                for item in synced_items:
                    logger.info(f"📋 已同步队列项: {dict(item)}")
                
                # 检查entries表的external_id
                entries_with_external_id = await self.imdb_client.execute_query(
                    "SELECT id, external_id, employee_id, entry_date FROM entries WHERE external_id IS NOT NULL ORDER BY id DESC LIMIT 5"
                )
                
                if entries_with_external_id:
                    logger.info(f"✅ 找到 {len(entries_with_external_id)} 个有external_id的entries记录")
                    for entry in entries_with_external_id:
                        logger.info(f"📋 entries记录: {dict(entry)}")
                    
                    return True
                else:
                    logger.warning("⚠️ 没有找到有external_id的entries记录")
                    return False
            else:
                logger.warning("⚠️ 没有找到已同步的队列项")
                return False
                
        except Exception as e:
            logger.error(f"❌ 步骤4失败: {e}")
            return False
    
    async def run_complete_test(self):
        """运行完整测试"""
        logger.info("🧪 开始完整数据流测试")
        logger.info("=" * 60)
        
        try:
            await self.start()
            
            # 步骤1: UI到PostgreSQL
            entry_id = await self.test_step1_ui_to_postgres()
            if not entry_id:
                logger.error("❌ 步骤1失败，测试终止")
                return False
            
            # 步骤2: 触发器到队列
            queue_item = await self.test_step2_trigger_to_queue()
            if not queue_item:
                logger.error("❌ 步骤2失败，测试终止")
                return False
            
            # 步骤3: f2处理
            f2_success = await self.test_step3_f2_processing()
            if not f2_success:
                logger.error("❌ 步骤3失败，测试终止")
                return False
            
            # 步骤4: 验证MDB同步
            sync_success = await self.test_step4_verify_mdb_sync()
            if not sync_success:
                logger.error("❌ 步骤4失败，测试终止")
                return False
            
            logger.info("\n" + "=" * 60)
            logger.info("🎉 完整数据流测试成功！")
            logger.info("✅ 所有步骤都通过了")
            logger.info("💡 数据流: UI → PostgreSQL → 触发器 → 队列 → f2 → Server6 → MDB")
            logger.info("=" * 60)
            
            return True
            
        except Exception as e:
            logger.error(f"❌ 测试过程中发生异常: {e}")
            return False
        finally:
            await self.stop()

async def main():
    """主函数"""
    test = CompleteDataFlowTest()
    success = await test.run_complete_test()
    
    if success:
        print("\n🎉 完整数据流测试通过！")
        print("💡 系统工作正常，可以处理UI到MDB的完整数据流")
    else:
        print("\n❌ 完整数据流测试失败")
        print("💡 需要检查相关组件和配置")

if __name__ == "__main__":
    asyncio.run(main()) 