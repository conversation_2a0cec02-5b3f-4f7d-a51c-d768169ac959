#!/usr/bin/env python3
"""
完整插入操作测试 - 验证从entries插入到MDB同步的完整流程
测试内容：
1. 插入真实数据到entries，验证日期格式转换（YYYY-MM-DD -> YYYY/MM/DD）
2. 验证f1监听器是否被触发并调用f2
3. 验证f2是否成功写入MDB并执行相关操作
4. 验证整个INSERT流程是否按预期执行
5. 验证f2只执行一次真实数据写入
"""

import sys
import os
import asyncio
import json
from datetime import datetime, date
import time
import logging

# 添加项目根目录到Python路径
sys.path.append(os.path.dirname(os.path.dirname(__file__)))

from app.services.f1_listener import ListenerService
from app.services.f2_push_writer import PushWriterService
from app.database.postgresql_client import IMDBClient
from app.database.redis_client import RedisClient
from app.utils.server6_client import Server6Client
from config.config import PlatformConfig

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

class InsertOperationTest:
    def __init__(self):
        self.config = PlatformConfig.get_config()
        self.redis_client = RedisClient()
        self.postgresql_client = IMDBClient()
        self.server6_client = Server6Client()
        
        # 初始化服务
        self.f1_service = ListenerService()
        self.f2_service = PushWriterService()
        
        # 真实测试数据（来自写入例子.md）
        self.test_data = {
            'employee_id': '215829',
            'date': '2025/06/26',  # MDB格式：YYYY/MM/DD
            'model': '',  # 機種
            'number': '',  # 号機
            'factory_number': '',  # 工場製番
            'project_number': '24585',  # 工事番号
            'unit_number': '',  # ﾕﾆｯﾄ番号
            'category': 3,  # 区分
            'item': 7,  # 項目
            'time': 9,  # 時間
            'department': '131'  # 所属ｺｰﾄﾞ
        }
        
        # 测试结果记录
        self.test_results = {
            'test1_entries_insert': False,
            'test2_f1_trigger': False,
            'test3_f2_mdb_write': False,
            'test4_complete_flow': False,
            'test5_single_execution': False
        }
    
    async def setup(self):
        """初始化测试环境"""
        print("🔧 初始化测试环境...")
        
        try:
            # 连接数据库
            await self.redis_client.connect()
            await self.postgresql_client.connect()
            await self.server6_client.connect()
            
            # 清理测试数据
            await self._cleanup_test_data()
            
            print("✅ 测试环境初始化完成")
            return True
            
        except Exception as e:
            print(f"❌ 测试环境初始化失败: {e}")
            return False
    
    async def cleanup(self):
        """清理测试环境"""
        print("🧹 清理测试环境...")
        
        try:
            # 停止服务
            await self.f1_service.stop()
            await self.f2_service.stop()
            
            # 清理测试数据
            await self._cleanup_test_data()
            
            # 断开连接
            await self.redis_client.disconnect()
            await self.postgresql_client.disconnect()
            await self.server6_client.disconnect()
            
            print("✅ 测试环境清理完成")
            
        except Exception as e:
            print(f"❌ 测试环境清理失败: {e}")
    
    async def _cleanup_test_data(self):
        """清理测试数据"""
        try:
            # 删除测试员工的数据
            await self.postgresql_client.execute_query(
                "DELETE FROM entries WHERE employee_id = $1",
                self.test_data['employee_id']
            )
            
            # 清理队列
            await self.postgresql_client.execute_query(
                "DELETE FROM entries_push_queue WHERE entry_id IN (SELECT id FROM entries WHERE employee_id = $1)",
                self.test_data['employee_id']
            )
            
            print("🧹 测试数据清理完成")
            
        except Exception as e:
            print(f"⚠️ 清理测试数据时出错: {e}")
    
    async def test1_entries_insert_with_date_conversion(self):
        """测试1：插入真实数据到entries，验证日期格式转换"""
        print("\n=== 测试1：entries插入和日期格式转换 ===")
        
        try:
            # 准备entries数据（PostgreSQL格式：YYYY-MM-DD）
            entry_date_pg = datetime.strptime(self.test_data['date'], '%Y/%m/%d').date()
            
            entry_data = {
                'employee_id': self.test_data['employee_id'],
                'entry_date': entry_date_pg,  # PostgreSQL格式
                'model': self.test_data['model'] or None,
                'number': self.test_data['number'] or None,
                'factory_number': self.test_data['factory_number'] or None,
                'project_number': self.test_data['project_number'] or None,
                'unit_number': self.test_data['unit_number'] or None,
                'category': self.test_data['category'],
                'item': self.test_data['item'],
                'duration': float(self.test_data['time']),
                'department': self.test_data['department']
            }
            
            print(f"📝 准备插入数据:")
            print(f"   employee_id: {entry_data['employee_id']}")
            print(f"   entry_date (PostgreSQL): {entry_data['entry_date']} (YYYY-MM-DD)")
            print(f"   project_number: {entry_data['project_number']}")
            print(f"   category: {entry_data['category']}")
            print(f"   item: {entry_data['item']}")
            print(f"   duration: {entry_data['duration']}")
            print(f"   department: {entry_data['department']}")
            
            # 记录插入前的状态
            before_entries = await self.postgresql_client.execute_query(
                "SELECT COUNT(*) as count FROM entries WHERE employee_id = $1",
                self.test_data['employee_id']
            )
            before_count = before_entries[0]['count']
            print(f"📊 插入前entries数量: {before_count}")
            
            # 插入到entries表
            entry_id = await self.postgresql_client.create_entry(entry_data, source='system')
            
            if not entry_id:
                print("❌ entries插入失败")
                return False
            
            print(f"✅ entries插入成功: entry_id={entry_id}")
            
            # 验证插入结果
            inserted_entry = await self.postgresql_client.execute_query(
                "SELECT id, external_id, employee_id, entry_date, project_number, category, item, duration, department FROM entries WHERE id = $1",
                entry_id
            )
            
            if inserted_entry:
                entry = inserted_entry[0]
                print(f"📊 插入后entries数据:")
                print(f"   id: {entry['id']}")
                print(f"   external_id: {entry['external_id']} (应该为NULL)")
                print(f"   employee_id: {entry['employee_id']}")
                print(f"   entry_date: {entry['entry_date']} (PostgreSQL格式)")
                print(f"   project_number: {entry['project_number']}")
                print(f"   category: {entry['category']}")
                print(f"   item: {entry['item']}")
                print(f"   duration: {entry['duration']}")
                print(f"   department: {entry['department']}")
                
                # 验证external_id为NULL
                if entry['external_id'] is None:
                    print("✅ external_id正确为NULL")
                else:
                    print("❌ external_id不应该有值")
                    return False
                
                # 验证日期格式
                if str(entry['entry_date']) == '2025-06-26':
                    print("✅ 日期格式正确 (YYYY-MM-DD)")
                else:
                    print(f"❌ 日期格式错误: {entry['entry_date']}")
                    return False
                
                self.test_results['test1_entries_insert'] = True
                return entry_id
            else:
                print("❌ 无法获取插入的数据")
                return False
                
        except Exception as e:
            print(f"❌ entries插入测试失败: {e}")
            return False
    
    async def test2_f1_listener_trigger(self):
        """测试2：验证f1监听器是否被触发"""
        print("\n=== 测试2：f1监听器触发验证 ===")
        
        try:
            # 启动f1监听器
            await self.f1_service.start()
            print("✅ f1监听器启动成功")
            
            # 等待监听器建立连接
            await asyncio.sleep(3)
            
            # 检查队列状态
            queue_items = await self.postgresql_client.execute_query(
                "SELECT * FROM entries_push_queue WHERE synced = FALSE ORDER BY created_ts DESC"
            )
            
            print(f"📋 未同步队列项数量: {len(queue_items)}")
            
            if queue_items:
                for item in queue_items:
                    print(f"📋 队列项: ID={item['queue_id']}, 操作={item['operation']}, entry_id={item['entry_id']}, synced={item['synced']}")
                
                # 验证队列项存在
                if len(queue_items) > 0:
                    print("✅ 队列项已创建，触发器工作正常")
                    self.test_results['test2_f1_trigger'] = True
                    return True
                else:
                    print("❌ 队列项未创建")
                    return False
            else:
                print("❌ 没有找到队列项")
                return False
                
        except Exception as e:
            print(f"❌ f1监听器测试失败: {e}")
            return False
    
    async def test3_f2_mdb_write_operation(self):
        """测试3：验证f2是否成功写入MDB并执行相关操作"""
        print("\n=== 测试3：f2 MDB写入操作验证 ===")
        
        try:
            # 启动f2推送服务
            await self.f2_service.start()
            print("✅ f2推送服务启动成功")
            
            # 等待f2服务处理队列
            print("⏳ 等待f2服务处理队列...")
            await asyncio.sleep(5)
            
            # 检查队列处理结果 - 修复查询，移除synced_at字段
            synced_items = await self.postgresql_client.execute_query(
                "SELECT * FROM entries_push_queue WHERE synced = TRUE ORDER BY created_ts DESC"
            )
            
            print(f"📋 已同步队列项数量: {len(synced_items)}")
            
            if synced_items:
                for item in synced_items:
                    print(f"📋 已同步队列项: ID={item['queue_id']}, 操作={item['operation']}, entry_id={item['entry_id']}")
                
                # 检查entries表是否更新了external_id
                entries_updated = await self.postgresql_client.execute_query(
                    "SELECT id, external_id, employee_id, entry_date FROM entries WHERE employee_id = $1 AND external_id IS NOT NULL",
                    self.test_data['employee_id']
                )
                
                if entries_updated:
                    entry = entries_updated[0]
                    print(f"📊 更新后entries数据:")
                    print(f"   id: {entry['id']}")
                    print(f"   external_id: {entry['external_id']} (MDB返回的ID)")
                    print(f"   employee_id: {entry['employee_id']}")
                    print(f"   entry_date: {entry['entry_date']}")
                    
                    if entry['external_id'] is not None:
                        print("✅ external_id已成功回写")
                        
                        # 验证MDB中的数据
                        await self._verify_mdb_data(entry['external_id'])
                        
                        self.test_results['test3_f2_mdb_write'] = True
                        return True
                    else:
                        print("❌ external_id未回写")
                        return False
                else:
                    print("❌ entries表未更新external_id")
                    return False
            else:
                print("❌ 队列项未同步")
                return False
                
        except Exception as e:
            print(f"❌ f2 MDB写入测试失败: {e}")
            return False
    
    async def _verify_mdb_data(self, external_id: int):
        """验证MDB中的数据"""
        print(f"\n🔍 验证MDB中的数据 (external_id={external_id})...")
        
        try:
            # 通过Server6查询MDB数据
            # 注意：这里需要根据实际的Server6 API来查询
            # 由于Server6的API设计，我们需要通过员工ID和日期来查询
            
            # 查询指定员工和日期的数据
            start_date = date(2025, 6, 26)
            end_date = date(2025, 6, 26)
            
            mdb_entries = await self.server6_client.query_entries(
                self.test_data['employee_id'],
                start_date,
                end_date
            )
            
            print(f"📊 MDB查询结果数量: {len(mdb_entries)}")
            
            if mdb_entries:
                mdb_entry = mdb_entries[0]  # 假设只有一条记录
                print(f"📊 MDB数据:")
                print(f"   ID: {mdb_entry.get('ID')}")
                print(f"   従業員ｺｰﾄﾞ: {mdb_entry.get('従業員ｺｰﾄﾞ')}")
                print(f"   日付: {mdb_entry.get('日付')} (MDB格式: YYYY/MM/DD)")
                print(f"   工事番号: {mdb_entry.get('工事番号')}")
                print(f"   区分: {mdb_entry.get('区分')}")
                print(f"   項目: {mdb_entry.get('項目')}")
                print(f"   時間: {mdb_entry.get('時間')}")
                print(f"   所属ｺｰﾄﾞ: {mdb_entry.get('所属ｺｰﾄﾞ')}")
                
                # 验证数据一致性
                if (mdb_entry.get('従業員ｺｰﾄﾞ') == self.test_data['employee_id'] and
                    mdb_entry.get('日付') == self.test_data['date'] and
                    mdb_entry.get('工事番号') == self.test_data['project_number'] and
                    mdb_entry.get('区分') == self.test_data['category'] and
                    mdb_entry.get('項目') == self.test_data['item'] and
                    mdb_entry.get('時間') == self.test_data['time'] and
                    mdb_entry.get('所属ｺｰﾄﾞ') == self.test_data['department']):
                    print("✅ MDB数据与测试数据一致")
                    return True
                else:
                    print("❌ MDB数据与测试数据不一致")
                    return False
            else:
                print("❌ MDB中未找到数据")
                return False
                
        except Exception as e:
            print(f"❌ 验证MDB数据失败: {e}")
            return False
    
    async def test4_complete_insert_flow(self):
        """测试4：验证完整的INSERT流程"""
        print("\n=== 测试4：完整INSERT流程验证 ===")
        
        try:
            # 检查整个流程是否按预期执行
            # 1. entries表有数据且external_id已更新
            entries_final = await self.postgresql_client.execute_query(
                "SELECT id, external_id, employee_id, entry_date FROM entries WHERE employee_id = $1",
                self.test_data['employee_id']
            )
            
            if not entries_final:
                print("❌ entries表中没有数据")
                return False
            
            entry_final = entries_final[0]
            print(f"📊 最终entries数据:")
            print(f"   id: {entry_final['id']}")
            print(f"   external_id: {entry_final['external_id']}")
            print(f"   employee_id: {entry_final['employee_id']}")
            print(f"   entry_date: {entry_final['entry_date']}")
            
            # 2. 队列项已同步
            synced_queue = await self.postgresql_client.execute_query(
                "SELECT COUNT(*) as count FROM entries_push_queue WHERE synced = TRUE AND entry_id = $1",
                entry_final['id']
            )
            
            if synced_queue[0]['count'] > 0:
                print("✅ 队列项已同步")
            else:
                print("❌ 队列项未同步")
                return False
            
            # 3. 验证流程完整性
            if (entry_final['external_id'] is not None and
                entry_final['employee_id'] == self.test_data['employee_id']):
                print("✅ 完整INSERT流程验证通过")
                print("   UI→entries (external_id=NULL) → 触发器入队+NOTIFY → worker 插 MDB → 取 external_id → UPDATE entries.external_id → synced=TRUE → 运行一次专属id同步函数 f6")
                self.test_results['test4_complete_flow'] = True
                return True
            else:
                print("❌ 完整INSERT流程验证失败")
                return False
                
        except Exception as e:
            print(f"❌ 完整INSERT流程测试失败: {e}")
            return False
    
    async def test5_single_execution_verification(self):
        """测试5：验证f2只执行一次真实数据写入"""
        print("\n=== 测试5：单次执行验证 ===")
        
        try:
            # 检查队列项数量
            queue_items = await self.postgresql_client.execute_query(
                "SELECT COUNT(*) as count FROM entries_push_queue WHERE entry_id IN (SELECT id FROM entries WHERE employee_id = $1)",
                self.test_data['employee_id']
            )
            
            queue_count = queue_items[0]['count']
            print(f"📋 队列项总数: {queue_count}")
            
            # 检查已同步的队列项数量
            synced_items = await self.postgresql_client.execute_query(
                "SELECT COUNT(*) as count FROM entries_push_queue WHERE synced = TRUE AND entry_id IN (SELECT id FROM entries WHERE employee_id = $1)",
                self.test_data['employee_id']
            )
            
            synced_count = synced_items[0]['count']
            print(f"📋 已同步队列项数量: {synced_count}")
            
            # 检查entries表中的记录数量
            entries_count = await self.postgresql_client.execute_query(
                "SELECT COUNT(*) as count FROM entries WHERE employee_id = $1",
                self.test_data['employee_id']
            )
            
            entries_count = entries_count[0]['count']
            print(f"📊 entries表记录数量: {entries_count}")
            
            # 验证：应该只有一条记录，一个队列项，且已同步
            if (entries_count == 1 and 
                queue_count == 1 and 
                synced_count == 1):
                print("✅ f2只执行了一次真实数据写入")
                self.test_results['test5_single_execution'] = True
                return True
            else:
                print("❌ f2可能执行了多次写入")
                return False
                
        except Exception as e:
            print(f"❌ 单次执行验证失败: {e}")
            return False
    
    async def run_all_tests(self):
        """运行所有测试"""
        print("🚀 开始完整插入操作测试")
        print("=" * 60)
        
        try:
            # 初始化
            if not await self.setup():
                return False
            
            # 测试1：entries插入和日期格式转换
            entry_id = await self.test1_entries_insert_with_date_conversion()
            if not entry_id:
                print("❌ 测试1失败，停止后续测试")
                return False
            
            # 等待触发器执行
            await asyncio.sleep(2)
            
            # 测试2：f1监听器触发
            if not await self.test2_f1_listener_trigger():
                print("❌ 测试2失败")
                return False
            
            # 测试3：f2 MDB写入操作
            if not await self.test3_f2_mdb_write_operation():
                print("❌ 测试3失败")
                return False
            
            # 测试4：完整INSERT流程
            if not await self.test4_complete_insert_flow():
                print("❌ 测试4失败")
                return False
            
            # 测试5：单次执行验证
            if not await self.test5_single_execution_verification():
                print("❌ 测试5失败")
                return False
            
            # 输出测试结果
            self._print_test_results()
            
            return True
            
        except Exception as e:
            print(f"❌ 测试过程中发生错误: {e}")
            return False
        finally:
            await self.cleanup()
    
    def _print_test_results(self):
        """打印测试结果"""
        print("\n" + "=" * 60)
        print("📊 测试结果汇总")
        print("=" * 60)
        
        for test_name, result in self.test_results.items():
            status = "✅ 通过" if result else "❌ 失败"
            print(f"{test_name}: {status}")
        
        passed_count = sum(self.test_results.values())
        total_count = len(self.test_results)
        
        print(f"\n总体结果: {passed_count}/{total_count} 测试通过")
        
        if passed_count == total_count:
            print("🎉 所有测试通过！插入操作流程验证成功")
        else:
            print("⚠️ 部分测试失败，需要检查系统配置")

async def main():
    """主函数"""
    test = InsertOperationTest()
    success = await test.run_all_tests()
    
    if success:
        print("\n🎉 插入操作测试完成，所有验证通过！")
        sys.exit(0)
    else:
        print("\n❌ 插入操作测试失败，请检查系统配置")
        sys.exit(1)

if __name__ == "__main__":
    asyncio.run(main()) 