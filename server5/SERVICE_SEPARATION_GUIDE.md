# Server5 服务分离指南

## 概述

Server5 现已实现 **HTTP API** 和 **微服务** 的完全分离，符合专业运维的"**单进程只做一件事**"最佳实践。

## 架构变更

### 旧架构（单体）
```
┌─────────────────────────────────────┐
│            main.py                  │
│  ┌─────────────────────────────────┐│
│  │        HTTP API                 ││
│  │  - FastAPI 路由                 ││
│  │  - entries_api                  ││
│  │  - timeprotab_api               ││
│  └─────────────────────────────────┘│
│  ┌─────────────────────────────────┐│
│  │        微服务                   ││
│  │  - f1_listener                  ││
│  │  - f2_push_writer               ││
│  │  - f3_data_puller               ││
│  │  - f4_operation_handler         ││
│  └─────────────────────────────────┘│
└─────────────────────────────────────┘
```

### 新架构（分离）
```
┌─────────────────────────────────────┐  ┌─────────────────────────────────────┐
│     HTTP API 进程                   │  │      微服务进程                     │
│  ┌─────────────────────────────────┐│  │  ┌─────────────────────────────────┐│
│  │        HTTP API                 ││  │  │        微服务                   ││
│  │  - FastAPI 路由                 ││  │  │  - f1_listener                  ││
│  │  - entries_api                  ││  │  │  - f2_push_writer               ││
│  │  - timeprotab_api               ││  │  │  - f3_data_puller               ││
│  └─────────────────────────────────┘│  │  │  - f4_operation_handler         ││
│  start_server5_http_server.py       │  │  └─────────────────────────────────┘│
└─────────────────────────────────────┘  │  start_server5_notwith_api.py       │
                                         └─────────────────────────────────────┘
```

## 启动方式

### 1. 仅启动 HTTP API

```bash
cd server5
python start_server5_http_server.py
```

**功能**：
- ✅ 提供 HTTP API 接口
- ✅ 处理客户端请求
- ✅ 数据库查询和响应
- ❌ 不运行后台微服务

**访问**：
- API 文档：http://localhost:8009/docs
- 健康检查：http://localhost:8009/health
- 状态检查：http://localhost:8009/status

### 2. 仅启动微服务

```bash
cd server5
python start_server5_notwith_api.py
```

**功能**：
- ✅ 运行 f1-f4 后台微服务
- ✅ 数据同步和处理
- ✅ Redis 消息队列处理
- ❌ 不提供 HTTP API

**微服务列表**：
- `f1_listener`: 监听器服务
- `f2_push_writer`: 推送回写服务
- `f3_data_puller`: 数据拉取服务
- `f4_operation_handler`: 操作处理服务

### 3. 同时启动（推荐）

**终端1**：
```bash
cd server5
python start_server5_http_server.py
```

**终端2**：
```bash
cd server5
python start_server5_notwith_api.py
```

## 技术实现

### 环境变量控制

通过 `HTTP_ONLY_MODE` 环境变量控制运行模式：

```python
# HTTP-only 模式
os.environ['HTTP_ONLY_MODE'] = 'true'

# 微服务模式
os.environ['HTTP_ONLY_MODE'] = 'false'
```

### main.py 适配

```python
# 检查运行模式
HTTP_ONLY_MODE = os.getenv('HTTP_ONLY_MODE', 'false').lower() == 'true'

# 条件导入微服务
if not HTTP_ONLY_MODE:
    from app.services import (
        ListenerService,
        PushWriterServiceFixed as PushWriterService,
        DataPullerService,
        OperationHandlerService
    )
```

### ServiceManager 适配

```python
class ServiceManager:
    def __init__(self):
        self.http_only_mode = HTTP_ONLY_MODE
        if not self.http_only_mode:
            # 只在非 HTTP-only 模式下初始化微服务
            self.f1_listener = None
            self.f2_push_writer = None
            # ...
```

## 运维优势

### 1. 独立部署
- HTTP API 和微服务可以独立部署
- 支持不同的资源配置
- 支持不同的扩展策略

### 2. 故障隔离
- HTTP API 故障不影响微服务
- 微服务故障不影响 HTTP API
- 更好的系统稳定性

### 3. 监控和日志
- 独立的日志文件
- 独立的进程监控
- 更清晰的问题排查

### 4. 资源优化
- 按需启动服务
- 减少资源占用
- 提高系统效率

## 测试验证

运行分离测试：

```bash
cd server5
python test_separation.py
```

测试内容：
- ✅ HTTP-only 模式验证
- ✅ 微服务模式验证
- ✅ 服务导入分离验证
- ✅ 状态API响应验证

## 迁移指南

### 从旧版本迁移

1. **停止旧版本服务**：
   ```bash
   # 停止旧的 start_server5.py
   pkill -f start_server5.py
   ```

2. **启动新版本服务**：
   ```bash
   # 启动 HTTP API
   python start_server5_http_server.py
   
   # 启动微服务（另一个终端）
   python start_server5_notwith_api.py
   ```

### 配置文件

无需修改配置文件，所有现有配置保持兼容。

## 最佳实践

### 1. 生产环境部署

```bash
# 使用 systemd 服务
sudo systemctl start server5-http
sudo systemctl start server5-services

# 使用 supervisor
supervisorctl start server5-http
supervisorctl start server5-services
```

### 2. 日志管理

```bash
# HTTP API 日志
tail -f logs/server5_http_only.log

# 微服务日志
tail -f logs/server5_services_only.log
```

### 3. 健康检查

```bash
# HTTP API 健康检查
curl http://localhost:8009/health

# 微服务状态检查（需要 HTTP API 运行）
curl http://localhost:8009/status
```

## 故障排查

### 1. HTTP API 无响应

```bash
# 检查进程
ps aux | grep start_server5_http_server.py

# 检查端口
netstat -tlnp | grep 8009

# 检查日志
tail -f logs/server5_http_only.log
```

### 2. 微服务不工作

```bash
# 检查进程
ps aux | grep start_server5_notwith_api.py

# 检查日志
tail -f logs/server5_services_only.log

# 检查 Redis 连接
redis-cli ping
```

### 3. 数据同步问题

```bash
# 检查 Redis 队列
redis-cli llen push_job

# 检查 PostgreSQL 连接
psql -h ************ -U postgres -d my_suite_db -c "SELECT 1"
```

## 总结

新的分离架构提供了：
- ✅ 更好的系统稳定性
- ✅ 更灵活的部署策略
- ✅ 更清晰的问题排查
- ✅ 更高的运维效率

遵循"**单进程只做一件事**"的原则，实现了专业级的微服务架构。 