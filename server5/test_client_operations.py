#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
模拟实际的客户端插入和更新操作
对比两者的数据流差异
"""

import asyncio
import sys
import aiohttp
import json
from pathlib import Path
sys.path.append(str(Path(__file__).parent))

from app.database import IMDBClient

async def test_client_operations():
    """测试客户端插入和更新操作"""
    
    print("🧪 模拟客户端插入和更新操作")
    print("=" * 50)
    
    base_url = "http://localhost:8009"
    imdb_client = IMDBClient()
    
    try:
        await imdb_client.connect()
        
        # 测试数据 - 模拟客户端输入
        test_data = {
            "entry_date": "2025/07/15",
            "employee_id": "215829",
            "duration": 0.5,
            "model": "",
            "number": "",
            "factory_number": "",
            "project_number": "TEST001",
            "unit_number": "",
            "category": "0",  # 客户端输入字符串"0"
            "item": "0",      # 客户端输入字符串"0"
            "department": "131",
            "source": "user"
        }
        
        async with aiohttp.ClientSession() as session:
            try:
                # 1. 测试插入操作（按钮4）
                print("\n📝 步骤1: 测试插入操作（按钮4）...")
                print(f"📦 发送数据: {test_data}")
                
                # 使用客户端entries网关API
                insert_url = f"{base_url}/client/entries/create"
                async with session.post(insert_url, json=test_data) as response:
                    insert_result = await response.json()
                    print(f"📤 插入响应: {insert_result}")
                    
                    if response.status == 200 and insert_result.get('success'):
                        entry_id = insert_result.get('entry_id')
                        print(f"✅ 插入成功: entry_id={entry_id}")
                        
                        # 2. 等待一段时间让f2处理
                        print("\n⏳ 步骤2: 等待f2处理...")
                        await asyncio.sleep(3)
                        
                        # 3. 检查PostgreSQL中的数据
                        print("\n📋 步骤3: 检查PostgreSQL中的数据...")
                        entry_record = await imdb_client.fetch_one(
                            "SELECT * FROM entries WHERE id = $1", entry_id
                        )
                        
                        if entry_record:
                            print(f"📊 PostgreSQL中的数据:")
                            print(f"   category: {entry_record['category']} (类型: {type(entry_record['category'])})")
                            print(f"   item: {entry_record['item']} (类型: {type(entry_record['item'])})")
                            print(f"   external_id: {entry_record['external_id']}")
                            print(f"   source: {entry_record['source']}")
                            print(f"   duration: {entry_record['duration']}")
                        else:
                            print("❌ 无法获取记录")
                        
                        # 4. 测试更新操作（按钮5）
                        print("\n🔄 步骤4: 测试更新操作（按钮5）...")
                        update_data = {
                            "entry_date": "2025/07/15",
                            "employee_id": "215829",
                            "duration": 0.8,
                            "model": "",
                            "number": "",
                            "factory_number": "",
                            "project_number": "TEST002",
                            "unit_number": "",
                            "category": "0",  # 保持为"0"
                            "item": "0",      # 保持为"0"
                            "department": "131",
                            "source": "user"
                        }
                        
                        print(f"📦 更新数据: {update_data}")
                        
                        # 使用entries API
                        update_url = f"{base_url}/api/entries/{entry_id}"
                        async with session.put(update_url, json=update_data) as update_response:
                            update_result = await update_response.json()
                            print(f"📤 更新响应: {update_result}")
                            
                            if update_response.status == 200:
                                print(f"✅ 更新成功: entry_id={entry_id}")
                                
                                # 5. 再次检查PostgreSQL中的数据
                                print("\n📋 步骤5: 再次检查PostgreSQL中的数据...")
                                entry_record2 = await imdb_client.fetch_one(
                                    "SELECT * FROM entries WHERE id = $1", entry_id
                                )
                                
                                if entry_record2:
                                    print(f"📊 更新后的数据:")
                                    print(f"   category: {entry_record2['category']} (类型: {type(entry_record2['category'])})")
                                    print(f"   item: {entry_record2['item']} (类型: {type(entry_record2['item'])})")
                                    print(f"   external_id: {entry_record2['external_id']}")
                                    print(f"   source: {entry_record2['source']}")
                                    print(f"   duration: {entry_record2['duration']}")
                                else:
                                    print("❌ 无法获取更新后的记录")
                            else:
                                print(f"❌ 更新失败: {update_response.status}")
                        
                        # 6. 清理测试数据
                        print("\n🧹 步骤6: 清理测试数据...")
                        delete_url = f"{base_url}/api/entries/{entry_id}"
                        async with session.delete(delete_url) as delete_response:
                            if delete_response.status == 200:
                                print(f"✅ 测试数据已清理: entry_id={entry_id}")
                            else:
                                print(f"⚠️ 清理失败: {delete_response.status}")
                    else:
                        print(f"❌ 插入失败: {response.status}")
                        print(f"   错误信息: {insert_result}")
                
                print("\n✅ 测试完成！")
                
            except Exception as e:
                print(f"❌ 测试失败: {e}")
                import traceback
                traceback.print_exc()
                
    finally:
        await imdb_client.disconnect()

if __name__ == "__main__":
    asyncio.run(test_client_operations()) 