#!/usr/bin/env python3
"""
系统修复测试脚本
测试所有修复的问题：端口冲突、性能、连接释放等
"""

import requests
import time
import subprocess
import sys
import os
from datetime import datetime

def test_port_availability():
    """测试端口可用性"""
    print("🔍 测试端口可用性...")
    
    try:
        # 检查server5 (8009) 是否可用
        response = requests.get("http://localhost:8009/docs", timeout=5)
        if response.status_code == 200:
            print("✅ Server5 (8009) 正常运行")
            server5_running = True
        else:
            print("❌ Server5 (8009) 无响应")
            server5_running = False
    except:
        print("❌ Server5 (8009) 连接失败")
        server5_running = False
    
    try:
        # 检查server6 (8019) 是否可用
        response = requests.get("http://************:8019/docs", timeout=5)
        if response.status_code == 200:
            print("✅ Server6 (8019) 正常运行")
            server6_running = True
        else:
            print("❌ Server6 (8019) 无响应")
            server6_running = False
    except:
        print("❌ Server6 (8019) 连接失败")
        server6_running = False
    
    return server5_running, server6_running

def test_chart_data_performance():
    """测试chart-data API性能"""
    print("\n📊 测试chart-data API性能...")
    
    url = "http://localhost:8009/api/entries/chart-data"
    params = {
        "employee_id": "215829",
        "start_date": "2025-06-01",
        "end_date": "2025-06-30"
    }
    
    try:
        start_time = time.time()
        response = requests.get(url, params=params, timeout=15)
        end_time = time.time()
        
        duration = end_time - start_time
        print(f"⏱️ 请求耗时: {duration:.2f}秒")
        
        if response.status_code == 200:
            data = response.json()
            print(f"✅ 请求成功，数据长度: {len(data.get('daily_data', []))}")
            print(f"📈 entries数量: {data.get('entries_count', 0)}")
            print(f"📅 timeprotab数量: {data.get('timeprotab_count', 0)}")
            
            if duration < 1.0:
                print("🎉 性能优秀 (<1秒)")
            elif duration < 3.0:
                print("✅ 性能良好 (<3秒)")
            else:
                print("⚠️ 性能需要改善 (>3秒)")
            
            return True
        else:
            print(f"❌ 请求失败: {response.status_code}")
            return False
    except Exception as e:
        print(f"❌ 请求异常: {e}")
        return False

def test_multiple_concurrent_requests():
    """测试多个并发请求"""
    print("\n🚀 测试并发请求性能...")
    
    import threading
    import queue
    
    results = queue.Queue()
    
    def make_request(request_id):
        try:
            start_time = time.time()
            response = requests.get("http://localhost:8009/api/entries/months?employee_id=215829", timeout=10)
            end_time = time.time()
            duration = end_time - start_time
            
            results.put({
                'id': request_id,
                'success': response.status_code == 200,
                'duration': duration
            })
        except Exception as e:
            results.put({
                'id': request_id,
                'success': False,
                'error': str(e)
            })
    
    # 创建5个并发请求
    threads = []
    for i in range(5):
        thread = threading.Thread(target=make_request, args=(i,))
        threads.append(thread)
        thread.start()
    
    # 等待所有请求完成
    for thread in threads:
        thread.join()
    
    # 收集结果
    success_count = 0
    total_duration = 0
    
    while not results.empty():
        result = results.get()
        if result['success']:
            success_count += 1
            total_duration += result['duration']
            print(f"✅ 请求{result['id']}: {result['duration']:.2f}秒")
        else:
            print(f"❌ 请求{result['id']}: 失败")
    
    if success_count > 0:
        avg_duration = total_duration / success_count
        print(f"📊 并发测试结果: {success_count}/5 成功，平均耗时: {avg_duration:.2f}秒")
        return success_count >= 4  # 至少80%成功
    else:
        print("❌ 所有并发请求都失败")
        return False

def test_program1_startup():
    """测试program1启动建议"""
    print("\n📝 Program1启动建议:")
    print("1. 重新启动server5:")
    print("   python start_server5_with_api.py")
    print("2. 从Launcher启动program1:")
    print("   按正常流程登录 -> 启动program1")
    print("3. 观察启动日志:")
    print("   - 步骤1-5应该依次完成")
    print("   - 每个步骤间隔2-4秒")
    print("   - 图表数据加载应该在1秒内完成")
    print("4. 测试连接释放:")
    print("   - 关闭program1窗口")
    print("   - 应该看到'所有连接已释放'消息")
    print("   - 重新启动program1不应该卡顿")

def main():
    """主测试函数"""
    print("🧪 MySuite系统修复测试")
    print("=" * 50)
    
    # 测试端口可用性
    server5_ok, server6_ok = test_port_availability()
    
    if not server5_ok:
        print("\n❌ Server5未运行，请先启动:")
        print("python start_server5_with_api.py")
        return
    
    # 测试性能
    chart_ok = test_chart_data_performance()
    
    # 测试并发
    concurrent_ok = test_multiple_concurrent_requests()
    
    # 给出建议
    test_program1_startup()
    
    print("\n📋 测试总结:")
    print(f"✅ 端口配置: Server5(8009){'✓' if server5_ok else '✗'}, Server6(8019){'✓' if server6_ok else '✗'}")
    print(f"✅ 性能优化: {'✓' if chart_ok else '✗'}")
    print(f"✅ 并发处理: {'✓' if concurrent_ok else '✗'}")
    
    if all([server5_ok, chart_ok, concurrent_ok]):
        print("\n🎉 所有修复均已生效！系统运行正常！")
    else:
        print("\n⚠️ 某些问题仍需要处理，请查看上面的测试结果")

if __name__ == "__main__":
    main() 