#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
仅启动 F2 Push Writer 服务
避免启动完整的 Server5 应用
"""

import asyncio
import logging
import sys
from pathlib import Path

# 添加项目路径
sys.path.append(str(Path(__file__).parent))

# 配置简单日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

async def start_f2_only():
    """仅启动 F2 Push Writer 服务"""
    try:
        print("🚀 启动 F2 Push Writer 服务...")
        
        # 导入配置
        from app.config_selector import ConfigSelector
        config_selector = ConfigSelector()
        config_dict, config_name = config_selector.load_config()
        config_module = config_selector.apply_config_to_module(config_dict)
        
        print(f"📍 使用配置: {config_name}")
        print(f"🗄️ 数据库: {config_dict.get('IMDB_DATABASE_URL', 'local').split('@')[1] if '@' in config_dict.get('IMDB_DATABASE_URL', '') else 'local'}")
        print(f"🌐 Server6: {config_dict.get('SERVER6_BASE_URL', 'unknown')}")
        
        # 仅导入和启动 F2 服务
        from app.services.f2_push_writer import PushWriterServiceFixed
        
        f2_service = PushWriterServiceFixed()
        
        # 启动 F2 服务
        success = await f2_service.start()
        if not success:
            print("❌ F2 服务启动失败")
            return
        
        print("✅ F2 Push Writer 服务启动成功")
        print("🔄 正在处理队列中的任务...")
        print("按 Ctrl+C 停止服务")
        
        # 保持运行
        try:
            while True:
                await asyncio.sleep(1)
        except KeyboardInterrupt:
            print("\n👋 收到停止信号...")
        finally:
            print("🔌 正在停止 F2 服务...")
            await f2_service.stop()
            print("✅ F2 服务已停止")
            
    except Exception as e:
        print(f"❌ F2 服务启动失败: {e}")
        logger.error(f"F2 服务错误: {e}", exc_info=True)

def main():
    """主函数"""
    print("🎯 F2 Push Writer 独立启动器")
    print("=" * 40)
    
    try:
        asyncio.run(start_f2_only())
    except KeyboardInterrupt:
        print("\n👋 程序已退出")
    except Exception as e:
        print(f"❌ 启动失败: {e}")

if __name__ == "__main__":
    main() 