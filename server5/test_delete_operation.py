#!/usr/bin/env python3
"""
测试删除操作的行为
"""

import asyncio
import aiohttp
import json
import logging
from datetime import datetime

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

# 配置
SERVER5_BASE_URL = "http://localhost:8009"

async def test_delete_operation(entry_id: int):
    """测试删除操作"""
    try:
        logger.info(f"🗑️ 开始测试删除操作: entry_id={entry_id}")
        
        # 发送删除请求
        async with aiohttp.ClientSession() as session:
            url = f"{SERVER5_BASE_URL}/api/entries/{entry_id}"
            
            async with session.delete(url) as response:
                if response.status == 200:
                    result = await response.json()
                    logger.info(f"✅ 删除请求成功: {result}")
                    
                    # 等待一段时间让队列处理完成
                    logger.info("⏳ 等待5秒让队列处理完成...")
                    await asyncio.sleep(5)
                    
                    # 检查队列项
                    await check_delete_queue_items(entry_id)
                    
                else:
                    error_text = await response.text()
                    logger.error(f"❌ 删除请求失败: {response.status} - {error_text}")
                    
    except Exception as e:
        logger.error(f"❌ 测试删除操作失败: {e}")

async def check_delete_queue_items(entry_id: int):
    """检查删除操作的队列项"""
    try:
        logger.info(f"🔍 检查entry_id={entry_id}的删除队列项...")
        logger.info("📋 请检查以下内容:")
        logger.info("1. 终端日志中的删除操作")
        logger.info("2. entries_push_queue表中的DELETE记录")
        logger.info("3. 如果source不是'user'，可能会有UPDATE和DELETE两条记录")
        logger.info("4. 数据库查询: SELECT * FROM entries_push_queue WHERE entry_id = $1 ORDER BY created_ts DESC")
        
    except Exception as e:
        logger.error(f"❌ 检查删除队列项失败: {e}")

async def test_delete_behavior():
    """测试删除行为说明"""
    logger.info("📚 删除操作行为说明:")
    logger.info("=" * 50)
    logger.info("1. 如果记录的source='user':")
    logger.info("   - 直接执行DELETE")
    logger.info("   - 触发一次DELETE触发器")
    logger.info("   - 创建一条DELETE队列项")
    logger.info("")
    logger.info("2. 如果记录的source='system':")
    logger.info("   - 先执行UPDATE SET source='user'")
    logger.info("   - 触发UPDATE触发器，创建UPDATE队列项")
    logger.info("   - 然后执行DELETE")
    logger.info("   - 触发DELETE触发器，创建DELETE队列项")
    logger.info("   - 总共创建两条队列项")
    logger.info("")
    logger.info("这是正常行为，因为系统需要先同步数据到MDB，然后删除")
    logger.info("=" * 50)

async def main():
    """主函数"""
    logger.info("🚀 开始测试删除操作")
    
    # 显示删除行为说明
    await test_delete_behavior()
    
    # 询问用户是否要测试删除
    logger.info("⚠️ 注意：删除操作会永久删除数据！")
    logger.info("如果要测试删除操作，请手动运行:")
    logger.info("python test_delete_operation.py <entry_id>")
    
    logger.info("✅ 测试说明完成")

if __name__ == "__main__":
    import sys
    if len(sys.argv) > 1:
        entry_id = int(sys.argv[1])
        asyncio.run(test_delete_operation(entry_id))
    else:
        asyncio.run(main()) 