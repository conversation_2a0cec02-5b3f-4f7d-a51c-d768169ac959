# server5/app/main_minimal.py
# 最简化的FastAPI应用 - 用于Windows容错启动
# 不依赖复杂的服务管理器和数据库连接

from fastapi import FastAPI
from fastapi.responses import JSONResponse
import platform
import sys
from pathlib import Path

# 创建FastAPI应用
app = FastAPI(
    title="MySuite Server5 - Minimal Mode",
    description="数据同步微服务 - Windows容错模式",
    version="1.0.0"
)

@app.get("/")
async def root():
    """根路径"""
    return {
        "message": "MySuite Server5 运行中",
        "status": "ok",
        "mode": "minimal",
        "platform": platform.system()
    }

@app.get("/health")
async def health():
    """健康检查"""
    return {
        "status": "healthy",
        "mode": "minimal",
        "platform": platform.system(),
        "python_version": sys.version,
        "services": {
            "note": "运行在最简模式，数据库服务已禁用"
        }
    }

@app.get("/status")
async def status():
    """状态检查"""
    return {
        "server": "Server5",
        "status": "running",
        "mode": "minimal",
        "platform": platform.system(),
        "services": {
            "f1_listener": "disabled",
            "f2_push_writer": "disabled", 
            "f3_data_puller": "disabled",
            "f4_operation_handler": "disabled",
            "f5_bulk_sync": "disabled",
            "f6_user_sync": "disabled"
        },
        "databases": {
            "postgresql": "not_required",
            "redis": "not_required",
            "mongodb": "not_required",
            "mdb": "not_required"
        }
    }

@app.get("/test")
async def test():
    """基础测试"""
    try:
        # 尝试导入数据库工具（如果可用）
        try:
            from app.utils.db_utils_enhanced import MultiPlatformMDBClient
            
            client = MultiPlatformMDBClient()
            test_result = client.test_connection()
            
            return {
                "mdb_test": test_result,
                "platform": platform.system(),
                "mode": "minimal",
                "status": "success"
            }
        except ImportError:
            return {
                "mdb_test": "MDB工具未可用",
                "platform": platform.system(),
                "mode": "minimal",
                "status": "limited"
            }
        except Exception as e:
            return {
                "mdb_test": f"测试失败: {str(e)}",
                "platform": platform.system(),
                "mode": "minimal",
                "status": "error"
            }
    except Exception as e:
        return {
            "error": str(e),
            "platform": platform.system(),
            "mode": "minimal",
            "status": "failed"
        }

@app.get("/info")
async def info():
    """系统信息"""
    return {
        "server": "MySuite Server5",
        "version": "1.0.0",
        "mode": "minimal",
        "platform": {
            "system": platform.system(),
            "release": platform.release(),
            "version": platform.version(),
            "machine": platform.machine(),
            "processor": platform.processor()
        },
        "python": {
            "version": sys.version,
            "executable": sys.executable,
            "path": sys.path[:3]  # 只显示前3个路径
        },
        "working_directory": str(Path.cwd()),
        "description": "Windows容错模式 - 不依赖外部数据库服务"
    }

# 简单的生命周期管理（无数据库依赖）
@app.on_event("startup")
async def startup_event():
    """启动事件"""
    print("Server5最简模式已启动")

@app.on_event("shutdown") 
async def shutdown_event():
    """关闭事件"""
    print("Server5最简模式已关闭") 