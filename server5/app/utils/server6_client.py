# 2025/06/27+13：52 +Server6客户端重构以适配新API
# server5/app/utils/server6_client.py
# Server6 MDB网关API客户端 - 重构以匹配新的专用API

import aiohttp
import asyncio
import logging
from typing import Dict, List, Optional, Any
from datetime import date, datetime
import json
import sys
from pathlib import Path
import decimal

# 添加配置文件路径
sys.path.append(str(Path(__file__).parent.parent.parent))
from config.config import SERVER6_CONFIG

logger = logging.getLogger(__name__)

class Server6Client:
    """Server6 MDB网关API客户端 (适配重构后的API)"""
    
    def __init__(self, base_url: str = None, api_key: Optional[str] = None):
        config = SERVER6_CONFIG
        self.base_url = (base_url or config["base_url"]).rstrip('/')
        self.api_key = api_key or config["api_key"]
        self.timeout = config["timeout"]
        self.session: Optional[aiohttp.ClientSession] = None
        
        logger.info(f"📡 Server6客户端初始化 (新版): {self.base_url}")
    
    async def connect(self):
        """创建并保持 aiohttp.ClientSession."""
        if self.session is None or self.session.closed:
            timeout = aiohttp.ClientTimeout(total=self.timeout)
            self.session = aiohttp.ClientSession(
                timeout=timeout,
                headers=self._get_headers()
            )
            logger.info("AIOHTTP session for Server6 created.")

    async def disconnect(self):
        """关闭 aiohttp.ClientSession."""
        if self.session and not self.session.closed:
            await self.session.close()
            self.session = None
            logger.info("AIOHTTP session for Server6 closed.")

    def _get_headers(self) -> Dict[str, str]:
        """获取请求头"""
        headers = {'Content-Type': 'application/json'}
        if self.api_key:
            headers['X-API-Key'] = self.api_key
        return headers

    async def _make_request(self, method: str, endpoint: str, json_data: Optional[Dict] = None, path_param: Optional[Any] = None) -> Dict:
        """发起HTTP请求的核心函数"""
        await self.connect() # 确保 session 存在
        
        url = f"{self.base_url}{endpoint}"
        if path_param:
            url += f"/{path_param}"

        # 重试逻辑
        max_retries = 3
        retry_delay = 2  # 初始延迟2秒
        
        for attempt in range(max_retries):
            try:
                async with self.session.request(method, url, json=json_data) as response:
                    if response.status == 422:
                        # 获取详细的验证错误信息
                        error_detail = await response.json()
                        logger.error(f"❌ Server6验证错误: {method} {url} - {error_detail}")
                        raise Exception(f"Server6数据验证失败: {error_detail}")
                    
                    response.raise_for_status()  # 如果状态码是 4xx 或 5xx，则抛出异常
                    return await response.json()
            except asyncio.TimeoutError as e:
                if attempt < max_retries - 1:
                    logger.warning(f"⏰ Server6请求超时 (尝试 {attempt + 1}/{max_retries}): {method} {url}，{retry_delay}秒后重试...")
                    await asyncio.sleep(retry_delay)
                    retry_delay *= 2  # 指数退避
                    continue
                else:
                    logger.error(f"❌ Server6请求最终超时: {method} {url}")
                    raise
            except aiohttp.ClientResponseError as e:
                # 记录更详细的错误，包括请求体，帮助调试
                req_data_summary = f" Request Body: {json.dumps(json_data, ensure_ascii=False)}" if json_data else ""
                logger.error(f"❌ Server6请求失败: {method} {url} - Status: {e.status}, Message: {e.message}.{req_data_summary}")
                # 重新包装异常，包含更丰富的信息
                raise Exception(f"Server6请求返回错误 {e.status} (URL: {url}, Employee: {json_data.get('employee_id', 'N/A')}): {e.message}") from e
            except Exception as e:
                if attempt < max_retries - 1 and isinstance(e, (asyncio.TimeoutError, ConnectionError)):
                    logger.warning(f"🔄 Server6连接错误 (尝试 {attempt + 1}/{max_retries}): {method} {url}，{retry_delay}秒后重试...")
                    await asyncio.sleep(retry_delay)
                    retry_delay *= 2
                    continue
                else:
                    logger.error(f"❌ Server6请求异常: {method} {url} - {e}", exc_info=True)
                    raise

    # --- 新的专用API方法 ---

    async def test_mdb_connection(self) -> Dict:
        """测试MDB连接"""
        return await self._make_request('GET', '/mdb/test')

    async def query_entries(self, employee_id: str, start_date: date, end_date: date) -> List[Dict]:
        """根据员工ID和日期范围查询记录"""
        payload = {
            "employee_id": employee_id,
            "start_date": start_date.isoformat(),
            "end_date": end_date.isoformat(),
        }
        response = await self._make_request('POST', '/mdb/entries/query', json_data=payload)
        
        # 检查响应状态
        if response.get('status') == 'error':
            error_msg = response.get('message', 'Unknown error')
            logger.error(f"Server6查询返回错误: {error_msg}")
            return []
        
        # 假设成功时，server6返回 {"status": "success", "data": [...]}
        return response.get('data', [])

    async def query_all_entries_by_date_range(self, start_date: date, end_date: date) -> List[Dict]:
        """根据日期范围查询所有员工的记录"""
        payload = {
            "start_date": start_date.isoformat(),
            "end_date": end_date.isoformat(),
        }
        response = await self._make_request('POST', '/mdb/entries/query_by_date', json_data=payload)
        return response.get('data', [])

    async def query_bulk_fast(self, start_date: date, end_date: date) -> List[Dict]:
        """
        快速批量查询指定日期范围的所有记录。
        这是性能优化的版本，替代逐个员工查询。
        """
        payload = {
            "start_date": start_date.isoformat(),
            "end_date": end_date.isoformat(),
        }
        response = await self._make_request('POST', '/mdb/entries/query_bulk_fast', json_data=payload)
        
        # 检查响应状态
        if response.get('status') == 'error':
            error_msg = response.get('message', 'Unknown error')
            logger.error(f"Server6快速批量查询返回错误: {error_msg}")
            return []
        
        return response.get('data', [])

    async def insert_entry(self, entry_data: Dict[str, Any]) -> Dict:
        """
        插入一条新记录到MDB。
        entry_data 应符合 EntryRecordCreate 模型。
        关键修复：空值不跳过，明确发送NULL给Server6
        """
        # 字段名映射：英文 -> 日文
        field_mapping = {
            'employee_id': '従業員ｺｰﾄﾞ',
            'entry_date': '日付',
            'model': '機種',
            'number': '号機',
            'factory_number': '工場製番',
            'project_number': '工事番号',
            'unit_number': 'ﾕﾆｯﾄ番号',
            'category': '区分',
            'item': '項目',
            'duration': '時間',
            'department': '所属ｺｰﾄﾞ'
        }
        
        # 转换数据并映射字段名
        serializable_data = {}
        for key, value in entry_data.items():
            # 映射字段名
            japanese_key = field_mapping.get(key, key)
            
            # 处理空值：发送None给Server6，让Server6处理NULL转换
            if value is None:
                serializable_data[japanese_key] = None
                logger.debug(f"  📤 字段 {key} 设置为 None")
                continue
            
            # 处理空字符串：发送None给Server6，让Server6处理NULL转换
            if isinstance(value, str) and value.strip() == '':
                serializable_data[japanese_key] = None
                logger.debug(f"  📤 字段 {key} (空字符串) 设置为 None")
                continue
            
            # 特殊处理：确保category和item字段发送正确的值
            if key in ['category', 'item']:
                # 确保值不为空字符串或None
                if value == "" or value is None:
                    final_value = "0"
                    logger.debug(f"  📤 字段 {key} (空值) 设置为默认值 '0'")
                else:
                    final_value = str(value)  # 确保是字符串
                    logger.debug(f"  📤 字段 {key} 设置为 '{value}'")
                
                # 只设置日文字段名，避免Server6的or逻辑问题
                serializable_data[japanese_key] = final_value  # 日文字段名
                continue
            
            # 处理日期格式
            if isinstance(value, (datetime, date)):
                if isinstance(value, datetime):
                    serializable_data[japanese_key] = value.strftime('%Y-%m-%d')
                else:
                    serializable_data[japanese_key] = value.strftime('%Y-%m-%d')
            elif isinstance(value, str) and key == 'entry_date':
                # 如果是字符串格式的日期，转换为YYYY-MM-DD格式
                if '/' in value:
                    # 将 YYYY/MM/DD 转换为 YYYY-MM-DD
                    serializable_data[japanese_key] = value.replace('/', '-')
                else:
                    serializable_data[japanese_key] = value
            elif isinstance(value, decimal.Decimal):
                serializable_data[japanese_key] = float(value)
            else:
                serializable_data[japanese_key] = value
        
        logger.info(f"📤 发送到Server6的数据: {serializable_data}")
        logger.info(f"🔍 调试 - category最终值: {serializable_data.get('区分')}, 类型: {type(serializable_data.get('区分'))}")
        logger.info(f"🔍 调试 - item最终值: {serializable_data.get('項目')}, 类型: {type(serializable_data.get('項目'))}")
        return await self._make_request('POST', '/mdb/entries/insert', json_data=serializable_data)

    async def update_entry(self, external_id: int, update_data: Dict[str, Any]) -> Dict:
        """
        根据external_id更新MDB中的记录。
        update_data 应符合 EntryRecordUpdate 模型。
        修复：Server6依旧期望日文字段名，需与insert保持一致的映射方式
        关键修复：空值不跳过，明确发送NULL给Server6
        """
        # 字段名映射：英文 -> 日文（与insert保持一致）
        field_mapping = {
            'employee_id': '従業員ｺｰﾄﾞ',
            'entry_date': '日付',
            'model': '機種',
            'number': '号機',
            'factory_number': '工場製番',
            'project_number': '工事番号',
            'unit_number': 'ﾕﾆｯﾄ番号',
            'category': '区分',
            'item': '項目',
            'duration': '時間',
            'department': '所属ｺｰﾄﾞ'
        }
        serializable_data = {}
        for key, value in update_data.items():
            # 映射字段名
            japanese_key = field_mapping.get(key, key)
            
            # 处理空值：发送None给Server6，让Server6处理NULL转换
            if value is None:
                serializable_data[japanese_key] = None
                logger.debug(f"  📤 字段 {key} 设置为 None")
                continue
            
            # 处理空字符串：发送None给Server6，让Server6处理NULL转换
            if isinstance(value, str) and value.strip() == '':
                serializable_data[japanese_key] = None
                logger.debug(f"  📤 字段 {key} (空字符串) 设置为 None")
                continue
            
            # 特殊处理：确保category和item字段发送正确的值
            if key in ['category', 'item']:
                # 确保值不为空字符串或None
                if value == "" or value is None:
                    final_value = "0"
                    logger.debug(f"  📤 字段 {key} (空值) 设置为默认值 '0'")
                else:
                    final_value = str(value)  # 确保是字符串
                    logger.debug(f"  📤 字段 {key} 设置为 '{value}'")
                
                # 只设置日文字段名，避免Server6的or逻辑问题
                serializable_data[japanese_key] = final_value  # 日文字段名
                continue
            
            # 处理日期格式
            if isinstance(value, (datetime, date)):
                if isinstance(value, datetime):
                    serializable_data[japanese_key] = value.strftime('%Y-%m-%d')
                else:
                    serializable_data[japanese_key] = value.strftime('%Y-%m-%d')
            elif isinstance(value, str) and key == 'entry_date':
                # 转换 YYYY/MM/DD -> YYYY-MM-DD
                if '/' in value:
                    serializable_data[japanese_key] = value.replace('/', '-')
                else:
                    serializable_data[japanese_key] = value
            elif isinstance(value, decimal.Decimal):
                serializable_data[japanese_key] = float(value)
            else:
                serializable_data[japanese_key] = value
        logger.info(f"📤 发送到Server6的更新数据: {serializable_data}")
        return await self._make_request('PUT', f'/mdb/entries/update/{external_id}', json_data=serializable_data)

    async def delete_entry(self, external_id: int) -> Dict:
        """根据external_id删除MDB中的记录"""
        return await self._make_request('DELETE', f'/mdb/entries/delete/{external_id}')

    async def get_distinct_employee_ids(self) -> List[str]:
        """从Server6获取所有唯一的员工ID列表"""
        response = await self._make_request('GET', '/mdb/employees/distinct')
        return response.get('data', [])

    # --- 辅助方法和兼容性方法 ---

    async def get_status(self) -> Dict:
        """获取Server6状态"""
        try:
            test_result = await self.test_mdb_connection()
            return {
                'connected': True,
                'base_url': self.base_url,
                'mdb_connection': test_result
            }
        except Exception as e:
            return {
                'connected': False,
                'error': str(e)
            }
    
    async def get_incremental_data(self, start_date: date, end_date: date, employee_id: str) -> List[Dict]:
        """
        为f3, f5, f6等服务提供统一的数据拉取方法。
        注意：现在拉取数据必须绑定到一个employee_id。
        如果需要拉取所有人的，server6需要一个新接口，或者在这里循环调用。
        根据当前设计，同步总是与特定用户关联的。
        """
                
        # 设定目标日期对象
        self.start_date = start_date
        self.end_date = end_date

        # 将目标日期格式化为 YYYY/MM/DD
        date_start_date = start_date.strftime("%Y/%m/%d")
        date_end_date = end_date.strftime("%Y/%m/%d")

        logger.info(f"向 Server6 请求用户 '{employee_id}' 从 {date_start_date} 到 {date_end_date} 的数据")
        return await self.query_entries(
            employee_id=employee_id,
            start_date=date_start_date,
            end_date=date_end_date
        )

    async def __aenter__(self):
        await self.connect()
        return self

    async def __aexit__(self, exc_type, exc_val, exc_tb):
        await self.disconnect()

    # 20250708+11:30 + 新增调用 server6 新的高性能ID获取API的方法
    async def get_ids_in_range(self, start_date: date, end_date: date) -> Optional[List[int]]:
        """
        调用Server6的 /entries/get_ids_in_range 接口，快速获取指定日期范围内的所有记录ID。
        
        Args:
            start_date: 开始日期
            end_date: 结束日期
            
        Returns:
            一个包含所有ID的列表，如果API调用失败则返回None。
        """
        endpoint = "/mdb/entries/get_ids_in_range"
        payload = {
            "start_date": start_date.isoformat(),
            "end_date": end_date.isoformat()
        }
        
        try:
            response = await self._make_request('POST', endpoint, json_data=payload)
            # 2025/07/08 - 修复：适配server6返回的APIResponse格式
            if response and response.get("status") == "success":
                return response.get("data", [])
            else:
                error_msg = response.get("message", "未知错误") if response else "无响应"
                logger.error(f"从Server6获取ID列表失败: {error_msg}")
                return None
        except Exception as e:
            logger.error(f"调用Server6 get_ids_in_range API失败: {e}", exc_info=True)
            return None 