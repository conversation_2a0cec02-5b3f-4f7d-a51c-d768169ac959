#2025/26/27 +修正Server5启动错误：服务解耦
# server5/app/utils/mdb_constants.py
# Shared constants for interacting with the MDB database via Server6

# MDB数据库中的日文字段名 (必须完全匹配)
# These are copied from server6/app.core.mdb_client.py to decouple the services.
TABLE_NAME = "元作業時間"
COL_ID = "ID"
COL_EMPLOYEE_ID = "従業員ｺｰﾄﾞ"
COL_DATE = "日付"
COL_MODEL = "機種"
COL_NUMBER = "号機"
COL_FACTORY_NUMBER = "工場製番"
COL_PROJECT_NUMBER = "工事番号"
COL_UNIT_NUMBER = "ﾕﾆｯﾄ番号"
COL_CATEGORY = "区分"
COL_ITEM = "項目"
COL_TIME = "時間"
COL_DEPARTMENT = "所属ｺｰﾄﾞ" 