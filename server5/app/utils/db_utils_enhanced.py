# server5/app/utils/db_utils_enhanced.py
# 增强版数据库工具 - 多平台兼容MDB操作
# 25.06.26 增加多平台自动检测功能

import os
import platform
import logging
from typing import Optional, Dict, List, Tuple, Any
from datetime import datetime, timedelta
import asyncio
import sys
from pathlib import Path

logger = logging.getLogger(__name__)

class MultiPlatformMDBClient:
    """多平台兼容的MDB数据库客户端"""
    
    def __init__(self, db_path: str = None):
        # 25.06.26 增加多平台自动检测
        self.platform_name = platform.system()
        self.is_windows = self.platform_name == 'Windows'
        self.is_linux = self.platform_name == 'Linux'
        self.is_darwin = self.platform_name == 'Darwin'  # macOS
        
        # 默认数据库路径
        self.db_path = db_path or r"D:\actest25\6.mdb"
        
        # 初始化平台特定的环境
        self.win32_available = False
        self.pythoncom_available = False
        
        logger.info(f"🌐 MDB客户端检测到操作系统: {self.platform_name}")
        
        if self.is_windows:
            self._init_windows_env()
        elif self.is_linux:
            self._init_linux_env()
        elif self.is_darwin:
            self._init_macos_env()
        else:
            logger.warning(f"⚠️ 未识别的操作系统: {self.platform_name}")
    
    def _init_windows_env(self):
        """初始化Windows环境"""
        try:
            import win32com.client
            import pythoncom
            self.win32_available = True
            self.pythoncom_available = True
            logger.info("✅ Windows COM组件可用 - 可直接操作MDB")
        except ImportError as e:
            logger.warning(f"❌ Windows COM组件不可用: {e}")
            logger.info("💡 请安装pywin32: pip install pywin32")
    
    def _init_linux_env(self):
        """初始化Linux环境"""
        logger.info("🐧 Linux环境 - 将使用模拟或远程MDB访问")
        # 在Linux上可以考虑：
        # 1. 使用mdb-tools (需要安装)
        # 2. 通过网络调用Windows上的MDB服务
        # 3. 使用SQLite模拟MDB数据
        
        # 检查是否安装了mdb-tools
        try:
            import subprocess
            result = subprocess.run(['which', 'mdb-ver'], capture_output=True, text=True)
            if result.returncode == 0:
                logger.info("✅ 检测到mdb-tools，可以读取MDB文件")
                self.mdb_tools_available = True
            else:
                logger.info("❌ 未检测到mdb-tools")
                self.mdb_tools_available = False
        except Exception:
            self.mdb_tools_available = False
    
    def _init_macos_env(self):
        """初始化macOS环境"""
        logger.info("🍎 macOS环境 - 将使用模拟MDB访问")
        self.mdb_tools_available = False
    
    def write_work_time(self, employee_data: Dict) -> Tuple[bool, str, Optional[int]]:
        """
        插入一条记录到MDB，并返回 (ok, msg, new_id)
        25.06.26 增加多平台支持
        """
        if self.is_windows and self.win32_available:
            return self._windows_write_work_time(employee_data)
        else:
            return self._mock_write_work_time(employee_data)
    
    def _windows_write_work_time(self, employee_data: Dict) -> Tuple[bool, str, Optional[int]]:
        """Windows环境下的真实MDB写入"""
        import win32com.client
        import pythoncom
        
        pythoncom.CoInitialize()
        access = None
        try:
            access = win32com.client.Dispatch("Access.Application")
            access.OpenCurrentDatabase(self.db_path)
            db = access.CurrentDb()

            # 构造 INSERT 语句（使用验证过的格式）
            sql = f"""INSERT INTO 元作業時間
              (従業員ｺｰﾄﾞ, 日付, 機種, 号機, 工場製番, 工事番号,
               ﾕﾆｯﾄ番号, 区分, 項目, 時間, 所属ｺｰﾄﾞ)
             VALUES
              ('{employee_data['employee_id']}', 
               #{employee_data['date']}#,
               {f"'{employee_data['model']}'" if employee_data.get('model') else 'NULL'},
               {f"'{employee_data['number']}'" if employee_data.get('number') else 'NULL'},
               {f"'{employee_data['factory_number']}'" if employee_data.get('factory_number') else 'NULL'},
               {f"'{employee_data['project_number']}'" if employee_data.get('project_number') else 'NULL'},
               {f"'{employee_data['unit_number']}'" if employee_data.get('unit_number') else 'NULL'},
               '{employee_data['category']}',
               '{employee_data['item']}',
               {employee_data['time']},
               '{employee_data['department']}'
              )"""
            
            db.Execute(sql)

            # 取回新插入行的自增 ID
            rs = db.OpenRecordset("SELECT @@IDENTITY AS NewID")
            new_id = rs.Fields("NewID").Value
            rs.Close()

            logger.debug(f"✅ Windows MDB写入成功，新ID: {new_id}")
            return True, "写入数据库成功", new_id

        except Exception as e:
            logger.error(f"❌ Windows MDB写入失败: {e}")
            return False, f"写入数据库时出错: {e}", None

        finally:
            if access:
                try:
                    access.CloseCurrentDatabase()
                    access.Quit()
                except: 
                    pass
            pythoncom.CoUninitialize()
    
    def _mock_write_work_time(self, employee_data: Dict) -> Tuple[bool, str, Optional[int]]:
        """模拟MDB写入（用于非Windows平台）"""
        try:
            # 生成模拟ID
            import random
            new_id = random.randint(10000, 99999)
            
            logger.debug(f"🔧 模拟MDB写入成功，模拟ID: {new_id}")
            logger.debug(f"📝 模拟数据: {employee_data}")
            
            return True, f"模拟写入成功 (平台: {self.platform_name})", new_id
            
        except Exception as e:
            logger.error(f"❌ 模拟写入失败: {e}")
            return False, f"模拟写入时出错: {e}", None
    
    def delete_work_time_by_id(self, entry_id: str) -> Tuple[bool, str]:
        """
        根据 ID 从MDB删除一条记录，并返回 (ok, msg)
        25.06.26 增加多平台支持
        """
        if self.is_windows and self.win32_available:
            return self._windows_delete_work_time(entry_id)
        else:
            return self._mock_delete_work_time(entry_id)
    
    def _windows_delete_work_time(self, entry_id: str) -> Tuple[bool, str]:
        """Windows环境下的真实MDB删除"""
        import win32com.client
        import pythoncom
        
        pythoncom.CoInitialize()
        access = None
        try:
            access = win32com.client.Dispatch("Access.Application")
            access.OpenCurrentDatabase(self.db_path)
            db = access.CurrentDb()

            # 使用验证过的DELETE语句
            sql = f"DELETE FROM 元作業時間 WHERE ID = {entry_id}"
            db.Execute(sql)
            
            logger.debug(f"✅ Windows MDB删除成功，ID: {entry_id}")
            return True, "レコード削除成功。"

        except Exception as e:
            logger.error(f"❌ Windows MDB删除失败: {e}")
            return False, f"データベースからの削除中にエラー発生: {e}"

        finally:
            if access:
                try:
                    access.CloseCurrentDatabase()
                    access.Quit()
                except: 
                    pass
            pythoncom.CoUninitialize()
    
    def _mock_delete_work_time(self, entry_id: str) -> Tuple[bool, str]:
        """模拟MDB删除（用于非Windows平台）"""
        try:
            logger.debug(f"🔧 模拟MDB删除成功，ID: {entry_id}")
            return True, f"模拟删除成功 (平台: {self.platform_name})"
            
        except Exception as e:
            logger.error(f"❌ 模拟删除失败: {e}")
            return False, f"模拟删除时出错: {e}"
    
    def get_user_data(self, employee_id: str, start_date: str, end_date: str) -> List[Dict]:
        """
        获取用户数据（30天同步用）
        25.06.26 增加多平台支持
        """
        if self.is_windows and self.win32_available:
            return self._windows_get_user_data(employee_id, start_date, end_date)
        else:
            return self._mock_get_user_data(employee_id, start_date, end_date)
    
    def get_all_data_by_date(self, target_date: str, limit: int = 100) -> List[Dict]:
        """
        获取指定日期的所有数据（不限员工ID）
        25.06.26 新增功能 - 支持查询特定日期的所有记录
        """
        if self.is_windows and self.win32_available:
            return self._windows_get_all_data_by_date(target_date, limit)
        else:
            return self._mock_get_all_data_by_date(target_date, limit)
    
    def _windows_get_user_data(self, employee_id: str, start_date: str, end_date: str) -> List[Dict]:
        """Windows环境下的真实数据查询"""
        import win32com.client
        import pythoncom
        
        pythoncom.CoInitialize()
        access = None
        results = []
        
        try:
            access = win32com.client.Dispatch("Access.Application")
            access.OpenCurrentDatabase(self.db_path)
            db = access.CurrentDb()

            # 查询SQL
            sql = f"""SELECT * FROM 元作業時間 
                     WHERE 従業員ｺｰﾄﾞ = '{employee_id}' 
                     AND 日付 BETWEEN #{start_date}# AND #{end_date}#
                     ORDER BY 日付 DESC"""
            
            rs = db.OpenRecordset(sql)
            
            while not rs.EOF:
                record = {}
                for i in range(rs.Fields.Count):
                    field = rs.Fields(i)
                    record[field.Name] = field.Value
                results.append(record)
                rs.MoveNext()
            
            rs.Close()
            logger.debug(f"✅ Windows查询成功，返回{len(results)}条记录")
            
        except Exception as e:
            logger.error(f"❌ Windows查询失败: {e}")
            
        finally:
            if access:
                try:
                    access.CloseCurrentDatabase()
                    access.Quit()
                except: 
                    pass
            pythoncom.CoUninitialize()
        
        return results
    
    def _windows_get_all_data_by_date(self, target_date: str, limit: int) -> List[Dict]:
        """Windows环境下查询指定日期的所有数据"""
        import win32com.client
        import pythoncom
        
        pythoncom.CoInitialize()
        access = None
        results = []
        
        try:
            access = win32com.client.Dispatch("Access.Application")
            access.OpenCurrentDatabase(self.db_path)
            db = access.CurrentDb()

            # 查询指定日期的所有数据
            sql = f"""SELECT TOP {limit} * FROM 元作業時間 
                     WHERE 日付 = #{target_date}#
                     ORDER BY ID DESC"""
            
            logger.debug(f"🔍 执行SQL查询: {sql}")
            rs = db.OpenRecordset(sql)
            
            while not rs.EOF:
                record = {}
                for i in range(rs.Fields.Count):
                    field = rs.Fields(i)
                    record[field.Name] = field.Value
                results.append(record)
                rs.MoveNext()
            
            rs.Close()
            logger.debug(f"✅ Windows查询成功，返回{len(results)}条记录")
            
        except Exception as e:
            logger.error(f"❌ Windows查询失败: {e}")
            
        finally:
            if access:
                try:
                    access.CloseCurrentDatabase()
                    access.Quit()
                except: 
                    pass
            pythoncom.CoUninitialize()
        
        return results
    
    def _mock_get_user_data(self, employee_id: str, start_date: str, end_date: str) -> List[Dict]:
        """模拟数据查询（用于非Windows平台）"""
        try:
            # 生成一些模拟数据
            mock_data = []
            for i in range(3):  # 模拟3条记录
                mock_data.append({
                    'ID': 10000 + i,
                    'employee_id': employee_id,
                    'date': start_date,
                    'model': f'Mock_Model_{i}',
                    'category': 'Mock',
                    'item': f'Mock_Item_{i}',
                    'time': 8.0,
                    'department': 'Mock_Dept'
                })
            
            logger.debug(f"🔧 模拟查询成功，返回{len(mock_data)}条记录")
            return mock_data
            
        except Exception as e:
            logger.error(f"❌ 模拟查询失败: {e}")
            return []
    
    def _mock_get_all_data_by_date(self, target_date: str, limit: int) -> List[Dict]:
        """模拟查询指定日期的所有数据"""
        try:
            # 生成一些模拟数据
            mock_data = []
            for i in range(min(5, limit)):  # 模拟最多5条记录
                mock_data.append({
                    'ID': 30000 + i,
                    '従業員ｺｰﾄﾞ': f'EMP{i:03d}',
                    '日付': target_date,
                    '機種': f'Mock_Model_{i}',
                    '号機': f'{i+1:03d}',
                    '工場製番': f'F{i:03d}',
                    '工事番号': f'P{i:03d}',
                    'ﾕﾆｯﾄ番号': f'U{i:03d}',
                    '区分': 'MOCK',
                    '項目': f'Mock_Item_{i}',
                    '時間': 7.5 + i * 0.5,
                    '所属ｺｰﾄﾞ': f'DEPT{i:02d}'
                })
            
            logger.debug(f"🔧 模拟日期查询成功，返回{len(mock_data)}条记录")
            return mock_data
            
        except Exception as e:
            logger.error(f"❌ 模拟日期查询失败: {e}")
            return []
    
    def get_platform_info(self) -> Dict:
        """获取平台信息"""
        return {
            "platform": self.platform_name,
            "is_windows": self.is_windows,
            "is_linux": self.is_linux,
            "is_darwin": self.is_darwin,
            "win32_available": self.win32_available,
            "pythoncom_available": self.pythoncom_available,
            "mdb_tools_available": getattr(self, 'mdb_tools_available', False),
            "db_path": self.db_path,
            "can_real_mdb_access": self.is_windows and self.win32_available
        }


# 异步包装器类
class AsyncMDBClient:
    """异步MDB客户端 - 将同步操作包装为异步"""
    
    def __init__(self, db_path: str = None):
        self.sync_client = MultiPlatformMDBClient(db_path)
        logger.info(f"🚀 异步MDB客户端初始化完成 - 平台: {self.sync_client.platform_name}")
    
    async def write_work_time(self, employee_data: Dict) -> Tuple[bool, str, Optional[int]]:
        """异步写入工时记录"""
        loop = asyncio.get_event_loop()
        return await loop.run_in_executor(None, self.sync_client.write_work_time, employee_data)
    
    async def delete_work_time_by_id(self, entry_id: str) -> Tuple[bool, str]:
        """异步删除工时记录"""
        loop = asyncio.get_event_loop()
        return await loop.run_in_executor(None, self.sync_client.delete_work_time_by_id, entry_id)
    
    async def get_user_data(self, employee_id: str, start_date: str, end_date: str) -> List[Dict]:
        """异步获取用户数据"""
        loop = asyncio.get_event_loop()
        return await loop.run_in_executor(None, self.sync_client.get_user_data, employee_id, start_date, end_date)
    
    async def get_all_data_by_date(self, target_date: str, limit: int = 100) -> List[Dict]:
        """异步获取指定日期的所有数据"""
        loop = asyncio.get_event_loop()
        return await loop.run_in_executor(None, self.sync_client.get_all_data_by_date, target_date, limit)
    
    def get_platform_info(self) -> Dict:
        """获取平台信息"""
        return self.sync_client.get_platform_info()


# 兼容性函数 - 保持与原db_utils.py的接口一致
def write_work_time(employee_data: Dict) -> Tuple[bool, str, Optional[int]]:
    """
    兼容性函数 - 插入一条记录到MDB
    25.06.26 增加多平台自动适配
    """
    client = MultiPlatformMDBClient()
    return client.write_work_time(employee_data)


def delete_work_time_by_id(entry_id: str) -> Tuple[bool, str]:
    """
    兼容性函数 - 根据ID删除MDB记录
    25.06.26 增加多平台自动适配
    """
    client = MultiPlatformMDBClient()
    return client.delete_work_time_by_id(entry_id)


# 测试函数
def test_platform_detection():
    """测试平台检测功能"""
    client = MultiPlatformMDBClient()
    info = client.get_platform_info()
    
    print("🧪 平台检测测试结果:")
    for key, value in info.items():
        print(f"  {key}: {value}")
    
    return info


if __name__ == "__main__":
    # 运行平台检测测试
    test_platform_detection()
