# №02025/06/27 + 日本东京时间11：24 + Server6字段映射器
# 处理Server5-Server6之间的字段映射兼容性

from typing import Dict, Any, Optional
from datetime import datetime
import logging
import re
import decimal

logger = logging.getLogger(__name__)

class Server6FieldMapper:
    """Server6字段映射器 - 处理Server5和Server6之间的字段兼容性"""
    
    def __init__(self):
        # Server5 (PostgreSQL) -> Server6 (MDB) 字段映射
        self.pg_to_mdb_mapping = {
            # PostgreSQL entries表字段 -> MDB 元作業時間表字段
            'employee_id': '従業員ｺｰﾄﾞ',
            'entry_date': '日付',
            'model': '機種',
            'number': '号機',
            'factory_number': '工場製番',
            'project_number': '工事番号',
            'unit_number': 'ﾕﾆｯﾄ番号',
            'category': '区分',
            'item': '項目',
            'duration': '時間',          # 修复: time_hours -> duration
            'department': '所属ｺｰﾄﾞ',   # 修复: department_code -> department
            'ts': '更新時間',            # 修复: updated_at -> ts
            'external_id': 'ID'
        }
        
        # MDB -> PostgreSQL 反向映射
        self.mdb_to_pg_mapping = {v: k for k, v in self.pg_to_mdb_mapping.items()}
    
    def _round_duration(self, value) -> float:
        """对duration值进行精度控制，避免浮点数精度问题"""
        if value is None:
            return 0.0
        
        try:
            # 使用decimal进行精确的四舍五入
            decimal_value = decimal.Decimal(str(value))
            # 四舍五入到2位小数
            rounded_value = decimal_value.quantize(decimal.Decimal('0.01'), rounding=decimal.ROUND_HALF_UP)
            return float(rounded_value)
        except (ValueError, TypeError, decimal.InvalidOperation):
            # 如果转换失败，返回0.0
            logger.warning(f"⚠️ duration值转换失败: {value}, 使用默认值0.0")
            return 0.0
    
    def _format_number_field(self, value) -> str:
        """对号機字段进行格式化，确保数字类型转换为整数格式的字符串"""
        if value is None:
            return ""
        
        try:
            # 如果是数字类型，转换为整数再转为字符串
            if isinstance(value, (int, float)):
                # 对于浮点数，先四舍五入到整数
                if isinstance(value, float):
                    value = round(value)
                return str(int(value))
            else:
                # 如果是字符串，尝试转换为数字再格式化
                numeric_value = float(value)
                return str(int(round(numeric_value)))
        except (ValueError, TypeError):
            # 如果转换失败，返回原始值的字符串形式
            return str(value) if value is not None else ""
    
    def pg_to_mdb(self, pg_data: Dict[str, Any]) -> Dict[str, Any]:
        """将PostgreSQL格式的数据转换为MDB格式"""
        mdb_data = {}
        
        for pg_field, pg_value in pg_data.items():
            mdb_field = self.pg_to_mdb_mapping.get(pg_field, pg_field)
            
            # 特殊字段处理
            if pg_field == 'entry_date' and isinstance(pg_value, str):
                # 日期格式转换: YYYY-MM-DD -> YYYY/MM/DD
                mdb_data[mdb_field] = pg_value.replace('-', '/')
            elif pg_field == 'ts' and isinstance(pg_value, datetime):
                # 时间戳转换
                mdb_data[mdb_field] = pg_value
            elif pg_field == 'duration':
                # 工时转换 - 使用精度控制
                mdb_data[mdb_field] = self._round_duration(pg_value)
            else:
                mdb_data[mdb_field] = pg_value
        
        return mdb_data
    
    def mdb_to_pg(self, mdb_data: Dict[str, Any]) -> Dict[str, Any]:
        """将MDB格式的数据转换为PostgreSQL格式"""
        pg_data = {}
        
        for mdb_field, mdb_value in mdb_data.items():
            pg_field = self.mdb_to_pg_mapping.get(mdb_field, mdb_field)
            
            # 特殊字段处理
            if mdb_field == '日付' and isinstance(mdb_value, str):
                # 日期格式转换: YYYY/MM/DD -> YYYY-MM-DD
                pg_data[pg_field] = mdb_value.replace('/', '-')
            elif mdb_field == '時間':
                # 工时转换 - 使用精度控制
                pg_data[pg_field] = self._round_duration(mdb_value)
            elif mdb_field == '号機':
                # 号機字段特殊处理 - 确保数字类型转换为整数格式的字符串
                pg_data[pg_field] = self._format_number_field(mdb_value)
            elif mdb_field in ['区分', '項目']:
                # 整数字段
                pg_data[pg_field] = int(mdb_value) if mdb_value is not None else 0
            elif mdb_field == 'ID':
                # external_id字段
                pg_data[pg_field] = int(mdb_value) if mdb_value is not None else None
            else:
                # 字符串字段 - 处理日语编码
                if isinstance(mdb_value, str):
                    pg_data[pg_field] = mdb_value.strip()
                else:
                    pg_data[pg_field] = mdb_value
        
        return pg_data
    
    def validate_mdb_data(self, mdb_data: Dict[str, Any]) -> bool:
        """验证MDB数据格式"""
        required_fields = ['従業員ｺｰﾄﾞ', '日付', '区分', '項目', '時間']
        
        for field in required_fields:
            if field not in mdb_data or mdb_data[field] is None:
                logger.warning(f"⚠️ MDB数据缺少必需字段: {field}")
                return False
        
        # 验证日期格式
        date_value = mdb_data.get('日付')
        if date_value and isinstance(date_value, str):
            if not re.match(r'\d{4}/\d{2}/\d{2}', date_value):
                logger.warning(f"⚠️ 日期格式不正确: {date_value}")
                return False
        
        return True
    
    def validate_pg_data(self, pg_data: Dict[str, Any]) -> bool:
        """验证PostgreSQL数据格式"""
        required_fields = ['employee_id', 'entry_date', 'category', 'item', 'duration']
        
        for field in required_fields:
            if field not in pg_data or pg_data[field] is None:
                logger.warning(f"⚠️ PostgreSQL数据缺少必需字段: {field}")
                return False
        
        return True
    
    def sanitize_japanese_text(self, text: str) -> str:
        """清理日语文本，处理编码问题"""
        if not isinstance(text, str):
            return str(text) if text is not None else ""
        
        # 移除控制字符
        text = re.sub(r'[\x00-\x1f\x7f-\x9f]', '', text)
        
        # 标准化空格
        text = re.sub(r'\s+', ' ', text).strip()
        
        return text

# 全局映射器实例
field_mapper = Server6FieldMapper()
