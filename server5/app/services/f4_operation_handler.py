# server5/app/services/f4_operation_handler.py
# f4操作处理器服务 - 处理客户端UI操作并生成队列任务  
# 20250626/ f2-f6

import asyncio
import logging
from typing import Optional, Dict, List
from datetime import datetime
import sys
from pathlib import Path
sys.path.append(str(Path(__file__).parent.parent.parent))

from app.database import IMDBClient, RedisClient, MongoDBClient
from config.config import OPERATION_TIMEOUT

logger = logging.getLogger(__name__)

class OperationHandlerService:
    """f4: 操作处理器服务 - 处理客户端UI操作并生成队列任务"""
    
    def __init__(self):
        self.imdb_client = IMDBClient()
        self.redis_client = RedisClient()
        self.mongo_client = MongoDBClient()
        
        self.is_running = False
        
        logger.info("🎮 f4操作处理器初始化完成")
    
    async def start(self) -> bool:
        """启动操作处理服务"""
        try:
            # 连接数据库
            connections = await asyncio.gather(
                self.imdb_client.connect(),
                self.redis_client.connect(),
                self.mongo_client.connect(),
                return_exceptions=True
            )
            
            if not all(isinstance(conn, bool) and conn for conn in connections[:3]):
                logger.error("❌ 数据库连接失败，无法启动操作处理服务")
                return False
            
            self.is_running = True
            
            logger.info("✅ f4操作处理服务启动成功")
            
            # 记录启动事件
            # await self.redis_client.log_sync_event("service_start", {
            #     "service": "f4_operation_handler",
            #     "operation_timeout": OPERATION_TIMEOUT
            # })
            
            return True
            
        except Exception as e:
            logger.error(f"❌ f4操作处理服务启动失败: {e}")
            # await self._log_error("service_start_failed", str(e))
            return False
    
    async def stop(self):
        """停止操作处理服务"""
        try:
            self.is_running = False
            
            # 断开数据库连接
            await asyncio.gather(
                self.imdb_client.disconnect(),
                self.redis_client.disconnect(),
                self.mongo_client.disconnect(),
                return_exceptions=True
            )
            
            logger.info("🔌 f4操作处理服务已停止")
            
        except Exception as e:
            logger.error(f"❌ f4操作处理服务停止失败: {e}")
    
    async def handle_insert_operation(self, entry_data: Dict, user_id: str = None) -> Dict:
        """处理INSERT操作 - 客户端创建新记录"""
        try:
            start_time = asyncio.get_event_loop().time()
            
            logger.info(f"➕ 处理INSERT操作: {entry_data.get('employee_id')}")
            
            # 验证数据
            if not self._validate_entry_data(entry_data):
                raise ValueError("入力数据验证失败")
            
            # 检查必需分区是否存在
            entry_date = entry_data.get('entry_date')
            if entry_date:
                month_code = self._get_month_code(entry_date)
                await self.imdb_client.create_partition_if_needed(month_code)
            
            # 插入记录到PostgreSQL (external_id = NULL)
            entry_data_copy = entry_data.copy()
            entry_data_copy['external_id'] = None  # INSERT时external_id为NULL
            entry_data_copy['source'] = 'user'  # 2025/07/03 +13:40+ 明确标记为用户操作，触发PostgreSQL触发器
            
            entry_id = await self.imdb_client.create_entry(entry_data_copy)
            
            if not entry_id:
                raise RuntimeError("PostgreSQL插入失败")
            
            logger.info(f"✅ PostgreSQL插入成功: entry_id={entry_id}")
            
            # 2025/07/03 +13:40+ 注意：此时PostgreSQL触发器会自动将任务加入队列，f2会处理MDB同步
            # f2成功同步到MDB后，会自动将source更新为'system'
            
            # 记录操作到日志
            # await self.mongo_client.log_ui_operation({
            #     "operation_type": "INSERT",
            #     "entry_id": entry_id,
            #     "employee_id": entry_data.get('employee_id'),
            #     "user_id": user_id,
            #     "status": "entries_inserted",
            #     "data": entry_data
            # })
            
            # 计算执行时间
            execution_time = (asyncio.get_event_loop().time() - start_time) * 1000
            
            # 更新计数器
            await self.redis_client.increment_counter("server5:ui_insert_count")
            
            return {
                "status": "success",
                "operation": "INSERT",
                "entry_id": entry_id,
                "message": "记录已创建，等待MDB同步",
                "execution_time_ms": execution_time
            }
            
        except Exception as e:
            logger.error(f"❌ INSERT操作失败: {e}")
            # await self._log_error("insert_operation_failed", str(e), {
            #     "entry_data": entry_data,
            #     "user_id": user_id
            # })
            return {
                "status": "error",
                "operation": "INSERT",
                "error_message": str(e)
            }
    
    async def handle_update_operation(self, entry_id: int, entry_data: Dict, user_id: str = None) -> Dict:
        """处理UPDATE操作 - 客户端更新现有记录"""
        try:
            start_time = asyncio.get_event_loop().time()
            
            logger.info(f"🔄 处理UPDATE操作: entry_id={entry_id}")
            
            # 验证数据
            if not self._validate_entry_data(entry_data):
                raise ValueError("入力数据验证失败")
            
            # 检查记录是否存在
            existing_entry = await self.imdb_client.fetch_one(
                "SELECT * FROM entries WHERE id = $1", entry_id
            )
            
            if not existing_entry:
                raise ValueError(f"记录不存在: entry_id={entry_id}")
            
            # 检查是否有external_id
            if not existing_entry.get('external_id'):
                raise ValueError("记录缺少external_id，无法执行UPDATE操作")
            
            # 2025/07/03 +13:40+ 更新PostgreSQL记录，确保标记为用户操作以触发同步流程
            update_query = """
                UPDATE entries SET
                    employee_id = $1, entry_date = $2, model = $3, number = $4,
                    factory_number = $5, project_number = $6, unit_number = $7,
                    category = $8, item = $9, duration = $10, department = $11,
                    source = 'user',  -- 2025/07/03 +13:40+ 标记为用户操作，触发PostgreSQL触发器
                    ts = NOW()  -- 250626，第二阶段: 修复字段名
                WHERE id = $12
            """
            
            await self.imdb_client.execute_command(
                update_query,
                entry_data['employee_id'],
                entry_data['entry_date'],
                entry_data.get('model'),
                entry_data.get('number'),
                entry_data.get('factory_number'),
                entry_data.get('project_number'),
                entry_data.get('unit_number'),
                entry_data['category'],
                entry_data['item'],
                entry_data['duration'],
                entry_data['department'],
                entry_id
            )
            
            logger.info(f"✅ PostgreSQL更新成功: entry_id={entry_id}")
            
            # 记录操作到日志
            # await self.mongo_client.log_ui_operation({
            #     "operation_type": "UPDATE",
            #     "entry_id": entry_id,
            #     "external_id": existing_entry.get('external_id'),
            #     "employee_id": entry_data.get('employee_id'),
            #     "user_id": user_id,
            #     "status": "entries_updated",
            #     "data": entry_data
            # })
            
            # 计算执行时间
            execution_time = (asyncio.get_event_loop().time() - start_time) * 1000
            
            # 更新计数器
            await self.redis_client.increment_counter("server5:ui_update_count")
            
            return {
                "status": "success",
                "operation": "UPDATE",
                "entry_id": entry_id,
                "external_id": existing_entry.get('external_id'),
                "message": "记录已更新，等待MDB同步",
                "execution_time_ms": execution_time
            }
            
        except Exception as e:
            logger.error(f"❌ UPDATE操作失败: {e}")
            # await self._log_error("update_operation_failed", str(e), {
            #     "entry_id": entry_id,
            #     "entry_data": entry_data,
            #     "user_id": user_id
            # })
            return {
                "status": "error",
                "operation": "UPDATE",
                "entry_id": entry_id,
                "error_message": str(e)
            }
    
    async def handle_delete_operation(self, entry_id: int, user_id: str = None) -> Dict:
        """处理DELETE操作 - 客户端删除记录"""
        try:
            start_time = asyncio.get_event_loop().time()
            
            logger.info(f"❌ 处理DELETE操作: entry_id={entry_id}")
            
            # 检查记录是否存在
            existing_entry = await self.imdb_client.fetch_one(
                "SELECT * FROM entries WHERE id = $1", entry_id
            )
            
            if not existing_entry:
                raise ValueError(f"记录不存在: entry_id={entry_id}")
            
            external_id = existing_entry.get('external_id')
            employee_id = existing_entry.get('employee_id')
            
            # 2025/07/03 +13:40+ 先标记为用户操作以触发PostgreSQL触发器，然后删除记录
            # 注意：DELETE触发器会在删除前处理OLD记录，所以需要先更新source
            if existing_entry.get('source') != 'user':
                await self.imdb_client.execute_command(
                    "UPDATE entries SET source = 'user' WHERE id = $1", entry_id
                )
            
            # 删除PostgreSQL记录（触发器会处理source='user'的OLD记录）
            await self.imdb_client.execute_command(
                "DELETE FROM entries WHERE id = $1", entry_id
            )
            
            logger.info(f"✅ PostgreSQL删除成功: entry_id={entry_id}")
            
            # 记录操作到日志
            # await self.mongo_client.log_ui_operation({
            #     "operation_type": "DELETE",
            #     "entry_id": entry_id,
            #     "external_id": external_id,
            #     "employee_id": employee_id,
            #     "user_id": user_id,
            #     "status": "entries_deleted"
            # })
            
            # 计算执行时间
            execution_time = (asyncio.get_event_loop().time() - start_time) * 1000
            
            # 更新计数器
            await self.redis_client.increment_counter("server5:ui_delete_count")
            
            return {
                "status": "success",
                "operation": "DELETE",
                "entry_id": entry_id,
                "external_id": external_id,
                "message": "记录已删除，等待MDB同步",
                "execution_time_ms": execution_time
            }
            
        except Exception as e:
            logger.error(f"❌ DELETE操作失败: {e}")
            # await self._log_error("delete_operation_failed", str(e), {
            #     "entry_id": entry_id,
            #     "user_id": user_id
            # })
            return {
                "status": "error",
                "operation": "DELETE",
                "entry_id": entry_id,
                "error_message": str(e)
            }
    
    async def batch_operation(self, operations: List[Dict], user_id: str = None) -> Dict:
        """批量操作处理"""
        try:
            start_time = asyncio.get_event_loop().time()
            
            logger.info(f"🗃️ 处理批量操作: {len(operations)}个操作")
            
            results = []
            success_count = 0
            error_count = 0
            
            for operation in operations:
                try:
                    op_type = operation.get('type')
                    
                    if op_type == 'INSERT':
                        result = await self.handle_insert_operation(
                            operation.get('data'), user_id
                        )
                    elif op_type == 'UPDATE':
                        result = await self.handle_update_operation(
                            operation.get('entry_id'),
                            operation.get('data'),
                            user_id
                        )
                    elif op_type == 'DELETE':
                        result = await self.handle_delete_operation(
                            operation.get('entry_id'), user_id
                        )
                    else:
                        result = {
                            "status": "error",
                            "error_message": f"未知操作类型: {op_type}"
                        }
                    
                    results.append(result)
                    
                    if result.get('status') == 'success':
                        success_count += 1
                    else:
                        error_count += 1
                        
                except Exception as e:
                    error_count += 1
                    results.append({
                        "status": "error",
                        "error_message": str(e)
                    })
            
            # 计算执行时间
            execution_time = (asyncio.get_event_loop().time() - start_time) * 1000
            
            # 记录批量操作结果
            batch_result = {
                "status": "completed",
                "total_operations": len(operations),
                "success_count": success_count,
                "error_count": error_count,
                "execution_time_ms": execution_time,
                "results": results
            }
            
            # await self.mongo_client.log_batch_operation({
            #     "user_id": user_id,
            #     "operation_count": len(operations),
            #     "success_count": success_count,
            #     "error_count": error_count,
            #     "execution_time_ms": execution_time
            # })
            
            # 更新计数器
            await self.redis_client.increment_counter("server5:batch_operation_count")
            
            logger.info(f"✅ 批量操作完成: {success_count}成功 {error_count}失败")
            
            return batch_result
            
        except Exception as e:
            logger.error(f"❌ 批量操作失败: {e}")
            return {
                "status": "error",
                "error_message": str(e)
            }
    
    def _validate_entry_data(self, entry_data: Dict) -> bool:
        """验证entry数据"""
        required_fields = ['employee_id', 'entry_date', 'category', 'item', 'duration', 'department']
        
        for field in required_fields:
            if field not in entry_data or entry_data[field] is None:
                logger.error(f"❌ 缺少必需字段: {field}")
                return False
        
        # 验证时间字段
        try:
            duration = float(entry_data['duration'])
            if duration <= 0:
                logger.error("❌ 时间必须大于0")
                return False
        except (ValueError, TypeError):
            logger.error("❌ 时间格式无效")
            return False
        
        # 验证日期字段
        entry_date = entry_data.get('entry_date')
        if isinstance(entry_date, str):
            try:
                datetime.strptime(entry_date, '%Y/%m/%d')
            except ValueError:
                logger.error("❌ 日期格式无效")
                return False
        
        return True
    
    def _get_month_code(self, entry_date) -> str:
        """获取月份代码"""
        if isinstance(entry_date, str):
            date_obj = datetime.strptime(entry_date, '%Y/%m/%d')
        else:
            date_obj = entry_date
        
        return f"{date_obj.year}{date_obj.month:02d}"
    
    # async def _log_error(self, error_type: str, error_message: str, context: Dict = None):
    #     """记录错误到MongoDB"""
    #     try:
    #         await self.mongo_client.log_error({
    #             "error_type": error_type,
    #             "error_message": error_message,
    #             "context": context or {},
    #             "function_name": "f4_operation_handler",
    #             "severity": "error"
    #         })
    #         
    #         await self.redis_client.increment_counter("server5:error_count")
    #         
    #     except Exception as e:
    #         logger.error(f"❌ 记录错误日志失败: {e}")
    
    async def get_status(self) -> Dict:
        """获取操作处理服务状态"""
        try:
            return {
                "service": "f4_operation_handler",
                "is_running": self.is_running,
                "operation_timeout": OPERATION_TIMEOUT,
                "databases": {
                    "imdb": await self.imdb_client.get_connection_status()
                }
            }
            
        except Exception as e:
            return {
                "service": "f4_operation_handler",
                "is_running": False,
                "error": str(e)
            }
    
    async def get_entry_info(self, entry_id: int) -> Dict:
        """获取记录信息"""
        try:
            entry = await self.imdb_client.fetch_one(
                "SELECT * FROM entries WHERE id = $1", entry_id
            )
            
            if not entry:
                return {
                    "status": "not_found",
                    "entry_id": entry_id
                }
            
            # 检查队列状态
            queue_status = await self.imdb_client.fetch_one(
                "SELECT * FROM entries_push_queue WHERE entry_id = $1 ORDER BY created_at DESC LIMIT 1",
                entry_id
            )
            
            return {
                "status": "found",
                "entry": dict(entry),
                "queue_status": dict(queue_status) if queue_status else None,
                "has_external_id": entry.get('external_id') is not None
            }
            
        except Exception as e:
            return {
                "status": "error",
                "entry_id": entry_id,
                "error_message": str(e)
            } 