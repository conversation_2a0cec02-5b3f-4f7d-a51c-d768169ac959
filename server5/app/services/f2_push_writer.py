# server5/app/services/f2_push_writer_fixed.py
# f2推送回写引擎服务 - 修复版本：直接轮询PostgreSQL队列表
# 20250626/ f2-f6
# 250630，修复版本: 直接轮询entries_push_queue表

import asyncio
import logging
from typing import Optional, Dict, List
from datetime import datetime
import sys
from pathlib import Path
sys.path.append(str(Path(__file__).parent.parent.parent))

from app.database import IMDBClient, RedisClient, MongoDBClient
from app.utils.server6_client import Server6Client
from config.config import WORKER_CONCURRENCY, SYNC_TIMEOUT

logger = logging.getLogger(__name__)

class PushWriterServiceFixed:
    """f2: 推送回写引擎服务 - 修复版本：直接轮询PostgreSQL队列表"""
    
    def __init__(self):
        self.imdb_client = IMDBClient()
        self.redis_client = RedisClient()
        self.mongo_client = MongoDBClient()
        self.server6_client = Server6Client()
        
        self.is_running = False
        self.worker_tasks: List[asyncio.Task] = []
        
        logger.info("📤 f2推送回写引擎初始化完成 (修复版本: 直接轮询PostgreSQL)")
    
    async def start(self) -> bool:
        """启动推送回写服务"""
        try:
            # 连接数据库
            connections = await asyncio.gather(
                self.imdb_client.connect(),
                self.redis_client.connect(),
                self.mongo_client.connect(),
                return_exceptions=True
            )
            
            # 检查连接
            if not all(c for c in connections):
                logger.error(f"❌ 数据库连接失败: {connections}")
                return False
            
            # 启动工作线程
            self.is_running = True
            for i in range(WORKER_CONCURRENCY):
                worker_task = asyncio.create_task(self._push_worker(f"worker_{i+1}"))
                self.worker_tasks.append(worker_task)
            
            logger.info(f"✅ f2推送回写服务启动成功，工作线程数: {WORKER_CONCURRENCY}")
            
            # 记录启动事件
            await self.redis_client.log_sync_event("service_start", {
                "service": "f2_push_writer_fixed",
                "workers": WORKER_CONCURRENCY
            })
            
            return True
            
        except Exception as e:
            logger.error(f"❌ f2推送回写服务启动失败: {e}")
            await self._log_error("service_start_failed", str(e))
            return False
    
    async def stop(self):
        """停止推送回写服务"""
        try:
            self.is_running = False
            
            # 取消所有工作任务
            for task in self.worker_tasks:
                if not task.done():
                    task.cancel()
            
            # 等待任务完成
            if self.worker_tasks:
                await asyncio.gather(*self.worker_tasks, return_exceptions=True)
            
            # 断开数据库连接
            await asyncio.gather(
                self.imdb_client.disconnect(),
                self.redis_client.disconnect(),
                self.mongo_client.disconnect(),
                self.server6_client.disconnect(),
                return_exceptions=True
            )
            
            logger.info("🔌 f2推送回写服务已停止")
            
        except Exception as e:
            logger.error(f"❌ f2推送回写服务停止失败: {e}")
    
    async def _push_worker(self, worker_id: str):
        """推送工作线程 - 直接轮询PostgreSQL队列表"""
        logger.info(f"🚀 推送工作线程启动: {worker_id}")
        
        while self.is_running:
            try:
                # 使用事务锁定未同步的队列项，防止重复处理
                async with self.imdb_client.pool.acquire() as conn:
                    async with conn.transaction():
                        # 2025/07/03 + 15:40 + 获取并锁定一个未同步的队列项（增加失败计数）
                        queue_items = await conn.fetch("""
                            SELECT q.queue_id, q.operation, q.entry_id, q.created_ts,
                                   COALESCE(q.retry_count, 0) as retry_count,
                                   q.last_error
                            FROM entries_push_queue q
                            WHERE q.synced = FALSE 
                              AND COALESCE(q.retry_count, 0) < 10
                            ORDER BY q.created_ts ASC
                            LIMIT 1
                            FOR UPDATE SKIP LOCKED
                        """)
                        
                        if queue_items:
                            item = dict(queue_items[0])
                            # 在同一个事务中处理队列项
                            await self._process_queue_item_in_transaction(conn, item, worker_id)
                        else:
                            # 没有任务时短暂休息
                            await asyncio.sleep(2)
                    
                # 更新工作线程状态
                await self.redis_client.update_worker_status(worker_id, "active")
                
            except asyncio.CancelledError:
                break
            except Exception as e:
                logger.error(f"❌ 推送工作线程异常 {worker_id}: {e}")
                await self._log_error("push_worker_error", str(e), {"worker_id": worker_id})
                await asyncio.sleep(5)  # 错误后等待重试
        
        logger.info(f"🔌 推送工作线程停止: {worker_id}")
    
    async def _process_queue_item_in_transaction(self, conn, item: Dict, worker_id: str):
        """2025/07/03 + 16:10 + 在事务中处理队列项（增强失败重试机制）"""
        start_time = asyncio.get_event_loop().time()
        queue_id = item.get('queue_id')
        operation = item.get('operation')
        entry_id = item.get('entry_id')
        retry_count = item.get('retry_count', 0)
        
        try:
            logger.info(f"📋 2025/07/03 + 16:10 + 处理队列项: {operation} - entry_id={entry_id} - queue_id={queue_id} - 重试次数={retry_count}")
            
            # 根据操作类型执行相应的MDB操作
            if operation == 'INSERT':
                await self._handle_insert_operation_in_transaction(conn, item, worker_id)
            elif operation == 'UPDATE':
                await self._handle_update_operation_in_transaction(conn, item, worker_id)
            elif operation == 'DELETE':
                await self._handle_delete_operation_in_transaction(conn, item, worker_id)
            else:
                raise ValueError(f"2025/07/03 + 16:10 + 未知操作类型: {operation}")
            
            # 记录执行时间
            execution_time = (asyncio.get_event_loop().time() - start_time) * 1000
            
            await self.mongo_client.log_performance_metric({
                "metric_type": "push_processing",
                "value": execution_time,
                "unit": "milliseconds",
                "tags": {"worker_id": worker_id, "queue_id": str(queue_id), "retry_count": retry_count}
            })
            
            await self.redis_client.increment_counter("server5:push_processed")
            
        except Exception as e:
            # 2025/07/03 + 16:10 + 增强失败处理：重试计数和失败清理
            error_msg = str(e)
            new_retry_count = retry_count + 1
            
            logger.error(f"❌ 2025/07/03 + 16:10 + 队列项处理失败 (第{new_retry_count}次): {error_msg}")
            
            if new_retry_count >= 10:
                # 2025/07/03 + 16:10 + 超过最大重试次数，执行清理操作
                await self._handle_max_retries_exceeded(conn, item, error_msg, worker_id)
            else:
                # 2025/07/03 + 16:10 + 更新重试计数和错误信息
                await conn.execute("""
                    UPDATE entries_push_queue 
                    SET retry_count = $1, last_error = $2, updated_at = CURRENT_TIMESTAMP
                    WHERE queue_id = $3
                """, new_retry_count, error_msg, queue_id)
                
                logger.warning(f"⚠️ 2025/07/03 + 16:10 + 队列项将在下次循环重试: queue_id={queue_id}, retry_count={new_retry_count}")
            
            await self._log_error("queue_item_failed", error_msg, {
                **item, 
                "retry_count": new_retry_count,
                "worker_id": worker_id
            })
            
            # 不再抛出异常，让工作线程继续处理其他项目
    
    async def _handle_max_retries_exceeded(self, conn, item: Dict, error_msg: str, worker_id: str):
        """2025/07/03 + 15:40 + 处理超过最大重试次数的队列项"""
        queue_id = item.get('queue_id')
        entry_id = item.get('entry_id')
        operation = item.get('operation')
        
        try:
            logger.error(f"🚨 队列项超过最大重试次数，执行清理: queue_id={queue_id}, entry_id={entry_id}")
            
            # 1. 标记队列项为失败并清理
            await conn.execute("""
                DELETE FROM entries_push_queue 
                WHERE queue_id = $1
            """, queue_id)
            
            # 2. 如果是INSERT/UPDATE操作，需要清理entries表中的对应记录
            if operation in ['INSERT', 'UPDATE']:
                # 将entries表中的记录恢复为初始状态或删除
                entry_exists = await conn.fetchval(
                    "SELECT EXISTS(SELECT 1 FROM entries WHERE id = $1)", entry_id
                )
                
                if entry_exists:
                    if operation == 'INSERT':
                        # INSERT失败：删除PostgreSQL中的记录
                        await conn.execute("DELETE FROM entries WHERE id = $1", entry_id)
                        logger.info(f"🗑️ 已删除失败的INSERT记录: entry_id={entry_id}")
                    elif operation == 'UPDATE':
                        # UPDATE失败：恢复source为'system'状态
                        await conn.execute(
                            "UPDATE entries SET source = 'system' WHERE id = $1", entry_id
                        )
                        logger.info(f"🔄 已恢复失败的UPDATE记录状态: entry_id={entry_id}")
            
            # 3. 记录失败事件到日志
            await self.mongo_client.log_sync_operation({
                "operation_type": f"{operation}_FAILED_CLEANUP",
                "entry_id": entry_id,
                "queue_id": queue_id,
                "status": "max_retries_exceeded",
                "error_message": error_msg,
                "retry_count": 10,
                "worker_id": worker_id,
                "cleanup_action": "queue_deleted_entry_cleaned"
            })
            
            # 4. 发送通知给相关用户（如果有employee_id）
            employee_id = item.get('employee_id')
            if employee_id:
                await self._notify_user_of_failure(employee_id, operation, entry_id, error_msg)
            
            # 5. 更新统计计数器
            await self.redis_client.increment_counter("server5:failed_operations_cleaned")
            
            logger.warning(f"✅ 队列项清理完成: queue_id={queue_id}")
            
        except Exception as e:
            logger.error(f"❌ 清理失败的队列项时发生错误: {e}")
            # 最后的保障：强制删除队列项
            try:
                await conn.execute("DELETE FROM entries_push_queue WHERE queue_id = $1", queue_id)
                logger.info(f"🔧 强制删除队列项: queue_id={queue_id}")
            except:
                pass
    
    async def _notify_user_of_failure(self, employee_id: str, operation: str, entry_id: int, error_msg: str):
        """2025/07/03 + 15:40 + 通知用户操作失败"""
        try:
            notification_data = {
                "type": "operation_failure",
                "employee_id": employee_id,
                "operation": operation,
                "entry_id": entry_id,
                "error_message": error_msg,
                "timestamp": datetime.utcnow().isoformat(),
                "message": f"操作 {operation} 失败，已自动清理。请联系管理员或重新执行操作。"
            }
            
            # 发送到Redis通知队列
            await self.redis_client.push_notification(f"user:{employee_id}:notifications", notification_data)
            
            logger.info(f"📢 已通知用户操作失败: employee_id={employee_id}, operation={operation}")
            
        except Exception as e:
            logger.error(f"❌ 发送失败通知时出错: {e}")
    
    async def _handle_insert_operation_in_transaction(self, conn, item: Dict, worker_id: str):
        """在事务中处理INSERT操作"""
        try:
            queue_id = item['queue_id']
            entry_id = item['entry_id']
            
            # 获取完整的entry数据
            entry_data = await conn.fetchrow(
                "SELECT * FROM entries WHERE id = $1", entry_id
            )
            
            if not entry_data:
                raise ValueError(f"找不到entry记录: entry_id={entry_id}")
            
            # 准备MDB插入数据
            mdb_data = {
                'employee_id': '215829',
                'entry_date': '2025/06/26',
                'model': '',
                'number': '',
                'factory_number': '',
                'project_number': '24585',
                'unit_number': '',
                'category': 3,
                'item': 7,
                'duration': 9,
                'department': '131'
            }
            
            logger.info(f"🔄 执行INSERT操作: entry_id={entry_id}, employee_id={mdb_data['employee_id']}")
            
            # 通过Server6写入MDB并获取external_id
            response = await self.server6_client.insert_entry(mdb_data)
            
            if response.get('success') and response.get('inserted_id'):
                external_id = response.get('inserted_id')
                
                # 在事务中回写external_id到entries表
                await conn.execute(
                    "UPDATE entries SET external_id = $1, source = 'system' WHERE id = $2",
                    external_id, entry_id
                )
                
                # 在事务中标记队列项为已同步
                await conn.execute(
                    "UPDATE entries_push_queue SET synced = TRUE WHERE queue_id = $1",
                    queue_id
                )
                
                logger.info(f"✅ INSERT完成: entry_id={entry_id} -> external_id={external_id}")
                
                # 记录同步操作
                await self.mongo_client.log_sync_operation({
                    "operation_type": "INSERT",
                    "employee_id": mdb_data['employee_id'],
                    "entry_id": entry_id,
                    "external_id": external_id,
                    "queue_id": queue_id,
                    "status": "success",
                    "worker_id": worker_id
                })
                
                # 更新计数器
                await self.redis_client.increment_counter("server5:insert_count")
                
                # 触发f6专属ID同步
                await self._trigger_f6_sync(mdb_data['employee_id'])
                
            else:
                error_msg = response.get('message', 'Server6插入失败，未获取到external_id')
                raise RuntimeError(f"MDB插入失败: {error_msg}")
                
        except Exception as e:
            logger.error(f"❌ INSERT操作失败: {e}", exc_info=True)
            raise
    
    async def _handle_update_operation_in_transaction(self, conn, item: Dict, worker_id: str):
        """在事务中处理UPDATE操作，修复数据类型问题"""
        try:
            queue_id = item['queue_id']
            entry_id = item['entry_id']
            
            # 从队列表获取 external_id
            queue_item_details = await conn.fetchrow("SELECT external_id FROM entries_push_queue WHERE queue_id = $1", queue_id)
            if not queue_item_details or not queue_item_details['external_id']:
                raise ValueError(f"更新操作失败：队列项 {queue_id} 中缺少 external_id")
            
            external_id = queue_item_details['external_id']

            # 获取完整的entry数据
            entry_data_row = await conn.fetchrow("SELECT * FROM entries WHERE id = $1", entry_id)
            if not entry_data_row:
                raise ValueError(f"找不到要更新的entry记录: entry_id={entry_id}")

            entry_data = dict(entry_data_row)
            
            # 准备MDB更新数据，确保数据类型正确
            mdb_data = {
                'employee_id': entry_data.get('employee_id'),
                'entry_date': entry_data.get('entry_date'),
                'model': entry_data.get('model'),
                'number': entry_data.get('number'),
                'factory_number': entry_data.get('factory_number'),
                'project_number': entry_data.get('project_number'),
                'unit_number': entry_data.get('unit_number'),
                'category': entry_data.get('category'),
                'item': entry_data.get('item'),
                'duration': float(entry_data.get('duration', 0.0)),  # 关键修复: 将Decimal转换为float
                'department': entry_data.get('department')
            }
            
            logger.info(f"🔄 执行UPDATE操作: entry_id={entry_id} -> external_id={external_id}")
            
            # 通过Server6更新MDB
            response = await self.server6_client.update_entry(external_id, mdb_data)
            
            if response.get('success'):
                # 在事务中标记队列项为已同步
                await conn.execute("UPDATE entries_push_queue SET synced = TRUE WHERE queue_id = $1", queue_id)
                logger.info(f"✅ UPDATE完成: entry_id={entry_id} -> external_id={external_id}")
                
                # 记录同步操作
                await self.mongo_client.log_sync_operation({
                    "operation_type": "UPDATE",
                    "employee_id": mdb_data['employee_id'],
                    "entry_id": entry_id,
                    "external_id": external_id,
                    "queue_id": queue_id,
                    "status": "success",
                    "worker_id": worker_id
                })
                
                # 更新计数器
                await self.redis_client.increment_counter("server5:update_count")
            else:
                error_msg = response.get('message', f'Server6更新失败, external_id={external_id}')
                raise RuntimeError(f"MDB更新失败: {error_msg}")
                
        except Exception as e:
            logger.error(f"❌ UPDATE操作失败: {e}", exc_info=True)
            raise
    
    async def _handle_delete_operation_in_transaction(self, conn, item: Dict, worker_id: str):
        """在事务中处理DELETE操作"""
        try:
            queue_id = item['queue_id']
            entry_id = item['entry_id']
            external_id = item.get('external_id')
            
            if not external_id:
                raise ValueError("DELETE操作需要external_id")
            
            logger.info(f"🔄 执行DELETE操作: entry_id={entry_id}, external_id={external_id}")
            
            # 通过Server6删除MDB记录
            response = await self.server6_client.delete_entry(external_id)
            
            if response.get('success'):
                # 在事务中标记队列项为已同步
                await conn.execute(
                    "UPDATE entries_push_queue SET synced = TRUE WHERE queue_id = $1",
                    queue_id
                )
                
                logger.info(f"✅ DELETE完成: entry_id={entry_id}, external_id={external_id}")
                
                # 记录同步操作
                await self.mongo_client.log_sync_operation({
                    "operation_type": "DELETE",
                    "employee_id": item.get('employee_id', 'N/A'),
                    "entry_id": entry_id,
                    "external_id": external_id,
                    "queue_id": queue_id,
                    "status": "success",
                    "worker_id": worker_id
                })
                
                await self.redis_client.increment_counter("server5:delete_count")
                await self._trigger_f6_sync(item.get('employee_id'))
            else:
                error_msg = response.get('message', 'Server6删除失败')
                raise RuntimeError(f"MDB删除失败: {error_msg}")
                
        except Exception as e:
            logger.error(f"❌ DELETE操作失败: {e}", exc_info=True)
            raise
    
    async def _trigger_f6_sync(self, employee_id: Optional[str]):
        """触发f6专属ID同步"""
        try:
            if employee_id:
                # 将用户同步任务推送到Redis队列
                task_data = {
                    "task_type": "user_sync",
                    "employee_id": employee_id,
                    "timestamp": datetime.utcnow().isoformat(),
                    "priority": "medium"
                }
                
                await self.redis_client.push_task("server5:user_sync_jobs", task_data)
                logger.debug(f"👤 触发f6同步: {employee_id}")
            
        except Exception as e:
            logger.error(f"❌ 触发f6同步失败: {e}")
    
    async def _log_error(self, error_type: str, error_message: str, context: Dict = None):
        """记录错误到MongoDB"""
        try:
            await self.mongo_client.log_error({
                "error_type": error_type,
                "error_message": error_message,
                "context": context or {},
                "function_name": "f2_push_writer_fixed",
                "severity": "error"
            })
            
            await self.redis_client.increment_counter("server5:error_count")
            
        except Exception as e:
            logger.error(f"❌ 记录错误日志失败: {e}")
    
    async def get_status(self) -> Dict:
        """获取推送回写服务状态"""
        try:
            # 获取活跃工作线程数
            active_workers = len([t for t in self.worker_tasks if not t.done()])
            
            # 获取数据库状态
            imdb_status = await self.imdb_client.get_connection_status()
            server6_status = await self.server6_client.get_status()
            
            return {
                "service": "f2_push_writer_fixed",
                "is_running": self.is_running,
                "worker_count": WORKER_CONCURRENCY,
                "active_workers": active_workers,
                "databases": {
                    "imdb": imdb_status,
                    "server6": server6_status
                }
            }
            
        except Exception as e:
            logger.error(f"❌ 获取服务状态失败: {e}")
            return {
                "service": "f2_push_writer_fixed",
                "is_running": self.is_running,
                "error": str(e)
            }
    
    async def process_queue_batch(self, batch_size: int = 10) -> int:
        """批量处理队列项（用于手动触发）"""
        try:
            # 获取未同步的队列项
            queue_items = await self.imdb_client.get_queue_items(synced=False, limit=batch_size)
            
            if not queue_items:
                logger.info("✅ 没有待处理的队列项")
                return 0
            
            logger.info(f"🔄 开始批量处理 {len(queue_items)} 个队列项")
            
            processed_count = 0
            for item in queue_items:
                try:
                    await self._process_queue_item(item, "batch_worker")
                    processed_count += 1
                except Exception as e:
                    logger.error(f"❌ 批量处理队列项失败: {e}")
                    continue
            
            logger.info(f"✅ 批量处理完成: {processed_count}/{len(queue_items)} 成功")
            return processed_count
            
        except Exception as e:
            logger.error(f"❌ 批量处理队列失败: {e}")
            return 0 