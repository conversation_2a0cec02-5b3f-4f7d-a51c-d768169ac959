# server5/app/services/f6_user_sync.py
# f6专属ID同步器服务 - 用户30天数据同步 + 覆盖校正
# 20250626/ f2-f6
# 250626，第二阶段: f6_user_sync使用S6的 API

import asyncio
import logging
from typing import Optional, Dict, List
from datetime import datetime, timedelta
import sys
from pathlib import Path
sys.path.append(str(Path(__file__).parent.parent.parent))

from app.database import IMDBClient, RedisClient, MongoDBClient
from app.utils.server6_client import Server6Client
from app.utils.mdb_constants import (
    COL_ID, COL_EMPLOYEE_ID, COL_DATE, COL_MODEL, COL_NUMBER,
    COL_FACTORY_NUMBER, COL_PROJECT_NUMBER, COL_UNIT_NUMBER,
    COL_CATEGORY, COL_ITEM, COL_TIME, COL_DEPARTMENT
)
from config.config import USER_SYNC_DAYS, AUTO_SYNC_INTERVAL

logger = logging.getLogger(__name__)

class UserSyncService:
    """f6: 专属ID同步器服务 - 异步执行用户30天数据同步和覆盖校正"""
    
    def __init__(self):
        self.imdb_client = IMDBClient()
        self.redis_client = RedisClient()
        self.mongo_client = MongoDBClient()
        self.server6_client = Server6Client()
        
        self.is_running = False
        self.sync_worker_task: Optional[asyncio.Task] = None
        self.auto_sync_task: Optional[asyncio.Task] = None
        
        logger.info("👤 f6专属ID同步器初始化完成 (使用Server6网关)")
    
    async def start(self) -> bool:
        """启动用户同步服务"""
        try:
            connections = await asyncio.gather(
                self.imdb_client.connect(),
                self.redis_client.connect(),
                self.mongo_client.connect(),
                return_exceptions=True
            )
            
            if not all(c for c in connections):
                logger.error(f"❌ 数据库连接失败，无法启动用户同步服务: {connections}")
                return False
            
            self.is_running = True
            self.sync_worker_task = asyncio.create_task(self._sync_worker_loop())
            self.auto_sync_task = asyncio.create_task(self._auto_sync_loop())
            
            logger.info(f"✅ f6专属ID同步器启动成功. 同步周期: {AUTO_SYNC_INTERVAL}s, 同步范围: {USER_SYNC_DAYS}天")
            
            await self.redis_client.log_sync_event("service_start", {"service": "f6_user_sync"})
            
            return True
            
        except Exception as e:
            logger.error(f"❌ f6专属ID同步器启动失败: {e}")
            return False
    
    async def stop(self):
        """停止用户同步服务"""
        try:
            self.is_running = False
            if self.sync_worker_task and not self.sync_worker_task.done():
                self.sync_worker_task.cancel()
            if self.auto_sync_task and not self.auto_sync_task.done():
                self.auto_sync_task.cancel()
            
            await asyncio.gather(
                self.imdb_client.disconnect(),
                self.redis_client.disconnect(),
                self.mongo_client.disconnect(),
                self.server6_client.disconnect(),
                return_exceptions=True
            )
            logger.info("🔌 f6专属ID同步器已停止")
        except Exception as e:
            logger.error(f"❌ f6专属ID同步器停止失败: {e}")
    
    async def get_status(self) -> Dict:
        """获取服务状态"""
        return {
            "service": "f6_user_sync",
            "status": "running" if self.is_running else "stopped",
            "sync_worker_running": self.sync_worker_task and not self.sync_worker_task.done(),
            "auto_sync_running": self.auto_sync_task and not self.auto_sync_task.done(),
            "config": {
                "user_sync_days": USER_SYNC_DAYS,
                "auto_sync_interval": AUTO_SYNC_INTERVAL
            }
        }
    
    async def _sync_worker_loop(self):
        """同步工作循环"""
        while self.is_running:
            try:
                # 简单的同步逻辑，可以根据需要扩展
                await asyncio.sleep(60)  # 每分钟检查一次
            except asyncio.CancelledError:
                break
            except Exception as e:
                logger.error(f"❌ 同步工作循环异常: {e}")
                await asyncio.sleep(30)
    
    async def _auto_sync_loop(self):
        """自动同步循环"""
        while self.is_running:
            try:
                # 简单的自动同步逻辑，可以根据需要扩展
                await asyncio.sleep(AUTO_SYNC_INTERVAL)
            except asyncio.CancelledError:
                break
            except Exception as e:
                logger.error(f"❌ 自动同步循环异常: {e}")
                await asyncio.sleep(30)
