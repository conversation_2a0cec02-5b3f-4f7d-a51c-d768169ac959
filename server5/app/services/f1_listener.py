# server5/app/services/f1_listener.py
# f1监听器服务 - PostgreSQL NOTIFY监听 + 自动分区管理

import asyncio
import logging
from typing import Optional, Dict, List
from datetime import datetime, timedelta
import sys
from pathlib import Path
sys.path.append(str(Path(__file__).parent.parent.parent))

from app.database import IMDBClient, RedisClient, MongoDBClient
from config.config import NOTIFY_CHANNELS, AUTO_PARTITION_CREATE

logger = logging.getLogger(__name__)

class ListenerService:
    """f1: Listener监听器服务 - 异步监听PostgreSQL NOTIFY事件"""
    
    def __init__(self):
        self.imdb_client = IMDBClient()
        self.redis_client = RedisClient()
        self.mongo_client = MongoDBClient()
        
        self.is_running = False
        self.listener_tasks: List[asyncio.Task] = []
        self.partition_check_task: Optional[asyncio.Task] = None
        
        logger.info("🔊 f1监听器服务初始化完成")
    
    async def start(self) -> bool:
        """启动监听器服务"""
        try:
            # 连接数据库
            connections = await asyncio.gather(
                self.imdb_client.connect(),
                self.redis_client.connect(),
                self.mongo_client.connect(),
                return_exceptions=True
            )
            
            if not all(isinstance(conn, bool) and conn for conn in connections):
                logger.error("数据库连接失败，无法启动监听器")
                return False
            
            # 注册NOTIFY监听器
            for channel in NOTIFY_CHANNELS:
                await self.imdb_client.add_listener(channel, self._handle_notification)
            
            # 启动自动分区检查任务
            if AUTO_PARTITION_CREATE:
                self.partition_check_task = asyncio.create_task(self._partition_check_loop())
            
            self.is_running = True
            logger.info("✅ f1监听器服务启动成功")
            
            # 记录启动事件
            await self.redis_client.log_sync_event("service_start", {
                "service": "f1_listener",
                "channels": NOTIFY_CHANNELS,
                "auto_partition": AUTO_PARTITION_CREATE
            })
            
            return True
            
        except Exception as e:
            logger.error(f"❌ f1监听器启动失败: {e}")
            await self._log_error("service_start_failed", str(e))
            return False
    
    async def stop(self):
        """停止监听器服务"""
        try:
            self.is_running = False
            
            # 取消分区检查任务
            if self.partition_check_task and not self.partition_check_task.done():
                self.partition_check_task.cancel()
            
            # 取消所有监听任务
            for task in self.listener_tasks:
                if not task.done():
                    task.cancel()
            
            # 断开数据库连接
            await asyncio.gather(
                self.imdb_client.disconnect(),
                self.redis_client.disconnect(),
                self.mongo_client.disconnect(),
                return_exceptions=True
            )
            
            logger.info("🔌 f1监听器服务已停止")
            
        except Exception as e:
            logger.error(f"❌ f1监听器停止失败: {e}")
    
    async def _handle_notification(self, channel: str, payload: str):
        """处理PostgreSQL NOTIFY通知"""
        try:
            start_time = asyncio.get_event_loop().time()
            
            logger.debug(f"📨 收到通知: {channel} -> {payload}")
            
            # 根据频道分发处理
            if channel == "push_job":
                await self._handle_push_job(payload)
            elif channel == "partition_check":
                await self._handle_partition_check(payload)
            elif channel == "sync_trigger":
                await self._handle_sync_trigger(payload)
            else:
                logger.warning(f"⚠️ 未知通知频道: {channel}")
            
            # 记录处理时间
            execution_time = (asyncio.get_event_loop().time() - start_time) * 1000
            
            await self.mongo_client.log_performance_metric({
                "metric_type": "notification_processing",
                "metric_name": f"handle_{channel}",
                "value": execution_time,
                "unit": "milliseconds",
                "tags": {"channel": channel, "payload": payload}
            })
            
            # 更新Redis计数器
            await self.redis_client.increment_counter(f"server5:notifications:{channel}")
            
        except Exception as e:
            logger.error(f"❌ 处理通知失败 {channel}: {e}")
            await self._log_error("notification_handling_failed", str(e), {
                "channel": channel,
                "payload": payload
            })
    
    async def _handle_push_job(self, queue_id: str):
        """处理推送任务通知"""
        try:
            # 解析队列ID
            if not queue_id.isdigit():
                logger.warning(f"⚠️ 无效的队列ID: {queue_id}")
                return
            
            queue_id = int(queue_id)
            
            # 将任务推送到Redis队列异步处理
            task_data = {
                "task_type": "push_job",
                "queue_id": queue_id,
                "timestamp": datetime.utcnow().isoformat(),
                "priority": "high"
            }
            
            await self.redis_client.push_task("server5:push_jobs", task_data)
            
            logger.debug(f"📤 推送任务已排队: {queue_id}")
            
            # 触发f2推送回写服务处理
            from .f2_push_writer import PushWriterService
            # 注意：这里应该通过服务管理器来调用，而不是直接导入
            
        except Exception as e:
            logger.error(f"❌ 处理推送任务失败: {e}")
            await self._log_error("push_job_handling_failed", str(e), {"queue_id": queue_id})
    
    async def _handle_partition_check(self, month_code: str):
        """处理分区检查通知"""
        try:
            if not month_code or len(month_code) != 6:
                logger.warning(f"⚠️ 无效的月份代码: {month_code}")
                return
            
            # 检查并创建分区
            await self._create_partition_if_needed(month_code)
            
            logger.info(f"📅 分区检查完成: {month_code}")
            
        except Exception as e:
            logger.error(f"❌ 分区检查失败: {e}")
            await self._log_error("partition_check_failed", str(e), {"month_code": month_code})
    
    async def _handle_sync_trigger(self, employee_id: str):
        """处理同步触发器通知"""
        try:
            if not employee_id:
                logger.warning("⚠️ 同步触发器收到空的员工ID")
                return
            
            # 触发f6专属ID同步
            task_data = {
                "task_type": "user_sync",
                "employee_id": employee_id,
                "timestamp": datetime.utcnow().isoformat(),
                "priority": "medium"
            }
            
            await self.redis_client.push_task("server5:user_sync_jobs", task_data)
            
            logger.debug(f"👤 用户同步任务已排队: {employee_id}")
            
        except Exception as e:
            logger.error(f"❌ 处理同步触发器失败: {e}")
            await self._log_error("sync_trigger_failed", str(e), {"employee_id": employee_id})
    
    async def _partition_check_loop(self):
        """自动分区检查循环"""
        try:
            while self.is_running:
                try:
                    await self._check_monthly_partitions()
                    await asyncio.sleep(3600)  # 每小时检查一次
                    
                except asyncio.CancelledError:
                    break
                except Exception as e:
                    logger.error(f"❌ 自动分区检查异常: {e}")
                    await asyncio.sleep(300)  # 异常后等待5分钟再重试
                    
        except asyncio.CancelledError:
            logger.info("📅 自动分区检查任务已取消")
    
    async def _check_monthly_partitions(self):
        """检查月度分区"""
        try:
            # 检查当前月份和下个月的分区
            current_date = datetime.now()
            
            # 当前月份
            current_month = current_date.strftime("%Y%m")
            await self._create_partition_if_needed(current_month)
            
            # 下个月
            next_month_date = current_date.replace(day=1) + timedelta(days=32)
            next_month = next_month_date.strftime("%Y%m")
            await self._create_partition_if_needed(next_month)
            
            # 如果是月末，提前创建后月的分区
            days_in_month = (current_date.replace(month=current_date.month + 1, day=1) - timedelta(days=1)).day
            if current_date.day >= days_in_month - 2:  # 月末前2天
                after_next_month_date = next_month_date.replace(day=1) + timedelta(days=32)
                after_next_month = after_next_month_date.strftime("%Y%m")
                await self._create_partition_if_needed(after_next_month)
            
            logger.debug("📅 月度分区检查完成")
            
        except Exception as e:
            logger.error(f"❌ 月度分区检查失败: {e}")
    
    async def _create_partition_if_needed(self, month_code: str):
        """创建分区（如果需要）"""
        try:
            # 使用分布式锁避免重复创建
            lock_key = f"server5:partition_lock:{month_code}"
            
            if await self.redis_client.acquire_lock(lock_key, timeout=300):
                try:
                    await self.imdb_client.create_partition_if_needed(month_code)
                    
                    # 记录分区创建事件
                    await self.mongo_client.log_sync_operation({
                        "operation_type": "partition_create",
                        "metadata": {"month_code": month_code},
                        "status": "success"
                    })
                    
                    await self.redis_client.increment_counter("server5:partitions_created")
                    
                finally:
                    await self.redis_client.release_lock(lock_key)
            else:
                logger.debug(f"📅 分区 {month_code} 正在被其他进程处理")
                
        except Exception as e:
            logger.error(f"❌ 创建分区失败 {month_code}: {e}")
            
            await self.mongo_client.log_error({
                "error_type": "partition_creation_failed",
                "error_message": str(e),
                "context": {"month_code": month_code},
                "function_name": "_create_partition_if_needed"
            })
    
    async def _log_error(self, error_type: str, error_message: str, context: Dict = None):
        """记录错误到MongoDB"""
        try:
            await self.mongo_client.log_error({
                "error_type": error_type,
                "error_message": error_message,
                "context": context or {},
                "function_name": "f1_listener",
                "severity": "error"
            })
            
            await self.redis_client.increment_counter("server5:error_count")
            
        except Exception as e:
            logger.error(f"❌ 记录错误日志失败: {e}")
    
    async def get_status(self) -> Dict:
        """获取监听器状态"""
        try:
            pg_status = await self.imdb_client.get_connection_status()
            redis_status = await self.redis_client.health_check()
            mongo_status = await self.mongo_client.health_check()
            
            return {
                "service": "f1_listener",
                "is_running": self.is_running,
                "channels": NOTIFY_CHANNELS,
                "auto_partition": AUTO_PARTITION_CREATE,
                "databases": {
                    "postgresql": pg_status,
                    "redis": redis_status,
                    "mongodb": mongo_status
                },
                "active_tasks": len([t for t in self.listener_tasks if not t.done()]),
                "partition_check_running": self.partition_check_task and not self.partition_check_task.done()
            }
            
        except Exception as e:
            return {
                "service": "f1_listener", 
                "is_running": False,
                "error": str(e)
            }
    
    async def trigger_partition_check(self, month_code: str = None) -> bool:
        """手动触发分区检查"""
        try:
            if month_code:
                await self._create_partition_if_needed(month_code)
            else:
                await self._check_monthly_partitions()
            
            logger.info(f"✅ 手动分区检查完成: {month_code or 'all'}")
            return True
            
        except Exception as e:
            logger.error(f"❌ 手动分区检查失败: {e}")
            return False 