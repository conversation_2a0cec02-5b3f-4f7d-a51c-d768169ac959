# 2025 07/04 +  16：00 + 相关主题: f5服务重构为删除同步器
# server5/app/services/f5_bulk_sync.py
# f5删除同步服务 - 对比PG与MDB，同步MDB中已删除的数据

import asyncio
import logging
from typing import Optional, Dict, List, Any
from datetime import datetime, timedelta
import sys
from pathlib import Path
sys.path.append(str(Path(__file__).parent.parent.parent))

from app.database import IMDBClient, RedisClient, MongoDBClient
from app.utils.server6_client import Server6Client
from config.config import BULK_SYNC_DAYS

logger = logging.getLogger(__name__)

class DeletionSyncService:
    """f5: 删除同步服务 - 找出并删除在MDB中已不存在的PostgreSQL记录"""
    
    def __init__(self):
        self.imdb_client = IMDBClient()
        self.redis_client = RedisClient()
        self.mongo_client = MongoDBClient()
        self.server6_client = Server6Client()
        self.is_running = False
        logger.info("🗑️ f5删除同步服务初始化完成")
    
    async def start(self) -> bool:
        """仅连接数据库，为执行任务做准备"""
        try:
            # 2025 07/04 +  16：00 + 相关主题: 简化start，只连接，不启动定时任务
            connections = await asyncio.gather(
                self.imdb_client.connect(),
                self.redis_client.connect(),
                self.mongo_client.connect(),
                return_exceptions=True
            )
            
            if not all(c for c in connections):
                logger.error(f"❌ 数据库连接失败，删除同步服务无法启动: {connections}")
                return False
            
            self.is_running = True
            logger.info("✅ f5删除同步服务已连接，准备执行任务。")
            return True
        except Exception as e:
            logger.error(f"❌ f5删除同步服务启动失败: {e}")
            return False
    
    async def stop(self):
        """停止服务并断开连接"""
        try:
            self.is_running = False
            await asyncio.gather(
                self.imdb_client.disconnect(),
                self.redis_client.disconnect(),
                self.mongo_client.disconnect(),
                self.server6_client.disconnect(),
                return_exceptions=True
            )
            logger.info("🔌 f5删除同步服务已停止")
        except Exception as e:
            logger.error(f"❌ f5删除同步服务停止失败: {e}")
    
    async def run_deletion_sync(self):
        """
        # 2025 07/04 +  16：00 + 相关主题: f5的核心业务逻辑
        执行一次删除同步。对比MDB和PostgreSQL在指定时间范围内的记录，
        删除在PostgreSQL中存在但在MDB中已不存在的记录。
        """
        logger.info("--- 开始执行f5删除同步任务 ---")
        start_time = asyncio.get_event_loop().time()
        deleted_count = 0  # 初始化删除计数器
        
        # 1. 定义同步的时间范围，例如最近60天
        end_date = datetime.now().date()
        start_date = end_date - timedelta(days=BULK_SYNC_DAYS)
        logger.info(f"删除同步范围: 从 {start_date} 到 {end_date}")

        try:
            # 2. 20250708+11:30 + 调用新的高性能API，只获取ID列表，避免拉取完整数据
            mdb_ids_list = await self.server6_client.get_ids_in_range(start_date, end_date)
            
            if mdb_ids_list is None: # API调用失败
                 logger.error("从Server6获取MDB ID列表失败，跳过此次删除同步。")
                 return
            
            mdb_ids = set(mdb_ids_list)
            logger.info(f"在MDB中找到 {len(mdb_ids)} 个唯一的 external_id。")

            # 3. 从PostgreSQL的entries表中获取相同范围内的所有external_id
            pg_ids = await self.imdb_client.get_external_ids_in_range(start_date, end_date)
            logger.info(f"在PostgreSQL中找到 {len(pg_ids)} 个唯一的 external_id。")
            
            # 4. 计算差集：找出在PG中但不在MDB中的ID
            ids_to_delete = pg_ids - mdb_ids
            
            if not ids_to_delete:
                logger.info("✅ 检查完成。没有发现在PostgreSQL中存在但在MDB中已被删除的记录。")
            else:
                logger.warning(f"发现 {len(ids_to_delete)} 条需要从PostgreSQL删除的记录。")
                
                # 5. 执行删除操作
                deleted_count = await self.imdb_client.delete_entries_by_external_ids(list(ids_to_delete))
                logger.info(f"成功执行删除操作，影响了 {deleted_count} 条记录。")

                await self.mongo_client.log_sync_operation({
                    "operation_type": "DELETE_SYNC",
                    "status": "success",
                    "deleted_count": deleted_count,
                    "ids_to_delete": list(ids_to_delete)
                })

        except Exception as e:
            logger.error(f"❌ 执行删除同步时发生严重错误: {e}", exc_info=True)
            await self.mongo_client.log_error({
                "error_type": "deletion_sync_failed",
                "error_message": str(e),
                "context": {"start_date": str(start_date), "end_date": str(end_date)}
            })
        
        finally:
            execution_time = (asyncio.get_event_loop().time() - start_time)
            logger.info(f"--- f5删除同步任务完成，耗时 {execution_time:.2f} 秒。本次共删除 {deleted_count} 条记录 ---") 