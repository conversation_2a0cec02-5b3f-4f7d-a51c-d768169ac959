#!/usr/bin/env python3
# -*- coding: utf-8 -*-
# 2025/06/27 + f3数据拉取服务重构

"""
f3_data_puller.py
- 核心职责: 从Server6 (MDB网关) 拉取数据, 通过staging表安全地同步到PostgreSQL。
- 这是从MDB到PostgreSQL的唯一数据入口。
"""

import asyncio
import logging
import sys
from pathlib import Path
from datetime import datetime, date, timedelta, time
from typing import Dict, List, Optional, Any
import json
import decimal

sys.path.append(str(Path(__file__).parent.parent.parent))

from app.database import IMDBClient, RedisClient
from app.utils.server6_client import Server6Client
from config.config import F3_PULL_SCHEDULE, USER_SYNC_DAYS as DATA_PULL_DAYS_RANGE
from app.utils.server6_field_mapper import field_mapper
from app.services.f5_bulk_sync import DeletionSyncService

logger = logging.getLogger(__name__)

class DataPullerService:
    """
    数据拉取服务
    - 定期从MDB拉取最近的数据。
    - 提供手动按天拉取的功能。
    """

    def __init__(self):
        self.imdb_client = IMDBClient()
        self.redis_client = RedisClient()
        self.server6_client = Server6Client()
        self._task: asyncio.Task = None
        self.is_running = False
        # 2025/07/08 - 解析预定时间
        try:
            self.scheduled_times = sorted([datetime.strptime(t, "%H:%M").time() for t in F3_PULL_SCHEDULE])
        except ValueError:
            logger.error(f"无效的时间格式在 F3_PULL_SCHEDULE 中，请使用 HH:MM 格式。服务将不会自动运行。")
            self.scheduled_times = []
        
        logger.info("📥 f3数据拉取器初始化完成 (Staging & UPSERT 模式)")

    def _round_duration(self, value) -> float:
        """对duration值进行精度控制，避免浮点数精度问题"""
        if value is None:
            return 0.0
        
        try:
            # 使用decimal进行精确的四舍五入
            decimal_value = decimal.Decimal(str(value))
            # 四舍五入到2位小数
            rounded_value = decimal_value.quantize(decimal.Decimal('0.01'), rounding=decimal.ROUND_HALF_UP)
            return float(rounded_value)
        except (ValueError, TypeError, decimal.InvalidOperation):
            # 如果转换失败，返回0.0
            logger.warning(f"⚠️ duration值转换失败: {value}, 使用默认值0.0")
            return 0.0

    def _format_number_field(self, value) -> str:
        """对号機字段进行格式化，确保数字类型转换为整数格式的字符串"""
        if value is None:
            return ""
        
        try:
            # 如果是数字类型，转换为整数再转为字符串
            if isinstance(value, (int, float)):
                # 对于浮点数，先四舍五入到整数
                if isinstance(value, float):
                    value = round(value)
                return str(int(value))
            else:
                # 如果是字符串，尝试转换为数字再格式化
                numeric_value = float(value)
                return str(int(round(numeric_value)))
        except (ValueError, TypeError):
            # 如果转换失败，返回原始值的字符串形式
            return str(value) if value is not None else ""

    async def start(self):
        """启动服务"""
        if self.is_running:
            return
        
        # 连接数据库
        await self.imdb_client.connect()
        await self.redis_client.connect()
        
        self.is_running = True
        self._task = asyncio.create_task(self._run_periodic_pull())
        logger.info(f"✅ f3数据拉取服务启动成功，预定执行时间: {F3_PULL_SCHEDULE}")
        return True

    async def connect_only(self):
        """仅连接数据库，不启动定期拉取循环"""
        if self.is_running:
            return
        
        # 连接数据库
        await self.imdb_client.connect()
        await self.redis_client.connect()
        
        logger.info("✅ f3数据拉取器数据库连接成功（仅连接模式）")
        return True

    async def stop(self):
        """停止服务"""
        if not self.is_running:
            return
            
        if self._task:
            self._task.cancel()
            try:
                await self._task
            except asyncio.CancelledError:
                pass
        
        await self.imdb_client.disconnect()
        await self.redis_client.disconnect()
        await self.server6_client.disconnect()
        self.is_running = False
        logger.info("🔌 f3数据拉取服务已停止")

    async def _get_seconds_until_next_run(self) -> float:
        """计算到下一个预定运行时间的秒数"""
        if not self.scheduled_times:
            # 如果没有有效的预定时间，则永远等待
            return float('inf')

        now = datetime.now()
        today = now.date()
        
        # 寻找今天内、晚于当前时间的下一个运行点
        next_run_time = None
        for t in self.scheduled_times:
            if t > now.time():
                next_run_time = t
                break
        
        # 如果今天没有了，则取明天的第一个预定时间
        if next_run_time:
            next_run_datetime = datetime.combine(today, next_run_time)
        else:
            tomorrow = today + timedelta(days=1)
            next_run_datetime = datetime.combine(tomorrow, self.scheduled_times[0])
            
        wait_seconds = (next_run_datetime - now).total_seconds()
        logger.info(f"下一次f3/f5同步任务将在 {next_run_datetime.strftime('%Y-%m-%d %H:%M:%S')} 执行，等待 {wait_seconds:.0f} 秒...")
        return wait_seconds

    async def _run_periodic_pull(self):
        """定期执行数据拉取，基于预定时间而非固定间隔"""
        if not self.scheduled_times:
            logger.warning("f3调度器未启动，因为没有配置有效的 F3_PULL_SCHEDULE。")
            return

        logger.info(f"🚀 数据拉取调度器启动，预定运行时间: {[t.strftime('%H:%M') for t in self.scheduled_times]}")
        while self.is_running:
            try:
                # 1. 计算到下一次运行的时间并等待
                wait_seconds = await self._get_seconds_until_next_run()
                if wait_seconds == float('inf'):
                    logger.warning("没有有效的预定时间，f3调度器将进入休眠。")
                    await asyncio.sleep(3600) # 永远等待，但每小时检查一次以防配置更改
                    continue
                
                await asyncio.sleep(wait_seconds)

                # 2. 执行完整的同步周期
                logger.info(f"⏰ 到达预定时间，开始执行 f3/f5 同步周期...")
                await self.run_full_sync_cycle()

            except asyncio.CancelledError:
                break
            except Exception as e:
                logger.error(f"调度循环失败: {e}", exc_info=True)
                # 出现异常时，等待5分钟再重新计算，防止快速失败循环
                await asyncio.sleep(300)

    async def run_full_sync_cycle(self):
        """
        # 2025 07/04 + 16：30 + 相关主题: 新增的完整同步周期函数
        执行一个完整的同步周期：先执行f3的UPSERT，然后执行f5的DELETE。
        这个方法应该只被计划任务调用。
        """
        # --- f3 阶段: 更新和插入 ---
        logger.info("--- 开始 f3 (UPSERT) 阶段 ---")
        #await self.pull_recent_data()
        logger.info("--- f3 (UPSERT) 阶段完成 ---")

        # --- f5 阶段: 删除 ---
        logger.info("--- 准备启动 f5 (Deletion) 阶段 ---")
        f5_service = DeletionSyncService()
        try:
            # 启动f5服务（仅连接）并执行删除同步
            await f5_service.start()
            await f5_service.run_deletion_sync()
        finally:
            # 确保f5服务被关闭
            await f5_service.stop()
            logger.info("--- f5 (Deletion) 阶段完成 ---")

    async def _pull_and_sync(self, start_date: date, end_date: date) -> int:
        """
        核心的拉取和同步执行逻辑。
        1. 获取活跃用户列表
        2. 循环为每个用户拉取数据
        3. 同步到数据库
        """
        logger.info(f"🔄 开始数据同步，范围: {start_date} 到 {end_date}")

        # 1. 从PG获取需要同步的活跃用户ID列表
        active_employee_ids = await self.imdb_client.get_active_employee_ids(days=DATA_PULL_DAYS_RANGE)
        if not active_employee_ids:
            logger.info("✅ 在PG中没有找到最近活跃的用户，跳过本次同步。")
            return 0
        
        logger.info(f"将为 {len(active_employee_ids)} 个活跃用户同步数据...")

        # 2. 为每个用户拉取数据并汇总
        all_records = []
        for employee_id in active_employee_ids:
            try:
                records = await self.server6_client.query_entries(
                    employee_id=employee_id,
                    start_date=start_date,
                    end_date=end_date
                )
                if records:
                    all_records.extend(records)
                    logger.debug(f"为员工 {employee_id} 获取到 {len(records)} 条记录")
            except Exception as e:
                logger.error(f"为员工 {employee_id} 拉取数据失败: {e}")

        if not all_records:
            logger.info("✅ 从Server6没有获取到任何需要同步的新数据。")
            return 0
        
        logger.info(f"从Server6总共获取到 {len(all_records)} 条记录")

        # 3. 同步到数据库
        return await self._sync_to_db(all_records)

    # 20250708+11:00 + 新增批量拉取函数，替换原有的按员工循环拉取逻辑
    async def pull_and_sync_bulk(self, start_date: date, end_date: date, batch_size: int = 7) -> int:
        """
        核心的批量拉取和同步执行逻辑。
        1. 按日期范围分批次。
        2. 为每个批次调用Server6的bulk_fast接口，一次性获取该批次所有员工的数据。
        3. 将获取到的数据同步到数据库。
        """
        logger.info(f"🔄 开始批量数据同步，范围: {start_date} 到 {end_date}，批次大小: {batch_size}天")
        
        total_records_pulled = 0
        total_rows_synced = 0
        batch_count = 0
        current_start = start_date
        
        while current_start <= end_date:
            batch_count += 1
            current_end = min(current_start + timedelta(days=batch_size - 1), end_date)
            
            logger.info(f"  📦 开始处理批次 {batch_count}: 从 {current_start} 到 {current_end}")
            
            try:
                # 1. 从Server6拉取当前批次的数据
                batch_records = await self.server6_client.query_bulk_fast(current_start, current_end)
                
                if not batch_records:
                    logger.info(f"    - 批次 {batch_count} 从Server6没有获取到数据，跳过。")
                    current_start = current_end + timedelta(days=1)
                    continue

                logger.info(f"    - 批次 {batch_count} 从Server6获取到 {len(batch_records)} 条记录。")
                total_records_pulled += len(batch_records)
                
                # 2. 同步到数据库
                affected_rows = await self._sync_to_db(batch_records)
                total_rows_synced += affected_rows
                logger.info(f"    - 批次 {batch_count} 同步完成，影响了 {affected_rows} 行。")

            except Exception as e:
                logger.error(f"    ❌ 批次 {batch_count} 处理失败: {e}", exc_info=True)
            
            # 3. 移动到下一批次的开始日期
            current_start = current_end + timedelta(days=1)
            
            # 4. 在批次间短暂休息，避免对Server6造成过大压力
            if current_start <= end_date:
                await asyncio.sleep(1)
        
        logger.info(f"✅ 批量数据同步完成。总共拉取 {total_records_pulled} 条记录, 同步影响 {total_rows_synced} 行。")
        return total_rows_synced

    async def pull_recent_data(self):
        """拉取最近N天的数据 (只执行UPSERT，不执行删除)"""
        end_date = date.today()
        start_date = end_date - timedelta(days=DATA_PULL_DAYS_RANGE)
        # 20250708+11:00 + f3的拉取逻辑从按员工循环改为按日期批量
        logger.info(f"执行f3数据拉取 (仅UPSERT)，范围: {start_date} 到 {end_date}，使用新的批量模式。")
        await self.pull_and_sync_bulk(start_date, end_date)

    async def manual_pull_for_date(self, target_date: date):
         """
         为特定日期手动触发一次数据拉取和同步。
         这会拉取当天所有的数据，无视PG中的用户，用于初始化和修复。
         采用快速批量查询模式，性能优化。
         """
         # 设定目标日期对象
         self.target_date = target_date

         # 将目标日期格式化为 YYYY/MM/DD
         date_str = target_date.strftime("%Y/%m/%d")
         logger.info(f"🔧 开始为日期 {date_str} 快速批量拉取数据...")

         try:
            # 使用快速批量查询，一次性获取所有记录
            all_records = await self.server6_client.query_bulk_fast(
                start_date=target_date,
                end_date=target_date
            )

            if not all_records:
                logger.warning(f"🤷 在 {date_str} 的 MDB 中没有找到任何记录。")
                return {
                    "status": "no_data_for_date",
                    "date_pulled": date_str,
                    "records_found": 0,
                    "records_synced": 0,
                }

            logger.info(f"从 Server6 快速批量获取到 {len(all_records)} 条记录，准备同步...")

            # 同步到数据库
            affected_rows = await self._sync_to_db(all_records)

            # 构造并返回结果，保持斜杠格式
            result = {
                "status": "success",
                "date_pulled": date_str,
                "records_found": len(all_records),
                "records_synced": affected_rows,
            }
            logger.info(f"✅ 快速批量拉取完成: {result}")
            return result

         except Exception as e:
            logger.error(f"快速批量拉取失败: {e}", exc_info=True)
            # 确保在异常情况下关闭会话
            await self.server6_client.disconnect()
            raise

    async def _fetch_records_for_employee(self, employee_id: str) -> list:
        """为单个员工拉取指定日期的记录，并包含错误处理"""
        try:
            records = await self.server6_client.query_entries(
                employee_id=employee_id,
                start_date=self.target_date,
                end_date=self.target_date
            )
            if records:
                logger.debug(f"为员工 {employee_id} 在 {self.target_date.strftime('%Y/%m/%d')} 获取到 {len(records)} 条记录")
                return records
            return []
        except Exception as e:
            # 记录错误，但返回空列表，以便主流程能继续处理其他员工
            logger.error(f"为员工 {employee_id} 在 {self.target_date.strftime('%Y/%m/%d')} 拉取数据时失败: {e}")
            return []

    async def _sync_to_db(self, records: list) -> int:
        """核心同步逻辑: Staging -> UPSERT -> Clear"""
        try:
            # 1. 清空Staging表
            await self.imdb_client.clear_staging_table()
            
            # 2. 批量插入到Staging表
            await self.imdb_client.bulk_insert_staging(records)
            
            # 3. 从Staging表执行UPSERT到主表
            affected_rows = await self.imdb_client.upsert_from_staging()
            
            # 4. 再次清空Staging表，保持清洁
            await self.imdb_client.clear_staging_table()
            
            # 5. 更新最后拉取时间
            await self.redis_client.set_last_pull_time(datetime.now())
            
            return affected_rows
            
        except Exception as e:
            logger.error(f"同步到数据库失败: {e}", exc_info=True)
            # 确保在失败时也清理staging表，避免脏数据
            await self.imdb_client.clear_staging_table()
            raise 

    def _map_mdb_to_pg_fields(self, mdb_record: Dict) -> Dict:
        """将MDB记录映射到PG字段"""
        # 2025/07/03 +13:40+ 确保f3拉取的数据source='system'，不触发PostgreSQL触发器
        return {
            'external_id': mdb_record.get('ID'),
            'employee_id': mdb_record.get('従業員ｺｰﾄﾞ'),
            'entry_date': mdb_record.get('日付'),
            'model': mdb_record.get('機種'),
            'number': self._format_number_field(mdb_record.get('号機')),  # 使用格式化处理
            'factory_number': mdb_record.get('工場製番'),
            'project_number': mdb_record.get('工事番号'),
            'unit_number': mdb_record.get('ﾕﾆｯﾄ番号'),
            'category': mdb_record.get('区分'),
            'item': mdb_record.get('項目'),
            'duration': self._round_duration(mdb_record.get('時間')),  # 使用精度控制
            'department': mdb_record.get('所属ｺｰﾄﾞ'),
            'source': 'system'  # 2025/07/03 +13:40+ 明确标记为系统操作，避免触发器响应
        } 