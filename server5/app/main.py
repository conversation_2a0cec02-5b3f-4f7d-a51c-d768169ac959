# server5/app/main.py
# Server5 主应用 - 支持 HTTP-only 模式和完整模式

import asyncio
import logging
import signal
import sys
import os
from pathlib import Path
from contextlib import asynccontextmanager

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent
sys.path.append(str(project_root))

from fastapi import FastAPI, Depends, HTTPException
from fastapi.middleware.cors import CORSMiddleware
from fastapi.responses import JSONResponse
import uvicorn

# 检查运行模式 - 通过环境变量控制
HTTP_ONLY_MODE = os.getenv('HTTP_ONLY_MODE', 'false').lower() == 'true'

# 导入配置 - 动态导入支持配置替换
try:
    # 尝试导入可能被替换的配置模块
    import config.config as config_module
    SERVICE_NAME = getattr(config_module, 'SERVICE_NAME', 'MySuite Server5')
    SERVICE_VERSION = getattr(config_module, 'SERVICE_VERSION', '1.0.0')
    SERVICE_PORT = getattr(config_module, 'SERVICE_PORT', 8009)
    DEBUG = getattr(config_module, 'DEBUG', False)
    LOG_LEVEL = getattr(config_module, 'LOG_LEVEL', 'INFO')
    LOG_DIR = getattr(config_module, 'LOG_DIR', Path(__file__).parent.parent / "logs")
except ImportError:
    # 回退到默认值
    SERVICE_NAME = 'MySuite Server5'
    SERVICE_VERSION = '1.0.0'
    SERVICE_PORT = 8009
    DEBUG = False
    LOG_LEVEL = 'INFO'
    LOG_DIR = Path(__file__).parent.parent / "logs"

# 导入数据库客户端 - HTTP-only 模式也需要
from app.database import IMDBClient

# 只在非 HTTP-only 模式下导入微服务
if not HTTP_ONLY_MODE:
    from app.services import (
        ListenerService,
        PushWriterServiceFixed as PushWriterService,
        DataPullerService,
        OperationHandlerService
    )

# 导入路由
from app.routers import entries_router, timeprotab_router
from app.routers.chart_api import router as chart_router
from app.routers.department_api import router as department_router
from app.routers.entries_gateway import router as entries_gateway_router
from app.routers.client_entries_gateway import router as client_entries_gateway_router

# 配置日志
logging.basicConfig(
    level=LOG_LEVEL,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.StreamHandler(),
        logging.FileHandler(LOG_DIR / "server5.log")
    ]
)

logger = logging.getLogger(__name__)

class ServiceManager:
    """服务管理器 - 支持 HTTP-only 模式"""
    
    def __init__(self):
        self.http_only_mode = HTTP_ONLY_MODE
        # HTTP-only 模式下也需要数据库连接用于 API 路由
        self.imdb_client = IMDBClient()
        
        if not self.http_only_mode:
            # 只在非 HTTP-only 模式下初始化微服务
            self.f1_listener: ListenerService = None
            self.f2_push_writer: PushWriterService = None
            self.f3_data_puller: DataPullerService = None  
            self.f4_operation_handler: OperationHandlerService = None
        self.services_running = False
        self.shutdown_event = asyncio.Event()
    
    async def start_services(self):
        """启动服务 - 根据模式决定启动内容"""
        try:
            if self.http_only_mode:
                logger.info(f"{SERVICE_NAME} v{SERVICE_VERSION} 启动中 (HTTP-only 模式)...")
                logger.info("🌐 HTTP-only 模式: 仅启动 HTTP API，不启动微服务")
                
                # HTTP-only 模式下也需要连接数据库用于 API 路由
                db_connected = await self.imdb_client.connect()
                if not db_connected:
                    logger.error("❌ HTTP-only 模式下数据库连接失败")
                    return False
                
                logger.info("✅ HTTP-only 模式下数据库连接成功")
                self.services_running = True
                return True
            
            logger.info(f"{SERVICE_NAME} v{SERVICE_VERSION} 启动中 (完整模式)...")
            logger.info("启动f1-f4服务...")
            
            # 初始化所有服务
            self.f1_listener = ListenerService()
            self.f2_push_writer = PushWriterService()
            self.f3_data_puller = DataPullerService()
            self.f4_operation_handler = OperationHandlerService()
            
            # 按顺序启动服务
            services_to_start = [
                ("f1_listener", self.f1_listener),
                ("f2_push_writer", self.f2_push_writer),
                ("f3_data_puller", self.f3_data_puller),
                ("f4_operation_handler", self.f4_operation_handler)
            ]
            
            started_services = []
            for service_name, service in services_to_start:
                try:
                    logger.info(f"启动 {service_name}...")
                    started = await service.start()
                    if started:
                        started_services.append(service_name)
                        logger.info(f"{service_name} 启动成功")
                    else:
                        logger.error(f"{service_name} 启动失败")
                except Exception as e:
                    logger.error(f"{service_name} 启动异常: {e}")
            
            self.services_running = True
            if started_services:
                logger.info(f"服务启动完成，已启动: {started_services}")
            else:
                logger.info("以最小模式运行，无后台服务")
                
            return True
        except Exception as e:
            logger.error(f"服务启动失败: {e}")
            await self.stop_services()
            raise
    
    async def stop_services(self):
        """停止服务 - 根据模式决定停止内容"""
        try:
            if self.http_only_mode:
                logger.info("🌐 HTTP-only 模式: 断开数据库连接")
                if self.imdb_client:
                    await self.imdb_client.disconnect()
                self.services_running = False
                self.shutdown_event.set()
                return
            
            logger.info("🔄 正在停止所有f1-f4服务...")
            self.services_running = False
            self.shutdown_event.set()
            
            services_to_stop = [
                ("f4_operation_handler", self.f4_operation_handler),
                ("f3_data_puller", self.f3_data_puller),
                ("f2_push_writer", self.f2_push_writer),
                ("f1_listener", self.f1_listener)
            ]
            
            for service_name, service in services_to_stop:
                if service:
                    try:
                        logger.info(f"🔄 停止 {service_name}...")
                        await service.stop()
                        logger.info(f"✅ {service_name} 已停止")
                    except Exception as e:
                        logger.error(f"❌ 停止{service_name}失败: {e}")
        except Exception as e:
            logger.error(f"❌ 停止服务失败: {e}")
    
    async def get_all_status(self) -> dict:
        """获取服务状态 - 根据模式返回不同内容"""
        status = {
            "service_name": SERVICE_NAME,
            "version": SERVICE_VERSION,
            "mode": "HTTP-only" if self.http_only_mode else "完整模式",
            "services_running": self.services_running,
            "services": {}
        }
        
        if self.http_only_mode:
            # 检查数据库连接状态
            db_status = await self.imdb_client.get_connection_status() if self.imdb_client else {"status": "disconnected"}
            status["services"]["http_api"] = {
                "is_running": True,
                "description": "HTTP API 服务运行中"
            }
            status["services"]["database"] = {
                "is_running": db_status.get("status") == "connected",
                "description": "数据库连接状态",
                "details": db_status
            }
            return status
        
        # 完整模式下返回所有服务状态
        service_list = [
            ("f1_listener", self.f1_listener),
            ("f2_push_writer", self.f2_push_writer),
            ("f3_data_puller", self.f3_data_puller),
            ("f4_operation_handler", self.f4_operation_handler)
        ]
        
        for service_name, service in service_list:
            if service:
                try:
                    status["services"][service_name] = await service.get_status()
                except Exception as e:
                    status["services"][service_name] = {
                        "is_running": False,
                        "error": str(e)
                    }
            else:
                status["services"][service_name] = {
                    "is_running": False,
                    "error": "服务未初始化"
                }
        
        return status

# 全局服务管理器
service_manager = ServiceManager()

@asynccontextmanager
async def lifespan(app: FastAPI):
    """应用生命周期管理"""
    # 启动时
    try:
        await service_manager.start_services()
        yield
    finally:
        # 关闭时
        await service_manager.stop_services()

# 创建FastAPI应用
app = FastAPI(
    title=SERVICE_NAME,
    description=f"MySuite Server5 - {'HTTP API服务器' if HTTP_ONLY_MODE else 'MDB-PostgreSQL双向数据同步微服务'}",
    version=SERVICE_VERSION,
    debug=DEBUG,
    lifespan=lifespan
)

# 添加路由
app.include_router(entries_router)
app.include_router(timeprotab_router)
app.include_router(chart_router)
app.include_router(department_router)
app.include_router(entries_gateway_router)
app.include_router(client_entries_gateway_router)

# 配置CORS
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],  # 生产环境应该限制具体域名
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# ===== 基础路由 =====

@app.get("/")
async def root():
    """根路径 - 服务信息"""
    return {
        "service": SERVICE_NAME,
        "version": SERVICE_VERSION,
        "mode": "HTTP-only" if HTTP_ONLY_MODE else "完整模式",
        "status": "running",
        "debug": DEBUG
    }

@app.get("/health")
async def health_check():
    """健康检查"""
    try:
        status = await service_manager.get_all_status()
        
        if status["services_running"]:
            return JSONResponse(
                status_code=200,
                content={
                    "status": "healthy",
                    "timestamp": asyncio.get_event_loop().time(),
                    **status
                }
            )
        else:
            return JSONResponse(
                status_code=503,
                content={
                    "status": "unhealthy",
                    "message": "服务未运行",
                    **status
                }
            )
    except Exception as e:
        return JSONResponse(
            status_code=500,
            content={
                "status": "error",
                "message": str(e)
            }
        )

@app.get("/status")
async def get_status():
    """获取详细状态"""
    try:
        return await service_manager.get_all_status()
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))

# ===== 服务控制路由 (仅在完整模式下可用) =====

@app.post("/services/restart")
async def restart_services():
    """重启所有服务"""
    if HTTP_ONLY_MODE:
        raise HTTPException(status_code=503, detail="HTTP-only 模式下无微服务可重启")
    
    try:
        await service_manager.stop_services()
        await asyncio.sleep(2)
        await service_manager.start_services()
        
        return {"message": "服务重启成功"}
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"服务重启失败: {e}")

@app.post("/services/f1/partition-check")
async def trigger_partition_check(month_code: str = None):
    """手动触发分区检查"""
    if HTTP_ONLY_MODE:
        raise HTTPException(status_code=503, detail="HTTP-only 模式下 f1 服务不可用")
    
    try:
        if service_manager.f1_listener:
            result = await service_manager.f1_listener.trigger_partition_check(month_code)
            if result:
                return {"message": f"分区检查成功: {month_code or 'all'}"}
            else:
                raise HTTPException(status_code=500, detail="分区检查失败")
        else:
            raise HTTPException(status_code=503, detail="f1监听器服务未运行")
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))

# ===== 信号处理 =====

def signal_handler(signum, frame):
    """信号处理器"""
    logger.info(f"收到信号 {signum}，正在关闭服务...")
    asyncio.create_task(service_manager.stop_services())

# 注册信号处理器 - 只在主线程中注册
if __name__ == "__main__":
    signal.signal(signal.SIGINT, signal_handler)
    signal.signal(signal.SIGTERM, signal_handler)

# ===== 主函数 =====

def main():
    """主函数"""
    try:
        mode_info = "HTTP-only 模式" if HTTP_ONLY_MODE else "完整模式"
        logger.info(f"🌟 {SERVICE_NAME} v{SERVICE_VERSION} 启动 ({mode_info})")
        logger.info(f"🐛 调试模式: {DEBUG}")
        logger.info(f"📡 监听端口: {SERVICE_PORT}")
        
        # 确保日志目录存在
        LOG_DIR.mkdir(parents=True, exist_ok=True)
        
        uvicorn.run(
            "app.main:app",
            host="0.0.0.0",
            port=SERVICE_PORT,
            reload=DEBUG,
            log_level=LOG_LEVEL.lower(),
            access_log=True
        )
        
    except KeyboardInterrupt:
        logger.info("👋 接收到中断信号，正在关闭...")
    except Exception as e:
        logger.error(f"❌ 应用启动失败: {e}")
        sys.exit(1)

if __name__ == "__main__":
    main() 