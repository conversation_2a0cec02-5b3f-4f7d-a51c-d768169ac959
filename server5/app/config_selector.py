# server5/app/config_selector.py
# 2025/06/27 09：14 修改多平台server5
# 配置选择器 - 动态选择配置文件

import os
import platform
import socket
from pathlib import Path
import importlib
import sys

class ConfigSelector:
    """配置选择器"""
    
    @staticmethod
    def get_local_ip():
        """获取本地IP地址"""
        try:
            with socket.socket(socket.AF_INET, socket.SOCK_DGRAM) as s:
                s.connect(("*******", 80))
                return s.getsockname()[0]
        except:
            try:
                return socket.gethostbyname(socket.gethostname())
            except:
                return "127.0.0.1"
    
    @staticmethod
    def detect_config_scenario():
        """自动检测配置场景"""
        current_platform = platform.system()
        local_ip = ConfigSelector.get_local_ip()
        
        # 检测是否为Server6所在机器
        is_server6_machine = (
            local_ip == "************" or
            os.getenv("FORCE_LOCAL_CONFIG") == "true"
        )
        
        if current_platform == "Windows":
            if is_server6_machine:
                return "config_win10_local"
            else:
                return "config_win10_remote"
        else:  # Linux/其他
            return "config_ubuntu_remote"
    
    @staticmethod
    def load_config(config_name=None):
        """加载配置文件"""
        if not config_name:
            # 从环境变量获取或自动检测
            config_name = os.getenv("SERVER5_CONFIG") or ConfigSelector.detect_config_scenario()
        
        # 2025/06/27 09：14 修改多平台server5 - 动态配置加载
        print(f"🔧 加载配置: {config_name}")
        
        try:
            # 动态导入配置模块
            config_module = importlib.import_module(f"config.{config_name}")
            
            # 返回配置属性字典
            config_dict = {}
            for attr in dir(config_module):
                if not attr.startswith('_'):
                    config_dict[attr] = getattr(config_module, attr)
            
            return config_dict, config_name
            
        except ImportError as e:
            print(f"❌ 无法加载配置 {config_name}: {e}")
            # 回退到默认配置
            fallback_config = "config"
            print(f"🔄 回退到默认配置: {fallback_config}")
            
            try:
                config_module = importlib.import_module(f"config.{fallback_config}")
                config_dict = {}
                for attr in dir(config_module):
                    if not attr.startswith('_'):
                        config_dict[attr] = getattr(config_module, attr)
                return config_dict, fallback_config
            except ImportError:
                raise Exception(f"无法加载任何配置文件")
    
    @staticmethod
    def apply_config_to_module(config_dict, module_name="config.config"):
        """将配置应用到指定模块"""
        try:
            # 创建或获取配置模块
            if module_name in sys.modules:
                config_module = sys.modules[module_name]
            else:
                import types
                config_module = types.ModuleType(module_name)
                sys.modules[module_name] = config_module
            
            # 应用配置
            for key, value in config_dict.items():
                setattr(config_module, key, value)
            
            return config_module
            
        except Exception as e:
            print(f"❌ 配置应用失败: {e}")
            raise
    
    @staticmethod
    def get_current_config_info():
        """获取当前配置信息"""
        config_name = os.getenv("SERVER5_CONFIG", "auto-detect")
        current_platform = platform.system()
        local_ip = ConfigSelector.get_local_ip()
        hostname = socket.gethostname()
        
        return {
            "config_name": config_name,
            "platform": current_platform,
            "hostname": hostname,
            "local_ip": local_ip,
            "auto_detected": ConfigSelector.detect_config_scenario()
        }

# 全局配置加载器
def load_dynamic_config():
    """全局配置加载函数"""
    selector = ConfigSelector()
    config_dict, config_name = selector.load_config()
    config_module = selector.apply_config_to_module(config_dict)
    
    # 显示配置信息
    info = selector.get_current_config_info()
    print(f"📋 配置信息:")
    print(f"  - 配置文件: {config_name}")
    print(f"  - 平台: {info['platform']}")
    print(f"  - 主机: {info['hostname']}")
    print(f"  - IP: {info['local_ip']}")
    print(f"  - 自动检测: {info['auto_detected']}")
    
    return config_module

# 自动执行配置加载（如果作为模块导入）
if __name__ != "__main__":
    try:
        load_dynamic_config()
    except Exception as e:
        print(f"⚠️ 动态配置加载失败: {e}")

if __name__ == "__main__":
    # 测试配置选择器
    print("🧪 测试配置选择器")
    print("=" * 40)
    
    selector = ConfigSelector()
    info = selector.get_current_config_info()
    
    print("当前环境信息:")
    for key, value in info.items():
        print(f"  {key}: {value}")
    
    print("\n测试配置加载:")
    try:
        config_dict, config_name = selector.load_config()
        print(f"✅ 成功加载配置: {config_name}")
        print(f"   包含 {len(config_dict)} 个配置项")
    except Exception as e:
        print(f"❌ 配置加载失败: {e}") 