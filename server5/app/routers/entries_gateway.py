# server5/app/routers/entries_gateway.py
# UI到PostgreSQL entries表的网关服务
# 专门处理UI输入数据的写入，确保数据格式正确并触发同步

import logging
from typing import Dict, Any, Optional
from fastapi import APIRouter, HTTPException, Depends, Body
from pydantic import BaseModel, Field
from datetime import date, datetime

from app.database import IMDBClient
from app.services.f4_operation_handler import OperationHandlerService

logger = logging.getLogger(__name__)
router = APIRouter(prefix="/gateway/entries", tags=["Entries Gateway"])

# 依赖注入
async def get_imdb_client() -> IMDBClient:
    return IMDBClient()

async def get_operation_handler() -> OperationHandlerService:
    return OperationHandlerService()

# 请求模型
class UIEntryCreate(BaseModel):
    """UI输入数据模型"""
    entry_date: str = Field(..., description="作业日期 (YYYY/MM/DD 或 YYYY-MM-DD)")
    employee_id: str = Field(..., description="员工ID")
    duration: float = Field(..., description="工作时间")
    project_code: Optional[str] = Field(None, description="项目代码")
    status: Optional[str] = Field(None, description="状态")
    description: Optional[str] = Field(None, description="作业描述")
    department: Optional[str] = Field(None, description="部门")
    notes: Optional[str] = Field(None, description="备注")
    
    # UI特有的字段映射
    model: Optional[str] = Field(None, description="机种")
    number: Optional[str] = Field(None, description="号机")
    factory_number: Optional[str] = Field(None, description="工场製番")
    project_number: Optional[str] = Field(None, description="工事番号")
    unit_number: Optional[str] = Field(None, description="ユニット番号")
    category: Optional[int] = Field(None, description="区分")
    item: Optional[int] = Field(None, description="項目")

class UIEntryUpdate(BaseModel):
    """UI更新数据模型"""
    entry_date: Optional[str] = Field(None, description="作业日期")
    employee_id: Optional[str] = Field(None, description="员工ID")
    duration: Optional[float] = Field(None, description="工作时间")
    project_code: Optional[str] = Field(None, description="项目代码")
    status: Optional[str] = Field(None, description="状态")
    description: Optional[str] = Field(None, description="作业描述")
    department: Optional[str] = Field(None, description="部门")
    notes: Optional[str] = Field(None, description="备注")
    
    # UI特有的字段映射
    model: Optional[str] = Field(None, description="机种")
    number: Optional[str] = Field(None, description="号机")
    factory_number: Optional[str] = Field(None, description="工场製番")
    project_number: Optional[str] = Field(None, description="工事番号")
    unit_number: Optional[str] = Field(None, description="ユニット番号")
    category: Optional[int] = Field(None, description="区分")
    item: Optional[int] = Field(None, description="項目")

# 响应模型
class GatewayResponse(BaseModel):
    """网关响应模型"""
    success: bool = Field(..., description="操作是否成功")
    message: str = Field(..., description="响应消息")
    data: Optional[Dict[str, Any]] = Field(None, description="响应数据")
    entry_id: Optional[int] = Field(None, description="entries表ID")
    external_id: Optional[int] = Field(None, description="MDB外部ID")

def _convert_ui_to_entries_format(ui_data: Dict[str, Any]) -> Dict[str, Any]:
    """将UI数据转换为entries表格式"""
    entries_data = {}
    
    # 日期处理
    if 'entry_date' in ui_data and ui_data['entry_date']:
        date_str = ui_data['entry_date']
        if '/' in date_str:
            # 转换 YYYY/MM/DD 为 YYYY-MM-DD
            entries_data['entry_date'] = date_str.replace('/', '-')
        else:
            entries_data['entry_date'] = date_str
    
    # 基本字段映射
    field_mapping = {
        'employee_id': 'employee_id',
        'duration': 'duration',
        'project_code': 'project_code',
        'status': 'status',
        'description': 'description',
        'department': 'department',
        'notes': 'notes'
    }
    
    for ui_field, entries_field in field_mapping.items():
        if ui_field in ui_data and ui_data[ui_field] is not None:
            entries_data[entries_field] = ui_data[ui_field]
    
    # UI特有字段映射到entries表字段
    ui_specific_mapping = {
        'model': 'model',
        'number': 'number',
        'factory_number': 'factory_number',
        'project_number': 'project_number',
        'unit_number': 'unit_number',
        'category': 'category',
        'item': 'item'
    }
    
    for ui_field, entries_field in ui_specific_mapping.items():
        if ui_field in ui_data and ui_data[ui_field] is not None:
            entries_data[entries_field] = ui_data[ui_field]
    
    # 确保source字段为user
    entries_data['source'] = 'user'
    
    return entries_data

@router.post("/", response_model=GatewayResponse)
async def create_entry_from_ui(
    entry_data: UIEntryCreate,
    operation_handler: OperationHandlerService = Depends(get_operation_handler),
    imdb_client: IMDBClient = Depends(get_imdb_client)
):
    """
    从UI创建新的entries记录
    确保数据格式正确并触发同步到MDB
    """
    try:
        logger.info(f"📝 收到UI创建请求: employee_id={entry_data.employee_id}")
        
        # 转换为entries表格式
        entries_format_data = _convert_ui_to_entries_format(entry_data.dict())
        
        logger.info(f"📦 转换后的数据: {entries_format_data}")
        
        # 使用操作处理器创建记录
        result = await operation_handler.handle_insert_operation(entries_format_data)
        
        if result.get('success'):
            entry_id = result.get('entry_id')
            logger.info(f"✅ UI创建成功: entry_id={entry_id}")
            
            return GatewayResponse(
                success=True,
                message="记录创建成功，已触发MDB同步",
                data=result,
                entry_id=entry_id,
                external_id=None  # INSERT时external_id为NULL，等待f2同步后更新
            )
        else:
            error_msg = result.get('message', '创建失败')
            logger.error(f"❌ UI创建失败: {error_msg}")
            raise HTTPException(status_code=400, detail=error_msg)
            
    except Exception as e:
        logger.error(f"❌ UI创建异常: {e}", exc_info=True)
        raise HTTPException(status_code=500, detail=f"创建失败: {str(e)}")

@router.put("/{entry_id}", response_model=GatewayResponse)
async def update_entry_from_ui(
    entry_id: int,
    entry_data: UIEntryUpdate,
    operation_handler: OperationHandlerService = Depends(get_operation_handler),
    imdb_client: IMDBClient = Depends(get_imdb_client)
):
    """
    从UI更新entries记录
    确保数据格式正确并触发同步到MDB
    """
    try:
        logger.info(f"📝 收到UI更新请求: entry_id={entry_id}")
        
        # 转换为entries表格式
        entries_format_data = _convert_ui_to_entries_format(entry_data.dict(exclude_unset=True))
        
        logger.info(f"📦 转换后的数据: {entries_format_data}")
        
        # 使用操作处理器更新记录
        result = await operation_handler.handle_update_operation(entry_id, entries_format_data)
        
        if result.get('success'):
            logger.info(f"✅ UI更新成功: entry_id={entry_id}")
            
            return GatewayResponse(
                success=True,
                message="记录更新成功，已触发MDB同步",
                data=result,
                entry_id=entry_id,
                external_id=result.get('external_id')
            )
        else:
            error_msg = result.get('message', '更新失败')
            logger.error(f"❌ UI更新失败: {error_msg}")
            raise HTTPException(status_code=400, detail=error_msg)
            
    except Exception as e:
        logger.error(f"❌ UI更新异常: {e}", exc_info=True)
        raise HTTPException(status_code=500, detail=f"更新失败: {str(e)}")

@router.delete("/{entry_id}", response_model=GatewayResponse)
async def delete_entry_from_ui(
    entry_id: int,
    operation_handler: OperationHandlerService = Depends(get_operation_handler),
    imdb_client: IMDBClient = Depends(get_imdb_client)
):
    """
    从UI删除entries记录
    确保触发同步到MDB
    """
    try:
        logger.info(f"📝 收到UI删除请求: entry_id={entry_id}")
        
        # 使用操作处理器删除记录
        result = await operation_handler.handle_delete_operation(entry_id)
        
        if result.get('success'):
            logger.info(f"✅ UI删除成功: entry_id={entry_id}")
            
            return GatewayResponse(
                success=True,
                message="记录删除成功，已触发MDB同步",
                data=result,
                entry_id=entry_id,
                external_id=result.get('external_id')
            )
        else:
            error_msg = result.get('message', '删除失败')
            logger.error(f"❌ UI删除失败: {error_msg}")
            raise HTTPException(status_code=400, detail=error_msg)
            
    except Exception as e:
        logger.error(f"❌ UI删除异常: {e}", exc_info=True)
        raise HTTPException(status_code=500, detail=f"删除失败: {str(e)}")

@router.get("/status/{entry_id}")
async def get_entry_status(
    entry_id: int,
    imdb_client: IMDBClient = Depends(get_imdb_client)
):
    """
    获取entries记录的状态信息
    包括同步状态和MDB external_id
    """
    try:
        logger.info(f"📝 查询记录状态: entry_id={entry_id}")
        
        # 获取记录信息
        entry_info = await operation_handler.get_entry_info(entry_id)
        
        if entry_info.get('status') == 'found':
            entry = entry_info.get('entry', {})
            queue_status = entry_info.get('queue_status')
            
            return {
                "success": True,
                "entry_id": entry_id,
                "external_id": entry.get('external_id'),
                "synced": entry.get('external_id') is not None,
                "source": entry.get('source'),
                "queue_status": queue_status,
                "entry_data": entry
            }
        else:
            raise HTTPException(status_code=404, detail="记录不存在")
            
    except Exception as e:
        logger.error(f"❌ 查询记录状态异常: {e}", exc_info=True)
        raise HTTPException(status_code=500, detail=f"查询失败: {str(e)}")

@router.get("/sync-status")
async def get_sync_status(
    imdb_client: IMDBClient = Depends(get_imdb_client)
):
    """
    获取同步状态概览
    """
    try:
        # 统计信息
        total_entries = await imdb_client.fetch_one("SELECT COUNT(*) as count FROM entries")
        synced_entries = await imdb_client.fetch_one("SELECT COUNT(*) as count FROM entries WHERE external_id IS NOT NULL")
        pending_queue = await imdb_client.fetch_one("SELECT COUNT(*) as count FROM entries_push_queue WHERE synced = FALSE")
        
        return {
            "success": True,
            "total_entries": total_entries['count'],
            "synced_entries": synced_entries['count'],
            "pending_sync": pending_queue['count'],
            "sync_rate": f"{(synced_entries['count'] / total_entries['count'] * 100):.2f}%" if total_entries['count'] > 0 else "0%"
        }
        
    except Exception as e:
        logger.error(f"❌ 查询同步状态异常: {e}", exc_info=True)
        raise HTTPException(status_code=500, detail=f"查询失败: {str(e)}") 