# server5/app/routers/timeprotab_api.py
# Timeprotab分区表API路由
# 20250710 - 支持 HTTP-only 模式，修复数据格式问题，修复字段名错误

from fastapi import APIRouter, Depends, HTTPException, Query
from typing import List, Optional, Dict, Any
from datetime import datetime, date
from pydantic import BaseModel, Field
import logging
import sys
import os
from pathlib import Path

# 添加项目根目录到Python路径
sys.path.append(str(Path(__file__).parent.parent.parent))

from app.database import IMDBClient

logger = logging.getLogger(__name__)

router = APIRouter(prefix="/api/timeprotab", tags=["timeprotab"])

# 检查是否为 HTTP-only 模式
HTTP_ONLY_MODE = os.getenv('HTTP_ONLY_MODE', 'false').lower() == 'true'

class TimeprotabRecord(BaseModel):
    """Timeprotab记录模型"""
    id: int = Field(..., description="记录ID")
    employee_id: str = Field(..., description="员工ID")
    日付: date = Field(..., description="日期")
    星期: Optional[str] = Field(None, description="星期")
    ｶﾚﾝﾀﾞ: Optional[str] = Field(None, description="日历")
    不在: Optional[str] = Field(None, description="不在")
    勤務区分: Optional[str] = Field(None, description="勤务区分")
    事由: Optional[str] = Field(None, description="事由")
    出勤時刻: Optional[str] = Field(None, description="出勤时刻")
    ＭＣ_出勤: Optional[str] = Field(None, description="MC出勤")
    退勤時刻: Optional[str] = Field(None, description="退勤时刻")
    ＭＣ_退勤: Optional[str] = Field(None, description="MC退勤")
    所定時間: Optional[str] = Field(None, description="所定时间")
    早出残業: Optional[str] = Field(None, description="早出残業")
    内深夜残業: Optional[str] = Field(None, description="内深夜残業")
    遅刻早退: Optional[str] = Field(None, description="迟到早退")
    休出時間: Optional[str] = Field(None, description="休出时间")
    出張残業: Optional[str] = Field(None, description="出差残業")
    外出時間: Optional[str] = Field(None, description="外出时间")
    戻り時間: Optional[str] = Field(None, description="返回时间")
    コメント: Optional[str] = Field(None, description="评论")
    created_at: Optional[datetime] = Field(None, description="创建时间")
    updated_at: Optional[datetime] = Field(None, description="更新时间")

class TimeprotabResponse(BaseModel):
    """Timeprotab响应模型 - 符合客户端期望的格式"""
    success: bool = Field(..., description="是否成功")
    data: List[Dict[str, Any]] = Field(..., description="数据列表")
    total: int = Field(..., description="总记录数")
    message: str = Field(..., description="响应消息")

# ===== 依赖注入 =====

async def get_imdb_client() -> IMDBClient:
    """获取IMDB客户端"""
    if HTTP_ONLY_MODE:
        # HTTP-only 模式下从 service_manager 获取数据库连接
        from app.main import service_manager
        if service_manager and service_manager.imdb_client:
            return service_manager.imdb_client
        else:
            raise HTTPException(status_code=503, detail="HTTP-only 模式下数据库连接不可用")
    else:
        # 完整模式下从 service_manager 获取数据库连接
        from app.main import service_manager
        if service_manager and hasattr(service_manager, 'imdb_client'):
            return service_manager.imdb_client
        else:
            raise HTTPException(status_code=503, detail="数据库连接不可用")

# ===== API 路由 =====

@router.get("/")
async def get_timeprotab_data(
    employee_id: Optional[str] = Query(None, description="员工ID过滤"),
    year: Optional[int] = Query(None, description="年份"),
    month: Optional[int] = Query(None, description="月份"),
    start_date: Optional[date] = Query(None, description="开始日期"),
    end_date: Optional[date] = Query(None, description="结束日期"),
    limit: int = Query(1000, ge=1, le=10000, description="返回条数限制"),
    offset: int = Query(0, ge=0, description="偏移量"),
    imdb_client: IMDBClient = Depends(get_imdb_client)
):
    """获取timeprotab数据 - 返回客户端期望的格式"""
    try:
        # 构建查询条件
        conditions = []
        params = []
        
        if employee_id:
            conditions.append("employee_id = ${}".format(len(params) + 1))
            params.append(employee_id)
        
        if year and month:
            # 如果指定了年月，构建月份范围
            start_date = date(year, month, 1)
            if month == 12:
                end_date = date(year + 1, 1, 1)
            else:
                end_date = date(year, month + 1, 1)
            
            conditions.append("日付 >= ${}".format(len(params) + 1))
            params.append(start_date)
            conditions.append("日付 < ${}".format(len(params) + 1))
            params.append(end_date)
        else:
            # 使用日期范围
            if start_date:
                conditions.append("日付 >= ${}".format(len(params) + 1))
                params.append(start_date)
            
            if end_date:
                conditions.append("日付 <= ${}".format(len(params) + 1))
                params.append(end_date)
        
        # 构建完整查询 - 修复字段名错误 ｶﾞﾚﾝﾀﾞ -> ｶﾚﾝﾀﾞ
        where_clause = " AND ".join(conditions) if conditions else "1=1"
        
        query = f"""
            SELECT 
                id,
                employee_id,
                日付,
                星期,
                ｶﾚﾝﾀﾞ,
                不在,
                勤務区分,
                事由,
                出勤時刻,
                ＭＣ_出勤,
                退勤時刻,
                ＭＣ_退勤,
                所定時間,
                早出残業,
                内深夜残業,
                遅刻早退,
                休出時間,
                出張残業,
                外出時間,
                戻り時間,
                コメント,
                created_at,
                updated_at
            FROM timeprotab
            WHERE {where_clause}
            ORDER BY 日付 DESC, id DESC
            LIMIT ${len(params) + 1} OFFSET ${len(params) + 2}
        """
        
        params.extend([limit, offset])
        
        records = await imdb_client.execute_query(query, *params)
        
        # 转换数据格式为客户端期望的格式
        formatted_records = []
        for record in records:
            formatted_record = {
                "id": record.get("id"),
                "employee_id": record.get("employee_id"),
                "日付": record.get("日付").isoformat() if record.get("日付") else None,
                "星期": record.get("星期"),
                "ｶﾚﾝﾀﾞ": record.get("ｶﾚﾝﾀﾞ"),
                "不在": record.get("不在"),
                "勤務区分": record.get("勤務区分"),
                "事由": record.get("事由"),
                "出勤時刻": str(record.get("出勤時刻")) if record.get("出勤時刻") else None,
                "ＭＣ_出勤": record.get("ＭＣ_出勤"),
                "退勤時刻": str(record.get("退勤時刻")) if record.get("退勤時刻") else None,
                "ＭＣ_退勤": record.get("ＭＣ_退勤"),
                "所定時間": record.get("所定時間"),
                "早出残業": record.get("早出残業"),
                "内深夜残業": record.get("内深夜残業"),
                "遅刻早退": record.get("遅刻早退"),
                "休出時間": record.get("休出時間"),
                "出張残業": record.get("出張残業"),
                "外出時間": str(record.get("外出時間")) if record.get("外出時間") else None,
                "戻り時間": str(record.get("戻り時間")) if record.get("戻り時間") else None,
                "コメント": record.get("コメント"),
                "created_at": record.get("created_at").isoformat() if record.get("created_at") else None,
                "updated_at": record.get("updated_at").isoformat() if record.get("updated_at") else None
            }
            formatted_records.append(formatted_record)
        
        logger.info(f"📊 Timeprotab查询完成: {len(formatted_records)}条记录")
        
        # 返回客户端期望的格式
        return {
            "ok": True,
            "data": formatted_records,
            "total": len(formatted_records),
            "message": "查询成功"
        }
        
    except Exception as e:
        logger.error(f"❌ 获取timeprotab数据失败: {e}")
        return {
            "ok": False,
            "data": [],
            "total": 0,
            "message": f"获取timeprotab数据失败: {str(e)}"
        }

@router.get("/months")
async def get_timeprotab_months(
    employee_id: str = Query(..., description="员工ID"),
    imdb_client: IMDBClient = Depends(get_imdb_client)
):
    """获取timeprotab可用月份列表"""
    try:
        query = """
            SELECT DISTINCT
                EXTRACT(YEAR FROM 日付) as year,
                EXTRACT(MONTH FROM 日付) as month,
                COUNT(*) as record_count
            FROM timeprotab
            WHERE employee_id = $1
            GROUP BY EXTRACT(YEAR FROM 日付), EXTRACT(MONTH FROM 日付)
            ORDER BY year DESC, month DESC
        """
        
        records = await imdb_client.execute_query(query, employee_id)
        
        # 格式化月份列表
        months = []
        for record in records:
            year = int(record["year"])
            month = int(record["month"])
            months.append({
                "year": year,
                "month": month,
                "display_name": f"{year}年{month:02d}月",
                "record_count": record["record_count"]
            })
        
        return {"ok": True, "data": months, "message": "查询成功"}
        
    except Exception as e:
        logger.error(f"❌ 获取timeprotab月份失败: {e}")
        return {"ok": False, "data": [], "message": f"获取timeprotab月份失败: {str(e)}"}

@router.get("/stats")
async def get_timeprotab_stats(
    employee_id: str = Query(..., description="员工ID"),
    year: int = Query(..., description="年份"),
    month: int = Query(..., description="月份"),
    imdb_client: IMDBClient = Depends(get_imdb_client)
):
    """获取timeprotab统计数据"""
    try:
        # 构建月份范围
        start_date = date(year, month, 1)
        if month == 12:
            end_date = date(year + 1, 1, 1)
        else:
            end_date = date(year, month + 1, 1)
        
        query = """
            SELECT 
                COUNT(*) as total_records,
                COUNT(DISTINCT 日付) as work_days,
                COUNT(CASE WHEN 所定時間 IS NOT NULL AND 所定時間 != '0' THEN 1 END) as scheduled_days,
                COUNT(CASE WHEN 早出残業 IS NOT NULL AND 早出残業 != '0' THEN 1 END) as overtime_days
            FROM timeprotab
            WHERE employee_id = $1 
              AND 日付 >= $2 
              AND 日付 < $3
        """
        
        result = await imdb_client.execute_query(query, employee_id, start_date, end_date)
        
        if result:
            stats = result[0]
            return {
                "ok": True,
                "data": {
                    "employee_id": employee_id,
                    "year": year,
                    "month": month,
                    "total_records": stats["total_records"],
                    "work_days": stats["work_days"],
                    "scheduled_days": stats["scheduled_days"],
                    "overtime_days": stats["overtime_days"]
                },
                "message": "查询成功"
            }
        else:
            return {
                "ok": True,
                "data": {
                    "employee_id": employee_id,
                    "year": year,
                    "month": month,
                    "total_records": 0,
                    "work_days": 0,
                    "scheduled_days": 0,
                    "overtime_days": 0
                },
                "message": "无数据"
            }
        
    except Exception as e:
        logger.error(f"❌ 获取timeprotab统计失败: {e}")
        return {
            "ok": False,
            "data": {},
            "message": f"获取timeprotab统计失败: {str(e)}"
        } 