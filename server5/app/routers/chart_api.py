# server5/app/routers/chart_api.py
# 图表API路由 - 匹配客户端期望的路径
# 20250710 - 支持 HTTP-only 模式，适配客户端请求路径

from fastapi import APIRouter, Depends, HTTPException, Query
from typing import List, Optional, Dict, Any
from datetime import datetime, date, timedelta
import logging
import sys
import os
from pathlib import Path

# 添加项目根目录到Python路径
sys.path.append(str(Path(__file__).parent.parent.parent))

from app.database import IMDBClient

logger = logging.getLogger(__name__)

router = APIRouter(prefix="/api/chart", tags=["chart"])

# 检查是否为 HTTP-only 模式
HTTP_ONLY_MODE = os.getenv('HTTP_ONLY_MODE', 'false').lower() == 'true'

# ===== 依赖注入 =====

async def get_imdb_client() -> IMDBClient:
    """获取IMDB客户端"""
    if HTTP_ONLY_MODE:
        # HTTP-only 模式下从 service_manager 获取数据库连接
        from app.main import service_manager
        if service_manager and service_manager.imdb_client:
            return service_manager.imdb_client
        else:
            raise HTTPException(status_code=503, detail="HTTP-only 模式下数据库连接不可用")
    else:
        # 完整模式下从 service_manager 获取数据库连接
        from app.main import service_manager
        if service_manager and hasattr(service_manager, 'imdb_client'):
            return service_manager.imdb_client
        else:
            raise HTTPException(status_code=503, detail="数据库连接不可用")

# ===== API 路由 =====

@router.get("/months")
async def get_chart_months(
    employee_id: str = Query(..., description="员工ID"),
    imdb_client: IMDBClient = Depends(get_imdb_client)
):
    """获取图表可用月份列表 - 匹配客户端期望的路径"""
    try:
        # 从 entries 表获取可用月份
        query = """
            SELECT DISTINCT
                EXTRACT(YEAR FROM entry_date) as year,
                EXTRACT(MONTH FROM entry_date) as month,
                COUNT(*) as record_count
            FROM entries
            WHERE employee_id = $1
            GROUP BY EXTRACT(YEAR FROM entry_date), EXTRACT(MONTH FROM entry_date)
            ORDER BY year DESC, month DESC
        """
        
        records = await imdb_client.execute_query(query, employee_id)
        
        # 格式化月份列表
        months = []
        for record in records:
            year = int(record["year"])
            month = int(record["month"])
            months.append({
                "year": year,
                "month": month,
                "display_name": f"{year}年{month:02d}月",
                "record_count": record["record_count"]
            })
        
        return {"ok": True, "data": months, "message": "查询成功"}
        
    except Exception as e:
        logger.error(f"❌ 获取图表月份失败: {e}")
        return {"ok": False, "data": [], "message": f"获取图表月份失败: {str(e)}"}

def convert_timeprotab_time_to_hours(time_str: str, is_overtime: bool = False) -> float:
    """
    转换timeprotab表的时间格式为小时数
    
    Args:
        time_str: 时间字符串，如 "07:50", "01:30"
        is_overtime: 是否为加班时间（早出残業）
        
    Returns:
        转换后的小时数
    """
    if not time_str or time_str.strip() == "":
        return 0.0
    
    try:
        # 分割小时和分钟
        parts = time_str.strip().split(":")
        if len(parts) != 2:
            return 0.0
            
        hours = int(parts[0])
        minutes = int(parts[1])
        
        if is_overtime:
            # 早出残業：标准转换，半小时间隔 (30分钟 = 0.5小时)
            return hours + (minutes / 60.0)
        else:
            # 所定時間：特殊规则
            if minutes == 50:
                # 07:50 → 8.0, 03:30 → 4.0 的特殊规则
                return float(hours + 1)
            elif minutes == 30:
                return float(hours + 1)
            else:
                # 其他情况按标准转换
                return hours + (minutes / 60.0)
                
    except (ValueError, IndexError):
        logger.warning(f"无法转换时间格式: {time_str}")
        return 0.0

@router.get("/generate")
async def generate_chart_data(
    employee_id: str = Query(..., description="员工ID"),
    start_date: date = Query(..., description="开始日期"),
    end_date: date = Query(..., description="结束日期"),
    chart_type: str = Query("daily", description="图表类型: daily, weekly, monthly"),
    imdb_client: IMDBClient = Depends(get_imdb_client)
):
    """生成图表数据 - 双表比较逻辑（entries vs timeprotab）"""
    try:
        # 1. 从 entries 表获取数据（按日期分组的duration总和）
        entries_query = """
            SELECT 
                entry_date,
                SUM(CAST(duration AS FLOAT)) as total_duration
            FROM entries
            WHERE employee_id = $1 
              AND entry_date >= $2 
              AND entry_date <= $3
            GROUP BY entry_date
            ORDER BY entry_date
        """
        
        entries_records = await imdb_client.execute_query(entries_query, employee_id, start_date, end_date)
        
        # 2. 从 timeprotab 表获取数据（需要动态查找分区表）
        year_month = start_date.strftime("%y%m")  # 2025-07 → 2507
        timeprotab_table = f"timeprotab_{year_month}"
        
        timeprotab_query = f"""
            SELECT 
                "日付",
                "所定時間",
                "早出残業"
            FROM {timeprotab_table}
            WHERE employee_id = $1 
              AND "日付" >= $2 
              AND "日付" <= $3
            ORDER BY "日付"
        """
        
        try:
            timeprotab_records = await imdb_client.execute_query(timeprotab_query, employee_id, start_date, end_date)
        except Exception as e:
            logger.warning(f"查询timeprotab表失败，可能分区不存在: {e}")
            timeprotab_records = []
        
        # 3. 构建日期字典用于比较
        entries_data = {}
        for record in entries_records:
            date_key = record["entry_date"].strftime("%Y-%m-%d")
            entries_data[date_key] = float(record["total_duration"] or 0)
        
        timeprotab_data = {}
        for record in timeprotab_records:
            date_key = record["日付"].strftime("%Y-%m-%d")
            
            # 转换时间格式
            standard_hours = convert_timeprotab_time_to_hours(record["所定時間"], is_overtime=False)
            overtime_hours = convert_timeprotab_time_to_hours(record["早出残業"], is_overtime=True)
            total_timeprotab_hours = standard_hours + overtime_hours
            
            timeprotab_data[date_key] = total_timeprotab_hours
        
        # 4. 从timeprotab表获取出勤退勤时刻数据（用于Chart的Y轴显示）
        timeprotab_display_query = f"""
            SELECT 
                "日付",
                "出勤時刻",
                "退勤時刻"
            FROM {timeprotab_table}
            WHERE employee_id = $1 
              AND "日付" >= $2 
              AND "日付" <= $3
            ORDER BY "日付"
        """
        
        try:
            timeprotab_display_records = await imdb_client.execute_query(timeprotab_display_query, employee_id, start_date, end_date)
        except Exception as e:
            logger.warning(f"查询timeprotab显示数据失败: {e}")
            timeprotab_display_records = []

        # 5. 生成所有日期范围内的Chart数据
        chart_days = []
        
        current_date = start_date
        while current_date <= end_date:
            date_str = current_date.strftime("%Y-%m-%d")
            
            # 获取entries和timeprotab的工时数据用于比较
            entries_hours = entries_data.get(date_str, 0.0)
            timeprotab_hours = timeprotab_data.get(date_str, 0.0)
            
            # 比较逻辑：相差小于0.1小时认为相等
            is_matched = abs(entries_hours - timeprotab_hours) < 0.1
            
            # 获取出勤退勤时刻用于Chart显示
            clock_in_time = None
            clock_out_time = None
            
            for display_record in timeprotab_display_records:
                if display_record["日付"].strftime("%Y-%m-%d") == date_str:
                    clock_in_time = display_record["出勤時刻"]
                    clock_out_time = display_record["退勤時刻"]
                    break
            
            chart_days.append({
                "date": date_str,
                "day": current_date.day,
                "entries_hours": entries_hours,
                "timeprotab_hours": timeprotab_hours,
                "is_matched": is_matched,
                "clock_in": clock_in_time.strftime("%H:%M") if clock_in_time else None,
                "clock_out": clock_out_time.strftime("%H:%M") if clock_out_time else None,
                "weekday": current_date.weekday()  # 0=Monday, 6=Sunday
            })
            
            current_date += timedelta(days=1)
        
        # 6. 如果没有任何数据，返回空结果
        if not chart_days or not any(day["clock_in"] or day["clock_out"] for day in chart_days):
            month_name = f"{start_date.year}年{start_date.month:02d}月"
            return {
                "ok": True,
                "data": {
                    "chart_days": [],
                    "month_name": month_name,
                    "year": start_date.year,
                    "month": start_date.month
                },
                "message": "无timeprotab显示数据"
            }
        
        # 7. 生成Chart数据（符合WorkTimeApp.py的格式）
        month_name = f"{start_date.year}年{start_date.month:02d}月"
        
        # 统计信息
        matched_days = len([day for day in chart_days if day["is_matched"]])
        unmatched_days = len([day for day in chart_days if not day["is_matched"] and (day["clock_in"] or day["clock_out"])])
        working_days = len([day for day in chart_days if day["clock_in"] or day["clock_out"]])
        
        chart_data = {
            "chart_days": chart_days,  # 每天的详细数据
            "month_name": month_name,
            "year": start_date.year,
            "month": start_date.month,
            "statistics": {
                "total_days": len(chart_days),
                "working_days": working_days,
                "matched_days": matched_days,
                "unmatched_days": unmatched_days,
                "entries_count": len([x for x in entries_data.values() if x > 0]),
                "timeprotab_count": len([x for x in timeprotab_data.values() if x > 0])
            }
        }
        
        logger.info(f"📊 Chart数据生成完成: {len(chart_days)}天, 工作日: {working_days}, 匹配: {matched_days}, 不匹配: {unmatched_days}")
        return {"ok": True, "data": chart_data, "message": "Chart数据生成成功"}
        
    except Exception as e:
        logger.error(f"❌ 生成图表数据失败: {e}")
        return {"ok": False, "data": {}, "message": f"生成图表数据失败: {str(e)}"}

@router.get("/summary")
async def get_chart_summary(
    employee_id: str = Query(..., description="员工ID"),
    year: int = Query(..., description="年份"),
    month: int = Query(..., description="月份"),
    imdb_client: IMDBClient = Depends(get_imdb_client)
):
    """获取图表摘要数据"""
    try:
        # 构建月份范围
        start_date = date(year, month, 1)
        if month == 12:
            end_date = date(year + 1, 1, 1)
        else:
            end_date = date(year, month + 1, 1)
        
        # 获取统计数据
        query = """
            SELECT 
                COUNT(*) as total_entries,
                COUNT(DISTINCT entry_date) as work_days,
                SUM(CAST(duration AS FLOAT)) as total_duration,
                AVG(CAST(duration AS FLOAT)) as avg_duration
            FROM entries
            WHERE employee_id = $1 
              AND entry_date >= $2 
              AND entry_date < $3
        """
        
        result = await imdb_client.execute_query(query, employee_id, start_date, end_date)
        
        if result:
            stats = result[0]
            total_duration = float(stats["total_duration"] or 0)
            avg_duration = float(stats["avg_duration"] or 0)
            
            # 计算加班时间（简化计算）
            work_days = stats["work_days"] or 0
            standard_hours = work_days * 8.0
            overtime_hours = max(0, total_duration - standard_hours)
            
            summary = {
                "employee_id": employee_id,
                "year": year,
                "month": month,
                "month_name": f"{year}年{month:02d}月",
                "total_entries": stats["total_entries"],
                "work_days": work_days,
                "total_duration": round(total_duration, 2),
                "avg_duration": round(avg_duration, 2),
                "standard_hours": standard_hours,
                "overtime_hours": round(overtime_hours, 2)
            }
            
            return {"ok": True, "data": summary, "message": "查询成功"}
        else:
            return {"ok": True, "data": {}, "message": "无数据"}
        
    except Exception as e:
        logger.error(f"❌ 获取图表摘要失败: {e}")
        return {"ok": False, "data": {}, "message": f"获取图表摘要失败: {str(e)}"} 