# server5/app/database/odbc_client.py
# ODBC客户端 - 跨平台兼容Access MDB数据库连接
# 25.06.26 增加多平台自动检测功能

import asyncio
import logging
from typing import Optional, Dict, List, Any
from datetime import datetime
import sys
import platform
from pathlib import Path
sys.path.append(str(Path(__file__).parent.parent.parent))

from config.config import PlatformConfig
import os

logger = logging.getLogger(__name__)

class ODBCClient:
    """ODBC数据库客户端 - 跨平台兼容"""
    
    def __init__(self):
        # 25.06.26 增加多平台自动检测
        self.platform_name = platform.system()
        self.is_windows = self.platform_name == 'Windows'
        self.is_linux = self.platform_name == 'Linux'
        self.is_darwin = self.platform_name == 'Darwin'  # macOS
        
        self.odbc_available = PlatformConfig.get_odbc_available()
        self.mdb_path = PlatformConfig.get_mdb_path()
        self.connection = None
        
        logger.info(f"🌐 检测到操作系统: {self.platform_name}")
        
        if self.is_windows:
            logger.info("🖥️ Windows环境 - 尝试初始化ODBC连接")
            self._init_windows_env()
        elif self.is_linux:
            logger.info("🐧 Linux环境 - 使用模拟/远程ODBC功能")
            self._init_linux_env()
        elif self.is_darwin:
            logger.info("🍎 macOS环境 - 使用模拟ODBC功能")
            self._init_macos_env()
        else:
            logger.warning(f"⚠️ 未识别的操作系统: {self.platform_name}")
    
    def _init_windows_env(self):
        """初始化Windows环境"""
        try:
            import win32com.client
            import pythoncom
            self.win32_available = True
            logger.info("✅ win32com模块可用")
        except ImportError:
            self.win32_available = False
            logger.warning("❌ win32com模块不可用，将使用模拟模式")
    
    def _init_linux_env(self):
        """初始化Linux环境"""
        self.win32_available = False
        # 在Linux上可以考虑使用远程连接或ODBC驱动
        logger.info("🔧 Linux环境初始化，使用模拟/远程模式")
    
    def _init_macos_env(self):
        """初始化macOS环境"""
        self.win32_available = False
        logger.info("🔧 macOS环境初始化，使用模拟模式")
    
    async def connect(self) -> bool:
        """建立MDB数据库连接"""
        # 25.06.26 增加多平台连接逻辑
        if self.is_windows and self.win32_available:
            # Windows环境下的真实ODBC连接
            return await self._windows_connect()
        elif self.is_linux:
            # Linux环境下的模拟/远程连接
            return await self._linux_connect()
        elif self.is_darwin:
            # macOS环境下的模拟连接
            return await self._macos_connect()
        else:
            # 其他平台的模拟连接
            return await self._mock_connect()
    
    async def _windows_connect(self) -> bool:
        """Windows环境下的ODBC连接"""
        try:
            import win32com.client
            import pythoncom
            
            def _sync_connect():
                pythoncom.CoInitialize()
                try:
                    access = win32com.client.Dispatch("Access.Application")
                    access.Visible = False
                    
                    # 尝试关闭已打开的数据库
                    try:
                        access.CloseCurrentDatabase()
                    except:
                        pass
                    
                    # 尝试以独占模式打开
                    try:
                        access.OpenCurrentDatabase(self.mdb_path, True)  # 独占模式
                    except:
                        # 如果独占模式失败，尝试只读模式
                        access.OpenCurrentDatabase(self.mdb_path, False, True)  # 只读模式
                    
                    return access
                except Exception as e:
                    pythoncom.CoUninitialize()
                    raise e
            
            # 在线程池中执行同步连接
            loop = asyncio.get_event_loop()
            self.connection = await loop.run_in_executor(None, _sync_connect)
            
            logger.info(f"Windows ODBC连接成功: {self.mdb_path}")
            return True
            
        except ImportError:
            logger.error("pywin32未安装，无法连接Access数据库")
            return False
        except Exception as e:
            logger.error(f"Windows ODBC连接失败: {e}")
            # 如果连接失败，切换到模拟模式
            logger.warning("切换到模拟模式继续运行")
            return await self._mock_connect()
    
    async def _linux_connect(self) -> bool:
        """Linux环境下的连接 - 可以是模拟或远程"""
        try:
            # 25.06.26 Linux环境下可选择远程连接Windows上的MDB服务
            # 这里可以实现HTTP API调用或其他远程连接方式
            
            # 方案1：模拟连接（用于开发和测试）
            if PlatformConfig.get_config().get("use_mock_in_linux", True):
                return await self._mock_connect()
            
            # 方案2：远程连接（需要在Windows上部署MDB服务）
            # return await self._remote_connect()
            
            return await self._mock_connect()
            
        except Exception as e:
            logger.error(f"❌ Linux连接失败: {e}")
            return False
    
    async def _macos_connect(self) -> bool:
        """macOS环境下的模拟连接"""
        return await self._mock_connect()
    
    async def _mock_connect(self) -> bool:
        """通用模拟连接"""
        try:
            # 创建模拟MDB文件用于测试
            os.makedirs(os.path.dirname(self.mdb_path), exist_ok=True)
            
            # 简单的SQLite文件模拟MDB
            import sqlite3
            self.connection = sqlite3.connect(self.mdb_path)
            
            # 创建模拟的表结构
            await self._create_mock_tables()
            
            logger.info(f"✅ 模拟ODBC连接成功: {self.mdb_path}")
            return True
            
        except Exception as e:
            logger.error(f"❌ 模拟连接失败: {e}")
            return False
    
    async def _create_mock_tables(self):
        """创建模拟的MDB表结构"""
        if not self.connection:
            return
        
        # 创建元作業時間表的模拟结构
        create_table_sql = """
            CREATE TABLE IF NOT EXISTS 元作業時間 (
                ID INTEGER PRIMARY KEY AUTOINCREMENT,
                従業員ｺｰﾄﾞ TEXT,
                日付 DATE,
                機種 TEXT,
                号機 TEXT,
                工場製番 TEXT,
                工事番号 TEXT,
                ﾕﾆｯﾄ番号 TEXT,
                区分 TEXT,
                項目 TEXT,
                時間 REAL,
                所属ｺｰﾄﾞ TEXT,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            )
        """
        
        self.connection.execute(create_table_sql)
        self.connection.commit()
        
        logger.debug("📋 模拟MDB表结构创建完成")
    
    async def disconnect(self):
        """关闭数据库连接"""
        if not self.connection:
            return
        
        try:
            if self.is_windows:
                await self._windows_disconnect()
            else:
                await self._mock_disconnect()
                
            logger.info("🔌 ODBC连接已关闭")
            
        except Exception as e:
            logger.error(f"❌ 关闭ODBC连接失败: {e}")
    
    async def _windows_disconnect(self):
        """Windows环境下关闭连接"""
        def _sync_disconnect():
            import pythoncom
            try:
                if self.connection:
                    self.connection.CloseCurrentDatabase()
                    self.connection.Quit()
            finally:
                pythoncom.CoUninitialize()
        
        loop = asyncio.get_event_loop()
        await loop.run_in_executor(None, _sync_disconnect)
        self.connection = None
    
    async def _mock_disconnect(self):
        """Ubuntu环境下关闭模拟连接"""
        if self.connection:
            self.connection.close()
            self.connection = None
    
    async def insert_work_time(self, data: Dict) -> Optional[int]:
        """插入工时记录并返回ID - 25.06.26 增加多平台支持"""
        if not self.connection:
            raise RuntimeError("ODBC连接未建立")
        
        if self.is_windows and self.win32_available:
            return await self._windows_insert(data)
        else:
            return await self._mock_insert(data)
    
    async def _windows_insert(self, data: Dict) -> Optional[int]:
        """Windows环境下的真实插入 - 使用win32com直接操作MDB"""
        def _sync_insert():
            import pythoncom
            pythoncom.CoInitialize()
            try:
                db = self.connection.CurrentDb()
                
                # 25.06.26 使用db_utils.py中验证过的SQL语句格式
                sql = f"""INSERT INTO 元作業時間
                    (従業員ｺｰﾄﾞ, 日付, 機種, 号機, 工場製番, 工事番号,
                     ﾕﾆｯﾄ番号, 区分, 項目, 時間, 所属ｺｰﾄﾞ)
                   VALUES
                    ('{data['employee_id']}', 
                     #{data['entry_date']}#,
                     {f"'{data.get('model', '')}'" if data.get('model') else 'NULL'},
                     {f"'{data.get('number', '')}'" if data.get('number') else 'NULL'},
                     {f"'{data.get('factory_number', '')}'" if data.get('factory_number') else 'NULL'},
                     {f"'{data.get('project_number', '')}'" if data.get('project_number') else 'NULL'},
                     {f"'{data.get('unit_number', '')}'" if data.get('unit_number') else 'NULL'},
                     '{data['category']}',
                     '{data['item']}',
                     {data['time_hours']},
                     '{data['department_code']}'
                    )"""
                
                db.Execute(sql)
                
                # 获取新插入的ID (使用@@IDENTITY)
                rs = db.OpenRecordset("SELECT @@IDENTITY AS NewID")
                new_id = rs.Fields("NewID").Value
                rs.Close()
                
                logger.debug(f"✅ Windows插入成功，新ID: {new_id}")
                return new_id
                
            except Exception as e:
                logger.error(f"❌ Windows插入失败: {e}")
                raise e
            finally:
                pythoncom.CoUninitialize()
        
        try:
            loop = asyncio.get_event_loop()
            result = await loop.run_in_executor(None, _sync_insert)
            return result
        except Exception as e:
            logger.error(f"❌ Windows异步插入失败: {e}")
            return None
    
    async def _mock_insert(self, data: Dict) -> Optional[int]:
        """Ubuntu环境下的模拟插入"""
        try:
            sql = """
                INSERT INTO 元作業時間 (
                    従業員ｺｰﾄﾞ, 日付, 機種, 号機, 工場製番, 工事番号,
                    ﾕﾆｯﾄ番号, 区分, 項目, 時間, 所属ｺｰﾄﾞ
                ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
            """
            
            cursor = self.connection.cursor()
            cursor.execute(sql, (
                data['employee_id'],
                data['entry_date'],
                data.get('model'),
                data.get('number'),
                data.get('factory_number'),
                data.get('project_number'),
                data.get('unit_number'),
                data['category'],
                data['item'],
                data['time_hours'],
                data['department_code']
            ))
            
            new_id = cursor.lastrowid
            self.connection.commit()
            cursor.close()
            
            logger.debug(f"🔄 模拟插入成功，ID: {new_id}")
            return new_id
            
        except Exception as e:
            logger.error(f"❌ 模拟插入失败: {e}")
            return None
    
    async def update_work_time(self, external_id: int, data: Dict) -> bool:
        """更新工时记录 - 25.06.26 增加多平台支持"""
        if not self.connection:
            raise RuntimeError("ODBC连接未建立")
        
        if self.is_windows and self.win32_available:
            return await self._windows_update(external_id, data)
        else:
            return await self._mock_update(external_id, data)
    
    async def _windows_update(self, external_id: int, data: Dict) -> bool:
        """Windows环境下的真实更新"""
        def _sync_update():
            import pythoncom
            pythoncom.CoInitialize()
            try:
                db = self.connection.CurrentDb()
                
                sql = f"""UPDATE 元作業時間 SET
                    従業員ｺｰﾄﾞ = '{data['employee_id']}',
                    日付 = #{data['entry_date']}#,
                    機種 = {f"'{data.get('model', '')}'" if data.get('model') else 'NULL'},
                    号機 = {f"'{data.get('number', '')}'" if data.get('number') else 'NULL'},
                    工場製番 = {f"'{data.get('factory_number', '')}'" if data.get('factory_number') else 'NULL'},
                    工事番号 = {f"'{data.get('project_number', '')}'" if data.get('project_number') else 'NULL'},
                    ﾕﾆｯﾄ番号 = {f"'{data.get('unit_number', '')}'" if data.get('unit_number') else 'NULL'},
                    区分 = '{data['category']}',
                    項目 = '{data['item']}',
                    時間 = {data['time_hours']},
                    所属ｺｰﾄﾞ = '{data['department_code']}'
                  WHERE ID = {external_id}"""
                
                db.Execute(sql)
                return True
                
            finally:
                pythoncom.CoUninitialize()
        
        loop = asyncio.get_event_loop()
        return await loop.run_in_executor(None, _sync_update)
    
    async def _mock_update(self, external_id: int, data: Dict) -> bool:
        """Ubuntu环境下的模拟更新"""
        try:
            sql = """
                UPDATE 元作業時間 SET
                    従業員ｺｰﾄﾞ = ?, 日付 = ?, 機種 = ?, 号機 = ?,
                    工場製番 = ?, 工事番号 = ?, ﾕﾆｯﾄ番号 = ?,
                    区分 = ?, 項目 = ?, 時間 = ?, 所属ｺｰﾄﾞ = ?
                WHERE ID = ?
            """
            
            cursor = self.connection.cursor()
            cursor.execute(sql, (
                data['employee_id'], data['entry_date'],
                data.get('model'), data.get('number'),
                data.get('factory_number'), data.get('project_number'),
                data.get('unit_number'), data['category'],
                data['item'], data['time_hours'],
                data['department_code'], external_id
            ))
            
            self.connection.commit()
            cursor.close()
            
            logger.debug(f"🔄 模拟更新成功，ID: {external_id}")
            return True
            
        except Exception as e:
            logger.error(f"❌ 模拟更新失败: {e}")
            return False
    
    async def delete_work_time(self, external_id: int) -> bool:
        """删除工时记录 - 25.06.26 增加多平台支持"""
        if not self.connection:
            raise RuntimeError("ODBC连接未建立")
        
        if self.is_windows and self.win32_available:
            return await self._windows_delete(external_id)
        else:
            return await self._mock_delete(external_id)
    
    async def _windows_delete(self, external_id: int) -> bool:
        """Windows环境下的真实删除 - 使用db_utils.py中验证过的方法"""
        def _sync_delete():
            import pythoncom
            pythoncom.CoInitialize()
            try:
                db = self.connection.CurrentDb()
                
                # 25.06.26 使用db_utils.py中验证过的DELETE语句
                sql = f"DELETE FROM 元作業時間 WHERE ID = {external_id}"
                db.Execute(sql)
                
                logger.debug(f"✅ Windows删除成功，ID: {external_id}")
                return True
                
            except Exception as e:
                logger.error(f"❌ Windows删除失败: {e}")
                return False
            finally:
                pythoncom.CoUninitialize()
        
        try:
            loop = asyncio.get_event_loop()
            result = await loop.run_in_executor(None, _sync_delete)
            return result
        except Exception as e:
            logger.error(f"❌ Windows异步删除失败: {e}")
            return False
    
    async def _mock_delete(self, external_id: int) -> bool:
        """Ubuntu环境下的模拟删除"""
        try:
            cursor = self.connection.cursor()
            cursor.execute("DELETE FROM 元作業時間 WHERE ID = ?", (external_id,))
            self.connection.commit()
            cursor.close()
            
            logger.debug(f"🔄 模拟删除成功，ID: {external_id}")
            return True
            
        except Exception as e:
            logger.error(f"❌ 模拟删除失败: {e}")
            return False
    
    async def get_user_data(self, employee_id: str, start_date: str, end_date: str) -> List[Dict]:
        """获取用户在指定时间范围内的数据"""
        if not self.connection:
            raise RuntimeError("ODBC连接未建立")
        
        if self.is_windows:
            return await self._windows_query(employee_id, start_date, end_date)
        else:
            return await self._mock_query(employee_id, start_date, end_date)
    
    async def _windows_query(self, employee_id: str, start_date: str, end_date: str) -> List[Dict]:
        """Windows环境下的真实查询"""
        def _sync_query():
            import pythoncom
            pythoncom.CoInitialize()
            try:
                db = self.connection.CurrentDb()
                sql = f"""SELECT * FROM 元作業時間 
                         WHERE 従業員ｺｰﾄﾞ = '{employee_id}' 
                           AND 日付 BETWEEN #{start_date}# AND #{end_date}#
                         ORDER BY 日付 DESC"""
                
                rs = db.OpenRecordset(sql)
                records = []
                
                while not rs.EOF:
                    record = {}
                    for i in range(rs.Fields.Count):
                        field = rs.Fields(i)
                        record[field.Name] = field.Value
                    records.append(record)
                    rs.MoveNext()
                
                rs.Close()
                return records
                
            finally:
                pythoncom.CoUninitialize()
        
        loop = asyncio.get_event_loop()
        return await loop.run_in_executor(None, _sync_query)
    
    async def _mock_query(self, employee_id: str, start_date: str, end_date: str) -> List[Dict]:
        """Ubuntu环境下的模拟查询"""
        try:
            sql = """SELECT * FROM 元作業時間 
                     WHERE 従業員ｺｰﾄﾞ = ? 
                       AND 日付 BETWEEN ? AND ?
                     ORDER BY 日付 DESC"""
            
            cursor = self.connection.cursor()
            cursor.execute(sql, (employee_id, start_date, end_date))
            
            columns = [description[0] for description in cursor.description]
            records = []
            
            for row in cursor.fetchall():
                record = dict(zip(columns, row))
                records.append(record)
            
            cursor.close()
            return records
            
        except Exception as e:
            logger.error(f"❌ 模拟查询失败: {e}")
            return []
    
    async def get_connection_status(self) -> Dict:
        """获取连接状态信息 - 25.06.26 增加平台信息"""
        return {
            "platform": self.platform_name,
            "is_windows": self.is_windows,
            "is_linux": self.is_linux,
            "is_darwin": self.is_darwin,
            "win32_available": getattr(self, 'win32_available', False),
            "connected": self.connection is not None,
            "mdb_path": self.mdb_path,
            "odbc_available": self.odbc_available,
            "connection_type": "real" if (self.is_windows and getattr(self, 'win32_available', False)) else "mock"
        }
    
    async def get_incremental_data(self, start_time: datetime, end_time: datetime, limit: int = 100) -> List[Dict]:
        """
        获取增量数据 - 25.06.26 增加多平台支持
        """
        if not self.connection:
            raise RuntimeError("ODBC连接未建立")
        
        if self.is_windows and self.win32_available:
            return await self._windows_get_incremental_data(start_time, end_time, limit)
        else:
            return await self._mock_get_incremental_data(start_time, end_time, limit)
    
    async def _windows_get_incremental_data(self, start_time: datetime, end_time: datetime, limit: int) -> List[Dict]:
        """Windows环境下的真实增量数据查询"""
        def _sync_query():
            import pythoncom
            pythoncom.CoInitialize()
            try:
                db = self.connection.CurrentDb()
                
                # 查询增量数据的SQL
                sql = f"""SELECT TOP {limit} * FROM 元作業時間 
                         WHERE 日付 BETWEEN #{start_time.strftime('%Y/%m/%d')}# AND #{end_time.strftime('%Y/%m/%d')}#
                         ORDER BY ID DESC"""
                
                rs = db.OpenRecordset(sql)
                results = []
                
                while not rs.EOF:
                    record = {}
                    for i in range(rs.Fields.Count):
                        field = rs.Fields(i)
                        record[field.Name] = field.Value
                    results.append(record)
                    rs.MoveNext()
                
                rs.Close()
                logger.debug(f"✅ Windows增量查询成功，返回{len(results)}条记录")
                return results
                
            except Exception as e:
                logger.error(f"❌ Windows增量查询失败: {e}")
                return []
            finally:
                pythoncom.CoUninitialize()
        
        try:
            loop = asyncio.get_event_loop()
            result = await loop.run_in_executor(None, _sync_query)
            return result
        except Exception as e:
            logger.error(f"❌ Windows异步增量查询失败: {e}")
            return []
    
    async def _mock_get_incremental_data(self, start_time: datetime, end_time: datetime, limit: int) -> List[Dict]:
        """模拟增量数据查询（用于非Windows平台）"""
        try:
            # 生成模拟的增量数据
            mock_data = []
            for i in range(min(3, limit)):  # 模拟最多3条记录
                mock_data.append({
                    'ID': 20000 + i,
                    'employee_id': f'EMP{i:03d}',
                    'date': start_time.strftime('%Y/%m/%d'),
                    'model': f'Incremental_Model_{i}',
                    'number': f'INC{i:03d}',
                    'factory_number': f'F{i:03d}',
                    'project_number': f'P{i:03d}',
                    'unit_number': f'U{i:03d}',
                    'category': 'INCREMENT',
                    'item': f'Incremental_Item_{i}',
                    'time': 7.5 + i * 0.5,
                    'department': f'Dept_{i}'
                })
            
            logger.debug(f"🔧 模拟增量查询成功，返回{len(mock_data)}条记录")
            return mock_data
            
        except Exception as e:
            logger.error(f"❌ 模拟增量查询失败: {e}")
            return [] 