# server5/app/database/redis_client.py
# Redis异步客户端 - 任务队列和缓存管理

import asyncio
import redis.asyncio as redis
import json
import logging
from typing import Optional, Dict, List, Any
import sys
from pathlib import Path
from datetime import datetime
sys.path.append(str(Path(__file__).parent.parent.parent))

from config.config import REDIS_URL

logger = logging.getLogger(__name__)

class RedisClient:
    """Redis异步客户端"""
    
    def __init__(self):
        self.redis_url = REDIS_URL
        self.client: Optional[redis.Redis] = None
        
    async def connect(self) -> bool:
        """建立Redis连接"""
        try:
            self.client = redis.from_url(
                self.redis_url,
                encoding="utf-8",
                decode_responses=True
            )
            
            # 测试连接
            await self.client.ping()
            logger.info("Redis连接成功")
            return True
            
        except Exception as e:
            logger.error(f"Redis连接失败: {e}")
            return False
    
    async def disconnect(self):
        """关闭Redis连接"""
        if self.client:
            await self.client.close()
            logger.info("🔌 Redis连接已关闭")
    
    # ===== 缓存操作 =====
    
    async def set_cache(self, key: str, value: Any, expire: int = 3600) -> bool:
        """设置缓存"""
        try:
            if isinstance(value, (dict, list)):
                value = json.dumps(value, ensure_ascii=False)
            
            await self.client.setex(key, expire, value)
            return True
            
        except Exception as e:
            logger.error(f"❌ 设置缓存失败 {key}: {e}")
            return False
    
    async def get_cache(self, key: str) -> Optional[Any]:
        """获取缓存"""
        try:
            value = await self.client.get(key)
            if value is None:
                return None
            
            # 尝试解析JSON
            try:
                return json.loads(value)
            except json.JSONDecodeError:
                return value
                
        except Exception as e:
            logger.error(f"❌ 获取缓存失败 {key}: {e}")
            return None
    
    async def delete_cache(self, key: str) -> bool:
        """删除缓存"""
        try:
            result = await self.client.delete(key)
            return result > 0
        except Exception as e:
            logger.error(f"❌ 删除缓存失败 {key}: {e}")
            return False
    
    # ===== 任务队列操作 =====
    
    async def push_task(self, queue_name: str, task_data: Dict) -> bool:
        """推送任务到队列"""
        try:
            task_json = json.dumps(task_data, ensure_ascii=False)
            await self.client.lpush(queue_name, task_json)
            logger.debug(f"📤 任务推送成功: {queue_name}")
            return True
            
        except Exception as e:
            logger.error(f"❌ 任务推送失败 {queue_name}: {e}")
            return False
    
    async def pop_task(self, queue_name: str, timeout: int = 10) -> Optional[Dict]:
        """从队列弹出任务（阻塞）"""
        try:
            result = await self.client.brpop(queue_name, timeout=timeout)
            if result:
                queue, task_json = result
                return json.loads(task_json)
            return None
            
        except Exception as e:
            logger.error(f"❌ 任务弹出失败 {queue_name}: {e}")
            return None
    
    async def get_queue_length(self, queue_name: str) -> int:
        """获取队列长度"""
        try:
            return await self.client.llen(queue_name)
        except Exception as e:
            logger.error(f"❌ 获取队列长度失败 {queue_name}: {e}")
            return 0
    
    async def clear_queue(self, queue_name: str) -> int:
        """清空指定队列，返回删除的元素数量"""
        try:
            count = await self.client.llen(queue_name)
            await self.client.delete(queue_name)
            logger.info(f"🧹 清空队列: {queue_name}，共{count}项")
            return count
        except Exception as e:
            logger.error(f"❌ 清空队列失败 {queue_name}: {e}")
            return 0
    
    # ===== 发布/订阅操作 =====
    
    async def publish(self, channel: str, message: Dict) -> bool:
        """发布消息"""
        try:
            message_json = json.dumps(message, ensure_ascii=False)
            await self.client.publish(channel, message_json)
            logger.debug(f"📡 消息发布成功: {channel}")
            return True
            
        except Exception as e:
            logger.error(f"❌ 消息发布失败 {channel}: {e}")
            return False
    
    async def subscribe(self, channel: str, callback):
        """订阅频道"""
        try:
            pubsub = self.client.pubsub()
            await pubsub.subscribe(channel)
            
            logger.info(f"📡 开始订阅频道: {channel}")
            
            async for message in pubsub.listen():
                if message['type'] == 'message':
                    try:
                        data = json.loads(message['data'])
                        await callback(channel, data)
                    except Exception as e:
                        logger.error(f"❌ 消息处理失败: {e}")
                        
        except Exception as e:
            logger.error(f"❌ 订阅失败 {channel}: {e}")
    
    # ===== 计数器操作 =====
    
    async def increment_counter(self, key: str, amount: int = 1) -> int:
        """递增计数器"""
        try:
            return await self.client.incrby(key, amount)
        except Exception as e:
            logger.error(f"❌ 计数器递增失败 {key}: {e}")
            return 0
    
    async def get_counter(self, key: str) -> int:
        """获取计数器值"""
        try:
            value = await self.client.get(key)
            return int(value) if value else 0
        except Exception as e:
            logger.error(f"❌ 获取计数器失败 {key}: {e}")
            return 0
    
    async def reset_counter(self, key: str) -> bool:
        """重置计数器"""
        try:
            await self.client.delete(key)
            return True
        except Exception as e:
            logger.error(f"❌ 重置计数器失败 {key}: {e}")
            return False
    
    # ===== 锁操作 =====
    
    async def acquire_lock(self, lock_key: str, timeout: int = 30) -> bool:
        """获取分布式锁"""
        try:
            result = await self.client.set(
                lock_key, 
                "locked", 
                nx=True,  # 只在键不存在时设置
                ex=timeout  # 过期时间
            )
            return result is not None
            
        except Exception as e:
            logger.error(f"❌ 获取锁失败 {lock_key}: {e}")
            return False
    
    async def release_lock(self, lock_key: str) -> bool:
        """释放分布式锁"""
        try:
            result = await self.client.delete(lock_key)
            return result > 0
        except Exception as e:
            logger.error(f"❌ 释放锁失败 {lock_key}: {e}")
            return False
    
    # ===== 健康检查 =====
    
    async def health_check(self) -> Dict:
        """健康检查"""
        try:
            # 测试基本连接
            start_time = asyncio.get_event_loop().time()
            await self.client.ping()
            ping_time = (asyncio.get_event_loop().time() - start_time) * 1000
            
            # 获取服务器信息
            info = await self.client.info()
            
            return {
                "status": "healthy",
                "ping_time_ms": round(ping_time, 2),
                "version": info.get("redis_version", "unknown"),
                "used_memory": info.get("used_memory_human", "unknown"),
                "connected_clients": info.get("connected_clients", 0),
                "uptime_in_seconds": info.get("uptime_in_seconds", 0)
            }
            
        except Exception as e:
            return {
                "status": "unhealthy",
                "error": str(e)
            }
    
    # ===== Server5特定操作 =====
    
    async def log_sync_event(self, event_type: str, data: Dict) -> bool:
        """记录同步事件到Redis"""
        try:
            event = {
                "timestamp": asyncio.get_event_loop().time(),
                "event_type": event_type,
                "data": data
            }
            
            # 使用列表存储最近的同步事件
            key = "server5:sync_events"
            await self.client.lpush(key, json.dumps(event, ensure_ascii=False))
            
            # 只保留最近1000条记录
            await self.client.ltrim(key, 0, 999)
            
            return True
            
        except Exception as e:
            logger.error(f"❌ 记录同步事件失败: {e}")
            return False
    
    async def get_sync_stats(self) -> Dict:
        """获取同步统计信息"""
        try:
            stats = {}
            
            # 获取各种计数器
            counters = [
                "server5:insert_count",
                "server5:update_count", 
                "server5:delete_count",
                "server5:sync_count",
                "server5:error_count"
            ]
            
            for counter in counters:
                key_name = counter.split(":")[-1]
                stats[key_name] = await self.get_counter(counter)
            
            # 获取最近的同步事件
            events = await self.client.lrange("server5:sync_events", 0, 9)
            stats["recent_events"] = [json.loads(event) for event in events]
            
            return stats
            
        except Exception as e:
            logger.error(f"❌ 获取同步统计失败: {e}")
            return {}
    
    async def update_worker_status(self, worker_id: str, status: str) -> bool:
        """更新工作进程状态"""
        try:
            key = f"server5:worker:{worker_id}"
            data = {
                "status": status,
                "timestamp": asyncio.get_event_loop().time(),
                "pid": asyncio.current_task().get_name() if asyncio.current_task() else "unknown"
            }
            
            await self.set_cache(key, data, expire=300)  # 5分钟过期
            return True
            
        except Exception as e:
            logger.error(f"❌ 更新工作进程状态失败: {e}")
            return False
    
    # ===== 数据拉取时间管理 =====
    # 250626，第二阶段: 修复f3_data_puller缺少的方法
    
    async def get_last_pull_time(self, service_name: str = "f3_data_puller") -> Optional[datetime]:
        """获取最后拉取时间，并将其转换为datetime对象"""
        try:
            key = f"server5:{service_name}:last_pull_time"
            timestamp_str = await self.client.get(key)
            if timestamp_str:
                return datetime.fromisoformat(timestamp_str)
            return None
        except Exception as e:
            logger.error(f"❌ 获取最后拉取时间失败 {service_name}: {e}")
            return None
    
    async def set_last_pull_time(self, timestamp: datetime, service_name: str = "f3_data_puller") -> bool:
        """设置最后拉取时间，将datetime对象转换为ISO格式字符串"""
        try:
            key = f"server5:{service_name}:last_pull_time"
            # 将datetime对象转换为ISO 8601字符串进行存储
            await self.client.set(key, timestamp.isoformat())
            return True
        except Exception as e:
            logger.error(f"❌ 设置最后拉取时间失败 {service_name}: {e}")
            return False 