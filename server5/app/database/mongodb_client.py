# server5/app/database/mongodb_client.py
# MongoDB异步客户端 - 日志存储和历史数据管理

import asyncio
import motor.motor_asyncio
import logging
from typing import Optional, Dict, List, Any
from datetime import datetime, timedelta
import sys
from pathlib import Path
sys.path.append(str(Path(__file__).parent.parent.parent))

from config.config import MONGO_URL, MONGO_DB

logger = logging.getLogger(__name__)

class MongoDBClient:
    """MongoDB异步客户端"""
    
    def __init__(self):
        self.mongo_url = MONGO_URL
        self.db_name = MONGO_DB
        self.client: Optional[motor.motor_asyncio.AsyncIOMotorClient] = None
        self.db: Optional[motor.motor_asyncio.AsyncIOMotorDatabase] = None
        
    async def connect(self) -> bool:
        """建立MongoDB连接"""
        try:
            self.client = motor.motor_asyncio.AsyncIOMotorClient(
                self.mongo_url,
                serverSelectionTimeoutMS=5000
            )
            
            # 测试连接
            await self.client.admin.command('ismaster')
            
            self.db = self.client[self.db_name]
            
            # 创建必要的索引
            await self._create_indexes()
            
            logger.info("✅ MongoDB连接成功")
            return True
            
        except Exception as e:
            logger.error(f"❌ MongoDB连接失败: {e}")
            return False
    
    async def disconnect(self):
        """关闭MongoDB连接"""
        if self.client:
            self.client.close()
            logger.info("🔌 MongoDB连接已关闭")
    
    async def _create_indexes(self):
        """创建必要的索引"""
        try:
            # 同步日志索引
            await self.db.sync_logs.create_index([
                ("timestamp", -1),
                ("operation_type", 1),
                ("employee_id", 1)
            ])
            
            # 错误日志索引
            await self.db.error_logs.create_index([
                ("timestamp", -1),
                ("error_type", 1)
            ])
            
            # 性能监控索引
            await self.db.performance_metrics.create_index([
                ("timestamp", -1),
                ("metric_type", 1)
            ])
            
            logger.debug("📋 MongoDB索引创建完成")
            
        except Exception as e:
            logger.error(f"❌ 创建索引失败: {e}")
    
    # ===== 同步日志操作 =====
    
    async def log_sync_operation(self, operation_data: Dict) -> bool:
        """记录同步操作日志"""
        try:
            log_doc = {
                "timestamp": datetime.utcnow(),
                "operation_type": operation_data.get("operation_type"),
                "employee_id": operation_data.get("employee_id"),
                "entry_id": operation_data.get("entry_id"),
                "external_id": operation_data.get("external_id"),
                "queue_id": operation_data.get("queue_id"),
                "status": operation_data.get("status", "success"),
                "error_message": operation_data.get("error_message"),
                "execution_time_ms": operation_data.get("execution_time_ms"),
                "data_before": operation_data.get("data_before"),
                "data_after": operation_data.get("data_after"),
                "metadata": operation_data.get("metadata", {})
            }
            
            await self.db.sync_logs.insert_one(log_doc)
            logger.debug(f"📝 同步日志记录成功: {operation_data.get('operation_type')}")
            return True
            
        except Exception as e:
            logger.error(f"❌ 同步日志记录失败: {e}")
            return False
    
    async def get_sync_logs(self, 
                           start_date: datetime = None,
                           end_date: datetime = None,
                           employee_id: str = None,
                           operation_type: str = None,
                           limit: int = 100) -> List[Dict]:
        """获取同步日志"""
        try:
            filter_dict = {}
            
            # 时间范围过滤
            if start_date or end_date:
                filter_dict["timestamp"] = {}
                if start_date:
                    filter_dict["timestamp"]["$gte"] = start_date
                if end_date:
                    filter_dict["timestamp"]["$lte"] = end_date
            
            # 员工ID过滤
            if employee_id:
                filter_dict["employee_id"] = employee_id
            
            # 操作类型过滤
            if operation_type:
                filter_dict["operation_type"] = operation_type
            
            cursor = self.db.sync_logs.find(filter_dict).sort("timestamp", -1).limit(limit)
            logs = await cursor.to_list(length=limit)
            
            return logs
            
        except Exception as e:
            logger.error(f"❌ 获取同步日志失败: {e}")
            return []
    
    # ===== 错误日志操作 =====
    
    async def log_error(self, error_data: Dict) -> bool:
        """记录错误日志"""
        try:
            error_doc = {
                "timestamp": datetime.utcnow(),
                "error_type": error_data.get("error_type"),
                "error_message": error_data.get("error_message"),
                "stack_trace": error_data.get("stack_trace"),
                "context": error_data.get("context", {}),
                "severity": error_data.get("severity", "error"),
                "function_name": error_data.get("function_name"),
                "employee_id": error_data.get("employee_id"),
                "entry_id": error_data.get("entry_id"),
                "queue_id": error_data.get("queue_id")
            }
            
            await self.db.error_logs.insert_one(error_doc)
            logger.debug(f"🚨 错误日志记录成功: {error_data.get('error_type')}")
            return True
            
        except Exception as e:
            logger.error(f"❌ 错误日志记录失败: {e}")
            return False
    
    async def get_error_stats(self, hours: int = 24) -> Dict:
        """获取错误统计信息"""
        try:
            start_time = datetime.utcnow() - timedelta(hours=hours)
            
            pipeline = [
                {"$match": {"timestamp": {"$gte": start_time}}},
                {"$group": {
                    "_id": "$error_type",
                    "count": {"$sum": 1},
                    "last_occurrence": {"$max": "$timestamp"}
                }},
                {"$sort": {"count": -1}}
            ]
            
            cursor = self.db.error_logs.aggregate(pipeline)
            error_stats = await cursor.to_list(length=None)
            
            # 计算总错误数
            total_errors = sum(stat["count"] for stat in error_stats)
            
            return {
                "total_errors": total_errors,
                "error_types": error_stats,
                "time_range_hours": hours
            }
            
        except Exception as e:
            logger.error(f"❌ 获取错误统计失败: {e}")
            return {}
    
    # ===== 性能监控操作 =====
    
    async def log_performance_metric(self, metric_data: Dict) -> bool:
        """记录性能指标"""
        try:
            metric_doc = {
                "timestamp": datetime.utcnow(),
                "metric_type": metric_data.get("metric_type"),
                "metric_name": metric_data.get("metric_name"),
                "value": metric_data.get("value"),
                "unit": metric_data.get("unit"),
                "tags": metric_data.get("tags", {}),
                "metadata": metric_data.get("metadata", {})
            }
            
            await self.db.performance_metrics.insert_one(metric_doc)
            return True
            
        except Exception as e:
            logger.error(f"❌ 性能指标记录失败: {e}")
            return False
    
    async def get_performance_stats(self, metric_type: str, hours: int = 24) -> Dict:
        """获取性能统计"""
        try:
            start_time = datetime.utcnow() - timedelta(hours=hours)
            
            pipeline = [
                {"$match": {
                    "metric_type": metric_type,
                    "timestamp": {"$gte": start_time}
                }},
                {"$group": {
                    "_id": None,
                    "avg_value": {"$avg": "$value"},
                    "min_value": {"$min": "$value"},
                    "max_value": {"$max": "$value"},
                    "count": {"$sum": 1}
                }}
            ]
            
            cursor = self.db.performance_metrics.aggregate(pipeline)
            result = await cursor.to_list(length=1)
            
            if result:
                return result[0]
            else:
                return {"avg_value": 0, "min_value": 0, "max_value": 0, "count": 0}
                
        except Exception as e:
            logger.error(f"❌ 获取性能统计失败: {e}")
            return {}
    
    # ===== 数据备份操作 =====
    
    async def backup_sync_data(self, employee_id: str, start_date: datetime, end_date: datetime) -> bool:
        """备份同步数据"""
        try:
            backup_doc = {
                "timestamp": datetime.utcnow(),
                "employee_id": employee_id,
                "start_date": start_date,
                "end_date": end_date,
                "backup_type": "sync_data",
                "status": "completed"
            }
            
            # 获取相关的同步日志
            sync_logs = await self.get_sync_logs(
                start_date=start_date,
                end_date=end_date,
                employee_id=employee_id
            )
            
            backup_doc["data_count"] = len(sync_logs)
            backup_doc["sync_logs"] = sync_logs
            
            await self.db.data_backups.insert_one(backup_doc)
            logger.info(f"💾 数据备份完成: {employee_id} ({len(sync_logs)}条记录)")
            return True
            
        except Exception as e:
            logger.error(f"❌ 数据备份失败: {e}")
            return False
    
    # ===== 清理操作 =====
    
    async def cleanup_old_logs(self, days: int = 30) -> Dict:
        """清理旧日志"""
        try:
            cutoff_date = datetime.utcnow() - timedelta(days=days)
            
            results = {}
            
            # 清理同步日志
            sync_result = await self.db.sync_logs.delete_many(
                {"timestamp": {"$lt": cutoff_date}}
            )
            results["sync_logs_deleted"] = sync_result.deleted_count
            
            # 清理错误日志
            error_result = await self.db.error_logs.delete_many(
                {"timestamp": {"$lt": cutoff_date}}
            )
            results["error_logs_deleted"] = error_result.deleted_count
            
            # 清理性能指标
            metric_result = await self.db.performance_metrics.delete_many(
                {"timestamp": {"$lt": cutoff_date}}
            )
            results["metrics_deleted"] = metric_result.deleted_count
            
            logger.info(f"🧹 旧日志清理完成: {results}")
            return results
            
        except Exception as e:
            logger.error(f"❌ 旧日志清理失败: {e}")
            return {}
    
    # ===== 健康检查 =====
    
    async def health_check(self) -> Dict:
        """健康检查"""
        try:
            # 测试连接
            start_time = asyncio.get_event_loop().time()
            await self.client.admin.command('ismaster')
            ping_time = (asyncio.get_event_loop().time() - start_time) * 1000
            
            # 获取数据库统计信息
            stats = await self.db.command("dbStats")
            
            # 获取集合统计
            collections = await self.db.list_collection_names()
            
            return {
                "status": "healthy",
                "ping_time_ms": round(ping_time, 2),
                "db_name": self.db_name,
                "collections": len(collections),
                "data_size_mb": round(stats.get("dataSize", 0) / 1024 / 1024, 2),
                "storage_size_mb": round(stats.get("storageSize", 0) / 1024 / 1024, 2),
                "index_size_mb": round(stats.get("indexSize", 0) / 1024 / 1024, 2),
                "documents": stats.get("objects", 0)
            }
            
        except Exception as e:
            return {
                "status": "unhealthy",
                "error": str(e)
            }
    
    # ===== Server5特定操作 =====
    
    async def get_dashboard_data(self) -> Dict:
        """获取仪表板数据"""
        try:
            # 获取最近24小时的统计数据
            end_time = datetime.utcnow()
            start_time = end_time - timedelta(hours=24)
            
            # 同步操作统计
            sync_pipeline = [
                {"$match": {"timestamp": {"$gte": start_time}}},
                {"$group": {
                    "_id": "$operation_type",
                    "count": {"$sum": 1},
                    "success_count": {
                        "$sum": {"$cond": [{"$eq": ["$status", "success"]}, 1, 0]}
                    },
                    "error_count": {
                        "$sum": {"$cond": [{"$ne": ["$status", "success"]}, 1, 0]}
                    }
                }}
            ]
            
            cursor = self.db.sync_logs.aggregate(sync_pipeline)
            sync_stats = await cursor.to_list(length=None)
            
            # 错误统计
            error_stats = await self.get_error_stats(hours=24)
            
            # 性能统计
            perf_stats = {}
            for metric_type in ["sync_time", "queue_processing", "db_query"]:
                perf_stats[metric_type] = await self.get_performance_stats(metric_type, hours=24)
            
            return {
                "sync_operations": sync_stats,
                "error_statistics": error_stats,
                "performance_metrics": perf_stats,
                "last_updated": datetime.utcnow()
            }
            
        except Exception as e:
            logger.error(f"❌ 获取仪表板数据失败: {e}")
            return {}
    
    async def log_f6_sync_result(self, employee_id: str, sync_result: Dict) -> bool:
        """记录f6专属ID同步结果"""
        try:
            f6_log = {
                "timestamp": datetime.utcnow(),
                "function": "f6_user_sync",
                "employee_id": employee_id,
                "records_compared": sync_result.get("records_compared", 0),
                "records_updated": sync_result.get("records_updated", 0),
                "execution_time_ms": sync_result.get("execution_time_ms", 0),
                "status": sync_result.get("status", "success"),
                "error_message": sync_result.get("error_message"),
                "changes": sync_result.get("changes", [])
            }
            
            await self.db.f6_sync_logs.insert_one(f6_log)
            logger.debug(f"📊 f6同步结果记录成功: {employee_id}")
            return True
            
        except Exception as e:
            logger.error(f"❌ f6同步结果记录失败: {e}")
            return False 