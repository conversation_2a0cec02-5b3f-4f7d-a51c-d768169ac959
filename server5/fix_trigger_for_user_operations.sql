-- №02025/06/27 + 修复触发器，只在用户操作时才触发队列
-- 解决随机数据插入问题

-- 1. 首先添加source字段到entries表，用于标识数据来源
ALTER TABLE entries ADD COLUMN IF NOT EXISTS source VARCHAR(20) DEFAULT 'system';

-- 2. 为现有数据设置source值
UPDATE entries SET source = 'system' WHERE source IS NULL;

-- 3. 修改触发器函数，只在用户操作时才触发队列
CREATE OR REPLACE FUNCTION trg_entries_enqueue() RETURNS TRIGGER AS $$
DECLARE q BIGINT;
BEGIN
    -- 只在source为'user'时才触发队列
    IF (TG_OP='INSERT' AND NEW.source = 'user') THEN
        INSERT INTO entries_push_queue(entry_id, operation)
        VALUES (NEW.id,'INSERT') RETURNING queue_id INTO q;
        PERFORM pg_notify('push_job', q::text);
    ELSIF (TG_OP='UPDATE' AND NEW.source = 'user') THEN
        INSERT INTO entries_push_queue(entry_id, external_id, operation)
        VALUES (NEW.id, NEW.external_id,'UPDATE') RETURNING queue_id INTO q;
        PERFORM pg_notify('push_job', q::text);
    ELSIF (TG_OP='DELETE' AND OLD.source = 'user') THEN
        INSERT INTO entries_push_queue(entry_id, external_id, operation)
        VALUES (OLD.id, OLD.external_id,'DELETE') RETURNING queue_id INTO q;
        PERFORM pg_notify('push_job', q::text);
    END IF;
    
    RETURN NULL;
END;
$$ LANGUAGE plpgsql;

-- 4. 重新创建触发器
DROP TRIGGER IF EXISTS trg_entries_enqueue ON public.entries;
CREATE TRIGGER trg_entries_enqueue
AFTER INSERT OR UPDATE OR DELETE ON public.entries
FOR EACH ROW EXECUTE FUNCTION trg_entries_enqueue();

-- 5. 验证修改结果
SELECT 
    'Trigger function updated' as status,
    'Only user operations (source=user) will trigger queue' as description;

-- 6. 检查当前entries表的source字段分布
SELECT 
    source,
    COUNT(*) as count
FROM entries 
GROUP BY source
ORDER BY source; 