#!/usr/bin/env python3
# -*- coding: utf-8 -*-
# 2025/06/27 + f3服务数据拉取测试脚本

"""
test_f3_60days_pull.py
- 测试f3数据拉取服务的数据拉取功能
- 支持测试不同天数的数据拉取 (30天、60天等)
- 用法: python test_f3_60days_pull.py
"""

import asyncio
import sys
from pathlib import Path
from datetime import datetime, date, timedelta
import logging
from decimal import Decimal

# 添加项目根目录到Python路径
sys.path.append(str(Path(__file__).parent.parent))

from app.services.f3_data_puller import DataPullerService
from config.config import USER_SYNC_DAYS

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    stream=sys.stdout
)
logger = logging.getLogger(__name__)

1
async def test_data_pull(days: int):
    """
    测试指定天数的数据拉取功能
    
    Args:
        days: 要拉取的天数
    """
    logger.info("=" * 80)
    logger.info(f"🚀 开始测试f3服务{days}天数据拉取功能")
    logger.info("=" * 80)

    # 初始化服务
    puller_service = DataPullerService()
     
    try:
        # 1. 建立数据库连接
        logger.info("--- 1. 建立数据库连接 ---")
        await puller_service.imdb_client.connect()
        await puller_service.redis_client.connect()
        logger.info("✅ 数据库连接成功。")

        # 2. 计算日期范围
        end_date = date.today()
        start_date = end_date - timedelta(days=days)
        
        logger.info(f"--- 2. 计算拉取范围 ---")
        logger.info(f"  - 开始日期: {start_date.strftime('%Y/%m/%d')}")
        logger.info(f"  - 结束日期: {end_date.strftime('%Y/%m/%d')}")
        logger.info(f"  - 总天数: {(end_date - start_date).days + 1} 天")

        # 3. 执行数据拉取
        logger.info("--- 3. 执行数据拉取 ---")
        logger.info("🔄 调用 pull_recent_data() 方法...")
        
        # 记录开始时间
        start_time = datetime.now()
        
        # 执行拉取
        await puller_service.pull_recent_data()
        
        # 记录结束时间
        end_time = datetime.now()
        duration = (end_time - start_time).total_seconds()
        
        logger.info(f"✅ {days}天数据拉取完成，耗时: {duration:.2f} 秒")

        # 4. 获取拉取统计信息
        logger.info("--- 4. 获取拉取统计信息 ---")
        
        # 获取最后拉取时间
        last_pull_time = await puller_service.redis_client.get_last_pull_time()
        if last_pull_time:
            logger.info(f"  - 最后拉取时间: {last_pull_time}")
        
        # 获取活跃用户数量
        active_employees = await puller_service.imdb_client.get_active_employee_ids(days=days)
        logger.info(f"  - 活跃用户数量: {len(active_employees)}")
        if active_employees:
            logger.info(f"  - 活跃用户ID: {active_employees[:5]}{'...' if len(active_employees) > 5 else ''}")

        # 5. 验证数据同步结果
        logger.info("--- 5. 验证数据同步结果 ---")
        
        # 查询最近days天的数据统计
        from app.database.postgresql_client import PostgreSQLClient
        pg_client = PostgreSQLClient("************************************************/mysuite")
        await pg_client.connect()
        
        try:
            # 统计最近days天的记录数
            count_query = """
                SELECT COUNT(*) as total_records,
                       COUNT(DISTINCT employee_id) as unique_employees,
                       MIN(entry_date) as earliest_date,
                       MAX(entry_date) as latest_date
                FROM entries 
                WHERE entry_date >= $1 AND entry_date <= $2
            """
            
            stats = await pg_client.fetch_one(count_query, start_date, end_date)
            
            if stats:
                logger.info(f"  - 总记录数: {stats['total_records']}")
                logger.info(f"  - 唯一员工数: {stats['unique_employees']}")
                logger.info(f"  - 最早日期: {stats['earliest_date']}")
                logger.info(f"  - 最晚日期: {stats['latest_date']}")
            else:
                logger.warning("  - 未找到统计数据")
                
        finally:
            await pg_client.disconnect()

        logger.info("=" * 80)
        logger.info(f"🎉 f3服务{days}天数据拉取测试完成！")
        logger.info("=" * 80)

    except Exception as e:
        logger.error(f"❌ 测试过程中发生严重错误: {e}", exc_info=True)
    finally:
        # 6. 关闭连接
        logger.info("--- 6. 关闭数据库连接 ---")
        await puller_service.imdb_client.disconnect()
        await puller_service.redis_client.disconnect()
        await puller_service.server6_client.disconnect()
        logger.info("✅ 连接已关闭。测试结束。")


async def test_data_pull_all_employees(days: int):
    """
    测试指定天数的数据拉取功能 (全员模式)
    
    Args:
        days: 要拉取的天数
    """
    logger.info("=" * 80)
    logger.info(f"🚀 开始测试f3服务{days}天数据拉取功能 (全员模式)")
    logger.info("=" * 80)

    # 初始化服务
    puller_service = DataPullerService()
    
    try:
        # 1. 建立数据库连接
        logger.info("--- 1. 建立数据库连接 ---")
        await puller_service.imdb_client.connect()
        await puller_service.redis_client.connect()
        logger.info("✅ 数据库连接成功。")

        # 2. 计算日期范围
        end_date = date.today()
        start_date = end_date - timedelta(days=days)
        
        logger.info(f"--- 2. 计算拉取范围 ---")
        logger.info(f"  - 开始日期: {start_date.strftime('%Y/%m/%d')}")
        logger.info(f"  - 结束日期: {end_date.strftime('%Y/%m/%d')}")
        logger.info(f"  - 总天数: {(end_date - start_date).days + 1} 天")

        # 3. 执行数据拉取 (全员模式)
        logger.info("--- 3. 执行数据拉取 (全员模式) ---")
        logger.info("🔄 调用 _pull_and_sync() 方法 (全员模式)...")
        
        # 记录开始时间
        start_time = datetime.now()
        
        # 执行拉取 (全员模式)
        affected_rows = await puller_service._pull_and_sync(start_date, end_date)
        
        # 记录结束时间
        end_time = datetime.now()
        duration = (end_time - start_time).total_seconds()
        
        logger.info(f"✅ {days}天数据拉取完成 (全员模式)，耗时: {duration:.2f} 秒")
        logger.info(f"  - 影响行数: {affected_rows}")

        # 4. 获取拉取统计信息
        logger.info("--- 4. 获取拉取统计信息 ---")
        
        # 获取最后拉取时间
        last_pull_time = await puller_service.redis_client.get_last_pull_time()
        if last_pull_time:
            logger.info(f"  - 最后拉取时间: {last_pull_time}")

        # 5. 验证数据同步结果
        logger.info("--- 5. 验证数据同步结果 ---")
        
        # 查询最近days天的数据统计
        from app.database.postgresql_client import PostgreSQLClient
        pg_client = PostgreSQLClient("************************************************/mysuite")
        await pg_client.connect()
        
        try:
            # 统计最近days天的记录数
            count_query = """
                SELECT COUNT(*) as total_records,
                       COUNT(DISTINCT employee_id) as unique_employees,
                       MIN(entry_date) as earliest_date,
                       MAX(entry_date) as latest_date
                FROM entries 
                WHERE entry_date >= $1 AND entry_date <= $2
            """
            
            stats = await pg_client.fetch_one(count_query, start_date, end_date)
            
            if stats:
                logger.info(f"  - 总记录数: {stats['total_records']}")
                logger.info(f"  - 唯一员工数: {stats['unique_employees']}")
                logger.info(f"  - 最早日期: {stats['earliest_date']}")
                logger.info(f"  - 最晚日期: {stats['latest_date']}")
            else:
                logger.warning("  - 未找到统计数据")
                
        finally:
            await pg_client.disconnect()

        logger.info("=" * 80)
        logger.info(f"🎉 f3服务{days}天数据拉取测试完成 (全员模式)！")
        logger.info("=" * 80)

    except Exception as e:
        logger.error(f"❌ 测试过程中发生严重错误: {e}", exc_info=True)
    finally:
        # 6. 关闭连接
        logger.info("--- 6. 关闭数据库连接 ---")
        await puller_service.imdb_client.disconnect()
        await puller_service.redis_client.disconnect()
        await puller_service.server6_client.disconnect()
        logger.info("✅ 连接已关闭。测试结束。")


async def test_manual_data_pull(days: int):
    """
    测试手动数据拉取 (使用 _pull_and_sync 方法)
    
    Args:
        days: 要拉取的天数
    """
    logger.info("=" * 80)
    logger.info(f"🚀 开始测试手动{days}天数据拉取 (使用 _pull_and_sync)")
    logger.info("=" * 80)

    # 初始化服务
    puller_service = DataPullerService()
    
    try:
        # 1. 建立数据库连接
        logger.info("--- 1. 建立数据库连接 ---")
        await puller_service.imdb_client.connect()
        await puller_service.redis_client.connect()
        logger.info("✅ 数据库连接成功。")

        # 2. 计算日期范围
        end_date = date.today()
        start_date = end_date - timedelta(days=days)
        
        logger.info(f"--- 2. 计算拉取范围 ---")
        logger.info(f"  - 开始日期: {start_date.strftime('%Y/%m/%d')}")
        logger.info(f"  - 结束日期: {end_date.strftime('%Y/%m/%d')}")
        logger.info(f"  - 总天数: {(end_date - start_date).days + 1} 天")

        # 3. 执行手动数据拉取
        logger.info("--- 3. 执行手动数据拉取 ---")
        logger.info("🔄 调用 _pull_and_sync() 方法...")
        
        # 记录开始时间
        start_time = datetime.now()
        
        # 执行拉取
        affected_rows = await puller_service._pull_and_sync(start_date, end_date)
        
        # 记录结束时间
        end_time = datetime.now()
        duration = (end_time - start_time).total_seconds()
        
        logger.info(f"✅ 手动{days}天数据拉取完成")
        logger.info(f"  - 影响行数: {affected_rows}")
        logger.info(f"  - 耗时: {duration:.2f} 秒")

        logger.info("=" * 80)
        logger.info(f"🎉 手动{days}天数据拉取测试完成！")
        logger.info("=" * 80)

    except Exception as e:
        logger.error(f"❌ 测试过程中发生严重错误: {e}", exc_info=True)
    finally:
        # 4. 关闭连接
        logger.info("--- 4. 关闭数据库连接 ---")
        await puller_service.imdb_client.disconnect()
        await puller_service.redis_client.disconnect()
        await puller_service.server6_client.disconnect()
        logger.info("✅ 连接已关闭。测试结束。")


async def test_manual_data_pull_all_employees(days: int):
    """
    测试手动数据拉取 (使用 _pull_and_sync 方法，全员模式)
    
    Args:
        days: 要拉取的天数
    """
    logger.info("=" * 80)
    logger.info(f"🚀 开始测试手动{days}天数据拉取 (使用 _pull_and_sync，全员模式)")
    logger.info("=" * 80)

    # 初始化服务
    puller_service = DataPullerService()
    
    try:
        # 1. 建立数据库连接
        logger.info("--- 1. 建立数据库连接 ---")
        await puller_service.imdb_client.connect()
        await puller_service.redis_client.connect()
        logger.info("✅ 数据库连接成功。")

        # 2. 计算日期范围
        end_date = date.today()
        start_date = end_date - timedelta(days=days)
        
        logger.info(f"--- 2. 计算拉取范围 ---")
        logger.info(f"  - 开始日期: {start_date.strftime('%Y/%m/%d')}")
        logger.info(f"  - 结束日期: {end_date.strftime('%Y/%m/%d')}")
        logger.info(f"  - 总天数: {(end_date - start_date).days + 1} 天")

        # 3. 执行手动数据拉取 (全员模式)
        logger.info("--- 3. 执行手动数据拉取 (全员模式) ---")
        logger.info("🔄 调用 _pull_and_sync() 方法 (全员模式)...")
        
        # 记录开始时间
        start_time = datetime.now()
        
        # 执行拉取 (全员模式)
        affected_rows = await puller_service._pull_and_sync(start_date, end_date)
        
        # 记录结束时间
        end_time = datetime.now()
        duration = (end_time - start_time).total_seconds()
        
        logger.info(f"✅ 手动{days}天数据拉取完成 (全员模式)")
        logger.info(f"  - 影响行数: {affected_rows}")
        logger.info(f"  - 耗时: {duration:.2f} 秒")

        logger.info("=" * 80)
        logger.info(f"🎉 手动{days}天数据拉取测试完成 (全员模式)！")
        logger.info("=" * 80)

    except Exception as e:
        logger.error(f"❌ 测试过程中发生严重错误: {e}", exc_info=True)
    finally:
        # 4. 关闭连接
        logger.info("--- 4. 关闭数据库连接 ---")
        await puller_service.imdb_client.disconnect()
        await puller_service.redis_client.disconnect()
        await puller_service.server6_client.disconnect()
        logger.info("✅ 连接已关闭。测试结束。")


async def test_bulk_fast_pull(days: int):
    """
    全员极速拉取（bulk_fast接口），按日期范围一次性拉取所有人的数据
    Args:
        days: 要拉取的天数
    """
    logger.info("=" * 80)
    logger.info(f"🚀 开始测试f3服务{days}天全员极速拉取 (bulk_fast)")
    logger.info("=" * 80)

    puller_service = DataPullerService()
    try:
        # 1. 建立数据库连接
        logger.info("--- 1. 建立数据库连接 ---")
        await puller_service.imdb_client.connect()
        await puller_service.redis_client.connect()
        logger.info("✅ 数据库连接成功。")

        # 2. 计算日期范围
        end_date = date.today()
        start_date = end_date - timedelta(days=days)
        logger.info(f"--- 2. 计算拉取范围 ---")
        logger.info(f"  - 开始日期: {start_date.strftime('%Y/%m/%d')}")
        logger.info(f"  - 结束日期: {end_date.strftime('%Y/%m/%d')}")
        logger.info(f"  - 总天数: {(end_date - start_date).days + 1} 天")

        # 3. 调用bulk_fast接口拉取
        logger.info("--- 3. 调用Server6 bulk_fast接口拉取全员数据 ---")
        start_time = datetime.now()
        all_records = await puller_service.server6_client.query_bulk_fast(start_date, end_date)
        end_time = datetime.now()
        duration = (end_time - start_time).total_seconds()
        logger.info(f"✅ bulk_fast接口拉取完成，耗时: {duration:.2f} 秒")
        logger.info(f"  - 拉取到记录数: {len(all_records)}")

        # 4. 同步到数据库
        logger.info("--- 4. 同步到数据库 ---")
        sync_start = datetime.now()
        affected_rows = await puller_service._sync_to_db(all_records)
        sync_end = datetime.now()
        sync_duration = (sync_end - sync_start).total_seconds()
        logger.info(f"✅ 数据同步完成，耗时: {sync_duration:.2f} 秒")
        logger.info(f"  - 同步到PostgreSQL的记录数: {affected_rows}")

        # 5. 统计信息
        logger.info("--- 5. 数据库统计信息 ---")
        try:
            # 使用现有的PostgreSQL客户端，而不是创建新的
            if hasattr(puller_service, 'imdb_client') and puller_service.imdb_client:
                count_query = """
                    SELECT COUNT(*) as total_records,
                           COUNT(DISTINCT employee_id) as unique_employees,
                           MIN(entry_date) as earliest_date,
                           MAX(entry_date) as latest_date
                    FROM entries 
                    WHERE entry_date >= $1 AND entry_date <= $2
                """
                stats = await puller_service.imdb_client.fetch_one(count_query, start_date, end_date)
                if stats:
                    logger.info(f"  - 总记录数: {stats['total_records']}")
                    logger.info(f"  - 唯一员工数: {stats['unique_employees']}")
                    logger.info(f"  - 最早日期: {stats['earliest_date']}")
                    logger.info(f"  - 最晚日期: {stats['latest_date']}")
                else:
                    logger.warning("  - 未找到统计数据")
            else:
                logger.warning("  - PostgreSQL客户端不可用，跳过统计")
        except Exception as stats_error:
            logger.error(f"  - 获取统计信息失败: {stats_error}")

        logger.info("=" * 80)
        logger.info(f"�� f3服务{days}天全员极速拉取（bulk_fast）测试完成！")
        logger.info("=" * 80)

    except Exception as e:
        logger.error(f"❌ 测试过程中发生严重错误: {e}", exc_info=True)
    finally:
        logger.info("--- 6. 关闭数据库连接 ---")
        await puller_service.imdb_client.disconnect()
        await puller_service.redis_client.disconnect()
        await puller_service.server6_client.disconnect()
        logger.info("✅ 连接已关闭。测试结束。")


async def test_bulk_fast_pull_batch(days: int, batch_size: int = 7):
    """
    全员极速拉取（bulk_fast接口）- 分批次处理，避免超时
    Args:
        days: 要拉取的天数
        batch_size: 每批次的天数（默认7天）
    """
    logger.info("=" * 80)
    logger.info(f"🚀 开始测试f3服务{days}天全员极速拉取 (bulk_fast - 分批次)")
    logger.info("=" * 80)

    puller_service = DataPullerService()
    try:
        # 1. 建立数据库连接
        logger.info("--- 1. 建立数据库连接 ---")
        await puller_service.imdb_client.connect()
        await puller_service.redis_client.connect()
        logger.info("✅ 数据库连接成功。")

        # 2. 计算日期范围
        end_date = date.today()
        start_date = end_date - timedelta(days=days)
        logger.info(f"--- 2. 计算拉取范围 ---")
        logger.info(f"  - 开始日期: {start_date.strftime('%Y/%m/%d')}")
        logger.info(f"  - 结束日期: {end_date.strftime('%Y/%m/%d')}")
        logger.info(f"  - 总天数: {(end_date - start_date).days + 1} 天")
        logger.info(f"  - 批次大小: {batch_size} 天")

        # 3. 分批次拉取
        logger.info("--- 3. 分批次拉取数据 ---")
        total_records = 0
        total_affected_rows = 0
        batch_count = 0
        
        current_start = start_date
        overall_start_time = datetime.now()
        
        while current_start <= end_date:
            batch_count += 1
            current_end = min(current_start + timedelta(days=batch_size - 1), end_date)
            
            logger.info(f"  📦 批次 {batch_count}: {current_start.strftime('%Y/%m/%d')} 到 {current_end.strftime('%Y/%m/%d')}")
            
            try:
                # 拉取当前批次
                batch_start_time = datetime.now()
                batch_records = await puller_service.server6_client.query_bulk_fast(current_start, current_end)
                batch_end_time = datetime.now()
                batch_duration = (batch_end_time - batch_start_time).total_seconds()
                
                logger.info(f"    ✅ 批次 {batch_count} 拉取完成，耗时: {batch_duration:.2f} 秒")
                logger.info(f"    📊 拉取到记录数: {len(batch_records)}")
                
                if batch_records:
                    # 同步当前批次到数据库
                    sync_start = datetime.now()
                    affected_rows = await puller_service._sync_to_db(batch_records)
                    sync_end = datetime.now()
                    sync_duration = (sync_end - sync_start).total_seconds()
                    
                    total_records += len(batch_records)
                    total_affected_rows += affected_rows
                    
                    logger.info(f"    💾 批次 {batch_count} 同步完成，耗时: {sync_duration:.2f} 秒")
                    logger.info(f"    📈 同步到PostgreSQL的记录数: {affected_rows}")
                else:
                    logger.info(f"    ⚠️ 批次 {batch_count} 无数据")
                    
            except Exception as e:
                logger.error(f"    ❌ 批次 {batch_count} 处理失败: {e}")
                # 继续处理下一批次，不中断整个流程
            
            # 移动到下一批次
            current_start = current_end + timedelta(days=1)
            
            # 批次间短暂休息，避免对Server6造成压力
            if current_start <= end_date:
                await asyncio.sleep(1)
        
        overall_end_time = datetime.now()
        overall_duration = (overall_end_time - overall_start_time).total_seconds()
        
        logger.info(f"✅ 分批次拉取完成，总耗时: {overall_duration:.2f} 秒")
        logger.info(f"📊 总计拉取记录数: {total_records}")
        logger.info(f"📊 总计同步记录数: {total_affected_rows}")
        logger.info(f"📊 处理批次数: {batch_count}")

        # 4. 统计信息
        logger.info("--- 4. 数据库统计信息 ---")
        try:
            # 使用现有的PostgreSQL客户端，而不是创建新的
            if hasattr(puller_service, 'imdb_client') and puller_service.imdb_client:
                count_query = """
                    SELECT COUNT(*) as total_records,
                           COUNT(DISTINCT employee_id) as unique_employees,
                           MIN(entry_date) as earliest_date,
                           MAX(entry_date) as latest_date
                    FROM entries 
                    WHERE entry_date >= $1 AND entry_date <= $2
                """
                stats = await puller_service.imdb_client.fetch_one(count_query, start_date, end_date)
                if stats:
                    logger.info(f"  - 总记录数: {stats['total_records']}")
                    logger.info(f"  - 唯一员工数: {stats['unique_employees']}")
                    logger.info(f"  - 最早日期: {stats['earliest_date']}")
                    logger.info(f"  - 最晚日期: {stats['latest_date']}")
                else:
                    logger.warning("  - 未找到统计数据")
            else:
                logger.warning("  - PostgreSQL客户端不可用，跳过统计")
        except Exception as stats_error:
            logger.error(f"  - 获取统计信息失败: {stats_error}")

        logger.info("=" * 80)
        logger.info(f"🎉 f3服务{days}天全员极速拉取（分批次）测试完成！")
        logger.info("=" * 80)

    except Exception as e:
        logger.error(f"❌ 测试过程中发生严重错误: {e}", exc_info=True)
    finally:
        logger.info("--- 5. 关闭数据库连接 ---")
        await puller_service.imdb_client.disconnect()
        await puller_service.redis_client.disconnect()
        await puller_service.server6_client.disconnect()
        logger.info("✅ 连接已关闭。测试结束。")


async def test_bulk_fast_pull_custom_date_range(start_date: date, end_date: date, batch_size: int = 7):
    """
    自定义日期范围全员极速拉取（分批次处理）
    
    Args:
        start_date: 开始日期
        end_date: 结束日期
        batch_size: 批次大小（天数）
    """
    logger.info("=" * 80)
    logger.info(f"🚀 开始测试f3服务自定义日期范围全员极速拉取 (bulk_fast - 分批次)")
    logger.info(f"📅 日期范围: {start_date} 到 {end_date}")
    logger.info("=" * 80)
    
    try:
        # 1. 初始化服务
        logger.info("--- 1. 建立数据库连接 ---")
        from app.services.f3_data_puller import DataPullerService
        from app.utils.server6_client import Server6Client
        
        puller_service = DataPullerService()
        await puller_service.connect_only()  # 仅连接数据库，不启动定期拉取循环
        logger.info("✅ 数据库连接成功。")
        
        # 2. 计算拉取范围
        logger.info("--- 2. 计算拉取范围 ---")
        total_days = (end_date - start_date).days + 1
        logger.info(f"  - 开始日期: {start_date}")
        logger.info(f"  - 结束日期: {end_date}")
        logger.info(f"  - 总天数: {total_days} 天")
        logger.info(f"  - 批次大小: {batch_size} 天")
        
        # 3. 分批次拉取数据
        logger.info("--- 3. 分批次拉取数据 ---")
        overall_start_time = datetime.now()
        total_records = 0
        total_affected_rows = 0
        batch_count = 0
        
        current_start = start_date
        
        while current_start <= end_date:
            batch_count += 1
            current_end = min(current_start + timedelta(days=batch_size - 1), end_date)
            
            logger.info(f"  📦 批次 {batch_count}: {current_start} 到 {current_end}")
            batch_start_time = datetime.now()
            
            try:
                # 拉取数据
                records = await puller_service.server6_client.query_bulk_fast(current_start, current_end)
                batch_duration = (datetime.now() - batch_start_time).total_seconds()
                logger.info(f"    ✅ 批次 {batch_count} 拉取完成，耗时: {batch_duration:.2f} 秒")
                logger.info(f"    📊 拉取到记录数: {len(records)}")
                
                if records:
                    total_records += len(records)
                    
                    # 同步到PostgreSQL
                    sync_start_time = datetime.now()
                    affected_rows = await puller_service._sync_to_db(records)
                    sync_duration = (datetime.now() - sync_start_time).total_seconds()
                    
                    total_affected_rows += affected_rows
                    logger.info(f"    💾 批次 {batch_count} 同步完成，耗时: {sync_duration:.2f} 秒")
                    logger.info(f"    📈 同步到PostgreSQL的记录数: {affected_rows}")
                else:
                    logger.info(f"    ⚠️ 批次 {batch_count} 无数据")
                    
            except Exception as e:
                logger.error(f"    ❌ 批次 {batch_count} 处理失败: {e}")
                # 继续处理下一批次，不中断整个流程
            
            # 移动到下一批次
            current_start = current_end + timedelta(days=1)
            
            # 批次间短暂休息，避免对Server6造成压力
            if current_start <= end_date:
                await asyncio.sleep(1)
        
        overall_end_time = datetime.now()
        overall_duration = (overall_end_time - overall_start_time).total_seconds()
        
        logger.info(f"✅ 分批次拉取完成，总耗时: {overall_duration:.2f} 秒")
        logger.info(f"📊 总计拉取记录数: {total_records}")
        logger.info(f"📊 总计同步记录数: {total_affected_rows}")
        logger.info(f"📊 处理批次数: {batch_count}")

        # 4. 统计信息
        logger.info("--- 4. 数据库统计信息 ---")
        try:
            # 使用现有的PostgreSQL客户端，而不是创建新的
            if hasattr(puller_service, 'imdb_client') and puller_service.imdb_client:
                count_query = """
                    SELECT COUNT(*) as total_records,
                           COUNT(DISTINCT employee_id) as unique_employees,
                           MIN(entry_date) as earliest_date,
                           MAX(entry_date) as latest_date
                    FROM entries 
                    WHERE entry_date >= $1 AND entry_date <= $2
                """
                stats = await puller_service.imdb_client.fetch_one(count_query, start_date, end_date)
                if stats:
                    logger.info(f"  - 总记录数: {stats['total_records']}")
                    logger.info(f"  - 唯一员工数: {stats['unique_employees']}")
                    logger.info(f"  - 最早日期: {stats['earliest_date']}")
                    logger.info(f"  - 最晚日期: {stats['latest_date']}")
                else:
                    logger.warning("  - 未找到统计数据")
            else:
                logger.warning("  - PostgreSQL客户端不可用，跳过统计")
        except Exception as stats_error:
            logger.error(f"  - 获取统计信息失败: {stats_error}")

        logger.info("=" * 80)
        logger.info(f"🎉 f3服务自定义日期范围全员极速拉取（分批次）测试完成！")
        logger.info("=" * 80)

    except Exception as e:
        logger.error(f"❌ 测试过程中发生严重错误: {e}", exc_info=True)
    finally:
        logger.info("--- 5. 关闭数据库连接 ---")
        await puller_service.imdb_client.disconnect()
        await puller_service.redis_client.disconnect()
        await puller_service.server6_client.disconnect()
        logger.info("✅ 连接已关闭。测试结束。")


async def main():
    """主函数"""
    print("=" * 60)
    print("🔧 f3数据拉取服务测试工具")
    print("=" * 60)
    print(f"📋 当前配置的同步天数: {USER_SYNC_DAYS} 天")
    print()
    print("请选择测试模式:")
    print("1. 测试配置的天数拉取 (pull_recent_data)")
    print("2. 测试配置的天数拉取 (pull_recent_data，全员模式)")
    print("3. 测试60天拉取 (pull_recent_data)")
    print("4. 测试60天拉取 (pull_recent_data，全员模式)")
    print("5. 测试手动配置天数拉取 (_pull_and_sync)")
    print("6. 测试手动配置天数拉取 (_pull_and_sync，全员模式)")
    print("7. 测试手动60天拉取 (_pull_and_sync)")
    print("8. 测试手动60天拉取 (_pull_and_sync，全员模式)")
    print("9. 自定义天数测试")
    print("10. 自定义天数测试 (全员模式)")
    print("11. 全部测试")
    print("12. 全部测试 (全员模式)")
    print("13. 全员极速拉取（bulk_fast接口）")
    print("14. 自定义天数全员极速拉取（bulk_fast接口）")
    print("15. 全员极速拉取（分批次处理，推荐）")
    print("16. 自定义天数全员极速拉取（分批次处理，推荐）")
    print("17. 自定义日期范围全员极速拉取（分批次处理，推荐）")
    
    choice = input("\n请输入选择 (1-17): ").strip()
    
    if choice == "1":
        await test_data_pull(USER_SYNC_DAYS)
    elif choice == "2":
        await test_data_pull_all_employees(USER_SYNC_DAYS)
    elif choice == "3":
        await test_data_pull(60)
    elif choice == "4":
        await test_data_pull_all_employees(60)
    elif choice == "5":
        await test_manual_data_pull(USER_SYNC_DAYS)
    elif choice == "6":
        await test_manual_data_pull_all_employees(USER_SYNC_DAYS)
    elif choice == "7":
        await test_manual_data_pull(60)
    elif choice == "8":
        await test_manual_data_pull_all_employees(60)
    elif choice == "9":
        try:
            custom_days = int(input("请输入要测试的天数: "))
            if custom_days <= 0:
                print("❌ 天数必须大于0")
                return
            print(f"\n选择测试方法:")
            print("1. 使用 pull_recent_data()")
            print("2. 使用 _pull_and_sync()")
            method_choice = input("请选择方法 (1/2): ").strip()
            if method_choice == "1":
                await test_data_pull(custom_days)
            elif method_choice == "2":
                await test_manual_data_pull(custom_days)
            else:
                print("❌ 无效选择")
        except ValueError:
            print("❌ 请输入有效的数字")
    elif choice == "10":
        try:
            custom_days = int(input("请输入要测试的天数: "))
            if custom_days <= 0:
                print("❌ 天数必须大于0")
                return
            print(f"\n选择测试方法:")
            print("1. 使用 pull_recent_data() (全员模式)")
            print("2. 使用 _pull_and_sync() (全员模式)")
            method_choice = input("请选择方法 (1/2): ").strip()
            if method_choice == "1":
                await test_data_pull_all_employees(custom_days)
            elif method_choice == "2":
                await test_manual_data_pull_all_employees(custom_days)
            else:
                print("❌ 无效选择")
        except ValueError:
            print("❌ 请输入有效的数字")
    elif choice == "11":
        print("\n🔄 执行全部测试...")
        await test_data_pull(USER_SYNC_DAYS)
        print("\n" + "="*80 + "\n")
        await test_data_pull(60)
        print("\n" + "="*80 + "\n")
        await test_manual_data_pull(USER_SYNC_DAYS)
        print("\n" + "="*80 + "\n")
        await test_manual_data_pull(60)
    elif choice == "12":
        print("\n🔄 执行全部测试 (全员模式)...")
        await test_data_pull_all_employees(USER_SYNC_DAYS)
        print("\n" + "="*80 + "\n")
        await test_data_pull_all_employees(60)
        print("\n" + "="*80 + "\n")
        await test_manual_data_pull_all_employees(USER_SYNC_DAYS)
        print("\n" + "="*80 + "\n")
        await test_manual_data_pull_all_employees(60)
    elif choice == "13":
        await test_bulk_fast_pull(USER_SYNC_DAYS)
    elif choice == "14":
        try:
            custom_days = int(input("请输入要测试的天数: "))
            if custom_days <= 0:
                print("❌ 天数必须大于0")
                return
            await test_bulk_fast_pull(custom_days)
        except ValueError:
            print("❌ 请输入有效的数字")
    elif choice == "15":
        await test_bulk_fast_pull_batch(USER_SYNC_DAYS)
    elif choice == "16":
        try:
            custom_days = int(input("请输入要测试的天数: "))
            if custom_days <= 0:
                print("❌ 天数必须大于0")
                return
            batch_size = input("请输入批次大小（默认7天，建议5-10天）: ").strip()
            if batch_size:
                try:
                    batch_size = int(batch_size)
                    if batch_size <= 0:
                        batch_size = 7
                except ValueError:
                    batch_size = 7
            else:
                batch_size = 7
            await test_bulk_fast_pull_batch(custom_days, batch_size)
        except ValueError:
            print("❌ 请输入有效的数字")
    elif choice == "17":
        try:
            print("\n📅 自定义日期范围全员极速拉取")
            print("=" * 50)
            start_date_str = input("请输入开始日期 (YYYY/MM/DD): ").strip()
            end_date_str = input("请输入结束日期 (YYYY/MM/DD): ").strip()
            
            if not start_date_str or not end_date_str:
                print("❌ 日期不能为空")
                return
                
            start_date = datetime.strptime(start_date_str, '%Y/%m/%d').date()
            end_date = datetime.strptime(end_date_str, '%Y/%m/%d').date()
            
            if start_date > end_date:
                print("❌ 开始日期不能大于结束日期")
                return
                
            total_days = (end_date - start_date).days + 1
            print(f"📊 总天数: {total_days} 天")
            
            batch_size_input = input("请输入批次大小（默认7天，建议5-10天）: ").strip()
            if batch_size_input:
                try:
                    batch_size = int(batch_size_input)
                    if batch_size <= 0:
                        batch_size = 7
                        print("⚠️ 批次大小无效，使用默认值7天")
                except ValueError:
                    batch_size = 7
                    print("⚠️ 批次大小格式无效，使用默认值7天")
            else:
                batch_size = 7
                
            print(f"📦 批次大小: {batch_size} 天")
            print(f"📊 预计批次数: {(total_days + batch_size - 1) // batch_size}")
            
            confirm = input("\n确认开始拉取? (y/N): ").strip().lower()
            if confirm in ['y', 'yes', '是']:
                await test_bulk_fast_pull_custom_date_range(start_date, end_date, batch_size)
            else:
                print("❌ 操作已取消")
        except ValueError as e:
            print(f"❌ 日期格式错误: {e}")
            print("请使用 YYYY/MM/DD 格式，例如: 2025/06/01")
    else:
        print("❌ 无效选择，默认执行配置天数拉取测试")
        await test_data_pull(USER_SYNC_DAYS)


if __name__ == "__main__":
    try:
        asyncio.run(main())
    except KeyboardInterrupt:
        logger.info("\n👋 测试被用户中断。") 