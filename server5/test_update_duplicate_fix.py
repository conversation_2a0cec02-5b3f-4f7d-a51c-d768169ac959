#!/usr/bin/env python3
"""
测试更新操作是否还会产生重复的队列项
"""

import asyncio
import aiohttp
import json
import logging
from datetime import datetime

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

# 配置
SERVER5_BASE_URL = "http://localhost:8009"
TEST_ENTRY_ID = 145178  # 使用已知存在的记录ID

async def test_update_operation():
    """测试更新操作"""
    try:
        # 准备更新数据
        update_data = {
            "category": "2",  # 修改区分
            "item": "3",      # 修改项目
            "duration": 3.5   # 修改时间
        }
        
        logger.info(f"🔄 开始测试更新操作: entry_id={TEST_ENTRY_ID}")
        logger.info(f"📦 更新数据: {update_data}")
        
        # 发送更新请求
        async with aiohttp.ClientSession() as session:
            url = f"{SERVER5_BASE_URL}/api/entries/{TEST_ENTRY_ID}"
            
            async with session.put(url, json=update_data) as response:
                if response.status == 200:
                    result = await response.json()
                    logger.info(f"✅ 更新请求成功: {result}")
                    
                    # 等待一段时间让队列处理完成
                    logger.info("⏳ 等待5秒让队列处理完成...")
                    await asyncio.sleep(5)
                    
                    # 检查队列项数量
                    await check_queue_items(TEST_ENTRY_ID)
                    
                else:
                    error_text = await response.text()
                    logger.error(f"❌ 更新请求失败: {response.status} - {error_text}")
                    
    except Exception as e:
        logger.error(f"❌ 测试更新操作失败: {e}")

async def check_queue_items(entry_id):
    """检查指定entry_id的队列项数量"""
    try:
        # 这里需要连接到数据库检查队列项
        # 由于这是测试脚本，我们通过日志来观察
        logger.info(f"🔍 检查entry_id={entry_id}的队列项...")
        logger.info("📋 请检查以下内容:")
        logger.info("1. 终端日志中是否只有一次UPDATE操作")
        logger.info("2. entries_push_queue表中是否只有一条记录")
        logger.info("3. 数据库查询: SELECT * FROM entries_push_queue WHERE entry_id = $1 ORDER BY created_ts DESC")
        
    except Exception as e:
        logger.error(f"❌ 检查队列项失败: {e}")

async def main():
    """主函数"""
    logger.info("🚀 开始测试更新操作重复问题修复")
    
    # 测试更新操作
    await test_update_operation()
    
    logger.info("✅ 测试完成")

if __name__ == "__main__":
    asyncio.run(main()) 