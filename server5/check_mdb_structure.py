#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
检查MDB的数据结构，特别是ID字段
"""

import asyncio
import sys
from pathlib import Path
import logging
from datetime import date

# 添加项目路径
sys.path.append(str(Path(__file__).parent))

from app.utils.server6_client import Server6Client

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

async def check_mdb_structure():
    """检查MDB的数据结构"""
    server6_client = Server6Client()
    
    try:
        # 测试MDB连接
        logger.info("🔍 测试MDB连接...")
        test_result = await server6_client.test_mdb_connection()
        logger.info(f"MDB连接测试结果: {test_result}")
        
        # 查询2025-06-26的数据
        target_date = date(2025, 6, 26)
        logger.info(f"📋 查询{target_date}的数据...")
        
        # 使用bulk_fast接口查询所有数据
        all_records = await server6_client.query_bulk_fast(target_date, target_date)
        
        if all_records:
            logger.info(f"✅ 找到 {len(all_records)} 条记录")
            
            # 显示前5条记录的完整结构
            logger.info("📊 前5条记录的完整结构:")
            for i, record in enumerate(all_records[:5]):
                logger.info(f"  记录{i+1}:")
                for key, value in record.items():
                    logger.info(f"    {key}: {value} (类型: {type(value).__name__})")
                logger.info("")
            
            # 查找ID为602610的记录
            logger.info("🔍 查找ID为602610的记录...")
            found_602610 = False
            for record in all_records:
                # 检查所有可能的ID字段
                id_fields = ['ID', 'id', 'Id', 'external_id', 'External_ID']
                for id_field in id_fields:
                    if id_field in record:
                        id_value = record[id_field]
                        if str(id_value).strip() == '602610':
                            found_602610 = True
                            logger.info(f"✅ 找到ID为602610的记录:")
                            for key, value in record.items():
                                logger.info(f"    {key}: {value}")
                            break
                if found_602610:
                    break
            
            if not found_602610:
                logger.warning("⚠️ 未找到ID为602610的记录")
                
                # 显示所有记录的ID字段
                logger.info("📋 所有记录的ID字段:")
                for i, record in enumerate(all_records):
                    id_values = []
                    for id_field in ['ID', 'id', 'Id', 'external_id', 'External_ID']:
                        if id_field in record:
                            id_values.append(f"{id_field}={record[id_field]}")
                    if id_values:
                        logger.info(f"  记录{i+1}: {', '.join(id_values)}")
        else:
            logger.warning(f"⚠️ 在{target_date}没有找到任何记录")
        
    except Exception as e:
        logger.error(f"❌ 检查失败: {e}", exc_info=True)

async def main():
    """主函数"""
    await check_mdb_structure()

if __name__ == "__main__":
    asyncio.run(main()) 