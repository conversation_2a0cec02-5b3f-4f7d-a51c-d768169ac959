#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试external_id修复后的删除功能
验证f2_push_writer.py正确处理external_id
"""

import asyncio
import sys
from pathlib import Path
import logging
import json
import requests
from datetime import datetime

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

class DeleteExternalIdTest:
    """删除external_id修复测试类"""
    
    def __init__(self):
        self.server5_base_url = "http://localhost:8009"
        self.server6_base_url = "http://************:8019"  # 局域网Win10中的Server6
        
    def test_delete_flow(self):
        """测试完整的删除流程"""
        print("🧪 测试删除功能external_id修复")
        print("=" * 60)
        
        # 1. 首先创建一个测试记录
        print("📝 步骤1: 创建测试记录")
        create_data = {
            "entry_date": "2025/07/15",
            "employee_id": "215829",
            "duration": 0.5,
            "model": "TEST_DELETE_MODEL",
            "number": "TEST_DELETE_001",
            "factory_number": "FACTORY_DELETE_001",
            "project_number": "PROJECT_DELETE_001",
            "unit_number": "UNIT_DELETE_001",
            "category": "1",
            "item": "1",
            "department": "131"
        }
        
        try:
            response = requests.post(
                f"{self.server5_base_url}/client/entries/create",
                json=create_data,
                timeout=10
            )
            
            if response.status_code == 200:
                result = response.json()
                entry_id = result.get('entry_id')
                print(f"✅ 创建成功: entry_id={entry_id}")
                
                # 2. 等待一段时间让数据同步到MDB
                print("⏳ 步骤2: 等待数据同步到MDB...")
                import time
                time.sleep(5)
                
                # 3. 检查entries_push_queue中的external_id
                print("🔍 步骤3: 检查队列中的external_id")
                self.check_queue_external_id(entry_id)
                
                # 4. 执行删除操作
                print("🗑️ 步骤4: 执行删除操作")
                delete_response = requests.delete(
                    f"{self.server5_base_url}/api/entries/{entry_id}",
                    timeout=10
                )
                
                if delete_response.status_code == 200:
                    print("✅ 删除请求成功")
                    result = delete_response.json()
                    print(f"📋 删除结果: {result}")
                    
                    # 5. 等待删除同步
                    print("⏳ 步骤5: 等待删除同步...")
                    time.sleep(5)
                    
                    # 6. 检查删除后的状态
                    print("🔍 步骤6: 检查删除后的状态")
                    self.check_delete_status(entry_id)
                    
                else:
                    print(f"❌ 删除失败: {delete_response.status_code}")
                    print(f"错误信息: {delete_response.text}")
                    
            else:
                print(f"❌ 创建失败: {response.status_code}")
                print(f"错误信息: {response.text}")
                
        except Exception as e:
            print(f"❌ 测试失败: {e}")
    
    def check_queue_external_id(self, entry_id):
        """检查队列中的external_id"""
        try:
            # 查询entries_push_queue表
            response = requests.get(
                f"{self.server5_base_url}/api/entries/",
                params={"limit": 1000},
                timeout=10
            )
            
            if response.status_code == 200:
                entries = response.json()
                
                # 查找对应的队列项
                for entry in entries:
                    if entry.get('id') == entry_id:
                        external_id = entry.get('external_id')
                        print(f"📊 找到记录: entry_id={entry_id}, external_id={external_id}")
                        
                        if external_id:
                            print("✅ external_id存在，可以正常删除MDB记录")
                        else:
                            print("⚠️ external_id不存在，删除时会跳过MDB操作")
                        return
                
                print(f"❌ 未找到entry_id={entry_id}的记录")
            else:
                print(f"❌ 查询失败: {response.status_code}")
                
        except Exception as e:
            print(f"❌ 检查队列失败: {e}")
    
    def check_delete_status(self, entry_id):
        """检查删除后的状态"""
        try:
            # 查询entries表
            response = requests.get(
                f"{self.server5_base_url}/api/entries/",
                params={"limit": 1000},
                timeout=10
            )
            
            if response.status_code == 200:
                entries = response.json()
                
                # 查找对应的记录
                found = False
                for entry in entries:
                    if entry.get('id') == entry_id:
                        found = True
                        print(f"❌ 记录仍然存在: {entry}")
                        break
                
                if not found:
                    print("✅ 记录已成功删除")
                else:
                    print("⚠️ 记录仍然存在，可能需要检查删除逻辑")
            else:
                print(f"❌ 查询失败: {response.status_code}")
                
        except Exception as e:
            print(f"❌ 检查删除状态失败: {e}")

def main():
    """主函数"""
    print("🚀 开始测试删除功能external_id修复")
    
    test = DeleteExternalIdTest()
    test.test_delete_flow()
    
    print("\n" + "=" * 60)
    print("🏁 测试完成")

if __name__ == "__main__":
    main() 