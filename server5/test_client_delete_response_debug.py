#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试客户端删除响应处理
"""

import requests
import json

def test_delete_response_handling():
    """测试删除响应处理逻辑"""
    
    # 模拟Server5的删除响应
    server5_response = {
        "message": "Entry 603639 删除成功，已触发同步"
    }
    
    # 模拟客户端的响应处理逻辑
    def _handle_delete_progress_response(res):
        print(f"📋 收到的响应: {res}")
        
        # 检查响应格式
        if res.get("message") and "删除成功" in res.get("message", ""):
            print("✅ 删除操作成功")
            return True
        else:
            # 处理错误情况
            error_detail = res.get('detail', res.get('message', res.get('status_code', '未知错误')))
            error_msg = f"削除操作失败: {error_detail}"
            print(f"❌ {error_msg}")
            return False
    
    # 测试响应处理
    print("🧪 测试客户端删除响应处理")
    print("=" * 50)
    
    result = _handle_delete_progress_response(server5_response)
    
    if result:
        print("✅ 测试通过：响应处理正确")
    else:
        print("❌ 测试失败：响应处理错误")
    
    # 测试其他可能的响应格式
    print("\n🧪 测试其他响应格式")
    print("=" * 50)
    
    test_cases = [
        {"message": "Entry 603639 删除成功，已触发同步"},
        {"message": "删除成功"},
        {"message": "Entry 603639 删除成功"},
        {"ok": True, "message": "删除成功"},
        {"status": "success", "message": "删除成功"},
        {"detail": "记录不存在"},
        {"status_code": 404, "message": "记录不存在"}
    ]
    
    for i, test_response in enumerate(test_cases, 1):
        print(f"\n📋 测试用例 {i}: {test_response}")
        result = _handle_delete_progress_response(test_response)
        print(f"结果: {'✅ 成功' if result else '❌ 失败'}")

if __name__ == "__main__":
    test_delete_response_handling() 