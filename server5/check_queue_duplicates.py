#!/usr/bin/env python3
"""
检查指定entry_id的队列项数量，验证是否还有重复
"""

import asyncio
import asyncpg
import logging
from datetime import datetime

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

# 数据库配置
DATABASE_URL = "postgresql://postgres:postgres@localhost:5432/mysuite"

async def check_queue_items(entry_id: int):
    """检查指定entry_id的队列项"""
    try:
        # 连接数据库
        conn = await asyncpg.connect(DATABASE_URL)
        
        # 查询队列项
        query = """
            SELECT queue_id, entry_id, external_id, operation, synced, created_ts
            FROM entries_push_queue 
            WHERE entry_id = $1 
            ORDER BY created_ts DESC
        """
        
        rows = await conn.fetch(query, entry_id)
        
        logger.info(f"🔍 entry_id={entry_id} 的队列项:")
        logger.info(f"📊 总数量: {len(rows)}")
        
        if len(rows) == 0:
            logger.info("✅ 没有找到队列项")
        elif len(rows) == 1:
            logger.info("✅ 只有一条队列项，没有重复")
        else:
            logger.warning(f"⚠️ 发现 {len(rows)} 条队列项，可能存在重复")
        
        # 显示详细信息
        for i, row in enumerate(rows):
            logger.info(f"  {i+1}. queue_id={row['queue_id']}, operation={row['operation']}, "
                       f"synced={row['synced']}, created_ts={row['created_ts']}")
        
        # 检查entries表中的记录
        entry_query = """
            SELECT id, external_id, source, ts
            FROM entries 
            WHERE id = $1
        """
        
        entry_row = await conn.fetchrow(entry_query, entry_id)
        if entry_row:
            logger.info(f"📋 entries表记录: id={entry_row['id']}, external_id={entry_row['external_id']}, "
                       f"source={entry_row['source']}, ts={entry_row['ts']}")
        else:
            logger.warning(f"⚠️ 在entries表中未找到entry_id={entry_id}")
        
        await conn.close()
        
    except Exception as e:
        logger.error(f"❌ 检查队列项失败: {e}")

async def check_recent_queue_items(limit: int = 10):
    """检查最近的队列项"""
    try:
        # 连接数据库
        conn = await asyncpg.connect(DATABASE_URL)
        
        # 查询最近的队列项
        query = """
            SELECT queue_id, entry_id, external_id, operation, synced, created_ts
            FROM entries_push_queue 
            ORDER BY created_ts DESC
            LIMIT $1
        """
        
        rows = await conn.fetch(query, limit)
        
        logger.info(f"🔍 最近 {len(rows)} 条队列项:")
        
        for i, row in enumerate(rows):
            logger.info(f"  {i+1}. queue_id={row['queue_id']}, entry_id={row['entry_id']}, "
                       f"operation={row['operation']}, synced={row['synced']}, "
                       f"created_ts={row['created_ts']}")
        
        await conn.close()
        
    except Exception as e:
        logger.error(f"❌ 检查最近队列项失败: {e}")

async def main():
    """主函数"""
    logger.info("🚀 开始检查队列项")
    
    # 检查指定entry_id的队列项
    await check_queue_items(145178)
    
    logger.info("\n" + "="*50 + "\n")
    
    # 检查最近的队列项
    await check_recent_queue_items(5)
    
    logger.info("✅ 检查完成")

if __name__ == "__main__":
    asyncio.run(main()) 