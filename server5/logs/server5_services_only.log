2025-07-10 14:08:59,671 - INFO - 🔊 f1监听器服务初始化完成
2025-07-10 14:08:59,672 - INFO - 📡 Server6客户端初始化 (新版): http://192.168.3.93:8019
2025-07-10 14:08:59,672 - INFO - 📤 f2推送回写引擎初始化完成 (修复版本: 直接轮询PostgreSQL)
2025-07-10 14:08:59,672 - INFO - 📡 Server6客户端初始化 (新版): http://192.168.3.93:8019
2025-07-10 14:08:59,673 - INFO - 📥 f3数据拉取器初始化完成 (Staging & UPSERT 模式)
2025-07-10 14:08:59,673 - INFO - 🎮 f4操作处理器初始化完成
2025-07-10 14:08:59,683 - INFO - Redis连接成功
2025-07-10 14:08:59,683 - INFO - Redis连接成功
2025-07-10 14:08:59,684 - INFO - Redis连接成功
2025-07-10 14:08:59,690 - INFO - ✅ MongoDB连接成功
2025-07-10 14:08:59,690 - INFO - ✅ MongoDB连接成功
2025-07-10 14:08:59,691 - INFO - ✅ MongoDB连接成功
2025-07-10 14:08:59,968 - INFO - ✅ PostgreSQL连接池创建成功
2025-07-10 14:08:59,969 - INFO - Redis连接成功
2025-07-10 14:08:59,969 - INFO - ✅ f3数据拉取服务启动成功，预定执行时间: ['02:00', '03:00']
2025-07-10 14:08:59,969 - INFO - 🚀 数据拉取调度器启动，预定运行时间: ['02:00', '03:00']
2025-07-10 14:08:59,969 - INFO - 下一次f3/f5同步任务将在 2025-07-11 02:00:00 执行，等待 42660 秒...
2025-07-10 14:08:59,995 - INFO - ✅ PostgreSQL连接池创建成功
2025-07-10 14:08:59,995 - INFO - ✅ f1监听器服务启动成功
2025-07-10 14:09:00,029 - INFO - ✅ PostgreSQL连接池创建成功
2025-07-10 14:09:00,029 - INFO - ✅ f2推送回写服务启动成功，工作线程数: 4
2025-07-10 14:09:00,029 - INFO - 🚀 推送工作线程启动: worker_1
2025-07-10 14:09:00,029 - INFO - 🚀 推送工作线程启动: worker_2
2025-07-10 14:09:00,029 - INFO - 🚀 推送工作线程启动: worker_3
2025-07-10 14:09:00,030 - INFO - 🚀 推送工作线程启动: worker_4
2025-07-10 14:09:00,032 - INFO - ✅ 分区创建成功: entries_2025_07
2025-07-10 14:09:00,036 - INFO - ✅ 分区创建成功: entries_2025_08
2025-07-10 14:09:00,094 - INFO - ✅ PostgreSQL连接池创建成功
2025-07-10 14:09:00,094 - INFO - ✅ f4操作处理服务启动成功
2025-07-10 14:09:00,094 - INFO - 🔌 推送工作线程停止: worker_3
2025-07-10 14:09:00,094 - INFO - 🔌 推送工作线程停止: worker_4
2025-07-10 14:09:00,096 - INFO - 🔌 推送工作线程停止: worker_1
2025-07-10 14:09:00,096 - INFO - 🔌 推送工作线程停止: worker_2
2025-07-10 14:09:09,434 - INFO - 🔌 MongoDB连接已关闭
2025-07-10 14:09:09,435 - INFO - 🔌 MongoDB连接已关闭
2025-07-10 14:09:09,436 - INFO - 🔌 MongoDB连接已关闭
2025-07-10 14:09:09,436 - INFO - 🔌 f1监听器服务已停止
2025-07-10 14:09:09,436 - INFO - 🔌 f2推送回写服务已停止
2025-07-10 14:09:09,436 - INFO - 🔌 f4操作处理服务已停止
2025-07-10 14:09:09,689 - INFO - 🔌 PostgreSQL连接已关闭
2025-07-10 14:09:09,689 - INFO - 🔌 PostgreSQL连接已关闭
2025-07-10 14:09:09,689 - INFO - 🔌 MongoDB连接已关闭
2025-07-10 14:09:09,689 - INFO - 🔌 PostgreSQL连接已关闭
2025-07-10 14:09:09,689 - INFO - 🔌 MongoDB连接已关闭
2025-07-10 14:09:09,689 - INFO - 🔌 PostgreSQL连接已关闭
2025-07-10 14:09:09,689 - INFO - 🔌 MongoDB连接已关闭
2025-07-10 14:09:09,689 - INFO - 🔌 Redis连接已关闭
2025-07-10 14:09:09,689 - INFO - 🔌 Redis连接已关闭
2025-07-10 14:09:09,689 - INFO - 🔌 Redis连接已关闭
2025-07-10 14:09:09,689 - INFO - 🔌 f1监听器服务已停止
2025-07-10 14:09:09,689 - INFO - 🔌 f2推送回写服务已停止
2025-07-10 14:09:09,689 - INFO - 🔌 f4操作处理服务已停止
2025-07-10 18:08:39,905 - INFO - 🔊 f1监听器服务初始化完成
2025-07-10 18:08:39,905 - INFO - 📡 Server6客户端初始化 (新版): http://192.168.3.93:8019
2025-07-10 18:08:39,905 - INFO - 📤 f2推送回写引擎初始化完成 (修复版本: 直接轮询PostgreSQL)
2025-07-10 18:08:39,905 - INFO - 📡 Server6客户端初始化 (新版): http://192.168.3.93:8019
2025-07-10 18:08:39,906 - INFO - 📥 f3数据拉取器初始化完成 (Staging & UPSERT 模式)
2025-07-10 18:08:39,906 - INFO - 🎮 f4操作处理器初始化完成
2025-07-10 18:08:39,915 - INFO - Redis连接成功
2025-07-10 18:08:39,915 - INFO - Redis连接成功
2025-07-10 18:08:39,916 - INFO - Redis连接成功
2025-07-10 18:08:39,922 - INFO - ✅ MongoDB连接成功
2025-07-10 18:08:39,923 - INFO - ✅ MongoDB连接成功
2025-07-10 18:08:39,924 - INFO - ✅ MongoDB连接成功
2025-07-10 18:08:40,245 - INFO - ✅ PostgreSQL连接池创建成功
2025-07-10 18:08:40,246 - INFO - Redis连接成功
2025-07-10 18:08:40,246 - INFO - ✅ f3数据拉取服务启动成功，预定执行时间: ['02:00', '03:00']
2025-07-10 18:08:40,246 - INFO - 🚀 数据拉取调度器启动，预定运行时间: ['02:00', '03:00']
2025-07-10 18:08:40,246 - INFO - 下一次f3/f5同步任务将在 2025-07-11 02:00:00 执行，等待 28280 秒...
2025-07-10 18:08:40,263 - INFO - ✅ PostgreSQL连接池创建成功
2025-07-10 18:08:40,263 - INFO - ✅ f1监听器服务启动成功
2025-07-10 18:08:40,281 - INFO - ✅ PostgreSQL连接池创建成功
2025-07-10 18:08:40,281 - INFO - ✅ f2推送回写服务启动成功，工作线程数: 4
2025-07-10 18:08:40,281 - INFO - 🚀 推送工作线程启动: worker_1
2025-07-10 18:08:40,282 - INFO - 🚀 推送工作线程启动: worker_2
2025-07-10 18:08:40,282 - INFO - 🚀 推送工作线程启动: worker_3
2025-07-10 18:08:40,282 - INFO - 🚀 推送工作线程启动: worker_4
2025-07-10 18:08:40,286 - INFO - ✅ PostgreSQL连接池创建成功
2025-07-10 18:08:40,286 - INFO - ✅ f4操作处理服务启动成功
2025-07-10 18:08:40,286 - INFO - 🔌 推送工作线程停止: worker_3
2025-07-10 18:08:40,286 - INFO - 🔌 推送工作线程停止: worker_4
2025-07-10 18:08:40,488 - INFO - 🔌 推送工作线程停止: worker_1
2025-07-10 18:08:40,509 - INFO - 🔌 推送工作线程停止: worker_2
