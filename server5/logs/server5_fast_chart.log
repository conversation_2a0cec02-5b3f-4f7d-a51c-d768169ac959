🔧 加载配置: config_ubuntu_remote
🐛 MySuite Server5 - 数据同步微服务 (Ubuntu远程) 运行在调试模式
📊 PostgreSQL: ************:5432
🍃 MongoDB: ************:27017
⚡ Redis: localhost:6379
💾 ODBC可用: False
📋 配置信息:
  - 配置文件: config_ubuntu_remote
  - 平台: Linux
  - 主机: test
  - IP: *************
  - 自动检测: config_ubuntu_remote
🔧 加载配置: config_ubuntu_remote
📋 配置信息:
  - 配置文件: config_ubuntu_remote
  - 平台: Linux
  - 主机: test
  - IP: *************
  - 自动检测: config_ubuntu_remote
🚀 MySuite Server5 - 完整启动模式 (微服务 + HTTP API)
🌐 启动HTTP API服务器...
🔍 发现占用端口8009的进程: ['1898517']
✅ 已杀掉进程 1898517
✅ 端口8009已释放
🔧 执行命令: /home/<USER>/miniconda3/envs/my_suite_unified/bin/python -m uvicorn app.main:app --host 0.0.0.0 --port 8009 --log-level info
✅ HTTP服务器启动成功，PID: 1900187
🚀 启动Server5微服务...
2025-07-10 07:41:36,596 - INFO - 🔊 f1监听器服务初始化完成
2025-07-10 07:41:36,596 - INFO - 📡 Server6客户端初始化 (新版): http://************:8009
2025-07-10 07:41:36,597 - INFO - 📤 f2推送回写引擎初始化完成 (修复版本: 直接轮询PostgreSQL)
2025-07-10 07:41:36,597 - INFO - 📡 Server6客户端初始化 (新版): http://************:8009
2025-07-10 07:41:36,597 - INFO - 📥 f3数据拉取器初始化完成 (Staging & UPSERT 模式)
2025-07-10 07:41:36,598 - INFO - 🎮 f4操作处理器初始化完成
2025-07-10 07:41:36,607 - INFO - Redis连接成功
2025-07-10 07:41:36,607 - INFO - Redis连接成功
2025-07-10 07:41:36,607 - INFO - Redis连接成功
2025-07-10 07:41:36,613 - INFO - ✅ MongoDB连接成功
2025-07-10 07:41:36,614 - INFO - ✅ MongoDB连接成功
2025-07-10 07:41:36,616 - INFO - ✅ MongoDB连接成功
2025-07-10 07:41:36,850 - INFO - ✅ PostgreSQL连接池创建成功
2025-07-10 07:41:36,851 - INFO - Redis连接成功
2025-07-10 07:41:36,851 - INFO - ✅ f3数据拉取服务启动成功，预定执行时间: ['02:00', '03:00']
2025-07-10 07:41:36,851 - INFO - 🚀 数据拉取调度器启动，预定运行时间: ['02:00', '03:00']
2025-07-10 07:41:36,852 - INFO - 下一次f3/f5同步任务将在 2025-07-11 02:00:00 执行，等待 65903 秒...
2025-07-10 07:41:36,906 - INFO - ✅ PostgreSQL连接池创建成功
2025-07-10 07:41:36,907 - INFO - ✅ f1监听器服务启动成功
2025-07-10 07:41:36,916 - INFO - ✅ 分区创建成功: entries_2025_07
2025-07-10 07:41:36,920 - INFO - ✅ 分区创建成功: entries_2025_08
2025-07-10 07:41:36,944 - INFO - ✅ PostgreSQL连接池创建成功
2025-07-10 07:41:36,944 - INFO - ✅ f2推送回写服务启动成功，工作线程数: 4
2025-07-10 07:41:36,944 - INFO - 🚀 推送工作线程启动: worker_1
2025-07-10 07:41:36,944 - INFO - 🚀 推送工作线程启动: worker_2
2025-07-10 07:41:36,944 - INFO - 🚀 推送工作线程启动: worker_3
2025-07-10 07:41:36,945 - INFO - 🚀 推送工作线程启动: worker_4
2025-07-10 07:41:36,986 - INFO - ✅ PostgreSQL连接池创建成功
2025-07-10 07:41:36,986 - INFO - ✅ f4操作处理服务启动成功
✅ f1_listener 启动成功
✅ f2_push_writer 启动成功
✅ f3_data_puller 启动成功
✅ f4_operation_handler 启动成功
🎉 所有微服务已启动
2025-07-10 07:41:36,986 - INFO - 🔌 推送工作线程停止: worker_4
2025-07-10 07:41:36,986 - INFO - 🔌 推送工作线程停止: worker_3
2025-07-10 07:41:36,987 - INFO - 🔌 推送工作线程停止: worker_2
2025-07-10 07:41:36,988 - INFO - 🔌 推送工作线程停止: worker_1
🎉 Server5完整启动成功！
📡 HTTP API地址: http://localhost:8009
🔧 微服务状态: 运行中
👋 按 Ctrl+C 退出

📡 接收到信号 15，正在关闭服务...

🧹 执行清理操作...
🛑 停止HTTP服务器...
🛑 停止微服务...
🛑 正在停止微服务...
2025-07-10 07:43:31,533 - INFO - 🔌 MongoDB连接已关闭
2025-07-10 07:43:31,535 - INFO - 🔌 MongoDB连接已关闭
2025-07-10 07:43:31,536 - INFO - 🔌 MongoDB连接已关闭
2025-07-10 07:43:31,536 - INFO - 🔌 f1监听器服务已停止
2025-07-10 07:43:31,536 - INFO - 🔌 f2推送回写服务已停止
2025-07-10 07:43:31,536 - INFO - 🔌 f4操作处理服务已停止
✅ 微服务已停止
✅ 清理完成

🧹 执行清理操作...
🛑 停止微服务...
🛑 正在停止微服务...
2025-07-10 07:43:31,643 - INFO - 🔌 PostgreSQL连接已关闭
2025-07-10 07:43:31,643 - INFO - 🔌 PostgreSQL连接已关闭
2025-07-10 07:43:31,643 - INFO - 🔌 MongoDB连接已关闭
2025-07-10 07:43:31,643 - INFO - 🔌 PostgreSQL连接已关闭
2025-07-10 07:43:31,643 - INFO - 🔌 MongoDB连接已关闭
2025-07-10 07:43:31,644 - INFO - 🔌 PostgreSQL连接已关闭
2025-07-10 07:43:31,644 - INFO - 🔌 MongoDB连接已关闭
2025-07-10 07:43:31,644 - INFO - 🔌 Redis连接已关闭
2025-07-10 07:43:31,644 - INFO - 🔌 Redis连接已关闭
2025-07-10 07:43:31,644 - INFO - 🔌 Redis连接已关闭
2025-07-10 07:43:31,644 - INFO - 🔌 f1监听器服务已停止
2025-07-10 07:43:31,644 - INFO - 🔌 f2推送回写服务已停止
2025-07-10 07:43:31,644 - INFO - 🔌 f4操作处理服务已停止
✅ 微服务已停止
✅ 清理完成
