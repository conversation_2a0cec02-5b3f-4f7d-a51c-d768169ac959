2025-07-08 13:11:17,081 - __main__ - INFO - 🚀 MySuite Server5 - Ubuntu 远程模式 (仅启动 f1~f4 服务)
2025-07-08 13:11:17,082 - asyncio - DEBUG - Using selector: EpollSelector
2025-07-08 13:11:17,257 - app.services.f1_listener - INFO - 🔊 f1监听器服务初始化完成
2025-07-08 13:11:17,257 - app.utils.server6_client - INFO - 📡 Server6客户端初始化 (新版): http://************:8009
2025-07-08 13:11:17,257 - app.services.f2_push_writer - INFO - 📤 f2推送回写引擎初始化完成 (修复版本: 直接轮询PostgreSQL)
2025-07-08 13:11:17,257 - app.utils.server6_client - INFO - 📡 Server6客户端初始化 (新版): http://************:8009
2025-07-08 13:11:17,257 - app.services.f3_data_puller - INFO - 📥 f3数据拉取器初始化完成 (Staging & UPSERT 模式)
2025-07-08 13:11:17,258 - app.services.f4_operation_handler - INFO - 🎮 f4操作处理器初始化完成
2025-07-08 13:11:17,261 - pymongo.topology - DEBUG - {"message": "Starting topology monitoring", "topologyId": {"$oid": "686c9a65363625d1011598fc"}}
2025-07-08 13:11:17,261 - pymongo.topology - DEBUG - {"message": "Topology description changed", "topologyId": {"$oid": "686c9a65363625d1011598fc"}, "previousDescription": "<TopologyDescription id: 686c9a65363625d1011598fc, topology_type: Unknown, servers: []>", "newDescription": "<TopologyDescription id: 686c9a65363625d1011598fc, topology_type: Unknown, servers: [<ServerDescription ('************', 27017) server_type: Unknown, rtt: None>]>"}
2025-07-08 13:11:17,261 - pymongo.topology - DEBUG - {"message": "Starting server monitoring", "topologyId": {"$oid": "686c9a65363625d1011598fc"}, "serverHost": "************", "serverPort": 27017}
2025-07-08 13:11:17,262 - pymongo.connection - DEBUG - {"message": "Connection pool created", "clientId": {"$oid": "686c9a65363625d1011598fc"}, "serverHost": "************", "serverPort": 27017}
2025-07-08 13:11:17,262 - pymongo.serverSelection - DEBUG - {"message": "Server selection started", "selector": "Primary()", "operation": "ismaster", "topologyDescription": "<TopologyDescription id: 686c9a65363625d1011598fc, topology_type: Unknown, servers: [<ServerDescription ('************', 27017) server_type: Unknown, rtt: None>]>", "clientId": {"$oid": "686c9a65363625d1011598fc"}}
2025-07-08 13:11:17,263 - pymongo.topology - DEBUG - {"message": "Server heartbeat started", "topologyId": {"$oid": "686c9a65363625d1011598fc"}, "driverConnectionId": 1, "serverHost": "************", "serverPort": 27017, "awaited": false}
2025-07-08 13:11:17,263 - pymongo.serverSelection - DEBUG - {"message": "Waiting for suitable server to become available", "selector": "Primary()", "operation": "ismaster", "topologyDescription": "<TopologyDescription id: 686c9a65363625d1011598fc, topology_type: Unknown, servers: [<ServerDescription ('************', 27017) server_type: Unknown, rtt: None>]>", "clientId": {"$oid": "686c9a65363625d1011598fc"}, "remainingTimeMS": 4999}
2025-07-08 13:11:17,263 - pymongo.topology - DEBUG - {"message": "Starting topology monitoring", "topologyId": {"$oid": "686c9a65363625d1011598fd"}}
2025-07-08 13:11:17,263 - pymongo.topology - DEBUG - {"message": "Topology description changed", "topologyId": {"$oid": "686c9a65363625d1011598fd"}, "previousDescription": "<TopologyDescription id: 686c9a65363625d1011598fd, topology_type: Unknown, servers: []>", "newDescription": "<TopologyDescription id: 686c9a65363625d1011598fd, topology_type: Unknown, servers: [<ServerDescription ('************', 27017) server_type: Unknown, rtt: None>]>"}
2025-07-08 13:11:17,264 - pymongo.topology - DEBUG - {"message": "Starting server monitoring", "topologyId": {"$oid": "686c9a65363625d1011598fd"}, "serverHost": "************", "serverPort": 27017}
2025-07-08 13:11:17,264 - pymongo.connection - DEBUG - {"message": "Connection pool created", "clientId": {"$oid": "686c9a65363625d1011598fd"}, "serverHost": "************", "serverPort": 27017}
2025-07-08 13:11:17,265 - pymongo.serverSelection - DEBUG - {"message": "Server selection started", "selector": "Primary()", "operation": "ismaster", "topologyDescription": "<TopologyDescription id: 686c9a65363625d1011598fd, topology_type: Unknown, servers: [<ServerDescription ('************', 27017) server_type: Unknown, rtt: None>]>", "clientId": {"$oid": "686c9a65363625d1011598fd"}}
2025-07-08 13:11:17,265 - pymongo.serverSelection - DEBUG - {"message": "Waiting for suitable server to become available", "selector": "Primary()", "operation": "ismaster", "topologyDescription": "<TopologyDescription id: 686c9a65363625d1011598fd, topology_type: Unknown, servers: [<ServerDescription ('************', 27017) server_type: Unknown, rtt: None>]>", "clientId": {"$oid": "686c9a65363625d1011598fd"}, "remainingTimeMS": 4999}
2025-07-08 13:11:17,265 - pymongo.topology - DEBUG - {"message": "Starting topology monitoring", "topologyId": {"$oid": "686c9a65363625d1011598fe"}}
2025-07-08 13:11:17,265 - pymongo.topology - DEBUG - {"message": "Server heartbeat started", "topologyId": {"$oid": "686c9a65363625d1011598fd"}, "driverConnectionId": 1, "serverHost": "************", "serverPort": 27017, "awaited": false}
2025-07-08 13:11:17,265 - pymongo.topology - DEBUG - {"message": "Topology description changed", "topologyId": {"$oid": "686c9a65363625d1011598fe"}, "previousDescription": "<TopologyDescription id: 686c9a65363625d1011598fe, topology_type: Unknown, servers: []>", "newDescription": "<TopologyDescription id: 686c9a65363625d1011598fe, topology_type: Unknown, servers: [<ServerDescription ('************', 27017) server_type: Unknown, rtt: None>]>"}
2025-07-08 13:11:17,265 - pymongo.topology - DEBUG - {"message": "Server heartbeat succeeded", "topologyId": {"$oid": "686c9a65363625d1011598fc"}, "driverConnectionId": 1, "serverConnectionId": 1735, "serverHost": "************", "serverPort": 27017, "awaited": false, "durationMS": 2.15936207678169, "reply": "{\"helloOk\": true, \"ismaster\": true, \"topologyVersion\": {\"processId\": {\"$oid\": \"685b97d01d8e2f809d089f93\"}}, \"maxBsonObjectSize\": 16777216, \"maxMessageSizeBytes\": 48000000, \"maxWriteBatchSize\": 100000, \"localTime\": {\"$date\": \"2025-07-08T04:11:17.276Z\"}, \"logicalSessionTimeoutMinutes\": 30, \"connectionId\": 1735, \"maxWireVersion\": 25, \"ok\": 1.0}"}
2025-07-08 13:11:17,265 - pymongo.topology - DEBUG - {"message": "Starting server monitoring", "topologyId": {"$oid": "686c9a65363625d1011598fe"}, "serverHost": "************", "serverPort": 27017}
2025-07-08 13:11:17,266 - pymongo.connection - DEBUG - {"message": "Connection pool ready", "clientId": {"$oid": "686c9a65363625d1011598fc"}, "serverHost": "************", "serverPort": 27017}
2025-07-08 13:11:17,266 - pymongo.connection - DEBUG - {"message": "Connection pool created", "clientId": {"$oid": "686c9a65363625d1011598fe"}, "serverHost": "************", "serverPort": 27017}
2025-07-08 13:11:17,266 - pymongo.topology - DEBUG - {"message": "Topology description changed", "topologyId": {"$oid": "686c9a65363625d1011598fc"}, "previousDescription": "<TopologyDescription id: 686c9a65363625d1011598fc, topology_type: Unknown, servers: [<ServerDescription ('************', 27017) server_type: Unknown, rtt: None>]>", "newDescription": "<TopologyDescription id: 686c9a65363625d1011598fc, topology_type: Single, servers: [<ServerDescription ('************', 27017) server_type: Standalone, rtt: 0.00215936207678169>]>"}
2025-07-08 13:11:17,267 - pymongo.topology - DEBUG - {"message": "Server heartbeat started", "topologyId": {"$oid": "686c9a65363625d1011598fc"}, "driverConnectionId": 1, "serverConnectionId": 1735, "serverHost": "************", "serverPort": 27017, "awaited": true}
2025-07-08 13:11:17,267 - pymongo.serverSelection - DEBUG - {"message": "Server selection succeeded", "selector": "Primary()", "operation": "ismaster", "topologyDescription": "<TopologyDescription id: 686c9a65363625d1011598fc, topology_type: Single, servers: [<ServerDescription ('************', 27017) server_type: Standalone, rtt: 0.00215936207678169>]>", "clientId": {"$oid": "686c9a65363625d1011598fc"}, "serverHost": "************", "serverPort": 27017}
2025-07-08 13:11:17,267 - pymongo.topology - DEBUG - {"message": "Server heartbeat started", "topologyId": {"$oid": "686c9a65363625d1011598fe"}, "driverConnectionId": 1, "serverHost": "************", "serverPort": 27017, "awaited": false}
2025-07-08 13:11:17,267 - pymongo.serverSelection - DEBUG - {"message": "Server selection started", "selector": "Primary()", "operation": "ismaster", "topologyDescription": "<TopologyDescription id: 686c9a65363625d1011598fe, topology_type: Unknown, servers: [<ServerDescription ('************', 27017) server_type: Unknown, rtt: None>]>", "clientId": {"$oid": "686c9a65363625d1011598fe"}}
2025-07-08 13:11:17,268 - pymongo.topology - DEBUG - {"message": "Server heartbeat succeeded", "topologyId": {"$oid": "686c9a65363625d1011598fd"}, "driverConnectionId": 1, "serverConnectionId": 1736, "serverHost": "************", "serverPort": 27017, "awaited": false, "durationMS": 2.229806035757065, "reply": "{\"helloOk\": true, \"ismaster\": true, \"topologyVersion\": {\"processId\": {\"$oid\": \"685b97d01d8e2f809d089f93\"}}, \"maxBsonObjectSize\": 16777216, \"maxMessageSizeBytes\": 48000000, \"maxWriteBatchSize\": 100000, \"localTime\": {\"$date\": \"2025-07-08T04:11:17.278Z\"}, \"logicalSessionTimeoutMinutes\": 30, \"connectionId\": 1736, \"maxWireVersion\": 25, \"ok\": 1.0}"}
2025-07-08 13:11:17,268 - pymongo.connection - DEBUG - {"message": "Connection checkout started", "clientId": {"$oid": "686c9a65363625d1011598fc"}, "serverHost": "************", "serverPort": 27017}
2025-07-08 13:11:17,268 - pymongo.serverSelection - DEBUG - {"message": "Waiting for suitable server to become available", "selector": "Primary()", "operation": "ismaster", "topologyDescription": "<TopologyDescription id: 686c9a65363625d1011598fe, topology_type: Unknown, servers: [<ServerDescription ('************', 27017) server_type: Unknown, rtt: None>]>", "clientId": {"$oid": "686c9a65363625d1011598fe"}, "remainingTimeMS": 4999}
2025-07-08 13:11:17,268 - pymongo.connection - DEBUG - {"message": "Connection pool ready", "clientId": {"$oid": "686c9a65363625d1011598fd"}, "serverHost": "************", "serverPort": 27017}
2025-07-08 13:11:17,268 - pymongo.connection - DEBUG - {"message": "Connection created", "clientId": {"$oid": "686c9a65363625d1011598fc"}, "serverHost": "************", "serverPort": 27017, "driverConnectionId": 1}
2025-07-08 13:11:17,269 - pymongo.topology - DEBUG - {"message": "Topology description changed", "topologyId": {"$oid": "686c9a65363625d1011598fd"}, "previousDescription": "<TopologyDescription id: 686c9a65363625d1011598fd, topology_type: Unknown, servers: [<ServerDescription ('************', 27017) server_type: Unknown, rtt: None>]>", "newDescription": "<TopologyDescription id: 686c9a65363625d1011598fd, topology_type: Single, servers: [<ServerDescription ('************', 27017) server_type: Standalone, rtt: 0.002229806035757065>]>"}
2025-07-08 13:11:17,269 - pymongo.serverSelection - DEBUG - {"message": "Server selection succeeded", "selector": "Primary()", "operation": "ismaster", "topologyDescription": "<TopologyDescription id: 686c9a65363625d1011598fd, topology_type: Single, servers: [<ServerDescription ('************', 27017) server_type: Standalone, rtt: 0.002229806035757065>]>", "clientId": {"$oid": "686c9a65363625d1011598fd"}, "serverHost": "************", "serverPort": 27017}
2025-07-08 13:11:17,269 - pymongo.connection - DEBUG - {"message": "Connection checkout started", "clientId": {"$oid": "686c9a65363625d1011598fd"}, "serverHost": "************", "serverPort": 27017}
2025-07-08 13:11:17,269 - pymongo.topology - DEBUG - {"message": "Server heartbeat started", "topologyId": {"$oid": "686c9a65363625d1011598fd"}, "driverConnectionId": 1, "serverConnectionId": 1736, "serverHost": "************", "serverPort": 27017, "awaited": true}
2025-07-08 13:11:17,269 - pymongo.connection - DEBUG - {"message": "Connection created", "clientId": {"$oid": "686c9a65363625d1011598fd"}, "serverHost": "************", "serverPort": 27017, "driverConnectionId": 1}
2025-07-08 13:11:17,269 - app.database.redis_client - INFO - Redis连接成功
2025-07-08 13:11:17,270 - app.database.redis_client - INFO - Redis连接成功
2025-07-08 13:11:17,270 - pymongo.topology - DEBUG - {"message": "Server heartbeat succeeded", "topologyId": {"$oid": "686c9a65363625d1011598fe"}, "driverConnectionId": 1, "serverConnectionId": 1737, "serverHost": "************", "serverPort": 27017, "awaited": false, "durationMS": 2.1269849967211485, "reply": "{\"helloOk\": true, \"ismaster\": true, \"topologyVersion\": {\"processId\": {\"$oid\": \"685b97d01d8e2f809d089f93\"}}, \"maxBsonObjectSize\": 16777216, \"maxMessageSizeBytes\": 48000000, \"maxWriteBatchSize\": 100000, \"localTime\": {\"$date\": \"2025-07-08T04:11:17.280Z\"}, \"logicalSessionTimeoutMinutes\": 30, \"connectionId\": 1737, \"maxWireVersion\": 25, \"ok\": 1.0}"}
2025-07-08 13:11:17,270 - app.database.redis_client - INFO - Redis连接成功
2025-07-08 13:11:17,270 - pymongo.connection - DEBUG - {"message": "Connection pool ready", "clientId": {"$oid": "686c9a65363625d1011598fe"}, "serverHost": "************", "serverPort": 27017}
2025-07-08 13:11:17,270 - pymongo.topology - DEBUG - {"message": "Topology description changed", "topologyId": {"$oid": "686c9a65363625d1011598fe"}, "previousDescription": "<TopologyDescription id: 686c9a65363625d1011598fe, topology_type: Unknown, servers: [<ServerDescription ('************', 27017) server_type: Unknown, rtt: None>]>", "newDescription": "<TopologyDescription id: 686c9a65363625d1011598fe, topology_type: Single, servers: [<ServerDescription ('************', 27017) server_type: Standalone, rtt: 0.0021269849967211485>]>"}
2025-07-08 13:11:17,270 - pymongo.serverSelection - DEBUG - {"message": "Server selection succeeded", "selector": "Primary()", "operation": "ismaster", "topologyDescription": "<TopologyDescription id: 686c9a65363625d1011598fe, topology_type: Single, servers: [<ServerDescription ('************', 27017) server_type: Standalone, rtt: 0.0021269849967211485>]>", "clientId": {"$oid": "686c9a65363625d1011598fe"}, "serverHost": "************", "serverPort": 27017}
2025-07-08 13:11:17,271 - pymongo.connection - DEBUG - {"message": "Connection checkout started", "clientId": {"$oid": "686c9a65363625d1011598fe"}, "serverHost": "************", "serverPort": 27017}
2025-07-08 13:11:17,271 - pymongo.topology - DEBUG - {"message": "Server heartbeat started", "topologyId": {"$oid": "686c9a65363625d1011598fe"}, "driverConnectionId": 1, "serverConnectionId": 1737, "serverHost": "************", "serverPort": 27017, "awaited": true}
2025-07-08 13:11:17,271 - pymongo.connection - DEBUG - {"message": "Connection created", "clientId": {"$oid": "686c9a65363625d1011598fe"}, "serverHost": "************", "serverPort": 27017, "driverConnectionId": 1}
2025-07-08 13:11:17,272 - pymongo.connection - DEBUG - {"message": "Connection ready", "clientId": {"$oid": "686c9a65363625d1011598fc"}, "serverHost": "************", "serverPort": 27017, "driverConnectionId": 1, "durationMS": 0.0017395990435034037}
2025-07-08 13:11:17,272 - pymongo.connection - DEBUG - {"message": "Connection checked out", "clientId": {"$oid": "686c9a65363625d1011598fc"}, "serverHost": "************", "serverPort": 27017, "driverConnectionId": 1, "durationMS": 0.003980520064942539}
2025-07-08 13:11:17,272 - pymongo.command - DEBUG - {"message": "Command started", "clientId": {"$oid": "686c9a65363625d1011598fc"}, "command": "{\"ismaster\": 1, \"lsid\": {\"id\": {\"$binary\": {\"base64\": \"v+NlGVsPQiaD8WQeGQtJog==\", \"subType\": \"04\"}}}, \"$db\": \"admin\"}", "commandName": "ismaster", "databaseName": "admin", "requestId": 1025202362, "operationId": 1025202362, "driverConnectionId": 1, "serverConnectionId": 1739, "serverHost": "************", "serverPort": 27017}
2025-07-08 13:11:17,273 - pymongo.connection - DEBUG - {"message": "Connection ready", "clientId": {"$oid": "686c9a65363625d1011598fd"}, "serverHost": "************", "serverPort": 27017, "driverConnectionId": 1, "durationMS": 0.0018234850140288472}
2025-07-08 13:11:17,273 - pymongo.connection - DEBUG - {"message": "Connection checked out", "clientId": {"$oid": "686c9a65363625d1011598fd"}, "serverHost": "************", "serverPort": 27017, "driverConnectionId": 1, "durationMS": 0.003503968007862568}
2025-07-08 13:11:17,273 - pymongo.command - DEBUG - {"message": "Command started", "clientId": {"$oid": "686c9a65363625d1011598fd"}, "command": "{\"ismaster\": 1, \"lsid\": {\"id\": {\"$binary\": {\"base64\": \"Fo1cXB7FQviBfXK9hy9pew==\", \"subType\": \"04\"}}}, \"$db\": \"admin\"}", "commandName": "ismaster", "databaseName": "admin", "requestId": 1102520059, "operationId": 1102520059, "driverConnectionId": 1, "serverConnectionId": 1741, "serverHost": "************", "serverPort": 27017}
2025-07-08 13:11:17,273 - pymongo.command - DEBUG - {"message": "Command succeeded", "clientId": {"$oid": "686c9a65363625d1011598fc"}, "durationMS": 1.355, "reply": "{\"ismaster\": true, \"topologyVersion\": {\"processId\": {\"$oid\": \"685b97d01d8e2f809d089f93\"}}, \"maxBsonObjectSize\": 16777216, \"maxMessageSizeBytes\": 48000000, \"maxWriteBatchSize\": 100000, \"localTime\": {\"$date\": \"2025-07-08T04:11:17.284Z\"}, \"logicalSessionTimeoutMinutes\": 30, \"connectionId\": 1739, \"maxWireVersion\": 25, \"ok\": 1.0}", "commandName": "ismaster", "databaseName": "admin", "requestId": 1025202362, "operationId": 1025202362, "driverConnectionId": 1, "serverConnectionId": 1739, "serverHost": "************", "serverPort": 27017}
2025-07-08 13:11:17,273 - pymongo.connection - DEBUG - {"message": "Connection checked in", "clientId": {"$oid": "686c9a65363625d1011598fc"}, "serverHost": "************", "serverPort": 27017, "driverConnectionId": 1}
2025-07-08 13:11:17,273 - pymongo.serverSelection - DEBUG - {"message": "Server selection started", "selector": "<function writable_server_selector at 0x7d79b7297920>", "operation": "createIndexes", "topologyDescription": "<TopologyDescription id: 686c9a65363625d1011598fc, topology_type: Single, servers: [<ServerDescription ('************', 27017) server_type: Standalone, rtt: 0.00215936207678169>]>", "clientId": {"$oid": "686c9a65363625d1011598fc"}}
2025-07-08 13:11:17,274 - pymongo.serverSelection - DEBUG - {"message": "Server selection succeeded", "selector": "<function writable_server_selector at 0x7d79b7297920>", "operation": "createIndexes", "topologyDescription": "<TopologyDescription id: 686c9a65363625d1011598fc, topology_type: Single, servers: [<ServerDescription ('************', 27017) server_type: Standalone, rtt: 0.00215936207678169>]>", "clientId": {"$oid": "686c9a65363625d1011598fc"}, "serverHost": "************", "serverPort": 27017}
2025-07-08 13:11:17,274 - pymongo.connection - DEBUG - {"message": "Connection checkout started", "clientId": {"$oid": "686c9a65363625d1011598fc"}, "serverHost": "************", "serverPort": 27017}
2025-07-08 13:11:17,274 - pymongo.connection - DEBUG - {"message": "Connection checked out", "clientId": {"$oid": "686c9a65363625d1011598fc"}, "serverHost": "************", "serverPort": 27017, "driverConnectionId": 1, "durationMS": 0.00011715805158019066}
2025-07-08 13:11:17,274 - pymongo.command - DEBUG - {"message": "Command started", "clientId": {"$oid": "686c9a65363625d1011598fc"}, "command": "{\"createIndexes\": \"sync_logs\", \"indexes\": [{\"name\": \"timestamp_-1_operation_type_1_employee_id_1\", \"key\": {\"timestamp\": -1, \"operation_type\": 1, \"employee_id\": 1}}], \"lsid\": {\"id\": {\"$binary\": {\"base64\": \"v+NlGVsPQiaD8WQeGQtJog==\", \"subType\": \"04\"}}}, \"$db\": \"server5_logs\"}", "commandName": "createIndexes", "databaseName": "server5_logs", "requestId": 2044897763, "operationId": 2044897763, "driverConnectionId": 1, "serverConnectionId": 1739, "serverHost": "************", "serverPort": 27017}
2025-07-08 13:11:17,274 - pymongo.connection - DEBUG - {"message": "Connection ready", "clientId": {"$oid": "686c9a65363625d1011598fe"}, "serverHost": "************", "serverPort": 27017, "driverConnectionId": 1, "durationMS": 0.002128751017153263}
2025-07-08 13:11:17,274 - pymongo.connection - DEBUG - {"message": "Connection checked out", "clientId": {"$oid": "686c9a65363625d1011598fe"}, "serverHost": "************", "serverPort": 27017, "driverConnectionId": 1, "durationMS": 0.0034654290648177266}
2025-07-08 13:11:17,274 - pymongo.command - DEBUG - {"message": "Command succeeded", "clientId": {"$oid": "686c9a65363625d1011598fd"}, "durationMS": 1.411, "reply": "{\"ismaster\": true, \"topologyVersion\": {\"processId\": {\"$oid\": \"685b97d01d8e2f809d089f93\"}}, \"maxBsonObjectSize\": 16777216, \"maxMessageSizeBytes\": 48000000, \"maxWriteBatchSize\": 100000, \"localTime\": {\"$date\": \"2025-07-08T04:11:17.285Z\"}, \"logicalSessionTimeoutMinutes\": 30, \"connectionId\": 1741, \"maxWireVersion\": 25, \"ok\": 1.0}", "commandName": "ismaster", "databaseName": "admin", "requestId": 1102520059, "operationId": 1102520059, "driverConnectionId": 1, "serverConnectionId": 1741, "serverHost": "************", "serverPort": 27017}
2025-07-08 13:11:17,274 - pymongo.command - DEBUG - {"message": "Command started", "clientId": {"$oid": "686c9a65363625d1011598fe"}, "command": "{\"ismaster\": 1, \"lsid\": {\"id\": {\"$binary\": {\"base64\": \"Th3qn+vaSw+pkyNwBeWPyw==\", \"subType\": \"04\"}}}, \"$db\": \"admin\"}", "commandName": "ismaster", "databaseName": "admin", "requestId": 1967513926, "operationId": 1967513926, "driverConnectionId": 1, "serverConnectionId": 1743, "serverHost": "************", "serverPort": 27017}
2025-07-08 13:11:17,274 - pymongo.connection - DEBUG - {"message": "Connection checked in", "clientId": {"$oid": "686c9a65363625d1011598fd"}, "serverHost": "************", "serverPort": 27017, "driverConnectionId": 1}
2025-07-08 13:11:17,275 - pymongo.serverSelection - DEBUG - {"message": "Server selection started", "selector": "<function writable_server_selector at 0x7d79b7297920>", "operation": "createIndexes", "topologyDescription": "<TopologyDescription id: 686c9a65363625d1011598fd, topology_type: Single, servers: [<ServerDescription ('************', 27017) server_type: Standalone, rtt: 0.002229806035757065>]>", "clientId": {"$oid": "686c9a65363625d1011598fd"}}
2025-07-08 13:11:17,275 - pymongo.serverSelection - DEBUG - {"message": "Server selection succeeded", "selector": "<function writable_server_selector at 0x7d79b7297920>", "operation": "createIndexes", "topologyDescription": "<TopologyDescription id: 686c9a65363625d1011598fd, topology_type: Single, servers: [<ServerDescription ('************', 27017) server_type: Standalone, rtt: 0.002229806035757065>]>", "clientId": {"$oid": "686c9a65363625d1011598fd"}, "serverHost": "************", "serverPort": 27017}
2025-07-08 13:11:17,275 - pymongo.connection - DEBUG - {"message": "Connection checkout started", "clientId": {"$oid": "686c9a65363625d1011598fd"}, "serverHost": "************", "serverPort": 27017}
2025-07-08 13:11:17,275 - pymongo.connection - DEBUG - {"message": "Connection checked out", "clientId": {"$oid": "686c9a65363625d1011598fd"}, "serverHost": "************", "serverPort": 27017, "driverConnectionId": 1, "durationMS": 6.880704313516617e-05}
2025-07-08 13:11:17,275 - pymongo.command - DEBUG - {"message": "Command started", "clientId": {"$oid": "686c9a65363625d1011598fd"}, "command": "{\"createIndexes\": \"sync_logs\", \"indexes\": [{\"name\": \"timestamp_-1_operation_type_1_employee_id_1\", \"key\": {\"timestamp\": -1, \"operation_type\": 1, \"employee_id\": 1}}], \"lsid\": {\"id\": {\"$binary\": {\"base64\": \"Fo1cXB7FQviBfXK9hy9pew==\", \"subType\": \"04\"}}}, \"$db\": \"server5_logs\"}", "commandName": "createIndexes", "databaseName": "server5_logs", "requestId": 1365180540, "operationId": 1365180540, "driverConnectionId": 1, "serverConnectionId": 1741, "serverHost": "************", "serverPort": 27017}
2025-07-08 13:11:17,275 - pymongo.command - DEBUG - {"message": "Command succeeded", "clientId": {"$oid": "686c9a65363625d1011598fc"}, "durationMS": 1.346, "reply": "{\"numIndexesBefore\": 2, \"numIndexesAfter\": 2, \"note\": \"all indexes already exist\", \"ok\": 1.0}", "commandName": "createIndexes", "databaseName": "server5_logs", "requestId": 2044897763, "operationId": 2044897763, "driverConnectionId": 1, "serverConnectionId": 1739, "serverHost": "************", "serverPort": 27017}
2025-07-08 13:11:17,275 - pymongo.connection - DEBUG - {"message": "Connection checked in", "clientId": {"$oid": "686c9a65363625d1011598fc"}, "serverHost": "************", "serverPort": 27017, "driverConnectionId": 1}
2025-07-08 13:11:17,276 - pymongo.command - DEBUG - {"message": "Command succeeded", "clientId": {"$oid": "686c9a65363625d1011598fe"}, "durationMS": 1.373, "reply": "{\"ismaster\": true, \"topologyVersion\": {\"processId\": {\"$oid\": \"685b97d01d8e2f809d089f93\"}}, \"maxBsonObjectSize\": 16777216, \"maxMessageSizeBytes\": 48000000, \"maxWriteBatchSize\": 100000, \"localTime\": {\"$date\": \"2025-07-08T04:11:17.286Z\"}, \"logicalSessionTimeoutMinutes\": 30, \"connectionId\": 1743, \"maxWireVersion\": 25, \"ok\": 1.0}", "commandName": "ismaster", "databaseName": "admin", "requestId": 1967513926, "operationId": 1967513926, "driverConnectionId": 1, "serverConnectionId": 1743, "serverHost": "************", "serverPort": 27017}
2025-07-08 13:11:17,276 - pymongo.serverSelection - DEBUG - {"message": "Server selection started", "selector": "<function writable_server_selector at 0x7d79b7297920>", "operation": "createIndexes", "topologyDescription": "<TopologyDescription id: 686c9a65363625d1011598fc, topology_type: Single, servers: [<ServerDescription ('************', 27017) server_type: Standalone, rtt: 0.00215936207678169>]>", "clientId": {"$oid": "686c9a65363625d1011598fc"}}
2025-07-08 13:11:17,276 - pymongo.connection - DEBUG - {"message": "Connection checked in", "clientId": {"$oid": "686c9a65363625d1011598fe"}, "serverHost": "************", "serverPort": 27017, "driverConnectionId": 1}
2025-07-08 13:11:17,276 - pymongo.command - DEBUG - {"message": "Command succeeded", "clientId": {"$oid": "686c9a65363625d1011598fd"}, "durationMS": 0.924, "reply": "{\"numIndexesBefore\": 2, \"numIndexesAfter\": 2, \"note\": \"all indexes already exist\", \"ok\": 1.0}", "commandName": "createIndexes", "databaseName": "server5_logs", "requestId": 1365180540, "operationId": 1365180540, "driverConnectionId": 1, "serverConnectionId": 1741, "serverHost": "************", "serverPort": 27017}
2025-07-08 13:11:17,276 - pymongo.serverSelection - DEBUG - {"message": "Server selection succeeded", "selector": "<function writable_server_selector at 0x7d79b7297920>", "operation": "createIndexes", "topologyDescription": "<TopologyDescription id: 686c9a65363625d1011598fc, topology_type: Single, servers: [<ServerDescription ('************', 27017) server_type: Standalone, rtt: 0.00215936207678169>]>", "clientId": {"$oid": "686c9a65363625d1011598fc"}, "serverHost": "************", "serverPort": 27017}
2025-07-08 13:11:17,276 - pymongo.connection - DEBUG - {"message": "Connection checked in", "clientId": {"$oid": "686c9a65363625d1011598fd"}, "serverHost": "************", "serverPort": 27017, "driverConnectionId": 1}
2025-07-08 13:11:17,276 - pymongo.serverSelection - DEBUG - {"message": "Server selection started", "selector": "<function writable_server_selector at 0x7d79b7297920>", "operation": "createIndexes", "topologyDescription": "<TopologyDescription id: 686c9a65363625d1011598fe, topology_type: Single, servers: [<ServerDescription ('************', 27017) server_type: Standalone, rtt: 0.0021269849967211485>]>", "clientId": {"$oid": "686c9a65363625d1011598fe"}}
2025-07-08 13:11:17,276 - pymongo.connection - DEBUG - {"message": "Connection checkout started", "clientId": {"$oid": "686c9a65363625d1011598fc"}, "serverHost": "************", "serverPort": 27017}
2025-07-08 13:11:17,277 - pymongo.serverSelection - DEBUG - {"message": "Server selection succeeded", "selector": "<function writable_server_selector at 0x7d79b7297920>", "operation": "createIndexes", "topologyDescription": "<TopologyDescription id: 686c9a65363625d1011598fe, topology_type: Single, servers: [<ServerDescription ('************', 27017) server_type: Standalone, rtt: 0.0021269849967211485>]>", "clientId": {"$oid": "686c9a65363625d1011598fe"}, "serverHost": "************", "serverPort": 27017}
2025-07-08 13:11:17,277 - pymongo.serverSelection - DEBUG - {"message": "Server selection started", "selector": "<function writable_server_selector at 0x7d79b7297920>", "operation": "createIndexes", "topologyDescription": "<TopologyDescription id: 686c9a65363625d1011598fd, topology_type: Single, servers: [<ServerDescription ('************', 27017) server_type: Standalone, rtt: 0.002229806035757065>]>", "clientId": {"$oid": "686c9a65363625d1011598fd"}}
2025-07-08 13:11:17,277 - pymongo.connection - DEBUG - {"message": "Connection checked out", "clientId": {"$oid": "686c9a65363625d1011598fc"}, "serverHost": "************", "serverPort": 27017, "driverConnectionId": 1, "durationMS": 0.0003588560502976179}
2025-07-08 13:11:17,277 - pymongo.connection - DEBUG - {"message": "Connection checkout started", "clientId": {"$oid": "686c9a65363625d1011598fe"}, "serverHost": "************", "serverPort": 27017}
2025-07-08 13:11:17,277 - pymongo.serverSelection - DEBUG - {"message": "Server selection succeeded", "selector": "<function writable_server_selector at 0x7d79b7297920>", "operation": "createIndexes", "topologyDescription": "<TopologyDescription id: 686c9a65363625d1011598fd, topology_type: Single, servers: [<ServerDescription ('************', 27017) server_type: Standalone, rtt: 0.002229806035757065>]>", "clientId": {"$oid": "686c9a65363625d1011598fd"}, "serverHost": "************", "serverPort": 27017}
2025-07-08 13:11:17,277 - pymongo.command - DEBUG - {"message": "Command started", "clientId": {"$oid": "686c9a65363625d1011598fc"}, "command": "{\"createIndexes\": \"error_logs\", \"indexes\": [{\"name\": \"timestamp_-1_error_type_1\", \"key\": {\"timestamp\": -1, \"error_type\": 1}}], \"lsid\": {\"id\": {\"$binary\": {\"base64\": \"v+NlGVsPQiaD8WQeGQtJog==\", \"subType\": \"04\"}}}, \"$db\": \"server5_logs\"}", "commandName": "createIndexes", "databaseName": "server5_logs", "requestId": 1540383426, "operationId": 1540383426, "driverConnectionId": 1, "serverConnectionId": 1739, "serverHost": "************", "serverPort": 27017}
2025-07-08 13:11:17,277 - pymongo.connection - DEBUG - {"message": "Connection checked out", "clientId": {"$oid": "686c9a65363625d1011598fe"}, "serverHost": "************", "serverPort": 27017, "driverConnectionId": 1, "durationMS": 0.00027800409588962793}
2025-07-08 13:11:17,277 - pymongo.connection - DEBUG - {"message": "Connection checkout started", "clientId": {"$oid": "686c9a65363625d1011598fd"}, "serverHost": "************", "serverPort": 27017}
2025-07-08 13:11:17,277 - pymongo.command - DEBUG - {"message": "Command started", "clientId": {"$oid": "686c9a65363625d1011598fe"}, "command": "{\"createIndexes\": \"sync_logs\", \"indexes\": [{\"name\": \"timestamp_-1_operation_type_1_employee_id_1\", \"key\": {\"timestamp\": -1, \"operation_type\": 1, \"employee_id\": 1}}], \"lsid\": {\"id\": {\"$binary\": {\"base64\": \"Th3qn+vaSw+pkyNwBeWPyw==\", \"subType\": \"04\"}}}, \"$db\": \"server5_logs\"}", "commandName": "createIndexes", "databaseName": "server5_logs", "requestId": 304089172, "operationId": 304089172, "driverConnectionId": 1, "serverConnectionId": 1743, "serverHost": "************", "serverPort": 27017}
2025-07-08 13:11:17,278 - pymongo.connection - DEBUG - {"message": "Connection checked out", "clientId": {"$oid": "686c9a65363625d1011598fd"}, "serverHost": "************", "serverPort": 27017, "driverConnectionId": 1, "durationMS": 0.0002685539657250047}
2025-07-08 13:11:17,278 - pymongo.command - DEBUG - {"message": "Command started", "clientId": {"$oid": "686c9a65363625d1011598fd"}, "command": "{\"createIndexes\": \"error_logs\", \"indexes\": [{\"name\": \"timestamp_-1_error_type_1\", \"key\": {\"timestamp\": -1, \"error_type\": 1}}], \"lsid\": {\"id\": {\"$binary\": {\"base64\": \"Fo1cXB7FQviBfXK9hy9pew==\", \"subType\": \"04\"}}}, \"$db\": \"server5_logs\"}", "commandName": "createIndexes", "databaseName": "server5_logs", "requestId": 1303455736, "operationId": 1303455736, "driverConnectionId": 1, "serverConnectionId": 1741, "serverHost": "************", "serverPort": 27017}
2025-07-08 13:11:17,278 - pymongo.command - DEBUG - {"message": "Command succeeded", "clientId": {"$oid": "686c9a65363625d1011598fc"}, "durationMS": 1.1329999999999998, "reply": "{\"numIndexesBefore\": 2, \"numIndexesAfter\": 2, \"note\": \"all indexes already exist\", \"ok\": 1.0}", "commandName": "createIndexes", "databaseName": "server5_logs", "requestId": 1540383426, "operationId": 1540383426, "driverConnectionId": 1, "serverConnectionId": 1739, "serverHost": "************", "serverPort": 27017}
2025-07-08 13:11:17,278 - pymongo.connection - DEBUG - {"message": "Connection checked in", "clientId": {"$oid": "686c9a65363625d1011598fc"}, "serverHost": "************", "serverPort": 27017, "driverConnectionId": 1}
2025-07-08 13:11:17,278 - pymongo.serverSelection - DEBUG - {"message": "Server selection started", "selector": "<function writable_server_selector at 0x7d79b7297920>", "operation": "createIndexes", "topologyDescription": "<TopologyDescription id: 686c9a65363625d1011598fc, topology_type: Single, servers: [<ServerDescription ('************', 27017) server_type: Standalone, rtt: 0.00215936207678169>]>", "clientId": {"$oid": "686c9a65363625d1011598fc"}}
2025-07-08 13:11:17,279 - pymongo.serverSelection - DEBUG - {"message": "Server selection succeeded", "selector": "<function writable_server_selector at 0x7d79b7297920>", "operation": "createIndexes", "topologyDescription": "<TopologyDescription id: 686c9a65363625d1011598fc, topology_type: Single, servers: [<ServerDescription ('************', 27017) server_type: Standalone, rtt: 0.00215936207678169>]>", "clientId": {"$oid": "686c9a65363625d1011598fc"}, "serverHost": "************", "serverPort": 27017}
2025-07-08 13:11:17,279 - pymongo.command - DEBUG - {"message": "Command succeeded", "clientId": {"$oid": "686c9a65363625d1011598fe"}, "durationMS": 1.2249999999999999, "reply": "{\"numIndexesBefore\": 2, \"numIndexesAfter\": 2, \"note\": \"all indexes already exist\", \"ok\": 1.0}", "commandName": "createIndexes", "databaseName": "server5_logs", "requestId": 304089172, "operationId": 304089172, "driverConnectionId": 1, "serverConnectionId": 1743, "serverHost": "************", "serverPort": 27017}
2025-07-08 13:11:17,279 - pymongo.connection - DEBUG - {"message": "Connection checkout started", "clientId": {"$oid": "686c9a65363625d1011598fc"}, "serverHost": "************", "serverPort": 27017}
2025-07-08 13:11:17,279 - pymongo.connection - DEBUG - {"message": "Connection checked in", "clientId": {"$oid": "686c9a65363625d1011598fe"}, "serverHost": "************", "serverPort": 27017, "driverConnectionId": 1}
2025-07-08 13:11:17,279 - pymongo.command - DEBUG - {"message": "Command succeeded", "clientId": {"$oid": "686c9a65363625d1011598fd"}, "durationMS": 1.183, "reply": "{\"numIndexesBefore\": 2, \"numIndexesAfter\": 2, \"note\": \"all indexes already exist\", \"ok\": 1.0}", "commandName": "createIndexes", "databaseName": "server5_logs", "requestId": 1303455736, "operationId": 1303455736, "driverConnectionId": 1, "serverConnectionId": 1741, "serverHost": "************", "serverPort": 27017}
2025-07-08 13:11:17,279 - pymongo.connection - DEBUG - {"message": "Connection checked out", "clientId": {"$oid": "686c9a65363625d1011598fc"}, "serverHost": "************", "serverPort": 27017, "driverConnectionId": 1, "durationMS": 0.00022593699395656586}
2025-07-08 13:11:17,279 - pymongo.connection - DEBUG - {"message": "Connection checked in", "clientId": {"$oid": "686c9a65363625d1011598fd"}, "serverHost": "************", "serverPort": 27017, "driverConnectionId": 1}
2025-07-08 13:11:17,279 - pymongo.serverSelection - DEBUG - {"message": "Server selection started", "selector": "<function writable_server_selector at 0x7d79b7297920>", "operation": "createIndexes", "topologyDescription": "<TopologyDescription id: 686c9a65363625d1011598fe, topology_type: Single, servers: [<ServerDescription ('************', 27017) server_type: Standalone, rtt: 0.0021269849967211485>]>", "clientId": {"$oid": "686c9a65363625d1011598fe"}}
2025-07-08 13:11:17,279 - pymongo.command - DEBUG - {"message": "Command started", "clientId": {"$oid": "686c9a65363625d1011598fc"}, "command": "{\"createIndexes\": \"performance_metrics\", \"indexes\": [{\"name\": \"timestamp_-1_metric_type_1\", \"key\": {\"timestamp\": -1, \"metric_type\": 1}}], \"lsid\": {\"id\": {\"$binary\": {\"base64\": \"v+NlGVsPQiaD8WQeGQtJog==\", \"subType\": \"04\"}}}, \"$db\": \"server5_logs\"}", "commandName": "createIndexes", "databaseName": "server5_logs", "requestId": 35005211, "operationId": 35005211, "driverConnectionId": 1, "serverConnectionId": 1739, "serverHost": "************", "serverPort": 27017}
2025-07-08 13:11:17,279 - pymongo.serverSelection - DEBUG - {"message": "Server selection succeeded", "selector": "<function writable_server_selector at 0x7d79b7297920>", "operation": "createIndexes", "topologyDescription": "<TopologyDescription id: 686c9a65363625d1011598fe, topology_type: Single, servers: [<ServerDescription ('************', 27017) server_type: Standalone, rtt: 0.0021269849967211485>]>", "clientId": {"$oid": "686c9a65363625d1011598fe"}, "serverHost": "************", "serverPort": 27017}
2025-07-08 13:11:17,280 - pymongo.serverSelection - DEBUG - {"message": "Server selection started", "selector": "<function writable_server_selector at 0x7d79b7297920>", "operation": "createIndexes", "topologyDescription": "<TopologyDescription id: 686c9a65363625d1011598fd, topology_type: Single, servers: [<ServerDescription ('************', 27017) server_type: Standalone, rtt: 0.002229806035757065>]>", "clientId": {"$oid": "686c9a65363625d1011598fd"}}
2025-07-08 13:11:17,280 - pymongo.connection - DEBUG - {"message": "Connection checkout started", "clientId": {"$oid": "686c9a65363625d1011598fe"}, "serverHost": "************", "serverPort": 27017}
2025-07-08 13:11:17,280 - pymongo.serverSelection - DEBUG - {"message": "Server selection succeeded", "selector": "<function writable_server_selector at 0x7d79b7297920>", "operation": "createIndexes", "topologyDescription": "<TopologyDescription id: 686c9a65363625d1011598fd, topology_type: Single, servers: [<ServerDescription ('************', 27017) server_type: Standalone, rtt: 0.002229806035757065>]>", "clientId": {"$oid": "686c9a65363625d1011598fd"}, "serverHost": "************", "serverPort": 27017}
2025-07-08 13:11:17,280 - pymongo.connection - DEBUG - {"message": "Connection checked out", "clientId": {"$oid": "686c9a65363625d1011598fe"}, "serverHost": "************", "serverPort": 27017, "driverConnectionId": 1, "durationMS": 0.0001585381105542183}
2025-07-08 13:11:17,280 - pymongo.connection - DEBUG - {"message": "Connection checkout started", "clientId": {"$oid": "686c9a65363625d1011598fd"}, "serverHost": "************", "serverPort": 27017}
2025-07-08 13:11:17,280 - pymongo.command - DEBUG - {"message": "Command started", "clientId": {"$oid": "686c9a65363625d1011598fe"}, "command": "{\"createIndexes\": \"error_logs\", \"indexes\": [{\"name\": \"timestamp_-1_error_type_1\", \"key\": {\"timestamp\": -1, \"error_type\": 1}}], \"lsid\": {\"id\": {\"$binary\": {\"base64\": \"Th3qn+vaSw+pkyNwBeWPyw==\", \"subType\": \"04\"}}}, \"$db\": \"server5_logs\"}", "commandName": "createIndexes", "databaseName": "server5_logs", "requestId": 521595368, "operationId": 521595368, "driverConnectionId": 1, "serverConnectionId": 1743, "serverHost": "************", "serverPort": 27017}
2025-07-08 13:11:17,280 - pymongo.connection - DEBUG - {"message": "Connection checked out", "clientId": {"$oid": "686c9a65363625d1011598fd"}, "serverHost": "************", "serverPort": 27017, "driverConnectionId": 1, "durationMS": 0.0001487169647589326}
2025-07-08 13:11:17,280 - pymongo.command - DEBUG - {"message": "Command started", "clientId": {"$oid": "686c9a65363625d1011598fd"}, "command": "{\"createIndexes\": \"performance_metrics\", \"indexes\": [{\"name\": \"timestamp_-1_metric_type_1\", \"key\": {\"timestamp\": -1, \"metric_type\": 1}}], \"lsid\": {\"id\": {\"$binary\": {\"base64\": \"Fo1cXB7FQviBfXK9hy9pew==\", \"subType\": \"04\"}}}, \"$db\": \"server5_logs\"}", "commandName": "createIndexes", "databaseName": "server5_logs", "requestId": 294702567, "operationId": 294702567, "driverConnectionId": 1, "serverConnectionId": 1741, "serverHost": "************", "serverPort": 27017}
2025-07-08 13:11:17,280 - pymongo.command - DEBUG - {"message": "Command succeeded", "clientId": {"$oid": "686c9a65363625d1011598fc"}, "durationMS": 1.2349999999999999, "reply": "{\"numIndexesBefore\": 2, \"numIndexesAfter\": 2, \"note\": \"all indexes already exist\", \"ok\": 1.0}", "commandName": "createIndexes", "databaseName": "server5_logs", "requestId": 35005211, "operationId": 35005211, "driverConnectionId": 1, "serverConnectionId": 1739, "serverHost": "************", "serverPort": 27017}
2025-07-08 13:11:17,281 - pymongo.connection - DEBUG - {"message": "Connection checked in", "clientId": {"$oid": "686c9a65363625d1011598fc"}, "serverHost": "************", "serverPort": 27017, "driverConnectionId": 1}
2025-07-08 13:11:17,281 - app.database.mongodb_client - DEBUG - 📋 MongoDB索引创建完成
2025-07-08 13:11:17,281 - app.database.mongodb_client - INFO - ✅ MongoDB连接成功
2025-07-08 13:11:17,281 - pymongo.command - DEBUG - {"message": "Command succeeded", "clientId": {"$oid": "686c9a65363625d1011598fe"}, "durationMS": 1.01, "reply": "{\"numIndexesBefore\": 2, \"numIndexesAfter\": 2, \"note\": \"all indexes already exist\", \"ok\": 1.0}", "commandName": "createIndexes", "databaseName": "server5_logs", "requestId": 521595368, "operationId": 521595368, "driverConnectionId": 1, "serverConnectionId": 1743, "serverHost": "************", "serverPort": 27017}
2025-07-08 13:11:17,281 - pymongo.connection - DEBUG - {"message": "Connection checked in", "clientId": {"$oid": "686c9a65363625d1011598fe"}, "serverHost": "************", "serverPort": 27017, "driverConnectionId": 1}
2025-07-08 13:11:17,281 - pymongo.serverSelection - DEBUG - {"message": "Server selection started", "selector": "<function writable_server_selector at 0x7d79b7297920>", "operation": "createIndexes", "topologyDescription": "<TopologyDescription id: 686c9a65363625d1011598fe, topology_type: Single, servers: [<ServerDescription ('************', 27017) server_type: Standalone, rtt: 0.0021269849967211485>]>", "clientId": {"$oid": "686c9a65363625d1011598fe"}}
2025-07-08 13:11:17,281 - pymongo.serverSelection - DEBUG - {"message": "Server selection succeeded", "selector": "<function writable_server_selector at 0x7d79b7297920>", "operation": "createIndexes", "topologyDescription": "<TopologyDescription id: 686c9a65363625d1011598fe, topology_type: Single, servers: [<ServerDescription ('************', 27017) server_type: Standalone, rtt: 0.0021269849967211485>]>", "clientId": {"$oid": "686c9a65363625d1011598fe"}, "serverHost": "************", "serverPort": 27017}
2025-07-08 13:11:17,281 - pymongo.connection - DEBUG - {"message": "Connection checkout started", "clientId": {"$oid": "686c9a65363625d1011598fe"}, "serverHost": "************", "serverPort": 27017}
2025-07-08 13:11:17,282 - pymongo.connection - DEBUG - {"message": "Connection checked out", "clientId": {"$oid": "686c9a65363625d1011598fe"}, "serverHost": "************", "serverPort": 27017, "driverConnectionId": 1, "durationMS": 7.005198858678341e-05}
2025-07-08 13:11:17,282 - pymongo.command - DEBUG - {"message": "Command succeeded", "clientId": {"$oid": "686c9a65363625d1011598fd"}, "durationMS": 1.343, "reply": "{\"numIndexesBefore\": 2, \"numIndexesAfter\": 2, \"note\": \"all indexes already exist\", \"ok\": 1.0}", "commandName": "createIndexes", "databaseName": "server5_logs", "requestId": 294702567, "operationId": 294702567, "driverConnectionId": 1, "serverConnectionId": 1741, "serverHost": "************", "serverPort": 27017}
2025-07-08 13:11:17,282 - pymongo.command - DEBUG - {"message": "Command started", "clientId": {"$oid": "686c9a65363625d1011598fe"}, "command": "{\"createIndexes\": \"performance_metrics\", \"indexes\": [{\"name\": \"timestamp_-1_metric_type_1\", \"key\": {\"timestamp\": -1, \"metric_type\": 1}}], \"lsid\": {\"id\": {\"$binary\": {\"base64\": \"Th3qn+vaSw+pkyNwBeWPyw==\", \"subType\": \"04\"}}}, \"$db\": \"server5_logs\"}", "commandName": "createIndexes", "databaseName": "server5_logs", "requestId": 1726956429, "operationId": 1726956429, "driverConnectionId": 1, "serverConnectionId": 1743, "serverHost": "************", "serverPort": 27017}
2025-07-08 13:11:17,282 - pymongo.connection - DEBUG - {"message": "Connection checked in", "clientId": {"$oid": "686c9a65363625d1011598fd"}, "serverHost": "************", "serverPort": 27017, "driverConnectionId": 1}
2025-07-08 13:11:17,282 - app.database.mongodb_client - DEBUG - 📋 MongoDB索引创建完成
2025-07-08 13:11:17,282 - app.database.mongodb_client - INFO - ✅ MongoDB连接成功
2025-07-08 13:11:17,283 - pymongo.command - DEBUG - {"message": "Command succeeded", "clientId": {"$oid": "686c9a65363625d1011598fe"}, "durationMS": 0.971, "reply": "{\"numIndexesBefore\": 2, \"numIndexesAfter\": 2, \"note\": \"all indexes already exist\", \"ok\": 1.0}", "commandName": "createIndexes", "databaseName": "server5_logs", "requestId": 1726956429, "operationId": 1726956429, "driverConnectionId": 1, "serverConnectionId": 1743, "serverHost": "************", "serverPort": 27017}
2025-07-08 13:11:17,283 - pymongo.connection - DEBUG - {"message": "Connection checked in", "clientId": {"$oid": "686c9a65363625d1011598fe"}, "serverHost": "************", "serverPort": 27017, "driverConnectionId": 1}
2025-07-08 13:11:17,283 - app.database.mongodb_client - DEBUG - 📋 MongoDB索引创建完成
2025-07-08 13:11:17,283 - app.database.mongodb_client - INFO - ✅ MongoDB连接成功
2025-07-08 13:11:17,552 - app.database.postgresql_client - INFO - ✅ PostgreSQL连接池创建成功
2025-07-08 13:11:17,553 - app.database.redis_client - INFO - Redis连接成功
2025-07-08 13:11:17,553 - app.services.f3_data_puller - INFO - ✅ f3数据拉取服务启动成功，拉取间隔: 300秒
2025-07-08 13:11:17,553 - app.services.f3_data_puller - INFO - 🚀 数据拉取循环启动
2025-07-08 13:11:17,553 - app.services.f3_data_puller - INFO - --- 开始 f3 (UPSERT) 阶段 ---
2025-07-08 13:11:17,553 - app.services.f3_data_puller - INFO - --- f3 (UPSERT) 阶段完成 ---
2025-07-08 13:11:17,553 - app.services.f3_data_puller - INFO - --- 准备启动 f5 (Deletion) 阶段 ---
2025-07-08 13:11:17,553 - app.utils.server6_client - INFO - 📡 Server6客户端初始化 (新版): http://************:8009
2025-07-08 13:11:17,553 - app.services.f5_bulk_sync - INFO - 🗑️ f5删除同步服务初始化完成
2025-07-08 13:11:17,554 - pymongo.topology - DEBUG - {"message": "Starting topology monitoring", "topologyId": {"$oid": "686c9a65363625d1011598ff"}}
2025-07-08 13:11:17,554 - pymongo.topology - DEBUG - {"message": "Topology description changed", "topologyId": {"$oid": "686c9a65363625d1011598ff"}, "previousDescription": "<TopologyDescription id: 686c9a65363625d1011598ff, topology_type: Unknown, servers: []>", "newDescription": "<TopologyDescription id: 686c9a65363625d1011598ff, topology_type: Unknown, servers: [<ServerDescription ('************', 27017) server_type: Unknown, rtt: None>]>"}
2025-07-08 13:11:17,554 - pymongo.topology - DEBUG - {"message": "Starting server monitoring", "topologyId": {"$oid": "686c9a65363625d1011598ff"}, "serverHost": "************", "serverPort": 27017}
2025-07-08 13:11:17,555 - pymongo.connection - DEBUG - {"message": "Connection pool created", "clientId": {"$oid": "686c9a65363625d1011598ff"}, "serverHost": "************", "serverPort": 27017}
2025-07-08 13:11:17,555 - pymongo.serverSelection - DEBUG - {"message": "Server selection started", "selector": "Primary()", "operation": "ismaster", "topologyDescription": "<TopologyDescription id: 686c9a65363625d1011598ff, topology_type: Unknown, servers: [<ServerDescription ('************', 27017) server_type: Unknown, rtt: None>]>", "clientId": {"$oid": "686c9a65363625d1011598ff"}}
2025-07-08 13:11:17,555 - pymongo.serverSelection - DEBUG - {"message": "Waiting for suitable server to become available", "selector": "Primary()", "operation": "ismaster", "topologyDescription": "<TopologyDescription id: 686c9a65363625d1011598ff, topology_type: Unknown, servers: [<ServerDescription ('************', 27017) server_type: Unknown, rtt: None>]>", "clientId": {"$oid": "686c9a65363625d1011598ff"}, "remainingTimeMS": 4999}
2025-07-08 13:11:17,556 - app.database.redis_client - INFO - Redis连接成功
2025-07-08 13:11:17,556 - pymongo.topology - DEBUG - {"message": "Server heartbeat started", "topologyId": {"$oid": "686c9a65363625d1011598ff"}, "driverConnectionId": 1, "serverHost": "************", "serverPort": 27017, "awaited": false}
2025-07-08 13:11:17,558 - pymongo.topology - DEBUG - {"message": "Server heartbeat succeeded", "topologyId": {"$oid": "686c9a65363625d1011598ff"}, "driverConnectionId": 1, "serverConnectionId": 1744, "serverHost": "************", "serverPort": 27017, "awaited": false, "durationMS": 2.0927389850839972, "reply": "{\"helloOk\": true, \"ismaster\": true, \"topologyVersion\": {\"processId\": {\"$oid\": \"685b97d01d8e2f809d089f93\"}}, \"maxBsonObjectSize\": 16777216, \"maxMessageSizeBytes\": 48000000, \"maxWriteBatchSize\": 100000, \"localTime\": {\"$date\": \"2025-07-08T04:11:17.569Z\"}, \"logicalSessionTimeoutMinutes\": 30, \"connectionId\": 1744, \"maxWireVersion\": 25, \"ok\": 1.0}"}
2025-07-08 13:11:17,558 - pymongo.connection - DEBUG - {"message": "Connection pool ready", "clientId": {"$oid": "686c9a65363625d1011598ff"}, "serverHost": "************", "serverPort": 27017}
2025-07-08 13:11:17,558 - pymongo.topology - DEBUG - {"message": "Topology description changed", "topologyId": {"$oid": "686c9a65363625d1011598ff"}, "previousDescription": "<TopologyDescription id: 686c9a65363625d1011598ff, topology_type: Unknown, servers: [<ServerDescription ('************', 27017) server_type: Unknown, rtt: None>]>", "newDescription": "<TopologyDescription id: 686c9a65363625d1011598ff, topology_type: Single, servers: [<ServerDescription ('************', 27017) server_type: Standalone, rtt: 0.0020927389850839972>]>"}
2025-07-08 13:11:17,558 - pymongo.serverSelection - DEBUG - {"message": "Server selection succeeded", "selector": "Primary()", "operation": "ismaster", "topologyDescription": "<TopologyDescription id: 686c9a65363625d1011598ff, topology_type: Single, servers: [<ServerDescription ('************', 27017) server_type: Standalone, rtt: 0.0020927389850839972>]>", "clientId": {"$oid": "686c9a65363625d1011598ff"}, "serverHost": "************", "serverPort": 27017}
2025-07-08 13:11:17,559 - pymongo.topology - DEBUG - {"message": "Server heartbeat started", "topologyId": {"$oid": "686c9a65363625d1011598ff"}, "driverConnectionId": 1, "serverConnectionId": 1744, "serverHost": "************", "serverPort": 27017, "awaited": true}
2025-07-08 13:11:17,559 - pymongo.connection - DEBUG - {"message": "Connection checkout started", "clientId": {"$oid": "686c9a65363625d1011598ff"}, "serverHost": "************", "serverPort": 27017}
2025-07-08 13:11:17,559 - pymongo.connection - DEBUG - {"message": "Connection created", "clientId": {"$oid": "686c9a65363625d1011598ff"}, "serverHost": "************", "serverPort": 27017, "driverConnectionId": 1}
2025-07-08 13:11:17,562 - pymongo.connection - DEBUG - {"message": "Connection ready", "clientId": {"$oid": "686c9a65363625d1011598ff"}, "serverHost": "************", "serverPort": 27017, "driverConnectionId": 1, "durationMS": 0.0021087100030854344}
2025-07-08 13:11:17,562 - pymongo.connection - DEBUG - {"message": "Connection checked out", "clientId": {"$oid": "686c9a65363625d1011598ff"}, "serverHost": "************", "serverPort": 27017, "driverConnectionId": 1, "durationMS": 0.003087557968683541}
2025-07-08 13:11:17,562 - pymongo.command - DEBUG - {"message": "Command started", "clientId": {"$oid": "686c9a65363625d1011598ff"}, "command": "{\"ismaster\": 1, \"lsid\": {\"id\": {\"$binary\": {\"base64\": \"ezFZlS3zR4WLtUxw84f9hQ==\", \"subType\": \"04\"}}}, \"$db\": \"admin\"}", "commandName": "ismaster", "databaseName": "admin", "requestId": 2145174067, "operationId": 2145174067, "driverConnectionId": 1, "serverConnectionId": 1746, "serverHost": "************", "serverPort": 27017}
2025-07-08 13:11:17,563 - pymongo.command - DEBUG - {"message": "Command succeeded", "clientId": {"$oid": "686c9a65363625d1011598ff"}, "durationMS": 0.8270000000000001, "reply": "{\"ismaster\": true, \"topologyVersion\": {\"processId\": {\"$oid\": \"685b97d01d8e2f809d089f93\"}}, \"maxBsonObjectSize\": 16777216, \"maxMessageSizeBytes\": 48000000, \"maxWriteBatchSize\": 100000, \"localTime\": {\"$date\": \"2025-07-08T04:11:17.574Z\"}, \"logicalSessionTimeoutMinutes\": 30, \"connectionId\": 1746, \"maxWireVersion\": 25, \"ok\": 1.0}", "commandName": "ismaster", "databaseName": "admin", "requestId": 2145174067, "operationId": 2145174067, "driverConnectionId": 1, "serverConnectionId": 1746, "serverHost": "************", "serverPort": 27017}
2025-07-08 13:11:17,563 - pymongo.connection - DEBUG - {"message": "Connection checked in", "clientId": {"$oid": "686c9a65363625d1011598ff"}, "serverHost": "************", "serverPort": 27017, "driverConnectionId": 1}
2025-07-08 13:11:17,563 - pymongo.serverSelection - DEBUG - {"message": "Server selection started", "selector": "<function writable_server_selector at 0x7d79b7297920>", "operation": "createIndexes", "topologyDescription": "<TopologyDescription id: 686c9a65363625d1011598ff, topology_type: Single, servers: [<ServerDescription ('************', 27017) server_type: Standalone, rtt: 0.0020927389850839972>]>", "clientId": {"$oid": "686c9a65363625d1011598ff"}}
2025-07-08 13:11:17,563 - pymongo.serverSelection - DEBUG - {"message": "Server selection succeeded", "selector": "<function writable_server_selector at 0x7d79b7297920>", "operation": "createIndexes", "topologyDescription": "<TopologyDescription id: 686c9a65363625d1011598ff, topology_type: Single, servers: [<ServerDescription ('************', 27017) server_type: Standalone, rtt: 0.0020927389850839972>]>", "clientId": {"$oid": "686c9a65363625d1011598ff"}, "serverHost": "************", "serverPort": 27017}
2025-07-08 13:11:17,563 - pymongo.connection - DEBUG - {"message": "Connection checkout started", "clientId": {"$oid": "686c9a65363625d1011598ff"}, "serverHost": "************", "serverPort": 27017}
2025-07-08 13:11:17,563 - pymongo.connection - DEBUG - {"message": "Connection checked out", "clientId": {"$oid": "686c9a65363625d1011598ff"}, "serverHost": "************", "serverPort": 27017, "driverConnectionId": 1, "durationMS": 5.980802234262228e-05}
2025-07-08 13:11:17,563 - pymongo.command - DEBUG - {"message": "Command started", "clientId": {"$oid": "686c9a65363625d1011598ff"}, "command": "{\"createIndexes\": \"sync_logs\", \"indexes\": [{\"name\": \"timestamp_-1_operation_type_1_employee_id_1\", \"key\": {\"timestamp\": -1, \"operation_type\": 1, \"employee_id\": 1}}], \"lsid\": {\"id\": {\"$binary\": {\"base64\": \"ezFZlS3zR4WLtUxw84f9hQ==\", \"subType\": \"04\"}}}, \"$db\": \"server5_logs\"}", "commandName": "createIndexes", "databaseName": "server5_logs", "requestId": 468703135, "operationId": 468703135, "driverConnectionId": 1, "serverConnectionId": 1746, "serverHost": "************", "serverPort": 27017}
2025-07-08 13:11:17,565 - pymongo.command - DEBUG - {"message": "Command succeeded", "clientId": {"$oid": "686c9a65363625d1011598ff"}, "durationMS": 1.0679999999999998, "reply": "{\"numIndexesBefore\": 2, \"numIndexesAfter\": 2, \"note\": \"all indexes already exist\", \"ok\": 1.0}", "commandName": "createIndexes", "databaseName": "server5_logs", "requestId": 468703135, "operationId": 468703135, "driverConnectionId": 1, "serverConnectionId": 1746, "serverHost": "************", "serverPort": 27017}
2025-07-08 13:11:17,565 - pymongo.connection - DEBUG - {"message": "Connection checked in", "clientId": {"$oid": "686c9a65363625d1011598ff"}, "serverHost": "************", "serverPort": 27017, "driverConnectionId": 1}
2025-07-08 13:11:17,565 - pymongo.serverSelection - DEBUG - {"message": "Server selection started", "selector": "<function writable_server_selector at 0x7d79b7297920>", "operation": "createIndexes", "topologyDescription": "<TopologyDescription id: 686c9a65363625d1011598ff, topology_type: Single, servers: [<ServerDescription ('************', 27017) server_type: Standalone, rtt: 0.0020927389850839972>]>", "clientId": {"$oid": "686c9a65363625d1011598ff"}}
2025-07-08 13:11:17,565 - pymongo.serverSelection - DEBUG - {"message": "Server selection succeeded", "selector": "<function writable_server_selector at 0x7d79b7297920>", "operation": "createIndexes", "topologyDescription": "<TopologyDescription id: 686c9a65363625d1011598ff, topology_type: Single, servers: [<ServerDescription ('************', 27017) server_type: Standalone, rtt: 0.0020927389850839972>]>", "clientId": {"$oid": "686c9a65363625d1011598ff"}, "serverHost": "************", "serverPort": 27017}
2025-07-08 13:11:17,565 - pymongo.connection - DEBUG - {"message": "Connection checkout started", "clientId": {"$oid": "686c9a65363625d1011598ff"}, "serverHost": "************", "serverPort": 27017}
2025-07-08 13:11:17,565 - pymongo.connection - DEBUG - {"message": "Connection checked out", "clientId": {"$oid": "686c9a65363625d1011598ff"}, "serverHost": "************", "serverPort": 27017, "driverConnectionId": 1, "durationMS": 5.7075987569987774e-05}
2025-07-08 13:11:17,565 - pymongo.command - DEBUG - {"message": "Command started", "clientId": {"$oid": "686c9a65363625d1011598ff"}, "command": "{\"createIndexes\": \"error_logs\", \"indexes\": [{\"name\": \"timestamp_-1_error_type_1\", \"key\": {\"timestamp\": -1, \"error_type\": 1}}], \"lsid\": {\"id\": {\"$binary\": {\"base64\": \"ezFZlS3zR4WLtUxw84f9hQ==\", \"subType\": \"04\"}}}, \"$db\": \"server5_logs\"}", "commandName": "createIndexes", "databaseName": "server5_logs", "requestId": 1101513929, "operationId": 1101513929, "driverConnectionId": 1, "serverConnectionId": 1746, "serverHost": "************", "serverPort": 27017}
2025-07-08 13:11:17,566 - pymongo.command - DEBUG - {"message": "Command succeeded", "clientId": {"$oid": "686c9a65363625d1011598ff"}, "durationMS": 0.885, "reply": "{\"numIndexesBefore\": 2, \"numIndexesAfter\": 2, \"note\": \"all indexes already exist\", \"ok\": 1.0}", "commandName": "createIndexes", "databaseName": "server5_logs", "requestId": 1101513929, "operationId": 1101513929, "driverConnectionId": 1, "serverConnectionId": 1746, "serverHost": "************", "serverPort": 27017}
2025-07-08 13:11:17,566 - pymongo.connection - DEBUG - {"message": "Connection checked in", "clientId": {"$oid": "686c9a65363625d1011598ff"}, "serverHost": "************", "serverPort": 27017, "driverConnectionId": 1}
2025-07-08 13:11:17,566 - pymongo.serverSelection - DEBUG - {"message": "Server selection started", "selector": "<function writable_server_selector at 0x7d79b7297920>", "operation": "createIndexes", "topologyDescription": "<TopologyDescription id: 686c9a65363625d1011598ff, topology_type: Single, servers: [<ServerDescription ('************', 27017) server_type: Standalone, rtt: 0.0020927389850839972>]>", "clientId": {"$oid": "686c9a65363625d1011598ff"}}
2025-07-08 13:11:17,566 - pymongo.serverSelection - DEBUG - {"message": "Server selection succeeded", "selector": "<function writable_server_selector at 0x7d79b7297920>", "operation": "createIndexes", "topologyDescription": "<TopologyDescription id: 686c9a65363625d1011598ff, topology_type: Single, servers: [<ServerDescription ('************', 27017) server_type: Standalone, rtt: 0.0020927389850839972>]>", "clientId": {"$oid": "686c9a65363625d1011598ff"}, "serverHost": "************", "serverPort": 27017}
2025-07-08 13:11:17,566 - pymongo.connection - DEBUG - {"message": "Connection checkout started", "clientId": {"$oid": "686c9a65363625d1011598ff"}, "serverHost": "************", "serverPort": 27017}
2025-07-08 13:11:17,567 - pymongo.connection - DEBUG - {"message": "Connection checked out", "clientId": {"$oid": "686c9a65363625d1011598ff"}, "serverHost": "************", "serverPort": 27017, "driverConnectionId": 1, "durationMS": 5.296699237078428e-05}
2025-07-08 13:11:17,567 - pymongo.command - DEBUG - {"message": "Command started", "clientId": {"$oid": "686c9a65363625d1011598ff"}, "command": "{\"createIndexes\": \"performance_metrics\", \"indexes\": [{\"name\": \"timestamp_-1_metric_type_1\", \"key\": {\"timestamp\": -1, \"metric_type\": 1}}], \"lsid\": {\"id\": {\"$binary\": {\"base64\": \"ezFZlS3zR4WLtUxw84f9hQ==\", \"subType\": \"04\"}}}, \"$db\": \"server5_logs\"}", "commandName": "createIndexes", "databaseName": "server5_logs", "requestId": 1801979802, "operationId": 1801979802, "driverConnectionId": 1, "serverConnectionId": 1746, "serverHost": "************", "serverPort": 27017}
2025-07-08 13:11:17,567 - pymongo.command - DEBUG - {"message": "Command succeeded", "clientId": {"$oid": "686c9a65363625d1011598ff"}, "durationMS": 0.846, "reply": "{\"numIndexesBefore\": 2, \"numIndexesAfter\": 2, \"note\": \"all indexes already exist\", \"ok\": 1.0}", "commandName": "createIndexes", "databaseName": "server5_logs", "requestId": 1801979802, "operationId": 1801979802, "driverConnectionId": 1, "serverConnectionId": 1746, "serverHost": "************", "serverPort": 27017}
2025-07-08 13:11:17,568 - pymongo.connection - DEBUG - {"message": "Connection checked in", "clientId": {"$oid": "686c9a65363625d1011598ff"}, "serverHost": "************", "serverPort": 27017, "driverConnectionId": 1}
2025-07-08 13:11:17,568 - app.database.mongodb_client - DEBUG - 📋 MongoDB索引创建完成
2025-07-08 13:11:17,568 - app.database.mongodb_client - INFO - ✅ MongoDB连接成功
2025-07-08 13:11:17,629 - app.database.postgresql_client - INFO - ✅ PostgreSQL连接池创建成功
2025-07-08 13:11:17,630 - app.services.f2_push_writer - INFO - ✅ f2推送回写服务启动成功，工作线程数: 4
2025-07-08 13:11:17,630 - app.services.f2_push_writer - INFO - 🚀 推送工作线程启动: worker_1
2025-07-08 13:11:17,630 - app.services.f2_push_writer - INFO - 🚀 推送工作线程启动: worker_2
2025-07-08 13:11:17,630 - app.services.f2_push_writer - INFO - 🚀 推送工作线程启动: worker_3
2025-07-08 13:11:17,631 - app.services.f2_push_writer - INFO - 🚀 推送工作线程启动: worker_4
2025-07-08 13:11:17,675 - app.database.postgresql_client - INFO - ✅ PostgreSQL连接池创建成功
2025-07-08 13:11:17,675 - app.services.f4_operation_handler - INFO - ✅ f4操作处理服务启动成功
2025-07-08 13:11:17,727 - app.database.postgresql_client - INFO - ✅ PostgreSQL连接池创建成功
2025-07-08 13:11:17,727 - app.services.f1_listener - INFO - ✅ f1监听器服务启动成功
2025-07-08 13:11:17,729 - __main__ - INFO - ✅ f1_listener 启动成功
2025-07-08 13:11:17,729 - __main__ - INFO - ✅ f2_push_writer 启动成功
2025-07-08 13:11:17,729 - __main__ - INFO - ✅ f3_data_puller 启动成功
2025-07-08 13:11:17,729 - __main__ - INFO - ✅ f4_operation_handler 启动成功
2025-07-08 13:11:17,729 - __main__ - INFO - 🎉 所有服务已启动，按 Ctrl+C 退出
2025-07-08 13:11:17,750 - app.database.postgresql_client - DEBUG - 命令执行成功: CREATE TABLE
2025-07-08 13:11:17,751 - app.database.postgresql_client - INFO - ✅ 分区创建成功: entries_2025_07
2025-07-08 13:11:17,752 - pymongo.serverSelection - DEBUG - {"message": "Server selection started", "selector": "<function writable_server_selector at 0x7d79b7297920>", "operation": "insert", "topologyDescription": "<TopologyDescription id: 686c9a65363625d1011598fc, topology_type: Single, servers: [<ServerDescription ('************', 27017) server_type: Standalone, rtt: 0.00215936207678169>]>", "clientId": {"$oid": "686c9a65363625d1011598fc"}}
2025-07-08 13:11:17,752 - pymongo.serverSelection - DEBUG - {"message": "Server selection succeeded", "selector": "<function writable_server_selector at 0x7d79b7297920>", "operation": "insert", "topologyDescription": "<TopologyDescription id: 686c9a65363625d1011598fc, topology_type: Single, servers: [<ServerDescription ('************', 27017) server_type: Standalone, rtt: 0.00215936207678169>]>", "clientId": {"$oid": "686c9a65363625d1011598fc"}, "serverHost": "************", "serverPort": 27017}
2025-07-08 13:11:17,752 - pymongo.connection - DEBUG - {"message": "Connection checkout started", "clientId": {"$oid": "686c9a65363625d1011598fc"}, "serverHost": "************", "serverPort": 27017}
2025-07-08 13:11:17,752 - pymongo.connection - DEBUG - {"message": "Connection checked out", "clientId": {"$oid": "686c9a65363625d1011598fc"}, "serverHost": "************", "serverPort": 27017, "driverConnectionId": 1, "durationMS": 7.52320047467947e-05}
2025-07-08 13:11:17,752 - pymongo.command - DEBUG - {"message": "Command started", "clientId": {"$oid": "686c9a65363625d1011598fc"}, "command": "{\"insert\": \"sync_logs\", \"ordered\": true, \"lsid\": {\"id\": {\"$binary\": {\"base64\": \"v+NlGVsPQiaD8WQeGQtJog==\", \"subType\": \"04\"}}}, \"$db\": \"server5_logs\", \"documents\": [{\"timestamp\": {\"$date\": \"2025-07-08T04:11:17.751Z\"}, \"operation_type\": \"partition_create\", \"status\": \"success\", \"metadata\": {\"month_code\": \"202507\"}, \"_id\": {\"$oid\": \"686c9a65363625d101159900\"}}]}", "commandName": "insert", "databaseName": "server5_logs", "requestId": 1315634022, "operationId": 1315634022, "driverConnectionId": 1, "serverConnectionId": 1739, "serverHost": "************", "serverPort": 27017}
2025-07-08 13:11:17,753 - pymongo.command - DEBUG - {"message": "Command succeeded", "clientId": {"$oid": "686c9a65363625d1011598fc"}, "durationMS": 1.109, "reply": "{\"n\": 1, \"ok\": 1.0}", "commandName": "insert", "databaseName": "server5_logs", "requestId": 1315634022, "operationId": 1315634022, "driverConnectionId": 1, "serverConnectionId": 1739, "serverHost": "************", "serverPort": 27017}
2025-07-08 13:11:17,753 - pymongo.connection - DEBUG - {"message": "Connection checked in", "clientId": {"$oid": "686c9a65363625d1011598fc"}, "serverHost": "************", "serverPort": 27017, "driverConnectionId": 1}
2025-07-08 13:11:17,753 - app.database.mongodb_client - DEBUG - 📝 同步日志记录成功: partition_create
2025-07-08 13:11:17,755 - app.database.postgresql_client - DEBUG - 命令执行成功: CREATE TABLE
2025-07-08 13:11:17,756 - app.database.postgresql_client - INFO - ✅ 分区创建成功: entries_2025_08
2025-07-08 13:11:17,756 - pymongo.serverSelection - DEBUG - {"message": "Server selection started", "selector": "<function writable_server_selector at 0x7d79b7297920>", "operation": "insert", "topologyDescription": "<TopologyDescription id: 686c9a65363625d1011598fc, topology_type: Single, servers: [<ServerDescription ('************', 27017) server_type: Standalone, rtt: 0.00215936207678169>]>", "clientId": {"$oid": "686c9a65363625d1011598fc"}}
2025-07-08 13:11:17,757 - pymongo.serverSelection - DEBUG - {"message": "Server selection succeeded", "selector": "<function writable_server_selector at 0x7d79b7297920>", "operation": "insert", "topologyDescription": "<TopologyDescription id: 686c9a65363625d1011598fc, topology_type: Single, servers: [<ServerDescription ('************', 27017) server_type: Standalone, rtt: 0.00215936207678169>]>", "clientId": {"$oid": "686c9a65363625d1011598fc"}, "serverHost": "************", "serverPort": 27017}
2025-07-08 13:11:17,757 - pymongo.connection - DEBUG - {"message": "Connection checkout started", "clientId": {"$oid": "686c9a65363625d1011598fc"}, "serverHost": "************", "serverPort": 27017}
2025-07-08 13:11:17,757 - pymongo.connection - DEBUG - {"message": "Connection checked out", "clientId": {"$oid": "686c9a65363625d1011598fc"}, "serverHost": "************", "serverPort": 27017, "driverConnectionId": 1, "durationMS": 6.859400309622288e-05}
2025-07-08 13:11:17,757 - pymongo.command - DEBUG - {"message": "Command started", "clientId": {"$oid": "686c9a65363625d1011598fc"}, "command": "{\"insert\": \"sync_logs\", \"ordered\": true, \"lsid\": {\"id\": {\"$binary\": {\"base64\": \"v+NlGVsPQiaD8WQeGQtJog==\", \"subType\": \"04\"}}}, \"$db\": \"server5_logs\", \"documents\": [{\"timestamp\": {\"$date\": \"2025-07-08T04:11:17.756Z\"}, \"operation_type\": \"partition_create\", \"status\": \"success\", \"metadata\": {\"month_code\": \"202508\"}, \"_id\": {\"$oid\": \"686c9a65363625d101159901\"}}]}", "commandName": "insert", "databaseName": "server5_logs", "requestId": 635723058, "operationId": 635723058, "driverConnectionId": 1, "serverConnectionId": 1739, "serverHost": "************", "serverPort": 27017}
2025-07-08 13:11:17,758 - pymongo.command - DEBUG - {"message": "Command succeeded", "clientId": {"$oid": "686c9a65363625d1011598fc"}, "durationMS": 0.888, "reply": "{\"n\": 1, \"ok\": 1.0}", "commandName": "insert", "databaseName": "server5_logs", "requestId": 635723058, "operationId": 635723058, "driverConnectionId": 1, "serverConnectionId": 1739, "serverHost": "************", "serverPort": 27017}
2025-07-08 13:11:17,758 - pymongo.connection - DEBUG - {"message": "Connection checked in", "clientId": {"$oid": "686c9a65363625d1011598fc"}, "serverHost": "************", "serverPort": 27017, "driverConnectionId": 1}
2025-07-08 13:11:17,758 - app.database.mongodb_client - DEBUG - 📝 同步日志记录成功: partition_create
2025-07-08 13:11:17,758 - app.services.f1_listener - DEBUG - 📅 月度分区检查完成
2025-07-08 13:11:17,890 - app.database.postgresql_client - INFO - 📡 开始监听频道: push_job
2025-07-08 13:11:17,907 - app.database.postgresql_client - INFO - 📡 开始监听频道: partition_check
2025-07-08 13:11:17,908 - app.database.postgresql_client - INFO - 📡 开始监听频道: sync_trigger
2025-07-08 13:11:17,911 - app.database.postgresql_client - INFO - ✅ PostgreSQL连接池创建成功
2025-07-08 13:11:17,911 - app.services.f5_bulk_sync - INFO - ✅ f5删除同步服务已连接，准备执行任务。
2025-07-08 13:11:17,911 - app.services.f5_bulk_sync - INFO - --- 开始执行f5删除同步任务 ---
2025-07-08 13:11:17,911 - app.services.f5_bulk_sync - INFO - 删除同步范围: 从 2025-05-09 到 2025-07-08
2025-07-08 13:11:17,911 - app.utils.server6_client - INFO - AIOHTTP session for Server6 created.
2025-07-08 13:11:27,270 - pymongo.topology - DEBUG - {"message": "Server heartbeat succeeded", "topologyId": {"$oid": "686c9a65363625d1011598fc"}, "driverConnectionId": 1, "serverConnectionId": 1735, "serverHost": "************", "serverPort": 27017, "awaited": true, "durationMS": 10002.642682986334, "reply": "{\"isWritablePrimary\": true, \"topologyVersion\": {\"processId\": {\"$oid\": \"685b97d01d8e2f809d089f93\"}}, \"maxBsonObjectSize\": 16777216, \"maxMessageSizeBytes\": 48000000, \"maxWriteBatchSize\": 100000, \"localTime\": {\"$date\": \"2025-07-08T04:11:27.280Z\"}, \"logicalSessionTimeoutMinutes\": 30, \"connectionId\": 1735, \"maxWireVersion\": 25, \"ok\": 1.0}"}
2025-07-08 13:11:27,271 - pymongo.topology - DEBUG - {"message": "Server heartbeat started", "topologyId": {"$oid": "686c9a65363625d1011598fc"}, "driverConnectionId": 1, "serverConnectionId": 1735, "serverHost": "************", "serverPort": 27017, "awaited": true}
2025-07-08 13:11:27,271 - pymongo.topology - DEBUG - {"message": "Server heartbeat succeeded", "topologyId": {"$oid": "686c9a65363625d1011598fd"}, "driverConnectionId": 1, "serverConnectionId": 1736, "serverHost": "************", "serverPort": 27017, "awaited": true, "durationMS": 10001.786676002666, "reply": "{\"isWritablePrimary\": true, \"topologyVersion\": {\"processId\": {\"$oid\": \"685b97d01d8e2f809d089f93\"}}, \"maxBsonObjectSize\": 16777216, \"maxMessageSizeBytes\": 48000000, \"maxWriteBatchSize\": 100000, \"localTime\": {\"$date\": \"2025-07-08T04:11:27.282Z\"}, \"logicalSessionTimeoutMinutes\": 30, \"connectionId\": 1736, \"maxWireVersion\": 25, \"ok\": 1.0}"}
2025-07-08 13:11:27,272 - pymongo.topology - DEBUG - {"message": "Server heartbeat started", "topologyId": {"$oid": "686c9a65363625d1011598fd"}, "driverConnectionId": 1, "serverConnectionId": 1736, "serverHost": "************", "serverPort": 27017, "awaited": true}
2025-07-08 13:11:27,273 - pymongo.topology - DEBUG - {"message": "Server heartbeat succeeded", "topologyId": {"$oid": "686c9a65363625d1011598fe"}, "driverConnectionId": 1, "serverConnectionId": 1737, "serverHost": "************", "serverPort": 27017, "awaited": true, "durationMS": 10002.334301010706, "reply": "{\"isWritablePrimary\": true, \"topologyVersion\": {\"processId\": {\"$oid\": \"685b97d01d8e2f809d089f93\"}}, \"maxBsonObjectSize\": 16777216, \"maxMessageSizeBytes\": 48000000, \"maxWriteBatchSize\": 100000, \"localTime\": {\"$date\": \"2025-07-08T04:11:27.284Z\"}, \"logicalSessionTimeoutMinutes\": 30, \"connectionId\": 1737, \"maxWireVersion\": 25, \"ok\": 1.0}"}
2025-07-08 13:11:27,274 - pymongo.topology - DEBUG - {"message": "Server heartbeat started", "topologyId": {"$oid": "686c9a65363625d1011598fe"}, "driverConnectionId": 1, "serverConnectionId": 1737, "serverHost": "************", "serverPort": 27017, "awaited": true}
2025-07-08 13:11:27,561 - pymongo.topology - DEBUG - {"message": "Server heartbeat succeeded", "topologyId": {"$oid": "686c9a65363625d1011598ff"}, "driverConnectionId": 1, "serverConnectionId": 1744, "serverHost": "************", "serverPort": 27017, "awaited": true, "durationMS": 10001.780277932994, "reply": "{\"isWritablePrimary\": true, \"topologyVersion\": {\"processId\": {\"$oid\": \"685b97d01d8e2f809d089f93\"}}, \"maxBsonObjectSize\": 16777216, \"maxMessageSizeBytes\": 48000000, \"maxWriteBatchSize\": 100000, \"localTime\": {\"$date\": \"2025-07-08T04:11:27.571Z\"}, \"logicalSessionTimeoutMinutes\": 30, \"connectionId\": 1744, \"maxWireVersion\": 25, \"ok\": 1.0}"}
2025-07-08 13:11:27,561 - pymongo.topology - DEBUG - {"message": "Server heartbeat started", "topologyId": {"$oid": "686c9a65363625d1011598ff"}, "driverConnectionId": 1, "serverConnectionId": 1744, "serverHost": "************", "serverPort": 27017, "awaited": true}
2025-07-08 13:11:37,271 - pymongo.topology - DEBUG - {"message": "Server heartbeat succeeded", "topologyId": {"$oid": "686c9a65363625d1011598fc"}, "driverConnectionId": 1, "serverConnectionId": 1735, "serverHost": "************", "serverPort": 27017, "awaited": true, "durationMS": 9999.717065016739, "reply": "{\"isWritablePrimary\": true, \"topologyVersion\": {\"processId\": {\"$oid\": \"685b97d01d8e2f809d089f93\"}}, \"maxBsonObjectSize\": 16777216, \"maxMessageSizeBytes\": 48000000, \"maxWriteBatchSize\": 100000, \"localTime\": {\"$date\": \"2025-07-08T04:11:37.281Z\"}, \"logicalSessionTimeoutMinutes\": 30, \"connectionId\": 1735, \"maxWireVersion\": 25, \"ok\": 1.0}"}
2025-07-08 13:11:37,272 - pymongo.topology - DEBUG - {"message": "Server heartbeat started", "topologyId": {"$oid": "686c9a65363625d1011598fc"}, "driverConnectionId": 1, "serverConnectionId": 1735, "serverHost": "************", "serverPort": 27017, "awaited": true}
2025-07-08 13:11:37,272 - pymongo.topology - DEBUG - {"message": "Server heartbeat succeeded", "topologyId": {"$oid": "686c9a65363625d1011598fd"}, "driverConnectionId": 1, "serverConnectionId": 1736, "serverHost": "************", "serverPort": 27017, "awaited": true, "durationMS": 9999.831135966815, "reply": "{\"isWritablePrimary\": true, \"topologyVersion\": {\"processId\": {\"$oid\": \"685b97d01d8e2f809d089f93\"}}, \"maxBsonObjectSize\": 16777216, \"maxMessageSizeBytes\": 48000000, \"maxWriteBatchSize\": 100000, \"localTime\": {\"$date\": \"2025-07-08T04:11:37.283Z\"}, \"logicalSessionTimeoutMinutes\": 30, \"connectionId\": 1736, \"maxWireVersion\": 25, \"ok\": 1.0}"}
2025-07-08 13:11:37,273 - pymongo.topology - DEBUG - {"message": "Server heartbeat started", "topologyId": {"$oid": "686c9a65363625d1011598fd"}, "driverConnectionId": 1, "serverConnectionId": 1736, "serverHost": "************", "serverPort": 27017, "awaited": true}
2025-07-08 13:11:37,274 - pymongo.topology - DEBUG - {"message": "Server heartbeat succeeded", "topologyId": {"$oid": "686c9a65363625d1011598fe"}, "driverConnectionId": 1, "serverConnectionId": 1737, "serverHost": "************", "serverPort": 27017, "awaited": true, "durationMS": 9999.67894796282, "reply": "{\"isWritablePrimary\": true, \"topologyVersion\": {\"processId\": {\"$oid\": \"685b97d01d8e2f809d089f93\"}}, \"maxBsonObjectSize\": 16777216, \"maxMessageSizeBytes\": 48000000, \"maxWriteBatchSize\": 100000, \"localTime\": {\"$date\": \"2025-07-08T04:11:37.285Z\"}, \"logicalSessionTimeoutMinutes\": 30, \"connectionId\": 1737, \"maxWireVersion\": 25, \"ok\": 1.0}"}
2025-07-08 13:11:37,275 - pymongo.topology - DEBUG - {"message": "Server heartbeat started", "topologyId": {"$oid": "686c9a65363625d1011598fe"}, "driverConnectionId": 1, "serverConnectionId": 1737, "serverHost": "************", "serverPort": 27017, "awaited": true}
2025-07-08 13:11:37,562 - pymongo.topology - DEBUG - {"message": "Server heartbeat succeeded", "topologyId": {"$oid": "686c9a65363625d1011598ff"}, "driverConnectionId": 1, "serverConnectionId": 1744, "serverHost": "************", "serverPort": 27017, "awaited": true, "durationMS": 10001.035869936459, "reply": "{\"isWritablePrimary\": true, \"topologyVersion\": {\"processId\": {\"$oid\": \"685b97d01d8e2f809d089f93\"}}, \"maxBsonObjectSize\": 16777216, \"maxMessageSizeBytes\": 48000000, \"maxWriteBatchSize\": 100000, \"localTime\": {\"$date\": \"2025-07-08T04:11:37.573Z\"}, \"logicalSessionTimeoutMinutes\": 30, \"connectionId\": 1744, \"maxWireVersion\": 25, \"ok\": 1.0}"}
2025-07-08 13:11:37,562 - pymongo.topology - DEBUG - {"message": "Server heartbeat started", "topologyId": {"$oid": "686c9a65363625d1011598ff"}, "driverConnectionId": 1, "serverConnectionId": 1744, "serverHost": "************", "serverPort": 27017, "awaited": true}
2025-07-08 13:11:47,271 - pymongo.topology - DEBUG - {"message": "Server heartbeat succeeded", "topologyId": {"$oid": "686c9a65363625d1011598fc"}, "driverConnectionId": 1, "serverConnectionId": 1735, "serverHost": "************", "serverPort": 27017, "awaited": true, "durationMS": 9999.179933103733, "reply": "{\"isWritablePrimary\": true, \"topologyVersion\": {\"processId\": {\"$oid\": \"685b97d01d8e2f809d089f93\"}}, \"maxBsonObjectSize\": 16777216, \"maxMessageSizeBytes\": 48000000, \"maxWriteBatchSize\": 100000, \"localTime\": {\"$date\": \"2025-07-08T04:11:47.282Z\"}, \"logicalSessionTimeoutMinutes\": 30, \"connectionId\": 1735, \"maxWireVersion\": 25, \"ok\": 1.0}"}
2025-07-08 13:11:47,272 - pymongo.topology - DEBUG - {"message": "Server heartbeat started", "topologyId": {"$oid": "686c9a65363625d1011598fc"}, "driverConnectionId": 1, "serverConnectionId": 1735, "serverHost": "************", "serverPort": 27017, "awaited": true}
2025-07-08 13:11:47,273 - pymongo.topology - DEBUG - {"message": "Server heartbeat succeeded", "topologyId": {"$oid": "686c9a65363625d1011598fd"}, "driverConnectionId": 1, "serverConnectionId": 1736, "serverHost": "************", "serverPort": 27017, "awaited": true, "durationMS": 9999.723316985182, "reply": "{\"isWritablePrimary\": true, \"topologyVersion\": {\"processId\": {\"$oid\": \"685b97d01d8e2f809d089f93\"}}, \"maxBsonObjectSize\": 16777216, \"maxMessageSizeBytes\": 48000000, \"maxWriteBatchSize\": 100000, \"localTime\": {\"$date\": \"2025-07-08T04:11:47.284Z\"}, \"logicalSessionTimeoutMinutes\": 30, \"connectionId\": 1736, \"maxWireVersion\": 25, \"ok\": 1.0}"}
2025-07-08 13:11:47,275 - pymongo.topology - DEBUG - {"message": "Server heartbeat started", "topologyId": {"$oid": "686c9a65363625d1011598fd"}, "driverConnectionId": 1, "serverConnectionId": 1736, "serverHost": "************", "serverPort": 27017, "awaited": true}
2025-07-08 13:11:47,275 - pymongo.topology - DEBUG - {"message": "Server heartbeat succeeded", "topologyId": {"$oid": "686c9a65363625d1011598fe"}, "driverConnectionId": 1, "serverConnectionId": 1737, "serverHost": "************", "serverPort": 27017, "awaited": true, "durationMS": 9999.447650974616, "reply": "{\"isWritablePrimary\": true, \"topologyVersion\": {\"processId\": {\"$oid\": \"685b97d01d8e2f809d089f93\"}}, \"maxBsonObjectSize\": 16777216, \"maxMessageSizeBytes\": 48000000, \"maxWriteBatchSize\": 100000, \"localTime\": {\"$date\": \"2025-07-08T04:11:47.286Z\"}, \"logicalSessionTimeoutMinutes\": 30, \"connectionId\": 1737, \"maxWireVersion\": 25, \"ok\": 1.0}"}
2025-07-08 13:11:47,276 - pymongo.topology - DEBUG - {"message": "Server heartbeat started", "topologyId": {"$oid": "686c9a65363625d1011598fe"}, "driverConnectionId": 1, "serverConnectionId": 1737, "serverHost": "************", "serverPort": 27017, "awaited": true}
2025-07-08 13:11:47,562 - pymongo.topology - DEBUG - {"message": "Server heartbeat succeeded", "topologyId": {"$oid": "686c9a65363625d1011598ff"}, "driverConnectionId": 1, "serverConnectionId": 1744, "serverHost": "************", "serverPort": 27017, "awaited": true, "durationMS": 10000.026174006052, "reply": "{\"isWritablePrimary\": true, \"topologyVersion\": {\"processId\": {\"$oid\": \"685b97d01d8e2f809d089f93\"}}, \"maxBsonObjectSize\": 16777216, \"maxMessageSizeBytes\": 48000000, \"maxWriteBatchSize\": 100000, \"localTime\": {\"$date\": \"2025-07-08T04:11:47.573Z\"}, \"logicalSessionTimeoutMinutes\": 30, \"connectionId\": 1744, \"maxWireVersion\": 25, \"ok\": 1.0}"}
2025-07-08 13:11:47,563 - pymongo.topology - DEBUG - {"message": "Server heartbeat started", "topologyId": {"$oid": "686c9a65363625d1011598ff"}, "driverConnectionId": 1, "serverConnectionId": 1744, "serverHost": "************", "serverPort": 27017, "awaited": true}
2025-07-08 13:11:53,812 - app.services.f5_bulk_sync - INFO - 在MDB中找到 3872 个唯一的 external_id。
2025-07-08 13:11:53,841 - app.services.f5_bulk_sync - INFO - 在PostgreSQL中找到 3741 个唯一的 external_id。
2025-07-08 13:11:53,841 - app.services.f5_bulk_sync - INFO - ✅ 检查完成。没有发现在PostgreSQL中存在但在MDB中已被删除的记录。
2025-07-08 13:11:53,841 - app.services.f5_bulk_sync - INFO - --- f5删除同步任务完成，耗时 35.93 秒。本次共删除 0 条记录 ---
2025-07-08 13:11:53,842 - pymongo.serverSelection - DEBUG - {"message": "Server selection started", "selector": "PrimaryPreferred(tag_sets=None, max_staleness=-1, hedge=None)", "operation": "endSessions", "topologyDescription": "<TopologyDescription id: 686c9a65363625d1011598ff, topology_type: Single, servers: [<ServerDescription ('************', 27017) server_type: Standalone, rtt: 0.001643890445120633>]>", "clientId": {"$oid": "686c9a65363625d1011598ff"}}
2025-07-08 13:11:53,842 - pymongo.serverSelection - DEBUG - {"message": "Server selection succeeded", "selector": "PrimaryPreferred(tag_sets=None, max_staleness=-1, hedge=None)", "operation": "endSessions", "topologyDescription": "<TopologyDescription id: 686c9a65363625d1011598ff, topology_type: Single, servers: [<ServerDescription ('************', 27017) server_type: Standalone, rtt: 0.001643890445120633>]>", "clientId": {"$oid": "686c9a65363625d1011598ff"}, "serverHost": "************", "serverPort": 27017}
2025-07-08 13:11:53,842 - pymongo.connection - DEBUG - {"message": "Connection checkout started", "clientId": {"$oid": "686c9a65363625d1011598ff"}, "serverHost": "************", "serverPort": 27017}
2025-07-08 13:11:53,842 - pymongo.connection - DEBUG - {"message": "Connection checked out", "clientId": {"$oid": "686c9a65363625d1011598ff"}, "serverHost": "************", "serverPort": 27017, "driverConnectionId": 1, "durationMS": 7.876299787312746e-05}
2025-07-08 13:11:53,842 - pymongo.command - DEBUG - {"message": "Command started", "clientId": {"$oid": "686c9a65363625d1011598ff"}, "command": "{\"endSessions\": [{\"id\": {\"$binary\": {\"base64\": \"ezFZlS3zR4WLtUxw84f9hQ==\", \"subType\": \"04\"}}}], \"$db\": \"admin\"}", "commandName": "endSessions", "databaseName": "admin", "requestId": 1734575198, "operationId": 1734575198, "driverConnectionId": 1, "serverConnectionId": 1746, "serverHost": "************", "serverPort": 27017}
2025-07-08 13:11:53,843 - pymongo.command - DEBUG - {"message": "Command succeeded", "clientId": {"$oid": "686c9a65363625d1011598ff"}, "durationMS": 0.749, "reply": "{\"ok\": 1.0}", "commandName": "endSessions", "databaseName": "admin", "requestId": 1734575198, "operationId": 1734575198, "driverConnectionId": 1, "serverConnectionId": 1746, "serverHost": "************", "serverPort": 27017}
2025-07-08 13:11:53,843 - pymongo.connection - DEBUG - {"message": "Connection checked in", "clientId": {"$oid": "686c9a65363625d1011598ff"}, "serverHost": "************", "serverPort": 27017, "driverConnectionId": 1}
2025-07-08 13:11:53,843 - pymongo.topology - DEBUG - {"message": "Stopped server monitoring", "topologyId": {"$oid": "686c9a65363625d1011598ff"}, "serverHost": "************", "serverPort": 27017}
2025-07-08 13:11:53,843 - pymongo.connection - DEBUG - {"message": "Connection closed", "clientId": {"$oid": "686c9a65363625d1011598ff"}, "serverHost": "************", "serverPort": 27017, "driverConnectionId": 1, "reason": "Connection pool was closed", "error": "poolClosed"}
2025-07-08 13:11:53,843 - pymongo.connection - DEBUG - {"message": "Connection pool closed", "clientId": {"$oid": "686c9a65363625d1011598ff"}, "serverHost": "************", "serverPort": 27017}
2025-07-08 13:11:53,843 - pymongo.topology - DEBUG - {"message": "Topology description changed", "topologyId": {"$oid": "686c9a65363625d1011598ff"}, "previousDescription": "<TopologyDescription id: 686c9a65363625d1011598ff, topology_type: Single, servers: [<ServerDescription ('************', 27017) server_type: Standalone, rtt: 0.001643890445120633>]>", "newDescription": "<TopologyDescription id: 686c9a65363625d1011598ff, topology_type: Single, servers: [<ServerDescription ('************', 27017) server_type: Unknown, rtt: None>]>"}
2025-07-08 13:11:53,843 - pymongo.topology - DEBUG - {"message": "Stopped topology monitoring", "topologyId": {"$oid": "686c9a65363625d1011598ff"}}
2025-07-08 13:11:53,843 - app.database.mongodb_client - INFO - 🔌 MongoDB连接已关闭
2025-07-08 13:11:53,844 - app.utils.server6_client - INFO - AIOHTTP session for Server6 closed.
2025-07-08 13:11:53,844 - app.database.redis_client - INFO - 🔌 Redis连接已关闭
2025-07-08 13:11:53,871 - app.database.postgresql_client - INFO - 🔌 PostgreSQL连接已关闭
2025-07-08 13:11:53,871 - app.services.f5_bulk_sync - INFO - 🔌 f5删除同步服务已停止
2025-07-08 13:11:53,871 - app.services.f3_data_puller - INFO - --- f5 (Deletion) 阶段完成 ---
2025-07-08 13:11:57,272 - pymongo.topology - DEBUG - {"message": "Server heartbeat succeeded", "topologyId": {"$oid": "686c9a65363625d1011598fc"}, "driverConnectionId": 1, "serverConnectionId": 1735, "serverHost": "************", "serverPort": 27017, "awaited": true, "durationMS": 9998.843924026005, "reply": "{\"isWritablePrimary\": true, \"topologyVersion\": {\"processId\": {\"$oid\": \"685b97d01d8e2f809d089f93\"}}, \"maxBsonObjectSize\": 16777216, \"maxMessageSizeBytes\": 48000000, \"maxWriteBatchSize\": 100000, \"localTime\": {\"$date\": \"2025-07-08T04:11:57.282Z\"}, \"logicalSessionTimeoutMinutes\": 30, \"connectionId\": 1735, \"maxWireVersion\": 25, \"ok\": 1.0}"}
2025-07-08 13:11:57,272 - pymongo.topology - DEBUG - {"message": "Server heartbeat started", "topologyId": {"$oid": "686c9a65363625d1011598fc"}, "driverConnectionId": 1, "serverConnectionId": 1735, "serverHost": "************", "serverPort": 27017, "awaited": true}
2025-07-08 13:11:57,274 - pymongo.topology - DEBUG - {"message": "Server heartbeat succeeded", "topologyId": {"$oid": "686c9a65363625d1011598fd"}, "driverConnectionId": 1, "serverConnectionId": 1736, "serverHost": "************", "serverPort": 27017, "awaited": true, "durationMS": 9999.04967600014, "reply": "{\"isWritablePrimary\": true, \"topologyVersion\": {\"processId\": {\"$oid\": \"685b97d01d8e2f809d089f93\"}}, \"maxBsonObjectSize\": 16777216, \"maxMessageSizeBytes\": 48000000, \"maxWriteBatchSize\": 100000, \"localTime\": {\"$date\": \"2025-07-08T04:11:57.285Z\"}, \"logicalSessionTimeoutMinutes\": 30, \"connectionId\": 1736, \"maxWireVersion\": 25, \"ok\": 1.0}"}
2025-07-08 13:11:57,275 - pymongo.topology - DEBUG - {"message": "Server heartbeat started", "topologyId": {"$oid": "686c9a65363625d1011598fd"}, "driverConnectionId": 1, "serverConnectionId": 1736, "serverHost": "************", "serverPort": 27017, "awaited": true}
2025-07-08 13:11:57,275 - pymongo.topology - DEBUG - {"message": "Server heartbeat succeeded", "topologyId": {"$oid": "686c9a65363625d1011598fe"}, "driverConnectionId": 1, "serverConnectionId": 1737, "serverHost": "************", "serverPort": 27017, "awaited": true, "durationMS": 9998.459133901633, "reply": "{\"isWritablePrimary\": true, \"topologyVersion\": {\"processId\": {\"$oid\": \"685b97d01d8e2f809d089f93\"}}, \"maxBsonObjectSize\": 16777216, \"maxMessageSizeBytes\": 48000000, \"maxWriteBatchSize\": 100000, \"localTime\": {\"$date\": \"2025-07-08T04:11:57.286Z\"}, \"logicalSessionTimeoutMinutes\": 30, \"connectionId\": 1737, \"maxWireVersion\": 25, \"ok\": 1.0}"}
2025-07-08 13:11:57,275 - pymongo.topology - DEBUG - {"message": "Server heartbeat started", "topologyId": {"$oid": "686c9a65363625d1011598fe"}, "driverConnectionId": 1, "serverConnectionId": 1737, "serverHost": "************", "serverPort": 27017, "awaited": true}
2025-07-08 13:12:07,272 - pymongo.topology - DEBUG - {"message": "Server heartbeat succeeded", "topologyId": {"$oid": "686c9a65363625d1011598fc"}, "driverConnectionId": 1, "serverConnectionId": 1735, "serverHost": "************", "serverPort": 27017, "awaited": true, "durationMS": 10000.137533992529, "reply": "{\"isWritablePrimary\": true, \"topologyVersion\": {\"processId\": {\"$oid\": \"685b97d01d8e2f809d089f93\"}}, \"maxBsonObjectSize\": 16777216, \"maxMessageSizeBytes\": 48000000, \"maxWriteBatchSize\": 100000, \"localTime\": {\"$date\": \"2025-07-08T04:12:07.283Z\"}, \"logicalSessionTimeoutMinutes\": 30, \"connectionId\": 1735, \"maxWireVersion\": 25, \"ok\": 1.0}"}
2025-07-08 13:12:07,273 - pymongo.topology - DEBUG - {"message": "Server heartbeat started", "topologyId": {"$oid": "686c9a65363625d1011598fc"}, "driverConnectionId": 1, "serverConnectionId": 1735, "serverHost": "************", "serverPort": 27017, "awaited": true}
2025-07-08 13:12:07,276 - pymongo.topology - DEBUG - {"message": "Server heartbeat succeeded", "topologyId": {"$oid": "686c9a65363625d1011598fd"}, "driverConnectionId": 1, "serverConnectionId": 1736, "serverHost": "************", "serverPort": 27017, "awaited": true, "durationMS": 10001.58893794287, "reply": "{\"isWritablePrimary\": true, \"topologyVersion\": {\"processId\": {\"$oid\": \"685b97d01d8e2f809d089f93\"}}, \"maxBsonObjectSize\": 16777216, \"maxMessageSizeBytes\": 48000000, \"maxWriteBatchSize\": 100000, \"localTime\": {\"$date\": \"2025-07-08T04:12:07.287Z\"}, \"logicalSessionTimeoutMinutes\": 30, \"connectionId\": 1736, \"maxWireVersion\": 25, \"ok\": 1.0}"}
2025-07-08 13:12:07,277 - pymongo.topology - DEBUG - {"message": "Server heartbeat started", "topologyId": {"$oid": "686c9a65363625d1011598fd"}, "driverConnectionId": 1, "serverConnectionId": 1736, "serverHost": "************", "serverPort": 27017, "awaited": true}
2025-07-08 13:12:07,278 - pymongo.topology - DEBUG - {"message": "Server heartbeat succeeded", "topologyId": {"$oid": "686c9a65363625d1011598fe"}, "driverConnectionId": 1, "serverConnectionId": 1737, "serverHost": "************", "serverPort": 27017, "awaited": true, "durationMS": 10002.276981947944, "reply": "{\"isWritablePrimary\": true, \"topologyVersion\": {\"processId\": {\"$oid\": \"685b97d01d8e2f809d089f93\"}}, \"maxBsonObjectSize\": 16777216, \"maxMessageSizeBytes\": 48000000, \"maxWriteBatchSize\": 100000, \"localTime\": {\"$date\": \"2025-07-08T04:12:07.287Z\"}, \"logicalSessionTimeoutMinutes\": 30, \"connectionId\": 1737, \"maxWireVersion\": 25, \"ok\": 1.0}"}
2025-07-08 13:12:07,279 - pymongo.topology - DEBUG - {"message": "Server heartbeat started", "topologyId": {"$oid": "686c9a65363625d1011598fe"}, "driverConnectionId": 1, "serverConnectionId": 1737, "serverHost": "************", "serverPort": 27017, "awaited": true}
2025-07-08 13:12:17,274 - pymongo.topology - DEBUG - {"message": "Server heartbeat succeeded", "topologyId": {"$oid": "686c9a65363625d1011598fc"}, "driverConnectionId": 1, "serverConnectionId": 1735, "serverHost": "************", "serverPort": 27017, "awaited": true, "durationMS": 9999.8717129929, "reply": "{\"isWritablePrimary\": true, \"topologyVersion\": {\"processId\": {\"$oid\": \"685b97d01d8e2f809d089f93\"}}, \"maxBsonObjectSize\": 16777216, \"maxMessageSizeBytes\": 48000000, \"maxWriteBatchSize\": 100000, \"localTime\": {\"$date\": \"2025-07-08T04:12:17.284Z\"}, \"logicalSessionTimeoutMinutes\": 30, \"connectionId\": 1735, \"maxWireVersion\": 25, \"ok\": 1.0}"}
2025-07-08 13:12:17,275 - pymongo.topology - DEBUG - {"message": "Server heartbeat started", "topologyId": {"$oid": "686c9a65363625d1011598fc"}, "driverConnectionId": 1, "serverConnectionId": 1735, "serverHost": "************", "serverPort": 27017, "awaited": true}
2025-07-08 13:12:17,278 - pymongo.topology - DEBUG - {"message": "Server heartbeat succeeded", "topologyId": {"$oid": "686c9a65363625d1011598fe"}, "driverConnectionId": 1, "serverConnectionId": 1737, "serverHost": "************", "serverPort": 27017, "awaited": true, "durationMS": 9998.520418070257, "reply": "{\"isWritablePrimary\": true, \"topologyVersion\": {\"processId\": {\"$oid\": \"685b97d01d8e2f809d089f93\"}}, \"maxBsonObjectSize\": 16777216, \"maxMessageSizeBytes\": 48000000, \"maxWriteBatchSize\": 100000, \"localTime\": {\"$date\": \"2025-07-08T04:12:17.288Z\"}, \"logicalSessionTimeoutMinutes\": 30, \"connectionId\": 1737, \"maxWireVersion\": 25, \"ok\": 1.0}"}
2025-07-08 13:12:17,279 - pymongo.topology - DEBUG - {"message": "Server heartbeat started", "topologyId": {"$oid": "686c9a65363625d1011598fe"}, "driverConnectionId": 1, "serverConnectionId": 1737, "serverHost": "************", "serverPort": 27017, "awaited": true}
2025-07-08 13:12:17,280 - pymongo.topology - DEBUG - {"message": "Server heartbeat succeeded", "topologyId": {"$oid": "686c9a65363625d1011598fd"}, "driverConnectionId": 1, "serverConnectionId": 1736, "serverHost": "************", "serverPort": 27017, "awaited": true, "durationMS": 10001.539540011436, "reply": "{\"isWritablePrimary\": true, \"topologyVersion\": {\"processId\": {\"$oid\": \"685b97d01d8e2f809d089f93\"}}, \"maxBsonObjectSize\": 16777216, \"maxMessageSizeBytes\": 48000000, \"maxWriteBatchSize\": 100000, \"localTime\": {\"$date\": \"2025-07-08T04:12:17.288Z\"}, \"logicalSessionTimeoutMinutes\": 30, \"connectionId\": 1736, \"maxWireVersion\": 25, \"ok\": 1.0}"}
2025-07-08 13:12:17,281 - pymongo.topology - DEBUG - {"message": "Server heartbeat started", "topologyId": {"$oid": "686c9a65363625d1011598fd"}, "driverConnectionId": 1, "serverConnectionId": 1736, "serverHost": "************", "serverPort": 27017, "awaited": true}
2025-07-08 13:12:27,275 - pymongo.topology - DEBUG - {"message": "Server heartbeat succeeded", "topologyId": {"$oid": "686c9a65363625d1011598fc"}, "driverConnectionId": 1, "serverConnectionId": 1735, "serverHost": "************", "serverPort": 27017, "awaited": true, "durationMS": 9999.346647993661, "reply": "{\"isWritablePrimary\": true, \"topologyVersion\": {\"processId\": {\"$oid\": \"685b97d01d8e2f809d089f93\"}}, \"maxBsonObjectSize\": 16777216, \"maxMessageSizeBytes\": 48000000, \"maxWriteBatchSize\": 100000, \"localTime\": {\"$date\": \"2025-07-08T04:12:27.285Z\"}, \"logicalSessionTimeoutMinutes\": 30, \"connectionId\": 1735, \"maxWireVersion\": 25, \"ok\": 1.0}"}
2025-07-08 13:12:27,275 - pymongo.topology - DEBUG - {"message": "Server heartbeat started", "topologyId": {"$oid": "686c9a65363625d1011598fc"}, "driverConnectionId": 1, "serverConnectionId": 1735, "serverHost": "************", "serverPort": 27017, "awaited": true}
2025-07-08 13:12:27,278 - pymongo.topology - DEBUG - {"message": "Server heartbeat succeeded", "topologyId": {"$oid": "686c9a65363625d1011598fe"}, "driverConnectionId": 1, "serverConnectionId": 1737, "serverHost": "************", "serverPort": 27017, "awaited": true, "durationMS": 9998.4026189195, "reply": "{\"isWritablePrimary\": true, \"topologyVersion\": {\"processId\": {\"$oid\": \"685b97d01d8e2f809d089f93\"}}, \"maxBsonObjectSize\": 16777216, \"maxMessageSizeBytes\": 48000000, \"maxWriteBatchSize\": 100000, \"localTime\": {\"$date\": \"2025-07-08T04:12:27.289Z\"}, \"logicalSessionTimeoutMinutes\": 30, \"connectionId\": 1737, \"maxWireVersion\": 25, \"ok\": 1.0}"}
2025-07-08 13:12:27,278 - pymongo.topology - DEBUG - {"message": "Server heartbeat started", "topologyId": {"$oid": "686c9a65363625d1011598fe"}, "driverConnectionId": 1, "serverConnectionId": 1737, "serverHost": "************", "serverPort": 27017, "awaited": true}
2025-07-08 13:12:27,278 - pymongo.topology - DEBUG - {"message": "Server heartbeat succeeded", "topologyId": {"$oid": "686c9a65363625d1011598fd"}, "driverConnectionId": 1, "serverConnectionId": 1736, "serverHost": "************", "serverPort": 27017, "awaited": true, "durationMS": 9997.261070995592, "reply": "{\"isWritablePrimary\": true, \"topologyVersion\": {\"processId\": {\"$oid\": \"685b97d01d8e2f809d089f93\"}}, \"maxBsonObjectSize\": 16777216, \"maxMessageSizeBytes\": 48000000, \"maxWriteBatchSize\": 100000, \"localTime\": {\"$date\": \"2025-07-08T04:12:27.289Z\"}, \"logicalSessionTimeoutMinutes\": 30, \"connectionId\": 1736, \"maxWireVersion\": 25, \"ok\": 1.0}"}
2025-07-08 13:12:27,279 - pymongo.topology - DEBUG - {"message": "Server heartbeat started", "topologyId": {"$oid": "686c9a65363625d1011598fd"}, "driverConnectionId": 1, "serverConnectionId": 1736, "serverHost": "************", "serverPort": 27017, "awaited": true}
2025-07-08 13:12:37,276 - pymongo.topology - DEBUG - {"message": "Server heartbeat succeeded", "topologyId": {"$oid": "686c9a65363625d1011598fc"}, "driverConnectionId": 1, "serverConnectionId": 1735, "serverHost": "************", "serverPort": 27017, "awaited": true, "durationMS": 10000.57652394753, "reply": "{\"isWritablePrimary\": true, \"topologyVersion\": {\"processId\": {\"$oid\": \"685b97d01d8e2f809d089f93\"}}, \"maxBsonObjectSize\": 16777216, \"maxMessageSizeBytes\": 48000000, \"maxWriteBatchSize\": 100000, \"localTime\": {\"$date\": \"2025-07-08T04:12:37.286Z\"}, \"logicalSessionTimeoutMinutes\": 30, \"connectionId\": 1735, \"maxWireVersion\": 25, \"ok\": 1.0}"}
2025-07-08 13:12:37,277 - pymongo.topology - DEBUG - {"message": "Server heartbeat started", "topologyId": {"$oid": "686c9a65363625d1011598fc"}, "driverConnectionId": 1, "serverConnectionId": 1735, "serverHost": "************", "serverPort": 27017, "awaited": true}
2025-07-08 13:12:37,279 - pymongo.topology - DEBUG - {"message": "Server heartbeat succeeded", "topologyId": {"$oid": "686c9a65363625d1011598fe"}, "driverConnectionId": 1, "serverConnectionId": 1737, "serverHost": "************", "serverPort": 27017, "awaited": true, "durationMS": 10000.172938918695, "reply": "{\"isWritablePrimary\": true, \"topologyVersion\": {\"processId\": {\"$oid\": \"685b97d01d8e2f809d089f93\"}}, \"maxBsonObjectSize\": 16777216, \"maxMessageSizeBytes\": 48000000, \"maxWriteBatchSize\": 100000, \"localTime\": {\"$date\": \"2025-07-08T04:12:37.289Z\"}, \"logicalSessionTimeoutMinutes\": 30, \"connectionId\": 1737, \"maxWireVersion\": 25, \"ok\": 1.0}"}
2025-07-08 13:12:37,280 - pymongo.topology - DEBUG - {"message": "Server heartbeat started", "topologyId": {"$oid": "686c9a65363625d1011598fe"}, "driverConnectionId": 1, "serverConnectionId": 1737, "serverHost": "************", "serverPort": 27017, "awaited": true}
2025-07-08 13:12:37,280 - pymongo.topology - DEBUG - {"message": "Server heartbeat succeeded", "topologyId": {"$oid": "686c9a65363625d1011598fd"}, "driverConnectionId": 1, "serverConnectionId": 1736, "serverHost": "************", "serverPort": 27017, "awaited": true, "durationMS": 10001.570052001625, "reply": "{\"isWritablePrimary\": true, \"topologyVersion\": {\"processId\": {\"$oid\": \"685b97d01d8e2f809d089f93\"}}, \"maxBsonObjectSize\": 16777216, \"maxMessageSizeBytes\": 48000000, \"maxWriteBatchSize\": 100000, \"localTime\": {\"$date\": \"2025-07-08T04:12:37.289Z\"}, \"logicalSessionTimeoutMinutes\": 30, \"connectionId\": 1736, \"maxWireVersion\": 25, \"ok\": 1.0}"}
2025-07-08 13:12:37,281 - pymongo.topology - DEBUG - {"message": "Server heartbeat started", "topologyId": {"$oid": "686c9a65363625d1011598fd"}, "driverConnectionId": 1, "serverConnectionId": 1736, "serverHost": "************", "serverPort": 27017, "awaited": true}
2025-07-08 13:12:47,277 - pymongo.topology - DEBUG - {"message": "Server heartbeat succeeded", "topologyId": {"$oid": "686c9a65363625d1011598fc"}, "driverConnectionId": 1, "serverConnectionId": 1735, "serverHost": "************", "serverPort": 27017, "awaited": true, "durationMS": 9999.767575995065, "reply": "{\"isWritablePrimary\": true, \"topologyVersion\": {\"processId\": {\"$oid\": \"685b97d01d8e2f809d089f93\"}}, \"maxBsonObjectSize\": 16777216, \"maxMessageSizeBytes\": 48000000, \"maxWriteBatchSize\": 100000, \"localTime\": {\"$date\": \"2025-07-08T04:12:47.287Z\"}, \"logicalSessionTimeoutMinutes\": 30, \"connectionId\": 1735, \"maxWireVersion\": 25, \"ok\": 1.0}"}
2025-07-08 13:12:47,278 - pymongo.topology - DEBUG - {"message": "Server heartbeat started", "topologyId": {"$oid": "686c9a65363625d1011598fc"}, "driverConnectionId": 1, "serverConnectionId": 1735, "serverHost": "************", "serverPort": 27017, "awaited": true}
2025-07-08 13:12:47,280 - pymongo.topology - DEBUG - {"message": "Server heartbeat succeeded", "topologyId": {"$oid": "686c9a65363625d1011598fd"}, "driverConnectionId": 1, "serverConnectionId": 1736, "serverHost": "************", "serverPort": 27017, "awaited": true, "durationMS": 9997.8884489974, "reply": "{\"isWritablePrimary\": true, \"topologyVersion\": {\"processId\": {\"$oid\": \"685b97d01d8e2f809d089f93\"}}, \"maxBsonObjectSize\": 16777216, \"maxMessageSizeBytes\": 48000000, \"maxWriteBatchSize\": 100000, \"localTime\": {\"$date\": \"2025-07-08T04:12:47.290Z\"}, \"logicalSessionTimeoutMinutes\": 30, \"connectionId\": 1736, \"maxWireVersion\": 25, \"ok\": 1.0}"}
2025-07-08 13:12:47,280 - pymongo.topology - DEBUG - {"message": "Server heartbeat started", "topologyId": {"$oid": "686c9a65363625d1011598fd"}, "driverConnectionId": 1, "serverConnectionId": 1736, "serverHost": "************", "serverPort": 27017, "awaited": true}
2025-07-08 13:12:47,281 - pymongo.topology - DEBUG - {"message": "Server heartbeat succeeded", "topologyId": {"$oid": "686c9a65363625d1011598fe"}, "driverConnectionId": 1, "serverConnectionId": 1737, "serverHost": "************", "serverPort": 27017, "awaited": true, "durationMS": 10000.783119001426, "reply": "{\"isWritablePrimary\": true, \"topologyVersion\": {\"processId\": {\"$oid\": \"685b97d01d8e2f809d089f93\"}}, \"maxBsonObjectSize\": 16777216, \"maxMessageSizeBytes\": 48000000, \"maxWriteBatchSize\": 100000, \"localTime\": {\"$date\": \"2025-07-08T04:12:47.290Z\"}, \"logicalSessionTimeoutMinutes\": 30, \"connectionId\": 1737, \"maxWireVersion\": 25, \"ok\": 1.0}"}
2025-07-08 13:12:47,282 - pymongo.topology - DEBUG - {"message": "Server heartbeat started", "topologyId": {"$oid": "686c9a65363625d1011598fe"}, "driverConnectionId": 1, "serverConnectionId": 1737, "serverHost": "************", "serverPort": 27017, "awaited": true}
2025-07-08 13:12:57,276 - pymongo.topology - DEBUG - {"message": "Server heartbeat succeeded", "topologyId": {"$oid": "686c9a65363625d1011598fc"}, "driverConnectionId": 1, "serverConnectionId": 1735, "serverHost": "************", "serverPort": 27017, "awaited": true, "durationMS": 9998.100787983276, "reply": "{\"isWritablePrimary\": true, \"topologyVersion\": {\"processId\": {\"$oid\": \"685b97d01d8e2f809d089f93\"}}, \"maxBsonObjectSize\": 16777216, \"maxMessageSizeBytes\": 48000000, \"maxWriteBatchSize\": 100000, \"localTime\": {\"$date\": \"2025-07-08T04:12:57.287Z\"}, \"logicalSessionTimeoutMinutes\": 30, \"connectionId\": 1735, \"maxWireVersion\": 25, \"ok\": 1.0}"}
2025-07-08 13:12:57,277 - pymongo.topology - DEBUG - {"message": "Server heartbeat started", "topologyId": {"$oid": "686c9a65363625d1011598fc"}, "driverConnectionId": 1, "serverConnectionId": 1735, "serverHost": "************", "serverPort": 27017, "awaited": true}
2025-07-08 13:12:57,279 - pymongo.topology - DEBUG - {"message": "Server heartbeat succeeded", "topologyId": {"$oid": "686c9a65363625d1011598fd"}, "driverConnectionId": 1, "serverConnectionId": 1736, "serverHost": "************", "serverPort": 27017, "awaited": true, "durationMS": 9998.6333809793, "reply": "{\"isWritablePrimary\": true, \"topologyVersion\": {\"processId\": {\"$oid\": \"685b97d01d8e2f809d089f93\"}}, \"maxBsonObjectSize\": 16777216, \"maxMessageSizeBytes\": 48000000, \"maxWriteBatchSize\": 100000, \"localTime\": {\"$date\": \"2025-07-08T04:12:57.290Z\"}, \"logicalSessionTimeoutMinutes\": 30, \"connectionId\": 1736, \"maxWireVersion\": 25, \"ok\": 1.0}"}
2025-07-08 13:12:57,280 - pymongo.topology - DEBUG - {"message": "Server heartbeat started", "topologyId": {"$oid": "686c9a65363625d1011598fd"}, "driverConnectionId": 1, "serverConnectionId": 1736, "serverHost": "************", "serverPort": 27017, "awaited": true}
2025-07-08 13:12:57,281 - pymongo.topology - DEBUG - {"message": "Server heartbeat succeeded", "topologyId": {"$oid": "686c9a65363625d1011598fe"}, "driverConnectionId": 1, "serverConnectionId": 1737, "serverHost": "************", "serverPort": 27017, "awaited": true, "durationMS": 9998.551400960423, "reply": "{\"isWritablePrimary\": true, \"topologyVersion\": {\"processId\": {\"$oid\": \"685b97d01d8e2f809d089f93\"}}, \"maxBsonObjectSize\": 16777216, \"maxMessageSizeBytes\": 48000000, \"maxWriteBatchSize\": 100000, \"localTime\": {\"$date\": \"2025-07-08T04:12:57.290Z\"}, \"logicalSessionTimeoutMinutes\": 30, \"connectionId\": 1737, \"maxWireVersion\": 25, \"ok\": 1.0}"}
2025-07-08 13:12:57,281 - pymongo.topology - DEBUG - {"message": "Server heartbeat started", "topologyId": {"$oid": "686c9a65363625d1011598fe"}, "driverConnectionId": 1, "serverConnectionId": 1737, "serverHost": "************", "serverPort": 27017, "awaited": true}
2025-07-08 13:13:05,842 - pymongo.serverSelection - DEBUG - {"message": "Server selection started", "selector": "PrimaryPreferred(tag_sets=None, max_staleness=-1, hedge=None)", "operation": "endSessions", "topologyDescription": "<TopologyDescription id: 686c9a65363625d1011598fc, topology_type: Single, servers: [<ServerDescription ('************', 27017) server_type: Standalone, rtt: 0.0013914925893097046>]>", "clientId": {"$oid": "686c9a65363625d1011598fc"}}
2025-07-08 13:13:05,843 - pymongo.serverSelection - DEBUG - {"message": "Server selection succeeded", "selector": "PrimaryPreferred(tag_sets=None, max_staleness=-1, hedge=None)", "operation": "endSessions", "topologyDescription": "<TopologyDescription id: 686c9a65363625d1011598fc, topology_type: Single, servers: [<ServerDescription ('************', 27017) server_type: Standalone, rtt: 0.0013914925893097046>]>", "clientId": {"$oid": "686c9a65363625d1011598fc"}, "serverHost": "************", "serverPort": 27017}
2025-07-08 13:13:05,843 - pymongo.connection - DEBUG - {"message": "Connection checkout started", "clientId": {"$oid": "686c9a65363625d1011598fc"}, "serverHost": "************", "serverPort": 27017}
2025-07-08 13:13:05,844 - pymongo.connection - DEBUG - {"message": "Connection checked out", "clientId": {"$oid": "686c9a65363625d1011598fc"}, "serverHost": "************", "serverPort": 27017, "driverConnectionId": 1, "durationMS": 0.0004187190206721425}
2025-07-08 13:13:05,844 - pymongo.command - DEBUG - {"message": "Command started", "clientId": {"$oid": "686c9a65363625d1011598fc"}, "command": "{\"endSessions\": [{\"id\": {\"$binary\": {\"base64\": \"v+NlGVsPQiaD8WQeGQtJog==\", \"subType\": \"04\"}}}], \"$db\": \"admin\"}", "commandName": "endSessions", "databaseName": "admin", "requestId": 1100661313, "operationId": 1100661313, "driverConnectionId": 1, "serverConnectionId": 1739, "serverHost": "************", "serverPort": 27017}
2025-07-08 13:13:05,846 - pymongo.command - DEBUG - {"message": "Command succeeded", "clientId": {"$oid": "686c9a65363625d1011598fc"}, "durationMS": 1.577, "reply": "{\"ok\": 1.0}", "commandName": "endSessions", "databaseName": "admin", "requestId": 1100661313, "operationId": 1100661313, "driverConnectionId": 1, "serverConnectionId": 1739, "serverHost": "************", "serverPort": 27017}
2025-07-08 13:13:05,846 - pymongo.connection - DEBUG - {"message": "Connection checked in", "clientId": {"$oid": "686c9a65363625d1011598fc"}, "serverHost": "************", "serverPort": 27017, "driverConnectionId": 1}
2025-07-08 13:13:05,847 - pymongo.topology - DEBUG - {"message": "Stopped server monitoring", "topologyId": {"$oid": "686c9a65363625d1011598fc"}, "serverHost": "************", "serverPort": 27017}
2025-07-08 13:13:05,847 - pymongo.connection - DEBUG - {"message": "Connection closed", "clientId": {"$oid": "686c9a65363625d1011598fc"}, "serverHost": "************", "serverPort": 27017, "driverConnectionId": 1, "reason": "Connection pool was closed", "error": "poolClosed"}
2025-07-08 13:13:05,847 - pymongo.connection - DEBUG - {"message": "Connection pool closed", "clientId": {"$oid": "686c9a65363625d1011598fc"}, "serverHost": "************", "serverPort": 27017}
2025-07-08 13:13:05,848 - pymongo.topology - DEBUG - {"message": "Topology description changed", "topologyId": {"$oid": "686c9a65363625d1011598fc"}, "previousDescription": "<TopologyDescription id: 686c9a65363625d1011598fc, topology_type: Single, servers: [<ServerDescription ('************', 27017) server_type: Standalone, rtt: 0.0013914925893097046>]>", "newDescription": "<TopologyDescription id: 686c9a65363625d1011598fc, topology_type: Single, servers: [<ServerDescription ('************', 27017) server_type: Unknown, rtt: None>]>"}
2025-07-08 13:13:05,848 - pymongo.topology - DEBUG - {"message": "Stopped topology monitoring", "topologyId": {"$oid": "686c9a65363625d1011598fc"}}
2025-07-08 13:13:05,848 - app.database.mongodb_client - INFO - 🔌 MongoDB连接已关闭
2025-07-08 13:13:05,849 - pymongo.serverSelection - DEBUG - {"message": "Server selection started", "selector": "PrimaryPreferred(tag_sets=None, max_staleness=-1, hedge=None)", "operation": "endSessions", "topologyDescription": "<TopologyDescription id: 686c9a65363625d1011598fe, topology_type: Single, servers: [<ServerDescription ('************', 27017) server_type: Standalone, rtt: 0.001261956351104725>]>", "clientId": {"$oid": "686c9a65363625d1011598fe"}}
2025-07-08 13:13:05,849 - pymongo.serverSelection - DEBUG - {"message": "Server selection succeeded", "selector": "PrimaryPreferred(tag_sets=None, max_staleness=-1, hedge=None)", "operation": "endSessions", "topologyDescription": "<TopologyDescription id: 686c9a65363625d1011598fe, topology_type: Single, servers: [<ServerDescription ('************', 27017) server_type: Standalone, rtt: 0.001261956351104725>]>", "clientId": {"$oid": "686c9a65363625d1011598fe"}, "serverHost": "************", "serverPort": 27017}
2025-07-08 13:13:05,849 - pymongo.connection - DEBUG - {"message": "Connection checkout started", "clientId": {"$oid": "686c9a65363625d1011598fe"}, "serverHost": "************", "serverPort": 27017}
2025-07-08 13:13:05,849 - pymongo.connection - DEBUG - {"message": "Connection checked out", "clientId": {"$oid": "686c9a65363625d1011598fe"}, "serverHost": "************", "serverPort": 27017, "driverConnectionId": 1, "durationMS": 6.583996582776308e-05}
2025-07-08 13:13:05,849 - pymongo.command - DEBUG - {"message": "Command started", "clientId": {"$oid": "686c9a65363625d1011598fe"}, "command": "{\"endSessions\": [{\"id\": {\"$binary\": {\"base64\": \"Th3qn+vaSw+pkyNwBeWPyw==\", \"subType\": \"04\"}}}], \"$db\": \"admin\"}", "commandName": "endSessions", "databaseName": "admin", "requestId": 1433925857, "operationId": 1433925857, "driverConnectionId": 1, "serverConnectionId": 1743, "serverHost": "************", "serverPort": 27017}
2025-07-08 13:13:05,850 - pymongo.command - DEBUG - {"message": "Command succeeded", "clientId": {"$oid": "686c9a65363625d1011598fe"}, "durationMS": 0.8130000000000001, "reply": "{\"ok\": 1.0}", "commandName": "endSessions", "databaseName": "admin", "requestId": 1433925857, "operationId": 1433925857, "driverConnectionId": 1, "serverConnectionId": 1743, "serverHost": "************", "serverPort": 27017}
2025-07-08 13:13:05,850 - pymongo.connection - DEBUG - {"message": "Connection checked in", "clientId": {"$oid": "686c9a65363625d1011598fe"}, "serverHost": "************", "serverPort": 27017, "driverConnectionId": 1}
2025-07-08 13:13:05,850 - pymongo.topology - DEBUG - {"message": "Stopped server monitoring", "topologyId": {"$oid": "686c9a65363625d1011598fe"}, "serverHost": "************", "serverPort": 27017}
2025-07-08 13:13:05,850 - pymongo.connection - DEBUG - {"message": "Connection closed", "clientId": {"$oid": "686c9a65363625d1011598fe"}, "serverHost": "************", "serverPort": 27017, "driverConnectionId": 1, "reason": "Connection pool was closed", "error": "poolClosed"}
2025-07-08 13:13:05,850 - pymongo.connection - DEBUG - {"message": "Connection pool closed", "clientId": {"$oid": "686c9a65363625d1011598fe"}, "serverHost": "************", "serverPort": 27017}
2025-07-08 13:13:05,850 - pymongo.topology - DEBUG - {"message": "Topology description changed", "topologyId": {"$oid": "686c9a65363625d1011598fe"}, "previousDescription": "<TopologyDescription id: 686c9a65363625d1011598fe, topology_type: Single, servers: [<ServerDescription ('************', 27017) server_type: Standalone, rtt: 0.001261956351104725>]>", "newDescription": "<TopologyDescription id: 686c9a65363625d1011598fe, topology_type: Single, servers: [<ServerDescription ('************', 27017) server_type: Unknown, rtt: None>]>"}
2025-07-08 13:13:05,850 - pymongo.topology - DEBUG - {"message": "Stopped topology monitoring", "topologyId": {"$oid": "686c9a65363625d1011598fe"}}
2025-07-08 13:13:05,850 - app.database.mongodb_client - INFO - 🔌 MongoDB连接已关闭
2025-07-08 13:13:05,850 - app.database.postgresql_client - INFO - 监听任务被取消
2025-07-08 13:13:05,851 - app.database.redis_client - INFO - 🔌 Redis连接已关闭
2025-07-08 13:13:05,851 - app.database.redis_client - INFO - 🔌 Redis连接已关闭
2025-07-08 13:13:05,852 - app.services.f2_push_writer - INFO - 🔌 推送工作线程停止: worker_1
2025-07-08 13:13:05,852 - app.services.f2_push_writer - INFO - 🔌 推送工作线程停止: worker_4
2025-07-08 13:13:05,852 - app.services.f2_push_writer - INFO - 🔌 推送工作线程停止: worker_3
2025-07-08 13:13:05,852 - app.services.f2_push_writer - INFO - 🔌 推送工作线程停止: worker_2
2025-07-08 13:13:05,852 - pymongo.serverSelection - DEBUG - {"message": "Server selection started", "selector": "PrimaryPreferred(tag_sets=None, max_staleness=-1, hedge=None)", "operation": "endSessions", "topologyDescription": "<TopologyDescription id: 686c9a65363625d1011598fd, topology_type: Single, servers: [<ServerDescription ('************', 27017) server_type: Standalone, rtt: 0.0012911503528356795>]>", "clientId": {"$oid": "686c9a65363625d1011598fd"}}
2025-07-08 13:13:05,852 - pymongo.serverSelection - DEBUG - {"message": "Server selection succeeded", "selector": "PrimaryPreferred(tag_sets=None, max_staleness=-1, hedge=None)", "operation": "endSessions", "topologyDescription": "<TopologyDescription id: 686c9a65363625d1011598fd, topology_type: Single, servers: [<ServerDescription ('************', 27017) server_type: Standalone, rtt: 0.0012911503528356795>]>", "clientId": {"$oid": "686c9a65363625d1011598fd"}, "serverHost": "************", "serverPort": 27017}
2025-07-08 13:13:05,852 - pymongo.connection - DEBUG - {"message": "Connection checkout started", "clientId": {"$oid": "686c9a65363625d1011598fd"}, "serverHost": "************", "serverPort": 27017}
2025-07-08 13:13:05,852 - pymongo.connection - DEBUG - {"message": "Connection checked out", "clientId": {"$oid": "686c9a65363625d1011598fd"}, "serverHost": "************", "serverPort": 27017, "driverConnectionId": 1, "durationMS": 6.153597496449947e-05}
2025-07-08 13:13:05,853 - pymongo.command - DEBUG - {"message": "Command started", "clientId": {"$oid": "686c9a65363625d1011598fd"}, "command": "{\"endSessions\": [{\"id\": {\"$binary\": {\"base64\": \"Fo1cXB7FQviBfXK9hy9pew==\", \"subType\": \"04\"}}}], \"$db\": \"admin\"}", "commandName": "endSessions", "databaseName": "admin", "requestId": 1141616124, "operationId": 1141616124, "driverConnectionId": 1, "serverConnectionId": 1741, "serverHost": "************", "serverPort": 27017}
2025-07-08 13:13:05,853 - pymongo.command - DEBUG - {"message": "Command succeeded", "clientId": {"$oid": "686c9a65363625d1011598fd"}, "durationMS": 0.703, "reply": "{\"ok\": 1.0}", "commandName": "endSessions", "databaseName": "admin", "requestId": 1141616124, "operationId": 1141616124, "driverConnectionId": 1, "serverConnectionId": 1741, "serverHost": "************", "serverPort": 27017}
2025-07-08 13:13:05,853 - pymongo.connection - DEBUG - {"message": "Connection checked in", "clientId": {"$oid": "686c9a65363625d1011598fd"}, "serverHost": "************", "serverPort": 27017, "driverConnectionId": 1}
2025-07-08 13:13:05,853 - pymongo.topology - DEBUG - {"message": "Stopped server monitoring", "topologyId": {"$oid": "686c9a65363625d1011598fd"}, "serverHost": "************", "serverPort": 27017}
2025-07-08 13:13:05,853 - pymongo.connection - DEBUG - {"message": "Connection closed", "clientId": {"$oid": "686c9a65363625d1011598fd"}, "serverHost": "************", "serverPort": 27017, "driverConnectionId": 1, "reason": "Connection pool was closed", "error": "poolClosed"}
2025-07-08 13:13:05,854 - pymongo.connection - DEBUG - {"message": "Connection pool closed", "clientId": {"$oid": "686c9a65363625d1011598fd"}, "serverHost": "************", "serverPort": 27017}
2025-07-08 13:13:05,854 - pymongo.topology - DEBUG - {"message": "Topology description changed", "topologyId": {"$oid": "686c9a65363625d1011598fd"}, "previousDescription": "<TopologyDescription id: 686c9a65363625d1011598fd, topology_type: Single, servers: [<ServerDescription ('************', 27017) server_type: Standalone, rtt: 0.0012911503528356795>]>", "newDescription": "<TopologyDescription id: 686c9a65363625d1011598fd, topology_type: Single, servers: [<ServerDescription ('************', 27017) server_type: Unknown, rtt: None>]>"}
2025-07-08 13:13:05,854 - pymongo.topology - DEBUG - {"message": "Stopped topology monitoring", "topologyId": {"$oid": "686c9a65363625d1011598fd"}}
2025-07-08 13:13:05,854 - app.database.mongodb_client - INFO - 🔌 MongoDB连接已关闭
2025-07-08 13:13:05,854 - app.database.redis_client - INFO - 🔌 Redis连接已关闭
2025-07-08 13:13:05,855 - app.database.postgresql_client - INFO - 🔌 PostgreSQL连接已关闭
2025-07-08 13:13:05,855 - app.database.postgresql_client - INFO - 🔌 PostgreSQL连接已关闭
2025-07-08 13:13:05,855 - app.services.f1_listener - INFO - 🔌 f1监听器服务已停止
2025-07-08 13:13:05,855 - app.services.f4_operation_handler - INFO - 🔌 f4操作处理服务已停止
2025-07-08 13:13:05,856 - app.database.postgresql_client - INFO - 🔌 PostgreSQL连接已关闭
2025-07-08 13:13:05,856 - app.database.redis_client - INFO - 🔌 Redis连接已关闭
2025-07-08 13:13:05,856 - app.services.f3_data_puller - INFO - 🔌 f3数据拉取服务已停止
2025-07-08 13:13:05,861 - app.database.postgresql_client - INFO - 监听连接已关闭
2025-07-08 13:13:05,862 - app.database.postgresql_client - INFO - 🔌 PostgreSQL连接已关闭
2025-07-08 13:13:05,862 - app.services.f2_push_writer - INFO - 🔌 f2推送回写服务已停止
2025-07-08 17:00:56,313 - ERROR - Server5服务启动失败: cannot import name 'F3_PULL_SCHEDULE' from 'config.config' (unknown location)
2025-07-08 17:00:56,313 - root - ERROR - Server5服务启动失败: cannot import name 'F3_PULL_SCHEDULE' from 'config.config' (unknown location)
2025-07-09 09:41:38,854 - ERROR - Server5服务启动失败: cannot import name 'F3_PULL_SCHEDULE' from 'config.config' (unknown location)
2025-07-09 09:41:38,854 - root - ERROR - Server5服务启动失败: cannot import name 'F3_PULL_SCHEDULE' from 'config.config' (unknown location)
2025-07-09 09:45:30,591 - ERROR - Server5服务启动失败: cannot import name 'F3_PULL_SCHEDULE' from 'config.config' (unknown location)
2025-07-09 09:45:30,591 - root - ERROR - Server5服务启动失败: cannot import name 'F3_PULL_SCHEDULE' from 'config.config' (unknown location)
