2025-07-15 11:06:55,317 - INFO - 🔊 f1监听器服务初始化完成
2025-07-15 11:06:55,317 - INFO - 📡 Server6客户端初始化 (新版): http://192.168.3.93:8019
2025-07-15 11:06:55,317 - INFO - 📤 f2推送回写引擎初始化完成 (修复版本: 直接轮询PostgreSQL)
2025-07-15 11:06:55,317 - INFO - 📡 Server6客户端初始化 (新版): http://192.168.3.93:8019
2025-07-15 11:06:55,318 - INFO - 📥 f3数据拉取器初始化完成 (Staging & UPSERT 模式)
2025-07-15 11:06:55,318 - INFO - 🎮 f4操作处理器初始化完成
2025-07-15 11:06:55,322 - INFO - Redis连接成功
2025-07-15 11:06:55,331 - INFO - ✅ MongoDB连接成功
2025-07-15 11:06:55,569 - INFO - ✅ PostgreSQL连接池创建成功
2025-07-15 11:06:55,569 - INFO - ✅ f1监听器服务启动成功
2025-07-15 11:06:55,573 - INFO - Redis连接成功
2025-07-15 11:06:55,580 - INFO - ✅ 分区创建成功: entries_2025_07
2025-07-15 11:06:55,583 - INFO - ✅ MongoDB连接成功
2025-07-15 11:06:55,583 - INFO - ✅ 分区创建成功: entries_2025_08
2025-07-15 11:06:55,691 - INFO - 📡 开始监听频道: push_job
2025-07-15 11:06:55,692 - INFO - 📡 开始监听频道: partition_check
2025-07-15 11:06:55,694 - INFO - 📡 开始监听频道: sync_trigger
2025-07-15 11:07:00,717 - INFO - 监听任务被取消
2025-07-15 11:07:00,726 - INFO - 监听连接已关闭
2025-07-15 11:07:00,727 - ERROR - Server5服务启动失败: asyncio.run() cannot be called from a running event loop
2025-07-15 11:07:01,082 - INFO - 🔌 MongoDB连接已关闭
2025-07-15 11:07:01,083 - INFO - 🔌 MongoDB连接已关闭
2025-07-15 11:07:01,084 - INFO - 🔌 f4操作处理服务已停止
2025-07-15 11:07:01,084 - INFO - 🔌 f1监听器服务已停止
2025-07-15 11:07:01,084 - INFO - 🔌 f2推送回写服务已停止
