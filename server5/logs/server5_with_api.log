2025-07-09 10:25:33,520 - INFO - 🔊 f1监听器服务初始化完成
2025-07-09 10:25:33,520 - INFO - 📡 Server6客户端初始化 (新版): http://192.168.3.93:8009
2025-07-09 10:25:33,521 - INFO - 📤 f2推送回写引擎初始化完成 (修复版本: 直接轮询PostgreSQL)
2025-07-09 10:25:33,521 - INFO - 📡 Server6客户端初始化 (新版): http://192.168.3.93:8009
2025-07-09 10:25:33,522 - INFO - 📥 f3数据拉取器初始化完成 (Staging & UPSERT 模式)
2025-07-09 10:25:33,522 - INFO - 🎮 f4操作处理器初始化完成
2025-07-09 10:25:33,532 - INFO - Redis连接成功
2025-07-09 10:25:33,532 - INFO - Redis连接成功
2025-07-09 10:25:33,532 - INFO - Redis连接成功
2025-07-09 10:25:33,539 - INFO - ✅ MongoDB连接成功
2025-07-09 10:25:33,541 - INFO - ✅ MongoDB连接成功
2025-07-09 10:25:33,541 - INFO - ✅ MongoDB连接成功
2025-07-09 10:25:33,821 - INFO - ✅ PostgreSQL连接池创建成功
2025-07-09 10:25:33,822 - INFO - Redis连接成功
2025-07-09 10:25:33,822 - INFO - ✅ f3数据拉取服务启动成功，预定执行时间: ['02:00', '03:00']
2025-07-09 10:25:33,822 - INFO - 🚀 数据拉取调度器启动，预定运行时间: ['02:00', '03:00']
2025-07-09 10:25:33,822 - INFO - 下一次f3/f5同步任务将在 2025-07-10 02:00:00 执行，等待 56066 秒...
2025-07-09 10:25:33,867 - INFO - ✅ PostgreSQL连接池创建成功
2025-07-09 10:25:33,867 - INFO - ✅ f1监听器服务启动成功
2025-07-09 10:25:33,871 - INFO - ✅ PostgreSQL连接池创建成功
2025-07-09 10:25:33,872 - INFO - ✅ f2推送回写服务启动成功，工作线程数: 4
2025-07-09 10:25:33,872 - INFO - 🚀 推送工作线程启动: worker_1
2025-07-09 10:25:33,872 - INFO - 🚀 推送工作线程启动: worker_2
2025-07-09 10:25:33,872 - INFO - 🚀 推送工作线程启动: worker_3
2025-07-09 10:25:33,873 - INFO - 🚀 推送工作线程启动: worker_4
2025-07-09 10:25:33,876 - INFO - ✅ 分区创建成功: entries_2025_07
2025-07-09 10:25:33,879 - INFO - ✅ 分区创建成功: entries_2025_08
2025-07-09 10:25:33,911 - INFO - ✅ PostgreSQL连接池创建成功
2025-07-09 10:25:33,911 - INFO - ✅ f4操作处理服务启动成功
2025-07-09 10:25:33,912 - INFO - 🔌 推送工作线程停止: worker_4
2025-07-09 10:25:33,912 - INFO - 🔌 推送工作线程停止: worker_3
2025-07-09 10:25:33,914 - INFO - 🔌 推送工作线程停止: worker_2
2025-07-09 10:25:33,914 - INFO - 🔌 推送工作线程停止: worker_1
2025-07-09 10:52:29,384 - INFO - 🔊 f1监听器服务初始化完成
2025-07-09 10:52:29,384 - INFO - 📡 Server6客户端初始化 (新版): http://192.168.3.93:8009
2025-07-09 10:52:29,384 - INFO - 📤 f2推送回写引擎初始化完成 (修复版本: 直接轮询PostgreSQL)
2025-07-09 10:52:29,384 - INFO - 📡 Server6客户端初始化 (新版): http://192.168.3.93:8009
2025-07-09 10:52:29,386 - INFO - 📥 f3数据拉取器初始化完成 (Staging & UPSERT 模式)
2025-07-09 10:52:29,386 - INFO - 🎮 f4操作处理器初始化完成
2025-07-09 10:52:29,396 - INFO - Redis连接成功
2025-07-09 10:52:29,396 - INFO - Redis连接成功
2025-07-09 10:52:29,396 - INFO - Redis连接成功
2025-07-09 10:52:29,402 - INFO - ✅ MongoDB连接成功
2025-07-09 10:52:29,403 - INFO - ✅ MongoDB连接成功
2025-07-09 10:52:29,404 - INFO - ✅ MongoDB连接成功
2025-07-09 10:52:29,653 - INFO - ✅ PostgreSQL连接池创建成功
2025-07-09 10:52:29,654 - INFO - Redis连接成功
2025-07-09 10:52:29,654 - INFO - ✅ f3数据拉取服务启动成功，预定执行时间: ['02:00', '03:00']
2025-07-09 10:52:29,654 - INFO - 🚀 数据拉取调度器启动，预定运行时间: ['02:00', '03:00']
2025-07-09 10:52:29,654 - INFO - 下一次f3/f5同步任务将在 2025-07-10 02:00:00 执行，等待 54450 秒...
2025-07-09 10:52:29,698 - INFO - ✅ PostgreSQL连接池创建成功
2025-07-09 10:52:29,698 - INFO - ✅ f1监听器服务启动成功
2025-07-09 10:52:29,721 - INFO - ✅ 分区创建成功: entries_2025_07
2025-07-09 10:52:29,741 - INFO - ✅ 分区创建成功: entries_2025_08
2025-07-09 10:52:29,745 - INFO - ✅ PostgreSQL连接池创建成功
2025-07-09 10:52:29,746 - INFO - ✅ f2推送回写服务启动成功，工作线程数: 4
2025-07-09 10:52:29,746 - INFO - 🚀 推送工作线程启动: worker_1
2025-07-09 10:52:29,746 - INFO - 🚀 推送工作线程启动: worker_2
2025-07-09 10:52:29,746 - INFO - 🚀 推送工作线程启动: worker_3
2025-07-09 10:52:29,746 - INFO - 🚀 推送工作线程启动: worker_4
2025-07-09 10:52:29,824 - INFO - 📡 开始监听频道: push_job
2025-07-09 10:52:29,826 - INFO - 📡 开始监听频道: partition_check
2025-07-09 10:52:29,827 - INFO - 📡 开始监听频道: sync_trigger
2025-07-09 10:52:29,868 - INFO - ✅ PostgreSQL连接池创建成功
2025-07-09 10:52:29,868 - INFO - ✅ f4操作处理服务启动成功
2025-07-09 10:52:29,868 - INFO - 🔌 推送工作线程停止: worker_4
2025-07-09 10:52:29,868 - INFO - 监听任务被取消
2025-07-09 10:52:29,868 - INFO - 🔌 推送工作线程停止: worker_3
2025-07-09 10:52:29,870 - INFO - 🔌 推送工作线程停止: worker_2
2025-07-09 10:52:29,870 - INFO - 🔌 推送工作线程停止: worker_1
2025-07-09 10:52:29,876 - INFO - 监听连接已关闭
2025-07-09 11:13:09,742 - INFO - 🔊 f1监听器服务初始化完成
2025-07-09 11:13:09,742 - INFO - 📡 Server6客户端初始化 (新版): http://192.168.3.93:8009
2025-07-09 11:13:09,743 - INFO - 📤 f2推送回写引擎初始化完成 (修复版本: 直接轮询PostgreSQL)
2025-07-09 11:13:09,743 - INFO - 📡 Server6客户端初始化 (新版): http://192.168.3.93:8009
2025-07-09 11:13:09,747 - INFO - 📥 f3数据拉取器初始化完成 (Staging & UPSERT 模式)
2025-07-09 11:13:09,747 - INFO - 🎮 f4操作处理器初始化完成
2025-07-09 11:13:09,756 - INFO - Redis连接成功
2025-07-09 11:13:09,756 - INFO - Redis连接成功
2025-07-09 11:13:09,756 - INFO - Redis连接成功
2025-07-09 11:13:09,763 - INFO - ✅ MongoDB连接成功
2025-07-09 11:13:09,763 - INFO - ✅ MongoDB连接成功
2025-07-09 11:13:09,765 - INFO - ✅ MongoDB连接成功
2025-07-09 11:13:10,057 - INFO - ✅ PostgreSQL连接池创建成功
2025-07-09 11:13:10,058 - INFO - Redis连接成功
2025-07-09 11:13:10,058 - INFO - ✅ f3数据拉取服务启动成功，预定执行时间: ['02:00', '03:00']
2025-07-09 11:13:10,058 - INFO - 🚀 数据拉取调度器启动，预定运行时间: ['02:00', '03:00']
2025-07-09 11:13:10,058 - INFO - 下一次f3/f5同步任务将在 2025-07-10 02:00:00 执行，等待 53210 秒...
2025-07-09 11:13:10,103 - INFO - ✅ PostgreSQL连接池创建成功
2025-07-09 11:13:10,103 - INFO - ✅ f1监听器服务启动成功
2025-07-09 11:13:10,118 - INFO - ✅ 分区创建成功: entries_2025_07
2025-07-09 11:13:10,122 - INFO - ✅ 分区创建成功: entries_2025_08
2025-07-09 11:13:10,170 - INFO - ✅ PostgreSQL连接池创建成功
2025-07-09 11:13:10,170 - INFO - ✅ f2推送回写服务启动成功，工作线程数: 4
2025-07-09 11:13:10,170 - INFO - 🚀 推送工作线程启动: worker_1
2025-07-09 11:13:10,170 - INFO - 🚀 推送工作线程启动: worker_2
2025-07-09 11:13:10,170 - INFO - 🚀 推送工作线程启动: worker_3
2025-07-09 11:13:10,171 - INFO - 🚀 推送工作线程启动: worker_4
2025-07-09 11:13:10,218 - INFO - ✅ PostgreSQL连接池创建成功
2025-07-09 11:13:10,218 - INFO - ✅ f4操作处理服务启动成功
2025-07-09 11:13:10,218 - INFO - 🔌 推送工作线程停止: worker_3
2025-07-09 11:13:10,218 - INFO - 🔌 推送工作线程停止: worker_4
2025-07-09 11:13:10,220 - INFO - 🔌 推送工作线程停止: worker_1
2025-07-09 11:13:10,220 - INFO - 🔌 推送工作线程停止: worker_2
2025-07-09 11:24:55,438 - INFO - 🔊 f1监听器服务初始化完成
2025-07-09 11:24:55,438 - INFO - 📡 Server6客户端初始化 (新版): http://192.168.3.93:8009
2025-07-09 11:24:55,438 - INFO - 📤 f2推送回写引擎初始化完成 (修复版本: 直接轮询PostgreSQL)
2025-07-09 11:24:55,438 - INFO - 📡 Server6客户端初始化 (新版): http://192.168.3.93:8009
2025-07-09 11:24:55,440 - INFO - 📥 f3数据拉取器初始化完成 (Staging & UPSERT 模式)
2025-07-09 11:24:55,440 - INFO - 🎮 f4操作处理器初始化完成
2025-07-09 11:24:55,454 - INFO - Redis连接成功
2025-07-09 11:24:55,454 - INFO - Redis连接成功
2025-07-09 11:24:55,454 - INFO - Redis连接成功
2025-07-09 11:24:55,458 - INFO - ✅ MongoDB连接成功
2025-07-09 11:24:55,460 - INFO - ✅ MongoDB连接成功
2025-07-09 11:24:55,462 - INFO - ✅ MongoDB连接成功
2025-07-09 11:24:55,755 - INFO - ✅ PostgreSQL连接池创建成功
2025-07-09 11:24:55,757 - INFO - Redis连接成功
2025-07-09 11:24:55,757 - INFO - ✅ f3数据拉取服务启动成功，预定执行时间: ['02:00', '03:00']
2025-07-09 11:24:55,757 - INFO - 🚀 数据拉取调度器启动，预定运行时间: ['02:00', '03:00']
2025-07-09 11:24:55,757 - INFO - 下一次f3/f5同步任务将在 2025-07-10 02:00:00 执行，等待 52504 秒...
2025-07-09 11:24:55,806 - INFO - ✅ PostgreSQL连接池创建成功
2025-07-09 11:24:55,807 - INFO - ✅ f1监听器服务启动成功
2025-07-09 11:24:55,827 - INFO - ✅ PostgreSQL连接池创建成功
2025-07-09 11:24:55,828 - INFO - ✅ f2推送回写服务启动成功，工作线程数: 4
2025-07-09 11:24:55,828 - INFO - 🚀 推送工作线程启动: worker_1
2025-07-09 11:24:55,828 - INFO - 🚀 推送工作线程启动: worker_2
2025-07-09 11:24:55,828 - INFO - 🚀 推送工作线程启动: worker_3
2025-07-09 11:24:55,829 - INFO - 🚀 推送工作线程启动: worker_4
2025-07-09 11:24:55,829 - INFO - ✅ 分区创建成功: entries_2025_07
2025-07-09 11:24:55,832 - INFO - ✅ PostgreSQL连接池创建成功
2025-07-09 11:24:55,832 - INFO - ✅ f4操作处理服务启动成功
2025-07-09 11:24:55,833 - INFO - 🔌 推送工作线程停止: worker_3
2025-07-09 11:24:55,833 - INFO - 🔌 推送工作线程停止: worker_4
2025-07-09 11:24:56,031 - INFO - 🔌 推送工作线程停止: worker_1
2025-07-09 11:24:56,044 - INFO - 🔌 推送工作线程停止: worker_2
2025-07-09 11:26:51,166 - INFO - 🔊 f1监听器服务初始化完成
2025-07-09 11:26:51,166 - INFO - 📡 Server6客户端初始化 (新版): http://192.168.3.93:8009
2025-07-09 11:26:51,166 - INFO - 📤 f2推送回写引擎初始化完成 (修复版本: 直接轮询PostgreSQL)
2025-07-09 11:26:51,166 - INFO - 📡 Server6客户端初始化 (新版): http://192.168.3.93:8009
2025-07-09 11:26:51,167 - INFO - 📥 f3数据拉取器初始化完成 (Staging & UPSERT 模式)
2025-07-09 11:26:51,167 - INFO - 🎮 f4操作处理器初始化完成
2025-07-09 11:26:51,177 - INFO - Redis连接成功
2025-07-09 11:26:51,177 - INFO - Redis连接成功
2025-07-09 11:26:51,177 - INFO - Redis连接成功
2025-07-09 11:26:51,183 - INFO - ✅ MongoDB连接成功
2025-07-09 11:26:51,184 - INFO - ✅ MongoDB连接成功
2025-07-09 11:26:51,184 - INFO - ✅ MongoDB连接成功
2025-07-09 11:26:51,438 - INFO - ✅ PostgreSQL连接池创建成功
2025-07-09 11:26:51,438 - INFO - Redis连接成功
2025-07-09 11:26:51,438 - INFO - ✅ f3数据拉取服务启动成功，预定执行时间: ['02:00', '03:00']
2025-07-09 11:26:51,439 - INFO - 🚀 数据拉取调度器启动，预定运行时间: ['02:00', '03:00']
2025-07-09 11:26:51,439 - INFO - 下一次f3/f5同步任务将在 2025-07-10 02:00:00 执行，等待 52389 秒...
2025-07-09 11:26:51,552 - INFO - ✅ PostgreSQL连接池创建成功
2025-07-09 11:26:51,552 - INFO - ✅ f1监听器服务启动成功
2025-07-09 11:26:51,558 - INFO - ✅ PostgreSQL连接池创建成功
2025-07-09 11:26:51,558 - INFO - ✅ f2推送回写服务启动成功，工作线程数: 4
2025-07-09 11:26:51,558 - INFO - 🚀 推送工作线程启动: worker_1
2025-07-09 11:26:51,558 - INFO - 🚀 推送工作线程启动: worker_2
2025-07-09 11:26:51,558 - INFO - 🚀 推送工作线程启动: worker_3
2025-07-09 11:26:51,559 - INFO - 🚀 推送工作线程启动: worker_4
2025-07-09 11:26:51,562 - INFO - ✅ 分区创建成功: entries_2025_07
2025-07-09 11:26:51,566 - INFO - ✅ 分区创建成功: entries_2025_08
2025-07-09 11:26:51,591 - INFO - ✅ PostgreSQL连接池创建成功
2025-07-09 11:26:51,591 - INFO - ✅ f4操作处理服务启动成功
2025-07-09 11:26:51,592 - INFO - 🔌 推送工作线程停止: worker_4
2025-07-09 11:26:51,592 - INFO - 🔌 推送工作线程停止: worker_3
2025-07-09 11:26:51,594 - INFO - 🔌 推送工作线程停止: worker_1
2025-07-09 11:26:51,594 - INFO - 🔌 推送工作线程停止: worker_2
2025-07-09 11:41:23,556 - INFO - 🔊 f1监听器服务初始化完成
2025-07-09 11:41:23,556 - INFO - 📡 Server6客户端初始化 (新版): http://192.168.3.93:8009
2025-07-09 11:41:23,556 - INFO - 📤 f2推送回写引擎初始化完成 (修复版本: 直接轮询PostgreSQL)
2025-07-09 11:41:23,556 - INFO - 📡 Server6客户端初始化 (新版): http://192.168.3.93:8009
2025-07-09 11:41:23,557 - INFO - 📥 f3数据拉取器初始化完成 (Staging & UPSERT 模式)
2025-07-09 11:41:23,557 - INFO - 🎮 f4操作处理器初始化完成
2025-07-09 11:41:23,567 - INFO - Redis连接成功
2025-07-09 11:41:23,567 - INFO - Redis连接成功
2025-07-09 11:41:23,567 - INFO - Redis连接成功
2025-07-09 11:41:23,573 - INFO - ✅ MongoDB连接成功
2025-07-09 11:41:23,574 - INFO - ✅ MongoDB连接成功
2025-07-09 11:41:23,575 - INFO - ✅ MongoDB连接成功
2025-07-09 11:41:23,852 - INFO - ✅ PostgreSQL连接池创建成功
2025-07-09 11:41:23,853 - INFO - Redis连接成功
2025-07-09 11:41:23,853 - INFO - ✅ f3数据拉取服务启动成功，预定执行时间: ['02:00', '03:00']
2025-07-09 11:41:23,854 - INFO - 🚀 数据拉取调度器启动，预定运行时间: ['02:00', '03:00']
2025-07-09 11:41:23,854 - INFO - 下一次f3/f5同步任务将在 2025-07-10 02:00:00 执行，等待 51516 秒...
2025-07-09 11:41:23,941 - INFO - ✅ PostgreSQL连接池创建成功
2025-07-09 11:41:23,941 - INFO - ✅ f1监听器服务启动成功
2025-07-09 11:41:23,946 - INFO - ✅ PostgreSQL连接池创建成功
2025-07-09 11:41:23,947 - INFO - ✅ f2推送回写服务启动成功，工作线程数: 4
2025-07-09 11:41:23,947 - INFO - 🚀 推送工作线程启动: worker_1
2025-07-09 11:41:23,947 - INFO - 🚀 推送工作线程启动: worker_2
2025-07-09 11:41:23,947 - INFO - 🚀 推送工作线程启动: worker_3
2025-07-09 11:41:23,948 - INFO - 🚀 推送工作线程启动: worker_4
2025-07-09 11:41:23,951 - INFO - ✅ 分区创建成功: entries_2025_07
2025-07-09 11:41:23,955 - INFO - ✅ 分区创建成功: entries_2025_08
2025-07-09 11:41:24,029 - INFO - ✅ PostgreSQL连接池创建成功
2025-07-09 11:41:24,029 - INFO - ✅ f4操作处理服务启动成功
2025-07-09 11:41:24,030 - INFO - 🔌 推送工作线程停止: worker_4
2025-07-09 11:41:24,030 - INFO - 🔌 推送工作线程停止: worker_3
2025-07-09 11:41:24,032 - INFO - 🔌 推送工作线程停止: worker_2
2025-07-09 11:41:24,032 - INFO - 🔌 推送工作线程停止: worker_1
2025-07-09 11:43:04,394 - INFO - 🔊 f1监听器服务初始化完成
2025-07-09 11:43:04,394 - INFO - 📡 Server6客户端初始化 (新版): http://192.168.3.93:8009
2025-07-09 11:43:04,394 - INFO - 📤 f2推送回写引擎初始化完成 (修复版本: 直接轮询PostgreSQL)
2025-07-09 11:43:04,394 - INFO - 📡 Server6客户端初始化 (新版): http://192.168.3.93:8009
2025-07-09 11:43:04,395 - INFO - 📥 f3数据拉取器初始化完成 (Staging & UPSERT 模式)
2025-07-09 11:43:04,395 - INFO - 🎮 f4操作处理器初始化完成
2025-07-09 11:43:04,405 - INFO - Redis连接成功
2025-07-09 11:43:04,405 - INFO - Redis连接成功
2025-07-09 11:43:04,405 - INFO - Redis连接成功
2025-07-09 11:43:04,412 - INFO - ✅ MongoDB连接成功
2025-07-09 11:43:04,413 - INFO - ✅ MongoDB连接成功
2025-07-09 11:43:04,414 - INFO - ✅ MongoDB连接成功
2025-07-09 11:43:04,698 - INFO - ✅ PostgreSQL连接池创建成功
2025-07-09 11:43:04,728 - INFO - Redis连接成功
2025-07-09 11:43:04,728 - INFO - ✅ f3数据拉取服务启动成功，预定执行时间: ['02:00', '03:00']
2025-07-09 11:43:04,728 - INFO - 🚀 数据拉取调度器启动，预定运行时间: ['02:00', '03:00']
2025-07-09 11:43:04,728 - INFO - 下一次f3/f5同步任务将在 2025-07-10 02:00:00 执行，等待 51415 秒...
2025-07-09 11:43:04,732 - INFO - ✅ PostgreSQL连接池创建成功
2025-07-09 11:43:04,733 - INFO - ✅ f1监听器服务启动成功
2025-07-09 11:43:04,763 - INFO - ✅ 分区创建成功: entries_2025_07
2025-07-09 11:43:04,767 - INFO - ✅ 分区创建成功: entries_2025_08
2025-07-09 11:43:04,768 - INFO - ✅ PostgreSQL连接池创建成功
2025-07-09 11:43:04,768 - INFO - ✅ f2推送回写服务启动成功，工作线程数: 4
2025-07-09 11:43:04,768 - INFO - 🚀 推送工作线程启动: worker_1
2025-07-09 11:43:04,768 - INFO - 🚀 推送工作线程启动: worker_2
2025-07-09 11:43:04,768 - INFO - 🚀 推送工作线程启动: worker_3
2025-07-09 11:43:04,769 - INFO - 🚀 推送工作线程启动: worker_4
2025-07-09 11:43:04,796 - INFO - ✅ PostgreSQL连接池创建成功
2025-07-09 11:43:04,796 - INFO - ✅ f4操作处理服务启动成功
2025-07-09 11:43:04,797 - INFO - 🔌 推送工作线程停止: worker_3
2025-07-09 11:43:04,797 - INFO - 🔌 推送工作线程停止: worker_4
2025-07-09 11:43:04,799 - INFO - 🔌 推送工作线程停止: worker_1
2025-07-09 11:43:04,799 - INFO - 🔌 推送工作线程停止: worker_2
2025-07-09 11:49:24,967 - INFO - 🔊 f1监听器服务初始化完成
2025-07-09 11:49:24,968 - INFO - 📡 Server6客户端初始化 (新版): http://192.168.3.93:8009
2025-07-09 11:49:24,968 - INFO - 📤 f2推送回写引擎初始化完成 (修复版本: 直接轮询PostgreSQL)
2025-07-09 11:49:24,968 - INFO - 📡 Server6客户端初始化 (新版): http://192.168.3.93:8009
2025-07-09 11:49:24,972 - INFO - 📥 f3数据拉取器初始化完成 (Staging & UPSERT 模式)
2025-07-09 11:49:24,972 - INFO - 🎮 f4操作处理器初始化完成
2025-07-09 11:49:24,982 - INFO - Redis连接成功
2025-07-09 11:49:24,982 - INFO - Redis连接成功
2025-07-09 11:49:24,982 - INFO - Redis连接成功
2025-07-09 11:49:24,990 - INFO - ✅ MongoDB连接成功
2025-07-09 11:49:24,990 - INFO - ✅ MongoDB连接成功
2025-07-09 11:49:24,992 - INFO - ✅ MongoDB连接成功
2025-07-09 11:49:25,241 - INFO - ✅ PostgreSQL连接池创建成功
2025-07-09 11:49:25,241 - INFO - Redis连接成功
2025-07-09 11:49:25,241 - INFO - ✅ f3数据拉取服务启动成功，预定执行时间: ['02:00', '03:00']
2025-07-09 11:49:25,242 - INFO - 🚀 数据拉取调度器启动，预定运行时间: ['02:00', '03:00']
2025-07-09 11:49:25,242 - INFO - 下一次f3/f5同步任务将在 2025-07-10 02:00:00 执行，等待 51035 秒...
2025-07-09 11:49:25,309 - INFO - ✅ PostgreSQL连接池创建成功
2025-07-09 11:49:25,309 - INFO - ✅ f1监听器服务启动成功
2025-07-09 11:49:25,318 - INFO - ✅ 分区创建成功: entries_2025_07
2025-07-09 11:49:25,322 - INFO - ✅ 分区创建成功: entries_2025_08
2025-07-09 11:49:25,346 - INFO - ✅ PostgreSQL连接池创建成功
2025-07-09 11:49:25,346 - INFO - ✅ f2推送回写服务启动成功，工作线程数: 4
2025-07-09 11:49:25,346 - INFO - 🚀 推送工作线程启动: worker_1
2025-07-09 11:49:25,346 - INFO - 🚀 推送工作线程启动: worker_2
2025-07-09 11:49:25,347 - INFO - 🚀 推送工作线程启动: worker_3
2025-07-09 11:49:25,347 - INFO - 🚀 推送工作线程启动: worker_4
2025-07-09 11:49:25,384 - INFO - ✅ PostgreSQL连接池创建成功
2025-07-09 11:49:25,384 - INFO - ✅ f4操作处理服务启动成功
2025-07-09 11:49:25,385 - INFO - 🔌 推送工作线程停止: worker_3
2025-07-09 11:49:25,385 - INFO - 🔌 推送工作线程停止: worker_4
2025-07-09 11:49:25,386 - INFO - 🔌 推送工作线程停止: worker_2
2025-07-09 11:49:25,386 - INFO - 🔌 推送工作线程停止: worker_1
2025-07-09 12:03:13,113 - INFO - 🔊 f1监听器服务初始化完成
2025-07-09 12:03:13,113 - INFO - 📡 Server6客户端初始化 (新版): http://192.168.3.93:8009
2025-07-09 12:03:13,113 - INFO - 📤 f2推送回写引擎初始化完成 (修复版本: 直接轮询PostgreSQL)
2025-07-09 12:03:13,113 - INFO - 📡 Server6客户端初始化 (新版): http://192.168.3.93:8009
2025-07-09 12:03:13,115 - INFO - 📥 f3数据拉取器初始化完成 (Staging & UPSERT 模式)
2025-07-09 12:03:13,115 - INFO - 🎮 f4操作处理器初始化完成
2025-07-09 12:03:13,124 - INFO - Redis连接成功
2025-07-09 12:03:13,124 - INFO - Redis连接成功
2025-07-09 12:03:13,125 - INFO - Redis连接成功
2025-07-09 12:03:13,131 - INFO - ✅ MongoDB连接成功
2025-07-09 12:03:13,131 - INFO - ✅ MongoDB连接成功
2025-07-09 12:03:13,133 - INFO - ✅ MongoDB连接成功
2025-07-09 12:03:13,378 - INFO - ✅ PostgreSQL连接池创建成功
2025-07-09 12:03:13,379 - INFO - Redis连接成功
2025-07-09 12:03:13,379 - INFO - ✅ f3数据拉取服务启动成功，预定执行时间: ['02:00', '03:00']
2025-07-09 12:03:13,379 - INFO - 🚀 数据拉取调度器启动，预定运行时间: ['02:00', '03:00']
2025-07-09 12:03:13,379 - INFO - 下一次f3/f5同步任务将在 2025-07-10 02:00:00 执行，等待 50207 秒...
2025-07-09 12:03:13,437 - INFO - ✅ PostgreSQL连接池创建成功
2025-07-09 12:03:13,454 - INFO - ✅ f1监听器服务启动成功
2025-07-09 12:03:13,464 - INFO - ✅ PostgreSQL连接池创建成功
2025-07-09 12:03:13,464 - INFO - ✅ f2推送回写服务启动成功，工作线程数: 4
2025-07-09 12:03:13,464 - INFO - 🚀 推送工作线程启动: worker_1
2025-07-09 12:03:13,464 - INFO - 🚀 推送工作线程启动: worker_2
2025-07-09 12:03:13,464 - INFO - 🚀 推送工作线程启动: worker_3
2025-07-09 12:03:13,465 - INFO - 🚀 推送工作线程启动: worker_4
2025-07-09 12:03:13,466 - INFO - ✅ 分区创建成功: entries_2025_07
2025-07-09 12:03:13,470 - INFO - ✅ 分区创建成功: entries_2025_08
2025-07-09 12:03:13,505 - INFO - ✅ PostgreSQL连接池创建成功
2025-07-09 12:03:13,505 - INFO - ✅ f4操作处理服务启动成功
2025-07-09 12:03:13,505 - INFO - 🔌 推送工作线程停止: worker_4
2025-07-09 12:03:13,506 - INFO - 🔌 推送工作线程停止: worker_3
2025-07-09 12:03:13,507 - INFO - 🔌 推送工作线程停止: worker_2
2025-07-09 12:03:13,507 - INFO - 🔌 推送工作线程停止: worker_1
2025-07-09 12:07:36,969 - INFO - 🔊 f1监听器服务初始化完成
2025-07-09 12:07:36,969 - INFO - 📡 Server6客户端初始化 (新版): http://192.168.3.93:8009
2025-07-09 12:07:36,969 - INFO - 📤 f2推送回写引擎初始化完成 (修复版本: 直接轮询PostgreSQL)
2025-07-09 12:07:36,969 - INFO - 📡 Server6客户端初始化 (新版): http://192.168.3.93:8009
2025-07-09 12:07:36,971 - INFO - 📥 f3数据拉取器初始化完成 (Staging & UPSERT 模式)
2025-07-09 12:07:36,971 - INFO - 🎮 f4操作处理器初始化完成
2025-07-09 12:07:36,984 - INFO - Redis连接成功
2025-07-09 12:07:36,984 - INFO - Redis连接成功
2025-07-09 12:07:36,984 - INFO - Redis连接成功
2025-07-09 12:07:36,989 - INFO - ✅ MongoDB连接成功
2025-07-09 12:07:36,990 - INFO - ✅ MongoDB连接成功
2025-07-09 12:07:36,991 - INFO - ✅ MongoDB连接成功
2025-07-09 12:07:37,303 - INFO - ✅ PostgreSQL连接池创建成功
2025-07-09 12:07:37,304 - INFO - Redis连接成功
2025-07-09 12:07:37,304 - INFO - ✅ f3数据拉取服务启动成功，预定执行时间: ['02:00', '03:00']
2025-07-09 12:07:37,304 - INFO - 🚀 数据拉取调度器启动，预定运行时间: ['02:00', '03:00']
2025-07-09 12:07:37,304 - INFO - 下一次f3/f5同步任务将在 2025-07-10 02:00:00 执行，等待 49943 秒...
2025-07-09 12:07:37,365 - INFO - ✅ PostgreSQL连接池创建成功
2025-07-09 12:07:37,366 - INFO - ✅ f1监听器服务启动成功
2025-07-09 12:07:37,375 - INFO - ✅ 分区创建成功: entries_2025_07
2025-07-09 12:07:37,378 - INFO - ✅ 分区创建成功: entries_2025_08
2025-07-09 12:07:37,464 - INFO - ✅ PostgreSQL连接池创建成功
2025-07-09 12:07:37,464 - INFO - ✅ f2推送回写服务启动成功，工作线程数: 4
2025-07-09 12:07:37,465 - INFO - 🚀 推送工作线程启动: worker_1
2025-07-09 12:07:37,465 - INFO - 🚀 推送工作线程启动: worker_2
2025-07-09 12:07:37,465 - INFO - 🚀 推送工作线程启动: worker_3
2025-07-09 12:07:37,465 - INFO - 🚀 推送工作线程启动: worker_4
2025-07-09 12:07:37,500 - INFO - ✅ PostgreSQL连接池创建成功
2025-07-09 12:07:37,501 - INFO - ✅ f4操作处理服务启动成功
2025-07-09 12:07:37,501 - INFO - 🔌 推送工作线程停止: worker_4
2025-07-09 12:07:37,501 - INFO - 🔌 推送工作线程停止: worker_3
2025-07-09 12:07:37,502 - INFO - 🔌 推送工作线程停止: worker_2
2025-07-09 12:07:37,503 - INFO - 🔌 推送工作线程停止: worker_1
2025-07-09 12:33:20,743 - INFO - 🔊 f1监听器服务初始化完成
2025-07-09 12:33:20,743 - INFO - 📡 Server6客户端初始化 (新版): http://192.168.3.93:8009
2025-07-09 12:33:20,743 - INFO - 📤 f2推送回写引擎初始化完成 (修复版本: 直接轮询PostgreSQL)
2025-07-09 12:33:20,743 - INFO - 📡 Server6客户端初始化 (新版): http://192.168.3.93:8009
2025-07-09 12:33:20,744 - INFO - 📥 f3数据拉取器初始化完成 (Staging & UPSERT 模式)
2025-07-09 12:33:20,744 - INFO - 🎮 f4操作处理器初始化完成
2025-07-09 12:33:20,754 - INFO - Redis连接成功
2025-07-09 12:33:20,754 - INFO - Redis连接成功
2025-07-09 12:33:20,754 - INFO - Redis连接成功
2025-07-09 12:33:20,759 - INFO - ✅ MongoDB连接成功
2025-07-09 12:33:20,760 - INFO - ✅ MongoDB连接成功
2025-07-09 12:33:20,761 - INFO - ✅ MongoDB连接成功
2025-07-09 12:33:21,069 - INFO - ✅ PostgreSQL连接池创建成功
2025-07-09 12:33:21,070 - INFO - Redis连接成功
2025-07-09 12:33:21,070 - INFO - ✅ f3数据拉取服务启动成功，预定执行时间: ['02:00', '03:00']
2025-07-09 12:33:21,070 - INFO - 🚀 数据拉取调度器启动，预定运行时间: ['02:00', '03:00']
2025-07-09 12:33:21,070 - INFO - 下一次f3/f5同步任务将在 2025-07-10 02:00:00 执行，等待 48399 秒...
2025-07-09 12:33:21,090 - INFO - ✅ PostgreSQL连接池创建成功
2025-07-09 12:33:21,091 - INFO - ✅ f1监听器服务启动成功
2025-07-09 12:33:21,095 - INFO - ✅ PostgreSQL连接池创建成功
2025-07-09 12:33:21,096 - INFO - ✅ f2推送回写服务启动成功，工作线程数: 4
2025-07-09 12:33:21,096 - INFO - 🚀 推送工作线程启动: worker_1
2025-07-09 12:33:21,096 - INFO - 🚀 推送工作线程启动: worker_2
2025-07-09 12:33:21,096 - INFO - 🚀 推送工作线程启动: worker_3
2025-07-09 12:33:21,097 - INFO - 🚀 推送工作线程启动: worker_4
2025-07-09 12:33:21,100 - INFO - ✅ 分区创建成功: entries_2025_07
2025-07-09 12:33:21,104 - INFO - ✅ 分区创建成功: entries_2025_08
2025-07-09 12:33:21,126 - INFO - ✅ PostgreSQL连接池创建成功
2025-07-09 12:33:21,126 - INFO - ✅ f4操作处理服务启动成功
2025-07-09 12:33:21,127 - INFO - 🔌 推送工作线程停止: worker_4
2025-07-09 12:33:21,127 - INFO - 🔌 推送工作线程停止: worker_3
2025-07-09 12:33:21,128 - INFO - 🔌 推送工作线程停止: worker_1
2025-07-09 12:33:21,128 - INFO - 🔌 推送工作线程停止: worker_2
2025-07-09 12:39:46,840 - INFO - 🔊 f1监听器服务初始化完成
2025-07-09 12:39:46,840 - INFO - 📡 Server6客户端初始化 (新版): http://192.168.3.93:8009
2025-07-09 12:39:46,841 - INFO - 📤 f2推送回写引擎初始化完成 (修复版本: 直接轮询PostgreSQL)
2025-07-09 12:39:46,841 - INFO - 📡 Server6客户端初始化 (新版): http://192.168.3.93:8009
2025-07-09 12:39:46,842 - INFO - 📥 f3数据拉取器初始化完成 (Staging & UPSERT 模式)
2025-07-09 12:39:46,842 - INFO - 🎮 f4操作处理器初始化完成
2025-07-09 12:39:46,851 - INFO - Redis连接成功
2025-07-09 12:39:46,851 - INFO - Redis连接成功
2025-07-09 12:39:46,851 - INFO - Redis连接成功
2025-07-09 12:39:46,857 - INFO - ✅ MongoDB连接成功
2025-07-09 12:39:46,858 - INFO - ✅ MongoDB连接成功
2025-07-09 12:39:46,859 - INFO - ✅ MongoDB连接成功
2025-07-09 12:39:47,093 - INFO - ✅ PostgreSQL连接池创建成功
2025-07-09 12:39:47,094 - INFO - Redis连接成功
2025-07-09 12:39:47,094 - INFO - ✅ f3数据拉取服务启动成功，预定执行时间: ['02:00', '03:00']
2025-07-09 12:39:47,094 - INFO - 🚀 数据拉取调度器启动，预定运行时间: ['02:00', '03:00']
2025-07-09 12:39:47,094 - INFO - 下一次f3/f5同步任务将在 2025-07-10 02:00:00 执行，等待 48013 秒...
2025-07-09 12:39:47,154 - INFO - ✅ PostgreSQL连接池创建成功
2025-07-09 12:39:47,155 - INFO - ✅ f1监听器服务启动成功
2025-07-09 12:39:47,179 - INFO - ✅ 分区创建成功: entries_2025_07
2025-07-09 12:39:47,183 - INFO - ✅ PostgreSQL连接池创建成功
2025-07-09 12:39:47,183 - INFO - ✅ f2推送回写服务启动成功，工作线程数: 4
2025-07-09 12:39:47,183 - INFO - 🚀 推送工作线程启动: worker_1
2025-07-09 12:39:47,183 - INFO - 🚀 推送工作线程启动: worker_2
2025-07-09 12:39:47,183 - INFO - 🚀 推送工作线程启动: worker_3
2025-07-09 12:39:47,184 - INFO - 🚀 推送工作线程启动: worker_4
2025-07-09 12:39:47,184 - INFO - ✅ 分区创建成功: entries_2025_08
2025-07-09 12:39:47,219 - INFO - ✅ PostgreSQL连接池创建成功
2025-07-09 12:39:47,219 - INFO - ✅ f4操作处理服务启动成功
2025-07-09 12:39:47,219 - INFO - 🔌 推送工作线程停止: worker_3
2025-07-09 12:39:47,219 - INFO - 🔌 推送工作线程停止: worker_4
2025-07-09 12:39:47,221 - INFO - 🔌 推送工作线程停止: worker_2
2025-07-09 12:39:47,221 - INFO - 🔌 推送工作线程停止: worker_1
2025-07-09 13:03:58,331 - INFO - 🔊 f1监听器服务初始化完成
2025-07-09 13:03:58,332 - INFO - 📡 Server6客户端初始化 (新版): http://192.168.3.93:8009
2025-07-09 13:03:58,332 - INFO - 📤 f2推送回写引擎初始化完成 (修复版本: 直接轮询PostgreSQL)
2025-07-09 13:03:58,333 - INFO - 📡 Server6客户端初始化 (新版): http://192.168.3.93:8009
2025-07-09 13:03:58,337 - INFO - 📥 f3数据拉取器初始化完成 (Staging & UPSERT 模式)
2025-07-09 13:03:58,337 - INFO - 🎮 f4操作处理器初始化完成
2025-07-09 13:03:58,362 - INFO - Redis连接成功
2025-07-09 13:03:58,362 - INFO - Redis连接成功
2025-07-09 13:03:58,362 - INFO - Redis连接成功
2025-07-09 13:03:58,366 - INFO - ✅ MongoDB连接成功
2025-07-09 13:03:58,368 - INFO - ✅ MongoDB连接成功
2025-07-09 13:03:58,369 - INFO - ✅ MongoDB连接成功
2025-07-09 13:03:58,625 - INFO - ✅ PostgreSQL连接池创建成功
2025-07-09 13:03:58,626 - INFO - Redis连接成功
2025-07-09 13:03:58,626 - INFO - ✅ f3数据拉取服务启动成功，预定执行时间: ['02:00', '03:00']
2025-07-09 13:03:58,626 - INFO - 🚀 数据拉取调度器启动，预定运行时间: ['02:00', '03:00']
2025-07-09 13:03:58,626 - INFO - 下一次f3/f5同步任务将在 2025-07-10 02:00:00 执行，等待 46561 秒...
2025-07-09 13:03:58,654 - INFO - ✅ PostgreSQL连接池创建成功
2025-07-09 13:03:58,654 - INFO - ✅ f1监听器服务启动成功
2025-07-09 13:03:58,665 - INFO - ✅ 分区创建成功: entries_2025_07
2025-07-09 13:03:58,688 - INFO - ✅ 分区创建成功: entries_2025_08
2025-07-09 13:03:58,693 - INFO - ✅ PostgreSQL连接池创建成功
2025-07-09 13:03:58,713 - INFO - ✅ f2推送回写服务启动成功，工作线程数: 4
2025-07-09 13:03:58,714 - INFO - 🚀 推送工作线程启动: worker_1
2025-07-09 13:03:58,714 - INFO - 🚀 推送工作线程启动: worker_2
2025-07-09 13:03:58,714 - INFO - 🚀 推送工作线程启动: worker_3
2025-07-09 13:03:58,715 - INFO - 🚀 推送工作线程启动: worker_4
2025-07-09 13:03:58,719 - INFO - ✅ PostgreSQL连接池创建成功
2025-07-09 13:03:58,719 - INFO - ✅ f4操作处理服务启动成功
2025-07-09 13:03:58,720 - INFO - 🔌 推送工作线程停止: worker_3
2025-07-09 13:03:58,720 - INFO - 🔌 推送工作线程停止: worker_4
2025-07-09 13:03:58,907 - INFO - 🔌 推送工作线程停止: worker_2
2025-07-09 13:03:58,941 - INFO - 🔌 推送工作线程停止: worker_1
2025-07-09 13:04:06,796 - INFO - 🔊 f1监听器服务初始化完成
2025-07-09 13:04:06,796 - INFO - 📡 Server6客户端初始化 (新版): http://192.168.3.93:8009
2025-07-09 13:04:06,796 - INFO - 📤 f2推送回写引擎初始化完成 (修复版本: 直接轮询PostgreSQL)
2025-07-09 13:04:06,796 - INFO - 📡 Server6客户端初始化 (新版): http://192.168.3.93:8009
2025-07-09 13:04:06,798 - INFO - 📥 f3数据拉取器初始化完成 (Staging & UPSERT 模式)
2025-07-09 13:04:06,798 - INFO - 🎮 f4操作处理器初始化完成
2025-07-09 13:04:06,811 - INFO - Redis连接成功
2025-07-09 13:04:06,811 - INFO - Redis连接成功
2025-07-09 13:04:06,811 - INFO - Redis连接成功
2025-07-09 13:04:06,815 - INFO - ✅ MongoDB连接成功
2025-07-09 13:04:06,817 - INFO - ✅ MongoDB连接成功
2025-07-09 13:04:06,818 - INFO - ✅ MongoDB连接成功
2025-07-09 13:04:07,072 - INFO - ✅ PostgreSQL连接池创建成功
2025-07-09 13:04:07,073 - INFO - Redis连接成功
2025-07-09 13:04:07,073 - INFO - ✅ f3数据拉取服务启动成功，预定执行时间: ['02:00', '03:00']
2025-07-09 13:04:07,073 - INFO - 🚀 数据拉取调度器启动，预定运行时间: ['02:00', '03:00']
2025-07-09 13:04:07,073 - INFO - 下一次f3/f5同步任务将在 2025-07-10 02:00:00 执行，等待 46553 秒...
2025-07-09 13:04:07,112 - INFO - ✅ PostgreSQL连接池创建成功
2025-07-09 13:04:07,112 - INFO - ✅ f1监听器服务启动成功
2025-07-09 13:04:07,122 - INFO - ✅ 分区创建成功: entries_2025_07
2025-07-09 13:04:07,142 - INFO - ✅ 分区创建成功: entries_2025_08
2025-07-09 13:04:07,161 - INFO - ✅ PostgreSQL连接池创建成功
2025-07-09 13:04:07,161 - INFO - ✅ f2推送回写服务启动成功，工作线程数: 4
2025-07-09 13:04:07,161 - INFO - 🚀 推送工作线程启动: worker_1
2025-07-09 13:04:07,161 - INFO - 🚀 推送工作线程启动: worker_2
2025-07-09 13:04:07,161 - INFO - 🚀 推送工作线程启动: worker_3
2025-07-09 13:04:07,162 - INFO - 🚀 推送工作线程启动: worker_4
2025-07-09 13:04:07,166 - INFO - ✅ PostgreSQL连接池创建成功
2025-07-09 13:04:07,166 - INFO - ✅ f4操作处理服务启动成功
2025-07-09 13:04:07,167 - INFO - 🔌 推送工作线程停止: worker_4
2025-07-09 13:04:07,167 - INFO - 🔌 推送工作线程停止: worker_3
2025-07-09 13:04:07,323 - INFO - 🔌 推送工作线程停止: worker_1
2025-07-09 13:04:07,352 - INFO - 🔌 推送工作线程停止: worker_2
2025-07-09 13:06:22,506 - INFO - 🔊 f1监听器服务初始化完成
2025-07-09 13:06:22,507 - INFO - 📡 Server6客户端初始化 (新版): http://192.168.3.93:8009
2025-07-09 13:06:22,507 - INFO - 📤 f2推送回写引擎初始化完成 (修复版本: 直接轮询PostgreSQL)
2025-07-09 13:06:22,507 - INFO - 📡 Server6客户端初始化 (新版): http://192.168.3.93:8009
2025-07-09 13:06:22,509 - INFO - 📥 f3数据拉取器初始化完成 (Staging & UPSERT 模式)
2025-07-09 13:06:22,509 - INFO - 🎮 f4操作处理器初始化完成
2025-07-09 13:06:22,526 - INFO - Redis连接成功
2025-07-09 13:06:22,526 - INFO - Redis连接成功
2025-07-09 13:06:22,526 - INFO - Redis连接成功
2025-07-09 13:06:22,530 - INFO - ✅ MongoDB连接成功
2025-07-09 13:06:22,533 - INFO - ✅ MongoDB连接成功
2025-07-09 13:06:22,533 - INFO - ✅ MongoDB连接成功
2025-07-09 13:06:22,790 - INFO - ✅ PostgreSQL连接池创建成功
2025-07-09 13:06:22,791 - INFO - ✅ f1监听器服务启动成功
2025-07-09 13:06:22,799 - INFO - ✅ 分区创建成功: entries_2025_07
2025-07-09 13:06:22,803 - INFO - ✅ 分区创建成功: entries_2025_08
2025-07-09 13:06:22,849 - INFO - ✅ PostgreSQL连接池创建成功
2025-07-09 13:06:22,849 - INFO - ✅ f2推送回写服务启动成功，工作线程数: 4
2025-07-09 13:06:22,849 - INFO - 🚀 推送工作线程启动: worker_1
2025-07-09 13:06:22,849 - INFO - 🚀 推送工作线程启动: worker_2
2025-07-09 13:06:22,849 - INFO - 🚀 推送工作线程启动: worker_3
2025-07-09 13:06:22,850 - INFO - 🚀 推送工作线程启动: worker_4
2025-07-09 13:06:22,892 - INFO - ✅ PostgreSQL连接池创建成功
2025-07-09 13:06:22,893 - INFO - Redis连接成功
2025-07-09 13:06:22,893 - INFO - ✅ f3数据拉取服务启动成功，预定执行时间: ['02:00', '03:00']
2025-07-09 13:06:22,894 - INFO - 🚀 数据拉取调度器启动，预定运行时间: ['02:00', '03:00']
2025-07-09 13:06:22,894 - INFO - 下一次f3/f5同步任务将在 2025-07-10 02:00:00 执行，等待 46417 秒...
2025-07-09 13:06:22,935 - INFO - ✅ PostgreSQL连接池创建成功
2025-07-09 13:06:22,964 - INFO - ✅ f4操作处理服务启动成功
2025-07-09 13:06:22,965 - INFO - 🔌 推送工作线程停止: worker_3
2025-07-09 13:06:22,965 - INFO - 🔌 推送工作线程停止: worker_4
2025-07-09 13:06:22,967 - INFO - 🔌 推送工作线程停止: worker_1
2025-07-09 13:06:22,967 - INFO - 🔌 推送工作线程停止: worker_2
2025-07-09 13:07:58,028 - INFO - 🔊 f1监听器服务初始化完成
2025-07-09 13:07:58,029 - INFO - 📡 Server6客户端初始化 (新版): http://192.168.3.93:8009
2025-07-09 13:07:58,029 - INFO - 📤 f2推送回写引擎初始化完成 (修复版本: 直接轮询PostgreSQL)
2025-07-09 13:07:58,029 - INFO - 📡 Server6客户端初始化 (新版): http://192.168.3.93:8009
2025-07-09 13:07:58,030 - INFO - 📥 f3数据拉取器初始化完成 (Staging & UPSERT 模式)
2025-07-09 13:07:58,030 - INFO - 🎮 f4操作处理器初始化完成
2025-07-09 13:07:58,041 - INFO - Redis连接成功
2025-07-09 13:07:58,041 - INFO - Redis连接成功
2025-07-09 13:07:58,041 - INFO - Redis连接成功
2025-07-09 13:07:58,047 - INFO - ✅ MongoDB连接成功
2025-07-09 13:07:58,047 - INFO - ✅ MongoDB连接成功
2025-07-09 13:07:58,048 - INFO - ✅ MongoDB连接成功
2025-07-09 13:07:58,366 - INFO - ✅ PostgreSQL连接池创建成功
2025-07-09 13:07:58,367 - INFO - Redis连接成功
2025-07-09 13:07:58,367 - INFO - ✅ f3数据拉取服务启动成功，预定执行时间: ['02:00', '03:00']
2025-07-09 13:07:58,367 - INFO - 🚀 数据拉取调度器启动，预定运行时间: ['02:00', '03:00']
2025-07-09 13:07:58,367 - INFO - 下一次f3/f5同步任务将在 2025-07-10 02:00:00 执行，等待 46322 秒...
2025-07-09 13:07:58,415 - INFO - ✅ PostgreSQL连接池创建成功
2025-07-09 13:07:58,415 - INFO - ✅ f1监听器服务启动成功
2025-07-09 13:07:58,437 - INFO - ✅ 分区创建成功: entries_2025_07
2025-07-09 13:07:58,440 - INFO - ✅ 分区创建成功: entries_2025_08
2025-07-09 13:07:58,467 - INFO - ✅ PostgreSQL连接池创建成功
2025-07-09 13:07:58,467 - INFO - ✅ f4操作处理服务启动成功
2025-07-09 13:07:58,475 - INFO - ✅ PostgreSQL连接池创建成功
2025-07-09 13:07:58,475 - INFO - ✅ f2推送回写服务启动成功，工作线程数: 4
2025-07-09 13:07:58,475 - INFO - 🚀 推送工作线程启动: worker_1
2025-07-09 13:07:58,475 - INFO - 🚀 推送工作线程启动: worker_2
2025-07-09 13:07:58,475 - INFO - 🚀 推送工作线程启动: worker_3
2025-07-09 13:07:58,476 - INFO - 🚀 推送工作线程启动: worker_4
2025-07-09 13:07:58,477 - INFO - 🔌 推送工作线程停止: worker_4
2025-07-09 13:07:58,477 - INFO - 🔌 推送工作线程停止: worker_3
2025-07-09 13:07:58,725 - INFO - 🔌 推送工作线程停止: worker_1
2025-07-09 13:07:58,748 - INFO - 🔌 推送工作线程停止: worker_2
2025-07-09 13:13:31,451 - INFO - 🔊 f1监听器服务初始化完成
2025-07-09 13:13:31,451 - INFO - 📡 Server6客户端初始化 (新版): http://192.168.3.93:8009
2025-07-09 13:13:31,451 - INFO - 📤 f2推送回写引擎初始化完成 (修复版本: 直接轮询PostgreSQL)
2025-07-09 13:13:31,451 - INFO - 📡 Server6客户端初始化 (新版): http://192.168.3.93:8009
2025-07-09 13:13:31,452 - INFO - 📥 f3数据拉取器初始化完成 (Staging & UPSERT 模式)
2025-07-09 13:13:31,452 - INFO - 🎮 f4操作处理器初始化完成
2025-07-09 13:13:31,462 - INFO - Redis连接成功
2025-07-09 13:13:31,462 - INFO - Redis连接成功
2025-07-09 13:13:31,462 - INFO - Redis连接成功
2025-07-09 13:13:31,469 - INFO - ✅ MongoDB连接成功
2025-07-09 13:13:31,469 - INFO - ✅ MongoDB连接成功
2025-07-09 13:13:31,470 - INFO - ✅ MongoDB连接成功
2025-07-09 13:13:31,788 - INFO - ✅ PostgreSQL连接池创建成功
2025-07-09 13:13:31,790 - INFO - Redis连接成功
2025-07-09 13:13:31,790 - INFO - ✅ f3数据拉取服务启动成功，预定执行时间: ['02:00', '03:00']
2025-07-09 13:13:31,790 - INFO - 🚀 数据拉取调度器启动，预定运行时间: ['02:00', '03:00']
2025-07-09 13:13:31,790 - INFO - 下一次f3/f5同步任务将在 2025-07-10 02:00:00 执行，等待 45988 秒...
2025-07-09 13:13:31,832 - INFO - ✅ PostgreSQL连接池创建成功
2025-07-09 13:13:31,832 - INFO - ✅ f1监听器服务启动成功
2025-07-09 13:13:31,842 - INFO - ✅ 分区创建成功: entries_2025_07
2025-07-09 13:13:31,846 - INFO - ✅ 分区创建成功: entries_2025_08
2025-07-09 13:13:31,887 - INFO - ✅ PostgreSQL连接池创建成功
2025-07-09 13:13:31,887 - INFO - ✅ f2推送回写服务启动成功，工作线程数: 4
2025-07-09 13:13:31,887 - INFO - 🚀 推送工作线程启动: worker_1
2025-07-09 13:13:31,888 - INFO - 🚀 推送工作线程启动: worker_2
2025-07-09 13:13:31,888 - INFO - 🚀 推送工作线程启动: worker_3
2025-07-09 13:13:31,888 - INFO - 🚀 推送工作线程启动: worker_4
2025-07-09 13:13:31,934 - INFO - ✅ PostgreSQL连接池创建成功
2025-07-09 13:13:31,934 - INFO - ✅ f4操作处理服务启动成功
2025-07-09 13:13:31,934 - INFO - 🔌 推送工作线程停止: worker_4
2025-07-09 13:13:31,935 - INFO - 🔌 推送工作线程停止: worker_3
2025-07-09 13:13:31,936 - INFO - 🔌 推送工作线程停止: worker_1
2025-07-09 13:13:31,936 - INFO - 🔌 推送工作线程停止: worker_2
2025-07-09 14:23:35,542 - INFO - 🔊 f1监听器服务初始化完成
2025-07-09 14:23:35,542 - INFO - 📡 Server6客户端初始化 (新版): http://192.168.3.93:8009
2025-07-09 14:23:35,542 - INFO - 📤 f2推送回写引擎初始化完成 (修复版本: 直接轮询PostgreSQL)
2025-07-09 14:23:35,542 - INFO - 📡 Server6客户端初始化 (新版): http://192.168.3.93:8009
2025-07-09 14:23:35,543 - INFO - 📥 f3数据拉取器初始化完成 (Staging & UPSERT 模式)
2025-07-09 14:23:35,543 - INFO - 🎮 f4操作处理器初始化完成
2025-07-09 14:23:35,552 - INFO - Redis连接成功
2025-07-09 14:23:35,552 - INFO - Redis连接成功
2025-07-09 14:23:35,553 - INFO - Redis连接成功
2025-07-09 14:23:35,560 - INFO - ✅ MongoDB连接成功
2025-07-09 14:23:35,561 - INFO - ✅ MongoDB连接成功
2025-07-09 14:23:35,562 - INFO - ✅ MongoDB连接成功
2025-07-09 14:23:35,817 - INFO - ✅ PostgreSQL连接池创建成功
2025-07-09 14:23:35,818 - INFO - Redis连接成功
2025-07-09 14:23:35,818 - INFO - ✅ f3数据拉取服务启动成功，预定执行时间: ['02:00', '03:00']
2025-07-09 14:23:35,818 - INFO - 🚀 数据拉取调度器启动，预定运行时间: ['02:00', '03:00']
2025-07-09 14:23:35,818 - INFO - 下一次f3/f5同步任务将在 2025-07-10 02:00:00 执行，等待 41784 秒...
2025-07-09 14:23:35,859 - INFO - ✅ PostgreSQL连接池创建成功
2025-07-09 14:23:35,859 - INFO - ✅ f1监听器服务启动成功
2025-07-09 14:23:35,870 - INFO - ✅ 分区创建成功: entries_2025_07
2025-07-09 14:23:35,873 - INFO - ✅ 分区创建成功: entries_2025_08
2025-07-09 14:23:35,896 - INFO - ✅ PostgreSQL连接池创建成功
2025-07-09 14:23:35,896 - INFO - ✅ f2推送回写服务启动成功，工作线程数: 4
2025-07-09 14:23:35,896 - INFO - 🚀 推送工作线程启动: worker_1
2025-07-09 14:23:35,896 - INFO - 🚀 推送工作线程启动: worker_2
2025-07-09 14:23:35,896 - INFO - 🚀 推送工作线程启动: worker_3
2025-07-09 14:23:35,897 - INFO - 🚀 推送工作线程启动: worker_4
2025-07-09 14:23:35,932 - INFO - ✅ PostgreSQL连接池创建成功
2025-07-09 14:23:35,932 - INFO - ✅ f4操作处理服务启动成功
2025-07-09 14:23:35,933 - INFO - 🔌 推送工作线程停止: worker_3
2025-07-09 14:23:35,933 - INFO - 🔌 推送工作线程停止: worker_4
2025-07-09 14:23:35,934 - INFO - 🔌 推送工作线程停止: worker_2
2025-07-09 14:23:35,934 - INFO - 🔌 推送工作线程停止: worker_1
2025-07-09 14:25:00,664 - INFO - 🔊 f1监听器服务初始化完成
2025-07-09 14:25:00,665 - INFO - 📡 Server6客户端初始化 (新版): http://192.168.3.93:8009
2025-07-09 14:25:00,665 - INFO - 📤 f2推送回写引擎初始化完成 (修复版本: 直接轮询PostgreSQL)
2025-07-09 14:25:00,665 - INFO - 📡 Server6客户端初始化 (新版): http://192.168.3.93:8009
2025-07-09 14:25:00,669 - INFO - 📥 f3数据拉取器初始化完成 (Staging & UPSERT 模式)
2025-07-09 14:25:00,670 - INFO - 🎮 f4操作处理器初始化完成
2025-07-09 14:25:00,699 - INFO - Redis连接成功
2025-07-09 14:25:00,699 - INFO - Redis连接成功
2025-07-09 14:25:00,699 - INFO - Redis连接成功
2025-07-09 14:25:00,704 - INFO - ✅ MongoDB连接成功
2025-07-09 14:25:00,710 - INFO - ✅ MongoDB连接成功
2025-07-09 14:25:00,710 - INFO - ✅ MongoDB连接成功
2025-07-09 14:25:01,019 - INFO - ✅ PostgreSQL连接池创建成功
2025-07-09 14:25:01,020 - INFO - Redis连接成功
2025-07-09 14:25:01,020 - INFO - ✅ f3数据拉取服务启动成功，预定执行时间: ['02:00', '03:00']
2025-07-09 14:25:01,020 - INFO - 🚀 数据拉取调度器启动，预定运行时间: ['02:00', '03:00']
2025-07-09 14:25:01,020 - INFO - 下一次f3/f5同步任务将在 2025-07-10 02:00:00 执行，等待 41699 秒...
2025-07-09 14:25:01,024 - INFO - ✅ PostgreSQL连接池创建成功
2025-07-09 14:25:01,024 - INFO - ✅ f1监听器服务启动成功
2025-07-09 14:25:01,033 - INFO - ✅ 分区创建成功: entries_2025_07
2025-07-09 14:25:01,037 - INFO - ✅ 分区创建成功: entries_2025_08
2025-07-09 14:25:01,068 - INFO - ✅ PostgreSQL连接池创建成功
2025-07-09 14:25:01,068 - INFO - ✅ f2推送回写服务启动成功，工作线程数: 4
2025-07-09 14:25:01,069 - INFO - 🚀 推送工作线程启动: worker_1
2025-07-09 14:25:01,069 - INFO - 🚀 推送工作线程启动: worker_2
2025-07-09 14:25:01,069 - INFO - 🚀 推送工作线程启动: worker_3
2025-07-09 14:25:01,069 - INFO - 🚀 推送工作线程启动: worker_4
2025-07-09 14:25:01,136 - INFO - ✅ PostgreSQL连接池创建成功
2025-07-09 14:25:01,136 - INFO - ✅ f4操作处理服务启动成功
2025-07-09 14:25:01,136 - INFO - 🔌 推送工作线程停止: worker_3
2025-07-09 14:25:01,137 - INFO - 🔌 推送工作线程停止: worker_4
2025-07-09 14:25:01,139 - INFO - 🔌 推送工作线程停止: worker_1
2025-07-09 14:25:01,139 - INFO - 🔌 推送工作线程停止: worker_2
2025-07-09 14:31:37,588 - INFO - 🔊 f1监听器服务初始化完成
2025-07-09 14:31:37,589 - INFO - 📡 Server6客户端初始化 (新版): http://192.168.3.93:8009
2025-07-09 14:31:37,589 - INFO - 📤 f2推送回写引擎初始化完成 (修复版本: 直接轮询PostgreSQL)
2025-07-09 14:31:37,589 - INFO - 📡 Server6客户端初始化 (新版): http://192.168.3.93:8009
2025-07-09 14:31:37,594 - INFO - 📥 f3数据拉取器初始化完成 (Staging & UPSERT 模式)
2025-07-09 14:31:37,595 - INFO - 🎮 f4操作处理器初始化完成
2025-07-09 14:31:37,618 - INFO - Redis连接成功
2025-07-09 14:31:37,618 - INFO - Redis连接成功
2025-07-09 14:31:37,618 - INFO - Redis连接成功
2025-07-09 14:31:37,623 - INFO - ✅ MongoDB连接成功
2025-07-09 14:31:37,626 - INFO - ✅ MongoDB连接成功
2025-07-09 14:31:37,626 - INFO - ✅ MongoDB连接成功
2025-07-09 14:31:37,899 - INFO - ✅ PostgreSQL连接池创建成功
2025-07-09 14:31:37,900 - INFO - Redis连接成功
2025-07-09 14:31:37,900 - INFO - ✅ f3数据拉取服务启动成功，预定执行时间: ['02:00', '03:00']
2025-07-09 14:31:37,900 - INFO - 🚀 数据拉取调度器启动，预定运行时间: ['02:00', '03:00']
2025-07-09 14:31:37,900 - INFO - 下一次f3/f5同步任务将在 2025-07-10 02:00:00 执行，等待 41302 秒...
2025-07-09 14:31:37,939 - INFO - ✅ PostgreSQL连接池创建成功
2025-07-09 14:31:37,939 - INFO - ✅ f1监听器服务启动成功
2025-07-09 14:31:37,960 - INFO - ✅ PostgreSQL连接池创建成功
2025-07-09 14:31:37,960 - INFO - ✅ f2推送回写服务启动成功，工作线程数: 4
2025-07-09 14:31:37,961 - INFO - 🚀 推送工作线程启动: worker_1
2025-07-09 14:31:37,961 - INFO - 🚀 推送工作线程启动: worker_2
2025-07-09 14:31:37,961 - INFO - 🚀 推送工作线程启动: worker_3
2025-07-09 14:31:37,961 - INFO - 🚀 推送工作线程启动: worker_4
2025-07-09 14:31:37,962 - INFO - ✅ 分区创建成功: entries_2025_07
2025-07-09 14:31:37,966 - INFO - ✅ PostgreSQL连接池创建成功
2025-07-09 14:31:37,966 - INFO - ✅ f4操作处理服务启动成功
2025-07-09 14:31:37,967 - INFO - 🔌 推送工作线程停止: worker_3
2025-07-09 14:31:37,967 - INFO - 🔌 推送工作线程停止: worker_4
2025-07-09 14:31:38,152 - INFO - 🔌 推送工作线程停止: worker_2
2025-07-09 14:31:38,172 - INFO - 🔌 推送工作线程停止: worker_1
2025-07-09 14:32:19,826 - INFO - 🔊 f1监听器服务初始化完成
2025-07-09 14:32:19,827 - INFO - 📡 Server6客户端初始化 (新版): http://192.168.3.93:8009
2025-07-09 14:32:19,827 - INFO - 📤 f2推送回写引擎初始化完成 (修复版本: 直接轮询PostgreSQL)
2025-07-09 14:32:19,827 - INFO - 📡 Server6客户端初始化 (新版): http://192.168.3.93:8009
2025-07-09 14:32:19,831 - INFO - 📥 f3数据拉取器初始化完成 (Staging & UPSERT 模式)
2025-07-09 14:32:19,832 - INFO - 🎮 f4操作处理器初始化完成
2025-07-09 14:32:19,842 - INFO - Redis连接成功
2025-07-09 14:32:19,842 - INFO - Redis连接成功
2025-07-09 14:32:19,842 - INFO - Redis连接成功
2025-07-09 14:32:19,848 - INFO - ✅ MongoDB连接成功
2025-07-09 14:32:19,849 - INFO - ✅ MongoDB连接成功
2025-07-09 14:32:19,849 - INFO - ✅ MongoDB连接成功
2025-07-09 14:32:20,087 - INFO - ✅ PostgreSQL连接池创建成功
2025-07-09 14:32:20,088 - INFO - Redis连接成功
2025-07-09 14:32:20,089 - INFO - ✅ f3数据拉取服务启动成功，预定执行时间: ['02:00', '03:00']
2025-07-09 14:32:20,089 - INFO - 🚀 数据拉取调度器启动，预定运行时间: ['02:00', '03:00']
2025-07-09 14:32:20,089 - INFO - 下一次f3/f5同步任务将在 2025-07-10 02:00:00 执行，等待 41260 秒...
2025-07-09 14:32:20,148 - INFO - ✅ PostgreSQL连接池创建成功
2025-07-09 14:32:20,148 - INFO - ✅ f1监听器服务启动成功
2025-07-09 14:32:20,171 - INFO - ✅ 分区创建成功: entries_2025_07
2025-07-09 14:32:20,175 - INFO - ✅ PostgreSQL连接池创建成功
2025-07-09 14:32:20,175 - INFO - ✅ f2推送回写服务启动成功，工作线程数: 4
2025-07-09 14:32:20,175 - INFO - 🚀 推送工作线程启动: worker_1
2025-07-09 14:32:20,175 - INFO - 🚀 推送工作线程启动: worker_2
2025-07-09 14:32:20,175 - INFO - 🚀 推送工作线程启动: worker_3
2025-07-09 14:32:20,176 - INFO - 🚀 推送工作线程启动: worker_4
2025-07-09 14:32:20,176 - INFO - ✅ 分区创建成功: entries_2025_08
2025-07-09 14:32:20,200 - INFO - ✅ PostgreSQL连接池创建成功
2025-07-09 14:32:20,200 - INFO - ✅ f4操作处理服务启动成功
2025-07-09 14:32:20,200 - INFO - 🔌 推送工作线程停止: worker_3
2025-07-09 14:32:20,201 - INFO - 🔌 推送工作线程停止: worker_4
2025-07-09 14:32:20,202 - INFO - 🔌 推送工作线程停止: worker_2
2025-07-09 14:32:20,202 - INFO - 🔌 推送工作线程停止: worker_1
2025-07-09 14:33:30,468 - INFO - 🔊 f1监听器服务初始化完成
2025-07-09 14:33:30,468 - INFO - 📡 Server6客户端初始化 (新版): http://192.168.3.93:8009
2025-07-09 14:33:30,468 - INFO - 📤 f2推送回写引擎初始化完成 (修复版本: 直接轮询PostgreSQL)
2025-07-09 14:33:30,468 - INFO - 📡 Server6客户端初始化 (新版): http://192.168.3.93:8009
2025-07-09 14:33:30,469 - INFO - 📥 f3数据拉取器初始化完成 (Staging & UPSERT 模式)
2025-07-09 14:33:30,469 - INFO - 🎮 f4操作处理器初始化完成
2025-07-09 14:33:30,478 - INFO - Redis连接成功
2025-07-09 14:33:30,478 - INFO - Redis连接成功
2025-07-09 14:33:30,479 - INFO - Redis连接成功
2025-07-09 14:33:30,484 - INFO - ✅ MongoDB连接成功
2025-07-09 14:33:30,486 - INFO - ✅ MongoDB连接成功
2025-07-09 14:33:30,486 - INFO - ✅ MongoDB连接成功
2025-07-09 14:33:30,705 - INFO - ✅ PostgreSQL连接池创建成功
2025-07-09 14:33:30,705 - INFO - Redis连接成功
2025-07-09 14:33:30,705 - INFO - ✅ f3数据拉取服务启动成功，预定执行时间: ['02:00', '03:00']
2025-07-09 14:33:30,705 - INFO - 🚀 数据拉取调度器启动，预定运行时间: ['02:00', '03:00']
2025-07-09 14:33:30,706 - INFO - 下一次f3/f5同步任务将在 2025-07-10 02:00:00 执行，等待 41189 秒...
2025-07-09 14:33:30,749 - INFO - ✅ PostgreSQL连接池创建成功
2025-07-09 14:33:30,749 - INFO - ✅ f1监听器服务启动成功
2025-07-09 14:33:30,759 - INFO - ✅ 分区创建成功: entries_2025_07
2025-07-09 14:33:30,762 - INFO - ✅ 分区创建成功: entries_2025_08
2025-07-09 14:33:30,792 - INFO - ✅ PostgreSQL连接池创建成功
2025-07-09 14:33:30,792 - INFO - ✅ f2推送回写服务启动成功，工作线程数: 4
2025-07-09 14:33:30,792 - INFO - 🚀 推送工作线程启动: worker_1
2025-07-09 14:33:30,792 - INFO - 🚀 推送工作线程启动: worker_2
2025-07-09 14:33:30,792 - INFO - 🚀 推送工作线程启动: worker_3
2025-07-09 14:33:30,793 - INFO - 🚀 推送工作线程启动: worker_4
2025-07-09 14:33:30,817 - INFO - ✅ PostgreSQL连接池创建成功
2025-07-09 14:33:30,817 - INFO - ✅ f4操作处理服务启动成功
2025-07-09 14:33:30,817 - INFO - 🔌 推送工作线程停止: worker_3
2025-07-09 14:33:30,817 - INFO - 🔌 推送工作线程停止: worker_4
2025-07-09 14:33:30,819 - INFO - 🔌 推送工作线程停止: worker_2
2025-07-09 14:33:30,819 - INFO - 🔌 推送工作线程停止: worker_1
2025-07-09 15:20:59,030 - INFO - 🔊 f1监听器服务初始化完成
2025-07-09 15:20:59,030 - INFO - 📡 Server6客户端初始化 (新版): http://192.168.3.93:8009
2025-07-09 15:20:59,030 - INFO - 📤 f2推送回写引擎初始化完成 (修复版本: 直接轮询PostgreSQL)
2025-07-09 15:20:59,030 - INFO - 📡 Server6客户端初始化 (新版): http://192.168.3.93:8009
2025-07-09 15:20:59,031 - INFO - 📥 f3数据拉取器初始化完成 (Staging & UPSERT 模式)
2025-07-09 15:20:59,031 - INFO - 🎮 f4操作处理器初始化完成
2025-07-09 15:20:59,041 - INFO - Redis连接成功
2025-07-09 15:20:59,041 - INFO - Redis连接成功
2025-07-09 15:20:59,041 - INFO - Redis连接成功
2025-07-09 15:20:59,047 - INFO - ✅ MongoDB连接成功
2025-07-09 15:20:59,048 - INFO - ✅ MongoDB连接成功
2025-07-09 15:20:59,048 - INFO - ✅ MongoDB连接成功
2025-07-09 15:20:59,326 - INFO - ✅ PostgreSQL连接池创建成功
2025-07-09 15:20:59,327 - INFO - Redis连接成功
2025-07-09 15:20:59,327 - INFO - ✅ f3数据拉取服务启动成功，预定执行时间: ['02:00', '03:00']
2025-07-09 15:20:59,327 - INFO - 🚀 数据拉取调度器启动，预定运行时间: ['02:00', '03:00']
2025-07-09 15:20:59,328 - INFO - 下一次f3/f5同步任务将在 2025-07-10 02:00:00 执行，等待 38341 秒...
2025-07-09 15:20:59,347 - INFO - ✅ PostgreSQL连接池创建成功
2025-07-09 15:20:59,347 - INFO - ✅ f1监听器服务启动成功
2025-07-09 15:20:59,367 - INFO - ✅ PostgreSQL连接池创建成功
2025-07-09 15:20:59,367 - INFO - ✅ f2推送回写服务启动成功，工作线程数: 4
2025-07-09 15:20:59,367 - INFO - 🚀 推送工作线程启动: worker_1
2025-07-09 15:20:59,367 - INFO - 🚀 推送工作线程启动: worker_2
2025-07-09 15:20:59,367 - INFO - 🚀 推送工作线程启动: worker_3
2025-07-09 15:20:59,368 - INFO - 🚀 推送工作线程启动: worker_4
2025-07-09 15:20:59,369 - INFO - ✅ 分区创建成功: entries_2025_07
2025-07-09 15:20:59,372 - INFO - ✅ PostgreSQL连接池创建成功
2025-07-09 15:20:59,372 - INFO - ✅ f4操作处理服务启动成功
2025-07-09 15:20:59,373 - INFO - 🔌 推送工作线程停止: worker_3
2025-07-09 15:20:59,373 - INFO - 🔌 推送工作线程停止: worker_4
2025-07-09 15:20:59,553 - INFO - 🔌 推送工作线程停止: worker_1
2025-07-09 15:20:59,568 - INFO - 🔌 推送工作线程停止: worker_2
2025-07-09 15:26:59,912 - INFO - 🔊 f1监听器服务初始化完成
2025-07-09 15:26:59,912 - INFO - 📡 Server6客户端初始化 (新版): http://192.168.3.93:8009
2025-07-09 15:26:59,913 - INFO - 📤 f2推送回写引擎初始化完成 (修复版本: 直接轮询PostgreSQL)
2025-07-09 15:26:59,913 - INFO - 📡 Server6客户端初始化 (新版): http://192.168.3.93:8009
2025-07-09 15:26:59,914 - INFO - 📥 f3数据拉取器初始化完成 (Staging & UPSERT 模式)
2025-07-09 15:26:59,914 - INFO - 🎮 f4操作处理器初始化完成
2025-07-09 15:26:59,925 - INFO - Redis连接成功
2025-07-09 15:26:59,925 - INFO - Redis连接成功
2025-07-09 15:26:59,925 - INFO - Redis连接成功
2025-07-09 15:26:59,932 - INFO - ✅ MongoDB连接成功
2025-07-09 15:26:59,932 - INFO - ✅ MongoDB连接成功
2025-07-09 15:26:59,933 - INFO - ✅ MongoDB连接成功
2025-07-09 15:27:00,288 - INFO - ✅ PostgreSQL连接池创建成功
2025-07-09 15:27:00,289 - INFO - Redis连接成功
2025-07-09 15:27:00,289 - INFO - ✅ f3数据拉取服务启动成功，预定执行时间: ['02:00', '03:00']
2025-07-09 15:27:00,289 - INFO - 🚀 数据拉取调度器启动，预定运行时间: ['02:00', '03:00']
2025-07-09 15:27:00,289 - INFO - 下一次f3/f5同步任务将在 2025-07-10 02:00:00 执行，等待 37980 秒...
2025-07-09 15:27:00,292 - INFO - ✅ PostgreSQL连接池创建成功
2025-07-09 15:27:00,292 - INFO - ✅ f1监听器服务启动成功
2025-07-09 15:27:00,315 - INFO - ✅ PostgreSQL连接池创建成功
2025-07-09 15:27:00,315 - INFO - ✅ f2推送回写服务启动成功，工作线程数: 4
2025-07-09 15:27:00,315 - INFO - 🚀 推送工作线程启动: worker_1
2025-07-09 15:27:00,315 - INFO - 🚀 推送工作线程启动: worker_2
2025-07-09 15:27:00,315 - INFO - 🚀 推送工作线程启动: worker_3
2025-07-09 15:27:00,316 - INFO - 🚀 推送工作线程启动: worker_4
2025-07-09 15:27:00,334 - INFO - ✅ 分区创建成功: entries_2025_07
2025-07-09 15:27:00,338 - INFO - ✅ 分区创建成功: entries_2025_08
2025-07-09 15:27:00,338 - INFO - ✅ PostgreSQL连接池创建成功
2025-07-09 15:27:00,338 - INFO - ✅ f4操作处理服务启动成功
2025-07-09 15:27:00,339 - INFO - 🔌 推送工作线程停止: worker_3
2025-07-09 15:27:00,339 - INFO - 🔌 推送工作线程停止: worker_4
2025-07-09 15:27:00,341 - INFO - 🔌 推送工作线程停止: worker_2
2025-07-09 15:27:00,563 - INFO - 🔌 推送工作线程停止: worker_1
2025-07-09 15:29:29,389 - INFO - 🔊 f1监听器服务初始化完成
2025-07-09 15:29:29,389 - INFO - 📡 Server6客户端初始化 (新版): http://192.168.3.93:8009
2025-07-09 15:29:29,389 - INFO - 📤 f2推送回写引擎初始化完成 (修复版本: 直接轮询PostgreSQL)
2025-07-09 15:29:29,389 - INFO - 📡 Server6客户端初始化 (新版): http://192.168.3.93:8009
2025-07-09 15:29:29,391 - INFO - 📥 f3数据拉取器初始化完成 (Staging & UPSERT 模式)
2025-07-09 15:29:29,391 - INFO - 🎮 f4操作处理器初始化完成
2025-07-09 15:29:29,403 - INFO - Redis连接成功
2025-07-09 15:29:29,404 - INFO - Redis连接成功
2025-07-09 15:29:29,404 - INFO - Redis连接成功
2025-07-09 15:29:29,409 - INFO - ✅ MongoDB连接成功
2025-07-09 15:29:29,411 - INFO - ✅ MongoDB连接成功
2025-07-09 15:29:29,411 - INFO - ✅ MongoDB连接成功
2025-07-09 15:29:29,709 - INFO - ✅ PostgreSQL连接池创建成功
2025-07-09 15:29:29,710 - INFO - Redis连接成功
2025-07-09 15:29:29,710 - INFO - ✅ f3数据拉取服务启动成功，预定执行时间: ['02:00', '03:00']
2025-07-09 15:29:29,710 - INFO - 🚀 数据拉取调度器启动，预定运行时间: ['02:00', '03:00']
2025-07-09 15:29:29,710 - INFO - 下一次f3/f5同步任务将在 2025-07-10 02:00:00 执行，等待 37830 秒...
2025-07-09 15:29:29,815 - INFO - ✅ PostgreSQL连接池创建成功
2025-07-09 15:29:29,815 - INFO - ✅ f1监听器服务启动成功
2025-07-09 15:29:29,839 - INFO - ✅ PostgreSQL连接池创建成功
2025-07-09 15:29:29,839 - INFO - ✅ f2推送回写服务启动成功，工作线程数: 4
2025-07-09 15:29:29,839 - INFO - 🚀 推送工作线程启动: worker_1
2025-07-09 15:29:29,839 - INFO - 🚀 推送工作线程启动: worker_2
2025-07-09 15:29:29,839 - INFO - 🚀 推送工作线程启动: worker_3
2025-07-09 15:29:29,840 - INFO - 🚀 推送工作线程启动: worker_4
2025-07-09 15:29:29,841 - INFO - ✅ 分区创建成功: entries_2025_07
2025-07-09 15:29:29,844 - INFO - ✅ PostgreSQL连接池创建成功
2025-07-09 15:29:29,844 - INFO - ✅ f4操作处理服务启动成功
2025-07-09 15:29:29,844 - INFO - 🔌 推送工作线程停止: worker_4
2025-07-09 15:29:29,845 - INFO - 🔌 推送工作线程停止: worker_3
2025-07-09 15:29:30,051 - INFO - 🔌 推送工作线程停止: worker_1
2025-07-09 15:29:30,084 - INFO - 🔌 推送工作线程停止: worker_2
2025-07-09 15:30:44,798 - INFO - 🔊 f1监听器服务初始化完成
2025-07-09 15:30:44,798 - INFO - 📡 Server6客户端初始化 (新版): http://192.168.3.93:8009
2025-07-09 15:30:44,798 - INFO - 📤 f2推送回写引擎初始化完成 (修复版本: 直接轮询PostgreSQL)
2025-07-09 15:30:44,799 - INFO - 📡 Server6客户端初始化 (新版): http://192.168.3.93:8009
2025-07-09 15:30:44,800 - INFO - 📥 f3数据拉取器初始化完成 (Staging & UPSERT 模式)
2025-07-09 15:30:44,800 - INFO - 🎮 f4操作处理器初始化完成
2025-07-09 15:30:44,810 - INFO - Redis连接成功
2025-07-09 15:30:44,810 - INFO - Redis连接成功
2025-07-09 15:30:44,810 - INFO - Redis连接成功
2025-07-09 15:30:44,817 - INFO - ✅ MongoDB连接成功
2025-07-09 15:30:44,820 - INFO - ✅ MongoDB连接成功
2025-07-09 15:30:44,821 - INFO - ✅ MongoDB连接成功
2025-07-09 15:30:45,140 - INFO - ✅ PostgreSQL连接池创建成功
2025-07-09 15:30:45,140 - INFO - Redis连接成功
2025-07-09 15:30:45,141 - INFO - ✅ f3数据拉取服务启动成功，预定执行时间: ['02:00', '03:00']
2025-07-09 15:30:45,141 - INFO - 🚀 数据拉取调度器启动，预定运行时间: ['02:00', '03:00']
2025-07-09 15:30:45,141 - INFO - 下一次f3/f5同步任务将在 2025-07-10 02:00:00 执行，等待 37755 秒...
2025-07-09 15:30:45,183 - INFO - ✅ PostgreSQL连接池创建成功
2025-07-09 15:30:45,184 - INFO - ✅ f1监听器服务启动成功
2025-07-09 15:30:45,193 - INFO - ✅ 分区创建成功: entries_2025_07
2025-07-09 15:30:45,197 - INFO - ✅ 分区创建成功: entries_2025_08
2025-07-09 15:30:45,225 - INFO - ✅ PostgreSQL连接池创建成功
2025-07-09 15:30:45,226 - INFO - ✅ f2推送回写服务启动成功，工作线程数: 4
2025-07-09 15:30:45,226 - INFO - 🚀 推送工作线程启动: worker_1
2025-07-09 15:30:45,226 - INFO - 🚀 推送工作线程启动: worker_2
2025-07-09 15:30:45,226 - INFO - 🚀 推送工作线程启动: worker_3
2025-07-09 15:30:45,227 - INFO - 🚀 推送工作线程启动: worker_4
2025-07-09 15:30:45,283 - INFO - ✅ PostgreSQL连接池创建成功
2025-07-09 15:30:45,283 - INFO - ✅ f4操作处理服务启动成功
2025-07-09 15:30:45,284 - INFO - 🔌 推送工作线程停止: worker_4
2025-07-09 15:30:45,284 - INFO - 🔌 推送工作线程停止: worker_3
2025-07-09 15:30:45,285 - INFO - 🔌 推送工作线程停止: worker_1
2025-07-09 15:30:45,285 - INFO - 🔌 推送工作线程停止: worker_2
2025-07-09 15:35:27,942 - INFO - 🔊 f1监听器服务初始化完成
2025-07-09 15:35:27,943 - INFO - 📡 Server6客户端初始化 (新版): http://192.168.3.93:8009
2025-07-09 15:35:27,943 - INFO - 📤 f2推送回写引擎初始化完成 (修复版本: 直接轮询PostgreSQL)
2025-07-09 15:35:27,944 - INFO - 📡 Server6客户端初始化 (新版): http://192.168.3.93:8009
2025-07-09 15:35:27,947 - INFO - 📥 f3数据拉取器初始化完成 (Staging & UPSERT 模式)
2025-07-09 15:35:27,947 - INFO - 🎮 f4操作处理器初始化完成
2025-07-09 15:35:27,957 - INFO - Redis连接成功
2025-07-09 15:35:27,957 - INFO - Redis连接成功
2025-07-09 15:35:27,957 - INFO - Redis连接成功
2025-07-09 15:35:27,962 - INFO - ✅ MongoDB连接成功
2025-07-09 15:35:27,963 - INFO - ✅ MongoDB连接成功
2025-07-09 15:35:27,964 - INFO - ✅ MongoDB连接成功
2025-07-09 15:35:28,327 - INFO - ✅ PostgreSQL连接池创建成功
2025-07-09 15:35:28,328 - INFO - Redis连接成功
2025-07-09 15:35:28,328 - INFO - ✅ f3数据拉取服务启动成功，预定执行时间: ['02:00', '03:00']
2025-07-09 15:35:28,328 - INFO - 🚀 数据拉取调度器启动，预定运行时间: ['02:00', '03:00']
2025-07-09 15:35:28,328 - INFO - 下一次f3/f5同步任务将在 2025-07-10 02:00:00 执行，等待 37472 秒...
2025-07-09 15:35:28,333 - INFO - ✅ PostgreSQL连接池创建成功
2025-07-09 15:35:28,333 - INFO - ✅ f1监听器服务启动成功
2025-07-09 15:35:28,353 - INFO - ✅ 分区创建成功: entries_2025_07
2025-07-09 15:35:28,356 - INFO - ✅ 分区创建成功: entries_2025_08
2025-07-09 15:35:28,357 - INFO - ✅ PostgreSQL连接池创建成功
2025-07-09 15:35:28,357 - INFO - ✅ f2推送回写服务启动成功，工作线程数: 4
2025-07-09 15:35:28,357 - INFO - 🚀 推送工作线程启动: worker_1
2025-07-09 15:35:28,357 - INFO - 🚀 推送工作线程启动: worker_2
2025-07-09 15:35:28,357 - INFO - 🚀 推送工作线程启动: worker_3
2025-07-09 15:35:28,358 - INFO - 🚀 推送工作线程启动: worker_4
2025-07-09 15:35:28,391 - INFO - ✅ PostgreSQL连接池创建成功
2025-07-09 15:35:28,392 - INFO - ✅ f4操作处理服务启动成功
2025-07-09 15:35:28,392 - INFO - 🔌 推送工作线程停止: worker_4
2025-07-09 15:35:28,392 - INFO - 🔌 推送工作线程停止: worker_3
2025-07-09 15:35:28,394 - INFO - 🔌 推送工作线程停止: worker_1
2025-07-09 15:35:28,394 - INFO - 🔌 推送工作线程停止: worker_2
2025-07-09 15:36:48,048 - INFO - 🔊 f1监听器服务初始化完成
2025-07-09 15:36:48,048 - INFO - 📡 Server6客户端初始化 (新版): http://192.168.3.93:8009
2025-07-09 15:36:48,048 - INFO - 📤 f2推送回写引擎初始化完成 (修复版本: 直接轮询PostgreSQL)
2025-07-09 15:36:48,048 - INFO - 📡 Server6客户端初始化 (新版): http://192.168.3.93:8009
2025-07-09 15:36:48,050 - INFO - 📥 f3数据拉取器初始化完成 (Staging & UPSERT 模式)
2025-07-09 15:36:48,050 - INFO - 🎮 f4操作处理器初始化完成
2025-07-09 15:36:48,064 - INFO - Redis连接成功
2025-07-09 15:36:48,064 - INFO - Redis连接成功
2025-07-09 15:36:48,064 - INFO - Redis连接成功
2025-07-09 15:36:48,069 - INFO - ✅ MongoDB连接成功
2025-07-09 15:36:48,073 - INFO - ✅ MongoDB连接成功
2025-07-09 15:36:48,073 - INFO - ✅ MongoDB连接成功
2025-07-09 15:36:48,371 - INFO - ✅ PostgreSQL连接池创建成功
2025-07-09 15:36:48,371 - INFO - Redis连接成功
2025-07-09 15:36:48,372 - INFO - ✅ f3数据拉取服务启动成功，预定执行时间: ['02:00', '03:00']
2025-07-09 15:36:48,372 - INFO - 🚀 数据拉取调度器启动，预定运行时间: ['02:00', '03:00']
2025-07-09 15:36:48,372 - INFO - 下一次f3/f5同步任务将在 2025-07-10 02:00:00 执行，等待 37392 秒...
2025-07-09 15:36:48,375 - INFO - ✅ PostgreSQL连接池创建成功
2025-07-09 15:36:48,375 - INFO - ✅ f1监听器服务启动成功
2025-07-09 15:36:48,397 - INFO - ✅ PostgreSQL连接池创建成功
2025-07-09 15:36:48,397 - INFO - ✅ f2推送回写服务启动成功，工作线程数: 4
2025-07-09 15:36:48,398 - INFO - 🚀 推送工作线程启动: worker_1
2025-07-09 15:36:48,398 - INFO - 🚀 推送工作线程启动: worker_2
2025-07-09 15:36:48,398 - INFO - 🚀 推送工作线程启动: worker_3
2025-07-09 15:36:48,398 - INFO - 🚀 推送工作线程启动: worker_4
2025-07-09 15:36:48,401 - INFO - ✅ 分区创建成功: entries_2025_07
2025-07-09 15:36:48,404 - INFO - ✅ 分区创建成功: entries_2025_08
2025-07-09 15:36:48,462 - INFO - ✅ PostgreSQL连接池创建成功
2025-07-09 15:36:48,462 - INFO - ✅ f4操作处理服务启动成功
2025-07-09 15:36:48,462 - INFO - 🔌 推送工作线程停止: worker_4
2025-07-09 15:36:48,462 - INFO - 🔌 推送工作线程停止: worker_3
2025-07-09 15:36:48,464 - INFO - 🔌 推送工作线程停止: worker_1
2025-07-09 15:36:48,464 - INFO - 🔌 推送工作线程停止: worker_2
2025-07-09 15:52:27,066 - INFO - 🔊 f1监听器服务初始化完成
2025-07-09 15:52:27,066 - INFO - 📡 Server6客户端初始化 (新版): http://192.168.3.93:8009
2025-07-09 15:52:27,066 - INFO - 📤 f2推送回写引擎初始化完成 (修复版本: 直接轮询PostgreSQL)
2025-07-09 15:52:27,066 - INFO - 📡 Server6客户端初始化 (新版): http://192.168.3.93:8009
2025-07-09 15:52:27,067 - INFO - 📥 f3数据拉取器初始化完成 (Staging & UPSERT 模式)
2025-07-09 15:52:27,067 - INFO - 🎮 f4操作处理器初始化完成
2025-07-09 15:52:27,078 - INFO - Redis连接成功
2025-07-09 15:52:27,078 - INFO - Redis连接成功
2025-07-09 15:52:27,078 - INFO - Redis连接成功
2025-07-09 15:52:27,084 - INFO - ✅ MongoDB连接成功
2025-07-09 15:52:27,086 - INFO - ✅ MongoDB连接成功
2025-07-09 15:52:27,087 - INFO - ✅ MongoDB连接成功
2025-07-09 15:52:27,360 - INFO - ✅ PostgreSQL连接池创建成功
2025-07-09 15:52:27,361 - INFO - Redis连接成功
2025-07-09 15:52:27,361 - INFO - ✅ f3数据拉取服务启动成功，预定执行时间: ['02:00', '03:00']
2025-07-09 15:52:27,361 - INFO - 🚀 数据拉取调度器启动，预定运行时间: ['02:00', '03:00']
2025-07-09 15:52:27,361 - INFO - 下一次f3/f5同步任务将在 2025-07-10 02:00:00 执行，等待 36453 秒...
2025-07-09 15:52:27,398 - INFO - ✅ PostgreSQL连接池创建成功
2025-07-09 15:52:27,398 - INFO - ✅ f1监听器服务启动成功
2025-07-09 15:52:27,420 - INFO - ✅ 分区创建成功: entries_2025_07
2025-07-09 15:52:27,424 - INFO - ✅ PostgreSQL连接池创建成功
2025-07-09 15:52:27,424 - INFO - ✅ f2推送回写服务启动成功，工作线程数: 4
2025-07-09 15:52:27,424 - INFO - 🚀 推送工作线程启动: worker_1
2025-07-09 15:52:27,424 - INFO - 🚀 推送工作线程启动: worker_2
2025-07-09 15:52:27,424 - INFO - 🚀 推送工作线程启动: worker_3
2025-07-09 15:52:27,425 - INFO - 🚀 推送工作线程启动: worker_4
2025-07-09 15:52:27,426 - INFO - ✅ 分区创建成功: entries_2025_08
2025-07-09 15:52:27,478 - INFO - ✅ PostgreSQL连接池创建成功
2025-07-09 15:52:27,478 - INFO - ✅ f4操作处理服务启动成功
2025-07-09 15:52:27,478 - INFO - 🔌 推送工作线程停止: worker_4
2025-07-09 15:52:27,478 - INFO - 🔌 推送工作线程停止: worker_3
2025-07-09 15:52:27,480 - INFO - 🔌 推送工作线程停止: worker_2
2025-07-09 15:52:27,480 - INFO - 🔌 推送工作线程停止: worker_1
2025-07-09 16:10:16,254 - INFO - MySuite Server5 - 数据同步微服务 (Ubuntu远程) v1.0.0 启动中...
2025-07-09 16:10:16,254 - INFO - 启动f1-f4服务...
2025-07-09 16:10:16,254 - INFO - 🔊 f1监听器服务初始化完成
2025-07-09 16:10:16,254 - INFO - 📡 Server6客户端初始化 (新版): http://192.168.3.93:8009
2025-07-09 16:10:16,254 - INFO - 📤 f2推送回写引擎初始化完成 (修复版本: 直接轮询PostgreSQL)
2025-07-09 16:10:16,255 - INFO - 📡 Server6客户端初始化 (新版): http://192.168.3.93:8009
2025-07-09 16:10:16,256 - INFO - 📥 f3数据拉取器初始化完成 (Staging & UPSERT 模式)
2025-07-09 16:10:16,256 - INFO - 🎮 f4操作处理器初始化完成
2025-07-09 16:10:16,256 - INFO - 启动 f1_listener...
2025-07-09 16:10:16,260 - INFO - Redis连接成功
2025-07-09 16:10:16,270 - INFO - ✅ MongoDB连接成功
2025-07-09 16:10:16,534 - INFO - ✅ PostgreSQL连接池创建成功
2025-07-09 16:10:16,534 - INFO - ✅ f1监听器服务启动成功
2025-07-09 16:10:16,536 - INFO - f1_listener 启动成功
2025-07-09 16:10:16,536 - INFO - 启动 f2_push_writer...
2025-07-09 16:10:16,538 - INFO - Redis连接成功
2025-07-09 16:10:16,544 - INFO - ✅ 分区创建成功: entries_2025_07
2025-07-09 16:10:16,548 - INFO - ✅ 分区创建成功: entries_2025_08
2025-07-09 16:10:16,549 - INFO - ✅ MongoDB连接成功
2025-07-09 16:10:16,674 - INFO - 📡 开始监听频道: push_job
2025-07-09 16:10:16,675 - INFO - 📡 开始监听频道: partition_check
2025-07-09 16:10:16,677 - INFO - 📡 开始监听频道: sync_trigger
2025-07-09 16:10:16,770 - INFO - ✅ PostgreSQL连接池创建成功
2025-07-09 16:10:16,770 - INFO - ✅ f2推送回写服务启动成功，工作线程数: 4
2025-07-09 16:10:16,770 - INFO - 🚀 推送工作线程启动: worker_1
2025-07-09 16:10:16,770 - INFO - 🚀 推送工作线程启动: worker_2
2025-07-09 16:10:16,770 - INFO - 🚀 推送工作线程启动: worker_3
2025-07-09 16:10:16,771 - INFO - 🚀 推送工作线程启动: worker_4
2025-07-09 16:10:16,772 - INFO - f2_push_writer 启动成功
2025-07-09 16:10:16,772 - INFO - 启动 f3_data_puller...
2025-07-09 16:10:17,086 - INFO - ✅ PostgreSQL连接池创建成功
2025-07-09 16:10:17,087 - INFO - Redis连接成功
2025-07-09 16:10:17,087 - INFO - ✅ f3数据拉取服务启动成功，预定执行时间: ['02:00', '03:00']
2025-07-09 16:10:17,087 - INFO - f3_data_puller 启动成功
2025-07-09 16:10:17,087 - INFO - 启动 f4_operation_handler...
2025-07-09 16:10:17,087 - INFO - 🚀 数据拉取调度器启动，预定运行时间: ['02:00', '03:00']
2025-07-09 16:10:17,087 - INFO - 下一次f3/f5同步任务将在 2025-07-10 02:00:00 执行，等待 35383 秒...
2025-07-09 16:10:17,090 - INFO - Redis连接成功
2025-07-09 16:10:17,101 - INFO - ✅ MongoDB连接成功
2025-07-09 16:10:17,374 - INFO - ✅ PostgreSQL连接池创建成功
2025-07-09 16:10:17,374 - INFO - ✅ f4操作处理服务启动成功
2025-07-09 16:10:17,374 - INFO - f4_operation_handler 启动成功
2025-07-09 16:10:17,374 - INFO - 服务启动完成，已启动: ['f1_listener', 'f2_push_writer', 'f3_data_puller', 'f4_operation_handler']
2025-07-09 16:10:17,375 - INFO - 🔄 正在停止所有f1-f4服务...
2025-07-09 16:10:17,375 - INFO - 🔄 停止 f4_operation_handler...
2025-07-09 16:10:17,376 - INFO - 🔌 MongoDB连接已关闭
2025-07-09 16:10:17,376 - INFO - 🔌 Redis连接已关闭
2025-07-09 16:10:17,379 - INFO - 🔌 PostgreSQL连接已关闭
2025-07-09 16:10:17,380 - INFO - 🔌 f4操作处理服务已停止
2025-07-09 16:10:17,380 - INFO - ✅ f4_operation_handler 已停止
2025-07-09 16:10:17,380 - INFO - 🔄 停止 f3_data_puller...
2025-07-09 16:10:17,383 - INFO - 🔌 PostgreSQL连接已关闭
2025-07-09 16:10:17,383 - INFO - 🔌 Redis连接已关闭
2025-07-09 16:10:17,383 - INFO - 🔌 f3数据拉取服务已停止
2025-07-09 16:10:17,383 - INFO - ✅ f3_data_puller 已停止
2025-07-09 16:10:17,383 - INFO - 🔄 停止 f2_push_writer...
2025-07-09 16:10:17,385 - INFO - 🔌 推送工作线程停止: worker_1
2025-07-09 16:10:17,385 - INFO - 🔌 推送工作线程停止: worker_2
2025-07-09 16:10:17,385 - INFO - 🔌 推送工作线程停止: worker_4
2025-07-09 16:10:17,385 - INFO - 🔌 推送工作线程停止: worker_3
2025-07-09 16:10:17,386 - INFO - 🔌 MongoDB连接已关闭
2025-07-09 16:10:17,386 - INFO - 🔌 Redis连接已关闭
2025-07-09 16:10:17,391 - INFO - 🔌 PostgreSQL连接已关闭
2025-07-09 16:10:17,391 - INFO - 🔌 f2推送回写服务已停止
2025-07-09 16:10:17,391 - INFO - ✅ f2_push_writer 已停止
2025-07-09 16:10:17,391 - INFO - 🔄 停止 f1_listener...
2025-07-09 16:10:17,392 - INFO - 🔌 MongoDB连接已关闭
2025-07-09 16:10:17,392 - INFO - 监听任务被取消
2025-07-09 16:10:17,392 - INFO - 🔌 Redis连接已关闭
2025-07-09 16:10:17,395 - INFO - 🔌 PostgreSQL连接已关闭
2025-07-09 16:10:17,395 - INFO - 🔌 f1监听器服务已停止
2025-07-09 16:10:17,396 - INFO - ✅ f1_listener 已停止
2025-07-09 16:10:18,846 - INFO - 🔊 f1监听器服务初始化完成
2025-07-09 16:10:18,846 - INFO - 📡 Server6客户端初始化 (新版): http://192.168.3.93:8009
2025-07-09 16:10:18,847 - INFO - 📤 f2推送回写引擎初始化完成 (修复版本: 直接轮询PostgreSQL)
2025-07-09 16:10:18,847 - INFO - 📡 Server6客户端初始化 (新版): http://192.168.3.93:8009
2025-07-09 16:10:18,847 - INFO - 📥 f3数据拉取器初始化完成 (Staging & UPSERT 模式)
2025-07-09 16:10:18,847 - INFO - 🎮 f4操作处理器初始化完成
2025-07-09 16:10:18,859 - INFO - Redis连接成功
2025-07-09 16:10:18,860 - INFO - Redis连接成功
2025-07-09 16:10:18,860 - INFO - Redis连接成功
2025-07-09 16:10:18,865 - INFO - ✅ MongoDB连接成功
2025-07-09 16:10:18,866 - INFO - ✅ MongoDB连接成功
2025-07-09 16:10:18,867 - INFO - ✅ MongoDB连接成功
2025-07-09 16:10:19,104 - INFO - ✅ PostgreSQL连接池创建成功
2025-07-09 16:10:19,105 - INFO - Redis连接成功
2025-07-09 16:10:19,105 - INFO - ✅ f3数据拉取服务启动成功，预定执行时间: ['02:00', '03:00']
2025-07-09 16:10:19,105 - INFO - 🚀 数据拉取调度器启动，预定运行时间: ['02:00', '03:00']
2025-07-09 16:10:19,105 - INFO - 下一次f3/f5同步任务将在 2025-07-10 02:00:00 执行，等待 35381 秒...
2025-07-09 16:10:19,146 - INFO - ✅ PostgreSQL连接池创建成功
2025-07-09 16:10:19,146 - INFO - ✅ f1监听器服务启动成功
2025-07-09 16:10:19,155 - INFO - ✅ 分区创建成功: entries_2025_07
2025-07-09 16:10:19,175 - INFO - ✅ 分区创建成功: entries_2025_08
2025-07-09 16:10:19,178 - INFO - ✅ PostgreSQL连接池创建成功
2025-07-09 16:10:19,178 - INFO - ✅ f2推送回写服务启动成功，工作线程数: 4
2025-07-09 16:10:19,178 - INFO - 🚀 推送工作线程启动: worker_1
2025-07-09 16:10:19,178 - INFO - 🚀 推送工作线程启动: worker_2
2025-07-09 16:10:19,178 - INFO - 🚀 推送工作线程启动: worker_3
2025-07-09 16:10:19,179 - INFO - 🚀 推送工作线程启动: worker_4
2025-07-09 16:10:19,219 - INFO - ✅ PostgreSQL连接池创建成功
2025-07-09 16:10:19,219 - INFO - ✅ f4操作处理服务启动成功
2025-07-09 16:10:19,219 - INFO - 🔌 推送工作线程停止: worker_4
2025-07-09 16:10:19,219 - INFO - 🔌 推送工作线程停止: worker_3
2025-07-09 16:10:19,221 - INFO - 🔌 推送工作线程停止: worker_2
2025-07-09 16:10:19,221 - INFO - 🔌 推送工作线程停止: worker_1
2025-07-09 16:11:06,772 - INFO - MySuite Server5 - 数据同步微服务 (Ubuntu远程) v1.0.0 启动中...
2025-07-09 16:11:06,772 - INFO - 启动f1-f4服务...
2025-07-09 16:11:06,772 - INFO - 🔊 f1监听器服务初始化完成
2025-07-09 16:11:06,773 - INFO - 📡 Server6客户端初始化 (新版): http://192.168.3.93:8009
2025-07-09 16:11:06,773 - INFO - 📤 f2推送回写引擎初始化完成 (修复版本: 直接轮询PostgreSQL)
2025-07-09 16:11:06,773 - INFO - 📡 Server6客户端初始化 (新版): http://192.168.3.93:8009
2025-07-09 16:11:06,774 - INFO - 📥 f3数据拉取器初始化完成 (Staging & UPSERT 模式)
2025-07-09 16:11:06,774 - INFO - 🎮 f4操作处理器初始化完成
2025-07-09 16:11:06,774 - INFO - 启动 f1_listener...
2025-07-09 16:11:06,779 - INFO - Redis连接成功
2025-07-09 16:11:06,786 - INFO - ✅ MongoDB连接成功
2025-07-09 16:11:07,010 - INFO - ✅ PostgreSQL连接池创建成功
2025-07-09 16:11:07,010 - INFO - ✅ f1监听器服务启动成功
2025-07-09 16:11:07,012 - INFO - f1_listener 启动成功
2025-07-09 16:11:07,012 - INFO - 启动 f2_push_writer...
2025-07-09 16:11:07,016 - INFO - Redis连接成功
2025-07-09 16:11:07,019 - INFO - ✅ 分区创建成功: entries_2025_07
2025-07-09 16:11:07,022 - INFO - ✅ 分区创建成功: entries_2025_08
2025-07-09 16:11:07,024 - INFO - ✅ MongoDB连接成功
2025-07-09 16:11:07,141 - INFO - 📡 开始监听频道: push_job
2025-07-09 16:11:07,142 - INFO - 📡 开始监听频道: partition_check
2025-07-09 16:11:07,144 - INFO - 📡 开始监听频道: sync_trigger
2025-07-09 16:11:07,266 - INFO - ✅ PostgreSQL连接池创建成功
2025-07-09 16:11:07,266 - INFO - ✅ f2推送回写服务启动成功，工作线程数: 4
2025-07-09 16:11:07,266 - INFO - 🚀 推送工作线程启动: worker_1
2025-07-09 16:11:07,266 - INFO - 🚀 推送工作线程启动: worker_2
2025-07-09 16:11:07,266 - INFO - 🚀 推送工作线程启动: worker_3
2025-07-09 16:11:07,267 - INFO - 🚀 推送工作线程启动: worker_4
2025-07-09 16:11:07,268 - INFO - f2_push_writer 启动成功
2025-07-09 16:11:07,268 - INFO - 启动 f3_data_puller...
2025-07-09 16:11:07,557 - INFO - ✅ PostgreSQL连接池创建成功
2025-07-09 16:11:07,559 - INFO - Redis连接成功
2025-07-09 16:11:07,559 - INFO - ✅ f3数据拉取服务启动成功，预定执行时间: ['02:00', '03:00']
2025-07-09 16:11:07,559 - INFO - f3_data_puller 启动成功
2025-07-09 16:11:07,559 - INFO - 启动 f4_operation_handler...
2025-07-09 16:11:07,559 - INFO - 🚀 数据拉取调度器启动，预定运行时间: ['02:00', '03:00']
2025-07-09 16:11:07,559 - INFO - 下一次f3/f5同步任务将在 2025-07-10 02:00:00 执行，等待 35332 秒...
2025-07-09 16:11:07,563 - INFO - Redis连接成功
2025-07-09 16:11:07,573 - INFO - ✅ MongoDB连接成功
2025-07-09 16:11:07,811 - INFO - ✅ PostgreSQL连接池创建成功
2025-07-09 16:11:07,811 - INFO - ✅ f4操作处理服务启动成功
2025-07-09 16:11:07,811 - INFO - f4_operation_handler 启动成功
2025-07-09 16:11:07,811 - INFO - 服务启动完成，已启动: ['f1_listener', 'f2_push_writer', 'f3_data_puller', 'f4_operation_handler']
2025-07-09 16:11:07,812 - INFO - 🔄 正在停止所有f1-f4服务...
2025-07-09 16:11:07,812 - INFO - 🔄 停止 f4_operation_handler...
2025-07-09 16:11:07,813 - INFO - 🔌 MongoDB连接已关闭
2025-07-09 16:11:07,813 - INFO - 🔌 Redis连接已关闭
2025-07-09 16:11:07,817 - INFO - 🔌 PostgreSQL连接已关闭
2025-07-09 16:11:07,817 - INFO - 🔌 f4操作处理服务已停止
2025-07-09 16:11:07,817 - INFO - ✅ f4_operation_handler 已停止
2025-07-09 16:11:07,817 - INFO - 🔄 停止 f3_data_puller...
2025-07-09 16:11:07,821 - INFO - 🔌 PostgreSQL连接已关闭
2025-07-09 16:11:07,821 - INFO - 🔌 Redis连接已关闭
2025-07-09 16:11:07,821 - INFO - 🔌 f3数据拉取服务已停止
2025-07-09 16:11:07,821 - INFO - ✅ f3_data_puller 已停止
2025-07-09 16:11:07,821 - INFO - 🔄 停止 f2_push_writer...
2025-07-09 16:11:07,822 - INFO - 🔌 推送工作线程停止: worker_2
2025-07-09 16:11:07,822 - INFO - 🔌 推送工作线程停止: worker_1
2025-07-09 16:11:07,823 - INFO - 🔌 推送工作线程停止: worker_3
2025-07-09 16:11:07,823 - INFO - 🔌 推送工作线程停止: worker_4
2025-07-09 16:11:07,824 - INFO - 🔌 MongoDB连接已关闭
2025-07-09 16:11:07,824 - INFO - 🔌 Redis连接已关闭
2025-07-09 16:11:07,827 - INFO - 🔌 PostgreSQL连接已关闭
2025-07-09 16:11:07,828 - INFO - 🔌 f2推送回写服务已停止
2025-07-09 16:11:07,828 - INFO - ✅ f2_push_writer 已停止
2025-07-09 16:11:07,828 - INFO - 🔄 停止 f1_listener...
2025-07-09 16:11:07,828 - INFO - 🔌 MongoDB连接已关闭
2025-07-09 16:11:07,829 - INFO - 监听任务被取消
2025-07-09 16:11:07,829 - INFO - 🔌 Redis连接已关闭
2025-07-09 16:11:07,832 - INFO - 🔌 PostgreSQL连接已关闭
2025-07-09 16:11:07,832 - INFO - 🔌 f1监听器服务已停止
2025-07-09 16:11:07,833 - INFO - ✅ f1_listener 已停止
2025-07-09 16:11:09,291 - INFO - 🔊 f1监听器服务初始化完成
2025-07-09 16:11:09,291 - INFO - 📡 Server6客户端初始化 (新版): http://192.168.3.93:8009
2025-07-09 16:11:09,292 - INFO - 📤 f2推送回写引擎初始化完成 (修复版本: 直接轮询PostgreSQL)
2025-07-09 16:11:09,292 - INFO - 📡 Server6客户端初始化 (新版): http://192.168.3.93:8009
2025-07-09 16:11:09,292 - INFO - 📥 f3数据拉取器初始化完成 (Staging & UPSERT 模式)
2025-07-09 16:11:09,292 - INFO - 🎮 f4操作处理器初始化完成
2025-07-09 16:11:09,301 - INFO - Redis连接成功
2025-07-09 16:11:09,301 - INFO - Redis连接成功
2025-07-09 16:11:09,301 - INFO - Redis连接成功
2025-07-09 16:11:09,306 - INFO - ✅ MongoDB连接成功
2025-07-09 16:11:09,307 - INFO - ✅ MongoDB连接成功
2025-07-09 16:11:09,309 - INFO - ✅ MongoDB连接成功
2025-07-09 16:11:09,557 - INFO - ✅ PostgreSQL连接池创建成功
2025-07-09 16:11:09,558 - INFO - Redis连接成功
2025-07-09 16:11:09,558 - INFO - ✅ f3数据拉取服务启动成功，预定执行时间: ['02:00', '03:00']
2025-07-09 16:11:09,558 - INFO - 🚀 数据拉取调度器启动，预定运行时间: ['02:00', '03:00']
2025-07-09 16:11:09,558 - INFO - 下一次f3/f5同步任务将在 2025-07-10 02:00:00 执行，等待 35330 秒...
2025-07-09 16:11:09,614 - INFO - ✅ PostgreSQL连接池创建成功
2025-07-09 16:11:09,614 - INFO - ✅ f1监听器服务启动成功
2025-07-09 16:11:09,624 - INFO - ✅ 分区创建成功: entries_2025_07
2025-07-09 16:11:09,628 - INFO - ✅ 分区创建成功: entries_2025_08
2025-07-09 16:11:09,652 - INFO - ✅ PostgreSQL连接池创建成功
2025-07-09 16:11:09,653 - INFO - ✅ f2推送回写服务启动成功，工作线程数: 4
2025-07-09 16:11:09,653 - INFO - 🚀 推送工作线程启动: worker_1
2025-07-09 16:11:09,653 - INFO - 🚀 推送工作线程启动: worker_2
2025-07-09 16:11:09,653 - INFO - 🚀 推送工作线程启动: worker_3
2025-07-09 16:11:09,654 - INFO - 🚀 推送工作线程启动: worker_4
2025-07-09 16:11:09,683 - INFO - ✅ PostgreSQL连接池创建成功
2025-07-09 16:11:09,683 - INFO - ✅ f4操作处理服务启动成功
2025-07-09 16:11:09,683 - INFO - 🔌 推送工作线程停止: worker_3
2025-07-09 16:11:09,684 - INFO - 🔌 推送工作线程停止: worker_4
2025-07-09 16:11:09,685 - INFO - 🔌 推送工作线程停止: worker_2
2025-07-09 16:11:09,685 - INFO - 🔌 推送工作线程停止: worker_1
2025-07-09 16:11:47,086 - INFO - MySuite Server5 - 数据同步微服务 (Ubuntu远程) v1.0.0 启动中...
2025-07-09 16:11:47,086 - INFO - 启动f1-f4服务...
2025-07-09 16:11:47,086 - INFO - 🔊 f1监听器服务初始化完成
2025-07-09 16:11:47,087 - INFO - 📡 Server6客户端初始化 (新版): http://192.168.3.93:8009
2025-07-09 16:11:47,087 - INFO - 📤 f2推送回写引擎初始化完成 (修复版本: 直接轮询PostgreSQL)
2025-07-09 16:11:47,087 - INFO - 📡 Server6客户端初始化 (新版): http://192.168.3.93:8009
2025-07-09 16:11:47,088 - INFO - 📥 f3数据拉取器初始化完成 (Staging & UPSERT 模式)
2025-07-09 16:11:47,088 - INFO - 🎮 f4操作处理器初始化完成
2025-07-09 16:11:47,088 - INFO - 启动 f1_listener...
2025-07-09 16:11:47,092 - INFO - Redis连接成功
2025-07-09 16:11:47,104 - INFO - ✅ MongoDB连接成功
2025-07-09 16:11:47,322 - INFO - ✅ PostgreSQL连接池创建成功
2025-07-09 16:11:47,322 - INFO - ✅ f1监听器服务启动成功
2025-07-09 16:11:47,328 - INFO - f1_listener 启动成功
2025-07-09 16:11:47,328 - INFO - 启动 f2_push_writer...
2025-07-09 16:11:47,336 - INFO - Redis连接成功
2025-07-09 16:11:47,337 - INFO - ✅ 分区创建成功: entries_2025_07
2025-07-09 16:11:47,341 - INFO - ✅ 分区创建成功: entries_2025_08
2025-07-09 16:11:47,346 - INFO - ✅ MongoDB连接成功
2025-07-09 16:11:47,466 - INFO - 📡 开始监听频道: push_job
2025-07-09 16:11:47,467 - INFO - 📡 开始监听频道: partition_check
2025-07-09 16:11:47,469 - INFO - 📡 开始监听频道: sync_trigger
2025-07-09 16:11:47,629 - INFO - ✅ PostgreSQL连接池创建成功
2025-07-09 16:11:47,630 - INFO - ✅ f2推送回写服务启动成功，工作线程数: 4
2025-07-09 16:11:47,630 - INFO - 🚀 推送工作线程启动: worker_1
2025-07-09 16:11:47,630 - INFO - 🚀 推送工作线程启动: worker_2
2025-07-09 16:11:47,630 - INFO - 🚀 推送工作线程启动: worker_3
2025-07-09 16:11:47,631 - INFO - 🚀 推送工作线程启动: worker_4
2025-07-09 16:11:47,631 - INFO - f2_push_writer 启动成功
2025-07-09 16:11:47,632 - INFO - 启动 f3_data_puller...
2025-07-09 16:11:47,896 - INFO - ✅ PostgreSQL连接池创建成功
2025-07-09 16:11:47,897 - INFO - Redis连接成功
2025-07-09 16:11:47,897 - INFO - ✅ f3数据拉取服务启动成功，预定执行时间: ['02:00', '03:00']
2025-07-09 16:11:47,897 - INFO - f3_data_puller 启动成功
2025-07-09 16:11:47,897 - INFO - 启动 f4_operation_handler...
2025-07-09 16:11:47,897 - INFO - 🚀 数据拉取调度器启动，预定运行时间: ['02:00', '03:00']
2025-07-09 16:11:47,897 - INFO - 下一次f3/f5同步任务将在 2025-07-10 02:00:00 执行，等待 35292 秒...
2025-07-09 16:11:47,900 - INFO - Redis连接成功
2025-07-09 16:11:47,909 - INFO - ✅ MongoDB连接成功
2025-07-09 16:11:48,172 - INFO - ✅ PostgreSQL连接池创建成功
2025-07-09 16:11:48,172 - INFO - ✅ f4操作处理服务启动成功
2025-07-09 16:11:48,172 - INFO - f4_operation_handler 启动成功
2025-07-09 16:11:48,172 - INFO - 服务启动完成，已启动: ['f1_listener', 'f2_push_writer', 'f3_data_puller', 'f4_operation_handler']
2025-07-09 16:11:49,671 - INFO - 🔊 f1监听器服务初始化完成
2025-07-09 16:11:49,671 - INFO - 📡 Server6客户端初始化 (新版): http://192.168.3.93:8009
2025-07-09 16:11:49,671 - INFO - 📤 f2推送回写引擎初始化完成 (修复版本: 直接轮询PostgreSQL)
2025-07-09 16:11:49,672 - INFO - 📡 Server6客户端初始化 (新版): http://192.168.3.93:8009
2025-07-09 16:11:49,672 - INFO - 📥 f3数据拉取器初始化完成 (Staging & UPSERT 模式)
2025-07-09 16:11:49,672 - INFO - 🎮 f4操作处理器初始化完成
2025-07-09 16:11:49,685 - INFO - Redis连接成功
2025-07-09 16:11:49,685 - INFO - Redis连接成功
2025-07-09 16:11:49,685 - INFO - Redis连接成功
2025-07-09 16:11:49,691 - INFO - ✅ MongoDB连接成功
2025-07-09 16:11:49,691 - INFO - ✅ MongoDB连接成功
2025-07-09 16:11:49,691 - INFO - ✅ MongoDB连接成功
2025-07-09 16:11:49,987 - INFO - ✅ PostgreSQL连接池创建成功
2025-07-09 16:11:49,987 - INFO - Redis连接成功
2025-07-09 16:11:49,987 - INFO - ✅ f3数据拉取服务启动成功，预定执行时间: ['02:00', '03:00']
2025-07-09 16:11:49,987 - INFO - 🚀 数据拉取调度器启动，预定运行时间: ['02:00', '03:00']
2025-07-09 16:11:49,987 - INFO - 下一次f3/f5同步任务将在 2025-07-10 02:00:00 执行，等待 35290 秒...
2025-07-09 16:11:50,010 - INFO - ✅ PostgreSQL连接池创建成功
2025-07-09 16:11:50,010 - INFO - ✅ f1监听器服务启动成功
2025-07-09 16:11:50,033 - INFO - ✅ 分区创建成功: entries_2025_07
2025-07-09 16:11:50,036 - INFO - ✅ 分区创建成功: entries_2025_08
2025-07-09 16:11:50,037 - INFO - ✅ PostgreSQL连接池创建成功
2025-07-09 16:11:50,037 - INFO - ✅ f2推送回写服务启动成功，工作线程数: 4
2025-07-09 16:11:50,037 - INFO - 🚀 推送工作线程启动: worker_1
2025-07-09 16:11:50,037 - INFO - 🚀 推送工作线程启动: worker_2
2025-07-09 16:11:50,037 - INFO - 🚀 推送工作线程启动: worker_3
2025-07-09 16:11:50,038 - INFO - 🚀 推送工作线程启动: worker_4
2025-07-09 16:11:50,060 - INFO - ✅ PostgreSQL连接池创建成功
2025-07-09 16:11:50,060 - INFO - ✅ f4操作处理服务启动成功
2025-07-09 16:11:50,060 - INFO - 🔌 推送工作线程停止: worker_4
2025-07-09 16:11:50,061 - INFO - 🔌 推送工作线程停止: worker_3
2025-07-09 16:11:50,216 - INFO - 🔌 推送工作线程停止: worker_2
2025-07-09 16:11:50,249 - INFO - 🔌 推送工作线程停止: worker_1
2025-07-09 16:12:02,330 - INFO - ✅ PostgreSQL连接池创建成功
2025-07-09 16:12:02,331 - INFO - 🔥🔥🔥 chart-data 端点被调用 - 新版本代码！
2025-07-09 16:12:02,331 - INFO - === chart-data 端点被调用 ===
2025-07-09 16:12:02,331 - INFO - 📊 请求参数: employee_id=215829, start_date=2025-07-01, end_date=2025-07-31
2025-07-09 16:12:02,394 - INFO - 📊 timeprotab表结构: ['id', 'employee_id', '日付', '星期', 'ｶﾚﾝﾀﾞ', '不在', '勤務区分', '事由', '出勤時刻', 'ＭＣ_出勤', '退勤時刻', 'ＭＣ_退勤', '所定時間', '早出残業', '内深夜残業', '遅刻早退', '休出時間', '出張残業', '外出時間', '戻り時間', 'コメント', 'created_at', 'updated_at']
2025-07-09 16:12:02,394 - INFO - 📊 执行timeprotab查询: employee_id=215829, start_date=2025-07-01, end_date=2025-07-31
2025-07-09 16:12:02,399 - INFO - 📊 timeprotab查询结果: 31条记录
2025-07-09 16:12:02,399 - INFO - 📊 第一条timeprotab记录: {'日付': datetime.date(2025, 7, 1), '所定時間': '07:50', '早出残業': None, '出勤時刻': datetime.time(7, 18), '退勤時刻': datetime.time(19, 25)}
2025-07-09 16:12:02,400 - INFO - 📊 获取图表数据成功: 31个数据点 (entries: 1, timeprotab: 31)
2025-07-09 16:12:02,400 - INFO - 📊 entries数据: {datetime.date(2025, 7, 1): 9.0}
2025-07-09 16:12:02,400 - INFO - 📊 timeprotab数据: {datetime.date(2025, 7, 1): 8.0, datetime.date(2025, 7, 2): 8.0, datetime.date(2025, 7, 3): 8.0, datetime.date(2025, 7, 4): 0.0, datetime.date(2025, 7, 5): 0.0, datetime.date(2025, 7, 6): 0.0, datetime.date(2025, 7, 7): 0.0, datetime.date(2025, 7, 8): 0.0, datetime.date(2025, 7, 9): 0.0, datetime.date(2025, 7, 10): 0.0, datetime.date(2025, 7, 11): 0.0, datetime.date(2025, 7, 12): 0.0, datetime.date(2025, 7, 13): 0.0, datetime.date(2025, 7, 14): 0.0, datetime.date(2025, 7, 15): 0.0, datetime.date(2025, 7, 16): 0.0, datetime.date(2025, 7, 17): 0.0, datetime.date(2025, 7, 18): 0.0, datetime.date(2025, 7, 19): 0.0, datetime.date(2025, 7, 20): 0.0, datetime.date(2025, 7, 21): 0.0, datetime.date(2025, 7, 22): 0.0, datetime.date(2025, 7, 23): 0.0, datetime.date(2025, 7, 24): 0.0, datetime.date(2025, 7, 25): 0.0, datetime.date(2025, 7, 26): 0.0, datetime.date(2025, 7, 27): 0.0, datetime.date(2025, 7, 28): 0.0, datetime.date(2025, 7, 29): 0.0, datetime.date(2025, 7, 30): 0.0, datetime.date(2025, 7, 31): 0.0}
2025-07-09 16:12:02,403 - INFO - 🔌 PostgreSQL连接已关闭
2025-07-09 16:13:09,003 - INFO - ✅ PostgreSQL连接池创建成功
2025-07-09 16:13:09,012 - INFO - 📋 获取timeprotab成功: 31条记录
2025-07-09 16:13:09,016 - INFO - 🔌 PostgreSQL连接已关闭
2025-07-09 16:13:09,273 - INFO - ✅ PostgreSQL连接池创建成功
2025-07-09 16:13:09,375 - INFO - 📅 获取可用月份成功: 33个月份 (employee_id=215829)
2025-07-09 16:13:09,383 - INFO - 🔌 PostgreSQL连接已关闭
2025-07-09 16:13:09,755 - INFO - ✅ PostgreSQL连接池创建成功
2025-07-09 16:13:09,855 - INFO - 📅 获取可用月份成功: 33个月份 (employee_id=215829)
2025-07-09 16:13:09,861 - INFO - 🔌 PostgreSQL连接已关闭
2025-07-09 16:13:10,969 - INFO - ✅ PostgreSQL连接池创建成功
2025-07-09 16:13:10,970 - INFO - 🔥🔥🔥 chart-data 端点被调用 - 新版本代码！
2025-07-09 16:13:10,970 - INFO - === chart-data 端点被调用 ===
2025-07-09 16:13:10,970 - INFO - 📊 请求参数: employee_id=215829, start_date=2025-06-01, end_date=2025-06-30
2025-07-09 16:13:10,997 - INFO - 📊 timeprotab表结构: ['id', 'employee_id', '日付', '星期', 'ｶﾚﾝﾀﾞ', '不在', '勤務区分', '事由', '出勤時刻', 'ＭＣ_出勤', '退勤時刻', 'ＭＣ_退勤', '所定時間', '早出残業', '内深夜残業', '遅刻早退', '休出時間', '出張残業', '外出時間', '戻り時間', 'コメント', 'created_at', 'updated_at']
2025-07-09 16:13:10,997 - INFO - 📊 执行timeprotab查询: employee_id=215829, start_date=2025-06-01, end_date=2025-06-30
2025-07-09 16:13:11,004 - INFO - 📊 timeprotab查询结果: 30条记录
2025-07-09 16:13:11,004 - INFO - 📊 第一条timeprotab记录: {'日付': datetime.date(2025, 6, 1), '所定時間': None, '早出残業': None, '出勤時刻': None, '退勤時刻': None}
2025-07-09 16:13:11,004 - INFO - 📊 获取图表数据成功: 30个数据点 (entries: 21, timeprotab: 30)
2025-07-09 16:13:11,005 - INFO - 📊 entries数据: {datetime.date(2025, 6, 2): 9.0, datetime.date(2025, 6, 3): 9.0, datetime.date(2025, 6, 4): 9.0, datetime.date(2025, 6, 5): 9.0, datetime.date(2025, 6, 6): 9.0, datetime.date(2025, 6, 9): 9.0, datetime.date(2025, 6, 10): 9.0, datetime.date(2025, 6, 11): 9.0, datetime.date(2025, 6, 12): 9.0, datetime.date(2025, 6, 13): 9.0, datetime.date(2025, 6, 16): 9.0, datetime.date(2025, 6, 17): 9.0, datetime.date(2025, 6, 18): 10.5, datetime.date(2025, 6, 19): 10.0, datetime.date(2025, 6, 20): 8.0, datetime.date(2025, 6, 23): 10.5, datetime.date(2025, 6, 24): 8.0, datetime.date(2025, 6, 25): 9.0, datetime.date(2025, 6, 26): 9.0, datetime.date(2025, 6, 27): 10.5, datetime.date(2025, 6, 30): 10.0}
2025-07-09 16:13:11,005 - INFO - 📊 timeprotab数据: {datetime.date(2025, 6, 1): 0.0, datetime.date(2025, 6, 2): 9.0, datetime.date(2025, 6, 3): 9.0, datetime.date(2025, 6, 4): 9.0, datetime.date(2025, 6, 5): 5.0, datetime.date(2025, 6, 6): 9.0, datetime.date(2025, 6, 7): 0.0, datetime.date(2025, 6, 8): 0.0, datetime.date(2025, 6, 9): 9.0, datetime.date(2025, 6, 10): 9.0, datetime.date(2025, 6, 11): 9.0, datetime.date(2025, 6, 12): 9.0, datetime.date(2025, 6, 13): 9.0, datetime.date(2025, 6, 14): 0.0, datetime.date(2025, 6, 15): 0.0, datetime.date(2025, 6, 16): 9.0, datetime.date(2025, 6, 17): 9.0, datetime.date(2025, 6, 18): 10.5, datetime.date(2025, 6, 19): 10.0, datetime.date(2025, 6, 20): 8.0, datetime.date(2025, 6, 21): 0.0, datetime.date(2025, 6, 22): 0.0, datetime.date(2025, 6, 23): 10.5, datetime.date(2025, 6, 24): 8.0, datetime.date(2025, 6, 25): 9.0, datetime.date(2025, 6, 26): 9.0, datetime.date(2025, 6, 27): 10.5, datetime.date(2025, 6, 28): 0.0, datetime.date(2025, 6, 29): 0.0, datetime.date(2025, 6, 30): 10.0}
2025-07-09 16:13:11,009 - INFO - 🔌 PostgreSQL连接已关闭
2025-07-09 16:13:11,071 - INFO - ✅ PostgreSQL连接池创建成功
2025-07-09 16:13:11,071 - INFO - 🔥🔥🔥 chart-data 端点被调用 - 新版本代码！
2025-07-09 16:13:11,071 - INFO - === chart-data 端点被调用 ===
2025-07-09 16:13:11,071 - INFO - 📊 请求参数: employee_id=215829, start_date=2025-05-01, end_date=2025-05-31
2025-07-09 16:13:11,096 - INFO - 📊 timeprotab表结构: ['id', 'employee_id', '日付', '星期', 'ｶﾚﾝﾀﾞ', '不在', '勤務区分', '事由', '出勤時刻', 'ＭＣ_出勤', '退勤時刻', 'ＭＣ_退勤', '所定時間', '早出残業', '内深夜残業', '遅刻早退', '休出時間', '出張残業', '外出時間', '戻り時間', 'コメント', 'created_at', 'updated_at']
2025-07-09 16:13:11,096 - INFO - 📊 执行timeprotab查询: employee_id=215829, start_date=2025-05-01, end_date=2025-05-31
2025-07-09 16:13:11,111 - INFO - 📊 timeprotab查询结果: 31条记录
2025-07-09 16:13:11,111 - INFO - 📊 第一条timeprotab记录: {'日付': datetime.date(2025, 5, 1), '所定時間': None, '早出残業': None, '出勤時刻': None, '退勤時刻': None}
2025-07-09 16:13:11,111 - INFO - 📊 获取图表数据成功: 31个数据点 (entries: 0, timeprotab: 31)
2025-07-09 16:13:11,111 - INFO - 📊 entries数据: {}
2025-07-09 16:13:11,111 - INFO - 📊 timeprotab数据: {datetime.date(2025, 5, 1): 0.0, datetime.date(2025, 5, 2): 0.0, datetime.date(2025, 5, 3): 0.0, datetime.date(2025, 5, 4): 0.0, datetime.date(2025, 5, 5): 8.0, datetime.date(2025, 5, 6): 8.0, datetime.date(2025, 5, 7): 9.0, datetime.date(2025, 5, 8): 9.0, datetime.date(2025, 5, 9): 9.0, datetime.date(2025, 5, 10): 0.0, datetime.date(2025, 5, 11): 0.0, datetime.date(2025, 5, 12): 9.0, datetime.date(2025, 5, 13): 9.0, datetime.date(2025, 5, 14): 9.0, datetime.date(2025, 5, 15): 9.0, datetime.date(2025, 5, 16): 9.0, datetime.date(2025, 5, 17): 0.0, datetime.date(2025, 5, 18): 0.0, datetime.date(2025, 5, 19): 9.0, datetime.date(2025, 5, 20): 9.0, datetime.date(2025, 5, 21): 9.0, datetime.date(2025, 5, 22): 9.0, datetime.date(2025, 5, 23): 8.0, datetime.date(2025, 5, 24): 0.0, datetime.date(2025, 5, 25): 0.0, datetime.date(2025, 5, 26): 9.0, datetime.date(2025, 5, 27): 9.0, datetime.date(2025, 5, 28): 9.0, datetime.date(2025, 5, 29): 9.0, datetime.date(2025, 5, 30): 9.0, datetime.date(2025, 5, 31): 0.0}
2025-07-09 16:13:11,115 - INFO - 🔌 PostgreSQL连接已关闭
2025-07-09 16:13:16,293 - INFO - ✅ PostgreSQL连接池创建成功
2025-07-09 16:13:16,300 - INFO - 📋 获取entries成功: 1条记录 (employee_id=215829, start_date=2025-07-01, end_date=2025-07-31)
2025-07-09 16:13:16,304 - INFO - 🔌 PostgreSQL连接已关闭
2025-07-09 16:13:30,595 - INFO - ✅ PostgreSQL连接池创建成功
2025-07-09 16:13:30,717 - INFO - 📅 获取可用月份成功: 33个月份 (employee_id=215829)
2025-07-09 16:13:30,723 - INFO - 🔌 PostgreSQL连接已关闭
2025-07-09 16:13:49,457 - INFO - ✅ PostgreSQL连接池创建成功
2025-07-09 16:13:49,465 - INFO - 📋 获取entries成功: 22条记录 (employee_id=215829, start_date=2025-06-01, end_date=2025-06-30)
2025-07-09 16:13:49,469 - INFO - 🔌 PostgreSQL连接已关闭
2025-07-09 16:14:15,503 - INFO - ✅ PostgreSQL连接池创建成功
2025-07-09 16:14:15,511 - INFO - 📋 获取timeprotab成功: 30条记录
2025-07-09 16:14:15,515 - INFO - 🔌 PostgreSQL连接已关闭
2025-07-09 16:14:17,866 - INFO - ✅ PostgreSQL连接池创建成功
2025-07-09 16:14:17,875 - INFO - 📋 获取timeprotab成功: 31条记录
2025-07-09 16:14:17,880 - INFO - 🔌 PostgreSQL连接已关闭
2025-07-09 16:16:53,127 - INFO - ✅ PostgreSQL连接池创建成功
2025-07-09 16:16:53,134 - INFO - 📋 获取entries成功: 0条记录 (employee_id=215829, start_date=2025-05-01, end_date=2025-05-31)
2025-07-09 16:16:53,138 - INFO - 🔌 PostgreSQL连接已关闭
2025-07-09 16:16:55,231 - INFO - ✅ PostgreSQL连接池创建成功
2025-07-09 16:16:55,239 - INFO - 📋 获取entries成功: 1条记录 (employee_id=215829, start_date=2025-04-01, end_date=2025-04-30)
2025-07-09 16:16:55,243 - INFO - 🔌 PostgreSQL连接已关闭
2025-07-09 16:16:57,151 - INFO - ✅ PostgreSQL连接池创建成功
2025-07-09 16:16:57,159 - INFO - 📋 获取entries成功: 21条记录 (employee_id=215829, start_date=2025-03-01, end_date=2025-03-31)
2025-07-09 16:16:57,163 - INFO - 🔌 PostgreSQL连接已关闭
2025-07-09 16:17:04,490 - INFO - ✅ PostgreSQL连接池创建成功
2025-07-09 16:17:04,497 - INFO - 📋 获取entries成功: 1条记录 (employee_id=215829, start_date=2025-04-01, end_date=2025-04-30)
2025-07-09 16:17:04,501 - INFO - 🔌 PostgreSQL连接已关闭
2025-07-09 16:17:19,795 - INFO - ✅ PostgreSQL连接池创建成功
2025-07-09 16:17:19,803 - INFO - 📋 获取entries成功: 0条记录 (employee_id=215829, start_date=2025-05-01, end_date=2025-05-31)
2025-07-09 16:17:19,808 - INFO - 🔌 PostgreSQL连接已关闭
2025-07-09 16:17:24,174 - INFO - ✅ PostgreSQL连接池创建成功
2025-07-09 16:17:24,183 - INFO - 📋 获取entries成功: 22条记录 (employee_id=215829, start_date=2025-06-01, end_date=2025-06-30)
2025-07-09 16:17:24,187 - INFO - 🔌 PostgreSQL连接已关闭
2025-07-09 16:17:30,101 - INFO - ✅ PostgreSQL连接池创建成功
2025-07-09 16:17:30,109 - INFO - 📋 获取entries成功: 0条记录 (employee_id=215829, start_date=2025-05-01, end_date=2025-05-31)
2025-07-09 16:17:30,113 - INFO - 🔌 PostgreSQL连接已关闭
2025-07-09 16:17:44,575 - INFO - ✅ PostgreSQL连接池创建成功
2025-07-09 16:17:44,582 - INFO - 📋 获取entries成功: 1条记录 (employee_id=215829, start_date=2025-07-01, end_date=2025-07-31)
2025-07-09 16:17:44,585 - INFO - 🔌 PostgreSQL连接已关闭
2025-07-09 16:18:59,667 - INFO - MySuite Server5 - 数据同步微服务 (Ubuntu远程) v1.0.0 启动中...
2025-07-09 16:18:59,667 - INFO - 启动f1-f4服务...
2025-07-09 16:18:59,668 - INFO - 🔊 f1监听器服务初始化完成
2025-07-09 16:18:59,668 - INFO - 📡 Server6客户端初始化 (新版): http://192.168.3.93:8009
2025-07-09 16:18:59,668 - INFO - 📤 f2推送回写引擎初始化完成 (修复版本: 直接轮询PostgreSQL)
2025-07-09 16:18:59,668 - INFO - 📡 Server6客户端初始化 (新版): http://192.168.3.93:8009
2025-07-09 16:18:59,669 - INFO - 📥 f3数据拉取器初始化完成 (Staging & UPSERT 模式)
2025-07-09 16:18:59,669 - INFO - 🎮 f4操作处理器初始化完成
2025-07-09 16:18:59,669 - INFO - 启动 f1_listener...
2025-07-09 16:18:59,673 - INFO - Redis连接成功
2025-07-09 16:18:59,683 - INFO - ✅ MongoDB连接成功
2025-07-09 16:18:59,939 - INFO - ✅ PostgreSQL连接池创建成功
2025-07-09 16:18:59,940 - INFO - ✅ f1监听器服务启动成功
2025-07-09 16:18:59,942 - INFO - f1_listener 启动成功
2025-07-09 16:18:59,942 - INFO - 启动 f2_push_writer...
2025-07-09 16:18:59,944 - INFO - Redis连接成功
2025-07-09 16:18:59,949 - INFO - ✅ 分区创建成功: entries_2025_07
2025-07-09 16:18:59,952 - INFO - ✅ 分区创建成功: entries_2025_08
2025-07-09 16:18:59,954 - INFO - ✅ MongoDB连接成功
2025-07-09 16:19:00,081 - INFO - 📡 开始监听频道: push_job
2025-07-09 16:19:00,082 - INFO - 📡 开始监听频道: partition_check
2025-07-09 16:19:00,084 - INFO - 📡 开始监听频道: sync_trigger
2025-07-09 16:19:00,210 - INFO - ✅ PostgreSQL连接池创建成功
2025-07-09 16:19:00,210 - INFO - ✅ f2推送回写服务启动成功，工作线程数: 4
2025-07-09 16:19:00,210 - INFO - 🚀 推送工作线程启动: worker_1
2025-07-09 16:19:00,210 - INFO - 🚀 推送工作线程启动: worker_2
2025-07-09 16:19:00,210 - INFO - 🚀 推送工作线程启动: worker_3
2025-07-09 16:19:00,211 - INFO - 🚀 推送工作线程启动: worker_4
2025-07-09 16:19:00,212 - INFO - f2_push_writer 启动成功
2025-07-09 16:19:00,212 - INFO - 启动 f3_data_puller...
2025-07-09 16:19:00,552 - INFO - ✅ PostgreSQL连接池创建成功
2025-07-09 16:19:00,553 - INFO - Redis连接成功
2025-07-09 16:19:00,553 - INFO - ✅ f3数据拉取服务启动成功，预定执行时间: ['02:00', '03:00']
2025-07-09 16:19:00,553 - INFO - f3_data_puller 启动成功
2025-07-09 16:19:00,553 - INFO - 启动 f4_operation_handler...
2025-07-09 16:19:00,554 - INFO - 🚀 数据拉取调度器启动，预定运行时间: ['02:00', '03:00']
2025-07-09 16:19:00,554 - INFO - 下一次f3/f5同步任务将在 2025-07-10 02:00:00 执行，等待 34859 秒...
2025-07-09 16:19:00,557 - INFO - Redis连接成功
2025-07-09 16:19:00,565 - INFO - ✅ MongoDB连接成功
2025-07-09 16:19:00,797 - INFO - ✅ PostgreSQL连接池创建成功
2025-07-09 16:19:00,797 - INFO - ✅ f4操作处理服务启动成功
2025-07-09 16:19:00,797 - INFO - f4_operation_handler 启动成功
2025-07-09 16:19:00,797 - INFO - 服务启动完成，已启动: ['f1_listener', 'f2_push_writer', 'f3_data_puller', 'f4_operation_handler']
2025-07-09 16:19:02,272 - INFO - 🔊 f1监听器服务初始化完成
2025-07-09 16:19:02,273 - INFO - 📡 Server6客户端初始化 (新版): http://192.168.3.93:8009
2025-07-09 16:19:02,273 - INFO - 📤 f2推送回写引擎初始化完成 (修复版本: 直接轮询PostgreSQL)
2025-07-09 16:19:02,273 - INFO - 📡 Server6客户端初始化 (新版): http://192.168.3.93:8009
2025-07-09 16:19:02,274 - INFO - 📥 f3数据拉取器初始化完成 (Staging & UPSERT 模式)
2025-07-09 16:19:02,274 - INFO - 🎮 f4操作处理器初始化完成
2025-07-09 16:19:02,285 - INFO - Redis连接成功
2025-07-09 16:19:02,285 - INFO - Redis连接成功
2025-07-09 16:19:02,285 - INFO - Redis连接成功
2025-07-09 16:19:02,292 - INFO - ✅ MongoDB连接成功
2025-07-09 16:19:02,293 - INFO - ✅ MongoDB连接成功
2025-07-09 16:19:02,293 - INFO - ✅ MongoDB连接成功
2025-07-09 16:19:02,561 - INFO - ✅ PostgreSQL连接池创建成功
2025-07-09 16:19:02,561 - INFO - Redis连接成功
2025-07-09 16:19:02,561 - INFO - ✅ f3数据拉取服务启动成功，预定执行时间: ['02:00', '03:00']
2025-07-09 16:19:02,561 - INFO - 🚀 数据拉取调度器启动，预定运行时间: ['02:00', '03:00']
2025-07-09 16:19:02,562 - INFO - 下一次f3/f5同步任务将在 2025-07-10 02:00:00 执行，等待 34857 秒...
2025-07-09 16:19:02,601 - INFO - ✅ PostgreSQL连接池创建成功
2025-07-09 16:19:02,601 - INFO - ✅ f1监听器服务启动成功
2025-07-09 16:19:02,630 - INFO - ✅ 分区创建成功: entries_2025_07
2025-07-09 16:19:02,634 - INFO - ✅ PostgreSQL连接池创建成功
2025-07-09 16:19:02,634 - INFO - ✅ f2推送回写服务启动成功，工作线程数: 4
2025-07-09 16:19:02,635 - INFO - 🚀 推送工作线程启动: worker_1
2025-07-09 16:19:02,635 - INFO - 🚀 推送工作线程启动: worker_2
2025-07-09 16:19:02,635 - INFO - 🚀 推送工作线程启动: worker_3
2025-07-09 16:19:02,636 - INFO - 🚀 推送工作线程启动: worker_4
2025-07-09 16:19:02,636 - INFO - ✅ 分区创建成功: entries_2025_08
2025-07-09 16:19:02,703 - INFO - ✅ PostgreSQL连接池创建成功
2025-07-09 16:19:02,703 - INFO - ✅ f4操作处理服务启动成功
2025-07-09 16:19:02,703 - INFO - 🔌 推送工作线程停止: worker_3
2025-07-09 16:19:02,703 - INFO - 🔌 推送工作线程停止: worker_4
2025-07-09 16:19:02,705 - INFO - 🔌 推送工作线程停止: worker_2
2025-07-09 16:19:02,705 - INFO - 🔌 推送工作线程停止: worker_1
2025-07-09 16:19:20,770 - INFO - ✅ PostgreSQL连接池创建成功
2025-07-09 16:19:20,778 - INFO - 📋 获取timeprotab成功: 31条记录
2025-07-09 16:19:20,782 - INFO - 🔌 PostgreSQL连接已关闭
2025-07-09 16:19:21,079 - INFO - ✅ PostgreSQL连接池创建成功
2025-07-09 16:19:21,176 - INFO - 📅 获取可用月份成功: 33个月份 (employee_id=215829)
2025-07-09 16:19:21,181 - INFO - 🔌 PostgreSQL连接已关闭
2025-07-09 16:19:21,496 - INFO - ✅ PostgreSQL连接池创建成功
2025-07-09 16:19:21,604 - INFO - 📅 获取可用月份成功: 33个月份 (employee_id=215829)
2025-07-09 16:19:21,608 - INFO - 🔌 PostgreSQL连接已关闭
2025-07-09 16:19:22,790 - INFO - ✅ PostgreSQL连接池创建成功
2025-07-09 16:19:22,791 - INFO - 🔥🔥🔥 chart-data 端点被调用 - 新版本代码！
2025-07-09 16:19:22,791 - INFO - === chart-data 端点被调用 ===
2025-07-09 16:19:22,791 - INFO - 📊 请求参数: employee_id=215829, start_date=2025-06-01, end_date=2025-06-30
2025-07-09 16:19:22,836 - INFO - 📊 timeprotab表结构: ['id', 'employee_id', '日付', '星期', 'ｶﾚﾝﾀﾞ', '不在', '勤務区分', '事由', '出勤時刻', 'ＭＣ_出勤', '退勤時刻', 'ＭＣ_退勤', '所定時間', '早出残業', '内深夜残業', '遅刻早退', '休出時間', '出張残業', '外出時間', '戻り時間', 'コメント', 'created_at', 'updated_at']
2025-07-09 16:19:22,837 - INFO - 📊 执行timeprotab查询: employee_id=215829, start_date=2025-06-01, end_date=2025-06-30
2025-07-09 16:19:22,843 - INFO - 📊 timeprotab查询结果: 30条记录
2025-07-09 16:19:22,843 - INFO - 📊 第一条timeprotab记录: {'日付': datetime.date(2025, 6, 1), '所定時間': None, '早出残業': None, '出勤時刻': None, '退勤時刻': None}
2025-07-09 16:19:22,843 - INFO - 📊 获取图表数据成功: 30个数据点 (entries: 21, timeprotab: 30)
2025-07-09 16:19:22,843 - INFO - 📊 entries数据: {datetime.date(2025, 6, 2): 9.0, datetime.date(2025, 6, 3): 9.0, datetime.date(2025, 6, 4): 9.0, datetime.date(2025, 6, 5): 9.0, datetime.date(2025, 6, 6): 9.0, datetime.date(2025, 6, 9): 9.0, datetime.date(2025, 6, 10): 9.0, datetime.date(2025, 6, 11): 9.0, datetime.date(2025, 6, 12): 9.0, datetime.date(2025, 6, 13): 9.0, datetime.date(2025, 6, 16): 9.0, datetime.date(2025, 6, 17): 9.0, datetime.date(2025, 6, 18): 10.5, datetime.date(2025, 6, 19): 10.0, datetime.date(2025, 6, 20): 8.0, datetime.date(2025, 6, 23): 10.5, datetime.date(2025, 6, 24): 8.0, datetime.date(2025, 6, 25): 9.0, datetime.date(2025, 6, 26): 9.0, datetime.date(2025, 6, 27): 10.5, datetime.date(2025, 6, 30): 10.0}
2025-07-09 16:19:22,843 - INFO - 📊 timeprotab数据: {datetime.date(2025, 6, 1): 0.0, datetime.date(2025, 6, 2): 9.0, datetime.date(2025, 6, 3): 9.0, datetime.date(2025, 6, 4): 9.0, datetime.date(2025, 6, 5): 5.0, datetime.date(2025, 6, 6): 9.0, datetime.date(2025, 6, 7): 0.0, datetime.date(2025, 6, 8): 0.0, datetime.date(2025, 6, 9): 9.0, datetime.date(2025, 6, 10): 9.0, datetime.date(2025, 6, 11): 9.0, datetime.date(2025, 6, 12): 9.0, datetime.date(2025, 6, 13): 9.0, datetime.date(2025, 6, 14): 0.0, datetime.date(2025, 6, 15): 0.0, datetime.date(2025, 6, 16): 9.0, datetime.date(2025, 6, 17): 9.0, datetime.date(2025, 6, 18): 10.5, datetime.date(2025, 6, 19): 10.0, datetime.date(2025, 6, 20): 8.0, datetime.date(2025, 6, 21): 0.0, datetime.date(2025, 6, 22): 0.0, datetime.date(2025, 6, 23): 10.5, datetime.date(2025, 6, 24): 8.0, datetime.date(2025, 6, 25): 9.0, datetime.date(2025, 6, 26): 9.0, datetime.date(2025, 6, 27): 10.5, datetime.date(2025, 6, 28): 0.0, datetime.date(2025, 6, 29): 0.0, datetime.date(2025, 6, 30): 10.0}
2025-07-09 16:19:22,847 - INFO - 🔌 PostgreSQL连接已关闭
2025-07-09 16:19:22,940 - INFO - ✅ PostgreSQL连接池创建成功
2025-07-09 16:19:22,941 - INFO - 🔥🔥🔥 chart-data 端点被调用 - 新版本代码！
2025-07-09 16:19:22,941 - INFO - === chart-data 端点被调用 ===
2025-07-09 16:19:22,941 - INFO - 📊 请求参数: employee_id=215829, start_date=2025-05-01, end_date=2025-05-31
2025-07-09 16:19:22,972 - INFO - 📊 timeprotab表结构: ['id', 'employee_id', '日付', '星期', 'ｶﾚﾝﾀﾞ', '不在', '勤務区分', '事由', '出勤時刻', 'ＭＣ_出勤', '退勤時刻', 'ＭＣ_退勤', '所定時間', '早出残業', '内深夜残業', '遅刻早退', '休出時間', '出張残業', '外出時間', '戻り時間', 'コメント', 'created_at', 'updated_at']
2025-07-09 16:19:22,973 - INFO - 📊 执行timeprotab查询: employee_id=215829, start_date=2025-05-01, end_date=2025-05-31
2025-07-09 16:19:22,980 - INFO - 📊 timeprotab查询结果: 31条记录
2025-07-09 16:19:22,981 - INFO - 📊 第一条timeprotab记录: {'日付': datetime.date(2025, 5, 1), '所定時間': None, '早出残業': None, '出勤時刻': None, '退勤時刻': None}
2025-07-09 16:19:22,981 - INFO - 📊 获取图表数据成功: 31个数据点 (entries: 0, timeprotab: 31)
2025-07-09 16:19:22,981 - INFO - 📊 entries数据: {}
2025-07-09 16:19:22,982 - INFO - 📊 timeprotab数据: {datetime.date(2025, 5, 1): 0.0, datetime.date(2025, 5, 2): 0.0, datetime.date(2025, 5, 3): 0.0, datetime.date(2025, 5, 4): 0.0, datetime.date(2025, 5, 5): 8.0, datetime.date(2025, 5, 6): 8.0, datetime.date(2025, 5, 7): 9.0, datetime.date(2025, 5, 8): 9.0, datetime.date(2025, 5, 9): 9.0, datetime.date(2025, 5, 10): 0.0, datetime.date(2025, 5, 11): 0.0, datetime.date(2025, 5, 12): 9.0, datetime.date(2025, 5, 13): 9.0, datetime.date(2025, 5, 14): 9.0, datetime.date(2025, 5, 15): 9.0, datetime.date(2025, 5, 16): 9.0, datetime.date(2025, 5, 17): 0.0, datetime.date(2025, 5, 18): 0.0, datetime.date(2025, 5, 19): 9.0, datetime.date(2025, 5, 20): 9.0, datetime.date(2025, 5, 21): 9.0, datetime.date(2025, 5, 22): 9.0, datetime.date(2025, 5, 23): 8.0, datetime.date(2025, 5, 24): 0.0, datetime.date(2025, 5, 25): 0.0, datetime.date(2025, 5, 26): 9.0, datetime.date(2025, 5, 27): 9.0, datetime.date(2025, 5, 28): 9.0, datetime.date(2025, 5, 29): 9.0, datetime.date(2025, 5, 30): 9.0, datetime.date(2025, 5, 31): 0.0}
2025-07-09 16:19:22,986 - INFO - 🔌 PostgreSQL连接已关闭
2025-07-09 16:31:08,366 - INFO - 🔊 f1监听器服务初始化完成
2025-07-09 16:31:08,366 - INFO - 📡 Server6客户端初始化 (新版): http://192.168.3.93:8009
2025-07-09 16:31:08,366 - INFO - 📤 f2推送回写引擎初始化完成 (修复版本: 直接轮询PostgreSQL)
2025-07-09 16:31:08,366 - INFO - 📡 Server6客户端初始化 (新版): http://192.168.3.93:8009
2025-07-09 16:31:08,367 - INFO - 📥 f3数据拉取器初始化完成 (Staging & UPSERT 模式)
2025-07-09 16:31:08,367 - INFO - 🎮 f4操作处理器初始化完成
2025-07-09 16:31:08,377 - INFO - Redis连接成功
2025-07-09 16:31:08,377 - INFO - Redis连接成功
2025-07-09 16:31:08,377 - INFO - Redis连接成功
2025-07-09 16:31:08,383 - INFO - ✅ MongoDB连接成功
2025-07-09 16:31:08,384 - INFO - ✅ MongoDB连接成功
2025-07-09 16:31:08,384 - INFO - ✅ MongoDB连接成功
2025-07-09 16:31:08,615 - INFO - ✅ PostgreSQL连接池创建成功
2025-07-09 16:31:08,633 - INFO - Redis连接成功
2025-07-09 16:31:08,633 - INFO - ✅ f3数据拉取服务启动成功，预定执行时间: ['02:00', '03:00']
2025-07-09 16:31:08,633 - INFO - 🚀 数据拉取调度器启动，预定运行时间: ['02:00', '03:00']
2025-07-09 16:31:08,633 - INFO - 下一次f3/f5同步任务将在 2025-07-10 02:00:00 执行，等待 34131 秒...
2025-07-09 16:31:08,638 - INFO - ✅ PostgreSQL连接池创建成功
2025-07-09 16:31:08,638 - INFO - ✅ f1监听器服务启动成功
2025-07-09 16:31:08,648 - INFO - ✅ 分区创建成功: entries_2025_07
2025-07-09 16:31:08,651 - INFO - ✅ 分区创建成功: entries_2025_08
2025-07-09 16:31:08,695 - INFO - ✅ PostgreSQL连接池创建成功
2025-07-09 16:31:08,695 - INFO - ✅ f2推送回写服务启动成功，工作线程数: 4
2025-07-09 16:31:08,696 - INFO - 🚀 推送工作线程启动: worker_1
2025-07-09 16:31:08,696 - INFO - 🚀 推送工作线程启动: worker_2
2025-07-09 16:31:08,696 - INFO - 🚀 推送工作线程启动: worker_3
2025-07-09 16:31:08,696 - INFO - 🚀 推送工作线程启动: worker_4
2025-07-09 16:31:08,722 - INFO - ✅ PostgreSQL连接池创建成功
2025-07-09 16:31:08,722 - INFO - ✅ f4操作处理服务启动成功
2025-07-09 16:31:08,722 - INFO - 🔌 推送工作线程停止: worker_4
2025-07-09 16:31:08,723 - INFO - 🔌 推送工作线程停止: worker_3
2025-07-09 16:31:08,724 - INFO - 🔌 推送工作线程停止: worker_2
2025-07-09 16:31:08,843 - INFO - 🔌 推送工作线程停止: worker_1
2025-07-09 17:20:26,292 - INFO - 🔌 MongoDB连接已关闭
2025-07-09 17:20:26,295 - INFO - 🔌 MongoDB连接已关闭
2025-07-09 17:20:26,296 - INFO - 🔌 MongoDB连接已关闭
2025-07-09 17:20:26,296 - INFO - 🔌 f1监听器服务已停止
2025-07-09 17:20:26,296 - INFO - 🔌 f2推送回写服务已停止
2025-07-09 17:20:26,296 - INFO - 🔌 f4操作处理服务已停止
2025-07-09 17:20:26,771 - INFO - 🔌 PostgreSQL连接已关闭
2025-07-09 17:20:26,772 - INFO - 🔌 PostgreSQL连接已关闭
2025-07-09 17:20:26,772 - INFO - 🔌 MongoDB连接已关闭
2025-07-09 17:20:26,773 - INFO - 🔌 PostgreSQL连接已关闭
2025-07-09 17:20:26,773 - INFO - 🔌 MongoDB连接已关闭
2025-07-09 17:20:26,774 - INFO - 🔌 PostgreSQL连接已关闭
2025-07-09 17:20:26,774 - INFO - 🔌 MongoDB连接已关闭
2025-07-09 17:20:26,775 - INFO - 🔌 Redis连接已关闭
2025-07-09 17:20:26,775 - INFO - 🔌 Redis连接已关闭
2025-07-09 17:20:26,775 - INFO - 🔌 Redis连接已关闭
2025-07-09 17:20:26,775 - INFO - 🔌 f1监听器服务已停止
2025-07-09 17:20:26,775 - INFO - 🔌 f2推送回写服务已停止
2025-07-09 17:20:26,775 - INFO - 🔌 f4操作处理服务已停止
2025-07-09 17:20:33,948 - INFO - 🔊 f1监听器服务初始化完成
2025-07-09 17:20:33,948 - INFO - 📡 Server6客户端初始化 (新版): http://192.168.3.93:8009
2025-07-09 17:20:33,948 - INFO - 📤 f2推送回写引擎初始化完成 (修复版本: 直接轮询PostgreSQL)
2025-07-09 17:20:33,948 - INFO - 📡 Server6客户端初始化 (新版): http://192.168.3.93:8009
2025-07-09 17:20:33,950 - INFO - 📥 f3数据拉取器初始化完成 (Staging & UPSERT 模式)
2025-07-09 17:20:33,950 - INFO - 🎮 f4操作处理器初始化完成
2025-07-09 17:20:33,960 - INFO - Redis连接成功
2025-07-09 17:20:33,960 - INFO - Redis连接成功
2025-07-09 17:20:33,960 - INFO - Redis连接成功
2025-07-09 17:20:33,965 - INFO - ✅ MongoDB连接成功
2025-07-09 17:20:33,966 - INFO - ✅ MongoDB连接成功
2025-07-09 17:20:33,969 - INFO - ✅ MongoDB连接成功
2025-07-09 17:20:34,283 - INFO - ✅ PostgreSQL连接池创建成功
2025-07-09 17:20:34,284 - INFO - Redis连接成功
2025-07-09 17:20:34,284 - INFO - ✅ f3数据拉取服务启动成功，预定执行时间: ['02:00', '03:00']
2025-07-09 17:20:34,284 - INFO - 🚀 数据拉取调度器启动，预定运行时间: ['02:00', '03:00']
2025-07-09 17:20:34,284 - INFO - 下一次f3/f5同步任务将在 2025-07-10 02:00:00 执行，等待 31166 秒...
2025-07-09 17:20:34,304 - INFO - ✅ PostgreSQL连接池创建成功
2025-07-09 17:20:34,305 - INFO - ✅ f1监听器服务启动成功
2025-07-09 17:20:34,310 - INFO - ✅ PostgreSQL连接池创建成功
2025-07-09 17:20:34,310 - INFO - ✅ f2推送回写服务启动成功，工作线程数: 4
2025-07-09 17:20:34,310 - INFO - 🚀 推送工作线程启动: worker_1
2025-07-09 17:20:34,310 - INFO - 🚀 推送工作线程启动: worker_2
2025-07-09 17:20:34,310 - INFO - 🚀 推送工作线程启动: worker_3
2025-07-09 17:20:34,311 - INFO - 🚀 推送工作线程启动: worker_4
2025-07-09 17:20:34,331 - INFO - ✅ 分区创建成功: entries_2025_07
2025-07-09 17:20:34,335 - INFO - ✅ PostgreSQL连接池创建成功
2025-07-09 17:20:34,336 - INFO - ✅ f4操作处理服务启动成功
2025-07-09 17:20:34,336 - INFO - ✅ 分区创建成功: entries_2025_08
2025-07-09 17:20:34,336 - INFO - 🔌 推送工作线程停止: worker_3
2025-07-09 17:20:34,336 - INFO - 🔌 推送工作线程停止: worker_4
2025-07-09 17:20:34,339 - INFO - 🔌 推送工作线程停止: worker_1
2025-07-09 17:20:34,495 - INFO - 🔌 推送工作线程停止: worker_2
2025-07-09 17:28:56,073 - INFO - 🔌 MongoDB连接已关闭
2025-07-09 17:28:56,074 - INFO - 🔌 MongoDB连接已关闭
2025-07-09 17:28:56,075 - INFO - 🔌 MongoDB连接已关闭
2025-07-09 17:28:56,076 - INFO - 🔌 f1监听器服务已停止
2025-07-09 17:28:56,076 - INFO - 🔌 f2推送回写服务已停止
2025-07-09 17:28:56,076 - INFO - 🔌 f4操作处理服务已停止
2025-07-09 17:28:56,506 - INFO - 🔌 PostgreSQL连接已关闭
2025-07-09 17:28:56,506 - INFO - 🔌 PostgreSQL连接已关闭
2025-07-09 17:28:56,507 - INFO - 🔌 MongoDB连接已关闭
2025-07-09 17:28:56,507 - INFO - 🔌 PostgreSQL连接已关闭
2025-07-09 17:28:56,508 - INFO - 🔌 MongoDB连接已关闭
2025-07-09 17:28:56,508 - INFO - 🔌 PostgreSQL连接已关闭
2025-07-09 17:28:56,508 - INFO - 🔌 MongoDB连接已关闭
2025-07-09 17:28:56,509 - INFO - 🔌 Redis连接已关闭
2025-07-09 17:28:56,509 - INFO - 🔌 Redis连接已关闭
2025-07-09 17:28:56,509 - INFO - 🔌 Redis连接已关闭
2025-07-09 17:28:56,510 - INFO - 🔌 f1监听器服务已停止
2025-07-09 17:28:56,510 - INFO - 🔌 f2推送回写服务已停止
2025-07-09 17:28:56,510 - INFO - 🔌 f4操作处理服务已停止
2025-07-09 17:29:03,562 - INFO - 🔊 f1监听器服务初始化完成
2025-07-09 17:29:03,562 - INFO - 📡 Server6客户端初始化 (新版): http://192.168.3.93:8009
2025-07-09 17:29:03,563 - INFO - 📤 f2推送回写引擎初始化完成 (修复版本: 直接轮询PostgreSQL)
2025-07-09 17:29:03,563 - INFO - 📡 Server6客户端初始化 (新版): http://192.168.3.93:8009
2025-07-09 17:29:03,564 - INFO - 📥 f3数据拉取器初始化完成 (Staging & UPSERT 模式)
2025-07-09 17:29:03,564 - INFO - 🎮 f4操作处理器初始化完成
2025-07-09 17:29:03,574 - INFO - Redis连接成功
2025-07-09 17:29:03,574 - INFO - Redis连接成功
2025-07-09 17:29:03,574 - INFO - Redis连接成功
2025-07-09 17:29:03,580 - INFO - ✅ MongoDB连接成功
2025-07-09 17:29:03,580 - INFO - ✅ MongoDB连接成功
2025-07-09 17:29:03,581 - INFO - ✅ MongoDB连接成功
2025-07-09 17:29:03,899 - INFO - ✅ PostgreSQL连接池创建成功
2025-07-09 17:29:03,901 - INFO - Redis连接成功
2025-07-09 17:29:03,901 - INFO - ✅ f3数据拉取服务启动成功，预定执行时间: ['02:00', '03:00']
2025-07-09 17:29:03,901 - INFO - 🚀 数据拉取调度器启动，预定运行时间: ['02:00', '03:00']
2025-07-09 17:29:03,901 - INFO - 下一次f3/f5同步任务将在 2025-07-10 02:00:00 执行，等待 30656 秒...
2025-07-09 17:29:03,955 - INFO - ✅ PostgreSQL连接池创建成功
2025-07-09 17:29:03,955 - INFO - ✅ f2推送回写服务启动成功，工作线程数: 4
2025-07-09 17:29:03,955 - INFO - 🚀 推送工作线程启动: worker_1
2025-07-09 17:29:03,955 - INFO - 🚀 推送工作线程启动: worker_2
2025-07-09 17:29:03,955 - INFO - 🚀 推送工作线程启动: worker_3
2025-07-09 17:29:03,956 - INFO - 🚀 推送工作线程启动: worker_4
2025-07-09 17:29:03,976 - INFO - ✅ PostgreSQL连接池创建成功
2025-07-09 17:29:03,976 - INFO - ✅ f1监听器服务启动成功
2025-07-09 17:29:03,982 - INFO - ✅ PostgreSQL连接池创建成功
2025-07-09 17:29:03,982 - INFO - ✅ f4操作处理服务启动成功
2025-07-09 17:29:03,983 - INFO - 🔌 推送工作线程停止: worker_4
2025-07-09 17:29:03,983 - INFO - 🔌 推送工作线程停止: worker_3
2025-07-09 17:29:03,984 - INFO - 🔌 推送工作线程停止: worker_2
2025-07-09 17:29:03,985 - INFO - 🔌 推送工作线程停止: worker_1
2025-07-09 17:33:34,177 - INFO - 🔌 MongoDB连接已关闭
2025-07-09 17:33:34,179 - INFO - 🔌 MongoDB连接已关闭
2025-07-09 17:33:34,182 - INFO - 🔌 MongoDB连接已关闭
2025-07-09 17:33:34,183 - INFO - 🔌 f1监听器服务已停止
2025-07-09 17:33:34,183 - INFO - 🔌 f2推送回写服务已停止
2025-07-09 17:33:34,183 - INFO - 🔌 f4操作处理服务已停止
2025-07-09 17:33:34,666 - INFO - 🔌 PostgreSQL连接已关闭
2025-07-09 17:33:34,666 - INFO - 🔌 PostgreSQL连接已关闭
2025-07-09 17:33:34,667 - INFO - 🔌 MongoDB连接已关闭
2025-07-09 17:33:34,667 - INFO - 🔌 PostgreSQL连接已关闭
2025-07-09 17:33:34,667 - INFO - 🔌 MongoDB连接已关闭
2025-07-09 17:33:34,667 - INFO - 🔌 PostgreSQL连接已关闭
2025-07-09 17:33:34,667 - INFO - 🔌 MongoDB连接已关闭
2025-07-09 17:33:34,667 - INFO - 🔌 Redis连接已关闭
2025-07-09 17:33:34,668 - INFO - 🔌 Redis连接已关闭
2025-07-09 17:33:34,668 - INFO - 🔌 Redis连接已关闭
2025-07-09 17:33:34,668 - INFO - 🔌 f1监听器服务已停止
2025-07-09 17:33:34,668 - INFO - 🔌 f2推送回写服务已停止
2025-07-09 17:33:34,668 - INFO - 🔌 f4操作处理服务已停止
2025-07-09 17:33:41,818 - INFO - 🔊 f1监听器服务初始化完成
2025-07-09 17:33:41,819 - INFO - 📡 Server6客户端初始化 (新版): http://192.168.3.93:8009
2025-07-09 17:33:41,819 - INFO - 📤 f2推送回写引擎初始化完成 (修复版本: 直接轮询PostgreSQL)
2025-07-09 17:33:41,819 - INFO - 📡 Server6客户端初始化 (新版): http://192.168.3.93:8009
2025-07-09 17:33:41,820 - INFO - 📥 f3数据拉取器初始化完成 (Staging & UPSERT 模式)
2025-07-09 17:33:41,820 - INFO - 🎮 f4操作处理器初始化完成
2025-07-09 17:33:41,830 - INFO - Redis连接成功
2025-07-09 17:33:41,830 - INFO - Redis连接成功
2025-07-09 17:33:41,830 - INFO - Redis连接成功
2025-07-09 17:33:41,835 - INFO - ✅ MongoDB连接成功
2025-07-09 17:33:41,836 - INFO - ✅ MongoDB连接成功
2025-07-09 17:33:41,837 - INFO - ✅ MongoDB连接成功
2025-07-09 17:33:42,122 - INFO - ✅ PostgreSQL连接池创建成功
2025-07-09 17:33:42,123 - INFO - Redis连接成功
2025-07-09 17:33:42,123 - INFO - ✅ f3数据拉取服务启动成功，预定执行时间: ['02:00', '03:00']
2025-07-09 17:33:42,123 - INFO - 🚀 数据拉取调度器启动，预定运行时间: ['02:00', '03:00']
2025-07-09 17:33:42,123 - INFO - 下一次f3/f5同步任务将在 2025-07-10 02:00:00 执行，等待 30378 秒...
2025-07-09 17:33:42,159 - INFO - ✅ PostgreSQL连接池创建成功
2025-07-09 17:33:42,160 - INFO - ✅ f1监听器服务启动成功
2025-07-09 17:33:42,169 - INFO - ✅ 分区创建成功: entries_2025_07
2025-07-09 17:33:42,172 - INFO - ✅ 分区创建成功: entries_2025_08
2025-07-09 17:33:42,206 - INFO - ✅ PostgreSQL连接池创建成功
2025-07-09 17:33:42,207 - INFO - ✅ f2推送回写服务启动成功，工作线程数: 4
2025-07-09 17:33:42,207 - INFO - 🚀 推送工作线程启动: worker_1
2025-07-09 17:33:42,207 - INFO - 🚀 推送工作线程启动: worker_2
2025-07-09 17:33:42,207 - INFO - 🚀 推送工作线程启动: worker_3
2025-07-09 17:33:42,207 - INFO - 🚀 推送工作线程启动: worker_4
2025-07-09 17:33:42,259 - INFO - ✅ PostgreSQL连接池创建成功
2025-07-09 17:33:42,259 - INFO - ✅ f4操作处理服务启动成功
2025-07-09 17:33:42,260 - INFO - 🔌 推送工作线程停止: worker_4
2025-07-09 17:33:42,260 - INFO - 🔌 推送工作线程停止: worker_3
2025-07-09 17:33:42,261 - INFO - 🔌 推送工作线程停止: worker_2
2025-07-09 17:33:42,261 - INFO - 🔌 推送工作线程停止: worker_1
2025-07-09 17:40:20,679 - INFO - 🔌 MongoDB连接已关闭
2025-07-09 17:40:20,681 - INFO - 🔌 MongoDB连接已关闭
2025-07-09 17:40:20,682 - INFO - 🔌 MongoDB连接已关闭
2025-07-09 17:40:20,683 - INFO - 🔌 f1监听器服务已停止
2025-07-09 17:40:20,683 - INFO - 🔌 f2推送回写服务已停止
2025-07-09 17:40:20,683 - INFO - 🔌 f4操作处理服务已停止
2025-07-09 17:40:20,964 - INFO - 🔌 PostgreSQL连接已关闭
2025-07-09 17:40:20,964 - INFO - 🔌 PostgreSQL连接已关闭
2025-07-09 17:40:20,964 - INFO - 🔌 MongoDB连接已关闭
2025-07-09 17:40:20,965 - INFO - 🔌 PostgreSQL连接已关闭
2025-07-09 17:40:20,965 - INFO - 🔌 MongoDB连接已关闭
2025-07-09 17:40:20,965 - INFO - 🔌 PostgreSQL连接已关闭
2025-07-09 17:40:20,966 - INFO - 🔌 MongoDB连接已关闭
2025-07-09 17:40:20,966 - INFO - 🔌 Redis连接已关闭
2025-07-09 17:40:20,966 - INFO - 🔌 Redis连接已关闭
2025-07-09 17:40:20,966 - INFO - 🔌 Redis连接已关闭
2025-07-09 17:40:20,966 - INFO - 🔌 f1监听器服务已停止
2025-07-09 17:40:20,966 - INFO - 🔌 f2推送回写服务已停止
2025-07-09 17:40:20,966 - INFO - 🔌 f4操作处理服务已停止
2025-07-09 17:40:32,639 - INFO - 🔊 f1监听器服务初始化完成
2025-07-09 17:40:32,639 - INFO - 📡 Server6客户端初始化 (新版): http://192.168.3.93:8009
2025-07-09 17:40:32,639 - INFO - 📤 f2推送回写引擎初始化完成 (修复版本: 直接轮询PostgreSQL)
2025-07-09 17:40:32,639 - INFO - 📡 Server6客户端初始化 (新版): http://192.168.3.93:8009
2025-07-09 17:40:32,640 - INFO - 📥 f3数据拉取器初始化完成 (Staging & UPSERT 模式)
2025-07-09 17:40:32,640 - INFO - 🎮 f4操作处理器初始化完成
2025-07-09 17:40:32,649 - INFO - Redis连接成功
2025-07-09 17:40:32,649 - INFO - Redis连接成功
2025-07-09 17:40:32,649 - INFO - Redis连接成功
2025-07-09 17:40:32,654 - INFO - ✅ MongoDB连接成功
2025-07-09 17:40:32,655 - INFO - ✅ MongoDB连接成功
2025-07-09 17:40:32,656 - INFO - ✅ MongoDB连接成功
2025-07-09 17:40:32,907 - INFO - ✅ PostgreSQL连接池创建成功
2025-07-09 17:40:32,908 - INFO - Redis连接成功
2025-07-09 17:40:32,908 - INFO - ✅ f3数据拉取服务启动成功，预定执行时间: ['02:00', '03:00']
2025-07-09 17:40:32,908 - INFO - 🚀 数据拉取调度器启动，预定运行时间: ['02:00', '03:00']
2025-07-09 17:40:32,908 - INFO - 下一次f3/f5同步任务将在 2025-07-10 02:00:00 执行，等待 29967 秒...
2025-07-09 17:40:32,949 - INFO - ✅ PostgreSQL连接池创建成功
2025-07-09 17:40:32,949 - INFO - ✅ f1监听器服务启动成功
2025-07-09 17:40:32,958 - INFO - ✅ 分区创建成功: entries_2025_07
2025-07-09 17:40:32,962 - INFO - ✅ 分区创建成功: entries_2025_08
2025-07-09 17:40:32,994 - INFO - ✅ PostgreSQL连接池创建成功
2025-07-09 17:40:32,995 - INFO - ✅ f2推送回写服务启动成功，工作线程数: 4
2025-07-09 17:40:32,995 - INFO - 🚀 推送工作线程启动: worker_1
2025-07-09 17:40:32,995 - INFO - 🚀 推送工作线程启动: worker_2
2025-07-09 17:40:32,995 - INFO - 🚀 推送工作线程启动: worker_3
2025-07-09 17:40:32,995 - INFO - 🚀 推送工作线程启动: worker_4
2025-07-09 17:40:33,038 - INFO - ✅ PostgreSQL连接池创建成功
2025-07-09 17:40:33,038 - INFO - ✅ f4操作处理服务启动成功
2025-07-09 17:40:33,039 - INFO - 🔌 推送工作线程停止: worker_4
2025-07-09 17:40:33,039 - INFO - 🔌 推送工作线程停止: worker_3
2025-07-09 17:40:33,040 - INFO - 🔌 推送工作线程停止: worker_2
2025-07-09 17:40:33,040 - INFO - 🔌 推送工作线程停止: worker_1
2025-07-09 17:45:38,459 - INFO - 🔌 MongoDB连接已关闭
2025-07-09 17:45:38,461 - INFO - 🔌 MongoDB连接已关闭
2025-07-09 17:45:38,461 - INFO - 🔌 MongoDB连接已关闭
2025-07-09 17:45:38,462 - INFO - 🔌 f1监听器服务已停止
2025-07-09 17:45:38,462 - INFO - 🔌 f2推送回写服务已停止
2025-07-09 17:45:38,462 - INFO - 🔌 f4操作处理服务已停止
2025-07-09 17:45:38,784 - INFO - 🔌 PostgreSQL连接已关闭
2025-07-09 17:45:38,784 - INFO - 🔌 PostgreSQL连接已关闭
2025-07-09 17:45:38,784 - INFO - 🔌 MongoDB连接已关闭
2025-07-09 17:45:38,784 - INFO - 🔌 PostgreSQL连接已关闭
2025-07-09 17:45:38,784 - INFO - 🔌 MongoDB连接已关闭
2025-07-09 17:45:38,784 - INFO - 🔌 PostgreSQL连接已关闭
2025-07-09 17:45:38,784 - INFO - 🔌 MongoDB连接已关闭
2025-07-09 17:45:38,784 - INFO - 🔌 Redis连接已关闭
2025-07-09 17:45:38,784 - INFO - 🔌 Redis连接已关闭
2025-07-09 17:45:38,784 - INFO - 🔌 Redis连接已关闭
2025-07-09 17:45:38,784 - INFO - 🔌 f1监听器服务已停止
2025-07-09 17:45:38,784 - INFO - 🔌 f2推送回写服务已停止
2025-07-09 17:45:38,784 - INFO - 🔌 f4操作处理服务已停止
2025-07-09 17:45:45,600 - INFO - 🔊 f1监听器服务初始化完成
2025-07-09 17:45:45,600 - INFO - 📡 Server6客户端初始化 (新版): http://192.168.3.93:8009
2025-07-09 17:45:45,600 - INFO - 📤 f2推送回写引擎初始化完成 (修复版本: 直接轮询PostgreSQL)
2025-07-09 17:45:45,600 - INFO - 📡 Server6客户端初始化 (新版): http://192.168.3.93:8009
2025-07-09 17:45:45,601 - INFO - 📥 f3数据拉取器初始化完成 (Staging & UPSERT 模式)
2025-07-09 17:45:45,601 - INFO - 🎮 f4操作处理器初始化完成
2025-07-09 17:45:45,609 - INFO - Redis连接成功
2025-07-09 17:45:45,609 - INFO - Redis连接成功
2025-07-09 17:45:45,609 - INFO - Redis连接成功
2025-07-09 17:45:45,615 - INFO - ✅ MongoDB连接成功
2025-07-09 17:45:45,616 - INFO - ✅ MongoDB连接成功
2025-07-09 17:45:45,617 - INFO - ✅ MongoDB连接成功
2025-07-09 17:45:45,858 - INFO - ✅ PostgreSQL连接池创建成功
2025-07-09 17:45:45,859 - INFO - Redis连接成功
2025-07-09 17:45:45,859 - INFO - ✅ f3数据拉取服务启动成功，预定执行时间: ['02:00', '03:00']
2025-07-09 17:45:45,859 - INFO - 🚀 数据拉取调度器启动，预定运行时间: ['02:00', '03:00']
2025-07-09 17:45:45,859 - INFO - 下一次f3/f5同步任务将在 2025-07-10 02:00:00 执行，等待 29654 秒...
2025-07-09 17:45:45,905 - INFO - ✅ PostgreSQL连接池创建成功
2025-07-09 17:45:45,905 - INFO - ✅ f1监听器服务启动成功
2025-07-09 17:45:45,914 - INFO - ✅ 分区创建成功: entries_2025_07
2025-07-09 17:45:45,934 - INFO - ✅ 分区创建成功: entries_2025_08
2025-07-09 17:45:45,952 - INFO - ✅ PostgreSQL连接池创建成功
2025-07-09 17:45:45,952 - INFO - ✅ f2推送回写服务启动成功，工作线程数: 4
2025-07-09 17:45:45,952 - INFO - 🚀 推送工作线程启动: worker_1
2025-07-09 17:45:45,953 - INFO - 🚀 推送工作线程启动: worker_2
2025-07-09 17:45:45,953 - INFO - 🚀 推送工作线程启动: worker_3
2025-07-09 17:45:45,953 - INFO - 🚀 推送工作线程启动: worker_4
2025-07-09 17:45:45,957 - INFO - ✅ PostgreSQL连接池创建成功
2025-07-09 17:45:45,957 - INFO - ✅ f4操作处理服务启动成功
2025-07-09 17:45:45,957 - INFO - 🔌 推送工作线程停止: worker_4
2025-07-09 17:45:45,957 - INFO - 🔌 推送工作线程停止: worker_3
2025-07-09 17:45:46,132 - INFO - 🔌 推送工作线程停止: worker_1
2025-07-09 17:45:46,160 - INFO - 🔌 推送工作线程停止: worker_2
2025-07-10 07:30:38,784 - INFO - 🔌 MongoDB连接已关闭
2025-07-10 07:30:38,785 - INFO - 🔌 MongoDB连接已关闭
2025-07-10 07:30:38,787 - INFO - 🔌 MongoDB连接已关闭
2025-07-10 07:30:38,787 - INFO - 🔌 f1监听器服务已停止
2025-07-10 07:30:38,787 - INFO - 🔌 f2推送回写服务已停止
2025-07-10 07:30:38,787 - INFO - 🔌 f4操作处理服务已停止
2025-07-10 07:30:39,246 - INFO - 🔌 PostgreSQL连接已关闭
2025-07-10 07:30:39,246 - INFO - 🔌 PostgreSQL连接已关闭
2025-07-10 07:30:39,247 - INFO - 🔌 MongoDB连接已关闭
2025-07-10 07:30:39,247 - INFO - 🔌 PostgreSQL连接已关闭
2025-07-10 07:30:39,248 - INFO - 🔌 MongoDB连接已关闭
2025-07-10 07:30:39,248 - INFO - 🔌 PostgreSQL连接已关闭
2025-07-10 07:30:39,248 - INFO - 🔌 MongoDB连接已关闭
2025-07-10 07:30:39,248 - INFO - 🔌 Redis连接已关闭
2025-07-10 07:30:39,249 - INFO - 🔌 Redis连接已关闭
2025-07-10 07:30:39,249 - INFO - 🔌 Redis连接已关闭
2025-07-10 07:30:39,249 - INFO - 🔌 f1监听器服务已停止
2025-07-10 07:30:39,249 - INFO - 🔌 f2推送回写服务已停止
2025-07-10 07:30:39,249 - INFO - 🔌 f4操作处理服务已停止
2025-07-10 07:31:18,203 - INFO - 🔊 f1监听器服务初始化完成
2025-07-10 07:31:18,203 - INFO - 📡 Server6客户端初始化 (新版): http://192.168.3.93:8009
2025-07-10 07:31:18,203 - INFO - 📤 f2推送回写引擎初始化完成 (修复版本: 直接轮询PostgreSQL)
2025-07-10 07:31:18,203 - INFO - 📡 Server6客户端初始化 (新版): http://192.168.3.93:8009
2025-07-10 07:31:18,204 - INFO - 📥 f3数据拉取器初始化完成 (Staging & UPSERT 模式)
2025-07-10 07:31:18,204 - INFO - 🎮 f4操作处理器初始化完成
2025-07-10 07:31:18,213 - INFO - Redis连接成功
2025-07-10 07:31:18,214 - INFO - Redis连接成功
2025-07-10 07:31:18,214 - INFO - Redis连接成功
2025-07-10 07:31:18,220 - INFO - ✅ MongoDB连接成功
2025-07-10 07:31:18,221 - INFO - ✅ MongoDB连接成功
2025-07-10 07:31:18,221 - INFO - ✅ MongoDB连接成功
2025-07-10 07:31:18,554 - INFO - ✅ PostgreSQL连接池创建成功
2025-07-10 07:31:18,555 - INFO - Redis连接成功
2025-07-10 07:31:18,555 - INFO - ✅ f3数据拉取服务启动成功，预定执行时间: ['02:00', '03:00']
2025-07-10 07:31:18,555 - INFO - 🚀 数据拉取调度器启动，预定运行时间: ['02:00', '03:00']
2025-07-10 07:31:18,555 - INFO - 下一次f3/f5同步任务将在 2025-07-11 02:00:00 执行，等待 66521 秒...
2025-07-10 07:31:18,587 - INFO - ✅ PostgreSQL连接池创建成功
2025-07-10 07:31:18,587 - INFO - ✅ f1监听器服务启动成功
2025-07-10 07:31:18,597 - INFO - ✅ 分区创建成功: entries_2025_07
2025-07-10 07:31:18,616 - INFO - ✅ 分区创建成功: entries_2025_08
2025-07-10 07:31:18,637 - INFO - ✅ PostgreSQL连接池创建成功
2025-07-10 07:31:18,637 - INFO - ✅ f2推送回写服务启动成功，工作线程数: 4
2025-07-10 07:31:18,637 - INFO - 🚀 推送工作线程启动: worker_1
2025-07-10 07:31:18,637 - INFO - 🚀 推送工作线程启动: worker_2
2025-07-10 07:31:18,637 - INFO - 🚀 推送工作线程启动: worker_3
2025-07-10 07:31:18,638 - INFO - 🚀 推送工作线程启动: worker_4
2025-07-10 07:31:18,643 - INFO - ✅ PostgreSQL连接池创建成功
2025-07-10 07:31:18,643 - INFO - ✅ f4操作处理服务启动成功
2025-07-10 07:31:18,643 - INFO - 🔌 推送工作线程停止: worker_4
2025-07-10 07:31:18,643 - INFO - 🔌 推送工作线程停止: worker_3
2025-07-10 07:31:18,880 - INFO - 🔌 推送工作线程停止: worker_1
2025-07-10 07:31:18,906 - INFO - 🔌 推送工作线程停止: worker_2
2025-07-10 07:41:29,437 - INFO - 🔌 MongoDB连接已关闭
2025-07-10 07:41:29,438 - INFO - 🔌 MongoDB连接已关闭
2025-07-10 07:41:29,439 - INFO - 🔌 MongoDB连接已关闭
2025-07-10 07:41:29,439 - INFO - 🔌 f1监听器服务已停止
2025-07-10 07:41:29,440 - INFO - 🔌 f2推送回写服务已停止
2025-07-10 07:41:29,440 - INFO - 🔌 f4操作处理服务已停止
2025-07-10 07:41:29,893 - INFO - 🔌 PostgreSQL连接已关闭
2025-07-10 07:41:29,894 - INFO - 🔌 PostgreSQL连接已关闭
2025-07-10 07:41:29,894 - INFO - 🔌 MongoDB连接已关闭
2025-07-10 07:41:29,894 - INFO - 🔌 PostgreSQL连接已关闭
2025-07-10 07:41:29,894 - INFO - 🔌 MongoDB连接已关闭
2025-07-10 07:41:29,894 - INFO - 🔌 PostgreSQL连接已关闭
2025-07-10 07:41:29,895 - INFO - 🔌 MongoDB连接已关闭
2025-07-10 07:41:29,895 - INFO - 🔌 Redis连接已关闭
2025-07-10 07:41:29,895 - INFO - 🔌 Redis连接已关闭
2025-07-10 07:41:29,895 - INFO - 🔌 Redis连接已关闭
2025-07-10 07:41:29,895 - INFO - 🔌 f1监听器服务已停止
2025-07-10 07:41:29,895 - INFO - 🔌 f2推送回写服务已停止
2025-07-10 07:41:29,895 - INFO - 🔌 f4操作处理服务已停止
2025-07-10 07:41:36,596 - INFO - 🔊 f1监听器服务初始化完成
2025-07-10 07:41:36,596 - INFO - 📡 Server6客户端初始化 (新版): http://192.168.3.93:8009
2025-07-10 07:41:36,597 - INFO - 📤 f2推送回写引擎初始化完成 (修复版本: 直接轮询PostgreSQL)
2025-07-10 07:41:36,597 - INFO - 📡 Server6客户端初始化 (新版): http://192.168.3.93:8009
2025-07-10 07:41:36,597 - INFO - 📥 f3数据拉取器初始化完成 (Staging & UPSERT 模式)
2025-07-10 07:41:36,598 - INFO - 🎮 f4操作处理器初始化完成
2025-07-10 07:41:36,607 - INFO - Redis连接成功
2025-07-10 07:41:36,607 - INFO - Redis连接成功
2025-07-10 07:41:36,607 - INFO - Redis连接成功
2025-07-10 07:41:36,613 - INFO - ✅ MongoDB连接成功
2025-07-10 07:41:36,614 - INFO - ✅ MongoDB连接成功
2025-07-10 07:41:36,616 - INFO - ✅ MongoDB连接成功
2025-07-10 07:41:36,850 - INFO - ✅ PostgreSQL连接池创建成功
2025-07-10 07:41:36,851 - INFO - Redis连接成功
2025-07-10 07:41:36,851 - INFO - ✅ f3数据拉取服务启动成功，预定执行时间: ['02:00', '03:00']
2025-07-10 07:41:36,851 - INFO - 🚀 数据拉取调度器启动，预定运行时间: ['02:00', '03:00']
2025-07-10 07:41:36,852 - INFO - 下一次f3/f5同步任务将在 2025-07-11 02:00:00 执行，等待 65903 秒...
2025-07-10 07:41:36,906 - INFO - ✅ PostgreSQL连接池创建成功
2025-07-10 07:41:36,907 - INFO - ✅ f1监听器服务启动成功
2025-07-10 07:41:36,916 - INFO - ✅ 分区创建成功: entries_2025_07
2025-07-10 07:41:36,920 - INFO - ✅ 分区创建成功: entries_2025_08
2025-07-10 07:41:36,944 - INFO - ✅ PostgreSQL连接池创建成功
2025-07-10 07:41:36,944 - INFO - ✅ f2推送回写服务启动成功，工作线程数: 4
2025-07-10 07:41:36,944 - INFO - 🚀 推送工作线程启动: worker_1
2025-07-10 07:41:36,944 - INFO - 🚀 推送工作线程启动: worker_2
2025-07-10 07:41:36,944 - INFO - 🚀 推送工作线程启动: worker_3
2025-07-10 07:41:36,945 - INFO - 🚀 推送工作线程启动: worker_4
2025-07-10 07:41:36,986 - INFO - ✅ PostgreSQL连接池创建成功
2025-07-10 07:41:36,986 - INFO - ✅ f4操作处理服务启动成功
2025-07-10 07:41:36,986 - INFO - 🔌 推送工作线程停止: worker_4
2025-07-10 07:41:36,986 - INFO - 🔌 推送工作线程停止: worker_3
2025-07-10 07:41:36,987 - INFO - 🔌 推送工作线程停止: worker_2
2025-07-10 07:41:36,988 - INFO - 🔌 推送工作线程停止: worker_1
2025-07-10 07:43:31,533 - INFO - 🔌 MongoDB连接已关闭
2025-07-10 07:43:31,535 - INFO - 🔌 MongoDB连接已关闭
2025-07-10 07:43:31,536 - INFO - 🔌 MongoDB连接已关闭
2025-07-10 07:43:31,536 - INFO - 🔌 f1监听器服务已停止
2025-07-10 07:43:31,536 - INFO - 🔌 f2推送回写服务已停止
2025-07-10 07:43:31,536 - INFO - 🔌 f4操作处理服务已停止
2025-07-10 07:43:31,643 - INFO - 🔌 PostgreSQL连接已关闭
2025-07-10 07:43:31,643 - INFO - 🔌 PostgreSQL连接已关闭
2025-07-10 07:43:31,643 - INFO - 🔌 MongoDB连接已关闭
2025-07-10 07:43:31,643 - INFO - 🔌 PostgreSQL连接已关闭
2025-07-10 07:43:31,643 - INFO - 🔌 MongoDB连接已关闭
2025-07-10 07:43:31,644 - INFO - 🔌 PostgreSQL连接已关闭
2025-07-10 07:43:31,644 - INFO - 🔌 MongoDB连接已关闭
2025-07-10 07:43:31,644 - INFO - 🔌 Redis连接已关闭
2025-07-10 07:43:31,644 - INFO - 🔌 Redis连接已关闭
2025-07-10 07:43:31,644 - INFO - 🔌 Redis连接已关闭
2025-07-10 07:43:31,644 - INFO - 🔌 f1监听器服务已停止
2025-07-10 07:43:31,644 - INFO - 🔌 f2推送回写服务已停止
2025-07-10 07:43:31,644 - INFO - 🔌 f4操作处理服务已停止
2025-07-10 07:43:38,641 - INFO - 🔊 f1监听器服务初始化完成
2025-07-10 07:43:38,641 - INFO - 📡 Server6客户端初始化 (新版): http://192.168.3.93:8009
2025-07-10 07:43:38,642 - INFO - 📤 f2推送回写引擎初始化完成 (修复版本: 直接轮询PostgreSQL)
2025-07-10 07:43:38,642 - INFO - 📡 Server6客户端初始化 (新版): http://192.168.3.93:8009
2025-07-10 07:43:38,643 - INFO - 📥 f3数据拉取器初始化完成 (Staging & UPSERT 模式)
2025-07-10 07:43:38,643 - INFO - 🎮 f4操作处理器初始化完成
2025-07-10 07:43:38,654 - INFO - Redis连接成功
2025-07-10 07:43:38,654 - INFO - Redis连接成功
2025-07-10 07:43:38,654 - INFO - Redis连接成功
2025-07-10 07:43:38,660 - INFO - ✅ MongoDB连接成功
2025-07-10 07:43:38,660 - INFO - ✅ MongoDB连接成功
2025-07-10 07:43:38,661 - INFO - ✅ MongoDB连接成功
2025-07-10 07:43:38,924 - INFO - ✅ PostgreSQL连接池创建成功
2025-07-10 07:43:38,926 - INFO - Redis连接成功
2025-07-10 07:43:38,926 - INFO - ✅ f3数据拉取服务启动成功，预定执行时间: ['02:00', '03:00']
2025-07-10 07:43:38,926 - INFO - 🚀 数据拉取调度器启动，预定运行时间: ['02:00', '03:00']
2025-07-10 07:43:38,926 - INFO - 下一次f3/f5同步任务将在 2025-07-11 02:00:00 执行，等待 65781 秒...
2025-07-10 07:43:38,969 - INFO - ✅ PostgreSQL连接池创建成功
2025-07-10 07:43:38,969 - INFO - ✅ f1监听器服务启动成功
2025-07-10 07:43:38,979 - INFO - ✅ 分区创建成功: entries_2025_07
2025-07-10 07:43:38,983 - INFO - ✅ 分区创建成功: entries_2025_08
2025-07-10 07:43:39,012 - INFO - ✅ PostgreSQL连接池创建成功
2025-07-10 07:43:39,012 - INFO - ✅ f2推送回写服务启动成功，工作线程数: 4
2025-07-10 07:43:39,012 - INFO - 🚀 推送工作线程启动: worker_1
2025-07-10 07:43:39,013 - INFO - 🚀 推送工作线程启动: worker_2
2025-07-10 07:43:39,013 - INFO - 🚀 推送工作线程启动: worker_3
2025-07-10 07:43:39,013 - INFO - 🚀 推送工作线程启动: worker_4
2025-07-10 07:43:39,054 - INFO - ✅ PostgreSQL连接池创建成功
2025-07-10 07:43:39,054 - INFO - ✅ f4操作处理服务启动成功
2025-07-10 07:43:39,055 - INFO - 🔌 推送工作线程停止: worker_3
2025-07-10 07:43:39,055 - INFO - 🔌 推送工作线程停止: worker_4
2025-07-10 07:43:39,056 - INFO - 🔌 推送工作线程停止: worker_1
2025-07-10 07:43:39,057 - INFO - 🔌 推送工作线程停止: worker_2
2025-07-10 07:44:18,157 - INFO - 🔌 MongoDB连接已关闭
2025-07-10 07:44:18,159 - INFO - 🔌 MongoDB连接已关闭
2025-07-10 07:44:18,160 - INFO - 🔌 MongoDB连接已关闭
2025-07-10 07:44:18,160 - INFO - 🔌 f1监听器服务已停止
2025-07-10 07:44:18,160 - INFO - 🔌 f2推送回写服务已停止
2025-07-10 07:44:18,160 - INFO - 🔌 f4操作处理服务已停止
2025-07-10 07:44:18,168 - INFO - 🔌 PostgreSQL连接已关闭
2025-07-10 07:44:18,168 - INFO - 🔌 PostgreSQL连接已关闭
2025-07-10 07:44:18,168 - INFO - 🔌 MongoDB连接已关闭
2025-07-10 07:44:18,168 - INFO - 🔌 PostgreSQL连接已关闭
2025-07-10 07:44:18,168 - INFO - 🔌 MongoDB连接已关闭
2025-07-10 07:44:18,168 - INFO - 🔌 PostgreSQL连接已关闭
2025-07-10 07:44:18,168 - INFO - 🔌 MongoDB连接已关闭
2025-07-10 07:44:18,168 - INFO - 🔌 Redis连接已关闭
2025-07-10 07:44:18,168 - INFO - 🔌 Redis连接已关闭
2025-07-10 07:44:18,169 - INFO - 🔌 Redis连接已关闭
2025-07-10 07:44:18,169 - INFO - 🔌 f1监听器服务已停止
2025-07-10 07:44:18,169 - INFO - 🔌 f2推送回写服务已停止
2025-07-10 07:44:18,169 - INFO - 🔌 f4操作处理服务已停止
2025-07-10 07:44:29,781 - INFO - 🔊 f1监听器服务初始化完成
2025-07-10 07:44:29,781 - INFO - 📡 Server6客户端初始化 (新版): http://192.168.3.93:8009
2025-07-10 07:44:29,781 - INFO - 📤 f2推送回写引擎初始化完成 (修复版本: 直接轮询PostgreSQL)
2025-07-10 07:44:29,781 - INFO - 📡 Server6客户端初始化 (新版): http://192.168.3.93:8009
2025-07-10 07:44:29,782 - INFO - 📥 f3数据拉取器初始化完成 (Staging & UPSERT 模式)
2025-07-10 07:44:29,782 - INFO - 🎮 f4操作处理器初始化完成
2025-07-10 07:44:29,792 - INFO - Redis连接成功
2025-07-10 07:44:29,792 - INFO - Redis连接成功
2025-07-10 07:44:29,792 - INFO - Redis连接成功
2025-07-10 07:44:29,798 - INFO - ✅ MongoDB连接成功
2025-07-10 07:44:29,798 - INFO - ✅ MongoDB连接成功
2025-07-10 07:44:29,799 - INFO - ✅ MongoDB连接成功
2025-07-10 07:44:30,077 - INFO - ✅ PostgreSQL连接池创建成功
2025-07-10 07:44:30,078 - INFO - Redis连接成功
2025-07-10 07:44:30,079 - INFO - ✅ f3数据拉取服务启动成功，预定执行时间: ['02:00', '03:00']
2025-07-10 07:44:30,079 - INFO - 🚀 数据拉取调度器启动，预定运行时间: ['02:00', '03:00']
2025-07-10 07:44:30,079 - INFO - 下一次f3/f5同步任务将在 2025-07-11 02:00:00 执行，等待 65730 秒...
2025-07-10 07:44:30,120 - INFO - ✅ PostgreSQL连接池创建成功
2025-07-10 07:44:30,120 - INFO - ✅ f1监听器服务启动成功
2025-07-10 07:44:30,160 - INFO - ✅ 分区创建成功: entries_2025_07
2025-07-10 07:44:30,164 - INFO - ✅ PostgreSQL连接池创建成功
2025-07-10 07:44:30,164 - INFO - ✅ f2推送回写服务启动成功，工作线程数: 4
2025-07-10 07:44:30,164 - INFO - 🚀 推送工作线程启动: worker_1
2025-07-10 07:44:30,164 - INFO - 🚀 推送工作线程启动: worker_2
2025-07-10 07:44:30,164 - INFO - 🚀 推送工作线程启动: worker_3
2025-07-10 07:44:30,165 - INFO - 🚀 推送工作线程启动: worker_4
2025-07-10 07:44:30,165 - INFO - ✅ 分区创建成功: entries_2025_08
2025-07-10 07:44:30,255 - INFO - 📡 开始监听频道: push_job
2025-07-10 07:44:30,257 - INFO - 📡 开始监听频道: partition_check
2025-07-10 07:44:30,258 - INFO - 📡 开始监听频道: sync_trigger
2025-07-10 07:44:30,302 - INFO - ✅ PostgreSQL连接池创建成功
2025-07-10 07:44:30,302 - INFO - ✅ f4操作处理服务启动成功
2025-07-10 07:44:30,302 - INFO - 🔌 推送工作线程停止: worker_4
2025-07-10 07:44:30,302 - INFO - 🔌 推送工作线程停止: worker_3
2025-07-10 07:44:30,303 - INFO - 监听任务被取消
2025-07-10 07:44:30,304 - INFO - 🔌 推送工作线程停止: worker_2
2025-07-10 07:44:30,304 - INFO - 🔌 推送工作线程停止: worker_1
2025-07-10 07:44:30,310 - INFO - 监听连接已关闭
2025-07-10 07:44:39,338 - INFO - 🔊 f1监听器服务初始化完成
2025-07-10 07:44:39,338 - INFO - 📡 Server6客户端初始化 (新版): http://192.168.3.93:8009
2025-07-10 07:44:39,338 - INFO - 📤 f2推送回写引擎初始化完成 (修复版本: 直接轮询PostgreSQL)
2025-07-10 07:44:39,338 - INFO - 📡 Server6客户端初始化 (新版): http://192.168.3.93:8009
2025-07-10 07:44:39,339 - INFO - 📥 f3数据拉取器初始化完成 (Staging & UPSERT 模式)
2025-07-10 07:44:39,339 - INFO - 🎮 f4操作处理器初始化完成
2025-07-10 07:44:39,349 - INFO - Redis连接成功
2025-07-10 07:44:39,349 - INFO - Redis连接成功
2025-07-10 07:44:39,349 - INFO - Redis连接成功
2025-07-10 07:44:39,355 - INFO - ✅ MongoDB连接成功
2025-07-10 07:44:39,356 - INFO - ✅ MongoDB连接成功
2025-07-10 07:44:39,357 - INFO - ✅ MongoDB连接成功
2025-07-10 07:44:39,620 - INFO - ✅ PostgreSQL连接池创建成功
2025-07-10 07:44:39,637 - INFO - Redis连接成功
2025-07-10 07:44:39,637 - INFO - ✅ f3数据拉取服务启动成功，预定执行时间: ['02:00', '03:00']
2025-07-10 07:44:39,637 - INFO - 🚀 数据拉取调度器启动，预定运行时间: ['02:00', '03:00']
2025-07-10 07:44:39,638 - INFO - 下一次f3/f5同步任务将在 2025-07-11 02:00:00 执行，等待 65720 秒...
2025-07-10 07:44:39,657 - INFO - ✅ PostgreSQL连接池创建成功
2025-07-10 07:44:39,657 - INFO - ✅ f1监听器服务启动成功
2025-07-10 07:44:39,661 - INFO - ✅ PostgreSQL连接池创建成功
2025-07-10 07:44:39,661 - INFO - ✅ f2推送回写服务启动成功，工作线程数: 4
2025-07-10 07:44:39,661 - INFO - 🚀 推送工作线程启动: worker_1
2025-07-10 07:44:39,661 - INFO - 🚀 推送工作线程启动: worker_2
2025-07-10 07:44:39,661 - INFO - 🚀 推送工作线程启动: worker_3
2025-07-10 07:44:39,662 - INFO - 🚀 推送工作线程启动: worker_4
2025-07-10 07:44:39,681 - INFO - ✅ 分区创建成功: entries_2025_07
2025-07-10 07:44:39,684 - INFO - ✅ 分区创建成功: entries_2025_08
2025-07-10 07:44:39,689 - INFO - ✅ PostgreSQL连接池创建成功
2025-07-10 07:44:39,689 - INFO - ✅ f4操作处理服务启动成功
2025-07-10 07:44:39,689 - INFO - 🔌 推送工作线程停止: worker_4
2025-07-10 07:44:39,690 - INFO - 🔌 推送工作线程停止: worker_3
2025-07-10 07:44:39,691 - INFO - 🔌 推送工作线程停止: worker_1
2025-07-10 07:44:39,691 - INFO - 🔌 推送工作线程停止: worker_2
2025-07-10 07:46:05,862 - INFO - 🔌 MongoDB连接已关闭
2025-07-10 07:46:05,863 - INFO - 🔌 MongoDB连接已关闭
2025-07-10 07:46:05,864 - INFO - 🔌 MongoDB连接已关闭
2025-07-10 07:46:05,864 - INFO - 🔌 f1监听器服务已停止
2025-07-10 07:46:05,864 - INFO - 🔌 f2推送回写服务已停止
2025-07-10 07:46:05,864 - INFO - 🔌 f4操作处理服务已停止
2025-07-10 07:46:06,328 - INFO - 🔌 PostgreSQL连接已关闭
2025-07-10 07:46:06,328 - INFO - 🔌 PostgreSQL连接已关闭
2025-07-10 07:46:06,328 - INFO - 🔌 MongoDB连接已关闭
2025-07-10 07:46:06,328 - INFO - 🔌 PostgreSQL连接已关闭
2025-07-10 07:46:06,328 - INFO - 🔌 MongoDB连接已关闭
2025-07-10 07:46:06,328 - INFO - 🔌 PostgreSQL连接已关闭
2025-07-10 07:46:06,329 - INFO - 🔌 MongoDB连接已关闭
2025-07-10 07:46:06,329 - INFO - 🔌 Redis连接已关闭
2025-07-10 07:46:06,329 - INFO - 🔌 Redis连接已关闭
2025-07-10 07:46:06,329 - INFO - 🔌 Redis连接已关闭
2025-07-10 07:46:06,329 - INFO - 🔌 f1监听器服务已停止
2025-07-10 07:46:06,329 - INFO - 🔌 f2推送回写服务已停止
2025-07-10 07:46:06,329 - INFO - 🔌 f4操作处理服务已停止
2025-07-10 07:52:49,964 - INFO - 🔊 f1监听器服务初始化完成
2025-07-10 07:52:49,964 - INFO - 📡 Server6客户端初始化 (新版): http://192.168.3.93:8009
2025-07-10 07:52:49,964 - INFO - 📤 f2推送回写引擎初始化完成 (修复版本: 直接轮询PostgreSQL)
2025-07-10 07:52:49,964 - INFO - 📡 Server6客户端初始化 (新版): http://192.168.3.93:8009
2025-07-10 07:52:49,965 - INFO - 📥 f3数据拉取器初始化完成 (Staging & UPSERT 模式)
2025-07-10 07:52:49,965 - INFO - 🎮 f4操作处理器初始化完成
2025-07-10 07:52:49,974 - INFO - Redis连接成功
2025-07-10 07:52:49,974 - INFO - Redis连接成功
2025-07-10 07:52:49,974 - INFO - Redis连接成功
2025-07-10 07:52:49,982 - INFO - ✅ MongoDB连接成功
2025-07-10 07:52:49,985 - INFO - ✅ MongoDB连接成功
2025-07-10 07:52:49,985 - INFO - ✅ MongoDB连接成功
2025-07-10 07:52:50,286 - INFO - ✅ PostgreSQL连接池创建成功
2025-07-10 07:52:50,287 - INFO - Redis连接成功
2025-07-10 07:52:50,287 - INFO - ✅ f3数据拉取服务启动成功，预定执行时间: ['02:00', '03:00']
2025-07-10 07:52:50,287 - INFO - 🚀 数据拉取调度器启动，预定运行时间: ['02:00', '03:00']
2025-07-10 07:52:50,287 - INFO - 下一次f3/f5同步任务将在 2025-07-11 02:00:00 执行，等待 65230 秒...
2025-07-10 07:52:50,319 - INFO - ✅ PostgreSQL连接池创建成功
2025-07-10 07:52:50,320 - INFO - ✅ f1监听器服务启动成功
2025-07-10 07:52:50,341 - INFO - ✅ 分区创建成功: entries_2025_07
2025-07-10 07:52:50,344 - INFO - ✅ PostgreSQL连接池创建成功
2025-07-10 07:52:50,345 - INFO - ✅ 分区创建成功: entries_2025_08
2025-07-10 07:52:50,345 - INFO - ✅ f2推送回写服务启动成功，工作线程数: 4
2025-07-10 07:52:50,345 - INFO - 🚀 推送工作线程启动: worker_1
2025-07-10 07:52:50,345 - INFO - 🚀 推送工作线程启动: worker_2
2025-07-10 07:52:50,345 - INFO - 🚀 推送工作线程启动: worker_3
2025-07-10 07:52:50,346 - INFO - 🚀 推送工作线程启动: worker_4
2025-07-10 07:52:50,374 - INFO - ✅ PostgreSQL连接池创建成功
2025-07-10 07:52:50,374 - INFO - ✅ f4操作处理服务启动成功
2025-07-10 07:52:50,375 - INFO - 🔌 推送工作线程停止: worker_3
2025-07-10 07:52:50,375 - INFO - 🔌 推送工作线程停止: worker_4
2025-07-10 07:52:50,376 - INFO - 🔌 推送工作线程停止: worker_1
2025-07-10 07:52:50,377 - INFO - 🔌 推送工作线程停止: worker_2
2025-07-10 08:01:06,556 - INFO - 🔌 MongoDB连接已关闭
2025-07-10 08:01:06,557 - INFO - 🔌 MongoDB连接已关闭
2025-07-10 08:01:06,558 - INFO - 🔌 MongoDB连接已关闭
2025-07-10 08:01:06,558 - INFO - 🔌 f1监听器服务已停止
2025-07-10 08:01:06,558 - INFO - 🔌 f2推送回写服务已停止
2025-07-10 08:01:06,559 - INFO - 🔌 f4操作处理服务已停止
2025-07-10 08:01:07,021 - INFO - 🔌 PostgreSQL连接已关闭
2025-07-10 08:01:07,021 - INFO - 🔌 PostgreSQL连接已关闭
2025-07-10 08:01:07,021 - INFO - 🔌 MongoDB连接已关闭
2025-07-10 08:01:07,021 - INFO - 🔌 PostgreSQL连接已关闭
2025-07-10 08:01:07,021 - INFO - 🔌 MongoDB连接已关闭
2025-07-10 08:01:07,021 - INFO - 🔌 PostgreSQL连接已关闭
2025-07-10 08:01:07,022 - INFO - 🔌 MongoDB连接已关闭
2025-07-10 08:01:07,022 - INFO - 🔌 Redis连接已关闭
2025-07-10 08:01:07,022 - INFO - 🔌 Redis连接已关闭
2025-07-10 08:01:07,022 - INFO - 🔌 Redis连接已关闭
2025-07-10 08:01:07,022 - INFO - 🔌 f1监听器服务已停止
2025-07-10 08:01:07,022 - INFO - 🔌 f2推送回写服务已停止
2025-07-10 08:01:07,022 - INFO - 🔌 f4操作处理服务已停止
2025-07-10 08:02:23,135 - INFO - 🔌 MongoDB连接已关闭
2025-07-10 08:02:23,136 - INFO - 🔌 MongoDB连接已关闭
2025-07-10 08:02:23,137 - INFO - 🔌 MongoDB连接已关闭
2025-07-10 08:02:23,137 - INFO - 🔌 f1监听器服务已停止
2025-07-10 08:02:23,137 - INFO - 🔌 f2推送回写服务已停止
2025-07-10 08:02:23,138 - INFO - 🔌 f4操作处理服务已停止
2025-07-10 08:02:23,424 - INFO - 🔌 PostgreSQL连接已关闭
2025-07-10 08:02:23,424 - INFO - 🔌 PostgreSQL连接已关闭
2025-07-10 08:02:23,425 - INFO - 🔌 MongoDB连接已关闭
2025-07-10 08:02:23,425 - INFO - 🔌 PostgreSQL连接已关闭
2025-07-10 08:02:23,425 - INFO - 🔌 MongoDB连接已关闭
2025-07-10 08:02:23,425 - INFO - 🔌 PostgreSQL连接已关闭
2025-07-10 08:02:23,425 - INFO - 🔌 MongoDB连接已关闭
2025-07-10 08:02:23,425 - INFO - 🔌 Redis连接已关闭
2025-07-10 08:02:23,425 - INFO - 🔌 Redis连接已关闭
2025-07-10 08:02:23,425 - INFO - 🔌 Redis连接已关闭
2025-07-10 08:02:23,425 - INFO - 🔌 f1监听器服务已停止
2025-07-10 08:02:23,425 - INFO - 🔌 f2推送回写服务已停止
2025-07-10 08:02:23,425 - INFO - 🔌 f4操作处理服务已停止
2025-07-10 08:02:31,572 - INFO - 🔊 f1监听器服务初始化完成
2025-07-10 08:02:31,572 - INFO - 📡 Server6客户端初始化 (新版): http://192.168.3.93:8019
2025-07-10 08:02:31,572 - INFO - 📤 f2推送回写引擎初始化完成 (修复版本: 直接轮询PostgreSQL)
2025-07-10 08:02:31,572 - INFO - 📡 Server6客户端初始化 (新版): http://192.168.3.93:8019
2025-07-10 08:02:31,573 - INFO - 📥 f3数据拉取器初始化完成 (Staging & UPSERT 模式)
2025-07-10 08:02:31,573 - INFO - 🎮 f4操作处理器初始化完成
2025-07-10 08:02:31,582 - INFO - Redis连接成功
2025-07-10 08:02:31,583 - INFO - Redis连接成功
2025-07-10 08:02:31,583 - INFO - Redis连接成功
2025-07-10 08:02:31,588 - INFO - ✅ MongoDB连接成功
2025-07-10 08:02:31,589 - INFO - ✅ MongoDB连接成功
2025-07-10 08:02:31,590 - INFO - ✅ MongoDB连接成功
2025-07-10 08:02:31,837 - INFO - ✅ PostgreSQL连接池创建成功
2025-07-10 08:02:31,838 - INFO - Redis连接成功
2025-07-10 08:02:31,838 - INFO - ✅ f3数据拉取服务启动成功，预定执行时间: ['02:00', '03:00']
2025-07-10 08:02:31,838 - INFO - 🚀 数据拉取调度器启动，预定运行时间: ['02:00', '03:00']
2025-07-10 08:02:31,838 - INFO - 下一次f3/f5同步任务将在 2025-07-11 02:00:00 执行，等待 64648 秒...
2025-07-10 08:02:31,884 - INFO - ✅ PostgreSQL连接池创建成功
2025-07-10 08:02:31,884 - INFO - ✅ f1监听器服务启动成功
2025-07-10 08:02:31,893 - INFO - ✅ 分区创建成功: entries_2025_07
2025-07-10 08:02:31,913 - INFO - ✅ 分区创建成功: entries_2025_08
2025-07-10 08:02:31,917 - INFO - ✅ PostgreSQL连接池创建成功
2025-07-10 08:02:31,917 - INFO - ✅ f2推送回写服务启动成功，工作线程数: 4
2025-07-10 08:02:31,917 - INFO - 🚀 推送工作线程启动: worker_1
2025-07-10 08:02:31,917 - INFO - 🚀 推送工作线程启动: worker_2
2025-07-10 08:02:31,917 - INFO - 🚀 推送工作线程启动: worker_3
2025-07-10 08:02:31,918 - INFO - 🚀 推送工作线程启动: worker_4
2025-07-10 08:02:31,951 - INFO - ✅ PostgreSQL连接池创建成功
2025-07-10 08:02:31,951 - INFO - ✅ f4操作处理服务启动成功
2025-07-10 08:02:31,951 - INFO - 🔌 推送工作线程停止: worker_4
2025-07-10 08:02:31,952 - INFO - 🔌 推送工作线程停止: worker_3
2025-07-10 08:02:31,954 - INFO - 🔌 推送工作线程停止: worker_2
2025-07-10 08:02:31,954 - INFO - 🔌 推送工作线程停止: worker_1
2025-07-10 08:04:30,058 - INFO - 🔌 MongoDB连接已关闭
2025-07-10 08:04:30,059 - INFO - 🔌 MongoDB连接已关闭
2025-07-10 08:04:30,060 - INFO - 🔌 MongoDB连接已关闭
2025-07-10 08:04:30,060 - INFO - 🔌 f1监听器服务已停止
2025-07-10 08:04:30,061 - INFO - 🔌 f2推送回写服务已停止
2025-07-10 08:04:30,061 - INFO - 🔌 f4操作处理服务已停止
2025-07-10 08:04:30,116 - INFO - 🔌 PostgreSQL连接已关闭
2025-07-10 08:04:30,116 - INFO - 🔌 PostgreSQL连接已关闭
2025-07-10 08:04:30,116 - INFO - 🔌 MongoDB连接已关闭
2025-07-10 08:04:30,116 - INFO - 🔌 PostgreSQL连接已关闭
2025-07-10 08:04:30,116 - INFO - 🔌 MongoDB连接已关闭
2025-07-10 08:04:30,116 - INFO - 🔌 PostgreSQL连接已关闭
2025-07-10 08:04:30,116 - INFO - 🔌 MongoDB连接已关闭
2025-07-10 08:04:30,116 - INFO - 🔌 Redis连接已关闭
2025-07-10 08:04:30,116 - INFO - 🔌 Redis连接已关闭
2025-07-10 08:04:30,116 - INFO - 🔌 Redis连接已关闭
2025-07-10 08:04:30,116 - INFO - 🔌 f1监听器服务已停止
2025-07-10 08:04:30,116 - INFO - 🔌 f2推送回写服务已停止
2025-07-10 08:04:30,116 - INFO - 🔌 f4操作处理服务已停止
2025-07-10 08:04:47,650 - INFO - 🔊 f1监听器服务初始化完成
2025-07-10 08:04:47,651 - INFO - 📡 Server6客户端初始化 (新版): http://192.168.3.93:8019
2025-07-10 08:04:47,651 - INFO - 📤 f2推送回写引擎初始化完成 (修复版本: 直接轮询PostgreSQL)
2025-07-10 08:04:47,651 - INFO - 📡 Server6客户端初始化 (新版): http://192.168.3.93:8019
2025-07-10 08:04:47,652 - INFO - 📥 f3数据拉取器初始化完成 (Staging & UPSERT 模式)
2025-07-10 08:04:47,652 - INFO - 🎮 f4操作处理器初始化完成
2025-07-10 08:04:47,662 - INFO - Redis连接成功
2025-07-10 08:04:47,662 - INFO - Redis连接成功
2025-07-10 08:04:47,662 - INFO - Redis连接成功
2025-07-10 08:04:47,667 - INFO - ✅ MongoDB连接成功
2025-07-10 08:04:47,668 - INFO - ✅ MongoDB连接成功
2025-07-10 08:04:47,669 - INFO - ✅ MongoDB连接成功
2025-07-10 08:04:47,913 - INFO - ✅ PostgreSQL连接池创建成功
2025-07-10 08:04:47,914 - INFO - Redis连接成功
2025-07-10 08:04:47,914 - INFO - ✅ f3数据拉取服务启动成功，预定执行时间: ['02:00', '03:00']
2025-07-10 08:04:47,914 - INFO - 🚀 数据拉取调度器启动，预定运行时间: ['02:00', '03:00']
2025-07-10 08:04:47,914 - INFO - 下一次f3/f5同步任务将在 2025-07-11 02:00:00 执行，等待 64512 秒...
2025-07-10 08:04:47,962 - INFO - ✅ PostgreSQL连接池创建成功
2025-07-10 08:04:47,962 - INFO - ✅ f1监听器服务启动成功
2025-07-10 08:04:47,971 - INFO - ✅ 分区创建成功: entries_2025_07
2025-07-10 08:04:47,975 - INFO - ✅ 分区创建成功: entries_2025_08
2025-07-10 08:04:48,013 - INFO - ✅ PostgreSQL连接池创建成功
2025-07-10 08:04:48,013 - INFO - ✅ f2推送回写服务启动成功，工作线程数: 4
2025-07-10 08:04:48,013 - INFO - 🚀 推送工作线程启动: worker_1
2025-07-10 08:04:48,014 - INFO - 🚀 推送工作线程启动: worker_2
2025-07-10 08:04:48,014 - INFO - 🚀 推送工作线程启动: worker_3
2025-07-10 08:04:48,014 - INFO - 🚀 推送工作线程启动: worker_4
2025-07-10 08:04:48,036 - INFO - ✅ PostgreSQL连接池创建成功
2025-07-10 08:04:48,036 - INFO - ✅ f4操作处理服务启动成功
2025-07-10 08:04:48,037 - INFO - 🔌 推送工作线程停止: worker_4
2025-07-10 08:04:48,037 - INFO - 🔌 推送工作线程停止: worker_3
2025-07-10 08:04:48,038 - INFO - 🔌 推送工作线程停止: worker_2
2025-07-10 08:04:48,163 - INFO - 🔌 推送工作线程停止: worker_1
2025-07-10 08:06:12,351 - INFO - 🔌 MongoDB连接已关闭
2025-07-10 08:06:12,353 - INFO - 🔌 MongoDB连接已关闭
2025-07-10 08:06:12,354 - INFO - 🔌 MongoDB连接已关闭
2025-07-10 08:06:12,354 - INFO - 🔌 f1监听器服务已停止
2025-07-10 08:06:12,355 - INFO - 🔌 f2推送回写服务已停止
2025-07-10 08:06:12,355 - INFO - 🔌 f4操作处理服务已停止
2025-07-10 08:06:12,689 - INFO - 🔌 PostgreSQL连接已关闭
2025-07-10 08:06:12,690 - INFO - 🔌 PostgreSQL连接已关闭
2025-07-10 08:06:12,690 - INFO - 🔌 MongoDB连接已关闭
2025-07-10 08:06:12,690 - INFO - 🔌 PostgreSQL连接已关闭
2025-07-10 08:06:12,691 - INFO - 🔌 MongoDB连接已关闭
2025-07-10 08:06:12,691 - INFO - 🔌 PostgreSQL连接已关闭
2025-07-10 08:06:12,691 - INFO - 🔌 MongoDB连接已关闭
2025-07-10 08:06:12,692 - INFO - 🔌 Redis连接已关闭
2025-07-10 08:06:12,692 - INFO - 🔌 Redis连接已关闭
2025-07-10 08:06:12,692 - INFO - 🔌 Redis连接已关闭
2025-07-10 08:06:12,692 - INFO - 🔌 f1监听器服务已停止
2025-07-10 08:06:12,692 - INFO - 🔌 f2推送回写服务已停止
2025-07-10 08:06:12,692 - INFO - 🔌 f4操作处理服务已停止
2025-07-10 08:07:53,667 - INFO - 🔊 f1监听器服务初始化完成
2025-07-10 08:07:53,667 - INFO - 📡 Server6客户端初始化 (新版): http://192.168.3.93:8019
2025-07-10 08:07:53,667 - INFO - 📤 f2推送回写引擎初始化完成 (修复版本: 直接轮询PostgreSQL)
2025-07-10 08:07:53,667 - INFO - 📡 Server6客户端初始化 (新版): http://192.168.3.93:8019
2025-07-10 08:07:53,668 - INFO - 📥 f3数据拉取器初始化完成 (Staging & UPSERT 模式)
2025-07-10 08:07:53,668 - INFO - 🎮 f4操作处理器初始化完成
2025-07-10 08:07:53,678 - INFO - Redis连接成功
2025-07-10 08:07:53,678 - INFO - Redis连接成功
2025-07-10 08:07:53,678 - INFO - Redis连接成功
2025-07-10 08:07:53,686 - INFO - ✅ MongoDB连接成功
2025-07-10 08:07:53,687 - INFO - ✅ MongoDB连接成功
2025-07-10 08:07:53,687 - INFO - ✅ MongoDB连接成功
2025-07-10 08:07:53,942 - INFO - ✅ PostgreSQL连接池创建成功
2025-07-10 08:07:53,942 - INFO - Redis连接成功
2025-07-10 08:07:53,942 - INFO - ✅ f3数据拉取服务启动成功，预定执行时间: ['02:00', '03:00']
2025-07-10 08:07:53,943 - INFO - 🚀 数据拉取调度器启动，预定运行时间: ['02:00', '03:00']
2025-07-10 08:07:53,943 - INFO - 下一次f3/f5同步任务将在 2025-07-11 02:00:00 执行，等待 64326 秒...
2025-07-10 08:07:53,973 - INFO - ✅ PostgreSQL连接池创建成功
2025-07-10 08:07:53,973 - INFO - ✅ f1监听器服务启动成功
2025-07-10 08:07:53,995 - INFO - ✅ 分区创建成功: entries_2025_07
2025-07-10 08:07:53,998 - INFO - ✅ PostgreSQL连接池创建成功
2025-07-10 08:07:53,999 - INFO - ✅ f2推送回写服务启动成功，工作线程数: 4
2025-07-10 08:07:53,999 - INFO - 🚀 推送工作线程启动: worker_1
2025-07-10 08:07:53,999 - INFO - 🚀 推送工作线程启动: worker_2
2025-07-10 08:07:53,999 - INFO - 🚀 推送工作线程启动: worker_3
2025-07-10 08:07:54,000 - INFO - 🚀 推送工作线程启动: worker_4
2025-07-10 08:07:54,001 - INFO - ✅ 分区创建成功: entries_2025_08
2025-07-10 08:07:54,040 - INFO - ✅ PostgreSQL连接池创建成功
2025-07-10 08:07:54,040 - INFO - ✅ f4操作处理服务启动成功
2025-07-10 08:07:54,040 - INFO - 🔌 推送工作线程停止: worker_3
2025-07-10 08:07:54,041 - INFO - 🔌 推送工作线程停止: worker_4
2025-07-10 08:07:54,042 - INFO - 🔌 推送工作线程停止: worker_2
2025-07-10 08:07:54,042 - INFO - 🔌 推送工作线程停止: worker_1
2025-07-10 08:10:26,450 - INFO - 🔌 MongoDB连接已关闭
2025-07-10 08:10:26,452 - INFO - 🔌 MongoDB连接已关闭
2025-07-10 08:10:26,454 - INFO - 🔌 MongoDB连接已关闭
2025-07-10 08:10:26,454 - INFO - 🔌 f1监听器服务已停止
2025-07-10 08:10:26,454 - INFO - 🔌 f2推送回写服务已停止
2025-07-10 08:10:26,454 - INFO - 🔌 f4操作处理服务已停止
2025-07-10 08:10:26,742 - INFO - 🔌 PostgreSQL连接已关闭
2025-07-10 08:10:26,742 - INFO - 🔌 PostgreSQL连接已关闭
2025-07-10 08:10:26,742 - INFO - 🔌 MongoDB连接已关闭
2025-07-10 08:10:26,742 - INFO - 🔌 PostgreSQL连接已关闭
2025-07-10 08:10:26,742 - INFO - 🔌 MongoDB连接已关闭
2025-07-10 08:10:26,743 - INFO - 🔌 PostgreSQL连接已关闭
2025-07-10 08:10:26,743 - INFO - 🔌 MongoDB连接已关闭
2025-07-10 08:10:26,743 - INFO - 🔌 Redis连接已关闭
2025-07-10 08:10:26,743 - INFO - 🔌 Redis连接已关闭
2025-07-10 08:10:26,743 - INFO - 🔌 Redis连接已关闭
2025-07-10 08:10:26,743 - INFO - 🔌 f1监听器服务已停止
2025-07-10 08:10:26,743 - INFO - 🔌 f2推送回写服务已停止
2025-07-10 08:10:26,743 - INFO - 🔌 f4操作处理服务已停止
2025-07-10 08:10:33,448 - INFO - 🔊 f1监听器服务初始化完成
2025-07-10 08:10:33,449 - INFO - 📡 Server6客户端初始化 (新版): http://192.168.3.93:8019
2025-07-10 08:10:33,449 - INFO - 📤 f2推送回写引擎初始化完成 (修复版本: 直接轮询PostgreSQL)
2025-07-10 08:10:33,449 - INFO - 📡 Server6客户端初始化 (新版): http://192.168.3.93:8019
2025-07-10 08:10:33,450 - INFO - 📥 f3数据拉取器初始化完成 (Staging & UPSERT 模式)
2025-07-10 08:10:33,450 - INFO - 🎮 f4操作处理器初始化完成
2025-07-10 08:10:33,459 - INFO - Redis连接成功
2025-07-10 08:10:33,459 - INFO - Redis连接成功
2025-07-10 08:10:33,459 - INFO - Redis连接成功
2025-07-10 08:10:33,465 - INFO - ✅ MongoDB连接成功
2025-07-10 08:10:33,465 - INFO - ✅ MongoDB连接成功
2025-07-10 08:10:33,465 - INFO - ✅ MongoDB连接成功
2025-07-10 08:10:33,735 - INFO - ✅ PostgreSQL连接池创建成功
2025-07-10 08:10:33,736 - INFO - Redis连接成功
2025-07-10 08:10:33,736 - INFO - ✅ f3数据拉取服务启动成功，预定执行时间: ['02:00', '03:00']
2025-07-10 08:10:33,736 - INFO - 🚀 数据拉取调度器启动，预定运行时间: ['02:00', '03:00']
2025-07-10 08:10:33,736 - INFO - 下一次f3/f5同步任务将在 2025-07-11 02:00:00 执行，等待 64166 秒...
2025-07-10 08:10:33,775 - INFO - ✅ PostgreSQL连接池创建成功
2025-07-10 08:10:33,776 - INFO - ✅ f1监听器服务启动成功
2025-07-10 08:10:33,789 - INFO - ✅ 分区创建成功: entries_2025_07
2025-07-10 08:10:33,797 - INFO - ✅ 分区创建成功: entries_2025_08
2025-07-10 08:10:33,829 - INFO - ✅ PostgreSQL连接池创建成功
2025-07-10 08:10:33,829 - INFO - ✅ f2推送回写服务启动成功，工作线程数: 4
2025-07-10 08:10:33,829 - INFO - 🚀 推送工作线程启动: worker_1
2025-07-10 08:10:33,829 - INFO - 🚀 推送工作线程启动: worker_2
2025-07-10 08:10:33,829 - INFO - 🚀 推送工作线程启动: worker_3
2025-07-10 08:10:33,830 - INFO - 🚀 推送工作线程启动: worker_4
2025-07-10 08:10:33,865 - INFO - ✅ PostgreSQL连接池创建成功
2025-07-10 08:10:33,866 - INFO - ✅ f4操作处理服务启动成功
2025-07-10 08:10:33,866 - INFO - 🔌 推送工作线程停止: worker_4
2025-07-10 08:10:33,866 - INFO - 🔌 推送工作线程停止: worker_3
2025-07-10 08:10:33,868 - INFO - 🔌 推送工作线程停止: worker_2
2025-07-10 08:10:33,868 - INFO - 🔌 推送工作线程停止: worker_1
2025-07-10 08:11:01,402 - INFO - 🔌 MongoDB连接已关闭
2025-07-10 08:11:01,405 - INFO - 🔌 MongoDB连接已关闭
2025-07-10 08:11:01,406 - INFO - 🔌 MongoDB连接已关闭
2025-07-10 08:11:01,406 - INFO - 🔌 f1监听器服务已停止
2025-07-10 08:11:01,406 - INFO - 🔌 f2推送回写服务已停止
2025-07-10 08:11:01,406 - INFO - 🔌 f4操作处理服务已停止
2025-07-10 08:11:01,469 - INFO - 🔌 PostgreSQL连接已关闭
2025-07-10 08:11:01,470 - INFO - 🔌 PostgreSQL连接已关闭
2025-07-10 08:11:01,470 - INFO - 🔌 MongoDB连接已关闭
2025-07-10 08:11:01,470 - INFO - 🔌 PostgreSQL连接已关闭
2025-07-10 08:11:01,470 - INFO - 🔌 MongoDB连接已关闭
2025-07-10 08:11:01,470 - INFO - 🔌 PostgreSQL连接已关闭
2025-07-10 08:11:01,470 - INFO - 🔌 MongoDB连接已关闭
2025-07-10 08:11:01,470 - INFO - 🔌 Redis连接已关闭
2025-07-10 08:11:01,470 - INFO - 🔌 Redis连接已关闭
2025-07-10 08:11:01,470 - INFO - 🔌 Redis连接已关闭
2025-07-10 08:11:01,470 - INFO - 🔌 f1监听器服务已停止
2025-07-10 08:11:01,470 - INFO - 🔌 f2推送回写服务已停止
2025-07-10 08:11:01,470 - INFO - 🔌 f4操作处理服务已停止
2025-07-10 08:11:35,931 - INFO - 🔊 f1监听器服务初始化完成
2025-07-10 08:11:35,932 - INFO - 📡 Server6客户端初始化 (新版): http://192.168.3.93:8019
2025-07-10 08:11:35,932 - INFO - 📤 f2推送回写引擎初始化完成 (修复版本: 直接轮询PostgreSQL)
2025-07-10 08:11:35,932 - INFO - 📡 Server6客户端初始化 (新版): http://192.168.3.93:8019
2025-07-10 08:11:35,933 - INFO - 📥 f3数据拉取器初始化完成 (Staging & UPSERT 模式)
2025-07-10 08:11:35,933 - INFO - 🎮 f4操作处理器初始化完成
2025-07-10 08:11:35,945 - INFO - Redis连接成功
2025-07-10 08:11:35,946 - INFO - Redis连接成功
2025-07-10 08:11:35,946 - INFO - Redis连接成功
2025-07-10 08:11:35,952 - INFO - ✅ MongoDB连接成功
2025-07-10 08:11:35,952 - INFO - ✅ MongoDB连接成功
2025-07-10 08:11:35,953 - INFO - ✅ MongoDB连接成功
2025-07-10 08:11:36,208 - INFO - ✅ PostgreSQL连接池创建成功
2025-07-10 08:11:36,209 - INFO - Redis连接成功
2025-07-10 08:11:36,209 - INFO - ✅ f3数据拉取服务启动成功，预定执行时间: ['02:00', '03:00']
2025-07-10 08:11:36,209 - INFO - 🚀 数据拉取调度器启动，预定运行时间: ['02:00', '03:00']
2025-07-10 08:11:36,209 - INFO - 下一次f3/f5同步任务将在 2025-07-11 02:00:00 执行，等待 64104 秒...
2025-07-10 08:11:36,231 - INFO - ✅ PostgreSQL连接池创建成功
2025-07-10 08:11:36,231 - INFO - ✅ f1监听器服务启动成功
2025-07-10 08:11:36,240 - INFO - ✅ 分区创建成功: entries_2025_07
2025-07-10 08:11:36,259 - INFO - ✅ 分区创建成功: entries_2025_08
2025-07-10 08:11:36,264 - INFO - ✅ PostgreSQL连接池创建成功
2025-07-10 08:11:36,264 - INFO - ✅ f2推送回写服务启动成功，工作线程数: 4
2025-07-10 08:11:36,264 - INFO - 🚀 推送工作线程启动: worker_1
2025-07-10 08:11:36,264 - INFO - 🚀 推送工作线程启动: worker_2
2025-07-10 08:11:36,265 - INFO - 🚀 推送工作线程启动: worker_3
2025-07-10 08:11:36,265 - INFO - 🚀 推送工作线程启动: worker_4
2025-07-10 08:11:36,303 - INFO - ✅ PostgreSQL连接池创建成功
2025-07-10 08:11:36,303 - INFO - ✅ f4操作处理服务启动成功
2025-07-10 08:11:36,304 - INFO - 🔌 推送工作线程停止: worker_4
2025-07-10 08:11:36,304 - INFO - 🔌 推送工作线程停止: worker_3
2025-07-10 08:11:36,306 - INFO - 🔌 推送工作线程停止: worker_1
2025-07-10 08:11:36,306 - INFO - 🔌 推送工作线程停止: worker_2
2025-07-10 08:12:09,665 - INFO - 🔌 MongoDB连接已关闭
2025-07-10 08:12:09,667 - INFO - 🔌 MongoDB连接已关闭
2025-07-10 08:12:09,668 - INFO - 🔌 MongoDB连接已关闭
2025-07-10 08:12:09,669 - INFO - 🔌 f1监听器服务已停止
2025-07-10 08:12:09,669 - INFO - 🔌 f2推送回写服务已停止
2025-07-10 08:12:09,669 - INFO - 🔌 f4操作处理服务已停止
2025-07-10 08:12:09,962 - INFO - 🔌 PostgreSQL连接已关闭
2025-07-10 08:12:09,962 - INFO - 🔌 PostgreSQL连接已关闭
2025-07-10 08:12:09,962 - INFO - 🔌 MongoDB连接已关闭
2025-07-10 08:12:09,962 - INFO - 🔌 PostgreSQL连接已关闭
2025-07-10 08:12:09,962 - INFO - 🔌 MongoDB连接已关闭
2025-07-10 08:12:09,962 - INFO - 🔌 PostgreSQL连接已关闭
2025-07-10 08:12:09,963 - INFO - 🔌 MongoDB连接已关闭
2025-07-10 08:12:09,963 - INFO - 🔌 Redis连接已关闭
2025-07-10 08:12:09,963 - INFO - 🔌 Redis连接已关闭
2025-07-10 08:12:09,963 - INFO - 🔌 Redis连接已关闭
2025-07-10 08:12:09,963 - INFO - 🔌 f1监听器服务已停止
2025-07-10 08:12:09,963 - INFO - 🔌 f2推送回写服务已停止
2025-07-10 08:12:09,963 - INFO - 🔌 f4操作处理服务已停止
2025-07-10 08:12:16,621 - INFO - 🔊 f1监听器服务初始化完成
2025-07-10 08:12:16,622 - INFO - 📡 Server6客户端初始化 (新版): http://192.168.3.93:8019
2025-07-10 08:12:16,622 - INFO - 📤 f2推送回写引擎初始化完成 (修复版本: 直接轮询PostgreSQL)
2025-07-10 08:12:16,622 - INFO - 📡 Server6客户端初始化 (新版): http://192.168.3.93:8019
2025-07-10 08:12:16,623 - INFO - 📥 f3数据拉取器初始化完成 (Staging & UPSERT 模式)
2025-07-10 08:12:16,623 - INFO - 🎮 f4操作处理器初始化完成
2025-07-10 08:12:16,632 - INFO - Redis连接成功
2025-07-10 08:12:16,633 - INFO - Redis连接成功
2025-07-10 08:12:16,633 - INFO - Redis连接成功
2025-07-10 08:12:16,640 - INFO - ✅ MongoDB连接成功
2025-07-10 08:12:16,640 - INFO - ✅ MongoDB连接成功
2025-07-10 08:12:16,641 - INFO - ✅ MongoDB连接成功
2025-07-10 08:12:16,936 - INFO - ✅ PostgreSQL连接池创建成功
2025-07-10 08:12:16,937 - INFO - Redis连接成功
2025-07-10 08:12:16,937 - INFO - ✅ f3数据拉取服务启动成功，预定执行时间: ['02:00', '03:00']
2025-07-10 08:12:16,937 - INFO - 🚀 数据拉取调度器启动，预定运行时间: ['02:00', '03:00']
2025-07-10 08:12:16,937 - INFO - 下一次f3/f5同步任务将在 2025-07-11 02:00:00 执行，等待 64063 秒...
2025-07-10 08:12:16,990 - INFO - ✅ PostgreSQL连接池创建成功
2025-07-10 08:12:16,990 - INFO - ✅ f1监听器服务启动成功
2025-07-10 08:12:17,000 - INFO - ✅ 分区创建成功: entries_2025_07
2025-07-10 08:12:17,004 - INFO - ✅ 分区创建成功: entries_2025_08
2025-07-10 08:12:17,040 - INFO - ✅ PostgreSQL连接池创建成功
2025-07-10 08:12:17,040 - INFO - ✅ f2推送回写服务启动成功，工作线程数: 4
2025-07-10 08:12:17,040 - INFO - 🚀 推送工作线程启动: worker_1
2025-07-10 08:12:17,040 - INFO - 🚀 推送工作线程启动: worker_2
2025-07-10 08:12:17,040 - INFO - 🚀 推送工作线程启动: worker_3
2025-07-10 08:12:17,041 - INFO - 🚀 推送工作线程启动: worker_4
2025-07-10 08:12:17,090 - INFO - ✅ PostgreSQL连接池创建成功
2025-07-10 08:12:17,090 - INFO - ✅ f4操作处理服务启动成功
2025-07-10 08:12:17,090 - INFO - 🔌 推送工作线程停止: worker_3
2025-07-10 08:12:17,090 - INFO - 🔌 推送工作线程停止: worker_4
2025-07-10 08:12:17,092 - INFO - 🔌 推送工作线程停止: worker_2
2025-07-10 08:12:17,092 - INFO - 🔌 推送工作线程停止: worker_1
2025-07-10 08:13:13,884 - INFO - 🔌 MongoDB连接已关闭
2025-07-10 08:13:13,886 - INFO - 🔌 MongoDB连接已关闭
2025-07-10 08:13:13,887 - INFO - 🔌 MongoDB连接已关闭
2025-07-10 08:13:13,887 - INFO - 🔌 f1监听器服务已停止
2025-07-10 08:13:13,887 - INFO - 🔌 f2推送回写服务已停止
2025-07-10 08:13:13,887 - INFO - 🔌 f4操作处理服务已停止
2025-07-10 08:13:14,159 - INFO - 🔌 PostgreSQL连接已关闭
2025-07-10 08:13:14,159 - INFO - 🔌 PostgreSQL连接已关闭
2025-07-10 08:13:14,159 - INFO - 🔌 MongoDB连接已关闭
2025-07-10 08:13:14,159 - INFO - 🔌 PostgreSQL连接已关闭
2025-07-10 08:13:14,159 - INFO - 🔌 MongoDB连接已关闭
2025-07-10 08:13:14,159 - INFO - 🔌 PostgreSQL连接已关闭
2025-07-10 08:13:14,159 - INFO - 🔌 MongoDB连接已关闭
2025-07-10 08:13:14,159 - INFO - 🔌 Redis连接已关闭
2025-07-10 08:13:14,160 - INFO - 🔌 Redis连接已关闭
2025-07-10 08:13:14,160 - INFO - 🔌 Redis连接已关闭
2025-07-10 08:13:14,160 - INFO - 🔌 f1监听器服务已停止
2025-07-10 08:13:14,160 - INFO - 🔌 f2推送回写服务已停止
2025-07-10 08:13:14,160 - INFO - 🔌 f4操作处理服务已停止
2025-07-10 08:13:24,488 - INFO - 🔊 f1监听器服务初始化完成
2025-07-10 08:13:24,489 - INFO - 📡 Server6客户端初始化 (新版): http://192.168.3.93:8019
2025-07-10 08:13:24,489 - INFO - 📤 f2推送回写引擎初始化完成 (修复版本: 直接轮询PostgreSQL)
2025-07-10 08:13:24,489 - INFO - 📡 Server6客户端初始化 (新版): http://192.168.3.93:8019
2025-07-10 08:13:24,490 - INFO - 📥 f3数据拉取器初始化完成 (Staging & UPSERT 模式)
2025-07-10 08:13:24,490 - INFO - 🎮 f4操作处理器初始化完成
2025-07-10 08:13:24,499 - INFO - Redis连接成功
2025-07-10 08:13:24,499 - INFO - Redis连接成功
2025-07-10 08:13:24,499 - INFO - Redis连接成功
2025-07-10 08:13:24,506 - INFO - ✅ MongoDB连接成功
2025-07-10 08:13:24,507 - INFO - ✅ MongoDB连接成功
2025-07-10 08:13:24,507 - INFO - ✅ MongoDB连接成功
2025-07-10 08:13:24,843 - INFO - ✅ PostgreSQL连接池创建成功
2025-07-10 08:13:24,844 - INFO - Redis连接成功
2025-07-10 08:13:24,844 - INFO - ✅ f3数据拉取服务启动成功，预定执行时间: ['02:00', '03:00']
2025-07-10 08:13:24,844 - INFO - 🚀 数据拉取调度器启动，预定运行时间: ['02:00', '03:00']
2025-07-10 08:13:24,844 - INFO - 下一次f3/f5同步任务将在 2025-07-11 02:00:00 执行，等待 63995 秒...
2025-07-10 08:13:24,875 - INFO - ✅ PostgreSQL连接池创建成功
2025-07-10 08:13:24,875 - INFO - ✅ f2推送回写服务启动成功，工作线程数: 4
2025-07-10 08:13:24,875 - INFO - 🚀 推送工作线程启动: worker_1
2025-07-10 08:13:24,875 - INFO - 🚀 推送工作线程启动: worker_2
2025-07-10 08:13:24,875 - INFO - 🚀 推送工作线程启动: worker_3
2025-07-10 08:13:24,876 - INFO - 🚀 推送工作线程启动: worker_4
2025-07-10 08:13:24,916 - INFO - ✅ PostgreSQL连接池创建成功
2025-07-10 08:13:24,917 - INFO - ✅ f1监听器服务启动成功
2025-07-10 08:13:24,921 - INFO - ✅ PostgreSQL连接池创建成功
2025-07-10 08:13:24,922 - INFO - ✅ f4操作处理服务启动成功
2025-07-10 08:13:24,922 - INFO - 🔌 推送工作线程停止: worker_4
2025-07-10 08:13:24,922 - INFO - 🔌 推送工作线程停止: worker_3
2025-07-10 08:13:24,923 - INFO - 🔌 推送工作线程停止: worker_1
2025-07-10 08:13:24,924 - INFO - 🔌 推送工作线程停止: worker_2
2025-07-10 08:14:14,900 - INFO - 🔌 MongoDB连接已关闭
2025-07-10 08:14:14,901 - INFO - 🔌 MongoDB连接已关闭
2025-07-10 08:14:14,902 - INFO - 🔌 MongoDB连接已关闭
2025-07-10 08:14:14,903 - INFO - 🔌 f1监听器服务已停止
2025-07-10 08:14:14,903 - INFO - 🔌 f2推送回写服务已停止
2025-07-10 08:14:14,903 - INFO - 🔌 f4操作处理服务已停止
2025-07-10 08:14:15,019 - INFO - 🔌 PostgreSQL连接已关闭
2025-07-10 08:14:15,019 - INFO - 🔌 PostgreSQL连接已关闭
2025-07-10 08:14:15,019 - INFO - 🔌 MongoDB连接已关闭
2025-07-10 08:14:15,019 - INFO - 🔌 PostgreSQL连接已关闭
2025-07-10 08:14:15,019 - INFO - 🔌 MongoDB连接已关闭
2025-07-10 08:14:15,019 - INFO - 🔌 PostgreSQL连接已关闭
2025-07-10 08:14:15,020 - INFO - 🔌 MongoDB连接已关闭
2025-07-10 08:14:15,020 - INFO - 🔌 Redis连接已关闭
2025-07-10 08:14:15,020 - INFO - 🔌 Redis连接已关闭
2025-07-10 08:14:15,020 - INFO - 🔌 Redis连接已关闭
2025-07-10 08:14:15,020 - INFO - 🔌 f1监听器服务已停止
2025-07-10 08:14:15,020 - INFO - 🔌 f2推送回写服务已停止
2025-07-10 08:14:15,020 - INFO - 🔌 f4操作处理服务已停止
2025-07-10 08:15:12,308 - INFO - 🔊 f1监听器服务初始化完成
2025-07-10 08:15:12,308 - INFO - 📡 Server6客户端初始化 (新版): http://192.168.3.93:8019
2025-07-10 08:15:12,308 - INFO - 📤 f2推送回写引擎初始化完成 (修复版本: 直接轮询PostgreSQL)
2025-07-10 08:15:12,308 - INFO - 📡 Server6客户端初始化 (新版): http://192.168.3.93:8019
2025-07-10 08:15:12,309 - INFO - 📥 f3数据拉取器初始化完成 (Staging & UPSERT 模式)
2025-07-10 08:15:12,309 - INFO - 🎮 f4操作处理器初始化完成
2025-07-10 08:15:12,319 - INFO - Redis连接成功
2025-07-10 08:15:12,319 - INFO - Redis连接成功
2025-07-10 08:15:12,319 - INFO - Redis连接成功
2025-07-10 08:15:12,326 - INFO - ✅ MongoDB连接成功
2025-07-10 08:15:12,326 - INFO - ✅ MongoDB连接成功
2025-07-10 08:15:12,326 - INFO - ✅ MongoDB连接成功
2025-07-10 08:15:12,614 - INFO - ✅ PostgreSQL连接池创建成功
2025-07-10 08:15:12,631 - INFO - Redis连接成功
2025-07-10 08:15:12,632 - INFO - ✅ f3数据拉取服务启动成功，预定执行时间: ['02:00', '03:00']
2025-07-10 08:15:12,632 - INFO - 🚀 数据拉取调度器启动，预定运行时间: ['02:00', '03:00']
2025-07-10 08:15:12,632 - INFO - 下一次f3/f5同步任务将在 2025-07-11 02:00:00 执行，等待 63887 秒...
2025-07-10 08:15:12,652 - INFO - ✅ PostgreSQL连接池创建成功
2025-07-10 08:15:12,652 - INFO - ✅ f1监听器服务启动成功
2025-07-10 08:15:12,656 - INFO - ✅ PostgreSQL连接池创建成功
2025-07-10 08:15:12,656 - INFO - ✅ f2推送回写服务启动成功，工作线程数: 4
2025-07-10 08:15:12,657 - INFO - 🚀 推送工作线程启动: worker_1
2025-07-10 08:15:12,657 - INFO - 🚀 推送工作线程启动: worker_2
2025-07-10 08:15:12,657 - INFO - 🚀 推送工作线程启动: worker_3
2025-07-10 08:15:12,657 - INFO - 🚀 推送工作线程启动: worker_4
2025-07-10 08:15:12,662 - INFO - ✅ 分区创建成功: entries_2025_07
2025-07-10 08:15:12,666 - INFO - ✅ 分区创建成功: entries_2025_08
2025-07-10 08:15:12,689 - INFO - ✅ PostgreSQL连接池创建成功
2025-07-10 08:15:12,689 - INFO - ✅ f4操作处理服务启动成功
2025-07-10 08:15:12,689 - INFO - 🔌 推送工作线程停止: worker_3
2025-07-10 08:15:12,689 - INFO - 🔌 推送工作线程停止: worker_4
2025-07-10 08:15:12,690 - INFO - 🔌 推送工作线程停止: worker_2
2025-07-10 08:15:12,691 - INFO - 🔌 推送工作线程停止: worker_1
2025-07-10 08:16:00,754 - INFO - 🔌 MongoDB连接已关闭
2025-07-10 08:16:00,756 - INFO - 🔌 MongoDB连接已关闭
2025-07-10 08:16:00,757 - INFO - 🔌 MongoDB连接已关闭
2025-07-10 08:16:00,757 - INFO - 🔌 f1监听器服务已停止
2025-07-10 08:16:00,757 - INFO - 🔌 f2推送回写服务已停止
2025-07-10 08:16:00,757 - INFO - 🔌 f4操作处理服务已停止
2025-07-10 08:16:00,841 - INFO - 🔌 PostgreSQL连接已关闭
2025-07-10 08:16:00,841 - INFO - 🔌 PostgreSQL连接已关闭
2025-07-10 08:16:00,842 - INFO - 🔌 MongoDB连接已关闭
2025-07-10 08:16:00,842 - INFO - 🔌 PostgreSQL连接已关闭
2025-07-10 08:16:00,843 - INFO - 🔌 MongoDB连接已关闭
2025-07-10 08:16:00,843 - INFO - 🔌 PostgreSQL连接已关闭
2025-07-10 08:16:00,844 - INFO - 🔌 MongoDB连接已关闭
2025-07-10 08:16:00,844 - INFO - 🔌 Redis连接已关闭
2025-07-10 08:16:00,844 - INFO - 🔌 Redis连接已关闭
2025-07-10 08:16:00,845 - INFO - 🔌 Redis连接已关闭
2025-07-10 08:16:00,845 - INFO - 🔌 f1监听器服务已停止
2025-07-10 08:16:00,845 - INFO - 🔌 f2推送回写服务已停止
2025-07-10 08:16:00,845 - INFO - 🔌 f4操作处理服务已停止
2025-07-10 08:16:08,108 - INFO - 🔊 f1监听器服务初始化完成
2025-07-10 08:16:08,108 - INFO - 📡 Server6客户端初始化 (新版): http://192.168.3.93:8019
2025-07-10 08:16:08,108 - INFO - 📤 f2推送回写引擎初始化完成 (修复版本: 直接轮询PostgreSQL)
2025-07-10 08:16:08,108 - INFO - 📡 Server6客户端初始化 (新版): http://192.168.3.93:8019
2025-07-10 08:16:08,109 - INFO - 📥 f3数据拉取器初始化完成 (Staging & UPSERT 模式)
2025-07-10 08:16:08,109 - INFO - 🎮 f4操作处理器初始化完成
2025-07-10 08:16:08,119 - INFO - Redis连接成功
2025-07-10 08:16:08,119 - INFO - Redis连接成功
2025-07-10 08:16:08,120 - INFO - Redis连接成功
2025-07-10 08:16:08,125 - INFO - ✅ MongoDB连接成功
2025-07-10 08:16:08,126 - INFO - ✅ MongoDB连接成功
2025-07-10 08:16:08,127 - INFO - ✅ MongoDB连接成功
2025-07-10 08:16:08,395 - INFO - ✅ PostgreSQL连接池创建成功
2025-07-10 08:16:08,396 - INFO - Redis连接成功
2025-07-10 08:16:08,396 - INFO - ✅ f3数据拉取服务启动成功，预定执行时间: ['02:00', '03:00']
2025-07-10 08:16:08,396 - INFO - 🚀 数据拉取调度器启动，预定运行时间: ['02:00', '03:00']
2025-07-10 08:16:08,396 - INFO - 下一次f3/f5同步任务将在 2025-07-11 02:00:00 执行，等待 63832 秒...
2025-07-10 08:16:08,422 - INFO - ✅ PostgreSQL连接池创建成功
2025-07-10 08:16:08,422 - INFO - ✅ f1监听器服务启动成功
2025-07-10 08:16:08,431 - INFO - ✅ 分区创建成功: entries_2025_07
2025-07-10 08:16:08,435 - INFO - ✅ 分区创建成功: entries_2025_08
2025-07-10 08:16:08,501 - INFO - ✅ PostgreSQL连接池创建成功
2025-07-10 08:16:08,501 - INFO - ✅ f2推送回写服务启动成功，工作线程数: 4
2025-07-10 08:16:08,501 - INFO - 🚀 推送工作线程启动: worker_1
2025-07-10 08:16:08,501 - INFO - 🚀 推送工作线程启动: worker_2
2025-07-10 08:16:08,502 - INFO - 🚀 推送工作线程启动: worker_3
2025-07-10 08:16:08,502 - INFO - 🚀 推送工作线程启动: worker_4
2025-07-10 08:16:08,530 - INFO - ✅ PostgreSQL连接池创建成功
2025-07-10 08:16:08,530 - INFO - ✅ f4操作处理服务启动成功
2025-07-10 08:16:08,531 - INFO - 🔌 推送工作线程停止: worker_4
2025-07-10 08:16:08,531 - INFO - 🔌 推送工作线程停止: worker_3
2025-07-10 08:16:08,532 - INFO - 🔌 推送工作线程停止: worker_1
2025-07-10 08:16:08,532 - INFO - 🔌 推送工作线程停止: worker_2
2025-07-10 08:47:58,338 - INFO - 🔌 MongoDB连接已关闭
2025-07-10 08:47:58,339 - INFO - 🔌 MongoDB连接已关闭
2025-07-10 08:47:58,340 - INFO - 🔌 MongoDB连接已关闭
2025-07-10 08:47:58,340 - INFO - 🔌 f1监听器服务已停止
2025-07-10 08:47:58,340 - INFO - 🔌 f2推送回写服务已停止
2025-07-10 08:47:58,340 - INFO - 🔌 f4操作处理服务已停止
2025-07-10 08:47:58,767 - INFO - 🔌 PostgreSQL连接已关闭
2025-07-10 08:47:58,767 - INFO - 🔌 PostgreSQL连接已关闭
2025-07-10 08:47:58,767 - INFO - 🔌 MongoDB连接已关闭
2025-07-10 08:47:58,767 - INFO - 🔌 PostgreSQL连接已关闭
2025-07-10 08:47:58,767 - INFO - 🔌 MongoDB连接已关闭
2025-07-10 08:47:58,767 - INFO - 🔌 PostgreSQL连接已关闭
2025-07-10 08:47:58,767 - INFO - 🔌 MongoDB连接已关闭
2025-07-10 08:47:58,767 - INFO - 🔌 Redis连接已关闭
2025-07-10 08:47:58,767 - INFO - 🔌 Redis连接已关闭
2025-07-10 08:47:58,767 - INFO - 🔌 Redis连接已关闭
2025-07-10 08:47:58,767 - INFO - 🔌 f1监听器服务已停止
2025-07-10 08:47:58,768 - INFO - 🔌 f2推送回写服务已停止
2025-07-10 08:47:58,768 - INFO - 🔌 f4操作处理服务已停止
2025-07-10 08:54:44,293 - INFO - 🔊 f1监听器服务初始化完成
2025-07-10 08:54:44,293 - INFO - 📡 Server6客户端初始化 (新版): http://192.168.3.93:8019
2025-07-10 08:54:44,293 - INFO - 📤 f2推送回写引擎初始化完成 (修复版本: 直接轮询PostgreSQL)
2025-07-10 08:54:44,293 - INFO - 📡 Server6客户端初始化 (新版): http://192.168.3.93:8019
2025-07-10 08:54:44,294 - INFO - 📥 f3数据拉取器初始化完成 (Staging & UPSERT 模式)
2025-07-10 08:54:44,294 - INFO - 🎮 f4操作处理器初始化完成
2025-07-10 08:54:44,305 - INFO - Redis连接成功
2025-07-10 08:54:44,305 - INFO - Redis连接成功
2025-07-10 08:54:44,305 - INFO - Redis连接成功
2025-07-10 08:54:44,310 - INFO - ✅ MongoDB连接成功
2025-07-10 08:54:44,310 - INFO - ✅ MongoDB连接成功
2025-07-10 08:54:44,312 - INFO - ✅ MongoDB连接成功
2025-07-10 08:54:44,533 - INFO - ✅ PostgreSQL连接池创建成功
2025-07-10 08:54:44,534 - INFO - Redis连接成功
2025-07-10 08:54:44,534 - INFO - ✅ f3数据拉取服务启动成功，预定执行时间: ['02:00', '03:00']
2025-07-10 08:54:44,534 - INFO - 🚀 数据拉取调度器启动，预定运行时间: ['02:00', '03:00']
2025-07-10 08:54:44,534 - INFO - 下一次f3/f5同步任务将在 2025-07-11 02:00:00 执行，等待 61515 秒...
2025-07-10 08:54:44,582 - INFO - ✅ PostgreSQL连接池创建成功
2025-07-10 08:54:44,582 - INFO - ✅ f1监听器服务启动成功
2025-07-10 08:54:44,591 - INFO - ✅ 分区创建成功: entries_2025_07
2025-07-10 08:54:44,595 - INFO - ✅ 分区创建成功: entries_2025_08
2025-07-10 08:54:44,618 - INFO - ✅ PostgreSQL连接池创建成功
2025-07-10 08:54:44,618 - INFO - ✅ f2推送回写服务启动成功，工作线程数: 4
2025-07-10 08:54:44,618 - INFO - 🚀 推送工作线程启动: worker_1
2025-07-10 08:54:44,618 - INFO - 🚀 推送工作线程启动: worker_2
2025-07-10 08:54:44,618 - INFO - 🚀 推送工作线程启动: worker_3
2025-07-10 08:54:44,619 - INFO - 🚀 推送工作线程启动: worker_4
2025-07-10 08:54:44,665 - INFO - ✅ PostgreSQL连接池创建成功
2025-07-10 08:54:44,665 - INFO - ✅ f4操作处理服务启动成功
2025-07-10 08:54:44,665 - INFO - 🔌 推送工作线程停止: worker_4
2025-07-10 08:54:44,665 - INFO - 🔌 推送工作线程停止: worker_3
2025-07-10 08:54:44,667 - INFO - 🔌 推送工作线程停止: worker_2
2025-07-10 08:54:44,667 - INFO - 🔌 推送工作线程停止: worker_1
2025-07-10 08:55:45,893 - INFO - 🔌 MongoDB连接已关闭
2025-07-10 08:55:45,894 - INFO - 🔌 MongoDB连接已关闭
2025-07-10 08:55:45,895 - INFO - 🔌 MongoDB连接已关闭
2025-07-10 08:55:45,895 - INFO - 🔌 f1监听器服务已停止
2025-07-10 08:55:45,895 - INFO - 🔌 f2推送回写服务已停止
2025-07-10 08:55:45,895 - INFO - 🔌 f4操作处理服务已停止
2025-07-10 08:55:46,324 - INFO - 🔌 PostgreSQL连接已关闭
2025-07-10 08:55:46,324 - INFO - 🔌 PostgreSQL连接已关闭
2025-07-10 08:55:46,324 - INFO - 🔌 MongoDB连接已关闭
2025-07-10 08:55:46,325 - INFO - 🔌 PostgreSQL连接已关闭
2025-07-10 08:55:46,325 - INFO - 🔌 MongoDB连接已关闭
2025-07-10 08:55:46,325 - INFO - 🔌 PostgreSQL连接已关闭
2025-07-10 08:55:46,325 - INFO - 🔌 MongoDB连接已关闭
2025-07-10 08:55:46,325 - INFO - 🔌 Redis连接已关闭
2025-07-10 08:55:46,325 - INFO - 🔌 Redis连接已关闭
2025-07-10 08:55:46,325 - INFO - 🔌 Redis连接已关闭
2025-07-10 08:55:46,325 - INFO - 🔌 f1监听器服务已停止
2025-07-10 08:55:46,325 - INFO - 🔌 f2推送回写服务已停止
2025-07-10 08:55:46,325 - INFO - 🔌 f4操作处理服务已停止
2025-07-10 08:57:22,996 - INFO - 🔊 f1监听器服务初始化完成
2025-07-10 08:57:22,996 - INFO - 📡 Server6客户端初始化 (新版): http://192.168.3.93:8019
2025-07-10 08:57:22,996 - INFO - 📤 f2推送回写引擎初始化完成 (修复版本: 直接轮询PostgreSQL)
2025-07-10 08:57:22,996 - INFO - 📡 Server6客户端初始化 (新版): http://192.168.3.93:8019
2025-07-10 08:57:22,997 - INFO - 📥 f3数据拉取器初始化完成 (Staging & UPSERT 模式)
2025-07-10 08:57:22,997 - INFO - 🎮 f4操作处理器初始化完成
2025-07-10 08:57:23,007 - INFO - Redis连接成功
2025-07-10 08:57:23,007 - INFO - Redis连接成功
2025-07-10 08:57:23,007 - INFO - Redis连接成功
2025-07-10 08:57:23,013 - INFO - ✅ MongoDB连接成功
2025-07-10 08:57:23,014 - INFO - ✅ MongoDB连接成功
2025-07-10 08:57:23,016 - INFO - ✅ MongoDB连接成功
2025-07-10 08:57:23,267 - INFO - ✅ PostgreSQL连接池创建成功
2025-07-10 08:57:23,268 - INFO - Redis连接成功
2025-07-10 08:57:23,269 - INFO - ✅ f3数据拉取服务启动成功，预定执行时间: ['02:00', '03:00']
2025-07-10 08:57:23,269 - INFO - 🚀 数据拉取调度器启动，预定运行时间: ['02:00', '03:00']
2025-07-10 08:57:23,269 - INFO - 下一次f3/f5同步任务将在 2025-07-11 02:00:00 执行，等待 61357 秒...
2025-07-10 08:57:23,308 - INFO - ✅ PostgreSQL连接池创建成功
2025-07-10 08:57:23,308 - INFO - ✅ f1监听器服务启动成功
2025-07-10 08:57:23,317 - INFO - ✅ 分区创建成功: entries_2025_07
2025-07-10 08:57:23,321 - INFO - ✅ 分区创建成功: entries_2025_08
2025-07-10 08:57:23,344 - INFO - ✅ PostgreSQL连接池创建成功
2025-07-10 08:57:23,344 - INFO - ✅ f2推送回写服务启动成功，工作线程数: 4
2025-07-10 08:57:23,344 - INFO - 🚀 推送工作线程启动: worker_1
2025-07-10 08:57:23,344 - INFO - 🚀 推送工作线程启动: worker_2
2025-07-10 08:57:23,344 - INFO - 🚀 推送工作线程启动: worker_3
2025-07-10 08:57:23,345 - INFO - 🚀 推送工作线程启动: worker_4
2025-07-10 08:57:23,385 - INFO - ✅ PostgreSQL连接池创建成功
2025-07-10 08:57:23,385 - INFO - ✅ f4操作处理服务启动成功
2025-07-10 08:57:23,386 - INFO - 🔌 推送工作线程停止: worker_4
2025-07-10 08:57:23,386 - INFO - 🔌 推送工作线程停止: worker_3
2025-07-10 08:57:23,388 - INFO - 🔌 推送工作线程停止: worker_2
2025-07-10 08:57:23,388 - INFO - 🔌 推送工作线程停止: worker_1
2025-07-10 08:59:02,000 - INFO - 🔌 MongoDB连接已关闭
2025-07-10 08:59:02,001 - INFO - 🔌 MongoDB连接已关闭
2025-07-10 08:59:02,003 - INFO - 🔌 MongoDB连接已关闭
2025-07-10 08:59:02,003 - INFO - 🔌 f1监听器服务已停止
2025-07-10 08:59:02,003 - INFO - 🔌 f2推送回写服务已停止
2025-07-10 08:59:02,003 - INFO - 🔌 f4操作处理服务已停止
2025-07-10 08:59:02,040 - INFO - 🔌 PostgreSQL连接已关闭
2025-07-10 08:59:02,040 - INFO - 🔌 PostgreSQL连接已关闭
2025-07-10 08:59:02,041 - INFO - 🔌 MongoDB连接已关闭
2025-07-10 08:59:02,041 - INFO - 🔌 PostgreSQL连接已关闭
2025-07-10 08:59:02,041 - INFO - 🔌 MongoDB连接已关闭
2025-07-10 08:59:02,041 - INFO - 🔌 PostgreSQL连接已关闭
2025-07-10 08:59:02,041 - INFO - 🔌 MongoDB连接已关闭
2025-07-10 08:59:02,041 - INFO - 🔌 Redis连接已关闭
2025-07-10 08:59:02,041 - INFO - 🔌 Redis连接已关闭
2025-07-10 08:59:02,041 - INFO - 🔌 Redis连接已关闭
2025-07-10 08:59:02,041 - INFO - 🔌 f1监听器服务已停止
2025-07-10 08:59:02,041 - INFO - 🔌 f2推送回写服务已停止
2025-07-10 08:59:02,041 - INFO - 🔌 f4操作处理服务已停止
2025-07-10 09:27:30,746 - INFO - 🔊 f1监听器服务初始化完成
2025-07-10 09:27:30,746 - INFO - 📡 Server6客户端初始化 (新版): http://192.168.3.93:8019
2025-07-10 09:27:30,746 - INFO - 📤 f2推送回写引擎初始化完成 (修复版本: 直接轮询PostgreSQL)
2025-07-10 09:27:30,746 - INFO - 📡 Server6客户端初始化 (新版): http://192.168.3.93:8019
2025-07-10 09:27:30,747 - INFO - 📥 f3数据拉取器初始化完成 (Staging & UPSERT 模式)
2025-07-10 09:27:30,747 - INFO - 🎮 f4操作处理器初始化完成
2025-07-10 09:27:30,757 - INFO - Redis连接成功
2025-07-10 09:27:30,757 - INFO - Redis连接成功
2025-07-10 09:27:30,757 - INFO - Redis连接成功
2025-07-10 09:27:30,763 - INFO - ✅ MongoDB连接成功
2025-07-10 09:27:30,763 - INFO - ✅ MongoDB连接成功
2025-07-10 09:27:30,764 - INFO - ✅ MongoDB连接成功
2025-07-10 09:27:31,020 - INFO - ✅ PostgreSQL连接池创建成功
2025-07-10 09:27:31,021 - INFO - Redis连接成功
2025-07-10 09:27:31,021 - INFO - ✅ f3数据拉取服务启动成功，预定执行时间: ['02:00', '03:00']
2025-07-10 09:27:31,021 - INFO - 🚀 数据拉取调度器启动，预定运行时间: ['02:00', '03:00']
2025-07-10 09:27:31,021 - INFO - 下一次f3/f5同步任务将在 2025-07-11 02:00:00 执行，等待 59549 秒...
2025-07-10 09:27:31,056 - INFO - ✅ PostgreSQL连接池创建成功
2025-07-10 09:27:31,056 - INFO - ✅ f1监听器服务启动成功
2025-07-10 09:27:31,065 - INFO - ✅ 分区创建成功: entries_2025_07
2025-07-10 09:27:31,069 - INFO - ✅ 分区创建成功: entries_2025_08
2025-07-10 09:27:31,125 - INFO - ✅ PostgreSQL连接池创建成功
2025-07-10 09:27:31,125 - INFO - ✅ f2推送回写服务启动成功，工作线程数: 4
2025-07-10 09:27:31,126 - INFO - 🚀 推送工作线程启动: worker_1
2025-07-10 09:27:31,126 - INFO - 🚀 推送工作线程启动: worker_2
2025-07-10 09:27:31,126 - INFO - 🚀 推送工作线程启动: worker_3
2025-07-10 09:27:31,126 - INFO - 🚀 推送工作线程启动: worker_4
2025-07-10 09:27:31,130 - INFO - ✅ PostgreSQL连接池创建成功
2025-07-10 09:27:31,130 - INFO - ✅ f4操作处理服务启动成功
2025-07-10 09:27:31,130 - INFO - 🔌 推送工作线程停止: worker_3
2025-07-10 09:27:31,130 - INFO - 🔌 推送工作线程停止: worker_4
2025-07-10 09:27:31,278 - INFO - 🔌 推送工作线程停止: worker_2
2025-07-10 09:27:31,289 - INFO - 🔌 推送工作线程停止: worker_1
2025-07-10 09:29:16,559 - INFO - 🔌 MongoDB连接已关闭
2025-07-10 09:29:16,560 - INFO - 🔌 MongoDB连接已关闭
2025-07-10 09:29:16,561 - INFO - 🔌 MongoDB连接已关闭
2025-07-10 09:29:16,561 - INFO - 🔌 f1监听器服务已停止
2025-07-10 09:29:16,561 - INFO - 🔌 f2推送回写服务已停止
2025-07-10 09:29:16,562 - INFO - 🔌 f4操作处理服务已停止
2025-07-10 09:29:16,798 - INFO - 🔌 PostgreSQL连接已关闭
2025-07-10 09:29:16,798 - INFO - 🔌 PostgreSQL连接已关闭
2025-07-10 09:29:16,799 - INFO - 🔌 MongoDB连接已关闭
2025-07-10 09:29:16,799 - INFO - 🔌 PostgreSQL连接已关闭
2025-07-10 09:29:16,799 - INFO - 🔌 MongoDB连接已关闭
2025-07-10 09:29:16,799 - INFO - 🔌 PostgreSQL连接已关闭
2025-07-10 09:29:16,799 - INFO - 🔌 MongoDB连接已关闭
2025-07-10 09:29:16,799 - INFO - 🔌 Redis连接已关闭
2025-07-10 09:29:16,799 - INFO - 🔌 Redis连接已关闭
2025-07-10 09:29:16,799 - INFO - 🔌 Redis连接已关闭
2025-07-10 09:29:16,800 - INFO - 🔌 f1监听器服务已停止
2025-07-10 09:29:16,800 - INFO - 🔌 f2推送回写服务已停止
2025-07-10 09:29:16,800 - INFO - 🔌 f4操作处理服务已停止
2025-07-10 09:30:04,415 - INFO - 🔊 f1监听器服务初始化完成
2025-07-10 09:30:04,415 - INFO - 📡 Server6客户端初始化 (新版): http://192.168.3.93:8019
2025-07-10 09:30:04,415 - INFO - 📤 f2推送回写引擎初始化完成 (修复版本: 直接轮询PostgreSQL)
2025-07-10 09:30:04,416 - INFO - 📡 Server6客户端初始化 (新版): http://192.168.3.93:8019
2025-07-10 09:30:04,417 - INFO - 📥 f3数据拉取器初始化完成 (Staging & UPSERT 模式)
2025-07-10 09:30:04,417 - INFO - 🎮 f4操作处理器初始化完成
2025-07-10 09:30:04,427 - INFO - Redis连接成功
2025-07-10 09:30:04,428 - INFO - Redis连接成功
2025-07-10 09:30:04,428 - INFO - Redis连接成功
2025-07-10 09:30:04,433 - INFO - ✅ MongoDB连接成功
2025-07-10 09:30:04,435 - INFO - ✅ MongoDB连接成功
2025-07-10 09:30:04,436 - INFO - ✅ MongoDB连接成功
2025-07-10 09:30:04,738 - INFO - ✅ PostgreSQL连接池创建成功
2025-07-10 09:30:04,738 - INFO - Redis连接成功
2025-07-10 09:30:04,739 - INFO - ✅ f3数据拉取服务启动成功，预定执行时间: ['02:00', '03:00']
2025-07-10 09:30:04,739 - INFO - 🚀 数据拉取调度器启动，预定运行时间: ['02:00', '03:00']
2025-07-10 09:30:04,739 - INFO - 下一次f3/f5同步任务将在 2025-07-11 02:00:00 执行，等待 59395 秒...
2025-07-10 09:30:04,782 - INFO - ✅ PostgreSQL连接池创建成功
2025-07-10 09:30:04,782 - INFO - ✅ f1监听器服务启动成功
2025-07-10 09:30:04,792 - INFO - ✅ 分区创建成功: entries_2025_07
2025-07-10 09:30:04,796 - INFO - ✅ 分区创建成功: entries_2025_08
2025-07-10 09:30:04,822 - INFO - ✅ PostgreSQL连接池创建成功
2025-07-10 09:30:04,822 - INFO - ✅ f2推送回写服务启动成功，工作线程数: 4
2025-07-10 09:30:04,822 - INFO - 🚀 推送工作线程启动: worker_1
2025-07-10 09:30:04,823 - INFO - 🚀 推送工作线程启动: worker_2
2025-07-10 09:30:04,823 - INFO - 🚀 推送工作线程启动: worker_3
2025-07-10 09:30:04,823 - INFO - 🚀 推送工作线程启动: worker_4
2025-07-10 09:30:04,864 - INFO - ✅ PostgreSQL连接池创建成功
2025-07-10 09:30:04,864 - INFO - ✅ f4操作处理服务启动成功
2025-07-10 09:30:04,864 - INFO - 🔌 推送工作线程停止: worker_4
2025-07-10 09:30:04,864 - INFO - 🔌 推送工作线程停止: worker_3
2025-07-10 09:30:04,865 - INFO - 🔌 推送工作线程停止: worker_2
2025-07-10 09:30:04,866 - INFO - 🔌 推送工作线程停止: worker_1
2025-07-10 09:49:18,065 - INFO - 🔌 MongoDB连接已关闭
2025-07-10 09:49:18,067 - INFO - 🔌 MongoDB连接已关闭
2025-07-10 09:49:18,070 - INFO - 🔌 MongoDB连接已关闭
2025-07-10 09:49:18,070 - INFO - 🔌 f1监听器服务已停止
2025-07-10 09:49:18,071 - INFO - 🔌 f2推送回写服务已停止
2025-07-10 09:49:18,071 - INFO - 🔌 f4操作处理服务已停止
2025-07-10 09:49:18,521 - INFO - 🔌 PostgreSQL连接已关闭
2025-07-10 09:49:18,522 - INFO - 🔌 PostgreSQL连接已关闭
2025-07-10 09:49:18,522 - INFO - 🔌 MongoDB连接已关闭
2025-07-10 09:49:18,522 - INFO - 🔌 PostgreSQL连接已关闭
2025-07-10 09:49:18,523 - INFO - 🔌 MongoDB连接已关闭
2025-07-10 09:49:18,523 - INFO - 🔌 PostgreSQL连接已关闭
2025-07-10 09:49:18,524 - INFO - 🔌 MongoDB连接已关闭
2025-07-10 09:49:18,524 - INFO - 🔌 Redis连接已关闭
2025-07-10 09:49:18,524 - INFO - 🔌 Redis连接已关闭
2025-07-10 09:49:18,524 - INFO - 🔌 Redis连接已关闭
2025-07-10 09:49:18,524 - INFO - 🔌 f1监听器服务已停止
2025-07-10 09:49:18,524 - INFO - 🔌 f2推送回写服务已停止
2025-07-10 09:49:18,524 - INFO - 🔌 f4操作处理服务已停止
2025-07-10 09:49:25,567 - INFO - 🔊 f1监听器服务初始化完成
2025-07-10 09:49:25,567 - INFO - 📡 Server6客户端初始化 (新版): http://192.168.3.93:8019
2025-07-10 09:49:25,567 - INFO - 📤 f2推送回写引擎初始化完成 (修复版本: 直接轮询PostgreSQL)
2025-07-10 09:49:25,567 - INFO - 📡 Server6客户端初始化 (新版): http://192.168.3.93:8019
2025-07-10 09:49:25,568 - INFO - 📥 f3数据拉取器初始化完成 (Staging & UPSERT 模式)
2025-07-10 09:49:25,568 - INFO - 🎮 f4操作处理器初始化完成
2025-07-10 09:49:25,577 - INFO - Redis连接成功
2025-07-10 09:49:25,577 - INFO - Redis连接成功
2025-07-10 09:49:25,577 - INFO - Redis连接成功
2025-07-10 09:49:25,583 - INFO - ✅ MongoDB连接成功
2025-07-10 09:49:25,583 - INFO - ✅ MongoDB连接成功
2025-07-10 09:49:25,584 - INFO - ✅ MongoDB连接成功
2025-07-10 09:49:25,813 - INFO - ✅ PostgreSQL连接池创建成功
2025-07-10 09:49:25,814 - INFO - Redis连接成功
2025-07-10 09:49:25,814 - INFO - ✅ f3数据拉取服务启动成功，预定执行时间: ['02:00', '03:00']
2025-07-10 09:49:25,815 - INFO - 🚀 数据拉取调度器启动，预定运行时间: ['02:00', '03:00']
2025-07-10 09:49:25,815 - INFO - 下一次f3/f5同步任务将在 2025-07-11 02:00:00 执行，等待 58234 秒...
2025-07-10 09:49:25,899 - INFO - ✅ PostgreSQL连接池创建成功
2025-07-10 09:49:25,899 - INFO - ✅ f1监听器服务启动成功
2025-07-10 09:49:25,904 - INFO - ✅ PostgreSQL连接池创建成功
2025-07-10 09:49:25,904 - INFO - ✅ f2推送回写服务启动成功，工作线程数: 4
2025-07-10 09:49:25,904 - INFO - 🚀 推送工作线程启动: worker_1
2025-07-10 09:49:25,904 - INFO - 🚀 推送工作线程启动: worker_2
2025-07-10 09:49:25,904 - INFO - 🚀 推送工作线程启动: worker_3
2025-07-10 09:49:25,905 - INFO - 🚀 推送工作线程启动: worker_4
2025-07-10 09:49:25,924 - INFO - ✅ 分区创建成功: entries_2025_07
2025-07-10 09:49:25,928 - INFO - ✅ 分区创建成功: entries_2025_08
2025-07-10 09:49:25,929 - INFO - ✅ PostgreSQL连接池创建成功
2025-07-10 09:49:25,929 - INFO - ✅ f4操作处理服务启动成功
2025-07-10 09:49:25,929 - INFO - 🔌 推送工作线程停止: worker_4
2025-07-10 09:49:25,929 - INFO - 🔌 推送工作线程停止: worker_3
2025-07-10 09:49:25,931 - INFO - 🔌 推送工作线程停止: worker_2
2025-07-10 09:49:25,931 - INFO - 🔌 推送工作线程停止: worker_1
2025-07-10 11:43:10,951 - INFO - 🔌 MongoDB连接已关闭
2025-07-10 11:43:10,953 - INFO - 🔌 MongoDB连接已关闭
2025-07-10 11:43:10,956 - INFO - 🔌 MongoDB连接已关闭
2025-07-10 11:43:10,956 - INFO - 🔌 f1监听器服务已停止
2025-07-10 11:43:10,956 - INFO - 🔌 f2推送回写服务已停止
2025-07-10 11:43:10,957 - INFO - 🔌 f4操作处理服务已停止
2025-07-10 11:43:11,139 - INFO - 🔌 PostgreSQL连接已关闭
2025-07-10 11:43:11,139 - INFO - 🔌 PostgreSQL连接已关闭
2025-07-10 11:43:11,139 - INFO - 🔌 MongoDB连接已关闭
2025-07-10 11:43:11,140 - INFO - 🔌 PostgreSQL连接已关闭
2025-07-10 11:43:11,140 - INFO - 🔌 MongoDB连接已关闭
2025-07-10 11:43:11,140 - INFO - 🔌 PostgreSQL连接已关闭
2025-07-10 11:43:11,140 - INFO - 🔌 MongoDB连接已关闭
2025-07-10 11:43:11,141 - INFO - 🔌 Redis连接已关闭
2025-07-10 11:43:11,141 - INFO - 🔌 Redis连接已关闭
2025-07-10 11:43:11,141 - INFO - 🔌 Redis连接已关闭
2025-07-10 11:43:11,141 - INFO - 🔌 f1监听器服务已停止
2025-07-10 11:43:11,141 - INFO - 🔌 f2推送回写服务已停止
2025-07-10 11:43:11,141 - INFO - 🔌 f4操作处理服务已停止
2025-07-10 11:43:18,633 - INFO - 🔊 f1监听器服务初始化完成
2025-07-10 11:43:18,633 - INFO - 📡 Server6客户端初始化 (新版): http://192.168.3.93:8019
2025-07-10 11:43:18,633 - INFO - 📤 f2推送回写引擎初始化完成 (修复版本: 直接轮询PostgreSQL)
2025-07-10 11:43:18,633 - INFO - 📡 Server6客户端初始化 (新版): http://192.168.3.93:8019
2025-07-10 11:43:18,634 - INFO - 📥 f3数据拉取器初始化完成 (Staging & UPSERT 模式)
2025-07-10 11:43:18,634 - INFO - 🎮 f4操作处理器初始化完成
2025-07-10 11:43:18,645 - INFO - Redis连接成功
2025-07-10 11:43:18,646 - INFO - Redis连接成功
2025-07-10 11:43:18,646 - INFO - Redis连接成功
2025-07-10 11:43:18,652 - INFO - ✅ MongoDB连接成功
2025-07-10 11:43:18,652 - INFO - ✅ MongoDB连接成功
2025-07-10 11:43:18,653 - INFO - ✅ MongoDB连接成功
2025-07-10 11:43:18,966 - INFO - ✅ PostgreSQL连接池创建成功
2025-07-10 11:43:18,967 - INFO - Redis连接成功
2025-07-10 11:43:18,967 - INFO - ✅ f3数据拉取服务启动成功，预定执行时间: ['02:00', '03:00']
2025-07-10 11:43:18,967 - INFO - 🚀 数据拉取调度器启动，预定运行时间: ['02:00', '03:00']
2025-07-10 11:43:18,968 - INFO - 下一次f3/f5同步任务将在 2025-07-11 02:00:00 执行，等待 51401 秒...
2025-07-10 11:43:19,005 - INFO - ✅ PostgreSQL连接池创建成功
2025-07-10 11:43:19,005 - INFO - ✅ f1监听器服务启动成功
2025-07-10 11:43:19,014 - INFO - ✅ 分区创建成功: entries_2025_07
2025-07-10 11:43:19,018 - INFO - ✅ 分区创建成功: entries_2025_08
2025-07-10 11:43:19,049 - INFO - ✅ PostgreSQL连接池创建成功
2025-07-10 11:43:19,049 - INFO - ✅ f2推送回写服务启动成功，工作线程数: 4
2025-07-10 11:43:19,049 - INFO - 🚀 推送工作线程启动: worker_1
2025-07-10 11:43:19,049 - INFO - 🚀 推送工作线程启动: worker_2
2025-07-10 11:43:19,049 - INFO - 🚀 推送工作线程启动: worker_3
2025-07-10 11:43:19,050 - INFO - 🚀 推送工作线程启动: worker_4
2025-07-10 11:43:19,089 - INFO - ✅ PostgreSQL连接池创建成功
2025-07-10 11:43:19,089 - INFO - ✅ f4操作处理服务启动成功
2025-07-10 11:43:19,089 - INFO - 🔌 推送工作线程停止: worker_4
2025-07-10 11:43:19,090 - INFO - 🔌 推送工作线程停止: worker_3
2025-07-10 11:43:19,092 - INFO - 🔌 推送工作线程停止: worker_1
2025-07-10 11:43:19,092 - INFO - 🔌 推送工作线程停止: worker_2
2025-07-10 11:45:33,117 - INFO - 🔌 MongoDB连接已关闭
2025-07-10 11:45:33,119 - INFO - 🔌 MongoDB连接已关闭
2025-07-10 11:45:33,120 - INFO - 🔌 MongoDB连接已关闭
2025-07-10 11:45:33,120 - INFO - 🔌 f1监听器服务已停止
2025-07-10 11:45:33,120 - INFO - 🔌 f2推送回写服务已停止
2025-07-10 11:45:33,120 - INFO - 🔌 f4操作处理服务已停止
2025-07-10 11:45:33,194 - INFO - 🔌 PostgreSQL连接已关闭
2025-07-10 11:45:33,194 - INFO - 🔌 PostgreSQL连接已关闭
2025-07-10 11:45:33,194 - INFO - 🔌 MongoDB连接已关闭
2025-07-10 11:45:33,194 - INFO - 🔌 PostgreSQL连接已关闭
2025-07-10 11:45:33,195 - INFO - 🔌 MongoDB连接已关闭
2025-07-10 11:45:33,195 - INFO - 🔌 PostgreSQL连接已关闭
2025-07-10 11:45:33,195 - INFO - 🔌 MongoDB连接已关闭
2025-07-10 11:45:33,195 - INFO - 🔌 Redis连接已关闭
2025-07-10 11:45:33,195 - INFO - 🔌 Redis连接已关闭
2025-07-10 11:45:33,195 - INFO - 🔌 Redis连接已关闭
2025-07-10 11:45:33,195 - INFO - 🔌 f1监听器服务已停止
2025-07-10 11:45:33,196 - INFO - 🔌 f2推送回写服务已停止
2025-07-10 11:45:33,196 - INFO - 🔌 f4操作处理服务已停止
2025-07-10 11:51:55,487 - INFO - 🔊 f1监听器服务初始化完成
2025-07-10 11:51:55,487 - INFO - 📡 Server6客户端初始化 (新版): http://192.168.3.93:8019
2025-07-10 11:51:55,488 - INFO - 📤 f2推送回写引擎初始化完成 (修复版本: 直接轮询PostgreSQL)
2025-07-10 11:51:55,488 - INFO - 📡 Server6客户端初始化 (新版): http://192.168.3.93:8019
2025-07-10 11:51:55,488 - INFO - 📥 f3数据拉取器初始化完成 (Staging & UPSERT 模式)
2025-07-10 11:51:55,488 - INFO - 🎮 f4操作处理器初始化完成
2025-07-10 11:51:55,498 - INFO - Redis连接成功
2025-07-10 11:51:55,498 - INFO - Redis连接成功
2025-07-10 11:51:55,498 - INFO - Redis连接成功
2025-07-10 11:51:55,504 - INFO - ✅ MongoDB连接成功
2025-07-10 11:51:55,504 - INFO - ✅ MongoDB连接成功
2025-07-10 11:51:55,505 - INFO - ✅ MongoDB连接成功
2025-07-10 11:51:55,767 - INFO - ✅ PostgreSQL连接池创建成功
2025-07-10 11:51:55,768 - INFO - Redis连接成功
2025-07-10 11:51:55,768 - INFO - ✅ f3数据拉取服务启动成功，预定执行时间: ['02:00', '03:00']
2025-07-10 11:51:55,768 - INFO - 🚀 数据拉取调度器启动，预定运行时间: ['02:00', '03:00']
2025-07-10 11:51:55,768 - INFO - 下一次f3/f5同步任务将在 2025-07-11 02:00:00 执行，等待 50884 秒...
2025-07-10 11:51:55,818 - INFO - ✅ PostgreSQL连接池创建成功
2025-07-10 11:51:55,818 - INFO - ✅ f1监听器服务启动成功
2025-07-10 11:51:55,828 - INFO - ✅ 分区创建成功: entries_2025_07
2025-07-10 11:51:55,831 - INFO - ✅ 分区创建成功: entries_2025_08
2025-07-10 11:51:55,860 - INFO - ✅ PostgreSQL连接池创建成功
2025-07-10 11:51:55,860 - INFO - ✅ f2推送回写服务启动成功，工作线程数: 4
2025-07-10 11:51:55,860 - INFO - 🚀 推送工作线程启动: worker_1
2025-07-10 11:51:55,861 - INFO - 🚀 推送工作线程启动: worker_2
2025-07-10 11:51:55,861 - INFO - 🚀 推送工作线程启动: worker_3
2025-07-10 11:51:55,861 - INFO - 🚀 推送工作线程启动: worker_4
2025-07-10 11:51:55,899 - INFO - ✅ PostgreSQL连接池创建成功
2025-07-10 11:51:55,899 - INFO - ✅ f4操作处理服务启动成功
2025-07-10 11:51:55,899 - INFO - 🔌 推送工作线程停止: worker_4
2025-07-10 11:51:55,900 - INFO - 🔌 推送工作线程停止: worker_3
2025-07-10 11:51:55,901 - INFO - 🔌 推送工作线程停止: worker_1
2025-07-10 11:51:55,901 - INFO - 🔌 推送工作线程停止: worker_2
2025-07-10 11:54:09,985 - INFO - 🔊 f1监听器服务初始化完成
2025-07-10 11:54:09,985 - INFO - 📡 Server6客户端初始化 (新版): http://192.168.3.93:8019
2025-07-10 11:54:09,985 - INFO - 📤 f2推送回写引擎初始化完成 (修复版本: 直接轮询PostgreSQL)
2025-07-10 11:54:09,985 - INFO - 📡 Server6客户端初始化 (新版): http://192.168.3.93:8019
2025-07-10 11:54:09,986 - INFO - 📥 f3数据拉取器初始化完成 (Staging & UPSERT 模式)
2025-07-10 11:54:09,986 - INFO - 🎮 f4操作处理器初始化完成
2025-07-10 11:54:09,996 - INFO - Redis连接成功
2025-07-10 11:54:09,996 - INFO - Redis连接成功
2025-07-10 11:54:09,996 - INFO - Redis连接成功
2025-07-10 11:54:10,002 - INFO - ✅ MongoDB连接成功
2025-07-10 11:54:10,002 - INFO - ✅ MongoDB连接成功
2025-07-10 11:54:10,003 - INFO - ✅ MongoDB连接成功
2025-07-10 11:54:10,238 - INFO - ✅ PostgreSQL连接池创建成功
2025-07-10 11:54:10,239 - INFO - Redis连接成功
2025-07-10 11:54:10,239 - INFO - ✅ f3数据拉取服务启动成功，预定执行时间: ['02:00', '03:00']
2025-07-10 11:54:10,239 - INFO - 🚀 数据拉取调度器启动，预定运行时间: ['02:00', '03:00']
2025-07-10 11:54:10,240 - INFO - 下一次f3/f5同步任务将在 2025-07-11 02:00:00 执行，等待 50750 秒...
2025-07-10 11:54:10,290 - INFO - ✅ PostgreSQL连接池创建成功
2025-07-10 11:54:10,290 - INFO - ✅ f1监听器服务启动成功
2025-07-10 11:54:10,311 - INFO - ✅ PostgreSQL连接池创建成功
2025-07-10 11:54:10,312 - INFO - ✅ f2推送回写服务启动成功，工作线程数: 4
2025-07-10 11:54:10,312 - INFO - 🚀 推送工作线程启动: worker_1
2025-07-10 11:54:10,312 - INFO - 🚀 推送工作线程启动: worker_2
2025-07-10 11:54:10,312 - INFO - 🚀 推送工作线程启动: worker_3
2025-07-10 11:54:10,313 - INFO - 🚀 推送工作线程启动: worker_4
2025-07-10 11:54:10,313 - INFO - ✅ 分区创建成功: entries_2025_07
2025-07-10 11:54:10,317 - INFO - ✅ PostgreSQL连接池创建成功
2025-07-10 11:54:10,317 - INFO - ✅ f4操作处理服务启动成功
2025-07-10 11:54:10,317 - INFO - 🔌 推送工作线程停止: worker_3
2025-07-10 11:54:10,317 - INFO - 🔌 推送工作线程停止: worker_4
2025-07-10 11:54:10,505 - INFO - 🔌 推送工作线程停止: worker_1
2025-07-10 11:54:10,535 - INFO - 🔌 推送工作线程停止: worker_2
2025-07-10 13:01:06,945 - INFO - 🔌 MongoDB连接已关闭
2025-07-10 13:01:06,946 - INFO - 🔌 MongoDB连接已关闭
2025-07-10 13:01:06,948 - INFO - 🔌 MongoDB连接已关闭
2025-07-10 13:01:06,948 - INFO - 🔌 f1监听器服务已停止
2025-07-10 13:01:06,948 - INFO - 🔌 f2推送回写服务已停止
2025-07-10 13:01:06,948 - INFO - 🔌 f4操作处理服务已停止
2025-07-10 13:01:07,375 - INFO - 🔌 PostgreSQL连接已关闭
2025-07-10 13:01:07,376 - INFO - 🔌 PostgreSQL连接已关闭
2025-07-10 13:01:07,376 - INFO - 🔌 MongoDB连接已关闭
2025-07-10 13:01:07,376 - INFO - 🔌 PostgreSQL连接已关闭
2025-07-10 13:01:07,376 - INFO - 🔌 MongoDB连接已关闭
2025-07-10 13:01:07,376 - INFO - 🔌 PostgreSQL连接已关闭
2025-07-10 13:01:07,376 - INFO - 🔌 MongoDB连接已关闭
2025-07-10 13:01:07,376 - INFO - 🔌 Redis连接已关闭
2025-07-10 13:01:07,376 - INFO - 🔌 Redis连接已关闭
2025-07-10 13:01:07,376 - INFO - 🔌 Redis连接已关闭
2025-07-10 13:01:07,376 - INFO - 🔌 f1监听器服务已停止
2025-07-10 13:01:07,377 - INFO - 🔌 f2推送回写服务已停止
2025-07-10 13:01:07,377 - INFO - 🔌 f4操作处理服务已停止
2025-07-10 16:47:23,067 - INFO - 🔊 f1监听器服务初始化完成
2025-07-10 16:47:23,067 - INFO - 📡 Server6客户端初始化 (新版): http://192.168.3.93:8019
2025-07-10 16:47:23,067 - INFO - 📤 f2推送回写引擎初始化完成 (修复版本: 直接轮询PostgreSQL)
2025-07-10 16:47:23,067 - INFO - 📡 Server6客户端初始化 (新版): http://192.168.3.93:8019
2025-07-10 16:47:23,068 - INFO - 📥 f3数据拉取器初始化完成 (Staging & UPSERT 模式)
2025-07-10 16:47:23,068 - INFO - 🎮 f4操作处理器初始化完成
2025-07-10 16:47:23,078 - INFO - Redis连接成功
2025-07-10 16:47:23,078 - INFO - Redis连接成功
2025-07-10 16:47:23,079 - INFO - Redis连接成功
2025-07-10 16:47:23,084 - INFO - ✅ MongoDB连接成功
2025-07-10 16:47:23,084 - INFO - ✅ MongoDB连接成功
2025-07-10 16:47:23,086 - INFO - ✅ MongoDB连接成功
2025-07-10 16:47:23,367 - INFO - ✅ PostgreSQL连接池创建成功
2025-07-10 16:47:23,367 - INFO - Redis连接成功
2025-07-10 16:47:23,368 - INFO - ✅ f3数据拉取服务启动成功，预定执行时间: ['02:00', '03:00']
2025-07-10 16:47:23,368 - INFO - 🚀 数据拉取调度器启动，预定运行时间: ['02:00', '03:00']
2025-07-10 16:47:23,368 - INFO - 下一次f3/f5同步任务将在 2025-07-11 02:00:00 执行，等待 33157 秒...
2025-07-10 16:47:23,432 - INFO - ✅ PostgreSQL连接池创建成功
2025-07-10 16:47:23,432 - INFO - ✅ f1监听器服务启动成功
2025-07-10 16:47:23,436 - INFO - ✅ PostgreSQL连接池创建成功
2025-07-10 16:47:23,436 - INFO - ✅ f2推送回写服务启动成功，工作线程数: 4
2025-07-10 16:47:23,436 - INFO - 🚀 推送工作线程启动: worker_1
2025-07-10 16:47:23,437 - INFO - 🚀 推送工作线程启动: worker_2
2025-07-10 16:47:23,437 - INFO - 🚀 推送工作线程启动: worker_3
2025-07-10 16:47:23,437 - INFO - 🚀 推送工作线程启动: worker_4
2025-07-10 16:47:23,463 - INFO - ✅ 分区创建成功: entries_2025_07
2025-07-10 16:47:23,467 - INFO - ✅ PostgreSQL连接池创建成功
2025-07-10 16:47:23,467 - INFO - ✅ f4操作处理服务启动成功
2025-07-10 16:47:23,467 - INFO - 🔌 推送工作线程停止: worker_4
2025-07-10 16:47:23,467 - INFO - 🔌 推送工作线程停止: worker_3
2025-07-10 16:47:23,469 - INFO - 🔌 推送工作线程停止: worker_2
2025-07-10 16:47:23,469 - INFO - 🔌 推送工作线程停止: worker_1
2025-07-10 16:48:27,091 - INFO - 🔌 MongoDB连接已关闭
2025-07-10 16:48:27,092 - INFO - 🔌 MongoDB连接已关闭
2025-07-10 16:48:27,093 - INFO - 🔌 MongoDB连接已关闭
2025-07-10 16:48:27,093 - INFO - 🔌 f1监听器服务已停止
2025-07-10 16:48:27,093 - INFO - 🔌 f2推送回写服务已停止
2025-07-10 16:48:27,093 - INFO - 🔌 f4操作处理服务已停止
2025-07-10 16:48:27,591 - INFO - 🔌 PostgreSQL连接已关闭
2025-07-10 16:48:27,592 - INFO - 🔌 PostgreSQL连接已关闭
2025-07-10 16:48:27,592 - INFO - 🔌 MongoDB连接已关闭
2025-07-10 16:48:27,592 - INFO - 🔌 PostgreSQL连接已关闭
2025-07-10 16:48:27,592 - INFO - 🔌 MongoDB连接已关闭
2025-07-10 16:48:27,592 - INFO - 🔌 PostgreSQL连接已关闭
2025-07-10 16:48:27,592 - INFO - 🔌 MongoDB连接已关闭
2025-07-10 16:48:27,592 - INFO - 🔌 Redis连接已关闭
2025-07-10 16:48:27,592 - INFO - 🔌 Redis连接已关闭
2025-07-10 16:48:27,592 - INFO - 🔌 Redis连接已关闭
2025-07-10 16:48:27,592 - INFO - 🔌 f1监听器服务已停止
2025-07-10 16:48:27,592 - INFO - 🔌 f2推送回写服务已停止
2025-07-10 16:48:27,592 - INFO - 🔌 f4操作处理服务已停止
