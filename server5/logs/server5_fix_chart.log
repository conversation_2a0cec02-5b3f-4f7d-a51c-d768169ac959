🔧 加载配置: config_ubuntu_remote
🐛 MySuite Server5 - 数据同步微服务 (Ubuntu远程) 运行在调试模式
📊 PostgreSQL: ************:5432
🍃 MongoDB: ************:27017
⚡ Redis: localhost:6379
💾 ODBC可用: False
📋 配置信息:
  - 配置文件: config_ubuntu_remote
  - 平台: Linux
  - 主机: test
  - IP: *************
  - 自动检测: config_ubuntu_remote
🔧 加载配置: config_ubuntu_remote
📋 配置信息:
  - 配置文件: config_ubuntu_remote
  - 平台: Linux
  - 主机: test
  - IP: *************
  - 自动检测: config_ubuntu_remote
🚀 MySuite Server5 - 完整启动模式 (微服务 + HTTP API)
🌐 启动HTTP API服务器...
🔍 发现占用端口8009的进程: ['1853257']
✅ 已杀掉进程 1853257
✅ 端口8009已释放
🔧 执行命令: /home/<USER>/miniconda3/envs/my_suite_unified/bin/python -m uvicorn app.main:app --host 0.0.0.0 --port 8009 --log-level info
✅ HTTP服务器启动成功，PID: 1854455
🚀 启动Server5微服务...
2025-07-09 17:45:45,600 - INFO - 🔊 f1监听器服务初始化完成
2025-07-09 17:45:45,600 - INFO - 📡 Server6客户端初始化 (新版): http://************:8009
2025-07-09 17:45:45,600 - INFO - 📤 f2推送回写引擎初始化完成 (修复版本: 直接轮询PostgreSQL)
2025-07-09 17:45:45,600 - INFO - 📡 Server6客户端初始化 (新版): http://************:8009
2025-07-09 17:45:45,601 - INFO - 📥 f3数据拉取器初始化完成 (Staging & UPSERT 模式)
2025-07-09 17:45:45,601 - INFO - 🎮 f4操作处理器初始化完成
2025-07-09 17:45:45,609 - INFO - Redis连接成功
2025-07-09 17:45:45,609 - INFO - Redis连接成功
2025-07-09 17:45:45,609 - INFO - Redis连接成功
2025-07-09 17:45:45,615 - INFO - ✅ MongoDB连接成功
2025-07-09 17:45:45,616 - INFO - ✅ MongoDB连接成功
2025-07-09 17:45:45,617 - INFO - ✅ MongoDB连接成功
2025-07-09 17:45:45,858 - INFO - ✅ PostgreSQL连接池创建成功
2025-07-09 17:45:45,859 - INFO - Redis连接成功
2025-07-09 17:45:45,859 - INFO - ✅ f3数据拉取服务启动成功，预定执行时间: ['02:00', '03:00']
2025-07-09 17:45:45,859 - INFO - 🚀 数据拉取调度器启动，预定运行时间: ['02:00', '03:00']
2025-07-09 17:45:45,859 - INFO - 下一次f3/f5同步任务将在 2025-07-10 02:00:00 执行，等待 29654 秒...
2025-07-09 17:45:45,905 - INFO - ✅ PostgreSQL连接池创建成功
2025-07-09 17:45:45,905 - INFO - ✅ f1监听器服务启动成功
2025-07-09 17:45:45,914 - INFO - ✅ 分区创建成功: entries_2025_07
2025-07-09 17:45:45,934 - INFO - ✅ 分区创建成功: entries_2025_08
2025-07-09 17:45:45,952 - INFO - ✅ PostgreSQL连接池创建成功
2025-07-09 17:45:45,952 - INFO - ✅ f2推送回写服务启动成功，工作线程数: 4
2025-07-09 17:45:45,952 - INFO - 🚀 推送工作线程启动: worker_1
2025-07-09 17:45:45,953 - INFO - 🚀 推送工作线程启动: worker_2
2025-07-09 17:45:45,953 - INFO - 🚀 推送工作线程启动: worker_3
2025-07-09 17:45:45,953 - INFO - 🚀 推送工作线程启动: worker_4
2025-07-09 17:45:45,957 - INFO - ✅ PostgreSQL连接池创建成功
2025-07-09 17:45:45,957 - INFO - ✅ f4操作处理服务启动成功
✅ f1_listener 启动成功
✅ f2_push_writer 启动成功
✅ f3_data_puller 启动成功
✅ f4_operation_handler 启动成功
🎉 所有微服务已启动
2025-07-09 17:45:45,957 - INFO - 🔌 推送工作线程停止: worker_4
2025-07-09 17:45:45,957 - INFO - 🔌 推送工作线程停止: worker_3
2025-07-09 17:45:46,132 - INFO - 🔌 推送工作线程停止: worker_1
2025-07-09 17:45:46,160 - INFO - 🔌 推送工作线程停止: worker_2
🎉 Server5完整启动成功！
📡 HTTP API地址: http://localhost:8009
🔧 微服务状态: 运行中
👋 按 Ctrl+C 退出

📡 接收到信号 2，正在关闭服务...

🧹 执行清理操作...
🛑 停止HTTP服务器...
🛑 停止微服务...
🛑 正在停止微服务...
2025-07-10 07:30:38,784 - INFO - 🔌 MongoDB连接已关闭
2025-07-10 07:30:38,785 - INFO - 🔌 MongoDB连接已关闭
2025-07-10 07:30:38,787 - INFO - 🔌 MongoDB连接已关闭
2025-07-10 07:30:38,787 - INFO - 🔌 f1监听器服务已停止
2025-07-10 07:30:38,787 - INFO - 🔌 f2推送回写服务已停止
2025-07-10 07:30:38,787 - INFO - 🔌 f4操作处理服务已停止
✅ 微服务已停止
✅ 清理完成

🧹 执行清理操作...
🛑 停止微服务...
🛑 正在停止微服务...
2025-07-10 07:30:39,246 - INFO - 🔌 PostgreSQL连接已关闭
2025-07-10 07:30:39,246 - INFO - 🔌 PostgreSQL连接已关闭
2025-07-10 07:30:39,247 - INFO - 🔌 MongoDB连接已关闭
2025-07-10 07:30:39,247 - INFO - 🔌 PostgreSQL连接已关闭
2025-07-10 07:30:39,248 - INFO - 🔌 MongoDB连接已关闭
2025-07-10 07:30:39,248 - INFO - 🔌 PostgreSQL连接已关闭
2025-07-10 07:30:39,248 - INFO - 🔌 MongoDB连接已关闭
2025-07-10 07:30:39,248 - INFO - 🔌 Redis连接已关闭
2025-07-10 07:30:39,249 - INFO - 🔌 Redis连接已关闭
2025-07-10 07:30:39,249 - INFO - 🔌 Redis连接已关闭
2025-07-10 07:30:39,249 - INFO - 🔌 f1监听器服务已停止
2025-07-10 07:30:39,249 - INFO - 🔌 f2推送回写服务已停止
2025-07-10 07:30:39,249 - INFO - 🔌 f4操作处理服务已停止
✅ 微服务已停止
✅ 清理完成
