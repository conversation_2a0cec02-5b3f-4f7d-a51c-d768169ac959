2025-07-10 14:08:43,595 - INFO - MySuite Server5 - 数据同步微服务 (Ubuntu远程) v1.0.0 启动中 (HTTP-only 模式)...
2025-07-10 14:08:43,595 - INFO - 🌐 HTTP-only 模式: 仅启动 HTTP API，不启动微服务
2025-07-10 14:08:49,910 - INFO - 🌐 HTTP-only 模式: 无微服务需要停止
2025-07-10 14:22:18,154 - INFO - MySuite Server5 - 数据同步微服务 (Ubuntu远程) v1.0.0 启动中 (HTTP-only 模式)...
2025-07-10 14:22:18,154 - INFO - 🌐 HTTP-only 模式: 仅启动 HTTP API，不启动微服务
2025-07-10 14:22:18,402 - INFO - ✅ PostgreSQL连接池创建成功
2025-07-10 14:22:18,402 - INFO - ✅ HTTP-only 模式下数据库连接成功
2025-07-10 14:22:29,424 - INFO - 🌐 HTTP-only 模式: 断开数据库连接
2025-07-10 14:22:29,428 - INFO - 🔌 PostgreSQL连接已关闭
2025-07-10 14:23:20,661 - INFO - MySuite Server5 - 数据同步微服务 (Ubuntu远程) v1.0.0 启动中 (HTTP-only 模式)...
2025-07-10 14:23:20,661 - INFO - 🌐 HTTP-only 模式: 仅启动 HTTP API，不启动微服务
2025-07-10 14:23:20,899 - INFO - ✅ PostgreSQL连接池创建成功
2025-07-10 14:23:20,899 - INFO - ✅ HTTP-only 模式下数据库连接成功
2025-07-10 14:24:27,949 - INFO - 🌐 HTTP-only 模式: 断开数据库连接
2025-07-10 14:24:27,952 - INFO - 🔌 PostgreSQL连接已关闭
2025-07-10 14:24:33,045 - INFO - MySuite Server5 - 数据同步微服务 (Ubuntu远程) v1.0.0 启动中 (HTTP-only 模式)...
2025-07-10 14:24:33,045 - INFO - 🌐 HTTP-only 模式: 仅启动 HTTP API，不启动微服务
2025-07-10 14:24:33,311 - INFO - ✅ PostgreSQL连接池创建成功
2025-07-10 14:24:33,311 - INFO - ✅ HTTP-only 模式下数据库连接成功
2025-07-10 14:25:05,727 - INFO - 🔗 HTTP-only 模式: 使用 service_manager 的数据库连接
2025-07-10 14:25:26,836 - INFO - 🌐 HTTP-only 模式: 断开数据库连接
2025-07-10 14:25:26,840 - INFO - 🔌 PostgreSQL连接已关闭
2025-07-10 14:39:58,000 - INFO - MySuite Server5 - 数据同步微服务 (Ubuntu远程) v1.0.0 启动中 (HTTP-only 模式)...
2025-07-10 14:39:58,000 - INFO - 🌐 HTTP-only 模式: 仅启动 HTTP API，不启动微服务
2025-07-10 14:39:58,213 - INFO - ✅ PostgreSQL连接池创建成功
2025-07-10 14:39:58,213 - INFO - ✅ HTTP-only 模式下数据库连接成功
2025-07-10 14:40:33,745 - INFO - 📊 Timeprotab查询完成: 31条记录
2025-07-10 14:40:33,752 - INFO - 🔗 HTTP-only 模式: 使用 service_manager 的数据库连接
2025-07-10 14:40:33,759 - INFO - 📊 Entries查询完成: 1条记录, 耗时: 0.006秒
2025-07-10 14:40:33,896 - INFO - 📊 图表数据生成完成: 1个数据点
2025-07-10 14:40:41,939 - INFO - 📊 Timeprotab查询完成: 31条记录
2025-07-10 14:40:48,762 - INFO - 📊 Timeprotab查询完成: 30条记录
2025-07-10 14:40:55,156 - INFO - 📊 Timeprotab查询完成: 31条记录
2025-07-10 14:41:11,555 - INFO - 📊 Timeprotab查询完成: 31条记录
2025-07-10 14:41:11,573 - INFO - 📊 Entries查询完成: 1条记录, 耗时: 0.002秒
2025-07-10 14:41:11,646 - INFO - 📊 图表数据生成完成: 1个数据点
2025-07-10 14:41:16,922 - INFO - 📊 Timeprotab查询完成: 31条记录
2025-07-10 14:41:28,405 - INFO - 🌐 HTTP-only 模式: 断开数据库连接
2025-07-10 14:41:28,409 - INFO - 🔌 PostgreSQL连接已关闭
2025-07-10 14:52:22,671 - INFO - MySuite Server5 - 数据同步微服务 (Ubuntu远程) v1.0.0 启动中 (HTTP-only 模式)...
2025-07-10 14:52:22,671 - INFO - 🌐 HTTP-only 模式: 仅启动 HTTP API，不启动微服务
2025-07-10 14:52:22,910 - INFO - ✅ PostgreSQL连接池创建成功
2025-07-10 14:52:22,910 - INFO - ✅ HTTP-only 模式下数据库连接成功
2025-07-10 14:52:44,163 - INFO - 📊 Timeprotab查询完成: 31条记录
2025-07-10 14:52:44,171 - INFO - 🔗 HTTP-only 模式: 使用 service_manager 的数据库连接
2025-07-10 14:52:44,177 - INFO - 📊 Entries查询完成: 1条记录, 耗时: 0.005秒
2025-07-10 14:52:47,454 - INFO - 📊 图表数据生成完成: 1个数据点
2025-07-10 14:52:58,489 - INFO - 📊 Timeprotab查询完成: 31条记录
2025-07-10 14:53:00,694 - INFO - 📊 Timeprotab查询完成: 30条记录
2025-07-10 14:53:15,515 - INFO - 🌐 HTTP-only 模式: 断开数据库连接
2025-07-10 14:53:15,519 - INFO - 🔌 PostgreSQL连接已关闭
2025-07-10 14:53:43,316 - INFO - MySuite Server5 - 数据同步微服务 (Ubuntu远程) v1.0.0 启动中 (HTTP-only 模式)...
2025-07-10 14:53:43,316 - INFO - 🌐 HTTP-only 模式: 仅启动 HTTP API，不启动微服务
2025-07-10 14:53:43,539 - INFO - ✅ PostgreSQL连接池创建成功
2025-07-10 14:53:43,539 - INFO - ✅ HTTP-only 模式下数据库连接成功
2025-07-10 14:54:09,998 - INFO - 🌐 HTTP-only 模式: 断开数据库连接
2025-07-10 14:54:10,002 - INFO - 🔌 PostgreSQL连接已关闭
2025-07-10 15:03:01,553 - INFO - MySuite Server5 - 数据同步微服务 (Ubuntu远程) v1.0.0 启动中 (HTTP-only 模式)...
2025-07-10 15:03:01,554 - INFO - 🌐 HTTP-only 模式: 仅启动 HTTP API，不启动微服务
2025-07-10 15:03:01,797 - INFO - ✅ PostgreSQL连接池创建成功
2025-07-10 15:03:01,797 - INFO - ✅ HTTP-only 模式下数据库连接成功
2025-07-10 15:03:54,580 - INFO - 🌐 HTTP-only 模式: 断开数据库连接
2025-07-10 15:03:54,584 - INFO - 🔌 PostgreSQL连接已关闭
2025-07-10 15:04:49,582 - INFO - MySuite Server5 - 数据同步微服务 (Ubuntu远程) v1.0.0 启动中 (HTTP-only 模式)...
2025-07-10 15:04:49,582 - INFO - 🌐 HTTP-only 模式: 仅启动 HTTP API，不启动微服务
2025-07-10 15:04:49,793 - INFO - ✅ PostgreSQL连接池创建成功
2025-07-10 15:04:49,793 - INFO - ✅ HTTP-only 模式下数据库连接成功
2025-07-10 15:05:16,863 - INFO - 🔗 HTTP-only 模式: 使用 service_manager 的数据库连接
2025-07-10 15:05:25,619 - INFO - 📊 获取员工 215829 的部门信息
2025-07-10 15:05:25,627 - ERROR - 查询执行失败: 
            SELECT DISTINCT department
            FROM entries
            WHERE employee_id = %s
... 错误: "%"またはその近辺で構文エラー
2025-07-10 15:05:25,627 - ERROR - ❌ 获取员工部门信息失败: "%"またはその近辺で構文エラー
2025-07-10 15:06:30,205 - INFO - 🌐 HTTP-only 模式: 断开数据库连接
2025-07-10 15:06:30,210 - INFO - 🔌 PostgreSQL连接已关闭
2025-07-10 15:06:35,602 - INFO - MySuite Server5 - 数据同步微服务 (Ubuntu远程) v1.0.0 启动中 (HTTP-only 模式)...
2025-07-10 15:06:35,602 - INFO - 🌐 HTTP-only 模式: 仅启动 HTTP API，不启动微服务
2025-07-10 15:06:35,821 - INFO - ✅ PostgreSQL连接池创建成功
2025-07-10 15:06:35,821 - INFO - ✅ HTTP-only 模式下数据库连接成功
2025-07-10 15:10:33,689 - INFO - 🌐 HTTP-only 模式: 断开数据库连接
2025-07-10 15:10:33,695 - INFO - 🔌 PostgreSQL连接已关闭
2025-07-10 15:13:46,784 - INFO - MySuite Server5 - 数据同步微服务 (Ubuntu远程) v1.0.0 启动中 (HTTP-only 模式)...
2025-07-10 15:13:46,784 - INFO - 🌐 HTTP-only 模式: 仅启动 HTTP API，不启动微服务
2025-07-10 15:13:47,017 - INFO - ✅ PostgreSQL连接池创建成功
2025-07-10 15:13:47,017 - INFO - ✅ HTTP-only 模式下数据库连接成功
2025-07-10 15:13:50,726 - INFO - 🌐 HTTP-only 模式: 断开数据库连接
2025-07-10 15:13:50,730 - INFO - 🔌 PostgreSQL连接已关闭
2025-07-10 15:17:45,875 - INFO - MySuite Server5 - 数据同步微服务 (Ubuntu远程) v1.0.0 启动中 (HTTP-only 模式)...
2025-07-10 15:17:45,875 - INFO - 🌐 HTTP-only 模式: 仅启动 HTTP API，不启动微服务
2025-07-10 15:17:46,102 - INFO - ✅ PostgreSQL连接池创建成功
2025-07-10 15:17:46,102 - INFO - ✅ HTTP-only 模式下数据库连接成功
2025-07-10 15:17:50,271 - INFO - 📊 Timeprotab查询完成: 31条记录
2025-07-10 15:17:50,777 - INFO - 🔗 HTTP-only 模式: 使用 service_manager 的数据库连接
2025-07-10 15:17:50,784 - INFO - 📊 Entries查询完成: 1条记录, 耗时: 0.006秒
2025-07-10 15:17:51,290 - INFO - 📊 获取员工 215829 的部门信息
2025-07-10 15:17:51,365 - ERROR - ❌ 获取员工部门信息失败: 0
2025-07-10 15:17:52,949 - INFO - 📊 图表数据生成完成: 1个数据点
2025-07-10 15:18:07,047 - INFO - 🌐 HTTP-only 模式: 断开数据库连接
2025-07-10 15:18:07,050 - INFO - 🔌 PostgreSQL连接已关闭
2025-07-10 15:55:20,427 - INFO - MySuite Server5 - 数据同步微服务 (Ubuntu远程) v1.0.0 启动中 (HTTP-only 模式)...
2025-07-10 15:55:20,427 - INFO - 🌐 HTTP-only 模式: 仅启动 HTTP API，不启动微服务
2025-07-10 15:55:20,677 - INFO - ✅ PostgreSQL连接池创建成功
2025-07-10 15:55:20,677 - INFO - ✅ HTTP-only 模式下数据库连接成功
2025-07-10 15:55:59,869 - INFO - 🌐 HTTP-only 模式: 断开数据库连接
2025-07-10 15:55:59,873 - INFO - 🔌 PostgreSQL连接已关闭
2025-07-10 15:56:10,650 - INFO - MySuite Server5 - 数据同步微服务 (Ubuntu远程) v1.0.0 启动中 (HTTP-only 模式)...
2025-07-10 15:56:10,650 - INFO - 🌐 HTTP-only 模式: 仅启动 HTTP API，不启动微服务
2025-07-10 15:56:10,996 - INFO - ✅ PostgreSQL连接池创建成功
2025-07-10 15:56:10,996 - INFO - ✅ HTTP-only 模式下数据库连接成功
2025-07-10 15:56:18,361 - INFO - 📊 Timeprotab查询完成: 31条记录
2025-07-10 15:56:18,368 - INFO - 🔗 HTTP-only 模式: 使用 service_manager 的数据库连接
2025-07-10 15:56:18,371 - INFO - 📊 Entries查询完成: 1条记录, 耗时: 0.003秒
2025-07-10 15:57:00,990 - INFO - 🌐 HTTP-only 模式: 断开数据库连接
2025-07-10 15:57:00,994 - INFO - 🔌 PostgreSQL连接已关闭
2025-07-10 16:50:53,334 - INFO - MySuite Server5 - 数据同步微服务 (Ubuntu远程) v1.0.0 启动中 (HTTP-only 模式)...
2025-07-10 16:50:53,334 - INFO - 🌐 HTTP-only 模式: 仅启动 HTTP API，不启动微服务
2025-07-10 16:50:53,603 - INFO - ✅ PostgreSQL连接池创建成功
2025-07-10 16:50:53,603 - INFO - ✅ HTTP-only 模式下数据库连接成功
2025-07-10 16:51:02,927 - ERROR - 查询执行失败: 
            SELECT 
                "日付",
                "所定時間",
                "早出残業"
          ... 错误: 列"従業員ｺｰﾄﾞ"は存在しません
2025-07-10 16:51:02,928 - WARNING - 查询timeprotab表失败，可能分区不存在: 列"従業員ｺｰﾄﾞ"は存在しません
2025-07-10 16:51:02,929 - INFO - 📊 双表图表数据生成完成: 31天, Entries有效数据: 1, Timeprotab有效数据: 0
2025-07-10 16:53:06,500 - INFO - 🌐 HTTP-only 模式: 断开数据库连接
2025-07-10 16:53:06,504 - INFO - 🔌 PostgreSQL连接已关闭
2025-07-10 16:54:10,886 - INFO - MySuite Server5 - 数据同步微服务 (Ubuntu远程) v1.0.0 启动中 (HTTP-only 模式)...
2025-07-10 16:54:10,887 - INFO - 🌐 HTTP-only 模式: 仅启动 HTTP API，不启动微服务
2025-07-10 16:54:11,142 - INFO - ✅ PostgreSQL连接池创建成功
2025-07-10 16:54:11,142 - INFO - ✅ HTTP-only 模式下数据库连接成功
2025-07-10 16:56:53,235 - INFO - 🌐 HTTP-only 模式: 断开数据库连接
2025-07-10 16:56:53,239 - INFO - 🔌 PostgreSQL连接已关闭
2025-07-10 16:56:59,752 - INFO - MySuite Server5 - 数据同步微服务 (Ubuntu远程) v1.0.0 启动中 (HTTP-only 模式)...
2025-07-10 16:56:59,752 - INFO - 🌐 HTTP-only 模式: 仅启动 HTTP API，不启动微服务
2025-07-10 16:56:59,968 - INFO - ✅ PostgreSQL连接池创建成功
2025-07-10 16:56:59,968 - INFO - ✅ HTTP-only 模式下数据库连接成功
2025-07-10 16:57:08,991 - INFO - 📊 双表图表数据生成完成: 31天, Entries有效数据: 1, Timeprotab有效数据: 3
2025-07-10 16:57:31,134 - INFO - 🌐 HTTP-only 模式: 断开数据库连接
2025-07-10 16:57:31,139 - INFO - 🔌 PostgreSQL连接已关闭
2025-07-10 17:14:19,014 - INFO - MySuite Server5 - 数据同步微服务 (Ubuntu远程) v1.0.0 启动中 (HTTP-only 模式)...
2025-07-10 17:14:19,014 - INFO - 🌐 HTTP-only 模式: 仅启动 HTTP API，不启动微服务
2025-07-10 17:14:19,258 - INFO - ✅ PostgreSQL连接池创建成功
2025-07-10 17:14:19,258 - INFO - ✅ HTTP-only 模式下数据库连接成功
2025-07-10 17:14:31,994 - INFO - 📊 Chart数据生成完成: 31天, 工作日: 4, 匹配: 28, 不匹配: 3
2025-07-10 17:17:28,229 - INFO - 🌐 HTTP-only 模式: 断开数据库连接
2025-07-10 17:17:28,234 - INFO - 🔌 PostgreSQL连接已关闭
2025-07-10 18:39:41,163 - INFO - MySuite Server5 - 数据同步微服务 (Ubuntu远程) v1.0.0 启动中 (HTTP-only 模式)...
2025-07-10 18:39:41,163 - INFO - 🌐 HTTP-only 模式: 仅启动 HTTP API，不启动微服务
2025-07-10 18:39:41,408 - INFO - ✅ PostgreSQL连接池创建成功
2025-07-10 18:39:41,408 - INFO - ✅ HTTP-only 模式下数据库连接成功
2025-07-10 18:40:13,994 - INFO - 📊 Timeprotab查询完成: 31条记录
2025-07-10 18:40:14,011 - INFO - 🔗 HTTP-only 模式: 使用 service_manager 的数据库连接
2025-07-10 18:40:14,018 - INFO - 📊 Entries查询完成: 1条记录, 耗时: 0.007秒
2025-07-10 18:40:14,387 - INFO - 📊 Chart数据生成完成: 31天, 工作日: 8, 匹配: 24, 不匹配: 7
2025-07-10 18:40:14,439 - INFO - 📊 Chart数据生成完成: 31天, 工作日: 8, 匹配: 24, 不匹配: 7
2025-07-10 18:40:14,459 - INFO - 📊 Chart数据生成完成: 31天, 工作日: 8, 匹配: 24, 不匹配: 7
2025-07-10 18:40:14,490 - INFO - 📊 Chart数据生成完成: 30天, 工作日: 21, 匹配: 29, 不匹配: 1
2025-07-10 18:40:14,542 - INFO - 📊 Chart数据生成完成: 30天, 工作日: 21, 匹配: 29, 不匹配: 1
2025-07-10 18:41:01,706 - INFO - 📊 Timeprotab查询完成: 31条记录
2025-07-10 18:41:01,745 - INFO - 📊 Entries查询完成: 1条记录, 耗时: 0.002秒
2025-07-10 18:41:02,049 - INFO - 📊 Chart数据生成完成: 31天, 工作日: 8, 匹配: 24, 不匹配: 7
2025-07-10 18:41:02,100 - INFO - 📊 Chart数据生成完成: 31天, 工作日: 8, 匹配: 24, 不匹配: 7
2025-07-10 18:41:02,129 - INFO - 📊 Chart数据生成完成: 31天, 工作日: 8, 匹配: 24, 不匹配: 7
2025-07-10 18:41:02,138 - INFO - 📊 Chart数据生成完成: 30天, 工作日: 21, 匹配: 29, 不匹配: 1
2025-07-10 18:41:02,237 - INFO - 📊 Chart数据生成完成: 30天, 工作日: 21, 匹配: 29, 不匹配: 1
2025-07-10 18:41:04,926 - INFO - 📊 Entries查询完成: 1条记录, 耗时: 0.003秒
2025-07-10 18:48:02,432 - INFO - 📊 Timeprotab查询完成: 31条记录
2025-07-10 18:48:02,450 - INFO - 📊 Entries查询完成: 1条记录, 耗时: 0.005秒
2025-07-10 18:48:02,720 - INFO - 📊 Chart数据生成完成: 31天, 工作日: 8, 匹配: 24, 不匹配: 7
2025-07-10 18:48:02,789 - INFO - 📊 Chart数据生成完成: 31天, 工作日: 8, 匹配: 24, 不匹配: 7
2025-07-10 18:48:02,809 - INFO - 📊 Chart数据生成完成: 31天, 工作日: 8, 匹配: 24, 不匹配: 7
2025-07-10 18:48:02,824 - INFO - 📊 Chart数据生成完成: 30天, 工作日: 21, 匹配: 29, 不匹配: 1
2025-07-10 18:48:02,892 - INFO - 📊 Chart数据生成完成: 30天, 工作日: 21, 匹配: 29, 不匹配: 1
2025-07-10 18:48:05,650 - INFO - 📊 Chart数据生成完成: 31天, 工作日: 8, 匹配: 24, 不匹配: 7
2025-07-10 18:48:05,747 - INFO - 📊 Chart数据生成完成: 30天, 工作日: 21, 匹配: 29, 不匹配: 1
2025-07-10 18:48:09,218 - INFO - 📊 Chart数据生成完成: 30天, 工作日: 21, 匹配: 29, 不匹配: 1
2025-07-10 18:48:09,322 - INFO - 📊 Chart数据生成完成: 31天, 工作日: 20, 匹配: 17, 不匹配: 14
2025-07-10 18:49:59,094 - INFO - 📊 Timeprotab查询完成: 31条记录
2025-07-10 18:49:59,130 - INFO - 📊 Entries查询完成: 1条记录, 耗时: 0.002秒
2025-07-10 18:49:59,414 - INFO - 📊 Chart数据生成完成: 31天, 工作日: 8, 匹配: 24, 不匹配: 7
2025-07-10 18:49:59,445 - INFO - 📊 Chart数据生成完成: 31天, 工作日: 8, 匹配: 24, 不匹配: 7
2025-07-10 18:49:59,489 - INFO - 📊 Chart数据生成完成: 31天, 工作日: 8, 匹配: 24, 不匹配: 7
2025-07-10 18:49:59,515 - INFO - 📊 Chart数据生成完成: 30天, 工作日: 21, 匹配: 29, 不匹配: 1
2025-07-10 18:49:59,591 - INFO - 📊 Chart数据生成完成: 30天, 工作日: 21, 匹配: 29, 不匹配: 1
2025-07-10 18:50:01,059 - INFO - 📊 Entries查询完成: 1条记录, 耗时: 0.002秒
2025-07-10 18:53:43,310 - INFO - 📊 Timeprotab查询完成: 31条记录
2025-07-10 18:53:43,332 - INFO - 📊 Entries查询完成: 1条记录, 耗时: 0.003秒
2025-07-10 18:53:43,635 - INFO - 📊 Chart数据生成完成: 31天, 工作日: 8, 匹配: 24, 不匹配: 7
2025-07-10 18:53:43,683 - INFO - 📊 Chart数据生成完成: 31天, 工作日: 8, 匹配: 24, 不匹配: 7
2025-07-10 18:53:43,717 - INFO - 📊 Chart数据生成完成: 31天, 工作日: 8, 匹配: 24, 不匹配: 7
2025-07-10 18:53:43,730 - INFO - 📊 Chart数据生成完成: 30天, 工作日: 21, 匹配: 29, 不匹配: 1
2025-07-10 18:53:43,821 - INFO - 📊 Chart数据生成完成: 30天, 工作日: 21, 匹配: 29, 不匹配: 1
2025-07-10 18:53:45,764 - INFO - 📊 Chart数据生成完成: 31天, 工作日: 8, 匹配: 24, 不匹配: 7
2025-07-10 18:53:45,859 - INFO - 📊 Chart数据生成完成: 30天, 工作日: 21, 匹配: 29, 不匹配: 1
2025-07-10 18:53:55,798 - INFO - 🌐 HTTP-only 模式: 断开数据库连接
2025-07-10 18:53:55,803 - INFO - 🔌 PostgreSQL连接已关闭
