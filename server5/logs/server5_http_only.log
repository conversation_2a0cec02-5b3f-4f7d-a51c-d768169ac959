nohup: ignoring input
🔧 加载配置: config_ubuntu_remote
🐛 MySuite Server5 - 数据同步微服务 (Ubuntu远程) 运行在调试模式
📊 PostgreSQL: ************:5432
🍃 MongoDB: ************:27017
⚡ Redis: localhost:6379
💾 ODBC可用: False
📋 配置信息:
  - 配置文件: config_ubuntu_remote
  - 平台: Linux
  - 主机: test
  - IP: *************
  - 自动检测: config_ubuntu_remote
🔧 加载配置: config_ubuntu_remote
📋 配置信息:
  - 配置文件: config_ubuntu_remote
  - 平台: Linux
  - 主机: test
  - IP: *************
  - 自动检测: config_ubuntu_remote
🌐 MySuite Server5 - 纯HTTP API服务器
============================================================
🔧 模式: HTTP-only (不启动 f1-f4 微服务)
🌐 功能: 仅提供 HTTP API 接口
🔗 微服务: 需要单独启动 start_server5_notwith_api.py
============================================================
🔍 检查端口 8009 可用性...
✅ 端口8009可用
🌐 启动纯HTTP API服务器...
📡 监听地址: http://0.0.0.0:8009
📋 API文档: http://localhost:8009/docs
🔍 健康检查: http://localhost:8009/health
⚠️  注意：此模式仅启动HTTP API，不包含f1-f4微服务
🔧 如需启动微服务，请另开终端运行: python start_server5_notwith_api.py
👋 按 Ctrl+C 退出
INFO:     Started server process [2401871]
INFO:     Waiting for application startup.
🔥 当前加载的 entries_api.py 路径: /home/<USER>/Documents/testF/2025/ser25/server0613/MySuiteDatebase2402newuigood-yolo5-3/server5/app/routers/entries_api.py
🔥 当前加载的 entries_api.py 内容版本: 20250710 - HTTP-only 模式支持
🔥🔥🔥 这是优化后的 entries_api.py！🔥🔥🔥
2025-07-15 07:50:40,918 - INFO - MySuite Server5 - 数据同步微服务 (Ubuntu远程) v1.0.0 启动中 (HTTP-only 模式)...
2025-07-15 07:50:40,918 - INFO - 🌐 HTTP-only 模式: 仅启动 HTTP API，不启动微服务
2025-07-15 07:50:41,191 - INFO - ✅ PostgreSQL连接池创建成功
2025-07-15 07:50:41,191 - INFO - ✅ HTTP-only 模式下数据库连接成功
INFO:     Application startup complete.
INFO:     Uvicorn running on http://0.0.0.0:8009 (Press CTRL+C to quit)
2025-07-15 07:50:50,966 - INFO - ✅ PostgreSQL连接池创建成功
2025-07-15 07:50:50,967 - INFO - 📝 客户端数据写入请求: employee_id=215829
2025-07-15 07:50:50,969 - INFO - 📦 准备插入数据: {'entry_date': datetime.date(2025, 7, 15), 'employee_id': '215829', 'duration': 0.3, 'model': None, 'number': None, 'factory_number': None, 'project_number': None, 'unit_number': None, 'category': None, 'item': None, 'department': '131', 'source': 'user', 'ts': datetime.datetime(2025, 7, 15, 7, 50, 50, 969043)}
2025-07-15 07:50:51,016 - INFO - ✅ 数据已插入到entries表: entry_id=145123
2025-07-15 07:50:51,020 - INFO - ✅ 触发器已创建队列项: queue_id=91
INFO:     127.0.0.1:47952 - "POST /client/entries/create HTTP/1.1" 200 OK
2025-07-15 07:51:24,798 - INFO - ✅ PostgreSQL连接池创建成功
2025-07-15 07:51:24,798 - INFO - 📝 客户端数据写入请求: employee_id=215829
2025-07-15 07:51:24,798 - INFO - 📦 准备插入数据: {'entry_date': datetime.date(2025, 7, 15), 'employee_id': '215829', 'duration': 0.3, 'model': None, 'number': None, 'factory_number': None, 'project_number': None, 'unit_number': None, 'category': None, 'item': None, 'department': '131', 'source': 'user', 'ts': datetime.datetime(2025, 7, 15, 7, 51, 24, 798258)}
2025-07-15 07:51:24,810 - INFO - ✅ 数据已插入到entries表: entry_id=145124
2025-07-15 07:51:24,813 - INFO - ✅ 触发器已创建队列项: queue_id=92
INFO:     127.0.0.1:58208 - "POST /client/entries/create HTTP/1.1" 200 OK
2025-07-15 07:52:59,124 - INFO - ✅ PostgreSQL连接池创建成功
2025-07-15 07:52:59,124 - INFO - 📝 客户端数据写入请求: employee_id=215829
2025-07-15 07:52:59,124 - INFO - 📦 准备插入数据: {'entry_date': datetime.date(2025, 7, 15), 'employee_id': '215829', 'duration': 0.4, 'model': None, 'number': None, 'factory_number': None, 'project_number': None, 'unit_number': None, 'category': None, 'item': None, 'department': '131', 'source': 'user', 'ts': datetime.datetime(2025, 7, 15, 7, 52, 59, 124675)}
2025-07-15 07:52:59,133 - INFO - ✅ 数据已插入到entries表: entry_id=145125
2025-07-15 07:52:59,136 - INFO - ✅ 触发器已创建队列项: queue_id=93
INFO:     127.0.0.1:47804 - "POST /client/entries/create HTTP/1.1" 200 OK
2025-07-15 08:00:04,216 - INFO - 📊 Timeprotab查询完成: 31条记录
INFO:     127.0.0.1:47012 - "GET /api/timeprotab/?employee_id=215829&year=2025&month=7&limit=1000 HTTP/1.1" 200 OK
2025-07-15 08:00:04,221 - INFO - 🔗 HTTP-only 模式: 使用 service_manager 的数据库连接
2025-07-15 08:00:04,228 - INFO - 📊 Entries查询完成: 28条记录, 耗时: 0.006秒
INFO:     127.0.0.1:47012 - "GET /api/entries/?employee_id=215829&start_date=2025-07-01&end_date=2025-07-31&limit=1000 HTTP/1.1" 200 OK
INFO:     127.0.0.1:47012 - "GET /api/department/employee/215829 HTTP/1.1" 200 OK
INFO:     127.0.0.1:47012 - "GET /api/chart/months?employee_id=215829 HTTP/1.1" 200 OK
INFO:     127.0.0.1:47012 - "GET /api/chart/months?employee_id=215829 HTTP/1.1" 200 OK
2025-07-15 08:00:04,519 - INFO - 📊 Chart数据生成完成: 31天, 工作日: 9, 匹配: 27, 不匹配: 2
INFO:     127.0.0.1:47012 - "GET /api/chart/generate?employee_id=215829&start_date=2025-07-01&end_date=2025-07-31&chart_type=daily HTTP/1.1" 200 OK
2025-07-15 08:00:04,523 - INFO - 📊 Timeprotab查询完成: 31条记录
INFO:     127.0.0.1:47012 - "GET /api/timeprotab/?employee_id=215829&year=2025&month=7&limit=1000 HTTP/1.1" 200 OK
2025-07-15 08:00:04,557 - INFO - 📊 Chart数据生成完成: 31天, 工作日: 9, 匹配: 27, 不匹配: 2
INFO:     127.0.0.1:47012 - "GET /api/chart/generate?employee_id=215829&start_date=2025-07-01&end_date=2025-07-31&chart_type=daily HTTP/1.1" 200 OK
2025-07-15 08:00:04,562 - INFO - 📊 Timeprotab查询完成: 31条记录
INFO:     127.0.0.1:47012 - "GET /api/timeprotab/?employee_id=215829&year=2025&month=7&limit=1000 HTTP/1.1" 200 OK
2025-07-15 08:00:04,594 - INFO - 📊 Chart数据生成完成: 31天, 工作日: 9, 匹配: 27, 不匹配: 2
INFO:     127.0.0.1:47012 - "GET /api/chart/generate?employee_id=215829&start_date=2025-07-01&end_date=2025-07-31&chart_type=daily HTTP/1.1" 200 OK
2025-07-15 08:00:04,598 - INFO - 📊 Timeprotab查询完成: 31条记录
INFO:     127.0.0.1:47012 - "GET /api/timeprotab/?employee_id=215829&year=2025&month=7&limit=1000 HTTP/1.1" 200 OK
2025-07-15 08:00:04,630 - INFO - 📊 Chart数据生成完成: 30天, 工作日: 21, 匹配: 29, 不匹配: 1
INFO:     127.0.0.1:47012 - "GET /api/chart/generate?employee_id=215829&start_date=2025-06-01&end_date=2025-06-30&chart_type=daily HTTP/1.1" 200 OK
2025-07-15 08:00:04,635 - INFO - 📊 Timeprotab查询完成: 30条记录
INFO:     127.0.0.1:47012 - "GET /api/timeprotab/?employee_id=215829&year=2025&month=6&limit=1000 HTTP/1.1" 200 OK
2025-07-15 08:00:04,662 - INFO - 📊 Chart数据生成完成: 30天, 工作日: 21, 匹配: 29, 不匹配: 1
INFO:     127.0.0.1:47012 - "GET /api/chart/generate?employee_id=215829&start_date=2025-06-01&end_date=2025-06-30&chart_type=daily HTTP/1.1" 200 OK
2025-07-15 08:00:04,695 - INFO - 📊 Timeprotab查询完成: 30条记录
INFO:     127.0.0.1:47012 - "GET /api/timeprotab/?employee_id=215829&year=2025&month=6&limit=1000 HTTP/1.1" 200 OK
INFO:     Shutting down
INFO:     Waiting for application shutdown.
2025-07-15 08:01:12,862 - INFO - 🌐 HTTP-only 模式: 断开数据库连接
2025-07-15 08:01:12,866 - INFO - 🔌 PostgreSQL连接已关闭
INFO:     Application shutdown complete.
INFO:     Finished server process [2401871]

📡 接收到信号 15，正在关闭HTTP服务...
2025-07-15 08:48:57,066 - INFO - MySuite Server5 - 数据同步微服务 (Ubuntu远程) v1.0.0 启动中 (HTTP-only 模式)...
2025-07-15 08:48:57,066 - INFO - 🌐 HTTP-only 模式: 仅启动 HTTP API，不启动微服务
2025-07-15 08:48:57,288 - INFO - ✅ PostgreSQL连接池创建成功
2025-07-15 08:48:57,288 - INFO - ✅ HTTP-only 模式下数据库连接成功
2025-07-15 08:49:03,652 - INFO - 🔗 HTTP-only 模式: 使用 service_manager 的数据库连接
2025-07-15 08:49:03,652 - INFO - 🗑️ 收到删除请求: entry_id=145129
2025-07-15 08:49:03,781 - INFO - ✅ HTTP-only模式删除成功: entry_id=145129
2025-07-15 08:58:17,162 - INFO - 🌐 HTTP-only 模式: 断开数据库连接
2025-07-15 08:58:17,162 - INFO - 🔌 PostgreSQL连接已关闭
2025-07-15 09:52:27,971 - INFO - MySuite Server5 - 数据同步微服务 (Ubuntu远程) v1.0.0 启动中 (HTTP-only 模式)...
2025-07-15 09:52:27,971 - INFO - 🌐 HTTP-only 模式: 仅启动 HTTP API，不启动微服务
2025-07-15 09:52:28,213 - INFO - ✅ PostgreSQL连接池创建成功
2025-07-15 09:52:28,213 - INFO - ✅ HTTP-only 模式下数据库连接成功
2025-07-15 09:52:32,362 - INFO - 🔗 HTTP-only 模式: 使用 service_manager 的数据库连接
2025-07-15 09:52:32,450 - INFO - 📊 Entries查询完成: 5条记录, 耗时: 0.087秒
2025-07-15 09:52:32,452 - INFO - 🗑️ 收到删除请求: entry_id=603643
2025-07-15 09:52:32,476 - INFO - 🔍 通过internal_id=603643未找到记录，尝试通过external_id查找
2025-07-15 09:52:32,495 - INFO - 📋 找到记录: internal_id=145157, external_id=603643
2025-07-15 09:52:32,539 - INFO - ✅ HTTP-only模式删除成功: internal_id=145157, external_id=603643
2025-07-15 09:52:36,702 - INFO - 📊 Entries查询完成: 3条记录, 耗时: 0.007秒
2025-07-15 09:54:31,364 - INFO - 📊 Timeprotab查询完成: 31条记录
2025-07-15 09:54:31,374 - INFO - 📊 Entries查询完成: 34条记录, 耗时: 0.004秒
2025-07-15 09:54:31,671 - INFO - 📊 Chart数据生成完成: 31天, 工作日: 9, 匹配: 27, 不匹配: 2
2025-07-15 09:54:31,675 - INFO - 📊 Timeprotab查询完成: 31条记录
2025-07-15 09:54:31,702 - INFO - 📊 Chart数据生成完成: 31天, 工作日: 9, 匹配: 27, 不匹配: 2
2025-07-15 09:54:31,707 - INFO - 📊 Timeprotab查询完成: 31条记录
2025-07-15 09:54:31,759 - INFO - 📊 Chart数据生成完成: 31天, 工作日: 9, 匹配: 27, 不匹配: 2
2025-07-15 09:54:31,764 - INFO - 📊 Timeprotab查询完成: 31条记录
2025-07-15 09:54:31,781 - INFO - 📊 Chart数据生成完成: 30天, 工作日: 21, 匹配: 29, 不匹配: 1
2025-07-15 09:54:31,786 - INFO - 📊 Timeprotab查询完成: 30条记录
2025-07-15 09:54:31,860 - INFO - 📊 Chart数据生成完成: 30天, 工作日: 21, 匹配: 29, 不匹配: 1
2025-07-15 09:54:31,869 - INFO - 📊 Timeprotab查询完成: 30条记录
2025-07-15 09:54:41,610 - INFO - 🗑️ 收到删除请求: entry_id=603642
2025-07-15 09:54:41,628 - INFO - 🔍 通过internal_id=603642未找到记录，尝试通过external_id查找
2025-07-15 09:54:41,647 - INFO - 📋 找到记录: internal_id=145156, external_id=603642
2025-07-15 09:54:41,686 - INFO - ✅ HTTP-only模式删除成功: internal_id=145156, external_id=603642
2025-07-15 10:00:58,635 - INFO - 🌐 HTTP-only 模式: 断开数据库连接
2025-07-15 10:00:58,635 - INFO - 🔌 PostgreSQL连接已关闭
