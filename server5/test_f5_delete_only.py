import asyncio
import logging
import sys
from pathlib import Path

# 将项目根目录添加到Python路径，以便能够导入app模块
sys.path.append(str(Path(__file__).parent.parent))

from app.services.f5_bulk_sync import DeletionSyncService

# 配置日志记录
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    stream=sys.stdout  # 确保日志输出到控制台
)
logger = logging.getLogger(__name__)

async def main():
    """
    独立测试 f5 (DeletionSyncService) 的主函数。
    这个脚本不依赖f3，直接启动和运行f5的删除同步逻辑。
    """
    logger.info("--- 开始独立测试 f5 (DeletionSyncService) ---")
    f5_service = DeletionSyncService()
    try:
        # 1. 启动f5服务所需的所有连接（PostgreSQL, Redis, Server6-Client）
        await f5_service.start()
        logger.info("✅ f5 服务连接已成功启动。")
        
        # 2. 直接执行核心的删除同步逻辑
        logger.info("🚀 现在执行删除同步 run_deletion_sync()...")
        await f5_service.run_deletion_sync()
        logger.info("✅ 删除同步 run_deletion_sync() 执行完毕。")
        
    except Exception as e:
        logger.error(f"❌ 测试过程中发生严重错误: {e}", exc_info=True)
    finally:
        # 3. 确保服务的所有连接都被安全关闭
        logger.info("🔌 正在关闭 f5 服务连接...")
        await f5_service.stop()
        logger.info("--- f5 独立测试流程结束 ---")

if __name__ == "__main__":
    # 在Windows上，为asyncio设置正确的事件循环策略，以避免常见的兼容性问题
    if sys.platform == "win32":
        asyncio.set_event_loop_policy(asyncio.WindowsSelectorEventLoopPolicy())
    
    # 运行主异步函数
    asyncio.run(main()) 