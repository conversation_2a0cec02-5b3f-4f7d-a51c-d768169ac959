# Server5 多平台支持说明
*25.06.26 增加多平台自动检测功能*

## 概述

Server5 现在支持在不同操作系统平台上运行，包括 Windows、Linux 和 macOS。系统会自动检测当前运行平台，并选择合适的MDB数据库访问方式。

## 支持的平台

### Windows 平台
- **特点**: 可直接访问MDB数据库
- **要求**: 需要安装 `pywin32` (`pip install pywin32`)
- **功能**: 全功能MDB读写操作
- **访问方式**: win32com.client + pythoncom

### Linux 平台
- **特点**: 使用模拟或远程MDB访问
- **功能**: 完整的模拟MDB操作（用于开发和测试）
- **访问方式**: SQLite模拟 + 可选远程API调用

### macOS 平台
- **特点**: 使用模拟MDB访问
- **功能**: 完整的模拟MDB操作
- **访问方式**: SQLite模拟

## 自动检测机制

### 平台检测
```python
import platform
platform_name = platform.system()  # 'Windows', 'Linux', 'Darwin'
```

### 功能适配
- **Windows**: 优先使用真实MDB访问
- **非Windows**: 自动切换到模拟模式
- **错误处理**: 如果真实访问失败，自动降级到模拟模式

## 使用方法

### 1. 直接使用增强版db_utils
```python
from app.utils.db_utils_enhanced import MultiPlatformMDBClient

# 自动检测平台并创建客户端
client = MultiPlatformMDBClient()

# 获取平台信息
info = client.get_platform_info()
print(f"运行平台: {info['platform']}")
print(f"可真实MDB访问: {info['can_real_mdb_access']}")

# 写入数据（自动适配平台）
employee_data = {
    'employee_id': 'TEST001',
    'date': '2025/06/26',
    'category': 'TEST',
    'item': 'TestItem',
    'time': 8.0,
    'department': 'TestDept'
}

ok, msg, new_id = client.write_work_time(employee_data)
print(f"写入结果: {ok}, 消息: {msg}, 新ID: {new_id}")
```

### 2. 异步使用
```python
from app.utils.db_utils_enhanced import AsyncMDBClient

async def test_async():
    client = AsyncMDBClient()
    
    # 异步写入
    ok, msg, new_id = await client.write_work_time(employee_data)
    
    # 异步查询
    results = await client.get_user_data('TEST001', '2025/06/01', '2025/06/30')
    
    return ok, results

# 运行异步函数
import asyncio
asyncio.run(test_async())
```

### 3. 兼容性函数
```python
# 保持与原db_utils.py完全相同的接口
from app.utils.db_utils_enhanced import write_work_time, delete_work_time_by_id

# 无需修改现有代码，自动支持多平台
ok, msg, new_id = write_work_time(employee_data)
ok, msg = delete_work_time_by_id(str(new_id))
```

## 配置选项

### 环境变量
```bash
# 强制指定平台类型（用于测试）
export FORCE_PLATFORM="Windows"  # 或 "Linux", "Darwin"

# 强制使用模拟模式
export USE_MOCK_MDB="true"
```

### 配置文件
```python
# config/config.py 中的设置
PlatformConfig.get_config() = {
    "platform": "Linux",
    "use_mock_in_linux": True,           # 在Linux上使用模拟模式
    "enable_remote_mdb": False,          # 是否启用远程MDB访问
    "remote_mdb_api_base": "http://************:8009",  # 远程API地址
    "mdb_path": "/tmp/mock_database.db", # 数据库路径
    "prefer_real_mdb": False             # 是否优先使用真实MDB
}
```

## 部署建议

### 开发环境（Ubuntu）
```bash
# 在Ubuntu开发机上运行Server5
cd server5
python test_platform_detection.py  # 测试平台检测
python start_server5.py           # 启动服务（使用模拟模式）
```

### 生产环境（Windows）
```bash
# 在Windows 10 (************)上运行Server5
pip install pywin32  # 安装Windows COM支持
cd server5
python test_platform_detection.py  # 测试平台检测
python start_server5.py           # 启动服务（使用真实MDB）
```

### 混合环境
- **Server5**: 可运行在任何平台
- **PostgreSQL + MongoDB**: 始终在************ Windows 10
- **MDB**: 始终在************ Windows 10
- **Redis**: 可运行在任何平台

## 测试验证

运行平台检测测试：
```bash
python test_platform_detection.py
```

测试输出示例（Linux）：
```
🎯 开始多平台检测测试
🌐 检测到操作系统: Linux
🔧 Linux环境初始化，使用模拟/远程模式
✅ 检测到mdb-tools，可以读取MDB文件

🔄 测试写入操作...
写入结果: True, 消息: 模拟写入成功 (平台: Linux), 新ID: 50554

🔍 测试查询操作...
查询结果: 3 条记录

✅ 所有测试完成!
```

## 技术细节

### 平台检测实现
```python
class MultiPlatformMDBClient:
    def __init__(self):
        # 检测操作系统
        self.platform_name = platform.system()
        self.is_windows = self.platform_name == 'Windows'
        self.is_linux = self.platform_name == 'Linux'
        
        # 检测win32com可用性
        if self.is_windows:
            try:
                import win32com.client
                self.win32_available = True
            except ImportError:
                self.win32_available = False
```

### 自动适配逻辑
```python
def write_work_time(self, employee_data):
    if self.is_windows and self.win32_available:
        return self._windows_write_work_time(employee_data)  # 真实MDB
    else:
        return self._mock_write_work_time(employee_data)     # 模拟MDB
```

## 优势

1. **无缝部署**: Server5可以在任何平台上运行
2. **自动适配**: 无需手动配置，自动检测并选择最佳访问方式
3. **开发友好**: 在Ubuntu上开发，自动使用模拟模式
4. **生产就绪**: 在Windows上部署，自动使用真实MDB
5. **向后兼容**: 保持与原db_utils.py完全相同的接口
6. **错误恢复**: 真实访问失败时自动降级到模拟模式

## 后续扩展

### 远程MDB访问
可以在Windows 10 (************)上部署一个MDB访问API服务，让其他平台通过HTTP调用访问：

```python
# 未来可实现
async def _remote_mdb_call(self, operation, data):
    async with aiohttp.ClientSession() as session:
        async with session.post(f"{self.remote_api}/mdb/{operation}", json=data) as resp:
            return await resp.json()
```

### 网络共享访问
通过SMB/CIFS共享，让Linux直接访问Windows上的MDB文件：

```bash
# 挂载共享
sudo mount -t cifs //************/shared /mnt/shared -o username=user,password=pass
```

这样就实现了Server5的多平台支持，可以灵活部署在不同环境中！ 