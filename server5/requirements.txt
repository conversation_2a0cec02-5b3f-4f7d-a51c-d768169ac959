# Server5 数据同步微服务依赖包
# 创建时间: 2025/06/27
# 250626，第二阶段: 添加aiohttp用于Server6 API通信

# FastAPI和异步框架
fastapi>=0.104.0
uvicorn[standard]>=0.24.0
httpx>=0.25.0
# 250626，第二阶段: Server6 API客户端依赖
aiohttp>=3.8.0

# PostgreSQL异步支持
asyncpg>=0.29.0
psycopg2-binary>=2.9.0
sqlalchemy>=2.0.0

# Redis异步支持
redis[hiredis]>=4.5.0

# MongoDB异步支持
motor>=3.3.0
pymongo>=4.5.0

# 日志和监控
loguru>=0.7.0
rich>=13.0.0

# 工具库
python-dateutil>=2.8.0
tenacity>=8.2.0
click>=8.1.0
pydantic>=2.4.0
python-jose[cryptography]>=3.3.0

# 性能和异步工具
aiojobs>=0.3.0
aiofiles>=23.2.1
asyncio-compat>=0.1.2

# 系统监控
psutil>=5.9.0

# 定时任务
schedule>=1.2.0
aiocron>=1.8

# 开发和测试
pytest>=7.4.0
pytest-asyncio>=0.21.0 