#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
多次 UPDATE 测试脚本
测试完整的用户操作 -> 触发器 -> F2 -> Server6 -> MDB 流程
目标：将 external_id=602631 的 duration 更新为 6.0
"""

import asyncio
import asyncpg
import sys
from pathlib import Path
from datetime import datetime
import time

# 添加项目路径
sys.path.append(str(Path(__file__).parent))

# 数据库连接
PG_DATABASE_URL = "***************************************************/imdb"
TARGET_EXTERNAL_ID = 602631
NEW_DURATION = 5.0

async def test_complete_update_workflow():
    """测试完整的 UPDATE 工作流程"""
    print("🎯 完整 UPDATE 工作流程测试")
    print("=" * 60)
    print(f"目标: external_id={TARGET_EXTERNAL_ID}, 新 duration={NEW_DURATION}")
    print("=" * 60)
    
    conn = None
    try:
        # 1. 连接数据库
        conn = await asyncpg.connect(PG_DATABASE_URL)
        print("✅ 数据库连接成功")
        
        # 2. 检查当前状态
        print(f"\n📋 1. 检查当前记录状态...")
        current_record = await conn.fetchrow("""
            SELECT id, external_id, employee_id, entry_date, duration, project_number, source, ts
            FROM public.entries 
            WHERE external_id = $1
        """, TARGET_EXTERNAL_ID)
        
        if not current_record:
            print(f"❌ 找不到 external_id = {TARGET_EXTERNAL_ID} 的记录")
            return False
        
        print(f"   📊 当前记录状态:")
        print(f"   - PostgreSQL ID: {current_record['id']}")
        print(f"   - External ID: {current_record['external_id']}")
        print(f"   - Employee ID: {current_record['employee_id']}")
        print(f"   - Entry Date: {current_record['entry_date']}")
        print(f"   - 当前 Duration: {current_record['duration']}")
        print(f"   - Project Number: {current_record['project_number']}")
        print(f"   - Source: {current_record['source']}")
        print(f"   - 最后更新: {current_record['ts']}")
        
        original_entry_id = current_record['id']
        original_duration = current_record['duration']
        
        # 3. 清理之前未处理的队列项
        print(f"\n🧹 2. 清理未处理的队列项...")
        cleanup_count = await conn.execute("""
            DELETE FROM entries_push_queue 
            WHERE entry_id = $1 AND synced = FALSE
        """, original_entry_id)
        cleanup_num = int(cleanup_count.split()[-1]) if cleanup_count else 0
        print(f"   清理了 {cleanup_num} 个未同步队列项")
        
        # 4. 模拟用户操作：更新 PostgreSQL 记录
        print(f"\n👤 3. 模拟用户操作：更新记录...")
        print(f"   将 duration 从 {original_duration} 更新为 {NEW_DURATION}")
        
        # 这是关键的用户操作，会触发触发器
        update_result = await conn.fetchrow("""
            UPDATE public.entries
            SET
                duration = $1,
                source = 'user',
                ts = NOW()
            WHERE external_id = $2
            RETURNING id, external_id, duration, ts;
        """, NEW_DURATION, TARGET_EXTERNAL_ID)
        
        if update_result:
            print(f"   ✅ 用户操作成功:")
            print(f"   - PostgreSQL ID: {update_result['id']}")
            print(f"   - External ID: {update_result['external_id']}")
            print(f"   - 新 Duration: {update_result['duration']}")
            print(f"   - 更新时间: {update_result['ts']}")
        else:
            print("   ❌ 用户操作失败")
            return False
        
        # 5. 等待触发器执行
        print(f"\n⏳ 4. 等待触发器执行...")
        await asyncio.sleep(3)
        
        # 6. 检查触发器是否创建了队列项
        print(f"\n📋 5. 检查触发器创建的队列项...")
        queue_items = await conn.fetch("""
            SELECT queue_id, operation, entry_id, external_id, synced, created_ts
            FROM entries_push_queue
            WHERE entry_id = $1 AND operation = 'UPDATE'
            ORDER BY created_ts DESC
            LIMIT 3
        """, original_entry_id)
        
        if not queue_items:
            print("   ❌ 触发器没有创建 UPDATE 任务")
            print("   💡 可能的原因:")
            print("   - 触发器条件不满足")
            print("   - source 字段不是 'user'")
            return False
        
        print(f"   ✅ 触发器创建了 {len(queue_items)} 个 UPDATE 队列项:")
        latest_queue_item = None
        for item in queue_items:
            status_icon = "🟢" if item['synced'] else "🔴"
            print(f"   {status_icon} queue_id={item['queue_id']}, synced={item['synced']}, created_ts={item['created_ts']}")
            if latest_queue_item is None:
                latest_queue_item = item
        
        # 7. 检查 F2 服务是否处理了队列项
        print(f"\n🔍 6. 监控 F2 服务处理...")
        queue_id = latest_queue_item['queue_id']
        
        print(f"   正在监控 queue_id={queue_id} 的处理状态...")
        
        # 监控最多 60 秒
        max_wait_time = 60
        check_interval = 3
        checks = 0
        max_checks = max_wait_time // check_interval
        
        f2_processed = False
        while checks < max_checks:
            await asyncio.sleep(check_interval)
            checks += 1
            
            # 检查队列项状态
            current_status = await conn.fetchrow("""
                SELECT synced, created_ts 
                FROM entries_push_queue 
                WHERE queue_id = $1
            """, queue_id)
            
            if current_status and current_status['synced']:
                print(f"   ✅ F2 服务处理完成！(等待了 {checks * check_interval} 秒)")
                print(f"   - queue_id: {queue_id}")
                print(f"   - 状态: synced = TRUE")
                f2_processed = True
                break
            else:
                synced_status = current_status['synced'] if current_status else 'Unknown'
                print(f"   ⏳ F2 处理中... ({checks * check_interval}/{max_wait_time}秒) synced={synced_status}")
        
        if not f2_processed:
            print(f"   ❌ F2 服务处理超时！(等待了 {max_wait_time} 秒)")
            print("   💡 可能的原因:")
            print("   - F2 服务没有运行")
            print("   - Server6 连接问题") 
            print("   - F2 代码有错误")
            
            # 但我们继续验证 PostgreSQL 的状态
            print(f"\n📊 7. 验证 PostgreSQL 状态...")
        else:
            print(f"\n🎉 7. 最终验证...")
        
        # 8. 最终状态检查
        final_record = await conn.fetchrow("""
            SELECT duration, ts, source
            FROM public.entries 
            WHERE external_id = $1
        """, TARGET_EXTERNAL_ID)
        
        final_queue_status = await conn.fetchrow("""
            SELECT synced 
            FROM entries_push_queue 
            WHERE queue_id = $1
        """, queue_id)
        
        print(f"   📊 PostgreSQL 最终状态:")
        print(f"   - Duration: {final_record['duration']}")
        print(f"   - Source: {final_record['source']}")
        print(f"   - 最后更新: {final_record['ts']}")
        
        print(f"   📋 队列最终状态:")
        print(f"   - Queue ID: {queue_id}")
        print(f"   - Synced: {final_queue_status['synced'] if final_queue_status else 'Unknown'}")
        
        # 9. 成功判断
        postgresql_updated = float(final_record['duration']) == NEW_DURATION
        queue_processed = final_queue_status and final_queue_status['synced']
        
        print(f"\n📈 测试结果:")
        print(f"   {'✅' if postgresql_updated else '❌'} PostgreSQL 更新: {postgresql_updated}")
        print(f"   {'✅' if queue_processed else '❌'} 队列处理: {queue_processed}")
        
        if postgresql_updated and queue_processed:
            print(f"\n🎉 完整工作流程测试成功！")
            print(f"   ✅ 用户操作 -> PostgreSQL 更新成功")
            print(f"   ✅ 触发器 -> 队列任务创建成功")
            print(f"   ✅ F2 服务 -> 队列处理成功")
            print(f"   ✅ Server6 -> MDB 同步成功")
            return True
        elif postgresql_updated:
            print(f"\n⚠️ 部分成功：PostgreSQL 更新成功，但 F2 处理可能有问题")
            print(f"   💡 建议检查 F2 服务状态")
            return True
        else:
            print(f"\n❌ 测试失败")
            return False
            
    except Exception as e:
        print(f"❌ 测试过程中出错: {e}")
        import traceback
        traceback.print_exc()
        return False
    finally:
        if conn:
            await conn.close()

async def main():
    """主函数"""
    print("🧪 多次 UPDATE 测试 - external_id=602631")
    
    success = await test_complete_update_workflow()
    
    print("=" * 60)
    if success:
        print("🎉 UPDATE 工作流程测试成功！")
        print("💡 可以继续进行多次更新测试")
    else:
        print("❌ UPDATE 工作流程测试失败")
        print("💡 需要检查 F2 服务和 Server6 连接")
    print("=" * 60)

if __name__ == "__main__":
    asyncio.run(main()) 