import asyncio
import logging
from datetime import date
import sys
from pathlib import Path

# Ensure the project root is in the Python path
sys.path.append(str(Path(__file__).parent.parent))

from server5.app.services.f3_data_puller import DataPullerService

# Configure basic logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')

async def run_f3_test_for_range(start_date: date, end_date: date):
    """
    Manually triggers the f3 data pull and sync process for a custom date range.

    This test script will:
    1. Initialize the f3 DataPullerService.
    2. Connect to its required database clients (PostgreSQL, Redis, Server6).
    3. Use the high-performance bulk query to fetch ALL records from the MDB
       within the specified date range.
    4. Use the service's internal _sync_to_db method to safely transfer the
       data to the main 'entries' table via the staging table.
    5. Cleanly disconnect all clients.
    """
    logging.info("Initializing F3 Data Puller Service for custom range test...")
    puller = DataPullerService()
    
    # Connect to PostgreSQL, Redis, and establish a session with Server6
    # connect_only prevents the periodic puller from starting
    await puller.connect_only()
    logging.info("Database and Server6 clients connected.")

    try:
        logging.info(f"Starting manual data pull for ALL employees from {start_date} to {end_date}...")
        
        # Step 1: Use the server6_client to fetch all records in the date range.
        # This is the same method used by manual_pull_for_date but applied to a range.
        all_mdb_records = await puller.server6_client.query_bulk_fast(
            start_date=start_date,
            end_date=end_date
        )

        if not all_mdb_records:
            logging.warning(f"No records found in MDB for the range {start_date} to {end_date}.")
            return

        logging.info(f"Found {len(all_mdb_records)} records in MDB. Preparing to sync...")

        # Step 2: Use the internal sync method to process the data.
        # This will handle the staging table and the final UPSERT logic.
        # The data mapping will correctly set `source='system'`.
        affected_rows = await puller._sync_to_db(all_mdb_records)
        
        logging.info("✅ Sync complete!")
        logging.info(f"Total records found in MDB: {len(all_mdb_records)}")
        logging.info(f"Rows affected in PostgreSQL 'entries' table: {affected_rows}")

    except Exception as e:
        logging.error(f"An error occurred during the test run: {e}", exc_info=True)
    finally:
        # Step 3: Ensure all connections are closed cleanly.
        logging.info("Closing all connections...")
        await puller.stop()
        logging.info("Test finished.")

if __name__ == "__main__":
    # --- Define the custom date range for the test ---
    start_pull_date = date(2025, 7, 1)
    end_pull_date = date(2025, 7, 5)
    # --------------------------------------------------

    # Run the asynchronous test function
    asyncio.run(run_f3_test_for_range(start_pull_date, end_pull_date)) 