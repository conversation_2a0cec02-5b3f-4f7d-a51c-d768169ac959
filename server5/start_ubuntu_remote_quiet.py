# №02025/06/27 + 日本东京时间11：24 + 安静版本的Ubuntu远程启动脚本
# Server5多平台启动 - Ubuntu远程模式 (Quiet版本)

import os
import sys
import asyncio
import logging
from pathlib import Path
import uvicorn

# 配置简化的日志 - 只输出重要信息和错误
logging.basicConfig(
    level=logging.WARNING,  # 只显示WARNING和ERROR
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        # 终端只显示错误
        logging.StreamHandler(sys.stdout),
        # 详细日志记录到文件
        logging.FileHandler('logs/server5_quiet.log', encoding='utf-8')
    ]
)

# 为文件日志设置详细级别
file_handler = logging.FileHandler('logs/server5_quiet.log', encoding='utf-8')
file_handler.setLevel(logging.DEBUG)
file_handler.setFormatter(logging.Formatter(
    '%(asctime)s - %(name)s - %(levelname)s - %(message)s'
))

# 获取根日志记录器并添加文件处理器
root_logger = logging.getLogger()
root_logger.addHandler(file_handler)

logger = logging.getLogger(__name__)

# 添加项目路径
sys.path.insert(0, str(Path(__file__).parent))

# 导入配置选择器并加载配置
from app.config_selector import ConfigSelector

# 在导入任何服务之前先加载配置
def load_configuration():
    """加载配置"""
    try:
        selector = ConfigSelector()
        config_dict, config_name = selector.load_config()
        config_module = selector.apply_config_to_module(config_dict)
        
        # 显示配置信息
        info = selector.get_current_config_info()
        print(f"📋 配置信息:")
        print(f"  - 配置文件: {config_name}")
        print(f"  - 平台: {info['platform']}")
        print(f"  - 主机: {info['hostname']}")
        print(f"  - IP: {info['local_ip']}")
        print(f"  - 自动检测: {info['auto_detected']}")
        
        return config_module
    except Exception as e:
        print(f"❌ 配置加载失败: {e}")
        raise

# 加载配置
load_configuration()

def main():
    """主函数 - 仅异步启动 f1~f5 服务"""
    print("🚀 MySuite Server5 - Ubuntu 远程模式 (仅启动 f1~f5 服务)")

    try:
        asyncio.run(run_services())
    except KeyboardInterrupt:
        print("\n👋 接收到中断信号，正在关闭服务...")
    except Exception as e:
        print(f"❌ 启动失败: {e}")
        logging.error(f"Server5服务启动失败: {e}")
        sys.exit(1)

# ------------------------------------------------------------------
# 新的异步启动逻辑

async def run_services():
    """异步启动 f1、f2、f3、f4 服务并保持运行"""

    from app.services.f1_listener import ListenerService
    from app.services.f2_push_writer import PushWriterServiceFixed
    from app.services.f3_data_puller import DataPullerService
    from app.services.f4_operation_handler import OperationHandlerService
    # 2025 07/04 +  16：00 + 相关主题: f5不再由启动脚本管理，而是由f3调用，因此移除
    # from app.services.f5_bulk_sync import DeletionSyncService

    # 实例化服务
    f1 = ListenerService()
    f2 = PushWriterServiceFixed()
    f3 = DataPullerService()
    f4 = OperationHandlerService()
    # 2025 07/04 +  16：00 + 相关主题: f5不再需要实例化
    # f5 = DeletionSyncService()

    # 启动所有服务
    results = await asyncio.gather(
        f1.start(),
        f2.start(),
        f3.start(),
        f4.start(),
        # 2025 07/04 +  16：00 + 相关主题: f5不再需要启动
        # f5.start(),
        return_exceptions=True,
    )

    # 打印启动结果
    services = ["f1_listener", "f2_push_writer", "f3_data_puller", "f4_operation_handler"] # 2025 07/04 +  16：00 + 相关主题: 从列表中移除f5
    for name, res in zip(services, results):
        status = "✅" if (isinstance(res, bool) and res) else "❌"
        print(f"{status} {name} {'启动成功' if status=='✅' else '启动失败'}")

    # 如果有任何服务启动失败，直接退出
    if not all(isinstance(r, bool) and r for r in results):
        print("❌ 有服务启动失败，请检查日志。即将退出...")
        await stop_services([f1, f2, f3, f4]) # 2025 07/04 +  16：00 + 相关主题: 从停止列表中移除f5
        return

    print("🎉 所有服务已启动，按 Ctrl+C 退出")

    # 阻塞运行，直到收到 KeyboardInterrupt
    stop_event = asyncio.Event()
    try:
        await stop_event.wait()
        #pass
    except asyncio.CancelledError:
        pass
    finally:
        #pass
        await stop_services([f1, f2, f3, f4]) # 2025 07/04 +  16：00 + 相关主题: 从停止列表中移除f5


async def stop_services(service_list):
    """停止所有服务"""
    stop_coros = []
    for svc in service_list:
        if hasattr(svc, "stop"):
            stop_coros.append(svc.stop())
    await asyncio.gather(*stop_coros, return_exceptions=True)

if __name__ == "__main__":
    main() 