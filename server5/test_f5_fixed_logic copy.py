#!/usr/bin/env python3
"""
测试修复后的f5逻辑
先恢复一些测试数据，然后验证删除同步功能
"""

import asyncio
import logging
from datetime import date
import sys
from pathlib import Path

# 添加项目路径
sys.path.append(str(Path(__file__).parent))

from app.services.f5_bulk_sync import DeletionSyncService

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

class F5FixedLogicTest:
    """f5修复逻辑测试类"""
    
    def __init__(self):
        self.f5_service = DeletionSyncService()
        self.test_employee_id = "215829"
        self.test_start_date = date(2025, 7, 1)
        self.test_end_date = date(2025, 7, 16)
    
    async def setup(self):
        """初始化服务"""
        logger.info("🚀 初始化f5删除同步服务...")
        
        # 启动f5服务
        success = await self.f5_service.start()
        if not success:
            logger.error("❌ f5服务启动失败")
            return False
        
        logger.info("✅ f5服务启动成功")
        return True
    
    async def cleanup(self):
        """清理资源"""
        logger.info("🔌 停止f5服务...")
        await self.f5_service.stop()
        logger.info("✅ f5服务已停止")
    
    async def restore_test_data(self):
        """恢复测试数据"""
        logger.info("📊 恢复测试数据...")
        
        try:
            # 从MDB获取一些记录作为测试数据
            mdb_records = await self.f5_service.server6_client.query_entries(
                employee_id=self.test_employee_id,
                start_date=self.test_start_date,
                end_date=self.test_end_date
            )
            
            if not mdb_records:
                logger.warning("⚠️ MDB中没有找到测试数据")
                return False
            
            # 选择前5条记录作为测试数据
            test_records = mdb_records[:24]
            logger.info(f"📋 选择 {len(test_records)} 条记录作为测试数据")
            
            # 插入到PostgreSQL中（不设置external_id，模拟未同步状态）
            for i, record in enumerate(test_records, 1):
                try:
                    # 准备插入数据
                    insert_data = {
                        'employee_id': record.get('employee_id'),
                        'entry_date': record.get('entry_date'),
                        'model': record.get('model'),
                        'number': record.get('number'),
                        'factory_number': record.get('factory_number'),
                        'project_number': record.get('project_number'),
                        'unit_number': record.get('unit_number'),
                        'category': int(record.get('category', 0)),  # 转换为整数
                        'item': int(record.get('item', 0)),  # 转换为整数
                        'duration': float(record.get('duration', 0)),  # 转换为浮点数
                        'department': record.get('department')
                    }
                    
                    # 插入记录（不设置external_id）
                    entry_id = await self.f5_service.imdb_client.create_entry(
                        insert_data, source='system'
                    )
                    
                    logger.info(f"✅ 插入测试记录 {i}: entry_id={entry_id}")
                    
                except Exception as e:
                    logger.error(f"❌ 插入测试记录 {i} 失败: {e}")
            
            logger.info("✅ 测试数据恢复完成")
            return True
            
        except Exception as e:
            logger.error(f"❌ 恢复测试数据失败: {e}")
            return False
    
    async def test_fixed_logic(self):
        """测试修复后的逻辑"""
        logger.info("=" * 80)
        logger.info("🧪 测试修复后的f5逻辑")
        logger.info("=" * 80)
        
        try:
            # 1. 恢复测试数据
            if not await self.restore_test_data():
                return False
            
            # 2. 检查恢复后的数据
            logger.info("")
            logger.info("📊 检查恢复后的数据...")
            
            pg_query = """
                SELECT id, external_id, entry_date, employee_id, source
                FROM entries 
                WHERE employee_id = $1
                  AND entry_date BETWEEN $2 AND $3
                ORDER BY entry_date DESC, ts DESC
            """
            
            pg_results = await self.f5_service.imdb_client.execute_query(
                pg_query, self.test_employee_id, self.test_start_date, self.test_end_date
            )
            
            logger.info(f"📈 PostgreSQL中找到 {len(pg_results)} 条记录")
            
            # 3. 执行删除同步
            logger.info("")
            logger.info("🔄 执行删除同步...")
            await self.f5_service.run_deletion_sync_for_employee(
                employee_id=self.test_employee_id,
                start_date=self.test_start_date,
                end_date=self.test_end_date
            )
            
            # 4. 检查同步后的结果
            logger.info("")
            logger.info("📊 检查同步后的结果...")
            
            pg_results_after = await self.f5_service.imdb_client.execute_query(
                pg_query, self.test_employee_id, self.test_start_date, self.test_end_date
            )
            
            logger.info(f"📈 同步后PostgreSQL中有 {len(pg_results_after)} 条记录")
            
            # 5. 分析结果
            logger.info("")
            logger.info("📊 结果分析:")
            logger.info(f"   同步前记录数: {len(pg_results)}")
            logger.info(f"   同步后记录数: {len(pg_results_after)}")
            
            if len(pg_results) > len(pg_results_after):
                deleted_count = len(pg_results) - len(pg_results_after)
                logger.info(f"   删除记录数: {deleted_count}")
                logger.info("   ✅ 删除同步功能正常工作")
            else:
                logger.info("   ℹ️ 没有记录被删除")
            
            return True
            
        except Exception as e:
            logger.error(f"❌ 测试修复后的逻辑失败: {e}", exc_info=True)
            return False
    
    async def run_test(self):
        """运行完整测试"""
        try:
            # 初始化
            if not await self.setup():
                return False
            
            # 执行测试
            success = await self.test_fixed_logic()
            if not success:
                return False
            
            logger.info("")
            logger.info("🎉 测试完成！")
            return True
            
        except Exception as e:
            logger.error(f"❌ 测试执行失败: {e}", exc_info=True)
            return False
        finally:
            # 清理资源
            await self.cleanup()

async def main():
    """主函数"""
    logger.info("🚀 开始f5修复逻辑测试")
    logger.info("=" * 80)
    
    # 创建测试实例
    test = F5FixedLogicTest()
    
    # 运行测试
    success = await test.run_test()
    
    if success:
        logger.info("✅ 所有测试通过")
    else:
        logger.error("❌ 测试失败")
    
    logger.info("=" * 80)
    logger.info("🏁 测试结束")

if __name__ == "__main__":
    asyncio.run(main()) 