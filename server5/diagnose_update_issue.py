#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
诊断UPDATE操作无法同步到MDB的问题
"""

import asyncio
import sys
from pathlib import Path
import logging
import aiohttp

# 添加项目路径
sys.path.append(str(Path(__file__).parent))

from app.database import IMDBClient
from app.utils.server6_client import Server6Client

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

async def test_server6_connection():
    """测试Server6连接"""
    print("\n🔍 测试Server6连接...")
    
    try:
        client = Server6Client()
        await client.connect()
        
        # 测试健康检查
        health_response = await client._make_request('GET', '/health')
        print(f"✅ Server6健康检查: {health_response}")
        
        # 测试MDB连接
        mdb_response = await client._make_request('GET', '/mdb/test')
        print(f"✅ MDB连接测试: {mdb_response}")
        
        await client.disconnect()
        return True
        
    except Exception as e:
        print(f"❌ Server6连接失败: {e}")
        return False

async def test_f2_service():
    """测试F2推送服务"""
    print("\n🔍 测试F2推送服务...")
    
    try:
        from app.services.f2_push_writer_fixed import PushWriterServiceFixed
        f2_service = PushWriterServiceFixed()
        
        # 检查Server6客户端
        if hasattr(f2_service, 'server6_client'):
            print("✅ F2服务已初始化Server6客户端")
            
            # 测试Server6连接
            status = await f2_service.server6_client.get_status()
            print(f"✅ Server6状态: {status}")
        else:
            print("❌ F2服务未初始化Server6客户端")
            return False
            
        return True
        
    except Exception as e:
        print(f"❌ F2服务测试失败: {e}")
        return False

async def test_queue_processing():
    """测试队列处理"""
    print("\n🔍 测试队列处理...")
    
    imdb_client = IMDBClient()
    
    try:
        await imdb_client.connect()
        
        # 检查队列状态
        queue_count = await imdb_client.execute_query(
            "SELECT COUNT(*) as count FROM entries_push_queue WHERE synced = false"
        )
        print(f"📊 未同步队列项数量: {queue_count[0]['count']}")
        
        # 检查最近的队列项
        recent_items = await imdb_client.execute_query(
            "SELECT queue_id, entry_id, operation, synced, created_ts FROM entries_push_queue ORDER BY created_ts DESC LIMIT 5"
        )
        
        print("📋 最近的队列项:")
        for item in recent_items:
            print(f"  - queue_id={item['queue_id']}, entry_id={item['entry_id']}, operation={item['operation']}, synced={item['synced']}")
        
        await imdb_client.disconnect()
        return True
        
    except Exception as e:
        print(f"❌ 队列处理测试失败: {e}")
        return False

async def test_update_operation():
    """测试UPDATE操作"""
    print("\n🔍 测试UPDATE操作...")
    
    imdb_client = IMDBClient()
    
    try:
        await imdb_client.connect()
        
        # 目标记录
        target_entry_id = 10612
        
        # 检查记录状态
        entry_info = await imdb_client.execute_query(
            "SELECT id, duration, external_id, source FROM entries WHERE id = $1",
            target_entry_id
        )
        
        if entry_info:
            entry = entry_info[0]
            print(f"📊 记录状态:")
            print(f"  - ID: {entry['id']}")
            print(f"  - duration: {entry['duration']}")
            print(f"  - external_id: {entry['external_id']}")
            print(f"  - source: {entry['source']}")
            
            if entry['external_id'] is None:
                print("❌ 记录缺少external_id，无法执行UPDATE操作")
                return False
        else:
            print(f"❌ 找不到记录: entry_id={target_entry_id}")
            return False
        
        await imdb_client.disconnect()
        return True
        
    except Exception as e:
        print(f"❌ UPDATE操作测试失败: {e}")
        return False

async def test_server6_update_api():
    """测试Server6的UPDATE API"""
    print("\n🔍 测试Server6的UPDATE API...")
    
    try:
        client = Server6Client()
        await client.connect()
        
        # 测试数据
        test_data = {
            'employee_id': '215829',
            'entry_date': '2025/06/26',
            'model': '',
            'number': '',
            'factory_number': '',
            'project_number': '24585',
            'unit_number': '',
            'category': 3,
            'item': 7,
            'duration': 6.0,
            'department': '131'
        }
        
        # 测试更新操作 (使用一个存在的external_id)
        external_id = 602610  # 使用已知的external_id
        
        try:
            response = await client.update_entry(external_id, test_data)
            print(f"✅ Server6 UPDATE API测试成功: {response}")
        except Exception as e:
            print(f"❌ Server6 UPDATE API测试失败: {e}")
            return False
        
        await client.disconnect()
        return True
        
    except Exception as e:
        print(f"❌ Server6 UPDATE API测试失败: {e}")
        return False

async def check_f2_worker_status():
    """检查F2工作线程状态"""
    print("\n🔍 检查F2工作线程状态...")
    
    try:
        from app.services.f2_push_writer_fixed import PushWriterServiceFixed
        f2_service = PushWriterServiceFixed()
        
        # 获取服务状态
        status = await f2_service.get_status()
        print(f"📊 F2服务状态: {status}")
        
        return True
        
    except Exception as e:
        print(f"❌ F2工作线程状态检查失败: {e}")
        return False

async def main():
    """主诊断函数"""
    print("🔧 开始诊断UPDATE操作问题...")
    print("=" * 60)
    
    # 1. 测试Server6连接
    server6_ok = await test_server6_connection()
    
    # 2. 测试F2服务
    f2_ok = await test_f2_service()
    
    # 3. 测试队列处理
    queue_ok = await test_queue_processing()
    
    # 4. 测试UPDATE操作
    update_ok = await test_update_operation()
    
    # 5. 测试Server6 UPDATE API
    api_ok = await test_server6_update_api()
    
    # 6. 检查F2工作线程状态
    worker_ok = await check_f2_worker_status()
    
    # 总结
    print("\n" + "=" * 60)
    print("📋 诊断结果总结:")
    print(f"  - Server6连接: {'✅ 正常' if server6_ok else '❌ 异常'}")
    print(f"  - F2服务: {'✅ 正常' if f2_ok else '❌ 异常'}")
    print(f"  - 队列处理: {'✅ 正常' if queue_ok else '❌ 异常'}")
    print(f"  - UPDATE操作: {'✅ 正常' if update_ok else '❌ 异常'}")
    print(f"  - Server6 UPDATE API: {'✅ 正常' if api_ok else '❌ 异常'}")
    print(f"  - F2工作线程: {'✅ 正常' if worker_ok else '❌ 异常'}")
    
    if all([server6_ok, f2_ok, queue_ok, update_ok, api_ok, worker_ok]):
        print("\n✅ 所有组件都正常，问题可能在其他地方")
    else:
        print("\n❌ 发现问题，请检查上述异常组件")

if __name__ == "__main__":
    asyncio.run(main()) 