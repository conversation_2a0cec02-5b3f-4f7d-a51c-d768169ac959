#!/usr/bin/env python3
# quick_test.py - Server5快速测试

import asyncio
import sys
from pathlib import Path

# 添加项目路径
sys.path.insert(0, str(Path(__file__).parent))

async def main():
    print("🧪 Server5快速功能测试")
    
    # 测试1: 配置加载
    print("\n1️⃣ 测试配置加载...")
    try:
        from config.config import SERVICE_NAME, SERVICE_VERSION, DEBUG
        print(f"✅ 配置成功: {SERVICE_NAME} v{SERVICE_VERSION}")
    except Exception as e:
        print(f"❌ 配置失败: {e}")
        return False
    
    # 测试2: 数据库客户端
    print("\n2️⃣ 测试数据库客户端...")
    try:
        from app.database import RedisClient, ODBCClient
        print("✅ 数据库客户端导入成功")
        
        # Redis连接测试（本地）
        redis_client = RedisClient()
        if await redis_client.connect():
            print("✅ Redis连接成功")
            await redis_client.set_cache("test", "value", 60)
            value = await redis_client.get_cache("test")
            print(f"✅ Redis操作测试: {value}")
            await redis_client.disconnect()
        else:
            print("⚠️ Redis连接失败（确保Redis服务运行）")
        
        # ODBC模拟测试
        odbc_client = ODBCClient()
        if await odbc_client.connect():
            print("✅ ODBC模拟连接成功")
            status = await odbc_client.get_connection_status()
            print(f"✅ ODBC状态: {status['platform']} - {status['mock_mode']}")
            await odbc_client.disconnect()
        else:
            print("❌ ODBC连接失败")
            
    except Exception as e:
        print(f"❌ 数据库测试失败: {e}")
        return False
    
    # 测试3: f1监听器服务
    print("\n3️⃣ 测试f1监听器服务...")
    try:
        from app.services.f1_listener import ListenerService
        
        service = ListenerService()
        print("✅ f1监听器创建成功")
        
        status = await service.get_status()
        print(f"✅ f1状态: {status['service']} - 运行中: {status['is_running']}")
        
    except Exception as e:
        print(f"❌ f1监听器测试失败: {e}")
        return False
    
    # 测试4: FastAPI应用
    print("\n4️⃣ 测试FastAPI应用...")
    try:
        from app.main import app
        print("✅ FastAPI应用创建成功")
        print(f"✅ 应用标题: {app.title}")
        print(f"✅ 应用版本: {app.version}")
        
    except Exception as e:
        print(f"❌ FastAPI应用测试失败: {e}")
        return False
    
    print("\n🎉 所有测试通过！Server5基础功能正常")
    print("\n📋 测试总结:")
    print("  ✅ 配置系统")
    print("  ✅ 数据库连接（Redis + ODBC模拟）")
    print("  ✅ f1监听器服务")
    print("  ✅ FastAPI应用")
    print("\n🚀 可以尝试启动服务:")
    print("  python start_server5.py")
    
    return True

if __name__ == "__main__":
    success = asyncio.run(main())
    sys.exit(0 if success else 1) 