#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试更新操作脚本
验证用户更新操作会触发队列，系统更新操作不会触发队列
"""

import asyncio
import sys
from pathlib import Path
import logging

# 添加项目路径
sys.path.append(str(Path(__file__).parent))

from app.database import IMDBClient, RedisClient, MongoDBClient
from app.services.f1_listener import ListenerService
from app.services.f2_push_writer_fixed import PushWriterServiceFixed

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

class UpdateOperationsTest:
    """更新操作测试类"""
    
    def __init__(self):
        self.imdb_client = IMDBClient()
        self.redis_client = RedisClient()
        self.mongo_client = MongoDBClient()
        
        # 目标记录
        self.target_entry_id = 10612  # 您指定的记录
        self.target_external_id = 602610  # 您指定的external_id
        
        # 服务实例
        self.f1_service = None
        self.f2_service = None
    
    async def start(self):
        """启动测试环境"""
        logger.info("🚀 启动测试环境...")
        
        # 连接数据库
        await self.imdb_client.connect()
        await self.redis_client.connect()
        await self.mongo_client.connect()
        
        logger.info("✅ 数据库连接成功")
    
    async def stop(self):
        """停止测试环境"""
        logger.info("🔌 停止测试环境...")
        
        if self.f1_service:
            await self.f1_service.stop()
        if self.f2_service:
            await self.f2_service.stop()
        
        await self.imdb_client.disconnect()
        await self.redis_client.disconnect()
        await self.mongo_client.disconnect()
        
        logger.info("✅ 测试环境已停止")
    
    async def cleanup_queue_items(self):
        """清理队列项"""
        logger.info("🧹 清理队列项...")
        
        try:
            # 只删除相关队列项，不删除entries表中的数据
            await self.imdb_client.execute_command(
                "DELETE FROM entries_push_queue WHERE entry_id = $1",
                self.target_entry_id
            )
            
            logger.info("✅ 队列项清理完成")
            
        except Exception as e:
            logger.error(f"❌ 清理队列项失败: {e}")
    
    async def check_queue_status(self, description: str):
        """检查队列状态"""
        logger.info(f"📋 {description} - 检查队列状态...")
        
        # 检查未同步队列项
        unsynced_items = await self.imdb_client.get_queue_items(synced=False, limit=10)
        logger.info(f"  未同步队列项: {len(unsynced_items)} 个")
        
        # 检查已同步队列项
        synced_items = await self.imdb_client.get_queue_items(synced=True, limit=10)
        logger.info(f"  已同步队列项: {len(synced_items)} 个")
        
        # 显示队列项详情
        if unsynced_items:
            logger.info("  未同步队列项详情:")
            for item in unsynced_items:
                logger.info(f"    queue_id={item['queue_id']}, operation={item['operation']}, entry_id={item['entry_id']}")
        
        if synced_items:
            logger.info("  已同步队列项详情:")
            for item in synced_items:
                logger.info(f"    queue_id={item['queue_id']}, operation={item['operation']}, entry_id={item['entry_id']}")
        
        return len(unsynced_items), len(synced_items)
    
    async def get_entry_info(self):
        """获取目标记录信息"""
        entry_info = await self.imdb_client.execute_query(
            "SELECT id, employee_id, entry_date, duration, external_id, source FROM entries WHERE id = $1",
            self.target_entry_id
        )
        
        if entry_info:
            entry = entry_info[0]
            logger.info(f"📊 目标记录状态:")
            logger.info(f"   ID: {entry['id']}")
            logger.info(f"   员工ID: {entry['employee_id']}")
            logger.info(f"   日期: {entry['entry_date']}")
            logger.info(f"   duration: {entry['duration']}")
            logger.info(f"   external_id: {entry['external_id']}")
            logger.info(f"   source: {entry['source']}")
            return entry
        else:
            logger.error(f"❌ 未找到目标记录: entry_id={self.target_entry_id}")
            return None
    
    async def test_user_update_operation(self):
        """测试用户更新操作（source='user'）- 应该启动触发器"""
        logger.info("=" * 60)
        logger.info("🧪 测试1: 用户更新操作（source='user'）")
        logger.info("=" * 60)
        
        # 清理之前的队列项
        await self.cleanup_queue_items()
        
        # 启动f1和f2服务
        logger.info("🚀 启动f1和f2服务...")
        self.f1_service = ListenerService()
        self.f2_service = PushWriterServiceFixed()
        
        await self.f1_service.start()
        await self.f2_service.start()
        logger.info("✅ f1和f2服务启动成功")
        
        # 记录操作前的状态
        await self.get_entry_info()
        unsynced_before, synced_before = await self.check_queue_status("用户更新前")
        
        # 执行用户更新操作（source='user'）
        logger.info("📝 执行用户更新操作（source='user'）- 修改duration为8.00...")
        
        # 使用事务更新，设置source='user'
        async with self.imdb_client.pool.acquire() as conn:
            async with conn.transaction():
                await conn.execute(
                    "UPDATE entries SET duration = $1, source = 'user' WHERE id = $2",
                    8.00, self.target_entry_id
                )
        
        logger.info("✅ 用户更新操作完成")
        
        # 等待触发器执行和队列处理
        logger.info("⏳ 等待触发器执行和队列处理...")
        await asyncio.sleep(8)
        
        # 检查操作后的状态
        await self.get_entry_info()
        unsynced_after, synced_after = await self.check_queue_status("用户更新后")
        
        # 验证结果
        queue_created = (unsynced_after > unsynced_before) or (synced_after > synced_before)
        
        if queue_created:
            logger.info("✅ 验证成功: 用户更新操作正确启动了触发器（队列项数量增加了）")
            return True
        else:
            logger.error("❌ 验证失败: 用户更新操作没有启动触发器（队列项数量未增加）")
            return False
    
    async def test_system_update_operation(self):
        """测试系统更新操作（source='system'）- 不应该启动触发器"""
        logger.info("=" * 60)
        logger.info("🧪 测试2: 系统更新操作（source='system'）")
        logger.info("=" * 60)
        
        # 清理之前的队列项
        await self.cleanup_queue_items()
        
        # 记录操作前的状态
        await self.get_entry_info()
        unsynced_before, synced_before = await self.check_queue_status("系统更新前")
        
        # 执行系统更新操作（source='system'）
        logger.info("📝 执行系统更新操作（source='system'）- 修改duration为7.00...")
        
        # 使用事务更新，设置source='system'
        async with self.imdb_client.pool.acquire() as conn:
            async with conn.transaction():
                await conn.execute(
                    "UPDATE entries SET duration = $1, source = 'system' WHERE id = $2",
                    7.00, self.target_entry_id
                )
        
        logger.info("✅ 系统更新操作完成")
        
        # 等待一段时间让触发器执行
        logger.info("⏳ 等待触发器执行...")
        await asyncio.sleep(3)
        
        # 检查操作后的状态
        await self.get_entry_info()
        unsynced_after, synced_after = await self.check_queue_status("系统更新后")
        
        # 验证结果
        queue_created = (unsynced_after > unsynced_before) or (synced_after > synced_before)
        
        if not queue_created:
            logger.info("✅ 验证成功: 系统更新操作没有启动触发器（队列项数量未增加）")
            return True
        else:
            logger.error("❌ 验证失败: 系统更新操作意外启动了触发器（队列项数量增加了）")
            return False
    
    async def run_all_tests(self):
        """运行所有测试"""
        logger.info("🎯 开始更新操作测试")
        logger.info("=" * 80)
        
        try:
            await self.start()
            
            # 测试1: 用户更新操作
            test1_result = await self.test_user_update_operation()
            
            # 等待一段时间
            await asyncio.sleep(2)
            
            # 测试2: 系统更新操作
            test2_result = await self.test_system_update_operation()
            
            # 输出测试结果
            logger.info("=" * 80)
            logger.info("📊 测试结果总结:")
            logger.info(f"  测试1 (用户更新操作): {'✅ 通过' if test1_result else '❌ 失败'}")
            logger.info(f"  测试2 (系统更新操作): {'✅ 通过' if test2_result else '❌ 失败'}")
            
            if test1_result and test2_result:
                logger.info("🎉 所有测试通过！更新操作触发器工作正常")
            else:
                logger.error("❌ 部分测试失败，需要检查触发器配置")
            
        except Exception as e:
            logger.error(f"❌ 测试过程中发生错误: {e}", exc_info=True)
        finally:
            await self.stop()

async def main():
    """主函数"""
    tester = UpdateOperationsTest()
    await tester.run_all_tests()

if __name__ == "__main__":
    asyncio.run(main()) 