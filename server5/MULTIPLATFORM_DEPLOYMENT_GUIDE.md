# Server5 多平台部署指南

**创建时间**: 2025/06/27 09：14  
**目的**: 修改多平台server5 - 支持Windows和Ubuntu多环境部署

## 📋 概述

Server5数据同步微服务现在支持多平台部署，可以根据不同的运行环境自动选择合适的配置。

### 🎯 支持的部署场景

1. **Windows 10本地运行** - Server5和Server6在同一台机器（************）
2. **Windows 10远程运行** - Server5在不同的Win10机器，连接远程Server6
3. **Ubuntu远程运行** - Server5在Ubuntu机器，连接远程Server6

## 🚀 快速开始

### 方法1: 智能启动（推荐）

```bash
# 自动检测平台并选择配置
python start_multiplatform.py
```

### 方法2: 手动选择平台

```bash
# Windows 10本地运行
python start_win10_local.py

# Windows 10远程运行  
python start_win10_remote.py

# Ubuntu远程运行
python start_ubuntu_remote.py
```

## 📁 文件结构

```
server5/
├── config/
│   ├── config.py                 # 默认配置
│   ├── config_win10_local.py     # Windows本地配置
│   ├── config_win10_remote.py    # Windows远程配置
│   └── config_ubuntu_remote.py   # Ubuntu远程配置
├── app/
│   └── config_selector.py        # 配置选择器
├── start_multiplatform.py        # 智能启动器
├── start_win10_local.py          # Windows本地启动
├── start_win10_remote.py         # Windows远程启动
├── start_ubuntu_remote.py        # Ubuntu远程启动
├── network_diagnostic.py         # 网络诊断工具
└── MULTIPLATFORM_DEPLOYMENT_GUIDE.md
```

## 🔧 配置说明

### Windows 10本地配置 (config_win10_local.py)

- **适用场景**: Server5和Server6在同一台机器
- **数据库**: 本地连接 (localhost)
- **Server6**: 本地连接 (localhost:8009)
- **MDB**: 直接ODBC访问
- **性能**: 高性能，短超时

### Windows 10远程配置 (config_win10_remote.py)

- **适用场景**: Server5在不同的Windows机器
- **数据库**: 远程连接 (************)
- **Server6**: 远程连接 (************:8009)
- **MDB**: 通过Server6网关访问
- **性能**: 适中性能，长超时

### Ubuntu远程配置 (config_ubuntu_remote.py)

- **适用场景**: Server5在Ubuntu开发机器
- **数据库**: 远程连接 (************)
- **Server6**: 远程连接 (************:8009)
- **MDB**: 通过Server6网关访问（模拟模式）
- **性能**: 开发模式，详细日志

## 🌐 网络要求

### 端口要求

| 服务 | 端口 | 说明 |
|------|------|------|
| Server5 | 8009 | HTTP API端口 |
| Server6 | 8009 | MDB网关端口 |
| PostgreSQL | 5432 | 数据库端口 |
| MongoDB | 27017 | 文档数据库端口 |
| Redis | 6379 | 缓存数据库端口 |

### 防火墙配置

**Windows防火墙**:
```cmd
# 允许Server6端口
netsh advfirewall firewall add rule name="Server6" dir=in action=allow protocol=TCP localport=8009

# 允许PostgreSQL端口
netsh advfirewall firewall add rule name="PostgreSQL" dir=in action=allow protocol=TCP localport=5432
```

**Ubuntu防火墙**:
```bash
# 允许出站连接到Server6
sudo ufw allow out 8009
sudo ufw allow out 5432
sudo ufw allow out 27017
```

## 🔍 故障排除

### 1. 使用网络诊断工具

```bash
python network_diagnostic.py
```

这个工具会检查：
- 基础网络连接
- 端口连通性
- HTTP服务状态
- 数据库连接
- 防火墙设置

### 2. 常见问题

#### Server6连接超时

**症状**: `Connection timeout to host http://************:8009/health`

**解决方案**:
1. 确保Server6正在运行
2. 检查Windows防火墙设置
3. 验证网络连接
4. 使用`ping ************`测试连通性

#### Redis连接失败

**症状**: `Redis连接: 失败`

**解决方案**:
```bash
# Ubuntu
sudo systemctl start redis-server
sudo systemctl enable redis-server

# Windows
# 下载并安装Redis for Windows
```

#### PostgreSQL连接失败

**症状**: `PostgreSQL连接: 失败`

**解决方案**:
1. 检查PostgreSQL服务状态
2. 验证用户名密码
3. 检查pg_hba.conf配置
4. 确保监听外部连接

## 🚀 部署步骤

### Ubuntu部署

1. **安装依赖**:
```bash
pip install -r requirements.txt
```

2. **启动Redis**:
```bash
sudo systemctl start redis-server
```

3. **运行诊断**:
```bash
python network_diagnostic.py
```

4. **启动服务**:
```bash
python start_ubuntu_remote.py
```

### Windows部署

1. **安装依赖**:
```cmd
pip install -r requirements.txt
```

2. **启动Redis** (如果需要):
```cmd
redis-server.exe
```

3. **运行诊断**:
```cmd
python network_diagnostic.py
```

4. **启动服务**:
```cmd
# 本地部署
python start_win10_local.py

# 远程部署
python start_win10_remote.py
```

## 📊 测试验证

### 1. 健康检查

```bash
curl http://localhost:8009/health
```

### 2. 集成测试

```bash
python test_server6_integration.py
```

### 3. 客户端测试

```bash
python test_client_integration.py
```

## 🔧 环境变量

可以通过环境变量强制使用特定配置：

```bash
# 强制使用特定配置
export SERVER5_CONFIG=config_ubuntu_remote
python start_server5.py

# 强制本地配置（即使IP不匹配）
export FORCE_LOCAL_CONFIG=true
python start_multiplatform.py
```

## 📝 日志文件

不同配置的日志文件：

```
logs/
├── server5_win10_local.log      # Windows本地日志
├── server5_win10_remote.log     # Windows远程日志
├── server5_ubuntu_remote.log    # Ubuntu远程日志
└── network_diagnostic.log       # 网络诊断日志
```

## 🎯 最佳实践

1. **开发环境**: 使用Ubuntu远程配置，启用详细日志
2. **测试环境**: 使用Windows远程配置，适中性能
3. **生产环境**: 使用Windows本地配置，最佳性能
4. **故障排除**: 先运行网络诊断工具
5. **监控**: 定期检查日志文件

## 📞 支持

如果遇到问题：

1. 运行 `python network_diagnostic.py` 进行诊断
2. 检查对应的日志文件
3. 验证网络连接和防火墙设置
4. 确保所有服务正在运行

---

**注意**: 此部署指南是2025/06/27的多平台server5修改的一部分，确保Server6在************:8009正常运行。 