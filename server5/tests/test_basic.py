# server5/tests/test_basic.py
# Server5基础测试

import asyncio
import sys
from pathlib import Path

# 添加项目路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

import pytest
import logging

# 配置测试日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class TestDatabaseConnections:
    """数据库连接测试"""
    
    @pytest.mark.asyncio
    async def test_postgresql_connection(self):
        """测试PostgreSQL连接"""
        try:
            from app.database.postgresql_client import IMDBClient
            
            client = IMDBClient()
            connected = await client.connect()
            
            assert connected, "PostgreSQL连接失败"
            
            # 测试基本查询
            status = await client.get_connection_status()
            assert status["status"] == "connected"
            
            await client.disconnect()
            logger.info("✅ PostgreSQL连接测试通过")
            
        except ImportError as e:
            pytest.skip(f"跳过PostgreSQL测试: {e}")
        except Exception as e:
            pytest.fail(f"PostgreSQL连接测试失败: {e}")
    
    @pytest.mark.asyncio
    async def test_redis_connection(self):
        """测试Redis连接"""
        try:
            from app.database.redis_client import RedisClient
            
            client = RedisClient()
            connected = await client.connect()
            
            assert connected, "Redis连接失败"
            
            # 测试基本操作
            await client.set_cache("test_key", "test_value", expire=60)
            value = await client.get_cache("test_key")
            assert value == "test_value"
            
            await client.delete_cache("test_key")
            await client.disconnect()
            
            logger.info("✅ Redis连接测试通过")
            
        except ImportError as e:
            pytest.skip(f"跳过Redis测试: {e}")
        except Exception as e:
            pytest.fail(f"Redis连接测试失败: {e}")
    
    @pytest.mark.asyncio
    async def test_mongodb_connection(self):
        """测试MongoDB连接"""
        try:
            from app.database.mongodb_client import MongoDBClient
            
            client = MongoDBClient()
            connected = await client.connect()
            
            assert connected, "MongoDB连接失败"
            
            # 测试健康检查
            health = await client.health_check()
            assert health["status"] == "healthy"
            
            await client.disconnect()
            logger.info("✅ MongoDB连接测试通过")
            
        except ImportError as e:
            pytest.skip(f"跳过MongoDB测试: {e}")
        except Exception as e:
            logger.warning(f"MongoDB连接测试失败(可能是配置问题): {e}")
            # MongoDB可能在远程，允许失败
    
    @pytest.mark.asyncio
    async def test_odbc_connection(self):
        """测试ODBC连接（跨平台）"""
        try:
            from app.database.odbc_client import ODBCClient
            
            client = ODBCClient()
            connected = await client.connect()
            
            # 在Ubuntu环境下应该使用模拟连接
            assert connected, "ODBC连接失败"
            
            # 测试连接状态
            status = await client.get_connection_status()
            assert status["connected"] == True
            
            await client.disconnect()
            logger.info("✅ ODBC连接测试通过")
            
        except ImportError as e:
            pytest.skip(f"跳过ODBC测试: {e}")
        except Exception as e:
            pytest.fail(f"ODBC连接测试失败: {e}")

class TestServices:
    """服务功能测试"""
    
    @pytest.mark.asyncio
    async def test_f1_listener_service(self):
        """测试f1监听器服务"""
        try:
            from app.services.f1_listener import ListenerService
            
            service = ListenerService()
            
            # 测试服务初始化
            assert service is not None
            assert not service.is_running
            
            # 测试获取状态（服务未启动时）
            status = await service.get_status()
            assert status["service"] == "f1_listener"
            assert not status["is_running"]
            
            logger.info("✅ f1监听器服务基础测试通过")
            
        except ImportError as e:
            pytest.skip(f"跳过f1服务测试: {e}")
        except Exception as e:
            pytest.fail(f"f1服务测试失败: {e}")

def run_manual_test():
    """手动运行测试（不依赖pytest）"""
    async def _run_tests():
        logger.info("🧪 开始Server5基础测试")
        
        # 测试配置加载
        try:
            from config.config import SERVICE_NAME, SERVICE_VERSION, DEBUG
            logger.info(f"📋 配置加载成功: {SERVICE_NAME} v{SERVICE_VERSION} (DEBUG: {DEBUG})")
        except Exception as e:
            logger.error(f"❌ 配置加载失败: {e}")
            return False
        
        # 测试数据库客户端导入
        try:
            from app.database import PostgreSQLClient, IMDBClient, RedisClient, MongoDBClient, ODBCClient
            logger.info("✅ 数据库客户端导入成功")
        except Exception as e:
            logger.error(f"❌ 数据库客户端导入失败: {e}")
            return False
        
        # 测试Redis连接（Ubuntu本地）
        try:
            redis_client = RedisClient()
            redis_connected = await redis_client.connect()
            if redis_connected:
                logger.info("✅ Redis连接测试成功")
                await redis_client.disconnect()
            else:
                logger.warning("⚠️ Redis连接失败（请确保Redis服务运行）")
        except Exception as e:
            logger.warning(f"⚠️ Redis测试异常: {e}")
        
        # 测试PostgreSQL连接（远程）
        try:
            pg_client = IMDBClient()
            pg_connected = await pg_client.connect()
            if pg_connected:
                logger.info("✅ PostgreSQL连接测试成功")
                
                # 测试分区创建
                current_month = "202412"  # 测试月份
                await pg_client.create_partition_if_needed(current_month)
                logger.info(f"✅ 分区测试成功: {current_month}")
                
                await pg_client.disconnect()
            else:
                logger.warning("⚠️ PostgreSQL连接失败（请检查远程数据库配置）")
        except Exception as e:
            logger.warning(f"⚠️ PostgreSQL测试异常: {e}")
        
        # 测试ODBC连接（模拟）
        try:
            odbc_client = ODBCClient()
            odbc_connected = await odbc_client.connect()
            if odbc_connected:
                logger.info("✅ ODBC模拟连接测试成功")
                await odbc_client.disconnect()
            else:
                logger.warning("⚠️ ODBC连接失败")
        except Exception as e:
            logger.warning(f"⚠️ ODBC测试异常: {e}")
        
        logger.info("🎉 Server5基础测试完成")
        return True
    
    return asyncio.run(_run_tests())

if __name__ == "__main__":
    # 运行手动测试
    success = run_manual_test()
    sys.exit(0 if success else 1) 