# Server5 Windows部署指南

## 🎯 概览

Server5是一个MDB-PostgreSQL双向同步微服务，提供了多种Windows启动方式以适应不同的环境和需求。

## 📋 启动选项对比

| 启动脚本 | 复杂度 | 功能 | 数据库要求 | 推荐场景 |
|---------|--------|------|-----------|----------|
| `start_windows_minimal.py` | ⭐ 最简 | 基础API + MDB测试 | 无 | 首次测试、验证环境 |
| `start_windows_simple.py` | ⭐⭐ 简单 | 基础API + 容错模式 | 可选 | 入门使用、开发测试 |
| `start_windows_quiet.py` | ⭐⭐⭐ 中等 | 完整f1-f6服务 | 必需 | 日常使用 |
| `start_windows_ultra_quiet.py` | ⭐⭐⭐⭐ 复杂 | 生产级安静模式 | 必需 | 生产环境 |

## 🚀 推荐启动流程

### 1. 首次部署 (推荐)
```bash
# 步骤1: 验证基础环境
python start_windows_minimal.py
```
- ✅ 验证Python环境
- ✅ 测试基础API
- ✅ 检查MDB连接
- ✅ 无数据库依赖

### 2. 开发测试
```bash
# 步骤2: 开发和测试
python start_windows_simple.py
```
- ✅ 容错性强，数据库连接失败也能运行
- ✅ 支持模拟模式
- ✅ Windows编码友好
- ✅ 适合调试

### 3. 生产部署
```bash
# 步骤3: 生产环境 (需要完整数据库)
python start_windows_quiet.py
```
- ✅ 完整f1-f6服务
- ✅ 减少日志输出
- ✅ 适合长期运行

## 🔧 配置文件说明

### config_windows_minimal.py
- **位置**: `server5/config/config_windows_minimal.py`
- **特点**: 最大容错性，即使数据库连接失败也能启动
- **适用**: 首次部署、环境验证

### config_windows_local.py
- **位置**: `server5/config/config_windows_local.py`
- **特点**: 本地连接配置 (127.0.0.1)
- **适用**: Windows本地部署

### config.py
- **位置**: `server5/config/config.py`
- **特点**: 远程连接配置 (192.168.3.93)
- **适用**: 跨网络部署

## 🛠️ 故障排除

### 问题1: 编码错误 (UnicodeEncodeError)
**症状**: `'cp932' codec can't encode character '\u274c'`
**原因**: Windows控制台不支持emoji符号
**解决**: 
- 使用 `start_windows_simple.py` (已移除emoji)
- 所有Windows启动脚本已修复编码问题
- 自动设置UTF-8编码支持

### 问题2: Redis连接失败
**症状**: `Error 22 connecting to 127.0.0.1:6379`
**原因**: Redis服务未启动
**解决**: 
1. 使用 `start_windows_minimal.py` (无Redis依赖)
2. 使用 `start_windows_simple.py` (容错模式)
3. 或启动Redis服务
4. `start_windows_quiet.py` 和 `start_windows_ultra_quiet.py` 现已使用简化应用

### 问题3: MDB文件被占用
**症状**: `既にこのデータベースは開いています`
**原因**: Access文件已被其他程序打开
**解决**: 
1. 关闭所有Access程序
2. 脚本会自动尝试只读模式
3. 失败时自动切换到模拟模式

### 问题4: PostgreSQL连接失败
**症状**: 连接超时或拒绝连接
**原因**: PostgreSQL服务未启动或配置错误
**解决**: 
1. 使用 `start_windows_minimal.py` (最小依赖)
2. 检查PostgreSQL服务状态
3. 验证连接配置

## 📊 服务监控

### 健康检查
```bash
# 检查服务状态
curl http://localhost:8009/health

# 检查详细状态
curl http://localhost:8009/status
```

### API文档
访问: http://localhost:8009/docs

### 日志文件
- 位置: `server5/logs/`
- 主日志: `server5.log`
- 安静模式: `server5_quiet.log`

## 🔍 测试验证

### 配置测试
```bash
# 测试最小配置
python test_config_minimal.py

# 测试Windows简单功能
python test_windows_simple.py
```

### 服务测试
```bash
# 启动服务后测试
curl http://localhost:8009/
curl http://localhost:8009/health
curl http://localhost:8009/docs
```

## 💡 最佳实践

### 1. 环境准备
- Python 3.12 + Conda环境
- 安装依赖: `pip install -r requirements.txt`
- Windows: 安装 `pip install pywin32`

### 2. 首次部署
1. 先运行 `start_windows_minimal.py` 验证基础环境
2. 再运行 `start_windows_simple.py` 测试完整功能
3. 最后运行生产模式

### 3. 生产部署
- 使用 `start_windows_quiet.py` 或 `start_windows_ultra_quiet.py`
- 确保所有数据库服务正常运行
- 定期检查日志文件

### 4. 故障恢复
- 服务异常时，先尝试简单模式启动
- 检查日志文件定位问题
- 使用最小模式验证基础环境

## 📞 技术支持

如果遇到问题：
1. 查看日志文件 `logs/server5.log`
2. 运行配置测试脚本
3. 尝试不同的启动模式
4. 检查数据库服务状态

---

**版本**: Server5 v1.0.0  
**更新**: 2025/06/26  
**平台**: Windows 10+ 