#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试f2_push_writer中插入和更新操作的数据处理差异
验证category和item字段为0时的处理逻辑
"""

import asyncio
import sys
from pathlib import Path
sys.path.append(str(Path(__file__).parent))

from app.database import IMDBClient
from app.services.f2_push_writer import PushWriterServiceFixed

async def test_f2_data_processing():
    """测试f2_push_writer中的数据处理逻辑"""
    
    print("🧪 测试f2_push_writer中的数据处理逻辑")
    print("=" * 50)
    
    # 初始化客户端和服务
    imdb_client = IMDBClient()
    f2_service = PushWriterServiceFixed()
    
    try:
        # 1. 连接数据库
        print("📡 步骤1: 连接数据库...")
        await imdb_client.connect()
        await f2_service.start()
        print("✅ 数据库连接成功")
        
        # 2. 模拟PostgreSQL中的entry_data（category=0, item=0）
        print("\n📝 步骤2: 模拟PostgreSQL中的entry_data...")
        
        # 模拟从PostgreSQL读取的数据（整数类型）
        entry_data = {
            'id': 99999,
            'employee_id': '215829',
            'entry_date': '2025-07-15',
            'model': '',
            'number': '',
            'factory_number': '',
            'project_number': 'TEST001',
            'unit_number': '',
            'category': 0,  # PostgreSQL中存储为整数0
            'item': 0,      # PostgreSQL中存储为整数0
            'duration': 0.5,
            'department': '131',
            'source': 'user',
            'external_id': None,
            'ts': '2025-07-15 10:00:00'
        }
        
        print(f"📊 模拟的entry_data:")
        print(f"   category: {entry_data['category']} (类型: {type(entry_data['category'])})")
        print(f"   item: {entry_data['item']} (类型: {type(entry_data['item'])})")
        
        # 3. 模拟f2_push_writer中的INSERT数据处理逻辑
        print("\n🔄 步骤3: 模拟INSERT操作的数据处理...")
        
        # 复制f2_push_writer中的INSERT数据处理逻辑
        mdb_data_insert = {
            'employee_id': entry_data.get('employee_id'),
            'entry_date': '2025/07/15',  # 简化日期处理
            'model': entry_data.get('model') if entry_data.get('model') and entry_data.get('model').strip() else None,
            'number': entry_data.get('number') if entry_data.get('number') and entry_data.get('number').strip() else None,
            'factory_number': entry_data.get('factory_number') if entry_data.get('factory_number') and entry_data.get('factory_number').strip() else None,
            'project_number': entry_data.get('project_number') if entry_data.get('project_number') and entry_data.get('project_number').strip() else None,
            'unit_number': entry_data.get('unit_number') if entry_data.get('unit_number') and entry_data.get('unit_number').strip() else None,
            'category': str(entry_data.get('category')) if entry_data.get('category') is not None else None,  # 整数转字符串
            'item': str(entry_data.get('item')) if entry_data.get('item') is not None else None,  # 整数转字符串
            'duration': float(entry_data.get('duration', 0.0)),  # 将Decimal转换为float
            'department': entry_data.get('department') if entry_data.get('department') and entry_data.get('department').strip() else None
        }
        
        print(f"📤 INSERT操作准备发送给Server6的数据:")
        print(f"   category: {mdb_data_insert['category']} (类型: {type(mdb_data_insert['category'])})")
        print(f"   item: {mdb_data_insert['item']} (类型: {type(mdb_data_insert['item'])})")
        
        # 4. 模拟f2_push_writer中的UPDATE数据处理逻辑
        print("\n🔄 步骤4: 模拟UPDATE操作的数据处理...")
        
        # 复制f2_push_writer中的UPDATE数据处理逻辑（与INSERT相同）
        mdb_data_update = {
            'employee_id': entry_data.get('employee_id'),
            'entry_date': '2025/07/15',  # 简化日期处理
            'model': entry_data.get('model') if entry_data.get('model') and entry_data.get('model').strip() else None,
            'number': entry_data.get('number') if entry_data.get('number') and entry_data.get('number').strip() else None,
            'factory_number': entry_data.get('factory_number') if entry_data.get('factory_number') and entry_data.get('factory_number').strip() else None,
            'project_number': entry_data.get('project_number') if entry_data.get('project_number') and entry_data.get('project_number').strip() else None,
            'unit_number': entry_data.get('unit_number') if entry_data.get('unit_number') and entry_data.get('unit_number').strip() else None,
            'category': str(entry_data.get('category')) if entry_data.get('category') is not None else None,  # 整数转字符串
            'item': str(entry_data.get('item')) if entry_data.get('item') is not None else None,  # 整数转字符串
            'duration': float(entry_data.get('duration', 0.0)),  # 将Decimal转换为float
            'department': entry_data.get('department') if entry_data.get('department') and entry_data.get('department').strip() else None
        }
        
        print(f"📤 UPDATE操作准备发送给Server6的数据:")
        print(f"   category: {mdb_data_update['category']} (类型: {type(mdb_data_update['category'])})")
        print(f"   item: {mdb_data_update['item']} (类型: {type(mdb_data_update['item'])})")
        
        # 5. 对比结果
        print("\n🔍 步骤5: 对比结果...")
        print(f"INSERT和UPDATE的数据处理逻辑是否相同: {mdb_data_insert == mdb_data_update}")
        print(f"INSERT category: {mdb_data_insert['category']}")
        print(f"UPDATE category: {mdb_data_update['category']}")
        print(f"INSERT item: {mdb_data_insert['item']}")
        print(f"UPDATE item: {mdb_data_update['item']}")
        
        # 6. 测试server6_client的处理逻辑
        print("\n🔄 步骤6: 测试server6_client的处理逻辑...")
        
        from app.utils.server6_client import Server6Client
        server6_client = Server6Client()
        
        # 模拟server6_client.insert_entry的处理逻辑
        field_mapping = {
            'employee_id': '従業員ｺｰﾄﾞ',
            'entry_date': '日付',
            'model': '機種',
            'number': '号機',
            'factory_number': '工場製番',
            'project_number': '工事番号',
            'unit_number': 'ﾕﾆｯﾄ番号',
            'category': '区分',
            'item': '項目',
            'duration': '時間',
            'department': '所属ｺｰﾄﾞ'
        }
        
        serializable_data = {}
        for key, value in mdb_data_insert.items():
            # 映射字段名
            japanese_key = field_mapping.get(key, key)
            
            # 处理空值：发送None给Server6，让Server6处理NULL转换
            if value is None:
                serializable_data[japanese_key] = None
                print(f"  📤 字段 {key} 设置为 None")
                continue
            
            # 处理空字符串：发送None给Server6，让Server6处理NULL转换
            if isinstance(value, str) and value.strip() == '':
                serializable_data[japanese_key] = None
                print(f"  📤 字段 {key} (空字符串) 设置为 None")
                continue
            
            # 特殊处理：确保category和item字段发送正确的值
            if key in ['category', 'item']:
                # 确保值不为空字符串或None
                if value == "" or value is None:
                    final_value = "0"
                    print(f"  📤 字段 {key} (空值) 设置为默认值 '0'")
                else:
                    final_value = str(value)  # 确保是字符串
                    print(f"  📤 字段 {key} 设置为 '{value}'")
                
                # 只设置日文字段名，避免Server6的or逻辑问题
                serializable_data[japanese_key] = final_value  # 日文字段名
                continue
            
            # 其他字段直接设置
            serializable_data[japanese_key] = value
        
        print(f"📤 最终发送给Server6的数据:")
        print(f"   区分: {serializable_data.get('区分')} (类型: {type(serializable_data.get('区分'))})")
        print(f"   項目: {serializable_data.get('項目')} (类型: {type(serializable_data.get('項目'))})")
        
        print("\n✅ 测试完成！")
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
    finally:
        await f2_service.stop()
        await imdb_client.disconnect()

if __name__ == "__main__":
    asyncio.run(test_f2_data_processing()) 