#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试客户端网关修复
验证按钮4调用Server5客户端网关API是否正常工作
"""

import asyncio
import sys
from pathlib import Path
import logging
import aiohttp
import json
from datetime import datetime

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

class ClientGatewayTest:
    """客户端网关测试类"""
    
    def __init__(self):
        self.base_url = "http://localhost:8009"
        
        # 模拟客户端程序1的按钮4输入数据
        self.client_input_data = {
            "entry_date": "2025/07/15",
            "employee_id": "215829",
            "duration": 0.5,
            "model": "TEST_MODEL",
            "number": "TEST001",
            "factory_number": "FACTORY001",
            "project_number": "PROJECT001",
            "unit_number": "UNIT001",
            "category": "1",
            "item": "1",
            "department": "131"
        }
    
    async def test_client_gateway(self):
        """测试客户端网关API"""
        try:
            logger.info("🔍 测试客户端网关API...")
            
            async with aiohttp.ClientSession() as session:
                url = f"{self.base_url}/client/entries/create"
                
                logger.info(f"📤 发送数据到: {url}")
                logger.info(f"📦 数据: {json.dumps(self.client_input_data, indent=2, ensure_ascii=False)}")
                
                async with session.post(url, json=self.client_input_data) as response:
                    if response.status == 200:
                        result = await response.json()
                        logger.info(f"✅ 客户端网关API调用成功: {json.dumps(result, indent=2, ensure_ascii=False)}")
                        return result
                    else:
                        error_text = await response.text()
                        logger.error(f"❌ 客户端网关API调用失败: {response.status} - {error_text}")
                        return None
                        
        except Exception as e:
            logger.error(f"❌ 客户端网关API测试失败: {e}")
            return None
    
    async def test_health_check(self):
        """测试健康检查"""
        try:
            logger.info("🔍 测试健康检查...")
            
            async with aiohttp.ClientSession() as session:
                url = f"{self.base_url}/client/entries/health"
                
                async with session.get(url) as response:
                    if response.status == 200:
                        result = await response.json()
                        logger.info(f"✅ 健康检查成功: {result}")
                        return True
                    else:
                        logger.error(f"❌ 健康检查失败: {response.status}")
                        return False
                        
        except Exception as e:
            logger.error(f"❌ 健康检查测试失败: {e}")
            return False
    
    async def run_test(self):
        """运行完整测试"""
        logger.info("🧪 开始客户端网关修复测试")
        logger.info("=" * 60)
        
        try:
            # 1. 测试健康检查
            health_ok = await self.test_health_check()
            if not health_ok:
                logger.error("❌ 健康检查失败，停止测试")
                return
            
            # 2. 测试客户端网关API
            gateway_result = await self.test_client_gateway()
            if not gateway_result or not gateway_result.get("success"):
                logger.error("❌ 客户端网关API测试失败")
                return
            
            entry_id = gateway_result.get("entry_id")
            logger.info(f"✅ 客户端网关API测试成功，entry_id={entry_id}")
            
            logger.info("✅ 客户端网关修复测试完成")
            
        except Exception as e:
            logger.error(f"❌ 测试失败: {e}")
            raise

async def main():
    """主函数"""
    test = ClientGatewayTest()
    await test.run_test()

if __name__ == "__main__":
    asyncio.run(main()) 