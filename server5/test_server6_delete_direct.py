#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
直接测试Server6删除操作
"""

import asyncio
import aiohttp
import json
import sys
from pathlib import Path
import logging

# 添加项目根目录到Python路径
sys.path.append(str(Path(__file__).parent.parent))

from app.utils.server6_client import Server6Client
from config.config import SERVER6_CONFIG

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

async def check_entry_exists(session, external_id):
    """检查记录是否存在并显示详细信息"""
    try:
        url = f"{SERVER6_CONFIG['base_url']}/api/entries/{external_id}"
        async with session.get(url) as response:
            if response.status == 200:
                data = await response.json()
                print(f"✅ 找到记录 external_id={external_id}:")
                print(f"   - 员工ID: {data.get('employee_id')}")
                print(f"   - 日期: {data.get('entry_date')}")
                print(f"   - 模型: {data.get('model')}")
                print(f"   - 编号: {data.get('number')}")
                print(f"   - 部门: {data.get('department')}")
                print(f"   - 时长: {data.get('duration')}")
                return True, data
            elif response.status == 404:
                print(f"❌ 记录不存在: external_id={external_id}")
                return False, None
            else:
                print(f"⚠️ 查询失败: status={response.status}")
                return False, None
    except Exception as e:
        print(f"❌ 查询记录时出错: {e}")
        return False, None

async def delete_entry(session, external_id):
    """删除记录"""
    try:
        url = f"{SERVER6_CONFIG['base_url']}/api/entries/{external_id}"
        async with session.delete(url) as response:
            if response.status == 200:
                data = await response.json()
                print(f"✅ 删除成功: external_id={external_id}")
                print(f"   响应: {data}")
                return True
            else:
                error_text = await response.text()
                print(f"❌ 删除失败: status={response.status}, error={error_text}")
                return False
    except Exception as e:
        print(f"❌ 删除记录时出错: {e}")
        return False

class Server6DeleteTest:
    """Server6删除操作测试类"""
    
    def __init__(self):
        self.server6_client = Server6Client()
        
    async def setup(self):
        """初始化连接"""
        try:
            await self.server6_client.connect()
            logger.info("✅ Server6连接成功")
            return True
        except Exception as e:
            logger.error(f"❌ Server6连接失败: {e}")
            return False
    
    async def cleanup(self):
        """清理连接"""
        await self.server6_client.disconnect()
    
    async def test_server6_status(self):
        """测试Server6状态"""
        try:
            response = await self.server6_client.get_status()
            logger.info(f"📊 Server6状态: {response}")
            return response.get('success', False)
        except Exception as e:
            logger.error(f"❌ 获取Server6状态失败: {e}")
            return False
    
    async def test_get_entry(self, external_id):
        """测试获取记录"""
        try:
            response = await self.server6_client.get_entry(external_id)
            logger.info(f"📋 获取记录 {external_id}: {response}")
            return response.get('success', False)
        except Exception as e:
            logger.error(f"❌ 获取记录失败: {e}")
            return False
    
    async def test_delete_entry(self, external_id):
        """测试删除记录"""
        try:
            logger.info(f"🗑️ 删除记录: external_id={external_id}")
            response = await self.server6_client.delete_entry(external_id)
            logger.info(f"📋 删除响应: {response}")
            return response.get('success', False)
        except Exception as e:
            logger.error(f"❌ 删除记录失败: {e}")
            return False
    
    async def test_complete_delete_flow(self, external_id):
        """测试完整的删除流程"""
        logger.info(f"🧪 测试完整删除流程: external_id={external_id}")
        
        # 1. 检查Server6状态
        if not await self.test_server6_status():
            logger.error("❌ Server6状态检查失败")
            return False
        
        # 2. 检查记录是否存在
        exists = await self.test_get_entry(external_id)
        if not exists:
            logger.warning(f"⚠️ 记录不存在: external_id={external_id}")
            return True  # 记录不存在也算成功
        
        # 3. 执行删除
        success = await self.test_delete_entry(external_id)
        if success:
            logger.info(f"✅ 删除成功: external_id={external_id}")
            
            # 4. 验证删除
            still_exists = await self.test_get_entry(external_id)
            if not still_exists:
                logger.info(f"✅ 删除验证成功: external_id={external_id}")
                return True
            else:
                logger.error(f"❌ 删除验证失败: external_id={external_id} 仍然存在")
                return False
        else:
            logger.error(f"❌ 删除失败: external_id={external_id}")
            return False
    
    async def run_tests(self):
        """运行所有测试"""
        try:
            if not await self.setup():
                return
            
            # 测试单个external_id
            test_ids = [603668]
            
            for external_id in test_ids:
                logger.info(f"\n{'='*50}")
                logger.info(f"测试 external_id: {external_id}")
                logger.info(f"{'='*50}")
                
                success = await self.test_complete_delete_flow(external_id)
                if success:
                    logger.info(f"✅ 测试通过: external_id={external_id}")
                else:
                    logger.error(f"❌ 测试失败: external_id={external_id}")
                
                await asyncio.sleep(2)  # 等待2秒再测试下一个
            
        except Exception as e:
            logger.error(f"❌ 测试失败: {e}")
        finally:
            await self.cleanup()

async def main():
    """主函数"""
    print("🔍 Server6 删除API 安全测试")
    print("=" * 50)
    
    # 测试单个external_id
    test_ids = [603668]
    
    async with aiohttp.ClientSession() as session:
        for external_id in test_ids:
            print(f"\n📋 检查记录: external_id={external_id}")
            print("-" * 30)
            
            # 首先检查记录是否存在
            exists, entry_data = await check_entry_exists(session, external_id)
            
            if not exists:
                print(f"⚠️ 跳过删除测试: external_id={external_id} 不存在")
                continue
            
            # 显示记录详情并请求确认
            print(f"\n⚠️ 即将删除以下记录:")
            print(f"   external_id: {external_id}")
            print(f"   员工ID: {entry_data.get('employee_id')}")
            print(f"   日期: {entry_data.get('entry_date')}")
            print(f"   模型: {entry_data.get('model')}")
            print(f"   部门: {entry_data.get('department')}")
            
            # 请求用户确认
            confirm = input(f"\n❓ 确认要删除这条记录吗? (输入 'YES' 确认删除): ")
            
            if confirm.strip().upper() == 'YES':
                print(f"\n🗑️ 执行删除操作: external_id={external_id}")
                success = await delete_entry(session, external_id)
                
                if success:
                    print(f"✅ 删除操作完成: external_id={external_id}")
                else:
                    print(f"❌ 删除操作失败: external_id={external_id}")
            else:
                print(f"⏭️ 跳过删除: external_id={external_id}")
    
    print("\n" + "=" * 50)
    print("🏁 测试完成")

if __name__ == "__main__":
    asyncio.run(main()) 