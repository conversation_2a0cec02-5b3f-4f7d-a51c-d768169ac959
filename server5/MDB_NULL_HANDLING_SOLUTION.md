# MDB NULL值处理解决方案

## 🎯 问题描述

Server6需要生成正确的MDB SQL语句，其中没有值的字段必须传入`NULL`（四个大写字母），而不是Python的`None`。例如：

```sql
INSERT INTO [元作業時間]
              (従業員ｺｰﾄﾞ, 日付, 機種, 号機,
               工場製番, 工事番号, ﾕﾆｯﾄ番号,
               区分, 項目, 時間, 所属ｺｰﾄﾞ)
             VALUES
              ('215829',
               #2025/07/15#,
               '1',
               '1',
               NULL,
               NULL,
               NULL,
               '1',
               '1',
               2.0,
               '131'
              )
```

## 📋 数据流分析

### 完整数据流
```
UI输入 → Server5 HTTP API → PostgreSQL entries → 触发器 → entries_push_queue → f1监听 → f2推送 → Server6 → MDB数据库
```

### 关键组件
- **entries表**: PostgreSQL主表，存储所有记录
- **entries_push_queue表**: 队列表，存储待同步任务
- **触发器**: 自动将用户操作加入队列
- **f2推送服务**: 处理队列任务，调用Server6
- **Server6**: MDB网关，执行实际的MDB操作

## 🔧 解决方案实现

### 1. 修改f2推送服务

**文件**: `server5/app/services/f2_push_writer.py`

**关键修改**:
- 正确处理空值，将空字符串和None转换为None
- 让Server6客户端跳过空字段，MDB使用NULL
- 确保必需字段有默认值

**INSERT操作数据准备**:
```python
# 准备MDB插入数据 - 使用从entries表中读取的实际数据
# 关键修复：正确处理空值，让Server6客户端跳过空字段，MDB使用NULL
mdb_data = {
    'employee_id': entry_data['employee_id'],
    'entry_date': entry_data['entry_date'].strftime('%Y/%m/%d') if entry_data['entry_date'] else datetime.now().strftime('%Y/%m/%d'),
    'model': entry_data['model'] if entry_data['model'] and entry_data['model'].strip() else None,
    'number': entry_data['number'] if entry_data['number'] and entry_data['number'].strip() else None,
    'factory_number': entry_data['factory_number'] if entry_data['factory_number'] and entry_data['factory_number'].strip() else None,
    'project_number': entry_data['project_number'] if entry_data['project_number'] and entry_data['project_number'].strip() else None,
    'unit_number': entry_data['unit_number'] if entry_data['unit_number'] and entry_data['unit_number'].strip() else None,
    'category': entry_data['category'] if entry_data['category'] is not None else 1,
    'item': entry_data['item'] if entry_data['item'] is not None else 1,
    'duration': float(entry_data['duration']) if entry_data['duration'] is not None else 0.0,
    'department': entry_data['department'] if entry_data['department'] and entry_data['department'].strip() else None
}
```

**UPDATE操作数据准备**:
```python
# 准备MDB更新数据，确保数据类型正确 - 使用从entries表中读取的实际数据
# 关键修复：正确处理空值，让Server6客户端跳过空字段，MDB使用NULL
mdb_data = {
    'employee_id': entry_data.get('employee_id'),
    'entry_date': entry_data.get('entry_date').strftime('%Y/%m/%d') if entry_data.get('entry_date') else datetime.now().strftime('%Y/%m/%d'),
    'model': entry_data.get('model') if entry_data.get('model') and entry_data.get('model').strip() else None,
    'number': entry_data.get('number') if entry_data.get('number') and entry_data.get('number').strip() else None,
    'factory_number': entry_data.get('factory_number') if entry_data.get('factory_number') and entry_data.get('factory_number').strip() else None,
    'project_number': entry_data.get('project_number') if entry_data.get('project_number') and entry_data.get('project_number').strip() else None,
    'unit_number': entry_data.get('unit_number') if entry_data.get('unit_number') and entry_data.get('unit_number').strip() else None,
    'category': entry_data.get('category') if entry_data.get('category') is not None else 1,
    'item': entry_data.get('item') if entry_data.get('item') is not None else 1,
    'duration': float(entry_data.get('duration', 0.0)),  # 关键修复: 将Decimal转换为float
    'department': entry_data.get('department') if entry_data.get('department') and entry_data.get('department').strip() else None
}
```

### 2. Server6客户端处理

**文件**: `server5/app/utils/server6_client.py`

**关键逻辑**:
- 空值不跳过，明确发送"NULL"字符串给Server6
- 确保所有字段都被发送，包括空值

```python
# 处理空值：明确发送NULL给Server6
if value is None:
    serializable_data[japanese_key] = "NULL"
    logger.debug(f"  📤 字段 {key} 设置为 NULL")
    continue

# 处理空字符串：也发送NULL
if isinstance(value, str) and value.strip() == '':
    serializable_data[japanese_key] = "NULL"
    logger.debug(f"  📤 字段 {key} (空字符串) 设置为 NULL")
    continue
```

### 3. 数据库表结构

**entries_push_queue表结构**:
```sql
column_name|data_type               |is_nullable|
-----------+------------------------+-----------+
queue_id   |bigint                  |NO         |
entry_id   |bigint                  |NO         |
external_id|integer                 |YES        |
operation  |character varying       |NO         |
synced     |boolean                 |NO         |
created_ts |timestamp with time zone|NO         |
retry_count|integer                 |YES        |
last_error |text                    |YES        |
updated_at |timestamp with time zone|YES        |
```

## 🧪 测试验证

### 测试脚本
**文件**: `server5/test_mdb_null_handling.py`

**测试内容**:
1. 设置包含空值的测试数据
2. 验证f2数据准备逻辑
3. 验证Server6客户端处理
4. 模拟MDB SQL语句生成

**测试数据示例**:
```python
test_entry_data = {
    "entry_date": "2025/07/15",
    "employee_id": "215829",
    "duration": 2.0,
    "model": "",  # 空字符串
    "number": None,  # None值
    "factory_number": "   ",  # 只有空格
    "project_number": None,
    "unit_number": "",
    "category": 1,
    "item": 1,
    "department": "131"
}
```

## 🏗️ 架构设计

### Server5分离架构
- **HTTP服务器** (`start_server5_http_server.py`): 负责读取数据到program1
- **微服务** (`start_server5_notwith_api.py`): 负责接受program1的数据，并进行后台f1-f6操作

### 数据流
```
UI输入 → HTTP服务器 → PostgreSQL entries → 触发器 → entries_push_queue → 微服务f1监听 → f2推送 → Server6 → MDB数据库
```

## ✅ 验证结果

### 正确的MDB SQL语句
```sql
INSERT INTO [元作業時間]
              (従業員ｺｰﾄﾞ, 日付, 機種, 号機,
               工場製番, 工事番号, ﾕﾆｯﾄ番号,
               区分, 項目, 時間, 所属ｺｰﾄﾞ)
             VALUES
              ('215829',
               #2025/07/15#,
               '1',
               '1',
               NULL,
               NULL,
               NULL,
               '1',
               '1',
               2.0,
               '131'
              )
```

### 关键改进
1. **空值处理**: 正确识别空字符串、None值和只有空格的字符串
2. **字段跳过**: 可选字段为空时跳过，让MDB使用NULL
3. **必需字段**: 确保必需字段有默认值
4. **数据类型**: 正确处理Decimal到float的转换

## 🚀 部署说明

### 启动顺序
1. 启动HTTP服务器: `python start_server5_http_server.py`
2. 启动微服务: `python start_server5_notwith_api.py`

### 验证步骤
1. 运行测试脚本: `python test_mdb_null_handling.py`
2. 检查日志输出，确认MDB SQL语句正确
3. 验证数据同步到MDB数据库

## 📝 总结

通过这个解决方案，我们确保了：
- UI输入的空值被正确处理
- Server6生成正确的MDB SQL语句
- MDB数据库接收正确的NULL值
- 数据流在分离架构中正常工作

这个解决方案解决了MDB NULL值处理的核心问题，确保了数据同步的准确性和可靠性。 