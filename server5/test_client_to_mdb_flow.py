#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
客户端到MDB完整数据流测试脚本
验证：客户端输入 → 客户端网关 → PostgreSQL entries → 触发器 → entries_push_queue → f2 → Server6 → MDB
"""

import asyncio
import sys
from pathlib import Path
import logging
import aiohttp
import json
from datetime import datetime, date
import asyncpg
import decimal

# 添加项目路径
sys.path.append(str(Path(__file__).parent))

from app.database import IMDBClient
from app.utils.server6_client import Server6Client

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

class ClientToMDBFlowTest:
    """客户端到MDB完整数据流测试类"""
    
    def __init__(self):
        self.imdb_client = IMDBClient()
        self.server6_client = Server6Client()
        self.base_url = "http://localhost:8009"
        
        # 导入配置
        sys.path.append(str(Path(__file__).parent))
        from config.config import IMDB_HOST, IMDB_PORT, IMDB_USER, IMDB_PASS, IMDB_DB_NAME
        
        self.db_config = {
            "host": IMDB_HOST,
            "port": IMDB_PORT,
            "user": IMDB_USER,
            "password": IMDB_PASS,
            "database": IMDB_DB_NAME
        }
        
        # 测试数据 - 模拟客户端输入
        self.client_input_data = {
            "entry_date": "2025/07/15",
            "employee_id": "215829",
            "duration": 0.4,
            "model": "",  # 空字符串
            "number": None,  # None值
            "factory_number": "   ",  # 只有空格
            "project_number": None,
            "unit_number": "",
            "category": "",  # 空字符串，测试必需字段
            "item": "",  # 空字符串，测试必需字段
            "department": "131"
        }
    
    async def test_client_gateway(self):
        """测试客户端网关API"""
        try:
            logger.info("🔍 步骤1: 测试客户端网关API...")
            
            async with aiohttp.ClientSession() as session:
                url = f"{self.base_url}/client/entries/create"
                
                async with session.post(url, json=self.client_input_data) as response:
                    if response.status == 200:
                        result = await response.json()
                        logger.info(f"✅ 客户端网关API调用成功: {result}")
                        return result
                    else:
                        error_text = await response.text()
                        logger.error(f"❌ 客户端网关API调用失败: {response.status} - {error_text}")
                        return None
                        
        except Exception as e:
            logger.error(f"❌ 客户端网关API测试失败: {e}")
            return None
    
    async def verify_entries_table_data(self, entry_id: int):
        """验证entries表中的数据"""
        try:
            logger.info(f"🔍 步骤2: 验证entries表数据 (entry_id={entry_id})...")
            
            # 连接到PostgreSQL
            conn = await asyncpg.connect(
                host=self.db_config["host"],
                port=self.db_config["port"],
                user=self.db_config["user"],
                password=self.db_config["password"],
                database=self.db_config["database"]
            )
            
            # 查询entry数据
            entry_data = await conn.fetchrow(
                "SELECT * FROM entries WHERE id = $1", entry_id
            )
            
            if not entry_data:
                logger.error(f"❌ 找不到entry记录: entry_id={entry_id}")
                await conn.close()
                return None
            
            logger.info("📊 entries表数据:")
            for key, value in entry_data.items():
                status = "✅ 有值" if value is not None else "❌ NULL"
                logger.info(f"  {key}: {value} ({status})")
            
            await conn.close()
            return entry_data
            
        except Exception as e:
            logger.error(f"❌ 验证entries表数据失败: {e}")
            return None
    
    async def verify_queue_data(self, entry_id: int):
        """验证队列数据"""
        try:
            logger.info(f"🔍 步骤3: 验证队列数据 (entry_id={entry_id})...")
            
            # 连接到PostgreSQL
            conn = await asyncpg.connect(
                host=self.db_config["host"],
                port=self.db_config["port"],
                user=self.db_config["user"],
                password=self.db_config["password"],
                database=self.db_config["database"]
            )
            
            # 查询队列数据
            queue_data = await conn.fetchrow("""
                SELECT * FROM entries_push_queue 
                WHERE entry_id = $1 AND operation = 'INSERT' AND synced = FALSE
                ORDER BY created_ts DESC LIMIT 1
            """, entry_id)
            
            if not queue_data:
                logger.error(f"❌ 找不到队列项: entry_id={entry_id}")
                await conn.close()
                return None
            
            logger.info(f"✅ 找到队列项: queue_id={queue_data['queue_id']}")
            logger.info("📊 队列数据:")
            for key, value in queue_data.items():
                logger.info(f"  {key}: {value}")
            
            await conn.close()
            return queue_data
            
        except Exception as e:
            logger.error(f"❌ 验证队列数据失败: {e}")
            return None
    
    async def simulate_f2_processing(self, entry_id: int):
        """模拟f2处理逻辑"""
        try:
            logger.info(f"🔍 步骤4: 模拟f2处理逻辑 (entry_id={entry_id})...")
            
            # 连接到PostgreSQL
            conn = await asyncpg.connect(
                host=self.db_config["host"],
                port=self.db_config["port"],
                user=self.db_config["user"],
                password=self.db_config["password"],
                database=self.db_config["database"]
            )
            
            # 获取完整的entry数据
            entry_data = await conn.fetchrow(
                "SELECT * FROM entries WHERE id = $1", entry_id
            )
            
            if not entry_data:
                raise ValueError(f"找不到entry记录: entry_id={entry_id}")
            
            # 模拟f2的数据准备逻辑 - 将PostgreSQL整数转换为字符串
            mdb_data = {
                'employee_id': entry_data['employee_id'],
                'entry_date': entry_data['entry_date'].strftime('%Y/%m/%d') if entry_data['entry_date'] else datetime.now().strftime('%Y/%m/%d'),
                'model': entry_data['model'] if entry_data['model'] and entry_data['model'].strip() else None,
                'number': entry_data['number'] if entry_data['number'] and entry_data['number'].strip() else None,
                'factory_number': entry_data['factory_number'] if entry_data['factory_number'] and entry_data['factory_number'].strip() else None,
                'project_number': entry_data['project_number'] if entry_data['project_number'] and entry_data['project_number'].strip() else None,
                'unit_number': entry_data['unit_number'] if entry_data['unit_number'] and entry_data['unit_number'].strip() else None,
                'category': str(entry_data['category']) if entry_data['category'] is not None else None,  # 整数转字符串
                'item': str(entry_data['item']) if entry_data['item'] is not None else None,  # 整数转字符串
                'duration': float(entry_data['duration']) if entry_data['duration'] is not None else 0.0,
                'department': entry_data['department'] if entry_data['department'] and entry_data['department'].strip() else None
            }
            
            logger.info("📊 f2数据准备结果:")
            for key, value in mdb_data.items():
                status = "✅ 有值" if value is not None else "❌ NULL"
                logger.info(f"  {key}: {value} ({status})")
            
            await conn.close()
            return mdb_data
            
        except Exception as e:
            logger.error(f"❌ 模拟f2处理失败: {e}")
            return None
    
    async def simulate_server6_processing(self, mdb_data: dict):
        """模拟Server6处理逻辑"""
        try:
            logger.info("🔍 步骤5: 模拟Server6处理逻辑...")
            
            # 模拟Server6客户端的insert_entry方法
            field_mapping = {
                'employee_id': '従業員ｺｰﾄﾞ',
                'entry_date': '日付',
                'model': '機種',
                'number': '号機',
                'factory_number': '工場製番',
                'project_number': '工事番号',
                'unit_number': 'ﾕﾆｯﾄ番号',
                'category': '区分',
                'item': '項目',
                'duration': '時間',
                'department': '所属ｺｰﾄﾞ'
            }
            
            # 转换数据并映射字段名
            serializable_data = {}
            for key, value in mdb_data.items():
                # 映射字段名
                japanese_key = field_mapping.get(key, key)
                
                # 处理空值：明确发送NULL给Server6
                if value is None:
                    serializable_data[japanese_key] = "NULL"
                    logger.info(f"  📤 字段 {key} 设置为 NULL")
                    continue
                
                # 处理空字符串：也发送NULL
                if isinstance(value, str) and value.strip() == '':
                    serializable_data[japanese_key] = "NULL"
                    logger.info(f"  📤 字段 {key} (空字符串) 设置为 NULL")
                    continue
                
                # 处理日期格式
                if isinstance(value, str) and key == 'entry_date':
                    # 如果是字符串格式的日期，转换为YYYY-MM-DD格式
                    if '/' in value:
                        # 将 YYYY/MM/DD 转换为 YYYY-MM-DD
                        serializable_data[japanese_key] = value.replace('/', '-')
                    else:
                        serializable_data[japanese_key] = value
                elif isinstance(value, decimal.Decimal):
                    serializable_data[japanese_key] = float(value)
                else:
                    serializable_data[japanese_key] = value
            
            logger.info("📤 发送到Server6的数据:")
            for key, value in serializable_data.items():
                status = "✅ 有值" if value != "NULL" else "❌ NULL"
                logger.info(f"  {key}: {value} ({status})")
            
            # 模拟MDB SQL语句
            logger.info("🔍 模拟MDB SQL语句:")
            sql_fields = []
            sql_values = []
            
            for japanese_key, value in serializable_data.items():
                sql_fields.append(japanese_key)
                if value == "NULL":
                    sql_values.append("NULL")
                elif isinstance(value, str):
                    sql_values.append(f"'{value}'")
                elif isinstance(value, (int, float)):
                    sql_values.append(str(value))
                else:
                    sql_values.append(f"'{value}'")
            
            sql_statement = f"""
INSERT INTO [元作業時間]
              ({', '.join(sql_fields)})
             VALUES
              ({', '.join(sql_values)})
            """
            
            logger.info(f"📝 生成的SQL语句:\n{sql_statement}")
            
            return serializable_data
            
        except Exception as e:
            logger.error(f"❌ 模拟Server6处理失败: {e}")
            return None
    
    async def cleanup_test_data(self, entry_id: int):
        """清理测试数据"""
        try:
            # 连接到PostgreSQL
            conn = await asyncpg.connect(
                host=self.db_config["host"],
                port=self.db_config["port"],
                user=self.db_config["user"],
                password=self.db_config["password"],
                database=self.db_config["database"]
            )
            
            # 删除队列项
            await conn.execute(
                "DELETE FROM entries_push_queue WHERE entry_id = $1", entry_id
            )
            
            # 删除entry记录
            await conn.execute(
                "DELETE FROM entries WHERE id = $1", entry_id
            )
            
            await conn.close()
            logger.info("✅ 测试数据已清理")
            
        except Exception as e:
            logger.error(f"❌ 清理测试数据失败: {e}")
    
    async def run_test(self):
        """运行完整测试"""
        logger.info("🧪 开始客户端到MDB完整数据流测试")
        logger.info("=" * 60)
        
        entry_id = None
        
        try:
            # 1. 测试客户端网关API
            gateway_result = await self.test_client_gateway()
            if not gateway_result or not gateway_result.get("success"):
                logger.error("❌ 客户端网关API测试失败")
                return
            
            entry_id = gateway_result["entry_id"]
            logger.info(f"✅ 客户端网关API测试成功，entry_id={entry_id}")
            
            # 2. 验证entries表数据
            entries_data = await self.verify_entries_table_data(entry_id)
            if not entries_data:
                logger.error("❌ entries表数据验证失败")
                return
            
            # 3. 验证队列数据
            queue_data = await self.verify_queue_data(entry_id)
            if not queue_data:
                logger.error("❌ 队列数据验证失败")
                return
            
            # 4. 模拟f2处理
            mdb_data = await self.simulate_f2_processing(entry_id)
            if not mdb_data:
                logger.error("❌ f2处理模拟失败")
                return
            
            # 5. 模拟Server6处理
            server6_data = await self.simulate_server6_processing(mdb_data)
            if not server6_data:
                logger.error("❌ Server6处理模拟失败")
                return
            
            logger.info("✅ 客户端到MDB完整数据流测试完成")
            
        except Exception as e:
            logger.error(f"❌ 测试失败: {e}")
            raise
        finally:
            # 清理测试数据
            if entry_id:
                await self.cleanup_test_data(entry_id)

async def main():
    """主函数"""
    test = ClientToMDBFlowTest()
    await test.run_test()

if __name__ == "__main__":
    asyncio.run(main()) 