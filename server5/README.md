# MySuite Server5 - 数据同步微服务

## 📋 项目概述

Server5是MySuite企业级工业管理系统的第五个微服务，专门负责MDB-PostgreSQL双向数据同步。该服务运行在Windows 10 (************)上，但在Ubuntu环境中开发和测试。

## ✨ 核心功能

### 🔧 六大核心功能模块

- **f1: Listener监听器** - PostgreSQL NOTIFY监听 + 自动分区管理 ✅
- **f2: 推送回写引擎** - 异步写入MDB + ID映射回写 🚧
- **f3: 拉取同步器** - 异步拉取MDB + 差异检测 🚧  
- **f4: 操作处理器** - entries操作 + 触发器调用 🚧
- **f5: 批量同步器** - 全量数据同步 + 数据一致性 🚧
- **f6: 专属ID同步器** - 用户30天数据 + 覆盖校正 🚧

### 💾 数据库支持

- **PostgreSQL** (************:5432) - 主数据库和entries分区表
- **MongoDB** (************:27017) - 日志存储和历史数据  
- **Redis** (localhost:6379) - 任务队列和缓存
- **Access MDB** - 32位工时数据库（跨平台兼容）

## 🏗️ 架构设计

```
客户端UI → f4操作处理器 → entries分区表 → 触发器+NOTIFY → f1监听器 → f2推送回写 → MDB数据库
                                                          ↓
                                              f6专属ID同步器 ← Redis任务队列
```

## 🚀 快速开始

### 1️⃣ 环境准备

```bash
# 激活虚拟环境
conda activate my_suite_unified

# 安装依赖
pip install motor asyncpg redis fastapi uvicorn
```

### 2️⃣ 配置检查

Server5会自动检测运行环境：
- **Ubuntu开发环境**: 使用模拟ODBC连接用于测试
- **Windows生产环境**: 使用真实ODBC连接Access数据库

### 3️⃣ 快速测试

```bash
cd server5
python quick_test.py
```

### 4️⃣ 启动服务

```bash
python start_server5.py
```

服务将在 `http://0.0.0.0:8009` 启动

## 🔍 API端点

### 基础端点
- `GET /` - 服务信息
- `GET /health` - 健康检查  
- `GET /status` - 详细状态

### 控制端点
- `POST /services/restart` - 重启所有服务
- `POST /services/f1/partition-check` - 手动触发分区检查

## 📊 监控和日志

### 日志位置
- **主日志**: `logs/server5.log`
- **错误日志**: MongoDB `error_logs` 集合
- **性能指标**: MongoDB `performance_metrics` 集合

### 监控指标
- 同步操作次数和耗时
- 数据库连接状态
- 队列长度和处理速度
- 错误率和类型统计

## 🔧 开发状态

### ✅ 已完成功能
1. **模块化架构设计** - 完全模块化，易于维护和扩展
2. **跨平台兼容** - Ubuntu开发 + Windows部署
3. **f1监听器服务** - PostgreSQL NOTIFY监听和自动分区
4. **数据库客户端** - PostgreSQL, Redis, MongoDB, ODBC
5. **基础API框架** - FastAPI + 健康检查
6. **日志和监控** - 结构化日志和性能监控
7. **测试框架** - 基础测试和快速验证

### 🚧 待完成功能
1. **f2推送回写引擎** - MDB数据写入和ID回写
2. **f3拉取同步器** - MDB数据拉取和差异检测
3. **f4操作处理器** - UI操作处理和触发器集成
4. **f5批量同步器** - 全量数据同步
5. **f6专属ID同步器** - 用户数据30天同步
6. **Web仪表板** - 实时监控界面
7. **自动化测试** - 完整的测试套件

## 🛠️ 部署到Windows

### 1️⃣ 文件传输
```bash
# 将server5文件夹复制到Windows 10 (************)
scp -r server5/ user@************:D:/MySuite/
```

### 2️⃣ Windows环境配置
1. 安装Python 3.12+
2. 安装依赖包: `pip install -r requirements.txt`
3. 安装pywin32: `pip install pywin32`
4. 确保Access数据库路径正确

### 3️⃣ 启动服务
```cmd
cd D:\MySuite\server5
python start_server5.py
```

## 📋 配置说明

主要配置在 `config/config.py`:

```python
# 数据库连接
PG_HOST = "************"      # PostgreSQL服务器
MONGO_HOST = "************"   # MongoDB服务器  
REDIS_HOST = "localhost"      # Redis服务器
MDB_PATH = r"D:\actest25\6.mdb"  # Windows MDB路径

# 服务配置
SERVICE_PORT = 8009           # 服务端口
DEBUG = True                  # 调试模式
WORKER_CONCURRENCY = 5        # 并发工作线程
```

## 🐛 故障排除

### 常见问题

1. **PostgreSQL连接失败**
   - 检查网络连接: `nc -zv ************ 5432`
   - 确认数据库服务运行
   - 验证用户名密码

2. **Redis连接失败**
   - 启动Redis服务: `sudo systemctl start redis`
   - 检查端口占用: `netstat -tlnp | grep 6379`

3. **ODBC连接问题**
   - Ubuntu环境会自动使用模拟连接
   - Windows环境需要安装pywin32

4. **模块导入错误**
   - 检查Python路径配置
   - 确认所有依赖已安装

## 🤝 开发计划

### 第一阶段 (1周) - 核心同步功能
- [ ] 完成f2推送回写引擎
- [ ] 实现f6专属ID同步器
- [ ] 集成触发器和NOTIFY机制

### 第二阶段 (1周) - 数据同步优化
- [ ] 完成f3拉取同步器
- [ ] 实现f5批量同步器
- [ ] 添加数据一致性检查

### 第三阶段 (1周) - 完整集成
- [ ] 完成f4操作处理器
- [ ] 集成客户端UI操作
- [ ] 完善错误处理和重试机制

### 第四阶段 (1周) - 监控和部署
- [ ] Web仪表板开发
- [ ] 完整的自动化测试
- [ ] Windows生产环境部署

## 📞 技术支持

如有问题，请查看：
1. 日志文件: `logs/server5.log`
2. 快速测试: `python quick_test.py`
3. 健康检查: `curl http://localhost:8009/health`

---

**版本**: v1.0.0  
**更新时间**: 2025年6月27日  
**开发环境**: Ubuntu 22.04 + Python 3.12  
**目标部署**: Windows 10 (************) 