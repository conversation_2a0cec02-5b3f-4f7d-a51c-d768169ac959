# Windows MDB 测试使用说明

## 目标
测试读取 `D:\actest25\6.mdb` 中日期为 `2025/06/25` 的数据。

## 文件说明

### 1. test_windows_mdb_direct.py
**专用于Windows环境的直接MDB访问测试**

这是一个独立的测试脚本，不依赖Server5的其他组件，可以直接在Windows上运行。

### 2. test_mdb_read_specific_date.py  
**跨平台的MDB测试脚本**

使用Server5的多平台框架，在Windows上会进行真实访问，在其他平台上使用模拟模式。

## 在Windows 10 (************) 上的使用步骤

### 步骤1: 准备环境
```bash
# 1. 确保Python已安装
python --version

# 2. 安装pywin32（用于Windows COM操作）
pip install pywin32

# 3. 确认MDB文件存在
# 检查 D:\actest25\6.mdb 文件是否存在
```

### 步骤2: 运行直接测试（推荐）
```bash
# 复制 test_windows_mdb_direct.py 到Windows机器
# 然后运行：
python test_windows_mdb_direct.py
```

### 步骤3: 运行完整框架测试
```bash
# 如果想测试完整的Server5框架：
# 1. 复制整个server5目录到Windows机器
# 2. 安装依赖：
pip install -r requirements.txt

# 3. 运行测试：
python test_mdb_read_specific_date.py
```

## 预期输出

### 成功连接并找到数据：
```
🎯 Windows MDB 直接访问测试
============================================================
📁 MDB文件路径: D:\actest25\6.mdb
📅 查询日期: 2025/06/25
✅ MDB文件存在: D:\actest25\6.mdb
✅ win32com 模块可用
------------------------------------------------------------
🔍 开始查询数据...
✅ Access应用程序连接成功
✅ MDB数据库打开成功
🔍 执行SQL查询: SELECT * FROM 元作業時間 WHERE 日付 = #2025/06/25# ORDER BY ID DESC
✅ 查询执行成功
📊 查询完成，共找到 X 条记录

============================================================
📋 查询结果详情:
============================================================

📝 记录 1:
----------------------------------------
  ID: 12345
  従業員ｺｰﾄﾞ: EMP001
  日付: 2025/06/25
  機種: ModelXYZ
  号機: 001
  工場製番: F001
  工事番号: P001
  ﾕﾆｯﾄ番号: U001
  区分: 生产
  項目: 组装
  時間: 8.0
  所属ｺｰﾄﾞ: DEPT01
```

### 成功连接但没有找到数据：
```
📊 查询完成，共找到 0 条记录

📭 在 2025/06/25 没有找到数据

💡 可能的原因:
  1. 该日期确实没有工时记录
  2. 日期格式可能不匹配
  3. 表名或字段名可能不正确

🔍 尝试查询表结构...
📋 表字段信息:
  字段 1: ID (4)
  字段 2: 従業員ｺｰﾄﾞ (10)
  字段 3: 日付 (8)
  ...

📋 最近5条记录示例:
  记录 1: 日付=2025/06/24, 従業員ｺｰﾄﾞ=EMP001
  记录 2: 日付=2025/06/23, 従業員ｺｰﾄﾞ=EMP002
  ...
```

### 连接失败：
```
❌ MDB文件不存在: D:\actest25\6.mdb
```
或
```
❌ win32com 模块不可用: No module named 'win32com'
💡 请安装: pip install pywin32
```

## 故障排除

### 1. 文件路径问题
- 确认MDB文件确实在 `D:\actest25\6.mdb`
- 检查文件权限，确保Python可以读取

### 2. pywin32安装问题
```bash
# 卸载后重新安装
pip uninstall pywin32
pip install pywin32

# 或使用conda安装
conda install pywin32
```

### 3. Access版本兼容性
- 确保系统已安装Microsoft Access或Access Database Engine
- 32位/64位版本要匹配Python版本

### 4. 日期格式问题
如果查询不到数据，可能是日期格式问题，可以尝试：
- `2025/6/25` (单数字月/日)
- `25/06/2025` (DD/MM/YYYY格式)
- `2025-06-25` (ISO格式)

## 修改查询日期

要查询其他日期的数据，修改脚本中的 `target_date` 变量：

```python
# 在 test_windows_mdb_direct.py 中找到这一行：
target_date = "2025/06/25"

# 修改为你要查询的日期：
target_date = "2025/06/26"  # 或其他日期
```

## 安全提示

- 此脚本仅用于读取数据，不会修改MDB文件
- 建议在测试前备份重要的MDB文件
- 如果在生产环境运行，请先在测试环境验证 