#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试MDB读取指定日期数据 - 基于xmlinit.py的逻辑
"""

import os
import sys
import win32com.client
import pythoncom
from datetime import datetime, timedelta
import pytz

# 数据库路径配置
MDB_PATH = r"D:\actest25\ser0613\1.mdb"  # 修改为实际MDB路径

def test_mdb_read_date(target_date: str, employee_id: str = "215829"):
    """
    测试读取MDB中指定日期的数据
    target_date: "2025/06/25" 格式
    employee_id: 员工ID，默认215829
    """
    print(f"🔍 测试读取MDB数据: {target_date}, 员工ID: {employee_id}")
    
    pythoncom.CoInitialize()
    records = []
    
    try:
        # 连接Access数据库
        access = win32com.client.Dispatch("Access.Application")
        access.OpenCurrentDatabase(MDB_PATH)
        db = access.CurrentDb()
        
        # 构造SQL查询 - 基于xmlinit.py的逻辑
        sql = f"""
        SELECT ID, 従業員ｺｰﾄﾞ, 日付, 機種, 号機, 工場製番, 工事番号,
               ﾕﾆｯﾄ番号, 区分, 項目, 時間, 所属ｺｰﾄﾞ
          FROM 元作業時間
         WHERE 従業員ｺｰﾄﾞ = '{employee_id}'
           AND 日付 = #{target_date}#
         ORDER BY 時間
        """
        
        print(f"📋 执行SQL: {sql}")
        
        # 执行查询
        rs = db.OpenRecordset(sql)
        
        # 读取记录
        while not rs.EOF:
            record = {
                'id': rs.Fields('ID').Value,
                'employee_id': rs.Fields('従業員ｺｰﾄﾞ').Value,
                'date': rs.Fields('日付').Value.strftime("%Y/%m/%d") if rs.Fields('日付').Value else None,
                'model': rs.Fields('機種').Value or "",
                'number': rs.Fields('号機').Value or "",
                'factory_number': rs.Fields('工場製番').Value or "",
                'project_number': rs.Fields('工事番号').Value or "",
                'unit_number': rs.Fields('ﾕﾆｯﾄ番号').Value or "",
                'category': rs.Fields('区分').Value or "",
                'item': rs.Fields('項目').Value or "",
                'time': rs.Fields('時間').Value or 0.0,
                'department': rs.Fields('所属ｺｰﾄﾞ').Value or ""
            }
            records.append(record)
            rs.MoveNext()
        
        print(f"✅ 成功读取 {len(records)} 条记录")
        
        # 输出记录详情
        for i, record in enumerate(records, 1):
            print(f"\n📄 记录 {i}:")
            print(f"  ID: {record['id']}")
            print(f"  员工ID: {record['employee_id']}")
            print(f"  日期: {record['date']}")
            print(f"  机种: {record['model']}")
            print(f"  号机: {record['number']}")
            print(f"  工厂制番: {record['factory_number']}")
            print(f"  工事番号: {record['project_number']}")
            print(f"  单元番号: {record['unit_number']}")
            print(f"  区分: {record['category']}")
            print(f"  项目: {record['item']}")
            print(f"  时间: {record['time']}")
            print(f"  部门: {record['department']}")
            
        return records
        
    except Exception as e:
        print(f"❌ MDB读取失败: {e}")
        return []
        
    finally:
        try:
            access.CloseCurrentDatabase()
            access.Quit()
        except:
            pass
        pythoncom.CoUninitialize()

def test_mdb_connection():
    """测试MDB数据库连接"""
    print(f"🔗 测试MDB连接: {MDB_PATH}")
    
    if not os.path.exists(MDB_PATH):
        print(f"❌ MDB文件不存在: {MDB_PATH}")
        return False
    
    pythoncom.CoInitialize()
    
    try:
        access = win32com.client.Dispatch("Access.Application")
        access.OpenCurrentDatabase(MDB_PATH)
        
        # 测试查询表结构
        db = access.CurrentDb()
        test_sql = "SELECT TOP 1 * FROM 元作業時間"
        rs = db.OpenRecordset(test_sql)
        
        print("✅ MDB连接成功")
        print(f"📊 字段列表:")
        for i in range(rs.Fields.Count):
            field = rs.Fields(i)
            print(f"  - {field.Name} ({field.Type})")
        
        return True
        
    except Exception as e:
        print(f"❌ MDB连接失败: {e}")
        return False
        
    finally:
        try:
            access.CloseCurrentDatabase()
            access.Quit()
        except:
            pass
        pythoncom.CoUninitialize()

if __name__ == "__main__":
    print("🧪 MDB读取测试工具")
    print("=" * 50)
    
    # 首先测试连接
    if test_mdb_connection():
        print("\n" + "=" * 50)
        
        # 测试读取2025/06/25的数据
        target_date = "2025/06/25"
        employee_id = "215829"
        
        records = test_mdb_read_date(target_date, employee_id)
        
        if records:
            print(f"\n📊 总结:")
            print(f"  - 目标日期: {target_date}")
            print(f"  - 员工ID: {employee_id}")
            print(f"  - 记录数量: {len(records)}")
            print(f"  - 总工时: {sum(r['time'] for r in records)} 小时")
        else:
            print(f"\n⚠️ 没有找到 {target_date} 的数据")
    
    print("\n✅ 测试完成") 