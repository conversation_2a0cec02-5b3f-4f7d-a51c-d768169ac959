#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试Server5支持通过external_id删除的功能
验证客户端可以使用external_id进行删除操作
"""

import requests
import json
import time
from datetime import datetime

# Server5配置
SERVER5_BASE_URL = "http://localhost:8009"
ENTRIES_API_BASE = f"{SERVER5_BASE_URL}/api/entries"

def test_entries_data():
    """获取entries数据"""
    try:
        print("🔍 获取entries数据...")
        
        response = requests.get(f"{ENTRIES_API_BASE}/?employee_id=215829&limit=5", timeout=10)
        if response.status_code == 200:
            entries = response.json()
            print(f"✅ 获取到 {len(entries)} 条记录")
            return entries
        else:
            print(f"❌ 获取entries失败: {response.status_code}")
            return []
    except Exception as e:
        print(f"❌ 获取entries异常: {e}")
        return []

def test_delete_by_internal_id(internal_id):
    """通过内部ID测试删除"""
    try:
        print(f"\n🗑️ 测试通过内部ID删除: internal_id={internal_id}")
        
        delete_response = requests.delete(f"{ENTRIES_API_BASE}/{internal_id}", timeout=10)
        
        if delete_response.status_code == 200:
            result = delete_response.json()
            print(f"✅ 内部ID删除成功: {result}")
            return True
        else:
            print(f"❌ 内部ID删除失败: {delete_response.status_code} - {delete_response.text}")
            return False
            
    except Exception as e:
        print(f"❌ 内部ID删除异常: {e}")
        return False

def test_delete_by_external_id(external_id):
    """通过外部ID测试删除"""
    try:
        print(f"\n🗑️ 测试通过外部ID删除: external_id={external_id}")
        
        delete_response = requests.delete(f"{ENTRIES_API_BASE}/{external_id}", timeout=10)
        
        if delete_response.status_code == 200:
            result = delete_response.json()
            print(f"✅ 外部ID删除成功: {result}")
            return True
        else:
            print(f"❌ 外部ID删除失败: {delete_response.status_code} - {delete_response.text}")
            return False
            
    except Exception as e:
        print(f"❌ 外部ID删除异常: {e}")
        return False

def main():
    """主测试函数"""
    print("🚀 开始测试Server5支持external_id删除功能")
    print("=" * 60)
    
    # 1. 获取entries数据
    entries = test_entries_data()
    if not entries:
        print("❌ 无法获取entries数据，测试终止")
        return
    
    # 2. 显示数据结构
    print("\n📋 Entries数据结构:")
    for i, entry in enumerate(entries):
        print(f"  记录 {i+1}: internal_id={entry.get('id')}, external_id={entry.get('external_id')}")
    
    # 3. 选择测试记录
    test_entry = entries[0]
    internal_id = test_entry.get('id')
    external_id = test_entry.get('external_id')
    
    print(f"\n📋 选择测试记录:")
    print(f"  - 内部ID: {internal_id}")
    print(f"  - 外部ID: {external_id}")
    
    # 4. 测试通过外部ID删除
    print("\n" + "=" * 40)
    print("🔧 测试通过external_id删除")
    success = test_delete_by_external_id(external_id)
    
    if success:
        print("\n" + "=" * 60)
        print("🎉 Server5支持external_id删除功能测试成功")
        print("💡 客户端可以使用external_id (DB_ID) 进行删除操作")
        print("💡 这与Server6的MDB操作保持一致")
    else:
        print("\n" + "=" * 60)
        print("💥 Server5支持external_id删除功能测试失败")
        print("🔧 需要检查Server5的删除API实现")
    
    print(f"📅 测试完成时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")

if __name__ == "__main__":
    main() 