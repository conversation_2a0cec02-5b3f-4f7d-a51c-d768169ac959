# №02025/07/10 + Server5 HTTP服务器启动脚本（纯HTTP API，不包含微服务）
# 完全分离HTTP服务与微服务 - 只启动HTTP API服务器

import os
import sys
import logging
from pathlib import Path
import uvicorn
import signal
import subprocess
import socket
import atexit

# 设置环境变量 - 启用 HTTP-only 模式
os.environ['HTTP_ONLY_MODE'] = 'true'

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.StreamHandler(sys.stdout),
        logging.FileHandler('logs/server5_http_only.log', encoding='utf-8')
    ]
)

logger = logging.getLogger(__name__)

# 添加项目路径
sys.path.insert(0, str(Path(__file__).parent))

def check_port_available(port):
    """检查端口是否可用"""
    try:
        with socket.socket(socket.AF_INET, socket.SOCK_STREAM) as s:
            s.bind(('localhost', port))
            return True
    except OSError:
        return False

def kill_existing_processes(port):
    """杀掉占用指定端口的进程 - 优化版本"""
    try:
        # 查找占用端口的进程
        result = subprocess.run(
            ['lsof', '-ti', f':{port}'],
            capture_output=True,
            text=True
        )
        
        if result.stdout.strip():
            pids = result.stdout.strip().split('\n')
            print(f"🔍 发现占用端口{port}的进程: {pids}")
            
            # 首先尝试优雅关闭 (SIGTERM)
            for pid in pids:
                if pid.strip():
                    try:
                        subprocess.run(['kill', '-15', pid.strip()], check=True)
                        print(f"📨 向进程 {pid} 发送SIGTERM信号")
                    except subprocess.CalledProcessError:
                        print(f"⚠️ 无法向进程 {pid} 发送SIGTERM")
            
            # 等待优雅关闭
            print(f"⏳ 等待进程优雅关闭...")
            import time
            time.sleep(2)
            
            # 检查是否还有进程占用端口
            result = subprocess.run(
                ['lsof', '-ti', f':{port}'],
                capture_output=True,
                text=True
            )
            
            if result.stdout.strip():
                # 如果还有进程，强制杀死
                remaining_pids = result.stdout.strip().split('\n')
                print(f"⚠️ 仍有进程占用端口，强制杀死: {remaining_pids}")
                
                for pid in remaining_pids:
                    if pid.strip():
                        try:
                            subprocess.run(['kill', '-9', pid.strip()], check=True)
                            print(f"💀 已强制杀死进程 {pid}")
                        except subprocess.CalledProcessError:
                            print(f"❌ 无法强制杀死进程 {pid}")
            
            # 等待端口释放
            print(f"⏳ 等待端口{port}释放...")
            time.sleep(1)
            
            # 检查端口是否释放
            if check_port_available(port):
                print(f"✅ 端口{port}已释放")
                return True
            else:
                print(f"❌ 端口{port}仍被占用")
                return False
        else:
            print(f"✅ 端口{port}可用")
        
        return True
    except Exception as e:
        print(f"❌ 检查端口时出错: {e}")
        return False

# 导入配置选择器并加载配置
from app.config_selector import ConfigSelector

def load_configuration():
    """加载配置"""
    try:
        selector = ConfigSelector()
        config_dict, config_name = selector.load_config()
        config_module = selector.apply_config_to_module(config_dict)
        
        # 显示配置信息
        info = selector.get_current_config_info()
        print(f"📋 配置信息:")
        print(f"  - 配置文件: {config_name}")
        print(f"  - 平台: {info['platform']}")
        print(f"  - 主机: {info['hostname']}")
        print(f"  - IP: {info['local_ip']}")
        print(f"  - 自动检测: {info['auto_detected']}")
        
        return config_module
    except Exception as e:
        print(f"❌ 配置加载失败: {e}")
        raise

# 加载配置
config_module = load_configuration()

def signal_handler(signum, frame):
    """信号处理器"""
    print(f"\n📡 接收到信号 {signum}，正在关闭HTTP服务...")
    sys.exit(0)

def main():
    """主函数 - 只启动HTTP API服务器（不包含微服务）"""
    print("🌐 MySuite Server5 - 纯HTTP API服务器")
    print("=" * 60)
    print("🔧 模式: HTTP-only (不启动 f1-f4 微服务)")
    print("🌐 功能: 仅提供 HTTP API 接口")
    print("🔗 微服务: 需要单独启动 start_server5_notwith_api.py")
    print("=" * 60)
    
    # 注册信号处理器
    signal.signal(signal.SIGINT, signal_handler)
    signal.signal(signal.SIGTERM, signal_handler)

    try:
        # 检查并清理端口
        print(f"🔍 检查端口 {config_module.SERVICE_PORT} 可用性...")
        if not kill_existing_processes(config_module.SERVICE_PORT):
            print(f"❌ 无法释放端口{config_module.SERVICE_PORT}")
            sys.exit(1)

        print("🌐 启动纯HTTP API服务器...")
        print(f"📡 监听地址: http://0.0.0.0:{config_module.SERVICE_PORT}")
        print(f"📋 API文档: http://localhost:{config_module.SERVICE_PORT}/docs")
        print(f"🔍 健康检查: http://localhost:{config_module.SERVICE_PORT}/health")
        print("⚠️  注意：此模式仅启动HTTP API，不包含f1-f4微服务")
        print("🔧 如需启动微服务，请另开终端运行: python start_server5_notwith_api.py")
        print("👋 按 Ctrl+C 退出")
        
        # 启动 FastAPI 应用 - 环境变量已设置 HTTP_ONLY_MODE=true
        uvicorn.run(
            "app.main:app",
            host="0.0.0.0", 
            port=config_module.SERVICE_PORT,
            log_level="info",
            reload=False,  # 生产环境不要热重载
            access_log=True
        )

    except KeyboardInterrupt:
        print("\n👋 接收到中断信号，正在关闭HTTP服务...")
    except Exception as e:
        print(f"❌ HTTP服务启动失败: {e}")
        logger.error(f"Server5 HTTP服务启动失败: {e}")
        sys.exit(1)

if __name__ == "__main__":
    main() 