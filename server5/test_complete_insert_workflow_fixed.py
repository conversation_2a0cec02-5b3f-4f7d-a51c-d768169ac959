#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
test_complete_insert_workflow_fixed.py
完整插入工作流测试 - 包含触发器、f1监听器、f2服务、日期格式转换
"""

import asyncio
import sys
import os
from pathlib import Path
from datetime import datetime, date
import logging

# 添加项目根目录到Python路径
sys.path.append(str(Path(__file__).parent))

from app.services.f4_operation_handler import OperationHandlerService
from app.services.f1_listener import ListenerService
from app.services.f2_push_writer import PushWriterService
from app.database import IMDBClient
from app.utils.server6_client import Server6Client

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)

logger = logging.getLogger(__name__)

class CompleteInsertWorkflowTest:
    """完整插入工作流测试类"""
    
    def __init__(self):
        self.f4_service = OperationHandlerService()
        self.f1_service = ListenerService()
        self.f2_service = PushWriterService()
        self.imdb_client = IMDBClient()
        self.server6_client = Server6Client()
        
        # 使用真实员工ID和测试数据（注意日期格式）
        self.test_data = {
            'employee_id': '215829',  # 真实员工ID格式
            'entry_date': '2025/06/30',  # 使用MDB格式的日期
            'model': 'TEST_MODEL',
            'number': '123',
            'factory_number': 'F001',
            'project_number': '24585',  # 从写入例子中获取
            'unit_number': 'U001',
            'category': 3,  # 使用整数
            'item': 7,      # 使用整数
            'duration': 9.0,  # 从写入例子中获取
            'department': '131'  # 从写入例子中获取
        }
        
    async def start(self):
        """启动测试环境"""
        try:
            # 启动所有服务
            await self.f4_service.start()
            await self.f1_service.start()
            await self.f2_service.start()
            await self.imdb_client.connect()
            
            logger.info("✅ 测试环境启动成功")
            return True
        except Exception as e:
            logger.error(f"❌ 测试环境启动失败: {e}")
            return False
    
    async def stop(self):
        """停止测试环境"""
        try:
            await self.f4_service.stop()
            await self.f1_service.stop()
            await self.f2_service.stop()
            await self.imdb_client.disconnect()
            await self.server6_client.disconnect()
            logger.info("✅ 测试环境已停止")
        except Exception as e:
            logger.error(f"❌ 测试环境停止失败: {e}")
    
    async def check_triggers(self):
        """检查数据库触发器"""
        print("\n" + "="*80)
        print("🔧 检查数据库触发器")
        print("="*80)
        
        try:
            # 检查entries表是否有触发器
            triggers = await self.imdb_client.fetch_all("""
                SELECT trigger_name, event_manipulation, event_object_table, action_statement
                FROM information_schema.triggers 
                WHERE event_object_table = 'entries'
            """)
            
            if triggers:
                print(f"✅ 找到 {len(triggers)} 个触发器:")
                for trigger in triggers:
                    print(f"   触发器: {trigger['trigger_name']}")
                    print(f"   事件: {trigger['event_manipulation']}")
                    print(f"   表: {trigger['event_object_table']}")
                    print(f"   动作: {trigger['action_statement'][:100]}...")
            else:
                print("❌ entries表没有触发器!")
                print("⚠️ 需要创建触发器来自动生成队列项")
                return False
                
        except Exception as e:
            print(f"❌ 检查触发器失败: {e}")
            return False
        
        return True
    
    async def create_triggers_if_needed(self):
        """如果需要，创建触发器"""
        print("\n" + "="*80)
        print("🔧 创建触发器（如果需要）")
        print("="*80)
        
        try:
            # 检查是否已有触发器
            triggers = await self.imdb_client.fetch_all("""
                SELECT trigger_name FROM information_schema.triggers 
                WHERE event_object_table = 'entries' AND trigger_name = 'entries_insert_trigger'
            """)
            
            if not triggers:
                print("📝 创建entries插入触发器...")
                
                # 创建触发器函数
                await self.imdb_client.execute_command("""
                    CREATE OR REPLACE FUNCTION entries_insert_trigger_function()
                    RETURNS TRIGGER AS $$
                    BEGIN
                        -- 插入队列项
                        INSERT INTO entries_push_queue(entry_id, operation, synced)
                        VALUES (NEW.id, 'INSERT', FALSE);
                        
                        -- 发送NOTIFY通知
                        PERFORM pg_notify('push_job', NEW.id::text);
                        
                        RETURN NEW;
                    END;
                    $$ LANGUAGE plpgsql;
                """)
                
                # 创建触发器
                await self.imdb_client.execute_command("""
                    CREATE TRIGGER entries_insert_trigger
                    AFTER INSERT ON entries
                    FOR EACH ROW
                    EXECUTE FUNCTION entries_insert_trigger_function();
                """)
                
                print("✅ 触发器创建成功")
            else:
                print("✅ 触发器已存在")
                
        except Exception as e:
            print(f"❌ 创建触发器失败: {e}")
            return False
        
        return True
    
    async def test_insert_workflow(self):
        """测试完整插入工作流"""
        print("\n" + "="*80)
        print("📝 测试完整插入工作流")
        print("="*80)
        
        print(f"📋 测试数据:")
        for key, value in self.test_data.items():
            print(f"   {key}: {value} (类型: {type(value).__name__})")
        
        try:
            print(f"\n🔄 执行插入操作...")
            start_time = datetime.now()
            
            # 1. 通过f4服务插入数据到entries
            result = await self.f4_service.handle_insert_operation(
                self.test_data, user_id="test_user_complete"
            )
            
            end_time = datetime.now()
            duration = (end_time - start_time).total_seconds()
            
            print(f"✅ 插入操作完成，耗时: {duration:.2f} 秒")
            print(f"📊 插入结果: {result}")
            
            # 检查结果
            if result.get('status') == 'success':
                entry_id = result.get('entry_id')
                print(f"✅ 获得entry_id: {entry_id}")
                
                # 2. 等待触发器创建队列项
                print(f"\n⏳ 等待触发器创建队列项...")
                await asyncio.sleep(2)
                
                # 3. 检查队列项是否创建
                queue_created = await self.check_queue_item_created(entry_id)
                
                if queue_created:
                    print(f"✅ 队列项创建成功")
                    
                    # 4. 等待f2服务处理队列
                    print(f"\n⏳ 等待f2服务处理队列...")
                    await asyncio.sleep(5)
                    
                    # 5. 检查MDB同步结果
                    sync_result = await self.check_mdb_sync_result(entry_id)
                    
                    if sync_result['success']:
                        print(f"✅ MDB同步成功!")
                        print(f"   external_id: {sync_result['external_id']}")
                        return {
                            'success': True,
                            'entry_id': entry_id,
                            'external_id': sync_result['external_id'],
                            'result': result
                        }
                    else:
                        print(f"❌ MDB同步失败: {sync_result['error']}")
                        return {
                            'success': False,
                            'error': f"MDB同步失败: {sync_result['error']}",
                            'entry_id': entry_id
                        }
                else:
                    print(f"❌ 队列项创建失败")
                    return {
                        'success': False,
                        'error': '队列项创建失败',
                        'entry_id': entry_id
                    }
            else:
                error_msg = result.get('error_message', 'Unknown error')
                print(f"❌ 插入操作失败: {error_msg}")
                return {
                    'success': False,
                    'error': error_msg,
                    'result': result
                }
                
        except Exception as e:
            print(f"❌ 插入工作流异常: {e}")
            return {
                'success': False,
                'error': str(e)
            }
    
    async def check_queue_item_created(self, entry_id):
        """检查队列项是否创建"""
        try:
            queue_items = await self.imdb_client.fetch_all("""
                SELECT queue_id, entry_id, operation, synced, created_ts
                FROM entries_push_queue 
                WHERE entry_id = $1 AND operation = 'INSERT'
                ORDER BY created_ts DESC
                LIMIT 1
            """, entry_id)
            
            if queue_items:
                item = queue_items[0]
                print(f"✅ 找到队列项:")
                print(f"   队列ID: {item['queue_id']}")
                print(f"   条目ID: {item['entry_id']}")
                print(f"   操作: {item['operation']}")
                print(f"   同步状态: {'已同步' if item['synced'] else '未同步'}")
                return True
            else:
                print(f"❌ 未找到队列项")
                return False
                
        except Exception as e:
            print(f"❌ 检查队列项失败: {e}")
            return False
    
    async def check_mdb_sync_result(self, entry_id):
        """检查MDB同步结果"""
        try:
            # 检查PostgreSQL中的external_id
            entry = await self.imdb_client.fetch_one(
                "SELECT external_id FROM entries WHERE id = $1", entry_id
            )
            
            if entry and entry.get('external_id'):
                external_id = entry['external_id']
                print(f"✅ PostgreSQL中external_id: {external_id}")
                
                # 检查队列项是否已同步
                queue_item = await self.imdb_client.fetch_one("""
                    SELECT synced FROM entries_push_queue 
                    WHERE entry_id = $1 AND operation = 'INSERT'
                    ORDER BY created_ts DESC LIMIT 1
                """, entry_id)
                
                if queue_item and queue_item['synced']:
                    print(f"✅ 队列项已同步")
                    
                    # 验证MDB中的数据
                    mdb_verified = await self.verify_mdb_data(external_id)
                    
                    return {
                        'success': True,
                        'external_id': external_id,
                        'mdb_verified': mdb_verified
                    }
                else:
                    return {
                        'success': False,
                        'error': '队列项未同步'
                    }
            else:
                return {
                    'success': False,
                    'error': 'PostgreSQL中没有external_id'
                }
                
        except Exception as e:
            return {
                'success': False,
                'error': f"检查同步结果失败: {e}"
            }
    
    async def verify_mdb_data(self, external_id):
        """验证MDB中的数据"""
        try:
            # 查询特定日期范围的数据
            start_date = date(2025, 6, 30)
            end_date = date(2025, 6, 30)
            
            records = await self.server6_client.query_bulk_fast(start_date, end_date)
            
            if records:
                # 查找我们的测试记录
                for record in records:
                    if record.get('external_id') == external_id:
                        print(f"✅ 在MDB中找到测试记录:")
                        print(f"   external_id: {record.get('external_id')}")
                        print(f"   employee_id: {record.get('employee_id')}")
                        print(f"   entry_date: {record.get('entry_date')}")
                        return True
                
                print(f"❌ 在MDB中未找到测试记录 (external_id: {external_id})")
                return False
            else:
                print(f"❌ MDB中没有数据")
                return False
                
        except Exception as e:
            print(f"❌ 验证MDB数据失败: {e}")
            return False
    
    async def cleanup_test_data(self, entry_id, external_id):
        """清理测试数据"""
        print("\n" + "="*80)
        print("🧹 清理测试数据")
        print("="*80)
        
        try:
            # 删除PostgreSQL记录
            if entry_id:
                result = await self.f4_service.handle_delete_operation(
                    entry_id, user_id="test_user_complete"
                )
                
                if result.get('status') == 'success':
                    print(f"✅ PostgreSQL记录删除成功")
                else:
                    print(f"❌ PostgreSQL记录删除失败: {result.get('error_message')}")
            
            # 直接删除MDB记录
            if external_id:
                try:
                    mdb_result = await self.server6_client.delete_entry(external_id)
                    if mdb_result.get('success'):
                        print(f"✅ MDB记录删除成功")
                    else:
                        print(f"❌ MDB记录删除失败: {mdb_result.get('message')}")
                except Exception as e:
                    print(f"❌ MDB记录删除异常: {e}")
                    
        except Exception as e:
            print(f"❌ 清理测试数据失败: {e}")

async def main():
    """主测试函数"""
    print("🧪 完整插入工作流测试开始")
    print("="*80)
    
    tester = CompleteInsertWorkflowTest()
    
    try:
        # 启动测试环境
        if not await tester.start():
            return
        
        # 1. 检查触发器
        triggers_ok = await tester.check_triggers()
        
        # 2. 如果需要，创建触发器
        if not triggers_ok:
            triggers_created = await tester.create_triggers_if_needed()
            if not triggers_created:
                print("❌ 触发器创建失败，测试停止")
                return
        
        # 3. 测试完整插入工作流
        workflow_result = await tester.test_insert_workflow()
        
        # 4. 总结
        print("\n" + "="*80)
        print("📋 测试总结")
        print("="*80)
        
        if workflow_result['success']:
            print("✅ 完整插入工作流测试成功!")
            print(f"✅ entry_id: {workflow_result['entry_id']}")
            print(f"✅ external_id: {workflow_result['external_id']}")
            print("✅ 触发器工作正常")
            print("✅ f1监听器工作正常")
            print("✅ f2服务工作正常")
            print("✅ 日期格式转换正确")
            print("✅ MDB同步成功")
            
            # 5. 清理测试数据
            await tester.cleanup_test_data(
                workflow_result['entry_id'], 
                workflow_result['external_id']
            )
        else:
            print("❌ 完整插入工作流测试失败!")
            print(f"❌ 错误: {workflow_result['error']}")
        
        print("\n" + "="*80)
        print("🎉 完整插入工作流测试完成")
        print("="*80)
        
    except Exception as e:
        logger.error(f"❌ 测试过程中出错: {e}")
    finally:
        await tester.stop()

if __name__ == "__main__":
    asyncio.run(main()) 