nohup: ignoring input
🔧 加载配置: config_ubuntu_remote
🐛 MySuite Server5 - 数据同步微服务 (Ubuntu远程) 运行在调试模式
📊 PostgreSQL: ************:5432
🍃 MongoDB: ************:27017
⚡ Redis: localhost:6379
💾 ODBC可用: False
📋 配置信息:
  - 配置文件: config_ubuntu_remote
  - 平台: Linux
  - 主机: test
  - IP: *************
  - 自动检测: config_ubuntu_remote
🔧 加载配置: config_ubuntu_remote
📋 配置信息:
  - 配置文件: config_ubuntu_remote
  - 平台: Linux
  - 主机: test
  - IP: *************
  - 自动检测: config_ubuntu_remote
🌐 MySuite Server5 - 纯HTTP API服务器
============================================================
🔧 模式: HTTP-only (不启动 f1-f4 微服务)
🌐 功能: 仅提供 HTTP API 接口
🔗 微服务: 需要单独启动 start_server5_notwith_api.py
============================================================
🔍 检查端口 8009 可用性...
✅ 端口8009可用
🌐 启动纯HTTP API服务器...
📡 监听地址: http://0.0.0.0:8009
📋 API文档: http://localhost:8009/docs
🔍 健康检查: http://localhost:8009/health
⚠️  注意：此模式仅启动HTTP API，不包含f1-f4微服务
🔧 如需启动微服务，请另开终端运行: python start_server5_notwith_api.py
👋 按 Ctrl+C 退出
INFO:     Started server process [1946823]
INFO:     Waiting for application startup.
🔥 当前加载的 entries_api.py 路径: /home/<USER>/Documents/testF/2025/ser25/server0613/MySuiteDatebase2402newuigood-yolo5-3/server5/app/routers/entries_api.py
🔥 当前加载的 entries_api.py 内容版本: 20250710 - HTTP-only 模式支持
🔥🔥🔥 这是优化后的 entries_api.py！🔥🔥🔥
2025-07-10 15:04:49,582 - INFO - MySuite Server5 - 数据同步微服务 (Ubuntu远程) v1.0.0 启动中 (HTTP-only 模式)...
2025-07-10 15:04:49,582 - INFO - 🌐 HTTP-only 模式: 仅启动 HTTP API，不启动微服务
2025-07-10 15:04:49,793 - INFO - ✅ PostgreSQL连接池创建成功
2025-07-10 15:04:49,793 - INFO - ✅ HTTP-only 模式下数据库连接成功
INFO:     Application startup complete.
INFO:     Uvicorn running on http://0.0.0.0:8009 (Press CTRL+C to quit)
INFO:     127.0.0.1:35108 - "GET /api/timeprotab?employee_id=215829&year=2025&month=7 HTTP/1.1" 307 Temporary Redirect
INFO:     127.0.0.1:41902 - "GET / HTTP/1.1" 200 OK
2025-07-10 15:05:16,863 - INFO - 🔗 HTTP-only 模式: 使用 service_manager 的数据库连接
INFO:     127.0.0.1:46938 - "GET /api/entries/months?employee_id=215829 HTTP/1.1" 200 OK
2025-07-10 15:05:25,619 - INFO - 📊 获取员工 215829 的部门信息
2025-07-10 15:05:25,627 - ERROR - 查询执行失败: 
            SELECT DISTINCT department
            FROM entries
            WHERE employee_id = %s
... 错误: "%"またはその近辺で構文エラー
2025-07-10 15:05:25,627 - ERROR - ❌ 获取员工部门信息失败: "%"またはその近辺で構文エラー
INFO:     127.0.0.1:35198 - "GET /api/department/employee/215829 HTTP/1.1" 200 OK
INFO:     127.0.0.1:35208 - "GET /api/timeprotab?employee_id=215829&year=2025&month=7 HTTP/1.1" 307 Temporary Redirect
INFO:     127.0.0.1:38850 - "GET /api/timeprotab?employee_id=215829&year=2025&month=7 HTTP/1.1" 307 Temporary Redirect
INFO:     127.0.0.1:45626 - "GET /api/timeprotab?employee_id=215829&year=2025&month=7 HTTP/1.1" 307 Temporary Redirect
INFO:     Shutting down
INFO:     Waiting for application shutdown.
2025-07-10 15:06:30,205 - INFO - 🌐 HTTP-only 模式: 断开数据库连接
2025-07-10 15:06:30,210 - INFO - 🔌 PostgreSQL连接已关闭
INFO:     Application shutdown complete.
INFO:     Finished server process [1946823]

📡 接收到信号 15，正在关闭HTTP服务...
