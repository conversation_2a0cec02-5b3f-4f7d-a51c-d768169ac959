# №02025/06/27 + 日本东京时间11：24 + 安静版本的Ubuntu远程启动脚本
# Server5多平台启动 - Ubuntu远程模式 (Quiet版本)

import os
import sys
import asyncio
import logging
from pathlib import Path
import uvicorn

# 1. 获取根日志记录器
root_logger = logging.getLogger()
root_logger.setLevel(logging.DEBUG)  # 根记录器捕获所有级别的日志

# 2. 清理所有旧的处理器，确保配置是干净的
for handler in root_logger.handlers[:]:
    root_logger.removeHandler(handler)

# 3. 创建终端处理器 (只显示INFO及以上级别)
console_handler = logging.StreamHandler(sys.stdout)
console_handler.setLevel(logging.INFO)
console_formatter = logging.Formatter('%(asctime)s - %(name)s - %(levelname)s - %(message)s')
console_handler.setFormatter(console_formatter)
root_logger.addHandler(console_handler)

# 4. 创建文件处理器 (记录所有DEBUG及以上级别)
log_dir = Path(__file__).parent / 'logs'
log_dir.mkdir(exist_ok=True)
file_handler = logging.FileHandler(log_dir / 'server5_quiet.log', mode='w', encoding='utf-8')
file_handler.setLevel(logging.DEBUG)
file_formatter = logging.Formatter('%(asctime)s - %(name)s - %(levelname)s - %(message)s')
file_handler.setFormatter(file_formatter)
root_logger.addHandler(file_handler)

logger = logging.getLogger(__name__)

# 添加项目路径
sys.path.insert(0, str(Path(__file__).parent))

# 导入配置选择器
from app.config_selector import ConfigSelector

def main():
    """主函数 - 仅异步启动 f1~f5 服务"""
    logger.info("🚀 MySuite Server5 - Ubuntu 远程模式 (仅启动 f1~f4 服务)")

    try:
        asyncio.run(run_services())
    except KeyboardInterrupt:
        logger.info("\n👋 接收到中断信号，正在关闭服务...")
    except Exception as e:
        logger.error(f"❌ 启动失败: {e}", exc_info=True)
        sys.exit(1)

# ------------------------------------------------------------------
# 新的异步启动逻辑

async def run_services():
    """异步启动 f1、f2、f3、f4 服务并保持运行"""

    from app.services.f1_listener import ListenerService
    from app.services.f2_push_writer import PushWriterServiceFixed
    from app.services.f3_data_puller import DataPullerService
    from app.services.f4_operation_handler import OperationHandlerService
    # 2025 07/04 +  16：00 + 相关主题: f5不再由启动脚本管理，而是由f3调用，因此移除
    # from app.services.f5_bulk_sync import DeletionSyncService

    # 实例化服务
    f1 = ListenerService()
    f2 = PushWriterServiceFixed()
    f3 = DataPullerService()
    f4 = OperationHandlerService()
    # 2025 07/04 +  16：00 + 相关主题: f5不再需要实例化
    # f5 = DeletionSyncService()

    # 启动所有服务
    results = await asyncio.gather(
        f1.start(),
        f2.start(),
        f3.start(),
        f4.start(),
        # 2025 07/04 +  16：00 + 相关主题: f5不再需要启动
        # f5.start(),
        return_exceptions=True,
    )

    # 打印启动结果
    services = ["f1_listener", "f2_push_writer", "f3_data_puller", "f4_operation_handler"] # 2025 07/04 +  16：00 + 相关主题: 从列表中移除f5
    for name, res in zip(services, results):
        status = "✅" if (isinstance(res, bool) and res) else "❌"
        logger.info(f"{status} {name} {'启动成功' if status=='✅' else '启动失败'}")

    # 如果有任何服务启动失败，直接退出
    if not all(isinstance(r, bool) and r for r in results):
        logger.error("❌ 有服务启动失败，请检查日志。即将退出...")
        await stop_services([f1, f2, f3, f4]) # 2025 07/04 +  16：00 + 相关主题: 从停止列表中移除f5
        return

    logger.info("🎉 所有服务已启动，按 Ctrl+C 退出")

    # 阻塞运行，直到收到 KeyboardInterrupt
    stop_event = asyncio.Event()
    try:
        await stop_event.wait()
    except asyncio.CancelledError:
        pass
    finally:
        await stop_services([f1, f2, f3, f4]) # 2025 07/04 +  16：00 + 相关主题: 从停止列表中移除f5


async def stop_services(service_list):
    """停止所有服务"""
    stop_coros = []
    for svc in service_list:
        if hasattr(svc, "stop"):
            stop_coros.append(svc.stop())
    await asyncio.gather(*stop_coros, return_exceptions=True)

if __name__ == "__main__":
    main() 