#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
检查entries表的结构，特别是默认值设置
"""

import asyncio
import sys
from pathlib import Path
sys.path.append(str(Path(__file__).parent))

from app.database import IMDBClient

async def check_table_structure():
    client = IMDBClient()
    await client.connect()
    
    # 检查entries表结构
    structure = await client.execute_query('''
        SELECT column_name, data_type, column_default, is_nullable
        FROM information_schema.columns
        WHERE table_name = 'entries'
        ORDER BY ordinal_position
    ''')
    
    print('entries表结构:')
    for col in structure:
        print(f'  {col["column_name"]}: {col["data_type"]}, default={col["column_default"]}, nullable={col["is_nullable"]}')
    
    await client.disconnect()

if __name__ == "__main__":
    asyncio.run(check_table_structure()) 