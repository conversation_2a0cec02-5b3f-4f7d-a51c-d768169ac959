#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
简单测试Server5更新端点
"""

import requests
import json

def test_update_endpoint():
    """测试更新端点 - 使用指定的external_id"""
    server5_base_url = "http://localhost:8009"
    target_external_id = 603682  # 使用指定的external_id
    
    print("🚀 测试Server5更新端点")
    print(f"🎯 目标external_id: {target_external_id}")
    print("="*50)
    
    try:
        # 1. 通过external_id查找记录
        print("📋 1. 通过external_id查找记录...")
        response = requests.get(
            f"{server5_base_url}/api/entries/",
            params={"limit": 1000},  # 获取更多记录以便查找
            timeout=10
        )
        
        if response.status_code != 200:
            print(f"❌ 获取记录失败: {response.status_code}")
            print(f"响应内容: {response.text}")
            return False
            
        entries = response.json()
        if not entries:
            print("❌ 没有找到记录")
            return False
        
        # 查找指定的external_id
        target_entry = None
        for entry in entries:
            if entry.get('external_id') == target_external_id:
                target_entry = entry
                break
        
        if not target_entry:
            print(f"❌ 没有找到external_id={target_external_id}的记录")
            print(f"可用的external_id: {[e.get('external_id') for e in entries[:5]]}")
            return False
        
        internal_id = target_entry.get('id')
        external_id = target_entry.get('external_id')
        
        print(f"✅ 找到目标记录: internal_id={internal_id}, external_id={external_id}")
        print(f"📊 当前数据: duration={target_entry.get('duration')}, model={target_entry.get('model')}")
        
        # 2. 准备更新数据
        update_data = {
            "duration": 9.0,
            "description": "测试更新",
            "project_code": "TEST_UPDATE",
            "status": "2",  # 字符串格式，会被转换为整数
            "notes": "号机:TEST001 工场製番:TEST_FACTORY 工事番号:TEST_PROJECT ユニット番号:TEST_UNIT",
            "source": "user"
        }
        
        print(f"📝 更新数据: {json.dumps(update_data, indent=2, ensure_ascii=False)}")
        
        # 3. 发送更新请求 - 使用external_id
        print("📤 3. 发送更新请求...")
        print(f"🔗 请求URL: {server5_base_url}/api/entries/{external_id}")
        update_response = requests.put(
            f"{server5_base_url}/api/entries/{external_id}",
            json=update_data,
            timeout=10
        )
        
        print(f"状态码: {update_response.status_code}")
        print(f"响应头: {dict(update_response.headers)}")
        
        if update_response.status_code == 200:
            result = update_response.json()
            print(f"✅ 更新成功: {json.dumps(result, indent=2, ensure_ascii=False)}")
            
            # 4. 验证更新结果
            print("🔍 4. 验证更新结果...")
            verify_response = requests.get(
                f"{server5_base_url}/api/entries/{external_id}",
                timeout=10
            )
            
            if verify_response.status_code == 200:
                updated_entry = verify_response.json()
                print(f"✅ 验证成功:")
                print(f"  - duration: {updated_entry.get('duration')}")
                print(f"  - model: {updated_entry.get('model')}")
                print(f"  - project_number: {updated_entry.get('project_number')}")
                print(f"  - category: {updated_entry.get('category')}")
            else:
                print(f"⚠️ 验证失败: {verify_response.status_code}")
            
            return True
        else:
            print(f"❌ 更新失败: {update_response.status_code}")
            print(f"错误响应: {update_response.text}")
            return False
            
    except Exception as e:
        print(f"❌ 测试异常: {e}")
        return False

if __name__ == "__main__":
    success = test_update_endpoint()
    if success:
        print("\n🎉 测试通过！")
    else:
        print("\n❌ 测试失败！") 