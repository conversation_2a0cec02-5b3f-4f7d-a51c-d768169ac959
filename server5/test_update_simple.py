#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
简化的UPDATE测试 - 直接测试每个环节
"""

import asyncio
import sys
from pathlib import Path
import logging
import aiohttp

# 添加项目路径
sys.path.append(str(Path(__file__).parent))

from app.database import IMDBClient
from app.utils.server6_client import Server6Client

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

async def step1_test_server6_health():
    """步骤1: 测试Server6健康状态"""
    print("\n🔍 步骤1: 测试Server6健康状态")
    
    try:
        # 直接HTTP请求测试
        async with aiohttp.ClientSession() as session:
            # 测试健康检查
            async with session.get('http://192.168.3.93:8009/health') as response:
                if response.status == 200:
                    health_data = await response.json()
                    print(f"✅ Server6健康检查成功: {health_data}")
                    return True
                else:
                    print(f"❌ Server6健康检查失败: {response.status}")
                    return False
    except Exception as e:
        print(f"❌ Server6连接失败: {e}")
        return False

async def step2_test_mdb_connection():
    """步骤2: 测试MDB连接"""
    print("\n🔍 步骤2: 测试MDB连接")
    
    try:
        async with aiohttp.ClientSession() as session:
            async with session.get('http://192.168.3.93:8009/mdb/test') as response:
                if response.status == 200:
                    mdb_data = await response.json()
                    print(f"✅ MDB连接测试成功: {mdb_data}")
                    return True
                else:
                    print(f"❌ MDB连接测试失败: {response.status}")
                    return False
    except Exception as e:
        print(f"❌ MDB连接测试失败: {e}")
        return False

async def step3_test_direct_update():
    """步骤3: 直接测试UPDATE API"""
    print("\n🔍 步骤3: 直接测试UPDATE API")
    
    try:
        # 测试数据
        test_data = {
            'employee_id': '215829',
            'entry_date': '2025/06/26',
            'model': '',
            'number': '',
            'factory_number': '',
            'project_number': '24585',
            'unit_number': '',
            'category': 3,
            'item': 7,
            'duration': 6.0,
            'department': '131'
        }
        
        async with aiohttp.ClientSession() as session:
            # 直接调用UPDATE API
            external_id = 602610
            url = f'http://192.168.3.93:8009/mdb/entries/update/{external_id}'
            
            async with session.put(url, json=test_data) as response:
                if response.status == 200:
                    result = await response.json()
                    print(f"✅ 直接UPDATE API成功: {result}")
                    return True
                else:
                    error_text = await response.text()
                    print(f"❌ 直接UPDATE API失败: {response.status} - {error_text}")
                    return False
    except Exception as e:
        print(f"❌ 直接UPDATE API测试失败: {e}")
        return False

async def step4_test_queue_generation():
    """步骤4: 测试队列生成"""
    print("\n🔍 步骤4: 测试队列生成")
    
    imdb_client = IMDBClient()
    
    try:
        await imdb_client.connect()
        
        # 目标记录
        target_entry_id = 10612
        
        # 检查记录状态
        entry_info = await imdb_client.execute_query(
            "SELECT id, duration, external_id, source FROM entries WHERE id = $1",
            target_entry_id
        )
        
        if entry_info:
            entry = entry_info[0]
            print(f"📊 记录状态:")
            print(f"  - ID: {entry['id']}")
            print(f"  - duration: {entry['duration']}")
            print(f"  - external_id: {entry['external_id']}")
            print(f"  - source: {entry['source']}")
            
            if entry['external_id'] is None:
                print("❌ 记录缺少external_id，无法执行UPDATE操作")
                return False
        else:
            print(f"❌ 找不到记录: entry_id={target_entry_id}")
            return False
        
        # 清理之前的队列项
        await imdb_client.execute_command(
            "DELETE FROM entries_push_queue WHERE entry_id = $1",
            target_entry_id
        )
        
        # 执行UPDATE操作
        print("📝 执行UPDATE操作...")
        async with imdb_client.pool.acquire() as conn:
            async with conn.transaction():
                await conn.execute(
                    "UPDATE entries SET duration = $1, source = 'test' WHERE id = $2",
                    6.00, target_entry_id
                )
        
        # 检查队列项是否生成
        queue_items = await imdb_client.execute_query(
            "SELECT queue_id, entry_id, operation, synced, created_ts FROM entries_push_queue WHERE entry_id = $1 ORDER BY created_ts DESC LIMIT 1",
            target_entry_id
        )
        
        if queue_items:
            item = queue_items[0]
            print(f"✅ 队列项生成成功: queue_id={item['queue_id']}, operation={item['operation']}, synced={item['synced']}")
            return True
        else:
            print("❌ 队列项未生成")
            return False
        
    except Exception as e:
        print(f"❌ 队列生成测试失败: {e}")
        return False
    finally:
        await imdb_client.disconnect()

async def step5_test_f2_processing():
    """步骤5: 测试F2处理"""
    print("\n🔍 步骤5: 测试F2处理")
    
    try:
        from app.services.f2_push_writer_fixed import PushWriterServiceFixed
        f2_service = PushWriterServiceFixed()
        
        # 启动F2服务
        success = await f2_service.start()
        if not success:
            print("❌ F2服务启动失败")
            return False
        
        print("✅ F2服务启动成功")
        
        # 等待一段时间让F2处理队列
        print("⏳ 等待F2处理队列...")
        await asyncio.sleep(10)
        
        # 检查队列状态
        imdb_client = IMDBClient()
        await imdb_client.connect()
        
        queue_items = await imdb_client.execute_query(
            "SELECT queue_id, entry_id, operation, synced, created_ts FROM entries_push_queue WHERE entry_id = $1 ORDER BY created_ts DESC LIMIT 1",
            10612
        )
        
        if queue_items:
            item = queue_items[0]
            if item['synced']:
                print(f"✅ F2处理成功: queue_id={item['queue_id']}, synced={item['synced']}")
                result = True
            else:
                print(f"❌ F2处理失败: queue_id={item['queue_id']}, synced={item['synced']}")
                result = False
        else:
            print("❌ 找不到队列项")
            result = False
        
        await imdb_client.disconnect()
        await f2_service.stop()
        
        return result
        
    except Exception as e:
        print(f"❌ F2处理测试失败: {e}")
        return False

async def main():
    """主测试函数"""
    print("🧪 开始简化UPDATE测试...")
    print("=" * 60)
    
    # 逐步测试
    step1_ok = await step1_test_server6_health()
    if not step1_ok:
        print("❌ 步骤1失败，Server6可能未启动")
        return
    
    step2_ok = await step2_test_mdb_connection()
    if not step2_ok:
        print("❌ 步骤2失败，MDB连接有问题")
        return
    
    step3_ok = await step3_test_direct_update()
    if not step3_ok:
        print("❌ 步骤3失败，Server6 UPDATE API有问题")
        return
    
    step4_ok = await step4_test_queue_generation()
    if not step4_ok:
        print("❌ 步骤4失败，队列生成有问题")
        return
    
    step5_ok = await step5_test_f2_processing()
    if not step5_ok:
        print("❌ 步骤5失败，F2处理有问题")
        return
    
    print("\n" + "=" * 60)
    print("✅ 所有步骤测试通过！")
    print("如果MDB仍未更新，请检查:")
    print("1. Server6是否在正确的Windows机器上运行")
    print("2. MDB文件路径是否正确")
    print("3. 是否有权限访问MDB文件")

if __name__ == "__main__":
    asyncio.run(main()) 