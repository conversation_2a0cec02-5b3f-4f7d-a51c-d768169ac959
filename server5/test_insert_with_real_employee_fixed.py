#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
test_insert_with_real_employee_fixed.py
使用真实员工ID格式进行插入测试 - 修复数据类型问题
"""

import asyncio
import sys
import os
from pathlib import Path
from datetime import datetime, date
import logging

# 添加项目根目录到Python路径
sys.path.append(str(Path(__file__).parent))

from app.services.f4_operation_handler import OperationHandlerService
from app.database import IMDBClient
from app.utils.server6_client import Server6Client

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)

logger = logging.getLogger(__name__)

class RealEmployeeInsertTestFixed:
    """使用真实员工ID的插入测试类（修复版本）"""
    
    def __init__(self):
        self.f4_service = OperationHandlerService()
        self.imdb_client = IMDBClient()
        self.server6_client = Server6Client()
        
        # 使用真实的员工ID和测试数据（修复数据类型）
        self.test_data = {
            'employee_id': '215829',  # 真实员工ID格式
            'entry_date': '2025/06/30',  # 今天
            'model': 'TEST_MODEL',
            'number': '123',
            'factory_number': 'F001',
            'project_number': '24585',  # 从写入例子中获取
            'unit_number': 'U001',
            'category': 3,  # 修复：使用整数
            'item': 7,      # 修复：使用整数
            'duration': 9.0,  # 从写入例子中获取
            'department': '131'  # 从写入例子中获取
        }
        
    async def start(self):
        """启动测试环境"""
        try:
            await self.f4_service.start()
            await self.imdb_client.connect()
            logger.info("✅ 测试环境启动成功")
            return True
        except Exception as e:
            logger.error(f"❌ 测试环境启动失败: {e}")
            return False
    
    async def stop(self):
        """停止测试环境"""
        try:
            await self.f4_service.stop()
            await self.imdb_client.disconnect()
            await self.server6_client.disconnect()
            logger.info("✅ 测试环境已停止")
        except Exception as e:
            logger.error(f"❌ 测试环境停止失败: {e}")
    
    async def test_insert_operation(self):
        """测试插入操作"""
        print("\n" + "="*80)
        print("📝 测试使用真实员工ID的插入操作（修复版本）")
        print("="*80)
        
        print(f"📋 测试数据:")
        for key, value in self.test_data.items():
            print(f"   {key}: {value} (类型: {type(value).__name__})")
        
        try:
            print(f"\n🔄 执行插入操作...")
            start_time = datetime.now()
            
            # 执行插入操作
            result = await self.f4_service.handle_insert_operation(
                self.test_data, user_id="test_user_real_fixed"
            )
            
            end_time = datetime.now()
            duration = (end_time - start_time).total_seconds()
            
            print(f"✅ 插入操作完成，耗时: {duration:.2f} 秒")
            print(f"📊 插入结果: {result}")
            
            # 检查结果
            if result.get('status') == 'success':
                external_id = result.get('external_id')
                entry_id = result.get('entry_id')
                
                if external_id:
                    print(f"✅ 成功获得MDB external_id: {external_id}")
                    print(f"✅ PostgreSQL entry_id: {entry_id}")
                    return {
                        'success': True,
                        'external_id': external_id,
                        'entry_id': entry_id,
                        'result': result
                    }
                else:
                    print(f"❌ 插入成功但没有返回external_id!")
                    print(f"❌ 这表示数据没有成功写入MDB")
                    return {
                        'success': False,
                        'error': 'No external_id returned',
                        'result': result
                    }
            else:
                error_msg = result.get('error_message', 'Unknown error')
                print(f"❌ 插入操作失败: {error_msg}")
                return {
                    'success': False,
                    'error': error_msg,
                    'result': result
                }
                
        except Exception as e:
            print(f"❌ 插入操作异常: {e}")
            return {
                'success': False,
                'error': str(e)
            }
    
    async def verify_postgresql_data(self, entry_id):
        """验证PostgreSQL中的数据"""
        print("\n" + "="*80)
        print("📊 验证PostgreSQL中的数据")
        print("="*80)
        
        try:
            # 查询刚插入的记录
            entry = await self.imdb_client.fetch_one(
                "SELECT * FROM entries WHERE id = $1", entry_id
            )
            
            if entry:
                print(f"✅ 在PostgreSQL中找到记录:")
                for key, value in entry.items():
                    if value is not None:
                        print(f"   {key}: {value} (类型: {type(value).__name__})")
                
                # 检查external_id
                if entry.get('external_id'):
                    print(f"✅ external_id存在: {entry['external_id']}")
                    return True
                else:
                    print(f"❌ external_id为空!")
                    return False
            else:
                print(f"❌ 在PostgreSQL中未找到记录")
                return False
                
        except Exception as e:
            print(f"❌ 验证PostgreSQL数据失败: {e}")
            return False
    
    async def verify_mdb_data(self, external_id):
        """验证MDB中的数据"""
        print("\n" + "="*80)
        print("📊 验证MDB中的数据")
        print("="*80)
        
        try:
            # 查询特定日期范围的数据
            start_date = date(2025, 6, 30)
            end_date = date(2025, 6, 30)
            
            print(f"📅 查询日期范围: {start_date} 到 {end_date}")
            
            # 查询MDB数据
            records = await self.server6_client.query_bulk_fast(start_date, end_date)
            
            if records:
                print(f"✅ MDB中有 {len(records)} 条记录")
                
                # 查找我们的测试记录
                test_record = None
                for record in records:
                    if (record.get('external_id') == external_id and 
                        record.get('employee_id') == self.test_data['employee_id']):
                        test_record = record
                        break
                
                if test_record:
                    print(f"✅ 在MDB中找到测试记录:")
                    for key, value in test_record.items():
                        if value is not None:
                            print(f"   {key}: {value}")
                    return True
                else:
                    print(f"❌ 在MDB中未找到测试记录 (external_id: {external_id})")
                    print(f"🔍 检查所有记录的external_id:")
                    external_ids = [r.get('external_id') for r in records if r.get('external_id')]
                    print(f"   找到的external_id: {external_ids[:10]}...")
                    return False
            else:
                print(f"❌ MDB中没有数据")
                return False
                
        except Exception as e:
            print(f"❌ 验证MDB数据失败: {e}")
            return False
    
    async def check_queue_status(self, entry_id):
        """检查队列状态"""
        print("\n" + "="*80)
        print("📋 检查队列状态")
        print("="*80)
        
        try:
            # 查询队列项
            queue_items = await self.imdb_client.fetch_all("""
                SELECT queue_id, entry_id, external_id, operation, synced, created_ts
                FROM entries_push_queue 
                WHERE entry_id = $1
                ORDER BY queue_id DESC
            """, entry_id)
            
            if queue_items:
                print(f"✅ 找到 {len(queue_items)} 个队列项:")
                for item in queue_items:
                    status = "✅ 已同步" if item['synced'] else "❌ 未同步"
                    print(f"   队列ID: {item['queue_id']}, 操作: {item['operation']}, "
                          f"external_id: {item['external_id']}, {status}")
            else:
                print("❌ 没有找到队列项")
                
        except Exception as e:
            print(f"❌ 检查队列状态失败: {e}")
    
    async def cleanup_test_data(self, entry_id, external_id):
        """清理测试数据"""
        print("\n" + "="*80)
        print("🧹 清理测试数据")
        print("="*80)
        
        try:
            # 删除PostgreSQL记录
            if entry_id:
                result = await self.f4_service.handle_delete_operation(
                    entry_id, user_id="test_user_real_fixed"
                )
                
                if result.get('status') == 'success':
                    print(f"✅ PostgreSQL记录删除成功")
                else:
                    print(f"❌ PostgreSQL记录删除失败: {result.get('error_message')}")
            
            # 直接删除MDB记录
            if external_id:
                try:
                    mdb_result = await self.server6_client.delete_entry(external_id)
                    if mdb_result.get('success'):
                        print(f"✅ MDB记录删除成功")
                    else:
                        print(f"❌ MDB记录删除失败: {mdb_result.get('message')}")
                except Exception as e:
                    print(f"❌ MDB记录删除异常: {e}")
                    
        except Exception as e:
            print(f"❌ 清理测试数据失败: {e}")

async def main():
    """主测试函数"""
    print("🧪 使用真实员工ID的插入测试开始（修复版本）")
    print("="*80)
    
    tester = RealEmployeeInsertTestFixed()
    
    try:
        # 启动测试环境
        if not await tester.start():
            return
        
        # 1. 测试插入操作
        insert_result = await tester.test_insert_operation()
        
        if not insert_result['success']:
            print(f"\n❌ 插入测试失败: {insert_result['error']}")
            print("🛑 测试停止")
            return
        
        external_id = insert_result['external_id']
        entry_id = insert_result['entry_id']
        
        # 2. 验证PostgreSQL数据
        pg_ok = await tester.verify_postgresql_data(entry_id)
        
        # 3. 验证MDB数据
        mdb_ok = await tester.verify_mdb_data(external_id)
        
        # 4. 检查队列状态
        await tester.check_queue_status(entry_id)
        
        # 5. 总结
        print("\n" + "="*80)
        print("📋 测试总结")
        print("="*80)
        
        if pg_ok and mdb_ok:
            print("✅ 测试完全成功!")
            print("✅ PostgreSQL数据正确")
            print("✅ MDB数据正确")
            print("✅ external_id正确返回")
        else:
            print("❌ 测试部分失败!")
            if not pg_ok:
                print("❌ PostgreSQL数据验证失败")
            if not mdb_ok:
                print("❌ MDB数据验证失败")
        
        # 6. 清理测试数据
        await tester.cleanup_test_data(entry_id, external_id)
        
        print("\n" + "="*80)
        print("🎉 使用真实员工ID的插入测试完成（修复版本）")
        print("="*80)
        
    except Exception as e:
        logger.error(f"❌ 测试过程中出错: {e}")
    finally:
        await tester.stop()

if __name__ == "__main__":
    asyncio.run(main()) 