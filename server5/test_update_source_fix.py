#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试UPDATE操作后source字段是否正确更新为system
"""

import asyncio
import sys
from pathlib import Path
sys.path.append(str(Path(__file__).parent))

from app.database import IMDBClient
from app.utils.server6_client import Server6Client

async def test_update_source_fix():
    """测试UPDATE操作后source字段是否正确更新为system"""
    imdb_client = IMDBClient()
    server6_client = Server6Client()
    
    try:
        await imdb_client.connect()
        
        # 1. 创建一个测试记录
        print("1. 创建测试记录...")
        test_data = {
            "entry_date": "2025-07-15",
            "employee_id": "215829",
            "duration": 1.0,
            "model": "test_model",
            "number": "test_number",
            "factory_number": "test_factory",
            "project_number": "test_project",
            "unit_number": "test_unit",
            "category": "1",
            "item": "1",
            "department": "131",
            "source": "user"
        }
        
        # 插入测试记录
        result = await imdb_client.execute_query("""
            INSERT INTO entries (
                entry_date, employee_id, duration, model, number,
                factory_number, project_number, unit_number,
                category, item, department, source, ts
            ) VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9, $10, $11, $12, NOW())
            RETURNING id
        """, 
            test_data["entry_date"],
            test_data["employee_id"],
            test_data["duration"],
            test_data["model"],
            test_data["number"],
            test_data["factory_number"],
            test_data["project_number"],
            test_data["unit_number"],
            test_data["category"],
            test_data["item"],
            test_data["department"],
            test_data["source"]
        )
        
        entry_id = result[0]['id']
        print(f"   测试记录已创建: entry_id={entry_id}")
        
        # 2. 检查队列项是否创建
        print("2. 检查队列项...")
        queue_items = await imdb_client.execute_query("""
            SELECT queue_id, entry_id, operation, synced
            FROM entries_push_queue
            WHERE entry_id = $1
            ORDER BY created_ts DESC
        """, entry_id)
        
        if queue_items:
            queue_id = queue_items[0]['queue_id']
            print(f"   队列项已创建: queue_id={queue_id}, operation={queue_items[0]['operation']}")
        else:
            print("   ❌ 队列项未创建")
            return
        
        # 3. 等待f2推送服务处理（模拟）
        print("3. 等待f2推送服务处理...")
        await asyncio.sleep(5)
        
        # 4. 检查记录状态
        print("4. 检查记录状态...")
        entry_status = await imdb_client.execute_query("""
            SELECT id, source, external_id
            FROM entries
            WHERE id = $1
        """, entry_id)
        
        if entry_status:
            entry = entry_status[0]
            print(f"   记录状态: source={entry['source']}, external_id={entry['external_id']}")
            
            if entry['external_id']:
                external_id = entry['external_id']
                
                # 5. 执行UPDATE操作
                print("5. 执行UPDATE操作...")
                update_data = {
                    "employee_id": "215829",
                    "entry_date": "2025-07-15",
                    "model": "updated_model",
                    "number": "updated_number",
                    "factory_number": "updated_factory",
                    "project_number": "updated_project",
                    "unit_number": "updated_unit",
                    "category": "2",
                    "item": "2",
                    "duration": 2.0,
                    "department": "131"
                }
                
                # 更新记录
                await imdb_client.execute_query("""
                    UPDATE entries SET
                        model = $1, number = $2, factory_number = $3,
                        project_number = $4, unit_number = $5, category = $6,
                        item = $7, duration = $8, source = 'user'
                    WHERE id = $9
                """, 
                    update_data["model"],
                    update_data["number"],
                    update_data["factory_number"],
                    update_data["project_number"],
                    update_data["unit_number"],
                    update_data["category"],
                    update_data["item"],
                    update_data["duration"],
                    entry_id
                )
                
                print(f"   记录已更新: entry_id={entry_id}")
                
                # 6. 检查UPDATE队列项
                print("6. 检查UPDATE队列项...")
                update_queue_items = await imdb_client.execute_query("""
                    SELECT queue_id, entry_id, operation, synced, external_id
                    FROM entries_push_queue
                    WHERE entry_id = $1 AND operation = 'UPDATE'
                    ORDER BY created_ts DESC
                """, entry_id)
                
                if update_queue_items:
                    update_queue_id = update_queue_items[0]['queue_id']
                    print(f"   UPDATE队列项已创建: queue_id={update_queue_id}")
                    
                    # 7. 等待f2推送服务处理UPDATE
                    print("7. 等待f2推送服务处理UPDATE...")
                    await asyncio.sleep(5)
                    
                    # 8. 检查最终状态
                    print("8. 检查最终状态...")
                    final_status = await imdb_client.execute_query("""
                        SELECT id, source, external_id, model, category, item
                        FROM entries
                        WHERE id = $1
                    """, entry_id)
                    
                    if final_status:
                        final_entry = final_status[0]
                        print(f"   最终状态: source={final_entry['source']}, external_id={final_entry['external_id']}")
                        print(f"   字段值: model={final_entry['model']}, category={final_entry['category']}, item={final_entry['item']}")
                        
                        if final_entry['source'] == 'system':
                            print("   ✅ UPDATE操作后source字段已正确更新为'system'")
                        else:
                            print(f"   ❌ UPDATE操作后source字段仍为'{final_entry['source']}'")
                    else:
                        print("   ❌ 无法获取最终状态")
                else:
                    print("   ❌ UPDATE队列项未创建")
            else:
                print("   ❌ 记录未获得external_id，无法测试UPDATE")
        else:
            print("   ❌ 无法获取记录状态")
        
        # 9. 清理测试数据
        print("9. 清理测试数据...")
        await imdb_client.execute_query("DELETE FROM entries_push_queue WHERE entry_id = $1", entry_id)
        await imdb_client.execute_query("DELETE FROM entries WHERE id = $1", entry_id)
        print("   测试数据已清理")
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
    finally:
        await imdb_client.disconnect()

if __name__ == "__main__":
    asyncio.run(test_update_source_fix()) 