# 🗑️ f5员工删除同步功能说明

## 🎯 **功能概述**

f5员工删除同步功能用于对比指定员工在指定时间段内的MDB和PostgreSQL记录，找出并删除在MDB中已不存在的PostgreSQL记录，确保数据一致性。

## 🔧 **新增功能**

### 1. 新增函数

**文件**: `server5/app/services/f5_bulk_sync.py`

```python
async def run_deletion_sync_for_employee(self, employee_id: str, start_date: datetime.date, end_date: datetime.date):
    """
    为指定员工执行删除同步
    对比MDB和PostgreSQL在指定时间范围内指定员工的记录，
    删除在PostgreSQL中存在但在MDB中已不存在的记录。
    """
```

### 2. 新增数据库函数

**文件**: `server5/app/database/postgresql_client.py`

```python
async def get_external_ids_in_range_for_employee(self, employee_id: str, start_date: datetime.date, end_date: datetime.date) -> set:
    """获取指定员工在指定日期范围内所有唯一的、非空的 external_id"""

async def delete_entries_by_external_ids_system(self, ids: List[int]) -> int:
    """根据 external_id 列表批量删除记录（系统操作，不改变source属性）"""
```

## 📋 **操作逻辑**

### 1. 数据获取
- **MDB数据**: 通过Server6 API获取指定员工在指定时间段内的所有记录
- **PostgreSQL数据**: 从entries表获取相同员工在相同时间段内的所有external_id

### 2. 数据对比
- 提取MDB记录中的`ID`字段（external_id）
- 提取PostgreSQL记录中的`external_id`字段
- 计算差集：`PostgreSQL的external_id - MDB的ID`

### 3. 删除操作
- 删除在PostgreSQL中存在但在MDB中不存在的记录
- 使用系统删除模式，不改变source属性
- 禁用触发器避免触发f2同步

## 🧪 **测试代码**

### 测试文件
`server5/test_f5_employee_deletion_sync.py`

### 使用方法
```bash
cd server5
python test_f5_employee_deletion_sync.py
```

### 测试参数
- **员工ID**: 215829
- **开始日期**: 2025/07/01
- **结束日期**: 2025/07/16

## 📊 **执行流程**

```
1. 初始化f5服务
   ↓
2. 连接数据库（PostgreSQL、Redis、MongoDB、Server6）
   ↓
3. 从MDB获取指定员工在指定时间范围内的记录
   ↓
4. 从PostgreSQL获取相同员工在相同时间范围内的external_id
   ↓
5. 计算差集，找出需要删除的记录
   ↓
6. 执行系统删除操作（不改变source属性）
   ↓
7. 记录操作日志到MongoDB
   ↓
8. 验证删除结果
   ↓
9. 清理资源
```

## 🔍 **验证方法**

### 1. 日志检查
- 查看终端输出的详细日志
- 确认MDB和PostgreSQL的记录数量
- 验证删除操作是否成功

### 2. 数据库检查
```sql
-- 检查PostgreSQL中的记录
SELECT id, external_id, entry_date, employee_id, source, ts
FROM entries 
WHERE employee_id = '215829'
  AND entry_date BETWEEN '2025-07-01' AND '2025-07-16'
ORDER BY entry_date DESC, ts DESC;
```

### 3. 结果分析
- **数据一致**: PostgreSQL记录数 = MDB记录数
- **需要再次同步**: PostgreSQL记录数 > MDB记录数
- **正常情况**: MDB记录数 > PostgreSQL记录数

## ⚠️ **注意事项**

### 1. 安全性
- 删除操作是**不可逆**的
- 建议在执行前备份重要数据
- 测试环境验证后再在生产环境使用

### 2. 性能考虑
- 大量数据时可能需要较长时间
- 建议在系统负载较低时执行
- 可以分批处理大量数据

### 3. 数据一致性
- 确保Server6服务正常运行
- 确保MDB数据库可访问
- 确保网络连接稳定

## 🚀 **使用示例**

### 基本使用
```python
from app.services.f5_bulk_sync import DeletionSyncService
from datetime import date

# 创建服务实例
f5_service = DeletionSyncService()
await f5_service.start()

# 执行员工删除同步
await f5_service.run_deletion_sync_for_employee(
    employee_id="215829",
    start_date=date(2025, 7, 1),
    end_date=date(2025, 7, 16)
)

# 清理资源
await f5_service.stop()
```

### 自定义参数
```python
# 可以修改测试参数
test_employee_id = "215829"  # 员工ID
test_start_date = date(2025, 7, 1)  # 开始日期
test_end_date = date(2025, 7, 16)   # 结束日期
```

## 📝 **日志示例**

```
2025-07-15 14:45:00,123 - INFO - 🚀 初始化f5删除同步服务...
2025-07-15 14:45:00,456 - INFO - ✅ f5服务启动成功
2025-07-15 14:45:00,789 - INFO - --- 开始执行f5删除同步任务 (员工: 215829) ---
2025-07-15 14:45:01,012 - INFO - 删除同步范围: 员工 215829, 从 2025-07-01 到 2025-07-16
2025-07-15 14:45:01,345 - INFO - 在MDB中找到员工 215829 的 15 个唯一的 external_id。
2025-07-15 14:45:01,678 - INFO - 在PostgreSQL中找到员工 215829 的 18 个唯一的 external_id。
2025-07-15 14:45:01,901 - WARNING - 发现员工 215829 的 3 条需要从PostgreSQL删除的记录。
2025-07-15 14:45:02,134 - INFO - 成功执行删除操作，影响了 3 条记录。
2025-07-15 14:45:02,367 - INFO - --- f5员工删除同步任务完成，耗时 1.24 秒。本次共删除 3 条记录 ---
```

## ✅ **总结**

f5员工删除同步功能提供了精确的数据清理能力，确保PostgreSQL和MDB之间的数据一致性。通过指定员工ID和时间段，可以安全地删除在MDB中已不存在的PostgreSQL记录，同时保持source属性不变。 