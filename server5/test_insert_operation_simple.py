#!/usr/bin/env python3
"""
简化插入操作测试 - 验证从entries插入到MDB同步的完整流程
手动触发f2服务处理队列，确保整个流程正常工作
"""

import sys
import os
import asyncio
import json
from datetime import datetime, date
import time
import logging

# 添加项目根目录到Python路径
sys.path.append(os.path.dirname(os.path.dirname(__file__)))

from app.services.f1_listener import ListenerService
from app.services.f2_push_writer import PushWriterService
from app.database.postgresql_client import IMDBClient
from app.database.redis_client import RedisClient
from app.utils.server6_client import Server6Client
from config.config import PlatformConfig

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

class SimpleInsertTest:
    def __init__(self):
        self.config = PlatformConfig.get_config()
        self.redis_client = RedisClient()
        self.postgresql_client = IMDBClient()
        self.server6_client = Server6Client()
        
        # 初始化服务
        self.f1_service = ListenerService()
        self.f2_service = PushWriterService()
        
        # 真实测试数据（来自写入例子.md）
        self.test_data = {
            'employee_id': '215829',
            'date': '2025/06/26',  # MDB格式：YYYY/MM/DD
            'model': '',  # 機種
            'number': '',  # 号機
            'factory_number': '',  # 工場製番
            'project_number': '24585',  # 工事番号
            'unit_number': '',  # ﾕﾆｯﾄ番号
            'category': 3,  # 区分
            'item': 7,  # 項目
            'time': 9,  # 時間
            'department': '131'  # 所属ｺｰﾄﾞ
        }
    
    async def setup(self):
        """初始化测试环境"""
        print("🔧 初始化测试环境...")
        
        try:
            # 连接数据库
            await self.redis_client.connect()
            await self.postgresql_client.connect()
            await self.server6_client.connect()
            
            # 清理测试数据
            await self._cleanup_test_data()
            
            print("✅ 测试环境初始化完成")
            return True
            
        except Exception as e:
            print(f"❌ 测试环境初始化失败: {e}")
            return False
    
    async def cleanup(self):
        """清理测试环境"""
        print("🧹 清理测试环境...")
        
        try:
            # 停止服务
            await self.f1_service.stop()
            await self.f2_service.stop()
            
            # 清理测试数据
            await self._cleanup_test_data()
            
            # 断开连接
            await self.redis_client.disconnect()
            await self.postgresql_client.disconnect()
            await self.server6_client.disconnect()
            
            print("✅ 测试环境清理完成")
            
        except Exception as e:
            print(f"❌ 测试环境清理失败: {e}")
    
    async def _cleanup_test_data(self):
        """清理测试数据"""
        try:
            # 删除测试员工的数据
            await self.postgresql_client.execute_query(
                "DELETE FROM entries WHERE employee_id = $1",
                self.test_data['employee_id']
            )
            
            # 清理队列
            await self.postgresql_client.execute_query(
                "DELETE FROM entries_push_queue WHERE entry_id IN (SELECT id FROM entries WHERE employee_id = $1)",
                self.test_data['employee_id']
            )
            
            print("🧹 测试数据清理完成")
            
        except Exception as e:
            print(f"⚠️ 清理测试数据时出错: {e}")
    
    async def test1_insert_to_entries(self):
        """测试1：插入数据到entries表"""
        print("\n=== 测试1：插入数据到entries表 ===")
        
        try:
            # 准备entries数据（PostgreSQL格式：YYYY-MM-DD）
            entry_date_pg = datetime.strptime(self.test_data['date'], '%Y/%m/%d').date()
            
            entry_data = {
                'employee_id': self.test_data['employee_id'],
                'entry_date': entry_date_pg,  # PostgreSQL格式
                'model': self.test_data['model'] or None,
                'number': self.test_data['number'] or None,
                'factory_number': self.test_data['factory_number'] or None,
                'project_number': self.test_data['project_number'] or None,
                'unit_number': self.test_data['unit_number'] or None,
                'category': self.test_data['category'],
                'item': self.test_data['item'],
                'duration': float(self.test_data['time']),
                'department': self.test_data['department']
            }
            
            print(f"📝 准备插入数据:")
            print(f"   employee_id: {entry_data['employee_id']}")
            print(f"   entry_date (PostgreSQL): {entry_data['entry_date']} (YYYY-MM-DD)")
            print(f"   project_number: {entry_data['project_number']}")
            print(f"   category: {entry_data['category']}")
            print(f"   item: {entry_data['item']}")
            print(f"   duration: {entry_data['duration']}")
            print(f"   department: {entry_data['department']}")
            
            # 插入到entries表
            entry_id = await self.postgresql_client.create_entry(entry_data, source='system')
            
            if not entry_id:
                print("❌ entries插入失败")
                return None
            
            print(f"✅ entries插入成功: entry_id={entry_id}")
            
            # 验证插入结果
            inserted_entry = await self.postgresql_client.execute_query(
                "SELECT id, external_id, employee_id, entry_date, project_number, category, item, duration, department FROM entries WHERE id = $1",
                entry_id
            )
            
            if inserted_entry:
                entry = inserted_entry[0]
                print(f"📊 插入后entries数据:")
                print(f"   id: {entry['id']}")
                print(f"   external_id: {entry['external_id']} (应该为NULL)")
                print(f"   employee_id: {entry['employee_id']}")
                print(f"   entry_date: {entry['entry_date']} (PostgreSQL格式)")
                
                # 验证external_id为NULL
                if entry['external_id'] is None:
                    print("✅ external_id正确为NULL")
                else:
                    print("❌ external_id不应该有值")
                    return None
                
                # 验证日期格式
                if str(entry['entry_date']) == '2025-06-26':
                    print("✅ 日期格式正确 (YYYY-MM-DD)")
                else:
                    print(f"❌ 日期格式错误: {entry['entry_date']}")
                    return None
                
                return entry_id
            else:
                print("❌ 无法获取插入的数据")
                return None
                
        except Exception as e:
            print(f"❌ entries插入测试失败: {e}")
            return None
    
    async def test2_check_queue_creation(self):
        """测试2：检查队列项创建"""
        print("\n=== 测试2：检查队列项创建 ===")
        
        try:
            # 等待触发器执行
            await asyncio.sleep(2)
            
            # 检查队列状态
            queue_items = await self.postgresql_client.execute_query(
                "SELECT * FROM entries_push_queue WHERE synced = FALSE ORDER BY created_ts DESC"
            )
            
            print(f"📋 未同步队列项数量: {len(queue_items)}")
            
            if queue_items:
                for item in queue_items:
                    print(f"📋 队列项: ID={item['queue_id']}, 操作={item['operation']}, entry_id={item['entry_id']}, synced={item['synced']}")
                
                # 验证队列项存在
                if len(queue_items) > 0:
                    print("✅ 队列项已创建，触发器工作正常")
                    return True
                else:
                    print("❌ 队列项未创建")
                    return False
            else:
                print("❌ 没有找到队列项")
                return False
                
        except Exception as e:
            print(f"❌ 队列检查失败: {e}")
            return False
    
    async def test3_manual_f2_processing(self):
        """测试3：手动触发f2服务处理队列"""
        print("\n=== 测试3：手动触发f2服务处理队列 ===")
        
        try:
            # 启动f2推送服务
            await self.f2_service.start()
            print("✅ f2推送服务启动成功")
            
            # 手动处理队列项
            processed = await self.f2_service.process_queue_batch(batch_size=10)
            print(f"✅ 手动处理队列项: {processed} 项")
            
            # 等待处理完成
            await asyncio.sleep(3)
            
            # 检查处理结果
            synced_items = await self.postgresql_client.execute_query(
                "SELECT * FROM entries_push_queue WHERE synced = TRUE ORDER BY created_ts DESC"
            )
            
            print(f"📋 已同步队列项数量: {len(synced_items)}")
            
            if synced_items:
                for item in synced_items:
                    print(f"📋 已同步队列项: ID={item['queue_id']}, 操作={item['operation']}, entry_id={item['entry_id']}")
                
                # 检查entries表是否更新了external_id
                entries_updated = await self.postgresql_client.execute_query(
                    "SELECT id, external_id, employee_id, entry_date FROM entries WHERE employee_id = $1 AND external_id IS NOT NULL",
                    self.test_data['employee_id']
                )
                
                if entries_updated:
                    entry = entries_updated[0]
                    print(f"📊 更新后entries数据:")
                    print(f"   id: {entry['id']}")
                    print(f"   external_id: {entry['external_id']} (MDB返回的ID)")
                    print(f"   employee_id: {entry['employee_id']}")
                    print(f"   entry_date: {entry['entry_date']}")
                    
                    if entry['external_id'] is not None:
                        print("✅ external_id已成功回写")
                        return True
                    else:
                        print("❌ external_id未回写")
                        return False
                else:
                    print("❌ entries表未更新external_id")
                    return False
            else:
                print("❌ 队列项未同步")
                return False
                
        except Exception as e:
            print(f"❌ f2处理测试失败: {e}")
            return False
    
    async def test4_verify_mdb_data(self):
        """测试4：验证MDB中的数据"""
        print("\n=== 测试4：验证MDB中的数据 ===")
        
        try:
            # 获取entries表中的external_id
            entries = await self.postgresql_client.execute_query(
                "SELECT external_id FROM entries WHERE employee_id = $1 AND external_id IS NOT NULL",
                self.test_data['employee_id']
            )
            
            if not entries:
                print("❌ 没有找到external_id")
                return False
            
            external_id = entries[0]['external_id']
            print(f"🔍 验证MDB中的数据 (external_id={external_id})...")
            
            # 通过Server6查询MDB数据
            start_date = date(2025, 6, 26)
            end_date = date(2025, 6, 26)
            
            mdb_entries = await self.server6_client.query_entries(
                self.test_data['employee_id'],
                start_date,
                end_date
            )
            
            print(f"📊 MDB查询结果数量: {len(mdb_entries)}")
            
            if mdb_entries:
                mdb_entry = mdb_entries[0]  # 假设只有一条记录
                print(f"📊 MDB数据:")
                print(f"   ID: {mdb_entry.get('ID')}")
                print(f"   従業員ｺｰﾄﾞ: {mdb_entry.get('従業員ｺｰﾄﾞ')}")
                print(f"   日付: {mdb_entry.get('日付')} (MDB格式: YYYY/MM/DD)")
                print(f"   工事番号: {mdb_entry.get('工事番号')}")
                print(f"   区分: {mdb_entry.get('区分')}")
                print(f"   項目: {mdb_entry.get('項目')}")
                print(f"   時間: {mdb_entry.get('時間')}")
                print(f"   所属ｺｰﾄﾞ: {mdb_entry.get('所属ｺｰﾄﾞ')}")
                
                # 验证数据一致性
                if (mdb_entry.get('従業員ｺｰﾄﾞ') == self.test_data['employee_id'] and
                    mdb_entry.get('日付') == self.test_data['date'] and
                    mdb_entry.get('工事番号') == self.test_data['project_number'] and
                    mdb_entry.get('区分') == self.test_data['category'] and
                    mdb_entry.get('項目') == self.test_data['item'] and
                    mdb_entry.get('時間') == self.test_data['time'] and
                    mdb_entry.get('所属ｺｰﾄﾞ') == self.test_data['department']):
                    print("✅ MDB数据与测试数据一致")
                    return True
                else:
                    print("❌ MDB数据与测试数据不一致")
                    return False
            else:
                print("❌ MDB中未找到数据")
                return False
                
        except Exception as e:
            print(f"❌ 验证MDB数据失败: {e}")
            return False
    
    async def run_all_tests(self):
        """运行所有测试"""
        print("🚀 开始简化插入操作测试")
        print("=" * 60)
        
        try:
            # 初始化
            if not await self.setup():
                return False
            
            # 测试1：entries插入
            entry_id = await self.test1_insert_to_entries()
            if not entry_id:
                print("❌ 测试1失败，停止后续测试")
                return False
            
            # 测试2：检查队列创建
            if not await self.test2_check_queue_creation():
                print("❌ 测试2失败")
                return False
            
            # 测试3：手动f2处理
            if not await self.test3_manual_f2_processing():
                print("❌ 测试3失败")
                return False
            
            # 测试4：验证MDB数据
            if not await self.test4_verify_mdb_data():
                print("❌ 测试4失败")
                return False
            
            print("\n🎉 所有测试通过！插入操作流程验证成功")
            print("✅ 测试1：entries插入和日期格式转换 - 通过")
            print("✅ 测试2：队列项创建 - 通过")
            print("✅ 测试3：f2服务处理队列 - 通过")
            print("✅ 测试4：MDB数据验证 - 通过")
            
            return True
            
        except Exception as e:
            print(f"❌ 测试过程中发生错误: {e}")
            return False
        finally:
            await self.cleanup()

async def main():
    """主函数"""
    test = SimpleInsertTest()
    success = await test.run_all_tests()
    
    if success:
        print("\n🎉 简化插入操作测试完成，所有验证通过！")
        sys.exit(0)
    else:
        print("\n❌ 简化插入操作测试失败，请检查系统配置")
        sys.exit(1)

if __name__ == "__main__":
    asyncio.run(main()) 