#!/usr/bin/env python3
import sys
import time
import subprocess
import requests
from pathlib import Path

PYTHON = sys.executable
BASE_DIR = Path(__file__).parent
SERVER5 = BASE_DIR / "start_server5_with_api.py"
CLIENT  = BASE_DIR.parent / "program1.py"

def launch_server5():
    if not SERVER5.exists():
        print(f"❌ 找不到启动脚本: {SERVER5}")
        sys.exit(1)
    print("🛫 启动 Server5 ...")
    return subprocess.Popen(
        [PYTHON, str(SERVER5)],
        cwd=str(BASE_DIR),
        stdout=subprocess.DEVNULL,
        stderr=subprocess.DEVNULL
    )

def wait_for_health(timeout=20):
    print(f"⏳ 等待 /health 可用 (最多 {timeout}s)...")
    url = "http://localhost:8009/health"
    for i in range(timeout):
        try:
            r = requests.get(url, timeout=2)
            if r.status_code == 200 and r.json().get("status") in ("healthy", "ok"):
                print(f"✅ /health OK (等待 {i}s)")
                return True
        except Exception:
            pass
        time.sleep(1)
    print("❌ /health 超过等待时间")
    return False

def test_async(endpoints):
    ok = True
    for name, url, params in endpoints:
        print(f"→ 测试 {name} …")
        t0 = time.time()
        try:
            r = requests.get(url, params=params, timeout=10)
            dt = time.time() - t0
            if r.status_code == 200:
                print(f"  ✅ {name} ({dt:.2f}s)")
            else:
                print(f"  ❌ {name} 返回 {r.status_code}")
                ok = False
        except Exception as e:
            print(f"  ❌ {name} 失败: {e}")
            ok = False
    return ok

def test_timeout():
    print("\n🔄 测试超时/404 ...")
    urls = [
        "http://localhost:8009/api/nonexistent",
        "http://localhost:8009/api/entries/"
    ]
    for url in urls:
        print(f"→ 访问 {url}")
        t0 = time.time()
        try:
            requests.get(url, timeout=2)
            print(f"  ⚠️ 意外响应")
            return False
        except requests.exceptions.Timeout:
            print(f"  ✅ 超时机制正常 ({time.time()-t0:.2f}s)")
        except Exception:
            print(f"  ✅ HTTP 错误正常")
    return True

def test_client_restart():
    print("\n🔄 测试 Program1 重启稳定 …")
    if not CLIENT.exists():
        print(f"❌ 找不到客户端脚本: {CLIENT}")
        return False
    for i in range(3):
        proc = subprocess.Popen(
            [PYTHON, str(CLIENT), "dummy", "TEST001", "测试用户"],
            stdout=subprocess.DEVNULL,
            stderr=subprocess.DEVNULL
        )
        time.sleep(8)
        if proc.poll() is None:
            proc.terminate()
            proc.wait(5)
            print(f"  ✅ 第 {i+1} 次 启动→终止正常")
        else:
            print(f"  ❌ 第 {i+1} 次 客户端意外退出")
            return False
        time.sleep(2)
    return True

def test_resource_cleanup():
    print("\n🔄 测试残留进程 …")
    try:
        import psutil
        pids = [
            p.pid for p in psutil.process_iter(['cmdline'])
            if 'program1.py' in (p.info.get('cmdline') or [])
        ]
        if pids:
            print(f"  ⚠️ 残留进程: {pids}")
            return False
        print("  ✅ 无残留进程")
        return True
    except ImportError:
        print("  ⚠️ psutil 不可用，跳过")
        return True

def main():
    print("="*60)
    print("🧪 MySuite Server5 全流程稳定性测试")
    print("="*60)

    srv = launch_server5()
    try:
        if not wait_for_health():
            raise RuntimeError("Server5 /health 不可用")

        print("\n🔄 测试核心接口…")
        endpoints = [
            ("timeprotab",     "http://localhost:8009/api/timeprotab/",     
                                  {"employee_id":"TEST001","year":2025,"month":1}),
            ("entries",        "http://localhost:8009/api/entries/",        
                                  {"employee_id":"TEST001","start_date":"2025-01-01","end_date":"2025-01-31","limit":1}),
            ("months",         "http://localhost:8009/api/entries/months", 
                                  {"employee_id":"TEST001"}),
            ("chart-data",     "http://localhost:8009/api/entries/chart-data",
                                  {"employee_id":"TEST001","start_date":"2025-01-01","end_date":"2025-01-31","chart_type":"daily"}),
        ]
        ok_async   = test_async(endpoints)
        ok_timeout = test_timeout()
        ok_restart = test_client_restart()
        ok_cleanup = test_resource_cleanup()

        print("\n" + "="*60)
        print("📊 测试结果:")
        print("="*60)
        results = [
            ("async",   ok_async),
            ("timeout", ok_timeout),
            ("restart", ok_restart),
            ("cleanup", ok_cleanup),
        ]
        passed = sum(1 for _, v in results if v)
        for name, ok in results:
            print(f"{name:10}: {'✅' if ok else '❌'}")
        print(f"\n🎯 通过率: {passed}/{len(results)}")
        sys.exit(0 if passed == len(results) else 1)

    finally:
        print("\n🛑 关闭 Server5 ...")
        srv.terminate()
        try:
            srv.wait(5)
        except:
            srv.kill()

if __name__ == "__main__":
    main()
