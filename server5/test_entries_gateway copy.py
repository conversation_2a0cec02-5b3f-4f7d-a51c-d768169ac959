#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Entries网关测试脚本
测试新的UI到PostgreSQL entries表的网关功能
"""

import asyncio
import sys
from pathlib import Path
import logging
import aiohttp
import json
from datetime import datetime

# 添加项目路径
sys.path.append(str(Path(__file__).parent))

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

class EntriesGatewayTest:
    """Entries网关测试类"""
    
    def __init__(self):
        self.base_url = "http://localhost:8009"
        self.gateway_url = f"{self.base_url}/gateway/entries"
        
        # 测试数据
        self.test_create_data = {
            "entry_date": "2025/06/27",
            "employee_id": "215829",
            "duration": 8.5,
            "project_code": "GATEWAY001",
            "status": "3",
            "description": "网关测试项目",
            "department": "131",
            "notes": "号机:GW001 工场製番:SN001 工事番号:PRJ001 ユニット番号:UNIT001",
            "model": "TEST_MODEL",
            "number": "TEST001",
            "factory_number": "FACTORY001",
            "project_number": "PROJECT001",
            "unit_number": "UNIT001",
            "category": 3,
            "item": 7
        }
        
        self.test_update_data = {
            "duration": 9.0,
            "description": "网关测试项目-已更新",
            "notes": "号机:GW001-UPDATED 工场製番:SN001 工事番号:PRJ001 ユニット番号:UNIT001"
        }
    
    async def test_gateway_health(self):
        """测试网关健康状态"""
        logger.info("\n🔍 测试网关健康状态...")
        
        try:
            async with aiohttp.ClientSession() as session:
                # 测试主服务健康状态
                async with session.get(f"{self.base_url}/health") as response:
                    if response.status == 200:
                        health_data = await response.json()
                        logger.info(f"✅ 主服务健康检查成功: {health_data.get('status')}")
                        return True
                    else:
                        logger.error(f"❌ 主服务健康检查失败: {response.status}")
                        return False
        except Exception as e:
            logger.error(f"❌ 网关健康检查异常: {e}")
            return False
    
    async def test_create_entry(self):
        """测试创建entries记录"""
        logger.info("\n📝 测试创建entries记录...")
        
        try:
            async with aiohttp.ClientSession() as session:
                async with session.post(
                    self.gateway_url,
                    json=self.test_create_data,
                    headers={'Content-Type': 'application/json'}
                ) as response:
                    if response.status == 200:
                        result = await response.json()
                        logger.info(f"✅ 创建成功: {result}")
                        
                        if result.get('success'):
                            entry_id = result.get('entry_id')
                            logger.info(f"📋 创建的entry_id: {entry_id}")
                            return entry_id
                        else:
                            logger.error(f"❌ 创建失败: {result.get('message')}")
                            return None
                    else:
                        error_text = await response.text()
                        logger.error(f"❌ 创建请求失败: {response.status} - {error_text}")
                        return None
        except Exception as e:
            logger.error(f"❌ 创建测试异常: {e}")
            return None
    
    async def test_update_entry(self, entry_id: int):
        """测试更新entries记录"""
        logger.info(f"\n📝 测试更新entries记录: entry_id={entry_id}")
        
        try:
            async with aiohttp.ClientSession() as session:
                async with session.put(
                    f"{self.gateway_url}/{entry_id}",
                    json=self.test_update_data,
                    headers={'Content-Type': 'application/json'}
                ) as response:
                    if response.status == 200:
                        result = await response.json()
                        logger.info(f"✅ 更新成功: {result}")
                        
                        if result.get('success'):
                            logger.info(f"📋 更新的entry_id: {result.get('entry_id')}")
                            return True
                        else:
                            logger.error(f"❌ 更新失败: {result.get('message')}")
                            return False
                    else:
                        error_text = await response.text()
                        logger.error(f"❌ 更新请求失败: {response.status} - {error_text}")
                        return False
        except Exception as e:
            logger.error(f"❌ 更新测试异常: {e}")
            return False
    
    async def test_get_entry_status(self, entry_id: int):
        """测试获取entries记录状态"""
        logger.info(f"\n📝 测试获取entries记录状态: entry_id={entry_id}")
        
        try:
            async with aiohttp.ClientSession() as session:
                async with session.get(f"{self.gateway_url}/status/{entry_id}") as response:
                    if response.status == 200:
                        result = await response.json()
                        logger.info(f"✅ 状态查询成功: {result}")
                        
                        if result.get('success'):
                            external_id = result.get('external_id')
                            synced = result.get('synced')
                            source = result.get('source')
                            
                            logger.info(f"📋 记录状态:")
                            logger.info(f"   - entry_id: {entry_id}")
                            logger.info(f"   - external_id: {external_id}")
                            logger.info(f"   - synced: {synced}")
                            logger.info(f"   - source: {source}")
                            
                            return True
                        else:
                            logger.error(f"❌ 状态查询失败: {result.get('message')}")
                            return False
                    else:
                        error_text = await response.text()
                        logger.error(f"❌ 状态查询请求失败: {response.status} - {error_text}")
                        return False
        except Exception as e:
            logger.error(f"❌ 状态查询测试异常: {e}")
            return False
    
    async def test_get_sync_status(self):
        """测试获取同步状态"""
        logger.info("\n📝 测试获取同步状态...")
        
        try:
            async with aiohttp.ClientSession() as session:
                async with session.get(f"{self.gateway_url}/sync-status") as response:
                    if response.status == 200:
                        result = await response.json()
                        logger.info(f"✅ 同步状态查询成功: {result}")
                        
                        if result.get('success'):
                            total_entries = result.get('total_entries')
                            synced_entries = result.get('synced_entries')
                            pending_sync = result.get('pending_sync')
                            sync_rate = result.get('sync_rate')
                            
                            logger.info(f"📋 同步状态:")
                            logger.info(f"   - 总记录数: {total_entries}")
                            logger.info(f"   - 已同步: {synced_entries}")
                            logger.info(f"   - 待同步: {pending_sync}")
                            logger.info(f"   - 同步率: {sync_rate}")
                            
                            return True
                        else:
                            logger.error(f"❌ 同步状态查询失败: {result.get('message')}")
                            return False
                    else:
                        error_text = await response.text()
                        logger.error(f"❌ 同步状态查询请求失败: {response.status} - {error_text}")
                        return False
        except Exception as e:
            logger.error(f"❌ 同步状态查询测试异常: {e}")
            return False
    
    async def test_delete_entry(self, entry_id: int):
        """测试删除entries记录"""
        logger.info(f"\n📝 测试删除entries记录: entry_id={entry_id}")
        
        try:
            async with aiohttp.ClientSession() as session:
                async with session.delete(f"{self.gateway_url}/{entry_id}") as response:
                    if response.status == 200:
                        result = await response.json()
                        logger.info(f"✅ 删除成功: {result}")
                        
                        if result.get('success'):
                            logger.info(f"📋 删除的entry_id: {result.get('entry_id')}")
                            return True
                        else:
                            logger.error(f"❌ 删除失败: {result.get('message')}")
                            return False
                    else:
                        error_text = await response.text()
                        logger.error(f"❌ 删除请求失败: {response.status} - {error_text}")
                        return False
        except Exception as e:
            logger.error(f"❌ 删除测试异常: {e}")
            return False
    
    async def run_complete_test(self):
        """运行完整测试"""
        logger.info("🧪 开始Entries网关测试")
        logger.info("=" * 60)
        
        try:
            # 步骤1: 健康检查
            health_ok = await self.test_gateway_health()
            if not health_ok:
                logger.error("❌ 健康检查失败，测试终止")
                return False
            
            # 步骤2: 创建记录
            entry_id = await self.test_create_entry()
            if not entry_id:
                logger.error("❌ 创建记录失败，测试终止")
                return False
            
            # 等待一下让触发器执行
            await asyncio.sleep(2)
            
            # 步骤3: 查询状态
            status_ok = await self.test_get_entry_status(entry_id)
            if not status_ok:
                logger.error("❌ 状态查询失败")
            
            # 步骤4: 更新记录
            update_ok = await self.test_update_entry(entry_id)
            if not update_ok:
                logger.error("❌ 更新记录失败")
            
            # 等待一下让触发器执行
            await asyncio.sleep(2)
            
            # 步骤5: 再次查询状态
            status_ok2 = await self.test_get_entry_status(entry_id)
            if not status_ok2:
                logger.error("❌ 更新后状态查询失败")
            
            # 步骤6: 查询同步状态
            sync_status_ok = await self.test_get_sync_status()
            if not sync_status_ok:
                logger.error("❌ 同步状态查询失败")
            
            # 步骤7: 删除记录
            delete_ok = await self.test_delete_entry(entry_id)
            if not delete_ok:
                logger.error("❌ 删除记录失败")
            
            logger.info("\n" + "=" * 60)
            logger.info("🎉 Entries网关测试完成！")
            logger.info("✅ 所有API端点都正常工作")
            logger.info("💡 网关成功处理UI到PostgreSQL的数据流")
            logger.info("=" * 60)
            
            return True
            
        except Exception as e:
            logger.error(f"❌ 测试过程中发生异常: {e}")
            return False

async def main():
    """主函数"""
    test = EntriesGatewayTest()
    success = await test.run_complete_test()
    
    if success:
        print("\n🎉 Entries网关测试通过！")
        print("💡 网关功能正常，可以处理UI到PostgreSQL的完整数据流")
    else:
        print("\n❌ Entries网关测试失败")
        print("💡 需要检查网关服务和相关配置")

if __name__ == "__main__":
    asyncio.run(main()) 