# server5/config_windows_minimal.py
# Windows环境最小配置 - 容错性最强
# 即使数据库连接失败也能启动基础服务

import os
import platform
import logging
from pathlib import Path

# 服务基本配置 (兼容其他配置文件)
SERVICE_NAME = "MySuite Server5 - 数据同步微服务"
SERVICE_VERSION = "1.0.0"
SERVICE_PORT = 8009

# 运行环境配置
ENVIRONMENT = "minimal"
DEBUG = False
LOG_LEVEL = "ERROR"  # 只显示错误
PLATFORM = platform.system()

# 服务器配置
SERVER_HOST = "127.0.0.1"
SERVER_PORT = 8009

# ===== 数据库配置 (兼容现有代码) =====

# PostgreSQL主数据库 (本地连接，容错配置)
PG_HOST = "127.0.0.1"
PG_PORT = 5432
PG_USER = "postgres"
PG_PASS = "123456"
PG_DB_NAME = "postgres"
PG_DATABASE_URL = f"postgresql://{PG_USER}:{PG_PASS}@{PG_HOST}:{PG_PORT}/{PG_DB_NAME}"

# IMDB数据库 (entries分区表位于此数据库)
IMDB_HOST = "127.0.0.1"
IMDB_PORT = 5432
IMDB_USER = "postgres"
IMDB_PASS = "123456"
IMDB_DB_NAME = "imdb"
IMDB_DATABASE_URL = f"postgresql://{IMDB_USER}:{IMDB_PASS}@{IMDB_HOST}:{IMDB_PORT}/{IMDB_DB_NAME}"

# PostgreSQL连接配置别名 (兼容测试脚本)
POSTGRESQL_HOST = PG_HOST
POSTGRESQL_PORT = PG_PORT
POSTGRESQL_DB = PG_DB_NAME

# MongoDB配置 (本地连接)
MONGO_HOST = "127.0.0.1"
MONGO_PORT = 27017
MONGO_DB = "server5_logs"
MONGO_URL = f"mongodb://{MONGO_HOST}:{MONGO_PORT}"

# MongoDB连接配置别名 (兼容测试脚本)
MONGODB_HOST = MONGO_HOST
MONGODB_PORT = MONGO_PORT
MONGODB_DB = MONGO_DB

# Redis配置 (本地连接)
REDIS_HOST = "127.0.0.1"
REDIS_PORT = 6379
REDIS_DB = 0  # 使用默认数据库
REDIS_URL = f"redis://{REDIS_HOST}:{REDIS_PORT}/{REDIS_DB}"

# ===== MDB数据库配置 (Windows专用) =====
MDB_PATH = r"D:\actest25\6.mdb"  # Windows路径
MDB_BACKUP_PATH = r"D:\actest25\backup"  # Windows备份路径

# ===== 异步任务配置 =====
# PostgreSQL LISTEN/NOTIFY配置
NOTIFY_CHANNELS = [
    "push_job",           # 数据推送任务
    "partition_check",    # 分区检查
    "sync_trigger",       # 同步触发器
]

# 任务队列配置
WORKER_CONCURRENCY = 1        # 最小模式只用1个工作线程
QUEUE_BATCH_SIZE = 10         # 减少批处理大小
SYNC_TIMEOUT = 30            # 同步超时时间(秒)

# ===== 数据同步配置 (最小化) =====
# f2推送回写配置
OPERATION_TIMEOUT = 30          # 操作超时时间(秒)

# f3数据拉取配置
PULL_INTERVAL = 600             # 拉取间隔(秒) - 10分钟
PULL_BATCH_SIZE = 20            # 拉取批次大小

# f5批量同步配置
BULK_SYNC_INTERVAL = 3600       # 批量同步间隔(秒) - 1小时
BULK_SYNC_BATCH_SIZE = 50       # 批量同步批次大小
BULK_SYNC_DAYS = 30             # 批量同步检查最近30天的数据
CONSISTENCY_CHECK_INTERVAL = 7200  # 一致性检查间隔(秒) - 2小时

# f6专属ID同步配置
USER_SYNC_DAYS = 30          # 用户数据同步天数
AUTO_SYNC_INTERVAL = 1200    # 自动同步间隔(秒) - 20分钟

# 分区管理配置
PARTITION_RETENTION_MONTHS = 12  # 分区保留月数
AUTO_PARTITION_CREATE = True     # 自动创建分区

# ===== 兼容性配置 =====
# 数据库配置 - 字典格式 (向后兼容)
DATABASES = {
    "postgresql": {
        "host": PG_HOST,
        "port": PG_PORT,
        "database": PG_DB_NAME,
        "user": PG_USER,
        "password": PG_PASS,
        "pool_size": 2,
        "max_overflow": 5,
        "echo": False,
        "required": False,  # 不要求必须连接成功
    },
    "mongodb": {
        "host": MONGO_HOST,
        "port": MONGO_PORT,
        "database": MONGO_DB,
        "username": None,
        "password": None,
        "required": False,  # 不要求必须连接成功
    },
    "redis": {
        "host": REDIS_HOST,
        "port": REDIS_PORT,
        "db": REDIS_DB,
        "password": None,
        "required": False,  # 不要求必须连接成功
    }
}

# MDB配置
MDB_CONFIG = {
    "path": MDB_PATH,
    "required": False,  # 不要求必须连接成功
    "fallback_to_mock": True,  # 连接失败时使用模拟模式
}

# 服务配置 - 最小化
SERVICES = {
    "f1_listener": {
        "enabled": False,  # 禁用以避免数据库依赖
        "check_interval": 300,  # 5分钟
    },
    "f2_push_writer": {
        "enabled": False,  # 禁用以避免数据库依赖
        "worker_count": 1,
        "batch_size": 10,
    },
    "f3_data_puller": {
        "enabled": False,  # 禁用以避免数据库依赖
        "pull_interval": 300,  # 5分钟
        "batch_size": 50,
    },
    "f4_operation_handler": {
        "enabled": False,  # 禁用以避免数据库依赖
        "timeout": 30,
    },
    "f5_bulk_sync": {
        "enabled": False,  # 禁用以避免数据库依赖
        "sync_interval": 1800,  # 30分钟
    },
    "f6_user_sync": {
        "enabled": False,  # 禁用以避免数据库依赖
        "sync_interval": 3600,  # 1小时
    }
}

# 日志配置
LOGGING = {
    "level": LOG_LEVEL,
    "format": "%(asctime)s - %(name)s - %(levelname)s - %(message)s",
    "file": None,  # 不写文件日志
    "console": True,
    "disable_emoji": True,  # Windows兼容性
}

# 安全配置
SECURITY = {
    "secret_key": "windows-minimal-key-for-testing-only",
    "algorithm": "HS256",
    "access_token_expire_minutes": 30,
}

# 性能配置
PERFORMANCE = {
    "connection_pool_size": 5,
    "max_connections": 10,
    "timeout": 30,
    "retry_attempts": 3,
    "fail_fast": True,  # 快速失败，不阻塞启动
}

# ===== 日志配置 (安静模式) =====
LOG_DIR = Path(__file__).parent.parent / "logs"
LOG_ROTATION = "1 day"           # 日志轮转时间
LOG_RETENTION = "30 days"        # 日志保留时间

# ===== 安全配置 =====
JWT_SECRET_KEY = "server5-minimal-key-for-testing-only"
JWT_ALGORITHM = "HS256"
JWT_EXPIRE_HOURS = 24

# API限流配置
RATE_LIMIT_REQUESTS = 100        # 每分钟请求限制
RATE_LIMIT_WINDOW = 60          # 限流窗口(秒)

# ===== 跨平台兼容性配置 =====
class PlatformConfig:
    """平台配置类 - 多平台自动适配"""
    
    @staticmethod
    def is_windows():
        """检查是否为Windows平台"""
        return platform.system() == 'Windows'
    
    @staticmethod
    def is_linux():
        """检查是否为Linux平台"""
        return platform.system() == 'Linux'
    
    @staticmethod
    def is_darwin():
        """检查是否为macOS平台"""
        return platform.system() == 'Darwin'
    
    @staticmethod
    def get_platform_name():
        """获取平台名称"""
        return platform.system()
    
    @staticmethod
    def get_odbc_available():
        """检查ODBC是否可用"""
        if PlatformConfig.is_windows():
            try:
                import win32com.client
                return True
            except ImportError:
                return False
        else:
            return False
    
    @staticmethod
    def get_mdb_path():
        """获取MDB数据库路径"""
        if PlatformConfig.is_windows():
            return r"D:\actest25\6.mdb"
        else:
            return "/tmp/mock_database.db"
    
    @staticmethod
    def get_config():
        """获取平台特定的配置"""
        platform_name = PlatformConfig.get_platform_name()
        
        config = {
            "platform": platform_name,
            "use_mock_in_linux": False,  # Windows使用真实MDB
            "enable_remote_mdb": False,  # 本地直接访问
            "local_deployment": True,    # 标记为本地部署
        }
        
        if platform_name == "Windows":
            config.update({
                "mdb_path": r"D:\actest25\6.mdb",
                "prefer_real_mdb": True,
            })
        
        return config

# ===== 健康检查配置 =====
HEALTH_CHECK_INTERVAL = 300     # 健康检查间隔(秒) - 5分钟
DB_CONNECTION_TIMEOUT = 10      # 数据库连接超时(秒)

# ===== Windows最小模式优化配置 =====
WINDOWS_MINIMAL_MODE = True
REDUCE_LOG_OUTPUT = True
DISABLE_DEBUG_LOGS = True

# 功能开关
FEATURES = {
    "enable_real_database": False,  # 禁用真实数据库连接
    "enable_mock_mode": True,       # 启用模拟模式
    "enable_health_check": True,    # 启用健康检查
    "enable_api_docs": True,        # 启用API文档
    "strict_mode": False,           # 非严格模式，允许部分失败
}

# 禁用详细的第三方库日志
logging.getLogger("pymongo").setLevel(logging.ERROR)
logging.getLogger("asyncpg").setLevel(logging.ERROR)
logging.getLogger("redis").setLevel(logging.ERROR)
logging.getLogger("uvicorn").setLevel(logging.ERROR)
logging.getLogger("fastapi").setLevel(logging.ERROR)

def get_config():
    """获取配置字典"""
    return {
        "DEBUG": DEBUG,
        "LOG_LEVEL": LOG_LEVEL,
        "PLATFORM": PLATFORM,
        "SERVER_HOST": SERVER_HOST,
        "SERVER_PORT": SERVER_PORT,
        "DATABASES": DATABASES,
        "MDB_CONFIG": MDB_CONFIG,
        "SERVICES": SERVICES,
        "LOGGING": LOGGING,
        "SECURITY": SECURITY,
        "PERFORMANCE": PERFORMANCE,
        "FEATURES": FEATURES,
    } 