# server5/config/config_win10_local.py
# 2025/06/27 09：14 修改多平台server5
# Windows 10本地运行配置 - Server5和Server6在同一台机器（************）

import os
import platform
from pathlib import Path

# 服务基本配置
SERVICE_NAME = "MySuite Server5 - 数据同步微服务 (Win10本地)"
SERVICE_VERSION = "1.0.0"
SERVICE_PORT = 8009

# 运行环境配置
ENVIRONMENT = os.getenv("ENVIRONMENT", "production")  # 本地生产环境
DEBUG = False

# ===== 数据库配置 =====

# PostgreSQL主数据库 (本地)
PG_HOST = "localhost"  # 本地运行
PG_PORT = 5432
PG_USER = "postgres"
PG_PASS = "pojiami0602"
PG_DB_NAME = "postgres"
PG_DATABASE_URL = f"postgresql://{PG_USER}:{PG_PASS}@{PG_HOST}:{PG_PORT}/{PG_DB_NAME}"

# IMDB数据库 (本地)
IMDB_HOST = "localhost"
IMDB_PORT = 5432
IMDB_USER = "postgres"
IMDB_PASS = "pojiami0602"
IMDB_DB_NAME = "imdb"
IMDB_DATABASE_URL = f"postgresql://{IMDB_USER}:{IMDB_PASS}@{IMDB_HOST}:{IMDB_PORT}/{IMDB_DB_NAME}"

# MongoDB配置 (本地)
MONGO_HOST = "localhost"
MONGO_PORT = 27017
MONGO_DB = "server5_logs"
MONGO_URL = f"mongodb://{MONGO_HOST}:{MONGO_PORT}"

# Redis配置 (本地)
REDIS_HOST = "localhost"
REDIS_PORT = 6379
REDIS_DB = 5
REDIS_URL = f"redis://{REDIS_HOST}:{REDIS_PORT}/{REDIS_DB}"

# ===== MDB数据库配置 (Windows本地) =====
MDB_PATH = r"D:\actest25\6.mdb"  # 本地Windows路径
MDB_BACKUP_PATH = r"D:\actest25\backup"

# ===== Server6 MDB网关配置 (本地) =====
# 2025/06/27 09：14 修改多平台server5 - 本地配置
SERVER6_HOST = "localhost"        # 本地运行
SERVER6_PORT = 8019  # 修复端口冲突
SERVER6_BASE_URL = f"http://{SERVER6_HOST}:{SERVER6_PORT}"
SERVER6_API_KEY = None
SERVER6_TIMEOUT = 10             # 本地连接，短超时
SERVER6_MAX_RETRIES = 2          # 本地连接，少重试

# Server6连接配置
SERVER6_CONFIG = {
    "base_url": SERVER6_BASE_URL,
    "api_key": SERVER6_API_KEY,
    "timeout": SERVER6_TIMEOUT,
    "max_retries": SERVER6_MAX_RETRIES,
    "endpoints": {
        "health": "/health",
        "test": "/mdb/test",
        "query": "/mdb/query",
        "insert": "/mdb/insert",
        "update": "/mdb/update",
        "delete": "/mdb/delete"
    }
}

# ===== 异步任务配置 =====
NOTIFY_CHANNELS = [
    "push_job",
    "partition_check", 
    "sync_trigger",
]

# 任务队列配置
WORKER_CONCURRENCY = 8           # 本地运行，更多并发
QUEUE_BATCH_SIZE = 200           # 本地运行，更大批次
SYNC_TIMEOUT = 20                # 本地运行，短超时

# ===== 数据同步配置 =====
OPERATION_TIMEOUT = 20           # 本地运行，短超时
PULL_INTERVAL = 60               # 本地运行，频繁拉取(1分钟)
PULL_BATCH_SIZE = 200            # 本地运行，大批次
BULK_SYNC_INTERVAL = 1800        # 批量同步30分钟
BULK_SYNC_BATCH_SIZE = 500       # 本地运行，大批次
CONSISTENCY_CHECK_INTERVAL = 3600  # 一致性检查1小时
USER_SYNC_DAYS = 30
AUTO_SYNC_INTERVAL = 120         # 自动同步2分钟

# 分区管理配置
PARTITION_RETENTION_MONTHS = 12
AUTO_PARTITION_CREATE = True

# ===== 日志配置 =====
LOG_LEVEL = "INFO"
LOG_DIR = Path(__file__).parent.parent / "logs"
LOG_ROTATION = "1 day"
LOG_RETENTION = "30 days"

# ===== 安全配置 =====
JWT_SECRET_KEY = "server5-win10-local-secret-2025"
JWT_ALGORITHM = "HS256"
JWT_EXPIRE_HOURS = 24

# API限流配置 (本地宽松)
RATE_LIMIT_REQUESTS = 500        # 本地运行，更高限制
RATE_LIMIT_WINDOW = 60

# ===== 平台特定配置 =====
class PlatformConfig:
    """平台配置类 - Windows 10本地运行"""
    
    @staticmethod
    def is_windows():
        return platform.system() == 'Windows'
    
    @staticmethod
    def is_linux():
        return platform.system() == 'Linux'
    
    @staticmethod
    def get_platform_name():
        return platform.system()
    
    @staticmethod
    def get_odbc_available():
        """Windows本地环境，ODBC应该可用"""
        if PlatformConfig.is_windows():
            try:
                import win32com.client
                return True
            except ImportError:
                return False
        return False
    
    @staticmethod
    def get_mdb_path():
        """本地MDB路径"""
        return r"D:\actest25\6.mdb"
    
    @staticmethod
    def get_config():
        """Windows 10本地配置"""
        return {
            "platform": "Windows",
            "deployment": "local",
            "use_mock_in_linux": False,
            "enable_remote_mdb": False,    # 本地不需要远程
            "prefer_real_mdb": True,       # 本地使用真实MDB
            "server6_local": True,         # Server6本地运行
            "mdb_path": r"D:\actest25\6.mdb",
        }

# ===== 健康检查配置 =====
# 2025/06/27 09：14 修改多平台server5 - 添加缺失配置项
HEALTH_CHECK_INTERVAL = 30      # 本地运行，频繁健康检查
DB_CONNECTION_TIMEOUT = 5       # 本地连接，短超时

# ===== 开发/调试配置 =====
if DEBUG:
    # Windows本地开发环境特殊配置
    print(f"🐛 {SERVICE_NAME} 运行在调试模式")
    print(f"📊 PostgreSQL: {PG_HOST}:{PG_PORT}")
    print(f"🍃 MongoDB: {MONGO_HOST}:{MONGO_PORT}")
    print(f"⚡ Redis: {REDIS_HOST}:{REDIS_PORT}")
    print(f"💾 ODBC可用: {PlatformConfig.get_odbc_available()}")

# 25.06.26 使用环境变量覆盖平台检测
FORCE_PLATFORM = os.getenv("FORCE_PLATFORM")  # 可以强制指定平台类型
USE_MOCK_MDB = os.getenv("USE_MOCK_MDB", "false")  # Windows本地默认使用真实MDB

# 导出配置
PLATFORM_CONFIG = PlatformConfig.get_config() 