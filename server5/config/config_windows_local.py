# server5/config/config_windows_local.py
# Windows本地配置 - 使用localhost连接
# 25.06.26 为Windows本地部署优化

import os
import platform
import logging
from pathlib import Path

# 服务基本配置
SERVICE_NAME = "MySuite Server5 - 数据同步微服务"
SERVICE_VERSION = "1.0.0"
SERVICE_PORT = 8009

# 运行环境配置
ENVIRONMENT = "production"  # 本地生产环境
DEBUG = False  # Windows本地关闭调试

# ===== 数据库配置 (本地化) =====

# PostgreSQL主数据库 (本地连接)
PG_HOST = "127.0.0.1"  # 本地回环地址
PG_PORT = 5432
PG_USER = "postgres"
PG_PASS = "pojiami0602"
PG_DB_NAME = "postgres"
PG_DATABASE_URL = f"postgresql://{PG_USER}:{PG_PASS}@{PG_HOST}:{PG_PORT}/{PG_DB_NAME}"

# IMDB数据库 (entries分区表位于此数据库)
IMDB_HOST = "127.0.0.1"  # 本地回环地址
IMDB_PORT = 5432
IMDB_USER = "postgres"
IMDB_PASS = "pojiami0602"
IMDB_DB_NAME = "imdb"
IMDB_DATABASE_URL = f"postgresql://{IMDB_USER}:{IMDB_PASS}@{IMDB_HOST}:{IMDB_PORT}/{IMDB_DB_NAME}"

# PostgreSQL连接配置别名 (兼容test_windows_simple.py)
POSTGRESQL_HOST = PG_HOST
POSTGRESQL_PORT = PG_PORT
POSTGRESQL_DB = PG_DB_NAME

# MongoDB配置 (本地连接)
MONGO_HOST = "127.0.0.1"  # 本地回环地址
MONGO_PORT = 27017
MONGO_DB = "server5_logs"
MONGO_URL = f"mongodb://{MONGO_HOST}:{MONGO_PORT}"

# MongoDB连接配置别名 (兼容test_windows_simple.py)
MONGODB_HOST = MONGO_HOST
MONGODB_PORT = MONGO_PORT
MONGODB_DB = MONGO_DB

# Redis配置 (本地连接)
REDIS_HOST = "127.0.0.1"  # 本地回环地址
REDIS_PORT = 6379
REDIS_DB = 5  # 使用数据库5避免与其他服务冲突
REDIS_URL = f"redis://{REDIS_HOST}:{REDIS_PORT}/{REDIS_DB}"

# ===== MDB数据库配置 (Windows专用) =====
MDB_PATH = r"D:\actest25\6.mdb"  # Windows路径
MDB_BACKUP_PATH = r"D:\actest25\backup"  # Windows备份路径

# ===== 异步任务配置 =====
# PostgreSQL LISTEN/NOTIFY配置
NOTIFY_CHANNELS = [
    "push_job",           # 数据推送任务
    "partition_check",    # 分区检查
    "sync_trigger",       # 同步触发器
]

# 任务队列配置
WORKER_CONCURRENCY = 3        # Windows环境降低并发数
QUEUE_BATCH_SIZE = 50         # 减少批处理大小
SYNC_TIMEOUT = 30            # 同步超时时间(秒)

# ===== 数据同步配置 (优化间隔) =====
# f2推送回写配置
OPERATION_TIMEOUT = 30          # 操作超时时间(秒)

# f3数据拉取配置
PULL_INTERVAL = 300             # 拉取间隔(秒) - 5分钟
PULL_BATCH_SIZE = 50            # 拉取批次大小

# f5批量同步配置
BULK_SYNC_INTERVAL = 1800        # 批量同步30分钟
BULK_SYNC_BATCH_SIZE = 500       # 本地运行，大批次
BULK_SYNC_DAYS = 30              # 批量同步检查最近30天的数据
CONSISTENCY_CHECK_INTERVAL = 3600  # 一致性检查1小时

# f6专属ID同步配置
USER_SYNC_DAYS = 30          # 用户数据同步天数
AUTO_SYNC_INTERVAL = 600     # 自动同步间隔(秒) - 10分钟

# 分区管理配置
PARTITION_RETENTION_MONTHS = 12  # 分区保留月数
AUTO_PARTITION_CREATE = True     # 自动创建分区

# ===== 日志配置 (安静模式) =====
LOG_LEVEL = logging.WARNING      # 只显示警告和错误
LOG_DIR = Path(__file__).parent.parent / "logs"
LOG_ROTATION = "1 day"           # 日志轮转时间
LOG_RETENTION = "30 days"        # 日志保留时间

# ===== 安全配置 =====
JWT_SECRET_KEY = "server5-data-sync-secret-key-2025"
JWT_ALGORITHM = "HS256"
JWT_EXPIRE_HOURS = 24

# API限流配置
RATE_LIMIT_REQUESTS = 100        # 每分钟请求限制
RATE_LIMIT_WINDOW = 60          # 限流窗口(秒)

# ===== 跨平台兼容性配置 =====
class PlatformConfig:
    """平台配置类 - 多平台自动适配"""
    
    @staticmethod
    def is_windows():
        """检查是否为Windows平台"""
        return platform.system() == 'Windows'
    
    @staticmethod
    def is_linux():
        """检查是否为Linux平台"""
        return platform.system() == 'Linux'
    
    @staticmethod
    def is_darwin():
        """检查是否为macOS平台"""
        return platform.system() == 'Darwin'
    
    @staticmethod
    def get_platform_name():
        """获取平台名称"""
        return platform.system()
    
    @staticmethod
    def get_odbc_available():
        """检查ODBC是否可用"""
        if PlatformConfig.is_windows():
            try:
                import win32com.client
                return True
            except ImportError:
                return False
        else:
            return False
    
    @staticmethod
    def get_mdb_path():
        """获取MDB数据库路径"""
        if PlatformConfig.is_windows():
            return r"D:\actest25\6.mdb"
        else:
            return "/tmp/mock_database.db"
    
    @staticmethod
    def get_config():
        """获取平台特定的配置"""
        platform_name = PlatformConfig.get_platform_name()
        
        config = {
            "platform": platform_name,
            "use_mock_in_linux": False,  # Windows使用真实MDB
            "enable_remote_mdb": False,  # 本地直接访问
            "local_deployment": True,    # 标记为本地部署
        }
        
        if platform_name == "Windows":
            config.update({
                "mdb_path": r"D:\actest25\6.mdb",
                "prefer_real_mdb": True,
            })
        
        return config

# ===== 健康检查配置 =====
HEALTH_CHECK_INTERVAL = 120     # 健康检查间隔(秒) - 延长到2分钟
DB_CONNECTION_TIMEOUT = 10      # 数据库连接超时(秒)

# ===== Windows本地优化配置 =====
WINDOWS_LOCAL_MODE = True
REDUCE_LOG_OUTPUT = True
DISABLE_DEBUG_LOGS = True

# 禁用详细的第三方库日志
import logging
logging.getLogger("pymongo").setLevel(logging.WARNING)
logging.getLogger("asyncpg").setLevel(logging.WARNING)
logging.getLogger("redis").setLevel(logging.WARNING)
logging.getLogger("uvicorn").setLevel(logging.WARNING)
logging.getLogger("fastapi").setLevel(logging.WARNING)

print(f"📝 Windows本地配置已加载:")
print(f"  PostgreSQL: {PG_HOST}:{PG_PORT}")
print(f"  MongoDB: {MONGO_HOST}:{MONGO_PORT}")
print(f"  Redis: {REDIS_HOST}:{REDIS_PORT}")
print(f"  MDB路径: {MDB_PATH}")
print(f"  日志级别: WARNING (安静模式)")
print(f"  平台: {PlatformConfig.get_platform_name()}")
print(f"  ODBC可用: {PlatformConfig.get_odbc_available()}") 