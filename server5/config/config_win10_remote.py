# server5/config/config_win10_remote.py
# 2025/06/27 09：14 修改多平台server5
# Windows 10远程运行配置 - Server5在不同的Win10机器，连接远程Server6

import os
import platform
from pathlib import Path

# 服务基本配置
SERVICE_NAME = "MySuite Server5 - 数据同步微服务 (Win10远程)"
SERVICE_VERSION = "1.0.0"
SERVICE_PORT = 8009

# 运行环境配置
ENVIRONMENT = os.getenv("ENVIRONMENT", "production")  # 远程生产环境
DEBUG = False

# ===== 数据库配置 =====

# PostgreSQL主数据库 (远程)
PG_HOST = "************"  # 远程数据库服务器
PG_PORT = 5432
PG_USER = "postgres"
PG_PASS = "pojiami0602"
PG_DB_NAME = "postgres"
PG_DATABASE_URL = f"postgresql://{PG_USER}:{PG_PASS}@{PG_HOST}:{PG_PORT}/{PG_DB_NAME}"

# IMDB数据库 (远程)
IMDB_HOST = "************"
IMDB_PORT = 5432
IMDB_USER = "postgres"
IMDB_PASS = "pojiami0602"
IMDB_DB_NAME = "imdb"
IMDB_DATABASE_URL = f"postgresql://{IMDB_USER}:{IMDB_PASS}@{IMDB_HOST}:{IMDB_PORT}/{IMDB_DB_NAME}"

# MongoDB配置 (远程)
MONGO_HOST = "************"
MONGO_PORT = 27017
MONGO_DB = "server5_logs"
MONGO_URL = f"mongodb://{MONGO_HOST}:{MONGO_PORT}"

# Redis配置 (本地或远程，可配置)
REDIS_HOST = os.getenv("REDIS_HOST", "localhost")  # 可配置
REDIS_PORT = 6379
REDIS_DB = 5
REDIS_URL = f"redis://{REDIS_HOST}:{REDIS_PORT}/{REDIS_DB}"

# ===== MDB数据库配置 (不直接访问，通过Server6) =====
MDB_PATH = None  # 远程运行不直接访问MDB
MDB_BACKUP_PATH = None

# ===== Server6 MDB网关配置 (远程) =====
# 2025/06/27 09：14 修改多平台server5 - 远程配置
SERVER6_HOST = "************"     # 远程Server6
SERVER6_PORT = 8019  # 修复端口冲突
SERVER6_BASE_URL = f"http://{SERVER6_HOST}:{SERVER6_PORT}"
SERVER6_API_KEY = None
SERVER6_TIMEOUT = 30             # 远程连接，长超时
SERVER6_MAX_RETRIES = 3          # 远程连接，多重试

# Server6连接配置
SERVER6_CONFIG = {
    "base_url": SERVER6_BASE_URL,
    "api_key": SERVER6_API_KEY,
    "timeout": SERVER6_TIMEOUT,
    "max_retries": SERVER6_MAX_RETRIES,
    "endpoints": {
        "health": "/health",
        "test": "/mdb/test",
        "query": "/mdb/query",
        "insert": "/mdb/insert",
        "update": "/mdb/update",
        "delete": "/mdb/delete"
    }
}

# ===== 异步任务配置 =====
NOTIFY_CHANNELS = [
    "push_job",
    "partition_check", 
    "sync_trigger",
]

# 任务队列配置
WORKER_CONCURRENCY = 6           # 远程运行，适中并发
QUEUE_BATCH_SIZE = 100           # 远程运行，适中批次
SYNC_TIMEOUT = 45                # 远程运行，长超时

# ===== 数据同步配置 =====
OPERATION_TIMEOUT = 45           # 远程运行，长超时
PULL_INTERVAL = 120              # 远程运行，2分钟拉取
PULL_BATCH_SIZE = 100            # 远程运行，适中批次
BULK_SYNC_INTERVAL = 3600        # 批量同步1小时
BULK_SYNC_BATCH_SIZE = 300       # 远程运行，适中批次
BULK_SYNC_DAYS = 30              # 批量同步检查最近30天的数据
CONSISTENCY_CHECK_INTERVAL = 7200  # 一致性检查2小时
USER_SYNC_DAYS = 30
AUTO_SYNC_INTERVAL = 300         # 自动同步5分钟

# 分区管理配置
PARTITION_RETENTION_MONTHS = 12
AUTO_PARTITION_CREATE = True

# ===== 日志配置 =====
LOG_LEVEL = "INFO"
LOG_DIR = Path(__file__).parent.parent / "logs"
LOG_ROTATION = "1 day"
LOG_RETENTION = "30 days"

# ===== 安全配置 =====
JWT_SECRET_KEY = "server5-win10-remote-secret-2025"
JWT_ALGORITHM = "HS256"
JWT_EXPIRE_HOURS = 24

# API限流配置 (远程适中)
RATE_LIMIT_REQUESTS = 200        # 远程运行，适中限制
RATE_LIMIT_WINDOW = 60

# ===== 平台特定配置 =====
class PlatformConfig:
    """平台配置类 - Windows 10远程运行"""
    
    @staticmethod
    def is_windows():
        return platform.system() == 'Windows'
    
    @staticmethod
    def is_linux():
        return platform.system() == 'Linux'
    
    @staticmethod
    def get_platform_name():
        return platform.system()
    
    @staticmethod
    def get_odbc_available():
        """Windows环境，但远程运行不直接使用ODBC"""
        return False  # 远程运行不直接访问MDB
    
    @staticmethod
    def get_mdb_path():
        """远程运行不直接访问MDB"""
        return None
    
    @staticmethod
    def get_config():
        """Windows 10远程配置"""
        return {
            "platform": "Windows",
            "deployment": "remote",
            "use_mock_in_linux": False,
            "enable_remote_mdb": True,     # 必须使用远程MDB
            "prefer_real_mdb": False,      # 不直接访问MDB
            "server6_local": False,        # Server6远程运行
            "server6_required": True,      # 必须有Server6
            "mdb_path": None,
        }

# ===== 健康检查配置 =====
# 2025/06/27 09：14 修改多平台server5 - 添加缺失配置项
HEALTH_CHECK_INTERVAL = 60      # 远程运行，标准健康检查
DB_CONNECTION_TIMEOUT = 15      # 远程连接，长超时

# ===== 开发/调试配置 =====
if DEBUG:
    # Windows远程开发环境特殊配置
    print(f"🐛 {SERVICE_NAME} 运行在调试模式")
    print(f"📊 PostgreSQL: {PG_HOST}:{PG_PORT}")
    print(f"🍃 MongoDB: {MONGO_HOST}:{MONGO_PORT}")
    print(f"⚡ Redis: {REDIS_HOST}:{REDIS_PORT}")
    print(f"💾 ODBC可用: {PlatformConfig.get_odbc_available()}")

# 25.06.26 使用环境变量覆盖平台检测
FORCE_PLATFORM = os.getenv("FORCE_PLATFORM")  # 可以强制指定平台类型
USE_MOCK_MDB = os.getenv("USE_MOCK_MDB", "true")  # Windows远程默认使用模拟MDB

# 导出配置
PLATFORM_CONFIG = PlatformConfig.get_config() 