# server5/config/config.py
# Server5 数据同步微服务配置文件
# 创建时间: 2025/06/27
# 用途: MDB-PostgreSQL双向同步的配置管理

import os
import platform
from pathlib import Path

# 服务基本配置
SERVICE_NAME = "MySuite Server5 - 数据同步微服务"
SERVICE_VERSION = "1.0.0"
SERVICE_PORT = 8009

# 运行环境配置
ENVIRONMENT = os.getenv("ENVIRONMENT", "development")  # development/production
DEBUG = ENVIRONMENT == "development"

# ===== 数据库配置 =====

# PostgreSQL主数据库 (位于Windows 10: ************)
PG_HOST = "************"
PG_PORT = 5432
PG_USER = "postgres"
PG_PASS = "pojiami0602"
PG_DB_NAME = "postgres"
PG_DATABASE_URL = f"postgresql://{PG_USER}:{PG_PASS}@{PG_HOST}:{PG_PORT}/{PG_DB_NAME}"

# IMDB数据库 (entries分区表位于此数据库)
IMDB_HOST = "************"
IMDB_PORT = 5432
IMDB_USER = "postgres"
IMDB_PASS = "pojiami0602"
IMDB_DB_NAME = "imdb"
IMDB_DATABASE_URL = f"postgresql://{IMDB_USER}:{IMDB_PASS}@{IMDB_HOST}:{IMDB_PORT}/{IMDB_DB_NAME}"

# MongoDB配置 (位于Windows 10: ************)
MONGO_HOST = "************"
MONGO_PORT = 27017
MONGO_DB = "server5_logs"
MONGO_URL = f"mongodb://{MONGO_HOST}:{MONGO_PORT}"

# Redis配置 (位于Ubuntu本地)
REDIS_HOST = "localhost"
REDIS_PORT = 6379
REDIS_DB = 5  # 使用数据库5避免与其他服务冲突
REDIS_URL = f"redis://{REDIS_HOST}:{REDIS_PORT}/{REDIS_DB}"

# ===== MDB数据库配置 (Windows专用) =====
# 注意：这个路径在Ubuntu开发环境中不可用，仅在Windows部署时生效
MDB_PATH = r"D:\actest25\6.mdb"  # Windows路径
MDB_BACKUP_PATH = r"D:\actest25\backup"  # Windows备份路径

# ===== Server6 MDB网关配置 =====
# 20250626.s5，6 - 进行客户端的修改，Server6网关配置
# Server6位于Win10机器上，提供MDB数据访问API
SERVER6_HOST = "************"    # Server6所在的Win10机器IP
SERVER6_PORT = 8019              # Server6监听端口（修复端口冲突）
SERVER6_BASE_URL = f"http://{SERVER6_HOST}:{SERVER6_PORT}"
SERVER6_API_KEY = None           # 如果需要API密钥认证
SERVER6_TIMEOUT = 120            # 请求超时时间(秒) - 增加到120秒以处理大批量查询
SERVER6_MAX_RETRIES = 3          # 最大重试次数

# Server6连接配置
SERVER6_CONFIG = {
    "base_url": SERVER6_BASE_URL,
    "api_key": SERVER6_API_KEY,
    "timeout": SERVER6_TIMEOUT,
    "max_retries": SERVER6_MAX_RETRIES,
    "endpoints": {
        "health": "/health",
        "test": "/mdb/test",
        "query": "/mdb/query",
        "insert": "/mdb/insert",
        "update": "/mdb/update",
        "delete": "/mdb/delete"
    }
}

# ===== 异步任务配置 =====
# PostgreSQL LISTEN/NOTIFY配置
NOTIFY_CHANNELS = [
    "push_job",           # 数据推送任务
    "partition_check",    # 分区检查
    "sync_trigger",       # 同步触发器
]

# 任务队列配置
WORKER_CONCURRENCY = 5        # 并发工作线程数
QUEUE_BATCH_SIZE = 100        # 批处理大小
SYNC_TIMEOUT = 30            # 同步超时时间(秒)

# ===== 数据同步配置 =====
# f2推送回写配置
OPERATION_TIMEOUT = 30          # 操作超时时间(秒)

# f3数据拉取配置
# 2025/07/08 - 修改为定时执行，弃用PULL_INTERVAL
# PULL_INTERVAL = 3600            # 2025 07/04 +  16：00 + 相关主题: 拉取间隔(秒) - 从5分钟改为1小时
F3_PULL_SCHEDULE = ["02:00", "03:00"]  # f3/f5 同步任务的每日执行时间 (HH:MM格式)
PULL_BATCH_SIZE = 100           # 拉取批次大小

# f5批量同步配置
# 2025 07/04 +  16：00 + 相关主题: f5不再由定时器触发，而是由f3在成功执行后调用。因此，以下定时器配置不再生效。
BULK_SYNC_INTERVAL = 7200       # 批量同步间隔(秒) - 此设置已失效
BULK_SYNC_BATCH_SIZE = 200      # 批量同步批次大小
BULK_SYNC_DAYS = 60             # 2025 07/04 +  16：00 + 相关主题: 将删除检查的范围扩大到最近60天
CONSISTENCY_CHECK_INTERVAL = 7200  # 一致性检查间隔(秒) - 此设置已失效

# f6专属ID同步配置
USER_SYNC_DAYS = 30          # 用户数据同步天数
AUTO_SYNC_INTERVAL = 300     # 自动同步间隔(秒) - 5分钟

# 分区管理配置
PARTITION_RETENTION_MONTHS = 12  # 分区保留月数
AUTO_PARTITION_CREATE = True     # 自动创建分区

# ===== 日志配置 =====
LOG_LEVEL = "INFO" if not DEBUG else "DEBUG"
LOG_DIR = Path(__file__).parent.parent / "logs"
LOG_ROTATION = "1 day"           # 日志轮转时间
LOG_RETENTION = "30 days"        # 日志保留时间

# ===== 安全配置 =====
JWT_SECRET_KEY = "server5-data-sync-secret-key-2025"
JWT_ALGORITHM = "HS256"
JWT_EXPIRE_HOURS = 24

# API限流配置
RATE_LIMIT_REQUESTS = 100        # 每分钟请求限制
RATE_LIMIT_WINDOW = 60          # 限流窗口(秒)

# ===== 跨平台兼容性配置 =====
class PlatformConfig:
    """平台配置类 - 多平台自动适配"""
    
    @staticmethod
    def is_windows():
        """检查是否为Windows平台"""
        return platform.system() == 'Windows'
    
    @staticmethod
    def is_linux():
        """检查是否为Linux平台"""
        return platform.system() == 'Linux'
    
    @staticmethod
    def is_darwin():
        """检查是否为macOS平台"""
        return platform.system() == 'Darwin'
    
    @staticmethod
    def get_platform_name():
        """获取平台名称"""
        return platform.system()
    
    @staticmethod
    def get_odbc_available():
        """检查ODBC是否可用"""
        if PlatformConfig.is_windows():
            try:
                import win32com.client
                return True
            except ImportError:
                return False
        else:
            # 在非Windows平台上，可以考虑其他ODBC解决方案
            return False
    
    @staticmethod
    def get_mdb_path():
        """获取MDB数据库路径"""
        if PlatformConfig.is_windows():
            # Windows环境下的真实MDB路径
            return r"D:\actest25\6.mdb"
        else:
            # 非Windows环境下的模拟路径
            return "/tmp/mock_database.db"
    
    @staticmethod
    def get_config():
        """获取平台特定的配置"""
        platform_name = PlatformConfig.get_platform_name()
        
        config = {
            "platform": platform_name,
            "use_mock_in_linux": True,  # 在Linux上使用模拟模式
            "enable_remote_mdb": False,  # 是否启用远程MDB访问
            "remote_mdb_api_base": "http://************:8009",  # 远程MDB API地址
        }
        
        if platform_name == "Windows":
            config.update({
                "mdb_path": r"D:\actest25\6.mdb",
                "prefer_real_mdb": True,
            })
        elif platform_name == "Linux":
            config.update({
                "mdb_path": "/tmp/mock_database.db",
                "prefer_real_mdb": False,
            })
        elif platform_name == "Darwin":
            config.update({
                "mdb_path": "/tmp/mock_database.db",
                "prefer_real_mdb": False,
            })
        
        return config

# ===== 健康检查配置 =====
HEALTH_CHECK_INTERVAL = 60      # 健康检查间隔(秒)
DB_CONNECTION_TIMEOUT = 10      # 数据库连接超时(秒)

# ===== 开发/调试配置 =====
if DEBUG:
    # 开发环境特殊配置
    LOG_LEVEL = "DEBUG"
    WORKER_CONCURRENCY = 2
    AUTO_SYNC_INTERVAL = 60  # 开发环境1分钟同步一次
    
    print(f"🐛 {SERVICE_NAME} 运行在调试模式")
    print(f"📊 PostgreSQL: {PG_HOST}:{PG_PORT}")
    print(f"🍃 MongoDB: {MONGO_HOST}:{MONGO_PORT}")
    print(f"⚡ Redis: {REDIS_HOST}:{REDIS_PORT}")
    print(f"💾 ODBC可用: {PlatformConfig.get_odbc_available()}")

# 25.06.26 使用环境变量覆盖平台检测
FORCE_PLATFORM = os.getenv("FORCE_PLATFORM")  # 可以强制指定平台类型
USE_MOCK_MDB = os.getenv("USE_MOCK_MDB", "false").lower() == "true"  # 强制使用模拟MDB 