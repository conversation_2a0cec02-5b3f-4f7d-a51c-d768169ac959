# №02025/07/08 + Server5完整启动脚本（包含HTTP API）
# Server5多平台启动 - Ubuntu远程模式 (包含API服务)

import os
import sys
import asyncio
import logging
from pathlib import Path
import uvicorn
import threading
import time
import signal
import subprocess
import socket
import atexit

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.StreamHandler(sys.stdout),
        logging.FileHandler('logs/server5_with_api.log', encoding='utf-8')
    ]
)

logger = logging.getLogger(__name__)

# 添加项目路径
sys.path.insert(0, str(Path(__file__).parent))

# 全局变量用于进程管理
http_server_process = None
services_list = []

def cleanup_on_exit():
    """退出时的清理函数"""
    global http_server_process, services_list
    print("\n🧹 执行清理操作...")
    
    # 停止HTTP服务器
    if http_server_process and http_server_process.poll() is None:
        print("🛑 停止HTTP服务器...")
        http_server_process.terminate()
        try:
            http_server_process.wait(timeout=5)
        except subprocess.TimeoutExpired:
            http_server_process.kill()
    
    # 停止微服务
    if services_list:
        print("🛑 停止微服务...")
        asyncio.run(stop_services(services_list))
    
    print("✅ 清理完成")

def check_port_available(port):
    """检查端口是否可用"""
    try:
        with socket.socket(socket.AF_INET, socket.SOCK_STREAM) as s:
            s.bind(('localhost', port))
            return True
    except OSError:
        return False

def kill_existing_processes(port):
    """杀掉占用指定端口的进程 - 优化版本"""
    try:
        # 查找占用端口的进程
        result = subprocess.run(
            ['lsof', '-ti', f':{port}'],
            capture_output=True,
            text=True
        )
        
        if result.stdout.strip():
            pids = result.stdout.strip().split('\n')
            print(f"🔍 发现占用端口{port}的进程: {pids}")
            
            # 首先尝试优雅关闭 (SIGTERM)
            for pid in pids:
                if pid.strip():
                    try:
                        subprocess.run(['kill', '-15', pid.strip()], check=True)
                        print(f"📨 向进程 {pid} 发送SIGTERM信号")
                    except subprocess.CalledProcessError:
                        print(f"⚠️ 无法向进程 {pid} 发送SIGTERM")
            
            # 等待优雅关闭
            print(f"⏳ 等待进程优雅关闭...")
            time.sleep(2)
            
            # 检查是否还有进程占用端口
            result = subprocess.run(
                ['lsof', '-ti', f':{port}'],
                capture_output=True,
                text=True
            )
            
            if result.stdout.strip():
                # 如果还有进程，强制杀死
                remaining_pids = result.stdout.strip().split('\n')
                print(f"⚠️ 仍有进程占用端口，强制杀死: {remaining_pids}")
                
                for pid in remaining_pids:
                    if pid.strip():
                        try:
                            subprocess.run(['kill', '-9', pid.strip()], check=True)
                            print(f"💀 已强制杀死进程 {pid}")
                        except subprocess.CalledProcessError:
                            print(f"❌ 无法强制杀死进程 {pid}")
            
            # 等待端口释放
            print(f"⏳ 等待端口{port}释放...")
            time.sleep(3)
            
            # 多次检查端口是否释放
            for i in range(5):
                if check_port_available(port):
                    print(f"✅ 端口{port}已释放")
                    break
                time.sleep(1)
                print(f"⏳ 重新检查端口{port}... ({i+1}/5)")
            else:
                print(f"❌ 端口{port}仍被占用")
                return False
        else:
            print(f"✅ 端口{port}可用")
        
        return True
    except Exception as e:
        print(f"❌ 检查端口时出错: {e}")
        return False

# 注册退出清理函数
atexit.register(cleanup_on_exit)

# 导入配置选择器并加载配置
from app.config_selector import ConfigSelector

# 在导入任何服务之前先加载配置
def load_configuration():
    """加载配置"""
    try:
        selector = ConfigSelector()
        config_dict, config_name = selector.load_config()
        config_module = selector.apply_config_to_module(config_dict)
        
        # 显示配置信息
        info = selector.get_current_config_info()
        print(f"📋 配置信息:")
        print(f"  - 配置文件: {config_name}")
        print(f"  - 平台: {info['platform']}")
        print(f"  - 主机: {info['hostname']}")
        print(f"  - IP: {info['local_ip']}")
        print(f"  - 自动检测: {info['auto_detected']}")
        
        return config_module
    except Exception as e:
        print(f"❌ 配置加载失败: {e}")
        raise

# 加载配置
config_module = load_configuration()

def start_http_server():
    """在单独的线程中启动HTTP服务器"""
    global http_server_process
    try:
        print("🌐 启动HTTP API服务器...")
        
        # 检查并清理端口
        if not kill_existing_processes(config_module.SERVICE_PORT):
            print(f"❌ 无法释放端口{config_module.SERVICE_PORT}")
            return False
        
        # 使用subprocess启动uvicorn，这样可以更好地管理进程
        cmd = [
            sys.executable, '-m', 'uvicorn',
            'app.main:app',
            '--host', '0.0.0.0',
            '--port', str(config_module.SERVICE_PORT),
            '--log-level', 'info'
        ]
        # 如需开发热重载，可加: cmd.append('--reload')
        
        print(f"🔧 执行命令: {' '.join(cmd)}")
        
        http_server_process = subprocess.Popen(
            cmd,
            stdout=subprocess.PIPE,
            stderr=subprocess.STDOUT,
            universal_newlines=True,
            bufsize=1,
            cwd=str(Path(__file__).parent)  # 设置工作目录
        )
        
        # 等待服务器启动
        time.sleep(5)
        
        if http_server_process.poll() is None:
            print(f"✅ HTTP服务器启动成功，PID: {http_server_process.pid}")
            return True
        else:
            # 读取输出以查看错误信息
            stdout, stderr = http_server_process.communicate()
            print(f"❌ HTTP服务器启动失败，输出: {stdout}")
            if stderr:
                print(f"❌ 错误输出: {stderr}")
            return False
            
    except Exception as e:
        print(f"❌ HTTP服务器启动失败: {e}")
        import traceback
        traceback.print_exc()
        return False

async def run_services():
    """异步启动 f1、f2、f3、f4 服务"""
    global services_list
    print("🚀 启动Server5微服务...")

    from app.services.f1_listener import ListenerService
    from app.services.f2_push_writer import PushWriterServiceFixed
    from app.services.f3_data_puller import DataPullerService
    from app.services.f4_operation_handler import OperationHandlerService

    # 实例化服务
    f1 = ListenerService()
    f2 = PushWriterServiceFixed()
    f3 = DataPullerService()
    f4 = OperationHandlerService()
    
    # 保存服务列表用于清理
    services_list = [f1, f2, f3, f4]

    # 启动所有服务
    results = await asyncio.gather(
        f1.start(),
        f2.start(),
        f3.start(),
        f4.start(),
        return_exceptions=True,
    )

    # 打印启动结果
    services = ["f1_listener", "f2_push_writer", "f3_data_puller", "f4_operation_handler"]
    for name, res in zip(services, results):
        status = "✅" if (isinstance(res, bool) and res) else "❌"
        print(f"{status} {name} {'启动成功' if status=='✅' else '启动失败'}")

    # 如果有任何服务启动失败，直接退出
    if not all(isinstance(r, bool) and r for r in results):
        print("❌ 有服务启动失败，请检查日志。即将退出...")
        await stop_services(services_list)
        return False

    print("🎉 所有微服务已启动")
    return True

async def stop_services(service_list):
    """停止所有服务"""
    if not service_list:
        return
    
    print("🛑 正在停止微服务...")
    stop_coros = []
    for svc in service_list:
        if hasattr(svc, "stop"):
            stop_coros.append(svc.stop())
    
    if stop_coros:
        await asyncio.gather(*stop_coros, return_exceptions=True)
    print("✅ 微服务已停止")

def signal_handler(signum, frame):
    """信号处理器"""
    print(f"\n📡 接收到信号 {signum}，正在关闭服务...")
    cleanup_on_exit()
    sys.exit(0)

def main():
    """主函数 - 启动微服务和HTTP API"""
    print("🚀 MySuite Server5 - 完整启动模式 (微服务 + HTTP API)")
    
    # 注册信号处理器
    signal.signal(signal.SIGINT, signal_handler)
    signal.signal(signal.SIGTERM, signal_handler)

    # 启动HTTP服务器
    if not start_http_server():
        print("❌ HTTP服务器启动失败，退出")
        return

    try:
        # 启动微服务
        success = asyncio.run(run_services())
        if not success:
            print("❌ 微服务启动失败，退出")
            return

        print("🎉 Server5完整启动成功！")
        print(f"📡 HTTP API地址: http://localhost:{config_module.SERVICE_PORT}")
        print("🔧 微服务状态: 运行中")
        print("👋 按 Ctrl+C 退出")

        # 保持运行
        while True:
            time.sleep(1)

    except KeyboardInterrupt:
        print("\n👋 接收到中断信号，正在关闭服务...")
    except Exception as e:
        print(f"❌ 启动失败: {e}")
        logging.error(f"Server5服务启动失败: {e}")
        sys.exit(1)

if __name__ == "__main__":
    main() 