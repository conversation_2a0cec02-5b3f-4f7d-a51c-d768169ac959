# MySuite PLC编程工具使用说明

## 概述
MySuite PLC编程工具是一个专业的PLC代码转换和梯形图显示工具，支持ST (Structured Text)、LD (Ladder Diagram)、XML格式之间的相互转换，并提供实时梯形图显示和动态运行功能。

## 新增功能 (最新更新)

### 1. 参考2.py的连接方式
- 采用socket预连接测试，提高连接稳定性
- 先进行TCP连接验证，再建立PYRO连接
- 增强错误处理和连接状态反馈

### 2. 交通红绿灯ST代码演示
程序预设了完整的交通红绿灯控制ST代码，包含：
- 紧急停止处理逻辑
- 手动模式控制
- 4状态循环：红灯→红黄灯→绿灯→黄灯
- 定时器控制（红灯30s，绿灯25s，黄灯5s，红黄灯3s）
- 完整的变量定义和状态机实现

### 3. 梯形图显示功能
- **显示梯形图按钮**：将ST代码转换为标准梯形图表示
- **运行梯形图按钮**：启动梯形图动态运行演示
- **梯形图显示区域**：专门的显示窗口，支持文本和图形显示
- **实时状态指示**：显示当前梯形图加载和运行状态

### 4. 梯形图特性
- 使用lxml解析XML，生成标准梯形图格式
- 支持常开触点 `|---|`、常闭触点 `|─/─|`、线圈输出 `( )`
- 定时器显示 `|TON|` 和时间设定 `T#时间`
- 动态运行时显示激活状态：`●` 激活，`○` 非激活
- 网络状态标识：`[●活动]` 和 `[○非活动]`

## 主要功能

### 1. 代码转换功能
- **ST → LD**: 将结构化文本转换为梯形图
- **ST → XML**: 将结构化文本转换为XML格式
- **LD → ST**: 将梯形图转换为结构化文本
- **XML → ST**: 将XML格式转换为结构化文本

### 2. 图表显示功能
- **系统架构图**: 显示整体系统架构
- **PLC网络拓扑图**: 显示PLC网络连接关系
- **梯形图显示**: 实时显示和运行梯形图

### 3. 文件管理功能
- 加载ST文件
- 保存ST文件
- 保存转换结果
- 清空编辑器

## 界面布局

### 上部 - 双图表显示区域
```
┌─────────────────┬─────────────────┐
│   系统架构图     │  PLC网络拓扑图   │
│                │                │
└─────────────────┴─────────────────┘
```

### 中部 - 主工作区域 (5:1:5布局)
```
┌─────────────┬───┬─────────────┐
│  ST代码输入  │转换│  转换结果   │
│             │按钮│            │
│             │区域│            │
└─────────────┴───┴─────────────┘
```

### 下部 - 功能、梯形图、日志区域 (1:2:1布局)
```
┌─────┬─────────────────┬─────┐
│功能 │   梯形图显示     │日志 │
│按钮 │                │区域 │
└─────┴─────────────────┴─────┘
```

## 安装和启动

### 1. 依赖安装
```bash
pip install -r requirements.txt
```

新增依赖：
- `lxml>=4.6.0` - XML解析和梯形图生成

### 2. 启动Beremiz微服务
```bash
# 使用venvbere虚拟环境启动
venvbere/bin/python beremiz/Beremiz_service.py -p 61194 -a 1 ~/beremiz_runtime_workdir
```

### 3. 启动PLC编程工具
```bash
python program2.py <token> <employee_id> <employee_name>
```

## 使用流程

### 1. 基本转换流程
1. 启动程序后，点击"连接微服务"
2. 在ST代码输入区编写或使用预设的交通灯代码
3. 选择转换类型（ST→LD, ST→XML等）
4. 查看转换结果
5. 可选：保存转换结果

### 2. 梯形图显示流程
1. 确保已连接Beremiz微服务
2. 在ST代码输入区输入代码（或使用预设交通灯代码）
3. 点击"显示梯形图"按钮
4. 查看生成的梯形图
5. 点击"运行梯形图"查看动态运行效果

### 3. 交通灯演示
程序预设的交通灯代码包含完整的状态机逻辑：
- 状态0：红灯（30秒）
- 状态1：红黄灯（3秒）
- 状态2：绿灯（25秒）
- 状态3：黄灯（5秒）

运行梯形图时会显示：
- 当前状态和循环次数
- 各网络的激活状态
- 输出信号的实时状态

## 技术架构

### 连接方式
- 参考2.py的socket连接方式
- TCP预连接 + PYRO协议
- 连接地址：127.0.0.1:61194
- 超时设置：3秒TCP，10秒PYRO

### 梯形图生成
- XML解析：使用lxml库
- 格式转换：ST → XML → 梯形图文本
- 动态显示：QTimer定时更新
- 状态管理：状态机模拟

### 虚拟环境
- Beremiz运行环境：venvbere
- 启动命令：`venvbere/bin/python beremiz/Beremiz_service.py`

## 故障排除

### 1. 连接问题
- 确认Beremiz微服务已启动
- 检查端口61194是否被占用
- 验证venvbere环境是否正确配置

### 2. 梯形图显示问题
- 确认lxml依赖已安装
- 检查ST代码语法是否正确
- 验证XML转换是否成功

### 3. 动态运行问题
- 确认梯形图已成功生成
- 检查QTimer是否正常工作
- 验证状态机逻辑是否正确

## 更新历史

### 2025/06/26 - v2.0.0
- 参考2.py改进连接方式
- 添加交通红绿灯ST代码演示
- 新增梯形图显示和运行功能
- 优化UI布局（1:2:1底部布局）
- 增加lxml依赖用于XML解析
- 实现动态梯形图运行演示

### 2025/06/23 - v1.0.0
- 初始版本发布
- 基本ST/LD/XML转换功能
- 双图表显示功能
- 文件管理功能 