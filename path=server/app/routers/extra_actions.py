# 注释32 增加ui基面的功能 - 额外业务路由
import os, uuid, xml.etree.ElementTree as ET
from datetime import datetime
from typing import List, Dict
from fastapi import APIRouter, HTTPException
from pydantic import BaseModel, Field
from sqlalchemy import text

from ..databases.postgresql.client import AsyncSessionLocal          # DB 连接
from app.main import DB_CONNECTED                                     # 数据库可用状态

router = APIRouter(prefix="/api/extra", tags=["extra"])
XML_DIR = os.path.abspath(os.path.join(os.path.dirname(__file__), "../../xml_data2"))
os.makedirs(XML_DIR, exist_ok=True)


# ---------- 1) n+1 功能 ----------
class AddOneReq(BaseModel):
    n: int = Field(..., ge=0)

class AddOneResp(BaseModel):
    result: int

@router.post("/add_one", response_model=AddOneResp)
async def add_one(body: AddOneReq):
    return AddOneResp(result=body.n + 1)


# ---------- 2) 读取 1.xml / 2.xml ----------
class IndexReq(BaseModel):
    index: int = Field(..., ge=1)

@router.post("/read_data_xml")
async def read_data_xml(body: IndexReq):
    if body.index not in (1, 2):
        raise HTTPException(400, detail="只支持 1 或 2")
    f_path = os.path.join(XML_DIR, f"{body.index}.xml")
    if not os.path.exists(f_path):
        raise HTTPException(404, detail="XML 文件不存在")

    def _parse_table_xml(path) -> List[Dict]:
        tree = ET.parse(path)
        root = tree.getroot()
        recs = []
        for rec in root.findall("record"):
            d = {fld.get("name"): (fld.text or "") for fld in rec.findall("field")}
            recs.append(d)
        return recs

    return {"records": _parse_table_xml(f_path)}


# ---------- 3) 上传项目进度 -> 3.xml + PostgreSQL ----------
class ProgressRow(BaseModel):
    employee_id: str
    project1: str = ""
    project2: str = ""
    project3: str = ""

class ProgressReq(BaseModel):
    rows: List[ProgressRow]

@router.post("/upload_progress")
async def upload_progress(body: ProgressReq):
    # 3.xml 追加
    xml3 = os.path.join(XML_DIR, "3.xml")
    if os.path.exists(xml3):
        tree = ET.parse(xml3)
        root = tree.getroot()
    else:
        root = ET.Element("entries")
        tree = ET.ElementTree(root)

    for r in body.rows:
        entry = ET.SubElement(root, "entry", {
            "id": str(uuid.uuid4()),
            "ts": datetime.utcnow().isoformat()
        })
        for k, v in r.dict().items():
            child = ET.SubElement(entry, k)
            child.text = v
    tree.write(xml3, encoding="utf-8", xml_declaration=True)

    # 写入数据库（若可用）
    if DB_CONNECTED:
        async with AsyncSessionLocal() as session:
            for r in body.rows:
                table_name = f'progress_{r.employee_id}'
                await session.execute(text(
                    f'CREATE TABLE IF NOT EXISTS "{table_name}"'
                    '(ts TIMESTAMPTZ, project1 TEXT, project2 TEXT, project3 TEXT)'
                ))
                await session.execute(
                    text(f'INSERT INTO "{table_name}" VALUES (:ts,:p1,:p2,:p3)'),
                    {"ts": datetime.utcnow(),
                     "p1": r.project1, "p2": r.project2, "p3": r.project3}
                )
            await session.commit()

    return {"status": "success", "message": "数据已保存"}


# ---------- 4) 返回 4.xml ----------
@router.get("/progress_list")
async def get_progress_list():
    xml4 = os.path.join(XML_DIR, "4.xml")
    if not os.path.exists(xml4):
        raise HTTPException(404, detail="4.xml 不存在")

    def _parse(path):
        tree = ET.parse(path)
        root = tree.getroot()
        recs = []
        for entry in root.findall("entry"):
            rec = {child.tag: (child.text or "") for child in entry}
            rec["id"] = entry.get("id")
            rec["ts"] = entry.get("ts")
            recs.append(rec)
        return recs

    return {"records": _parse(xml4)} 