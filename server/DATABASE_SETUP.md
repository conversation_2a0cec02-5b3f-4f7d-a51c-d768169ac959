# MySuite 数据库环境搭建指南

## 常见报错

常见报错：
```
sqlalchemy.exc.InvalidRequestError: The asyncio extension requires an async driver to be used. The loaded 'psycopg2' is not async.
```

**原因**：SQLAlchemy异步扩展需要异步驱动，请确保URL使用了 asyncpg 而不是 psycopg2。

## 解决方法

### 1. 检查数据库URL
请务必将 `postgresql://` 改为 `postgresql+asyncpg://`。

### 2. 推荐的引擎参数
```python
engine = create_async_engine(
    DATABASE_URL,
    echo=False,
    future=True,
    pool_size=10,
    max_overflow=20,
    pool_recycle=3600,
    pool_pre_ping=True,
)
```

### 3. 连接池说明
常见连接池问题：
- 连接数不足导致阻塞
- 长时间无请求导致连接断开
- 数据库重启后连接失效，出现 503 错误
- 连接池参数需根据实际业务调整

## 测试方法

### 方法1：不连接数据库（仅测试API启动）
```bash
cd server
python test_server.py no-db
```
或
```bash
python test_server.py
# 默认模式
```

### 方法2：连接本地数据库
本地PostgreSQL配置：
- Host: localhost
- Port: 5432
- Database: postgres
- User: postgres
- Password: 123456

示例命令：
```bash
cd server
python test_server.py db
```

### 方法3：测试数据库连接
```bash
cd server
python test_server.py test-db
```

## 数据库配置

建议配置文件位于 `server/app/databases/postgresql/client.py`：
```python
DB_HOST = "localhost"
DB_PORT = 5432
DB_NAME = "postgres"
DB_USER = "postgres"
DB_PASS = "123456"
```

如有需要可通过环境变量覆盖。

## 常用接口

### 基础接口（可用于健康检查）
- `GET /` - 首页
- `GET /docs` - API文档
- `GET /health` - 健康检查
- `GET /api/db/status` - 数据库状态
- `GET /api/client/version` - 客户端版本
- `POST /api/excel/upload` - 上传Excel
- `POST /api/serial/data` - 上传串口数据
- `POST /api/xml/write` - 写入XML
- `GET /api/xml/read/{client_id}` - 读取XML

### 业务相关接口
- `GET /api/feature_flags/` - 获取所有特性开关
- `POST /api/feature_flags/` - 设置特性开关
- `GET /api/tasks/pending` - 获取待办任务
- `/api/odbc/` - ODBC相关接口
- `/ws/flags` - WebSocket推送

## 环境准备

建议依次执行以下命令：
```bash
conda env create -f environment.yml
conda activate my_suite_server
```

依赖说明：
- `asyncpg`（异步PostgreSQL驱动）
- `psycopg2`（同步PostgreSQL驱动，可选）
- `sqlalchemy`（ORM）
- `fastapi`（Web框架）
- `uvicorn`（ASGI服务器）

## 常见问题排查

### 启动PostgreSQL服务：

1. **启动PostgreSQL服务**：
   ```bash
   # Windows
   net start postgresql
   
   # Linux/Mac
   sudo service postgresql start
   ```

2. **连接数据库**：
   ```bash
   psql -h localhost -p 5432 -U postgres -d postgres
   ```

3. **确认端口监听**：
   请确认数据库监听5432端口。

4. **数据库用户权限**：
   请确保数据库用户有足够权限。

### 推荐的环境变量配置：

可在 `server/app/databases/postgresql/client.py` 通过环境变量灵活配置：
```python
DB_HOST = os.getenv("DB_HOST", "localhost")
DB_PORT = int(os.getenv("DB_PORT", 5432))
DB_NAME = os.getenv("DB_NAME", "postgres")
DB_USER = os.getenv("DB_USER", "postgres")
DB_PASS = os.getenv("DB_PASS", "123456")
```