# 注释42 再次增加ui基面的功能 - Redis缓存服务
# 20250619.13:00 修复Redis异步支持 - 兼容不同Redis版本
try:
    import redis.asyncio as redis
except ImportError:
    try:
        import redis
        # 如果没有asyncio模块，使用同步版本并添加异步包装
        print("警告: 使用同步Redis客户端，性能可能受影响")
    except ImportError:
        redis = None
        print("错误: 未安装Redis库")

from typing import Optional, Dict, Any
from ...config import REDIS_HOST, REDIS_PORT, REDIS_DB

import hashlib
import time

class RedisService:
    """Redis缓存服务类"""
    
    def __init__(self):
        self.redis_client: redis.Redis | None = None
        self.available = False
        self.is_async = False  # 20250619.13:00 修复Redis异步支持 - 标记是否为异步客户端
    
    async def _safe_call(self, method, *args, **kwargs):
        """20250619.13:00 修复Redis异步支持 - 安全调用Redis方法（处理异步/同步）"""
        try:
            if hasattr(method, '__await__'):
                return await method(*args, **kwargs)
            else:
                return method(*args, **kwargs)
        except Exception as e:
            print(f"Redis操作失败: {e}")
            return None
    
    # 20250619.13:00 修复Redis异步支持 - 将连接逻辑改为异步
    async def connect(self):
        """连接Redis服务器"""
        if redis is None:
            print("Redis库未安装，跳过Redis连接")
            self.available = False
            return
            
        try:
            print(f"尝试连接Redis: {REDIS_HOST}:{REDIS_PORT}")
            # 20250619.13:00 修复Redis异步支持 - 检查是否有异步支持
            if hasattr(redis, 'ConnectionPool'):
                # 创建异步连接池
                pool = redis.ConnectionPool(
                    host=REDIS_HOST,
                    port=REDIS_PORT,
                    db=REDIS_DB,
                    decode_responses=True
                )
                self.redis_client = redis.Redis(connection_pool=pool)
                # 测试连接
                if hasattr(self.redis_client, 'ping'):
                    ping_result = await self._safe_call(self.redis_client.ping)
                    if ping_result is not None:
                        self.is_async = hasattr(self.redis_client.ping, '__await__')
                    else:
                        raise Exception("Ping failed")
                else:
                    # 同步版本的ping
                    self.redis_client.ping()
                    self.is_async = False
            else:
                # 回退到基本连接
                self.redis_client = redis.Redis(
                    host=REDIS_HOST,
                    port=REDIS_PORT,
                    db=REDIS_DB,
                    decode_responses=True
                )
                self.redis_client.ping()
                self.is_async = False
                
            self.available = True
            print(f"Redis连接成功: {REDIS_HOST}:{REDIS_PORT}")
        except Exception as e:
            print(f"Redis连接失败: {e}")
            self.available = False
            self.redis_client = None
    
    # 20250619.13:00 修复Redis异步支持 - 将关闭逻辑改为异步
    async def close(self):
        """关闭Redis连接"""
        if self.redis_client:
            try:
                if hasattr(self.redis_client, 'close'):
                    if hasattr(self.redis_client.close, '__await__'):
                        await self.redis_client.close()
                    else:
                        self.redis_client.close()
                print("Redis connection closed.")
            except Exception as e:
                print(f"Redis关闭连接时出错: {e}")

    async def set_sensor_data(self, sensor_id: str, value: float, expire_seconds: int = 300):
        """缓存传感器数据"""
        if not self.available or not self.redis_client:
            return False
        
        try:
            cache_key = f"sensor:{sensor_id}:latest"
            # 20250619.13:00 修复Redis异步支持 - 使用安全调用方法
            hset_result = await self._safe_call(self.redis_client.hset, cache_key, mapping={
                "value": value,
                "timestamp": time.time(),
                "sensor_id": sensor_id
            })
            expire_result = await self._safe_call(self.redis_client.expire, cache_key, expire_seconds)
            return hset_result is not None and expire_result is not None
        except Exception as e:
            print(f"Redis缓存失败: {e}")
            return False
    
    async def get_sensor_data(self, sensor_id: str) -> Optional[Dict[str, Any]]:
        """获取缓存的传感器数据"""
        if not self.available or not self.redis_client:
            return None
        
        try:
            cache_key = f"sensor:{sensor_id}:latest"
            # 20250619.13:00 修复Redis异步支持 - 使用安全调用方法
            data = await self._safe_call(self.redis_client.hgetall, cache_key)
            if data:
                return {
                    "sensor_id": data.get("sensor_id"),
                    "value": float(data.get("value", 0)),
                    "timestamp": float(data.get("timestamp", 0))
                }
            return None
        except Exception as e:
            print(f"Redis读取失败: {e}")
            return None
    
    async def get_status(self) -> Dict[str, Any]:
        """获取Redis状态信息"""
        if not self.available or not self.redis_client:
            return { "status": "disconnected", "message": "Redis连接不可用" }
        
        try:
            # 20250619.13:00 修复Redis异步支持 - 使用安全调用方法
            info = await self._safe_call(self.redis_client.info)
            if info:
                return {
                    "status": "connected",
                    "message": "Redis连接正常",
                    "host": REDIS_HOST,
                    "port": REDIS_PORT,
                    "db": REDIS_DB,
                    "memory_used": info.get("used_memory_human", "N/A"),
                    "connected_clients": info.get("connected_clients", 0),
                    "uptime": info.get("uptime_in_seconds", 0)
                }
            else:
                return { "status": "error", "message": "无法获取Redis信息" }
        except Exception as e:
            return { "status": "error", "message": f"Redis状态检查失败: {str(e)}" }

# 全局Redis服务实例
redis_service = RedisService() 