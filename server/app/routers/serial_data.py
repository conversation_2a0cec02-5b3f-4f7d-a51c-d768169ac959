# server/app/routers/serial_data.py

from fastapi import APIRouter, HTTPException
from typing import Dict

router = APIRouter(prefix="/api/serial", tags=["serial"])

@router.post("/data")
async def receive_serial_data(payload: Dict):
    """
    接收客户端发送的串口数据，格式为 {"data": "xxx"}
    """
    try:
        print("Serial data from client:", payload)
        # TODO: 将 payload 中的数据写入 PostgreSQL
        return {"status": "ok"}
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))
