from fastapi import APIRouter, HTTPException, Depends
from typing import List, Dict

from ..database import odbc_client
from ..auth.jwt_auth import verify_token

router = APIRouter(prefix="/api/odbc", tags=["odbc"])

@router.get("/status", response_model=Dict)
async def get_odbc_status():
    """
    获取ODBC连接状态和平台支持信息
    """
    try:
        status = odbc_client.check_connection_status()
        return {
            "status": "ok",
            "data": status
        }
    except Exception as e:
        return {
            "status": "error", 
            "error": str(e),
            "data": {
                "platform_supported": False,
                "is_connected": False,
                "connection_attempted": False
            }
        }

@router.post("/connect", dependencies=[Depends(verify_token)], response_model=Dict)
async def connect_odbc():
    """
    手动连接ODBC数据库
    """
    try:
        odbc_client.connect()
        status = odbc_client.check_connection_status()
        return {
            "status": "success",
            "message": "ODBC连接成功",
            "data": status
        }
    except Exception as e:
        return {
            "status": "error", 
            "message": f"ODBC连接失败: {str(e)}",
            "data": odbc_client.check_connection_status()
        }

@router.post("/disconnect", dependencies=[Depends(verify_token)], response_model=Dict)
async def disconnect_odbc():
    """
    断开ODBC数据库连接
    """
    try:
        odbc_client.close()
        status = odbc_client.check_connection_status()
        return {
            "status": "success",
            "message": "ODBC连接已断开",
            "data": status
        }
    except Exception as e:
        return {
            "status": "error",
            "message": f"断开ODBC连接时出错: {str(e)}",
            "data": odbc_client.check_connection_status()
        }

@router.get("/read", dependencies=[Depends(verify_token)], response_model=List[Dict])
async def read_from_mdb():
    """
    从MDB数据库读取数据 - ODBC(ADODB)
    更改12 odbc按键处理，@client_fixed.py @/server - 增加连接状态检查
    """
    try:
        # 更改12 odbc按键处理，@client_fixed.py @/server - 先检查连接状态
        status = odbc_client.check_connection_status()
        if not status["platform_supported"]:
            raise HTTPException(
                status_code=501, 
                detail="ODBC功能需要Windows平台和pywin32库支持"
            )
        
        if not status["is_connected"]:
            raise HTTPException(
                status_code=400, 
                detail="ODBC未连接，请先点击'连接ODBC'按钮"
            )
        
        sql = "SELECT ID, Name, Email FROM Customers"
        results = odbc_client.execute_query(sql)
        return results
    except HTTPException:
        raise  # 重新抛出HTTP异常
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))
