# server/app/routers/feature_flags.py

from fastapi import APIRouter, Depends, HTTPException, status
from pydantic import BaseModel
from typing import Dict

from sqlalchemy.future import select
from sqlalchemy.exc import IntegrityError

from ..database import AsyncSessionLocal
from ..models import FeatureFlag
from ..auth.jwt_auth import verify_token
from ..websocket.manager import notify_all_clients

router = APIRouter(prefix="/api/feature_flags", tags=["feature_flags"])

# 数据库连接检查装饰器
def check_db_connection():
    from ..main import DB_CONNECTED
    if not DB_CONNECTED:
        raise HTTPException(
            status_code=503, 
            detail="Database not available. Feature flags require database connection."
        )

class FeatureFlagItem(BaseModel):
    name: str
    enabled: bool

@router.get("/", response_model=Dict[str, bool])
async def get_all_flags():
    """
    返回 { "feature_name": true/false, ... }
    """
    check_db_connection()
    async with AsyncSessionLocal() as session:
        result = await session.execute(select(FeatureFlag))
        flags = result.scalars().all()
        return {f.name: f.enabled for f in flags}

@router.post("/", dependencies=[Depends(verify_token)])
async def set_flag(flag: FeatureFlagItem):
    """
    修改指定特性标识的状态，需要 JWT 认证
    """
    check_db_connection()
    async with AsyncSessionLocal() as session:
        existing = await session.get(FeatureFlag, flag.name)
        if existing:
            existing.enabled = flag.enabled
        else:
            new_flag = FeatureFlag(name=flag.name, enabled=flag.enabled)
            session.add(new_flag)
        try:
            await session.commit()
        except IntegrityError:
            await session.rollback()
            raise HTTPException(status_code=500, detail="Database error")
    # 通知所有连接的 WebSocket 客户端
    await notify_all_clients(flag.name, flag.enabled)
    return {"status": "ok"}
