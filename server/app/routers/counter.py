# server/app/routers/counter.py

from fastapi import APIRouter, HTTPException
from pydantic import BaseModel
import os
import json
from cryptography.fernet import Fernet

router = APIRouter(prefix="/api/counter", tags=["counter"])

# 全局计数器变量 (在生产环境中应该存储在数据库或缓存中)
counter_value = 0

# 修改5：登录id - 加密文件路径和密钥配置
SCRIPT_DIR = os.path.dirname(os.path.dirname(os.path.dirname(__file__)))
CONFIG_FOLDER = os.path.join(SCRIPT_DIR, "config")
ENCRYPTED_EMPLOYEE_DATA_FILE = os.path.join(CONFIG_FOLDER, "employees.encrypted")
KEY_FOR_DEMO = b'OtFpdMgbd5k7tzakP6b2g8K5gX_0J6E5IikukRnmZGc='  # 请替换为实际生成的密钥

class CounterResponse(BaseModel):
    n: int
    message: str

# 修改5：登录id - 登录请求模型
class LoginRequest(BaseModel):
    employee_id: str
    password: str

# 修改5：登录id - 登录响应模型
class LoginResponse(BaseModel):
    success: bool
    message: str
    employee_id: str = None

# 修改5：登录id - 解密数据函数
def decrypt_data(encrypted_data, key):
    """解密员工数据"""
    try:
        f = Fernet(key)
        decrypted_bytes = f.decrypt(encrypted_data)
        return json.loads(decrypted_bytes.decode('utf-8'))
    except Exception as e:
        raise Exception(f"解密失败: {str(e)}")

# 修改5：登录id - 加载员工映射数据
def load_employee_mapping():
    """加载员工ID和姓名映射"""
    if not os.path.exists(ENCRYPTED_EMPLOYEE_DATA_FILE):
        return None, None
    
    try:
        with open(ENCRYPTED_EMPLOYEE_DATA_FILE, 'rb') as f:
            encrypted_content = f.read()
        name_to_id_map = decrypt_data(encrypted_content, KEY_FOR_DEMO)
        id_to_name_map = {v: k for k, v in name_to_id_map.items()}
        return name_to_id_map, id_to_name_map
    except Exception as e:
        print(f"加载员工数据失败: {e}")
        return None, None

# 修改5：登录id - 登录验证接口
@router.post("/login", response_model=LoginResponse)
async def login(request: LoginRequest):
    """
    员工登录验证
    """
    if not request.employee_id:
        raise HTTPException(
            status_code=400,
            detail="员工ID是必需的"
        )
    
    if not request.password:
        raise HTTPException(
            status_code=400,
            detail="密码是必需的"
        )
    
    # 加载员工数据
    name_to_id_map, id_to_name_map = load_employee_mapping()
    
    if not name_to_id_map or not id_to_name_map:
        raise HTTPException(
            status_code=500,
            detail="无法加载员工数据文件，请检查配置"
        )
    
    # 验证员工ID是否存在
    if request.employee_id not in id_to_name_map:
        return LoginResponse(
            success=False,
            message="员工ID不存在，请检查输入"
        )
    
    # 在实际应用中，这里应该验证密码
    # 目前简化处理，只要ID存在就认为登录成功
    # 可以根据需要添加密码验证逻辑
    
    return LoginResponse(
        success=True,
        message="登录成功",
        employee_id=request.employee_id
    )

@router.post("/btn1", response_model=CounterResponse)
async def btn1_func():
    """
    btn1功能：每次调用时n+=1，返回当前n值
    """
    global counter_value
    counter_value += 1
    return CounterResponse(
        n=counter_value,
        message=f"按钮点击成功，当前计数: {counter_value}"
    )

@router.get("/current", response_model=CounterResponse)
async def get_current_counter():
    """
    获取当前计数器值
    """
    global counter_value
    return CounterResponse(
        n=counter_value,
        message=f"当前计数: {counter_value}"
    )

@router.post("/btn2", response_model=CounterResponse)
async def btn2_func():
    """
    btn2功能：每次调用时n+=2，返回当前n值
    """
    global counter_value
    counter_value += 2
    return CounterResponse(
        n=counter_value,
        message=f"btn2点击成功，当前计数: {counter_value}"
    )

@router.post("/reset")
async def reset_counter():
    """
    重置计数器
    """
    global counter_value
    counter_value = 0
    return {"message": "计数器已重置", "n": counter_value} 