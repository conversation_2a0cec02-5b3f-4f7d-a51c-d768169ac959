from fastapi import APIRouter, HTTPException
from typing import List, Dict

router = APIRouter(prefix="/api/excel", tags=["excel"])

@router.post("/upload")
async def upload_excel(data: List[Dict]):
    """
    接收客户端上传的 Excel 数据，格式为 JSON 列表
    """
    try:
        print("Received Excel data:", data)
        # TODO: 将接收到的 data 写入 PostgreSQL
        return {"status": "received", "rows": len(data)}
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))
