import os
import xml.etree.ElementTree as ET
from fastapi import APIRouter, HTTPException, Depends
from pydantic import BaseModel
from typing import Dict

from ..auth.jwt_auth import verify_token

router = APIRouter(prefix="/api/xml", tags=["xml"])

# 存放 xml_data 文件的目录
XML_FOLDER = os.path.abspath(os.path.join(os.path.dirname(__file__), "../../xml_data"))

class XMLWriteRequest(BaseModel):
    client_id: str            # 客户端标识，例如 "1"、"2-1"、"2-2"
    data: Dict[str, str]      # 需要写入的数据，例如 {"fieldA":"valueA", "fieldB":"valueB"}

@router.post("/write", dependencies=[Depends(verify_token)])
async def write_to_xml(req: XMLWriteRequest):
    """
    根据 client_id 写入或更新对应的 XML 文件
    """
    client_id = req.client_id
    data = req.data

    # 确保 xml_data 目录存在
    os.makedirs(XML_FOLDER, exist_ok=True)
    xml_path = os.path.join(XML_FOLDER, f"{client_id}.xml")

    try:
        # 如果文件不存在，创建新的根元素 <Root>
        if not os.path.exists(xml_path):
            root = ET.Element("Root")
            tree = ET.ElementTree(root)
        else:
            tree = ET.parse(xml_path)
            root = tree.getroot()

        # 在 <Root> 下添加新的 <Entry> 元素，并在其中添加 <Field key="...">value</Field>
        entry = ET.SubElement(root, "Entry")
        for key, val in data.items():
            field = ET.SubElement(entry, "Field", {"key": key})
            field.text = val

        # 保存 XML 文件
        tree.write(xml_path, encoding="utf-8", xml_declaration=True)
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"写入 XML 失败: {e}")

    return {"status": "ok", "message": f"已写入到 {client_id}.xml"}

# 添加一个测试端点，用于读取XML文件
@router.get("/read/{client_id}")
async def read_xml(client_id: str):
    """
    读取指定客户端的XML文件内容
    """
    xml_path = os.path.join(XML_FOLDER, f"{client_id}.xml")
    
    if not os.path.exists(xml_path):
        raise HTTPException(status_code=404, detail=f"XML文件 {client_id}.xml 不存在")
    
    try:
        tree = ET.parse(xml_path)
        root = tree.getroot()
        
        entries = []
        for entry in root.findall("Entry"):
            entry_data = {}
            for field in entry.findall("Field"):
                key = field.get("key")
                value = field.text
                entry_data[key] = value
            entries.append(entry_data)
        
        return {
            "status": "ok",
            "client_id": client_id,
            "entries": entries
        }
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"读取 XML 失败: {e}")
