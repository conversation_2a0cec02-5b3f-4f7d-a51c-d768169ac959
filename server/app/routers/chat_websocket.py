# server/app/routers/chat_websocket.py
# 20250618.19:20 实时信息交流 - WebSocket聊天路由
"""
实时聊天WebSocket路由
支持百人以上在线信息交流
"""

from fastapi import APIRouter, WebSocket, WebSocketDisconnect, Depends, HTTPException
from typing import Dict, List, Set
import json
import asyncio
from datetime import datetime
import logging
from ..auth.jwt_auth import verify_jwt_token

# 20250618.19:20 实时信息交流 - 配置日志
logger = logging.getLogger(__name__)

router = APIRouter()

class ChatConnectionManager:
    """20250618.19:20 实时信息交流 - 聊天连接管理器"""
    
    def __init__(self):
        # 活跃连接: {employee_id: {websocket, employee_name, last_seen}}
        self.active_connections: Dict[str, Dict] = {}
        # 聊天室连接: {room_id: set(employee_ids)}
        self.chat_rooms: Dict[str, Set[str]] = {}
        # 消息历史缓存 (最近100条)
        self.message_history: List[Dict] = []
        self.max_history = 100
    
    async def connect(self, websocket: WebSocket, employee_id: str, employee_name: str):
        """20250618.19:20 实时信息交流 - 用户连接"""
        await websocket.accept()
        
        # 如果用户已连接，断开旧连接
        if employee_id in self.active_connections:
            old_ws = self.active_connections[employee_id]["websocket"]
            try:
                await old_ws.close()
            except:
                pass
        
        # 添加新连接
        self.active_connections[employee_id] = {
            "websocket": websocket,
            "employee_name": employee_name,
            "last_seen": datetime.now(),
            "status": "online"
        }
        
        # 加入默认聊天室
        default_room = "general"
        if default_room not in self.chat_rooms:
            self.chat_rooms[default_room] = set()
        self.chat_rooms[default_room].add(employee_id)
        
        logger.info(f"User {employee_name} ({employee_id}) connected to chat")
        
        # 发送欢迎消息和在线用户列表
        await self.send_welcome_message(employee_id)
        await self.broadcast_user_list_update()
        
        # 发送最近聊天历史
        await self.send_chat_history(employee_id)
    
    async def disconnect(self, employee_id: str):
        """20250618.19:20 实时信息交流 - 用户断开连接"""
        if employee_id in self.active_connections:
            user_info = self.active_connections[employee_id]
            del self.active_connections[employee_id]
            
            # 从所有聊天室移除
            for room_users in self.chat_rooms.values():
                room_users.discard(employee_id)
            
            logger.info(f"User {user_info['employee_name']} ({employee_id}) disconnected from chat")
            
            # 广播用户列表更新
            await self.broadcast_user_list_update()
    
    async def send_welcome_message(self, employee_id: str):
        """20250618.19:20 实时信息交流 - 发送欢迎消息"""
        if employee_id not in self.active_connections:
            return
        
        user_info = self.active_connections[employee_id]
        welcome_msg = {
            "type": "system_message",
            "message": f"欢迎 {user_info['employee_name']} 加入聊天室！",
            "timestamp": datetime.now().isoformat(),
            "user_id": "system",
            "user_name": "系统"
        }
        
        try:
            await self.active_connections[employee_id]["websocket"].send_text(json.dumps(welcome_msg))
        except Exception as e:
            logger.error(f"Failed to send welcome message to {employee_id}: {e}")
    
    async def send_chat_history(self, employee_id: str):
        """20250618.19:20 实时信息交流 - 发送聊天历史"""
        if employee_id not in self.active_connections:
            return
        
        history_msg = {
            "type": "chat_history",
            "messages": self.message_history[-20:],  # 最近20条消息
            "timestamp": datetime.now().isoformat()
        }
        
        try:
            await self.active_connections[employee_id]["websocket"].send_text(json.dumps(history_msg))
        except Exception as e:
            logger.error(f"Failed to send chat history to {employee_id}: {e}")
    
    async def broadcast_user_list_update(self):
        """20250618.19:20 实时信息交流 - 广播用户列表更新"""
        online_users = []
        for emp_id, user_info in self.active_connections.items():
            online_users.append({
                "employee_id": emp_id,
                "employee_name": user_info["employee_name"],
                "status": user_info["status"],
                "last_seen": user_info["last_seen"].isoformat()
            })
        
        user_list_msg = {
            "type": "user_list_update",
            "online_users": online_users,
            "total_count": len(online_users),
            "timestamp": datetime.now().isoformat()
        }
        
        # 广播给所有在线用户
        disconnected_users = []
        for emp_id, user_info in self.active_connections.items():
            try:
                await user_info["websocket"].send_text(json.dumps(user_list_msg))
            except Exception as e:
                logger.error(f"Failed to send user list to {emp_id}: {e}")
                disconnected_users.append(emp_id)
        
        # 清理断开的连接
        for emp_id in disconnected_users:
            await self.disconnect(emp_id)
    
    async def broadcast_message(self, sender_id: str, message: str, message_type: str = "chat_message"):
        """20250618.19:20 实时信息交流 - 广播消息"""
        if sender_id not in self.active_connections:
            return False
        
        sender_info = self.active_connections[sender_id]
        
        # 创建消息对象
        chat_message = {
            "type": message_type,
            "message": message,
            "timestamp": datetime.now().isoformat(),
            "user_id": sender_id,
            "user_name": sender_info["employee_name"],
            "message_id": f"{sender_id}_{int(datetime.now().timestamp() * 1000)}"
        }
        
        # 添加到历史记录
        self.message_history.append(chat_message)
        if len(self.message_history) > self.max_history:
            self.message_history.pop(0)
        
        # 广播给所有在线用户
        disconnected_users = []
        broadcast_count = 0
        
        for emp_id, user_info in self.active_connections.items():
            try:
                await user_info["websocket"].send_text(json.dumps(chat_message))
                broadcast_count += 1
            except Exception as e:
                logger.error(f"Failed to broadcast message to {emp_id}: {e}")
                disconnected_users.append(emp_id)
        
        # 清理断开的连接
        for emp_id in disconnected_users:
            await self.disconnect(emp_id)
        
        logger.info(f"Message from {sender_info['employee_name']} broadcasted to {broadcast_count} users")
        return True
    
    async def send_private_message(self, sender_id: str, recipient_id: str, message: str):
        """20250618.19:20 实时信息交流 - 发送私聊消息"""
        if sender_id not in self.active_connections or recipient_id not in self.active_connections:
            return False
        
        sender_info = self.active_connections[sender_id]
        recipient_info = self.active_connections[recipient_id]
        
        private_message = {
            "type": "private_message",
            "message": message,
            "timestamp": datetime.now().isoformat(),
            "sender_id": sender_id,
            "sender_name": sender_info["employee_name"],
            "recipient_id": recipient_id,
            "recipient_name": recipient_info["employee_name"],
            "message_id": f"private_{sender_id}_{recipient_id}_{int(datetime.now().timestamp() * 1000)}"
        }
        
        try:
            # 发送给接收者
            await recipient_info["websocket"].send_text(json.dumps(private_message))
            # 发送给发送者（确认消息）
            await sender_info["websocket"].send_text(json.dumps(private_message))
            return True
        except Exception as e:
            logger.error(f"Failed to send private message: {e}")
            return False
    
    def get_online_count(self) -> int:
        """20250618.19:20 实时信息交流 - 获取在线人数"""
        return len(self.active_connections)

# 20250618.19:20 实时信息交流 - 全局连接管理器
chat_manager = ChatConnectionManager()

@router.websocket("/ws/chat")
async def websocket_chat_endpoint(websocket: WebSocket, token: str):
    """20250618.19:20 实时信息交流 - WebSocket聊天端点"""
    try:
        # 验证JWT token
        payload = verify_jwt_token(token)
        employee_id = payload.get("employee_id", "unknown")
        employee_name = payload.get("employee_name", f"User_{employee_id}")
        
        # 连接用户
        await chat_manager.connect(websocket, employee_id, employee_name)
        
        try:
            while True:
                # 接收客户端消息
                data = await websocket.receive_text()
                message_data = json.loads(data)
                
                message_type = message_data.get("type", "chat_message")
                message_content = message_data.get("message", "").strip()
                
                if not message_content:
                    continue
                
                if message_type == "chat_message":
                    # 广播聊天消息
                    await chat_manager.broadcast_message(employee_id, message_content)
                    
                elif message_type == "private_message":
                    # 私聊消息
                    recipient_id = message_data.get("recipient_id")
                    if recipient_id:
                        await chat_manager.send_private_message(employee_id, recipient_id, message_content)
                
                elif message_type == "ping":
                    # 心跳检测
                    pong_msg = {
                        "type": "pong",
                        "timestamp": datetime.now().isoformat()
                    }
                    await websocket.send_text(json.dumps(pong_msg))
                
        except WebSocketDisconnect:
            pass
        except Exception as e:
            logger.error(f"WebSocket error for user {employee_id}: {e}")
        
    except Exception as e:
        logger.error(f"Authentication failed for chat WebSocket: {e}")
        await websocket.close(code=1008, reason="Authentication failed")
    
    finally:
        # 断开连接
        if 'employee_id' in locals():
            await chat_manager.disconnect(employee_id)

@router.get("/api/chat/status")
async def get_chat_status():
    """20250618.19:20 实时信息交流 - 获取聊天状态"""
    return {
        "status": "success",
        "online_users": chat_manager.get_online_count(),
        "chat_rooms": len(chat_manager.chat_rooms),
        "message_history_count": len(chat_manager.message_history),
        "timestamp": datetime.now().isoformat()
    } 