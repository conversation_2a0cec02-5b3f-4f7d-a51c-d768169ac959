# 注释42 再次增加ui基面的功能 - 额外业务路由
import os, uuid, xml.etree.ElementTree as ET
import random
import re
from datetime import datetime
from typing import List, Dict, Tuple, Optional
from fastapi import APIRouter, HTTPException, Depends
from fastapi.responses import FileResponse
from pydantic import BaseModel, Field
from sqlalchemy import text
from sqlalchemy.ext.asyncio import AsyncSession
from pymongo import MongoClient
from pathlib import Path
import io
import base64
from PIL import Image, ImageDraw, ImageFont

from ..databases.postgresql.client import AsyncSessionLocal, get_db, ImdbAsyncSessionLocal, init_imdb_tables          # DB 连接
from ..config import MONGO_HOST, MONGO_PORT, MONGO_DB, MONGO_COLLECTION  # 注释42
from ..core.services.redis_service import redis_service  # 注释42 导入Redis服务

router = APIRouter(prefix="/api/extra", tags=["extra"])
XML_DIR = os.path.abspath(os.path.join(os.path.dirname(__file__), "../../xml_data2"))
# 修改33 修改chart 逻辑: 定义一个健壮的、基于当前文件位置的绝对路径
XML_DATA2_PATH = Path(__file__).resolve().parent.parent.parent / "xml_data2"
os.makedirs(XML_DIR, exist_ok=True)

# MongoDB配置 - 注释42 简化连接方式
try:
    print(f"尝试连接MongoDB: {MONGO_HOST}:{MONGO_PORT}")  # 注释42
    # 使用更简单的连接方式，类似参考代码
    mongo_client = MongoClient(MONGO_HOST, MONGO_PORT)
    mongo_db = mongo_client[MONGO_DB]
    sensor_collection = mongo_db[MONGO_COLLECTION]
    
    # 测试连接 - 注释42
    mongo_client.server_info()  # 这会触发实际连接
    MONGO_AVAILABLE = True
    print(f"MongoDB连接成功: {MONGO_HOST}:{MONGO_PORT}")  # 注释42
except Exception as e:
    print(f"MongoDB连接失败: {e}")  # 注释42
    MONGO_AVAILABLE = False
    mongo_client = None
    sensor_collection = None


# ---------- 1) n+1 功能 ----------
class AddOneReq(BaseModel):
    n: int = Field(..., ge=0)

class AddOneResp(BaseModel):
    result: int

@router.post("/add_one", response_model=AddOneResp)
async def add_one(body: AddOneReq):
    return AddOneResp(result=body.n + 1)


# ---------- 2) 读取 1.xml / 2.xml ----------
class IndexReq(BaseModel):
    index: int = Field(..., ge=1)

@router.post("/read_data_xml")
async def read_data_xml(body: IndexReq):
    if body.index not in (1, 2):
        raise HTTPException(400, detail="只支持 1 或 2")
    f_path = os.path.join(XML_DIR, f"{body.index}.xml")
    if not os.path.exists(f_path):
        raise HTTPException(404, detail="XML 文件不存在")

    def _parse_table_xml(path) -> List[Dict]:
        tree = ET.parse(path)
        root = tree.getroot()
        recs = []
        for rec in root.findall("record"):
            d = {fld.get("name"): (fld.text or "") for fld in rec.findall("field")}
            recs.append(d)
        return recs

    return {"records": _parse_table_xml(f_path)}


# ---------- 3) 上传项目进度 -> 3.xml + PostgreSQL ----------
class ProgressRow(BaseModel):
    employee_id: str
    date: str
    model: str = ""
    number: str = ""
    factory_number: str = ""
    project_number: str = ""
    unit_number: str = ""
    category: str = ""
    item: str = ""
    time: str = ""
    department: str = ""

class ProgressReq(BaseModel):
    rows: List[ProgressRow]

@router.post("/upload_progress")
async def upload_progress(body: ProgressReq):
    # 3.xml 追加
    
    xml3 = os.path.join(XML_DIR, "3.xml")
    if os.path.exists(xml3):
        tree = ET.parse(xml3)
        root = tree.getroot()
    else:
        root = ET.Element("entries")
        tree = ET.ElementTree(root)

    generated_ids = []  # 存储生成的ID，用于后续数据库写入
    for r in body.rows:
        db_id = str(uuid.uuid4())
        generated_ids.append(db_id)
        entry = ET.SubElement(root, "entry", {
            "id": db_id,  # 使用生成的db_id
            "ts": datetime.utcnow().isoformat()
        })
        for k, v in r.dict().items():
            # 3.xml需要保存实际数据内容，不是自闭合标签
            child = ET.SubElement(entry, k)
            child.text = v or ""
    tree.write(xml3, encoding="utf-8", xml_declaration=True)

    # 写入数据库 - 注释42 修正数据类型
    try:
        async with AsyncSessionLocal() as session:
            for r in body.rows:
                table_name = f'progress_{r.employee_id}'
                await session.execute(text(
                    f'''CREATE TABLE IF NOT EXISTS "{table_name}" (
                        id SERIAL PRIMARY KEY,
                        ts TIMESTAMPTZ DEFAULT NOW(),
                        date DATE,
                        model VARCHAR(100),
                        number VARCHAR(50),
                        factory_number VARCHAR(100),
                        project_number VARCHAR(100),
                        unit_number VARCHAR(50),
                        category VARCHAR(50),
                        item VARCHAR(100),
                        time DECIMAL(10,2),
                        department VARCHAR(50)
                    )'''
                ))
                
                # 处理数据类型转换
                time_value = None
                if r.time:
                    try:
                        time_value = float(r.time)
                    except ValueError:
                        time_value = 0.0
                
                date_value = None
                if r.date:
                    try:
                        date_value = datetime.strptime(r.date, "%Y/%m/%d").date()
                    except ValueError:
                        date_value = datetime.now().date()
                
                await session.execute(
                    text(f'''INSERT INTO "{table_name}" 
                        (date, model, number, factory_number, project_number,
                         unit_number, category, item, time, department)
                        VALUES (:date, :model, :number, :factory_number, :project_number,
                                :unit_number, :category, :item, :time, :department)'''),
                    {
                        "date": date_value,
                        "model": r.model or None,
                        "number": r.number or None,
                        "factory_number": r.factory_number or None,
                        "project_number": r.project_number or None,
                        "unit_number": r.unit_number or None,
                        "category": r.category or None,
                        "item": r.item or None,
                        "time": time_value,
                        "department": r.department or None
                    }
                )
            await session.commit()
    except Exception as e:
        # 如果数据库写入失败，仍然返回成功，因为XML已经写入
        pass

    # 修改1：写入IMDB数据库的add25表
    try:
        print("开始写入IMDB数据库...")
        # 确保IMDB表已创建
        await init_imdb_tables()
        print("IMDB表初始化完成")
        
        async with ImdbAsyncSessionLocal() as imdb_session:
            for idx, r in enumerate(body.rows):
                print(f"处理第{idx+1}条记录: {r.employee_id}")
                
                # 处理数据类型转换
                time_value = None
                if r.time:
                    try:
                        time_value = float(r.time)
                        print(f"时间值转换: {r.time} -> {time_value}")
                    except ValueError as ve:
                        print(f"时间值转换失败: {r.time}, 错误: {ve}")
                        time_value = 0.0
                
                date_value = None
                if r.date:
                    try:
                        date_value = datetime.strptime(r.date, "%Y/%m/%d").date()
                        print(f"日期值转换: {r.date} -> {date_value}")
                    except ValueError as ve:
                        print(f"日期值转换失败: {r.date}, 错误: {ve}")
                        date_value = datetime.now().date()
                
                db_id_for_insert = generated_ids[idx] if idx < len(generated_ids) else str(uuid.uuid4())
                print(f"使用DB_ID: {db_id_for_insert}")
                
                insert_data = {
                    "db_id": db_id_for_insert,
                    "employee_id": r.employee_id,
                    "date": date_value,
                    "model": r.model or None,
                    "number": r.number or None,
                    "factory_number": r.factory_number or None,
                    "project_number": r.project_number or None,
                    "unit_number": r.unit_number or None,
                    "category": r.category or None,
                    "item": r.item or None,
                    "time": time_value,
                    "department": r.department or None
                }
                print(f"插入数据: {insert_data}")
                
                await imdb_session.execute(
                    text('''INSERT INTO add25 
                        (db_id, employee_id, date, model, number, factory_number, project_number,
                         unit_number, category, item, time, department)
                        VALUES (:db_id, :employee_id, :date, :model, :number, :factory_number, :project_number,
                                :unit_number, :category, :item, :time, :department)'''),
                    insert_data
                )
                print(f"第{idx+1}条记录插入完成")
            
            await imdb_session.commit()
            print("✅ 数据已成功写入IMDB的add25表并提交事务")
    except Exception as e:
        import traceback
        print(f"❌ 写入IMDB add25表失败: {e}")
        print(f"详细错误信息: {traceback.format_exc()}")
        # 不影响主流程，继续执行

    return {"status": "success", "message": "数据已保存"}


# ---------- 4) 返回 4.xml ----------
@router.get("/progress_list")
async def get_progress_list():
    xml4 = os.path.join(XML_DIR, "4.xml")
    if not os.path.exists(xml4):
        raise HTTPException(404, detail="4.xml 不存在")

    def _parse(path):
        tree = ET.parse(path)
        root = tree.getroot()
        recs = []
        for entry in root.findall("entry"):
            # 修改 b13  处理xml相关逻辑: 将 'id' 属性放在字典的开头，以确保它是表格的第一列。
            rec = {'id': entry.get('id')}
            rec.update({child.tag: (child.text or "") for child in entry})
            rec['ts'] = entry.get('ts')
            recs.append(rec)
        return recs

    return {"records": _parse(xml4)}


# ---------- 5) 传感器随机数据 ----------
class SensorDataResp(BaseModel):
    sensor_value: float
    timestamp: str
    sensor_id: str

@router.get("/sensor_data", response_model=SensorDataResp)
async def get_sensor_data():
    """注释42 再次增加ui基面的功能 - 生成随机传感器数据"""
    global MONGO_AVAILABLE, mongo_client, mongo_db, sensor_collection
    
    sensor_value = round(random.uniform(0.1, 100.0), 2)
    timestamp = datetime.utcnow().isoformat()
    sensor_id = f"SENSOR_{random.randint(1000, 9999)}"
    
    # 保存到Redis缓存
    redis_cached = await redis_service.set_sensor_data(sensor_id, sensor_value)
    if redis_cached:
        #print(f"传感器数据已缓存到Redis: {sensor_id} = {sensor_value}")
    
        # 保存到MongoDB
        try:
            if MONGO_AVAILABLE and sensor_collection is not None:
                sensor_data = {
                    "sensor_id": sensor_id,
                    "value": sensor_value,
                    "timestamp": timestamp,
                    "created_at": datetime.utcnow()
                }
                result = sensor_collection.insert_one(sensor_data)
                #print(f"传感器数据已保存到MongoDB: {result.inserted_id}")
        except Exception as e:
            print(f"MongoDB保存失败: {e}")
            try:
                # 重新连接MongoDB
                mongo_client = MongoClient(MONGO_HOST, MONGO_PORT)
                mongo_db = mongo_client[MONGO_DB]
                sensor_collection = mongo_db[MONGO_COLLECTION]
                mongo_client.server_info()
                MONGO_AVAILABLE = True
                print("MongoDB重新连接成功")
            except Exception as reconnect_error:
                print(f"MongoDB重新连接失败: {reconnect_error}")
            MONGO_AVAILABLE = False
    else:
        print("MongoDB不可用，跳过数据保存")
    
    return SensorDataResp(
        sensor_value=sensor_value,
        timestamp=timestamp,
        sensor_id=sensor_id
    )




# ---------- 6) MongoDB连接测试 ----------
@router.get("/mongo_status")
async def get_mongo_status():
    """注释42 再次增加ui基面的功能 - MongoDB连接状态测试"""
    global MONGO_AVAILABLE, mongo_client, mongo_db, sensor_collection
    
    if not MONGO_AVAILABLE:
        return {
            "status": "disconnected",
            "message": "MongoDB连接不可用",
            "host": MONGO_HOST,
            "port": MONGO_PORT
        }
    
    try:
        # 测试连接
        mongo_client.server_info()
        # 获取数据库信息
        db_stats = mongo_db.command("dbstats")
        collection_count = sensor_collection.count_documents({})
        
        return {
            "status": "connected",
            "message": "MongoDB连接正常",
            "host": MONGO_HOST,
            "port": MONGO_PORT,
            "database": MONGO_DB,
            "collection": MONGO_COLLECTION,
            "db_size": db_stats.get("dataSize", 0),
            "sensor_records": collection_count
        }
    except Exception as e:
        return {
            "status": "error",
            "message": f"MongoDB连接测试失败: {str(e)}",
            "host": MONGO_HOST,
            "port": MONGO_PORT
        }


# ---------- 7) Redis状态检查 ----------
@router.get("/redis_status")
async def get_redis_status():
    """注释42 再次增加ui基面的功能 - Redis连接状态测试"""
    return redis_service.get_status()


# ================= 图表数据处理端点 =================
# 修改35: 整个图表逻辑重写，使用XML文件进行对比

class ChartRequest(BaseModel):
    """图表数据请求体"""
    month_code: str
    employee_id: str

def _parse_time_str(time_str: str) -> Optional[Tuple[int, int]]:
    """
    将 "出勤時刻" 或 "退勤時刻" 字符串解析为 (小时, 分钟) 的整数元组。
    支持 "8:12", "当 8:12" 等格式。
    """
    if not time_str:
        return None
    match = re.search(r'(\d{1,2}):(\d{2})', time_str)
    if match:
        return int(match.group(1)), int(match.group(2))
    return None

def _compare_hours_for_day(date_str: str, timepro_xml_path: Path, employee_xml_path: Path) -> bool:
    """
    为指定日期比较TimePro XML和员工XML中的工时。
    逻辑完全基于 WorkTimeApp.py 中的 compare_time_for_date 函数。
    """
    # 1. 从 TimePro XML 计算总工时 (t1)
    t1 = 0.0
    try:
        if not timepro_xml_path.exists():
            print(f"Warning: TimePro XML not found at {timepro_xml_path}")
            return False

        timepro_tree = ET.parse(timepro_xml_path)
        for rec in timepro_tree.findall('.//record'):
            fld = rec.find(f"./field[@name='日付']")
            if fld is not None and fld.text and fld.text.strip() == date_str:
                sched_elem = rec.find("./field[@name='所定時間']")
                early_elem = rec.find("./field[@name='早出残業']")
                
                st = sched_elem.text.strip() if sched_elem is not None and sched_elem.text else ""
                et = early_elem.text.strip() if early_elem is not None and early_elem.text else ""
                
                # 解析所定時間 (分钟>0则小时数+1)
                sched_h = 0.0
                if st and st != "----":
                    try:
                        h, m = map(int, st.split(':'))
                        sched_h = h + (1 if m > 0 else 0)
                    except (ValueError, IndexError):
                        pass # sched_h remains 0.0
                
                # 解析早出残業
                early_h = 0.0
                if et and et != "----":
                    try:
                        h, m = map(int, et.split(':'))
                        early_h = h + m / 60.0
                    except (ValueError, IndexError):
                        pass # early_h remains 0.0
                
                t1 = sched_h + early_h
                break 
    except Exception as e:
        print(f"Error processing TimePro XML for date {date_str}: {e}")
        t1 = 0.0

    # 2. 从员工 XML 计算总工时 (t2)
    t2 = 0.0
    try:
        if not employee_xml_path.exists():
            print(f"Warning: Employee XML not found at {employee_xml_path}")
            return False
            
        emp_tree = ET.parse(employee_xml_path)
        for entry in emp_tree.findall('.//entry'):
            d_elem = entry.find('date')
            tm_elem = entry.find('time')
            if d_elem is not None and d_elem.text and d_elem.text.strip() == date_str:
                if tm_elem is not None and tm_elem.text:
                    try:
                        t2 += float(tm_elem.text)
                    except (ValueError, TypeError):
                        pass
    except Exception as e:
        print(f"Error processing Employee XML for date {date_str}: {e}")
        t2 = 0.0
    
    # 3. 比较 t1 和 t2
    return abs(t1 - t2) < 1e-6

@router.get("/chart/months")
async def get_available_chart_months(employee_id: str):
    """获取指定员工可用的图表月份列表"""
    try:
        if not employee_id:
            raise HTTPException(400, "缺少 employee_id")

        xml_data2_path = XML_DATA2_PATH
        if not xml_data2_path.exists():
            return {"status": "error", "message": "xml_data2 目录不存在"}

        # 1. 从员工的XML文件中提取有记录的月份
        employee_xml_path = xml_data2_path / f"{employee_id}.xml"
        if not employee_xml_path.exists():
            # 员工文件不存在，没有可用月份
            return {"status": "success", "months": [], "count": 0}

        employee_months = set()
        try:
            emp_tree = ET.parse(employee_xml_path)
            for entry in emp_tree.findall('.//entry'):
                date_elem = entry.find('date')
                if date_elem is not None and date_elem.text:
                    try:
                        # 从 'YYYY/MM/DD' 格式中提取 'YYYYMM'
                        dt = datetime.strptime(date_elem.text.strip(), "%Y/%m/%d")
                        employee_months.add(dt.strftime("%Y%m"))
                    except (ValueError, TypeError):
                        continue
        except ET.ParseError:
            # 文件存在但无法解析，当作没有数据
            return {"status": "success", "months": [], "count": 0}


        # 2. 扫描所有可用的 timepro report 月份
        all_report_months = set()
        for xml_file in xml_data2_path.glob("*_timepro_report.xml"):
            month_match = xml_file.stem.split('_')[0]
            if len(month_match) == 6 and month_match.isdigit():
                all_report_months.add(month_match)
        
        # 3. 找出交集
        available_month_codes = employee_months.intersection(all_report_months)

        # 4. 构建返回结果
        months_details = []
        for month_code in available_month_codes:
            year = month_code[:4]
            month = month_code[4:]
            months_details.append({
                "month_code": month_code,
                "display_name": f"{year}/{month}",
                "year": int(year),
                "month": int(month)
            })

        # 按时间排序
        months_details.sort(key=lambda x: x["month_code"], reverse=True)
        
        return {
            "status": "success",
            "months": months_details,
            "count": len(months_details)
        }
    except Exception as e:
        return {"status": "error", "message": f"获取月份列表失败: {str(e)}"}

@router.post("/chart/generate")
async def generate_chart_data(request: ChartRequest):
    """
    生成图表数据, 对比逻辑完全基于XML文件。
    不再使用数据库。
    """
    try:
        month_code = request.month_code
        employee_id = request.employee_id
        
        if not month_code or len(month_code) != 6 or not month_code.isdigit():
            raise HTTPException(400, "无效的月份代码")
        if not employee_id:
            raise HTTPException(400, "缺少 employee_id")

        redis_key = f"chart_data_v3:{employee_id}:{month_code}"
        
        # 1. 检查Redis缓存
        if redis_service.available:
            cached_data = await redis_service.redis_client.get(redis_key)
            if cached_data:
                import json
                return {"status": "success", "data": json.loads(cached_data), "from_cache": True}
        
        # 2. 准备文件路径
        year, month = int(month_code[:4]), int(month_code[4:])
        
        timepro_xml_path = XML_DATA2_PATH / f"{month_code}_timepro_report.xml"
        employee_xml_path = XML_DATA2_PATH / f"{employee_id}.xml"

        # 确保timepro文件存在，如果不存在则从html转换
        if not timepro_xml_path.exists():
            html_path = XML_DATA2_PATH / f"{month_code}_body_content.html"
            if not html_path.exists():
                raise HTTPException(404, f"源文件 HTML 和 XML 均不存在: {month_code}")
            if not await convert_html_to_xml(str(html_path), str(timepro_xml_path)):
                raise HTTPException(500, "HTML到XML自动转换失败")

        # 3. 解析TimePro XML并进行比较
        tree = ET.parse(timepro_xml_path)
        root = tree.getroot()
        
        daily_data = []
        for record in root.findall("record"):
            fields = {field.get("name"): (field.text or "") for field in record.findall("field")}
            
            date_str = fields.get("日付", "")
            if not date_str:
                continue

            # a. 调用新的对比函数
            is_match = _compare_hours_for_day(date_str, timepro_xml_path, employee_xml_path)

            # b. 解析出勤/退勤时间用于绘图
            clock_in = _parse_time_str(fields.get("出勤時刻"))
            clock_out = _parse_time_str(fields.get("退勤時刻"))
            
            try:
                day_of_month = datetime.strptime(date_str, "%Y/%m/%d").day
                daily_data.append({
                    "day_of_month": day_of_month,
                    "clock_in": clock_in,
                    "clock_out": clock_out,
                    "is_match": is_match,
                })
            except ValueError:
                continue # Skip records with invalid date format

        chart_data = {
            "month_name": f"{year}年{month:02d}月",
            "daily_data": sorted(daily_data, key=lambda x: x['day_of_month'])
        }

        # 4. 存入Redis缓存
        if redis_service.available:
            try:
                import json
                # 使用新v3 key，并设置1小时过期
                await redis_service.redis_client.setex(redis_key, 3600, json.dumps(chart_data))
            except Exception as redis_error:
                print(f"Redis缓存存储失败: {redis_error}")
        
        return {"status": "success", "data": chart_data, "from_cache": False}
        
    except Exception as e:
        import traceback
        traceback.print_exc()
        raise HTTPException(500, f"生成图表数据时发生内部错误: {e}")

async def convert_html_to_xml(html_file_path: str, xml_file_path: str) -> bool:
    """将HTML文件转换为XML文件"""
    try:
        from bs4 import BeautifulSoup
        import xml.etree.ElementTree as ET
        
        # 读取HTML文件
        with open(html_file_path, 'r', encoding='utf-8') as f:
            html_content = f.read()
        
        soup = BeautifulSoup(html_content, 'lxml')
        
        # 提取选中的年份
        selected_year = None
        cmb_ym_select = soup.find('select', {'id': 'CmbYM'})
        if cmb_ym_select:
            selected_option_tag = cmb_ym_select.find('option', selected=True)
            if selected_option_tag and selected_option_tag.has_attr('value'):
                value_str = selected_option_tag['value']
                if len(value_str) == 6 and value_str.isdigit():
                    selected_year = value_str[:4]
        
        # 定义XML输出的标题
        headers = [
            "日付", "星期", "ｶﾚﾝﾀﾞ", "不在", "勤務区分", "事由",
            "出勤時刻", "ＭＣ_出勤", "退勤時刻", "ＭＣ_退勤", "所定時間", "早出残業",
            "内深夜残業", "遅刻早退", "休出時間", "出張残業", "外出時間",
            "戻り時間", "コメント"
        ]
        
        # 创建XML根元素
        root = ET.Element("data")
        
        # 查找主数据表
        data_tables = soup.find_all('table', class_='ap_table', border='1')
        main_data_table = None
        
        for table in data_tables:
            header_row = table.find('tr', class_='ap_tr_title')
            if header_row and header_row.find(lambda tag: tag.name == 'td' and '日付' in tag.get_text(strip=True)):
                main_data_table = table
                break
        
        if not main_data_table:
            print("错误: 在HTML中找不到主数据表")
            return False
        
        # 遍历数据行
        for row in main_data_table.find_all('tr', class_='ap_tr_base'):
            cells = row.find_all('td')
            
            if len(cells) == len(headers):
                record_el = ET.SubElement(root, "record")
                for i, header in enumerate(headers):
                    cell_text = cells[i].get_text(strip=True)
                    
                    if header == "日付" and selected_year:
                        if '/' in cell_text:
                            cell_text = f"{selected_year}/{cell_text}"
                        elif cell_text:
                            cell_text = f"{selected_year}/{cell_text}"
                    
                    field_el = ET.SubElement(record_el, "field")
                    field_el.set("name", header)
                    field_el.text = cell_text
        
        # 写入XML文件
        tree = ET.ElementTree(root)
        tree.write(xml_file_path, encoding='utf-8', xml_declaration=True)
        print(f"XML文件已保存到 {xml_file_path}")
        return True
        
    except Exception as e:
        print(f"HTML到XML转换错误: {e}")
        return False 

# 修改，25061804  更改删除。
def _get_record_data_by_id(db_id: str) -> Optional[dict]:
    """
    从5.xml中获取指定db_id的完整记录数据
    """
    try:
        xml5_path = XML_DATA2_PATH / "5.xml"
        if not xml5_path.exists():
            return None
        
        tree = ET.parse(xml5_path)
        root = tree.getroot()
        
        for entry in root.findall(f".//entry[@id='{db_id}']"):
            record_data = {'db_id': db_id}
            record_data.update({child.tag: (child.text or "") for child in entry})
            record_data['ts'] = entry.get('ts')
            return record_data
        
        return None
    except Exception as e:
        print(f"Error getting record data for {db_id}: {e}")
        return None

def _log_full_record_to_xml(log_xml_path: Path, record_data: dict, tag: str = "record"):
    """
    修改2&3：将完整的记录数据写入到指定的日志XML文件中
    """
    try:
        root = None
        # 如果文件存在且不为空，则解析
        if log_xml_path.exists() and log_xml_path.stat().st_size > 0:
            try:
                tree = ET.parse(log_xml_path)
                root = tree.getroot()
            except ET.ParseError:
                # 如果文件存在但解析失败（例如是空的或损坏的），则创建一个新的根
                root = ET.Element("entries")
        else:
            # 如果文件不存在或为空，也创建一个新的根
            root = ET.Element("entries")

        # 添加新条目，包含完整数据
        entry = ET.SubElement(root, "entry", {
            "id": record_data.get('db_id', ''),
            "ts": datetime.utcnow().isoformat()
        })
        
        # 添加所有字段，db_id已经在属性中，所以跳过
        # 使用和215829.xml一样的自闭合标签格式
        for field, value in record_data.items():
            if field not in ['db_id', 'ts']:  # db_id在属性中，ts用当前时间
                # 始终使用自闭合标签格式，符合215829.xml的格式要求
                ET.SubElement(entry, field)

        # 创建 ElementTree 对象并写入文件
        tree = ET.ElementTree(root)
        tree.write(log_xml_path, encoding="utf-8", xml_declaration=True)
        return True
    except Exception as e:
        print(f"Error logging full record to {log_xml_path}: {e}")
        return False

def _log_id_to_xml(log_xml_path: Path, db_id: str, tag: str = "record"):
    """
    修改，25061804  更改删除。
    将一个ID记录到指定的日志XML文件中, 修复了对空文件的处理。
    """
    try:
        root = None
        # 如果文件存在且不为空，则解析
        if log_xml_path.exists() and log_xml_path.stat().st_size > 0:
            try:
                tree = ET.parse(log_xml_path)
                root = tree.getroot()
            except ET.ParseError:
                # 如果文件存在但解析失败（例如是空的或损坏的），则创建一个新的根
                root = ET.Element("data")
        else:
            # 如果文件不存在或为空，也创建一个新的根
            root = ET.Element("data")

        # 添加新条目
        ET.SubElement(root, tag, {
            "id": db_id,
            "timestamp": datetime.utcnow().isoformat()
        })

        # 创建 ElementTree 对象并写入文件
        tree = ET.ElementTree(root)
        tree.write(log_xml_path, encoding="utf-8", xml_declaration=True)
        return True
    except Exception as e:
        print(f"Error logging to {log_xml_path}: {e}")
        return False

#  修改 b21 增加table3的交互处理。
class XML5Request(BaseModel):
    employee_id: str
    month_code: str # YYYYMM format

class ActionRequest(BaseModel):
    db_id: str

#  修改 b21 增加table3的交互处理。
@router.post("/read_from_5xml")
async def read_from_5xml(body: XML5Request):
    xml5_path = os.path.join(XML_DIR, "5.xml")
    if not os.path.exists(xml5_path):
        raise HTTPException(status_code=404, detail="5.xml not found")

    try:
        tree = ET.parse(xml5_path)
        root = tree.getroot()
        
        recs = []
        year = body.month_code[:4]
        month = body.month_code[4:]

        for entry in root.findall("entry"):
            emp_id_elem = entry.find("employee_id")
            date_elem = entry.find("date")

            if emp_id_elem is not None and emp_id_elem.text == body.employee_id:
                if date_elem is not None and date_elem.text:
                    try:
                        entry_date = datetime.strptime(date_elem.text, "%Y/%m/%d")
                        if str(entry_date.year) == year and f"{entry_date.month:02d}" == month:
                            rec = {'id': entry.get('id')}
                            rec.update({child.tag: (child.text or "") for child in entry})
                            rec['ts'] = entry.get('ts')
                            recs.append(rec)
                    except (ValueError, TypeError):
                        continue
        
        return {"status": "success", "records": recs}
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Error processing 5.xml: {e}")

#  修改，25061803  更改删除。
@router.post("/log_action")
async def log_action(body: dict):
    """
    修改2&3：通用日志记录端点，记录完整数据到XML和IMDB数据库
    """
    db_id = body.get("db_id")
    log_type = body.get("log_type")
    record_data = body.get("record_data")  # 新增：从请求中获取完整记录数据

    if not db_id or not log_type:
        raise HTTPException(status_code=400, detail="db_id and log_type are required")

    # 如果没有提供record_data，从5.xml中获取
    if not record_data:
        record_data = _get_record_data_by_id(db_id)
        if not record_data:
            raise HTTPException(status_code=404, detail=f"Record with db_id {db_id} not found")

    if log_type == "change":
        log_file = XML_DATA2_PATH / "change.xml"
        table_name = "change25"
    elif log_type == "delete":
        log_file = XML_DATA2_PATH / "del.xml"
        table_name = "del25"
    else:
        raise HTTPException(status_code=400, detail="Invalid log_type specified")
    
    # 修改2&3：写入完整记录到XML
    if _log_full_record_to_xml(log_file, record_data):
        print(f"完整记录已写入{log_type}.xml")
    else:
        raise HTTPException(status_code=500, detail=f"Failed to log record to {log_type}.xml")
    
    # 修改2&3：写入IMDB数据库表
    try:
        # 确保IMDB表已创建
        await init_imdb_tables()
        
        async with ImdbAsyncSessionLocal() as imdb_session:
            # 处理数据类型转换
            time_value = None
            if record_data.get('time'):
                try:
                    time_value = float(record_data['time'])
                except ValueError:
                    time_value = 0.0
            
            date_value = None
            if record_data.get('date'):
                try:
                    date_value = datetime.strptime(record_data['date'], "%Y/%m/%d").date()
                except ValueError:
                    date_value = datetime.now().date()
            
            await imdb_session.execute(
                text(f'''INSERT INTO {table_name} 
                    (db_id, employee_id, date, model, number, factory_number, project_number,
                     unit_number, category, item, time, department)
                    VALUES (:db_id, :employee_id, :date, :model, :number, :factory_number, :project_number,
                            :unit_number, :category, :item, :time, :department)'''),
                {
                    "db_id": db_id,
                    "employee_id": record_data.get('employee_id', ''),
                    "date": date_value,
                    "model": record_data.get('model', '') or None,
                    "number": record_data.get('number', '') or None,
                    "factory_number": record_data.get('factory_number', '') or None,
                    "project_number": record_data.get('project_number', '') or None,
                    "unit_number": record_data.get('unit_number', '') or None,
                    "category": record_data.get('category', '') or None,
                    "item": record_data.get('item', '') or None,
                    "time": time_value,
                    "department": record_data.get('department', '') or None
                }
            )
            await imdb_session.commit()
            print(f"数据已成功写入IMDB的{table_name}表")
    except Exception as e:
        print(f"写入IMDB {table_name}表失败: {e}")
        # 不影响主流程，继续执行

    return {"status": "success", "message": f"完整记录已保存到{log_type}.xml和IMDB {table_name}表"}

#  修改，25061801  更改输入面板: 新的请求模型和更新路由
class UpdateProgressReq(BaseModel):
    db_id: str
    employee_id: str
    date: str
    model: str
    number: str
    factory_number: str
    project_number: str
    unit_number: str
    category: str
    item: str
    time: str
    department: str

def _update_xml_file(xml_path: Path, db_id: str, data: UpdateProgressReq) -> bool:
    """ 通用函数，用于在给定的XML文件中查找并更新一个条目 """
    if not xml_path.exists():
        return False # 文件不存在，无需更新

    try:
        tree = ET.parse(xml_path)
        root = tree.getroot()
        
        entry_found = False
        for entry in root.findall(f".//entry[@id='{db_id}']"):
            entry_found = True
            # 更新所有字段
            for field, value in data.dict().items():
                if field == 'db_id': continue # db_id是属性，不是子元素
                
                elem = entry.find(field)
                if elem is not None:
                    elem.text = value
                else: # 如果某个字段原本不存在，则创建它
                    new_elem = ET.SubElement(entry, field)
                    new_elem.text = value
            # 更新时间戳
            entry.set('ts', datetime.utcnow().isoformat())
        
        if entry_found:
            tree.write(xml_path, encoding="utf-8", xml_declaration=True)
            return True
        return False
    except Exception as e:
        print(f"Error updating {xml_path}: {e}")
        return False

@router.post("/update_progress")
async def update_progress(body: UpdateProgressReq):
    """ 修改2: 更新 4.xml 和 5.xml 中的进度记录，并同时记录到change.xml和IMDB """
    
    # 1. 总是更新 5.xml
    xml5_path = XML_DATA2_PATH / "5.xml"
    updated_in_5 = _update_xml_file(xml5_path, body.db_id, body)
    
    if not updated_in_5:
        raise HTTPException(status_code=404, detail=f"在 5.xml 中未找到 DB_ID: {body.db_id}")

    # 2. 根据日期决定是否更新 4.xml
    try:
        record_date = datetime.strptime(body.date, "%Y/%m/%d")
        current_date = datetime.now()
        
        is_current_month = (record_date.year == current_date.year and 
                            record_date.month == current_date.month)
        
        if is_current_month:
            xml4_path = XML_DATA2_PATH / "4.xml"
            _update_xml_file(xml4_path, body.db_id, body)
            # 我们不关心4.xml的更新结果，因为它可能不存在
            # 只要5.xml更新成功即可

    except ValueError:
        # 如果日期格式错误，我们只更新5.xml，并忽略4.xml
        pass
    except Exception as e:
        print(f"处理 4.xml 更新时出错: {e}")

    # 修改2&250624/修改数据库change存入逻辑：自动记录更改后的新数据到change.xml和IMDB的change25表
    try:
        # 构造记录数据（使用更改后的新数据，与输入面板数据格式一致，db_id放首位）
        record_data = {
            'db_id': body.db_id,
            'employee_id': body.employee_id,
            'date': body.date,
            'model': body.model,
            'number': body.number,
            'factory_number': body.factory_number,
            'project_number': body.project_number,
            'unit_number': body.unit_number,
            'category': body.category,
            'item': body.item,
            'time': body.time,
            'department': body.department
        }
        
        # 写入change.xml和IMDB change25表（记录更改后的新数据）
        log_file = XML_DATA2_PATH / "change.xml"
        if _log_full_record_to_xml(log_file, record_data):
            print(f"更改后的新数据已写入change.xml")
        
        # 写入IMDB数据库（记录更改后的新数据）
        try:
            await init_imdb_tables()
            
            async with ImdbAsyncSessionLocal() as imdb_session:
                # 处理数据类型转换
                time_value = None
                if body.time:
                    try:
                        time_value = float(body.time)
                    except ValueError:
                        time_value = 0.0
                
                date_value = None
                if body.date:
                    try:
                        date_value = datetime.strptime(body.date, "%Y/%m/%d").date()
                    except ValueError:
                        date_value = datetime.now().date()
                
                await imdb_session.execute(
                    text('''INSERT INTO change25 
                        (db_id, employee_id, date, model, number, factory_number, project_number,
                         unit_number, category, item, time, department)
                        VALUES (:db_id, :employee_id, :date, :model, :number, :factory_number, :project_number,
                                :unit_number, :category, :item, :time, :department)'''),
                    {
                        "db_id": body.db_id,
                        "employee_id": body.employee_id,
                        "date": date_value,
                        "model": body.model or None,
                        "number": body.number or None,
                        "factory_number": body.factory_number or None,
                        "project_number": body.project_number or None,
                        "unit_number": body.unit_number or None,
                        "category": body.category or None,
                        "item": body.item or None,
                        "time": time_value,
                        "department": body.department or None
                    }
                )
                await imdb_session.commit()
                print(f"更改后的新数据已成功写入IMDB的change25表")
        except Exception as e:
            print(f"写入IMDB change25表失败: {e}")
            # 不影响主流程
            
    except Exception as e:
        print(f"记录更改日志失败: {e}")
        # 不影响主流程

    return {"status": "success", "message": f"记录 {body.db_id} 已成功更新并记录更改日志"}


#  修改，25061802  更改删除。
@router.post("/delete_progress")
async def delete_progress(body: ActionRequest):
    """ 修改3: 删除进度记录并记录到del.xml和IMDB """
    db_id = body.db_id
    
    # 修改3：先获取要删除的完整记录数据，然后删除
    record_data = _get_record_data_by_id(db_id)
    if not record_data:
        raise HTTPException(status_code=404, detail=f"在5.xml中未找到要删除的记录 ID: {db_id}")
    
    # 1. 从 5.xml 中删除并获取日期
    xml5_path = XML_DATA2_PATH / "5.xml"
    deleted_date_str = _delete_entry_from_xml(xml5_path, db_id)
    found_in_5 = deleted_date_str is not None

    # 2. 如果日期有效且是当月，也从 4.xml 中删除
    found_in_4 = False
    if deleted_date_str:
        try:
            record_date = datetime.strptime(deleted_date_str, "%Y/%m/%d")
            current_date = datetime.now()
            if record_date.year == current_date.year and record_date.month == current_date.month:
                xml4_path = XML_DATA2_PATH / "4.xml"
                if _delete_entry_from_xml(xml4_path, db_id) is not None:
                    found_in_4 = True
        except (ValueError, TypeError) as e:
            print(f"解析日期 {deleted_date_str} 时出错: {e}，将跳过对 4.xml 的删除。")

    # 3. 如果在任何一个文件中都找不到，则报告错误
    if not found_in_5 and not found_in_4:
         raise HTTPException(status_code=404, detail=f"在 4.xml 和 5.xml 中均未找到要删除的记录 ID: {db_id}")

    # 修改3：记录删除操作到del.xml和IMDB的del25表
    try:
        # 写入del.xml
        log_file = XML_DATA2_PATH / "del.xml"
        if _log_full_record_to_xml(log_file, record_data):
            print(f"删除记录已写入del.xml")
        
        # 写入IMDB数据库
        try:
            await init_imdb_tables()
            
            async with ImdbAsyncSessionLocal() as imdb_session:
                # 处理数据类型转换
                time_value = None
                if record_data.get('time'):
                    try:
                        time_value = float(record_data['time'])
                    except ValueError:
                        time_value = 0.0
                
                date_value = None
                if record_data.get('date'):
                    try:
                        date_value = datetime.strptime(record_data['date'], "%Y/%m/%d").date()
                    except ValueError:
                        date_value = datetime.now().date()
                
                await imdb_session.execute(
                    text('''INSERT INTO del25 
                        (db_id, employee_id, date, model, number, factory_number, project_number,
                         unit_number, category, item, time, department)
                        VALUES (:db_id, :employee_id, :date, :model, :number, :factory_number, :project_number,
                                :unit_number, :category, :item, :time, :department)'''),
                    {
                        "db_id": db_id,
                        "employee_id": record_data.get('employee_id', ''),
                        "date": date_value,
                        "model": record_data.get('model', '') or None,
                        "number": record_data.get('number', '') or None,
                        "factory_number": record_data.get('factory_number', '') or None,
                        "project_number": record_data.get('project_number', '') or None,
                        "unit_number": record_data.get('unit_number', '') or None,
                        "category": record_data.get('category', '') or None,
                        "item": record_data.get('item', '') or None,
                        "time": time_value,
                        "department": record_data.get('department', '') or None
                    }
                )
                await imdb_session.commit()
                print(f"删除记录已成功写入IMDB的del25表")
        except Exception as e:
            print(f"写入IMDB del25表失败: {e}")
            # 不影响主流程
            
    except Exception as e:
        print(f"记录删除日志失败: {e}")
        # 不影响主流程

    return {"status": "success", "message": f"记录 {db_id} 已成功删除并记录删除日志"}

# 修改，25061804  输入面板
@router.get("/employee_department/{employee_id}")
async def get_employee_department(employee_id: str):
    """
    修改，25061804  输入面板
    从 6.xml 中根据员工ID查找对应的部门代码。
    """
    xml6_path = XML_DATA2_PATH / "6.xml"
    default_department = "111"

    if not xml6_path.exists():
        return {"department": default_department, "message": "6.xml not found, returning default."}

    try:
        tree = ET.parse(xml6_path)
        root = tree.getroot()

        for record in root.findall("record"):
            emp_id_elem = record.find("employee_id")
            if emp_id_elem is not None and emp_id_elem.text == employee_id:
                dept_elem = record.find("department")
                if dept_elem is not None and dept_elem.text:
                    return {"department": dept_elem.text.strip()}
        
        # 如果循环结束没找到员工ID
        return {"department": default_department, "message": f"Employee ID {employee_id} not found, returning default."}

    except ET.ParseError as e:
        print(f"Error parsing 6.xml: {e}")
        return {"department": default_department, "message": f"Error parsing 6.xml, returning default."}
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Internal server error while reading 6.xml: {e}")


# PLC编程工具 - 系统架构图生成
@router.get("/system_chart.png")
async def get_system_chart():
    """
    生成系统架构图PNG文件
    """
    try:
        # 创建图像
        width, height = 800, 600
        image = Image.new('RGB', (width, height), color='white')
        draw = ImageDraw.Draw(image)
        
        # 尝试使用系统字体，如果失败则使用默认字体
        try:
            # Linux系统常见字体
            font_large = ImageFont.truetype("/usr/share/fonts/truetype/dejavu/DejaVuSans-Bold.ttf", 24)
            font_medium = ImageFont.truetype("/usr/share/fonts/truetype/dejavu/DejaVuSans.ttf", 16)
            font_small = ImageFont.truetype("/usr/share/fonts/truetype/dejavu/DejaVuSans.ttf", 12)
        except:
            try:
                # Windows系统字体
                font_large = ImageFont.truetype("arial.ttf", 24)
                font_medium = ImageFont.truetype("arial.ttf", 16)
                font_small = ImageFont.truetype("arial.ttf", 12)
            except:
                # 使用默认字体
                font_large = ImageFont.load_default()
                font_medium = ImageFont.load_default()
                font_small = ImageFont.load_default()
        
        # 绘制标题
        title = "MySuite PLC编程工具 - 系统架构图"
        draw.text((50, 30), title, fill='black', font=font_large)
        
        # 绘制架构组件
        components = [
            {"name": "Launcher.py", "pos": (100, 100), "size": (120, 60), "color": "#E3F2FD"},
            {"name": "program2.py", "pos": (300, 100), "size": (120, 60), "color": "#E8F5E8"},
            {"name": "Beremiz微服务", "pos": (550, 100), "size": (150, 60), "color": "#FFF3E0"},
            {"name": "ST代码编辑器", "pos": (100, 220), "size": (140, 60), "color": "#F3E5F5"},
            {"name": "转换引擎", "pos": (300, 220), "size": (120, 60), "color": "#E0F2F1"},
            {"name": "结果显示器", "pos": (500, 220), "size": (140, 60), "color": "#FFF8E1"},
            {"name": "文件管理", "pos": (100, 340), "size": (120, 60), "color": "#FCE4EC"},
            {"name": "日志系统", "pos": (300, 340), "size": (120, 60), "color": "#F1F8E9"},
            {"name": "192.168.3.120:61194", "pos": (500, 340), "size": (180, 60), "color": "#FFEBEE"}
        ]
        
        # 绘制组件框
        for comp in components:
            x, y = comp["pos"]
            w, h = comp["size"]
            
            # 绘制矩形框
            draw.rectangle([x, y, x+w, y+h], fill=comp["color"], outline='black', width=2)
            
            # 绘制组件名称
            text_x = x + w//2
            text_y = y + h//2
            bbox = draw.textbbox((0, 0), comp["name"], font=font_small)
            text_width = bbox[2] - bbox[0]
            text_height = bbox[3] - bbox[1]
            draw.text((text_x - text_width//2, text_y - text_height//2), 
                     comp["name"], fill='black', font=font_small)
        
        # 绘制连接线
        connections = [
            ((160, 130), (300, 130)),  # Launcher -> program2
            ((420, 130), (550, 130)),  # program2 -> Beremiz
            ((170, 160), (170, 220)),  # ST编辑器连接
            ((360, 160), (360, 220)),  # 转换引擎连接
            ((570, 160), (570, 220)),  # 结果显示连接
            ((160, 280), (160, 340)),  # 文件管理连接
            ((360, 280), (360, 340)),  # 日志系统连接
            ((590, 280), (590, 340)),  # 微服务地址连接
        ]
        
        for start, end in connections:
            draw.line([start, end], fill='blue', width=2)
        
        # 绘制流程说明
        flow_text = [
            "数据流程:",
            "1. 用户在ST代码编辑器中输入代码",
            "2. 点击转换按钮调用Beremiz微服务",
            "3. 微服务处理ST/LD/XML转换",
            "4. 结果显示在右侧面板",
            "5. 支持文件保存和日志记录"
        ]
        
        y_pos = 450
        for text in flow_text:
            draw.text((50, y_pos), text, fill='black', font=font_small)
            y_pos += 20
        
        # 保存图像到内存
        img_buffer = io.BytesIO()
        image.save(img_buffer, format='PNG')
        img_buffer.seek(0)
        
        # 将图像保存到临时文件
        temp_path = "/tmp/system_chart.png"
        image.save(temp_path, format='PNG')
        
        return FileResponse(temp_path, media_type="image/png", filename="system_chart.png")
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"生成系统架构图失败: {e}")


# PLC编程工具 - PLC网络拓扑图生成
@router.get("/plc_network_chart.png")
async def get_plc_network_chart():
    """
    生成PLC网络拓扑图PNG文件
    """
    try:
        # 创建图像
        width, height = 800, 600
        image = Image.new('RGB', (width, height), color='white')
        draw = ImageDraw.Draw(image)
        
        # 尝试使用系统字体，如果失败则使用默认字体
        try:
            # Linux系统常见字体
            font_large = ImageFont.truetype("/usr/share/fonts/truetype/dejavu/DejaVuSans-Bold.ttf", 24)
            font_medium = ImageFont.truetype("/usr/share/fonts/truetype/dejavu/DejaVuSans.ttf", 16)
            font_small = ImageFont.truetype("/usr/share/fonts/truetype/dejavu/DejaVuSans.ttf", 12)
        except:
            try:
                # Windows系统字体
                font_large = ImageFont.truetype("arial.ttf", 24)
                font_medium = ImageFont.truetype("arial.ttf", 16)
                font_small = ImageFont.truetype("arial.ttf", 12)
            except:
                # 使用默认字体
                font_large = ImageFont.load_default()
                font_medium = ImageFont.load_default()
                font_small = ImageFont.load_default()
        
        # 绘制标题
        title = "PLC网络拓扑图"
        draw.text((width//2 - 100, 20), title, fill='black', font=font_large)
        
        # 绘制Beremiz Runtime微服务节点
        beremiz_x, beremiz_y = 150, 100
        draw.rectangle([beremiz_x-70, beremiz_y-35, beremiz_x+70, beremiz_y+35], 
                      outline='blue', fill='lightblue', width=3)
        draw.text((beremiz_x-60, beremiz_y-25), "Beremiz Runtime", fill='darkblue', font=font_medium)
        draw.text((beremiz_x-65, beremiz_y-5), "192.168.3.120:61194", fill='darkblue', font=font_small)
        draw.text((beremiz_x-40, beremiz_y+10), "PYRO服务", fill='darkblue', font=font_small)
        
        # 绘制Program2客户端
        client_x, client_y = 550, 100
        draw.rectangle([client_x-70, client_y-35, client_x+70, client_y+35], 
                      outline='green', fill='lightgreen', width=3)
        draw.text((client_x-50, client_y-25), "Program2", fill='darkgreen', font=font_medium)
        draw.text((client_x-55, client_y-5), "PLC编程工具", fill='darkgreen', font=font_small)
        draw.text((client_x-35, client_y+10), "客户端", fill='darkgreen', font=font_small)
        
        # 绘制主连接线
        draw.line([beremiz_x+70, beremiz_y, client_x-70, client_y], fill='red', width=4)
        draw.text((350, beremiz_y-25), "PYRO连接", fill='red', font=font_medium)
        draw.text((340, beremiz_y-5), "ST/LD/XML转换", fill='red', font=font_small)
        
        # 绘制PLC设备群
        plc_devices = [
            ("PLC-001", 100, 280, "Modbus TCP"),
            ("PLC-002", 300, 280, "Modbus RTU"),
            ("PLC-003", 500, 280, "EtherNet/IP"),
            ("PLC-004", 700, 280, "Profinet")
        ]
        
        for device_name, x, y, protocol in plc_devices:
            # PLC设备框
            draw.rectangle([x-50, y-30, x+50, y+30], 
                          outline='orange', fill='lightyellow', width=2)
            draw.text((x-35, y-20), device_name, fill='darkorange', font=font_medium)
            draw.text((x-40, y-5), protocol, fill='darkorange', font=font_small)
            draw.text((x-25, y+10), "西门子S7", fill='darkorange', font=font_small)
            
            # 连接到Beremiz
            draw.line([beremiz_x, beremiz_y+35, x, y-30], fill='gray', width=2)
        
        # 绘制HMI界面
        hmi_x, hmi_y = 350, 400
        draw.rectangle([hmi_x-60, hmi_y-25, hmi_x+60, hmi_y+25], 
                      outline='purple', fill='lavender', width=2)
        draw.text((hmi_x-40, hmi_y-15), "HMI界面", fill='purple', font=font_medium)
        draw.text((hmi_x-35, hmi_y), "人机界面", fill='purple', font=font_small)
        
        # HMI连接到Beremiz
        draw.line([beremiz_x, beremiz_y+35, hmi_x, hmi_y-25], fill='purple', width=2)
        
        # 绘制数据库
        db_x, db_y = 350, 500
        draw.ellipse([db_x-40, db_y-20, db_x+40, db_y+20], 
                     outline='brown', fill='wheat', width=2)
        draw.text((db_x-25, db_y-10), "数据库", fill='brown', font=font_medium)
        draw.text((db_x-30, db_y), "历史数据", fill='brown', font=font_small)
        
        # 数据库连接
        draw.line([hmi_x, hmi_y+25, db_x, db_y-20], fill='brown', width=2)
        
        # 绘制功能说明框
        func_x, func_y = 50, 180
        draw.rectangle([func_x, func_y, func_x+200, func_y+120], 
                      outline='black', fill='#f0f0f0', width=1)
        
        functions = [
            "转换功能:",
            "• ST → LD 转换",
            "• ST → XML 导出", 
            "• LD → ST 反向",
            "• XML → ST 导入",
            "• 实时编译验证"
        ]
        
        for i, func in enumerate(functions):
            draw.text((func_x+10, func_y+10+i*18), func, fill='black', font=font_small)
        
        # 绘制协议说明框
        proto_x, proto_y = 550, 180
        draw.rectangle([proto_x, proto_y, proto_x+200, proto_y+120], 
                      outline='black', fill='#f0f8ff', width=1)
        
        protocols = [
            "通信协议:",
            "• PYRO: 远程对象",
            "• Modbus: 工业标准",
            "• EtherNet/IP: 以太网",
            "• Profinet: 西门子",
            "• TCP/IP: 网络基础"
        ]
        
        for i, protocol in enumerate(protocols):
            draw.text((proto_x+10, proto_y+10+i*18), protocol, fill='black', font=font_small)
        
        # 绘制状态指示器
        status_indicators = [
            ("在线", 650, 50, 'green'),
            ("运行", 700, 50, 'blue'),
            ("就绪", 750, 50, 'orange')
        ]
        
        for status, x, y, color in status_indicators:
            draw.ellipse([x-8, y-8, x+8, y+8], fill=color, outline='black')
            draw.text((x-15, y+12), status, fill=color, font=font_small)
        
        # 绘制边框
        draw.rectangle([5, 5, width-5, height-5], outline='black', width=3)
        
        # 保存图像到临时文件
        temp_path = "/tmp/plc_network_chart.png"
        #temp_path = "/home/<USER>/Pictures/plc_network_chart.png"
        image.save(temp_path, format='PNG')
        
        return FileResponse(temp_path, media_type="image/png", filename="plc_network_chart.png")
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"生成PLC网络拓扑图失败: {e}")


# 提供config文件夹中的图片文件
@router.get("/testpic.png")
async def get_testpic():
    """
    返回config文件夹中的testpic.png图片
    """
    try:
        # 构建config文件夹的绝对路径
        config_dir = Path(__file__).resolve().parent.parent / "config"
        testpic_path = config_dir / "testpic.png"
        
        if not testpic_path.exists():
            raise HTTPException(status_code=404, detail="testpic.png文件不存在")
        
        return FileResponse(str(testpic_path), media_type="image/png", filename="testpic.png")
        
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"获取testpic.png失败: {e}")


@router.get("/testpic0.png")
async def get_testpic0():
    """
    返回config文件夹中的testpic0.png图片
    """
    try:
        # 构建config文件夹的绝对路径
        config_dir = Path(__file__).resolve().parent.parent / "config"
        testpic0_path = config_dir / "testpic0.png"
        
        if not testpic0_path.exists():
            raise HTTPException(status_code=404, detail="testpic0.png文件不存在")
        
        return FileResponse(str(testpic0_path), media_type="image/png", filename="testpic0.png")
        
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"获取testpic0.png失败: {e}")


@router.get("/test_imdb_connection")
async def test_imdb_connection():
    """测试IMDB数据库连接"""
    try:
        # 确保IMDB表已创建
        await init_imdb_tables()
        print("IMDB表初始化完成")
        
        async with ImdbAsyncSessionLocal() as imdb_session:
            # 测试连接
            result = await imdb_session.execute(text("SELECT 1 as test"))
            test_value = result.scalar()
            print(f"数据库连接测试成功: {test_value}")
            
            # 测试add25表是否存在
            result = await imdb_session.execute(text("""
                SELECT COUNT(*) 
                FROM information_schema.tables 
                WHERE table_name = 'add25'
            """))
            table_exists = result.scalar()
            print(f"add25表存在检查: {table_exists}")
            
            # 测试表结构
            result = await imdb_session.execute(text("""
                SELECT column_name, data_type 
                FROM information_schema.columns 
                WHERE table_name = 'add25' 
                ORDER BY ordinal_position
            """))
            columns = result.fetchall()
            print(f"add25表结构: {columns}")
            
            # 测试插入数据
            test_id = str(uuid.uuid4())
            await imdb_session.execute(
                text('''INSERT INTO add25 
                    (db_id, employee_id, date, model, number, factory_number, project_number,
                     unit_number, category, item, time, department)
                    VALUES (:db_id, :employee_id, :date, :model, :number, :factory_number, :project_number,
                            :unit_number, :category, :item, :time, :department)'''),
                {
                    "db_id": test_id,
                    "employee_id": "TEST_USER",
                    "date": datetime.now().date(),
                    "model": "TEST_MODEL",
                    "number": "TEST_NUM",
                    "factory_number": "TEST_FACTORY",
                    "project_number": "TEST_PROJECT",
                    "unit_number": "TEST_UNIT",
                    "category": "TEST_CAT",
                    "item": "TEST_ITEM",
                    "time": 8.0,
                    "department": "TEST_DEPT"
                }
            )
            await imdb_session.commit()
            print("测试数据插入成功")
            
            # 查询确认数据
            result = await imdb_session.execute(
                text("SELECT COUNT(*) FROM add25 WHERE db_id = :db_id"),
                {"db_id": test_id}
            )
            count = result.scalar()
            print(f"插入数据确认: {count}")
            
            return {
                "status": "success",
                "message": "IMDB连接测试成功",
                "details": {
                    "connection_test": test_value,
                    "table_exists": table_exists > 0,
                    "columns": [{"name": col[0], "type": col[1]} for col in columns],
                    "test_insert": count > 0,
                    "test_id": test_id
                }
            }
            
    except Exception as e:
        import traceback
        error_details = traceback.format_exc()
        print(f"IMDB连接测试失败: {e}")
        print(f"详细错误: {error_details}")
        return {
            "status": "error",
            "message": f"IMDB连接测试失败: {str(e)}",
            "details": error_details
        }

@router.get("/reset_imdb_tables")
async def reset_imdb_tables():
    """重置IMDB数据库表结构"""
    try:
        async with ImdbAsyncSessionLocal() as imdb_session:
            # 删除现有表
            print("正在删除现有IMDB表...")
            await imdb_session.execute(text("DROP TABLE IF EXISTS add25 CASCADE"))
            await imdb_session.execute(text("DROP TABLE IF EXISTS change25 CASCADE"))
            await imdb_session.execute(text("DROP TABLE IF EXISTS del25 CASCADE"))
            await imdb_session.commit()
            print("现有表删除完成")
            
        # 重新初始化表
        print("正在重新创建IMDB表...")
        await init_imdb_tables()
        print("✅ IMDB表重新创建完成")
        
        # 验证表结构
        async with ImdbAsyncSessionLocal() as imdb_session:
            result = await imdb_session.execute(text("""
                SELECT column_name, data_type, is_nullable
                FROM information_schema.columns 
                WHERE table_name = 'add25' 
                ORDER BY ordinal_position
            """))
            columns = result.fetchall()
            print(f"add25表新结构: {columns}")
            
            return {
                "status": "success",
                "message": "IMDB表重置成功",
                "add25_columns": [{"name": col[0], "type": col[1], "nullable": col[2]} for col in columns]
            }
            
    except Exception as e:
        import traceback
        error_details = traceback.format_exc()
        print(f"❌ IMDB表重置失败: {e}")
        print(f"详细错误: {error_details}")
        return {
            "status": "error",
            "message": f"IMDB表重置失败: {str(e)}",
            "details": error_details
        }

@router.get("/check_imdb_data")
async def check_imdb_data():
    """查询IMDB数据库中的数据"""
    try:
        async with ImdbAsyncSessionLocal() as imdb_session:
            # 查询add25表中最新的5条记录
            result = await imdb_session.execute(text("""
                SELECT db_id, employee_id, date, model, number, factory_number, 
                       project_number, unit_number, category, item, time, department, ts 
                FROM add25 
                ORDER BY ts DESC 
                LIMIT 5
            """))
            records = result.fetchall()
            
            # 查询总记录数
            count_result = await imdb_session.execute(text("SELECT COUNT(*) FROM add25"))
            total_count = count_result.scalar()
            
            return {
                "status": "success",
                "total_records": total_count,
                "latest_records": [
                    {
                        "db_id": record[0],
                        "employee_id": record[1], 
                        "date": str(record[2]) if record[2] else None,
                        "model": record[3],
                        "number": record[4],
                        "factory_number": record[5],
                        "project_number": record[6],
                        "unit_number": record[7],
                        "category": record[8],
                        "item": record[9],
                        "time": float(record[10]) if record[10] else None,
                        "department": record[11],
                        "timestamp": str(record[12]) if record[12] else None
                    }
                    for record in records
                ]
            }
            
    except Exception as e:
        import traceback
        error_details = traceback.format_exc()
        print(f"❌ IMDB数据查询失败: {e}")
        return {
            "status": "error",
            "message": f"IMDB数据查询失败: {str(e)}",
            "details": error_details
        }

@router.get("/check_change_del_data")
async def check_change_del_data():
    """查询IMDB change25和del25表中的数据"""
    try:
        async with ImdbAsyncSessionLocal() as imdb_session:
            # 查询change25表
            change_result = await imdb_session.execute(text("""
                SELECT db_id, employee_id, date, model, number, time, ts 
                FROM change25 
                ORDER BY ts DESC 
                LIMIT 3
            """))
            change_records = change_result.fetchall()
            
            # 查询del25表
            del_result = await imdb_session.execute(text("""
                SELECT db_id, employee_id, date, model, number, time, ts 
                FROM del25 
                ORDER BY ts DESC 
                LIMIT 3
            """))
            del_records = del_result.fetchall()
            
            # 查询总记录数
            change_count_result = await imdb_session.execute(text("SELECT COUNT(*) FROM change25"))
            change_count = change_count_result.scalar()
            
            del_count_result = await imdb_session.execute(text("SELECT COUNT(*) FROM del25"))
            del_count = del_count_result.scalar()
            
            return {
                "status": "success",
                "change25": {
                    "total_count": change_count,
                    "latest_records": [
                        {
                            "db_id": record.db_id,
                            "employee_id": record.employee_id,
                            "date": record.date.isoformat() if record.date else None,
                            "model": record.model,
                            "number": record.number,
                            "time": float(record.time) if record.time else None,
                            "timestamp": record.ts.isoformat() if record.ts else None
                        }
                        for record in change_records
                    ]
                },
                "del25": {
                    "total_count": del_count,
                    "latest_records": [
                        {
                            "db_id": record.db_id,
                            "employee_id": record.employee_id,
                            "date": record.date.isoformat() if record.date else None,
                            "model": record.model,
                            "number": record.number,
                            "time": float(record.time) if record.time else None,
                            "timestamp": record.ts.isoformat() if record.ts else None
                        }
                        for record in del_records
                    ]
                }
            }
    except Exception as e:
        print(f"查询change25和del25数据时出错: {e}")
        return {"status": "error", "message": str(e)}

