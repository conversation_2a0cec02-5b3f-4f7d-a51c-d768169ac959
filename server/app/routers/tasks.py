# server/app/routers/tasks.py

from fastapi import APIRouter, HTTPException
from typing import List
from datetime import datetime

from sqlalchemy.future import select

from ..database import AsyncSessionLocal
from ..models import Task
from pydantic import BaseModel

router = APIRouter(prefix="/api/tasks", tags=["tasks"])

# 数据库连接检查
def check_db_connection():
    from ..main import DB_CONNECTED
    if not DB_CONNECTED:
        raise HTTPException(
            status_code=503, 
            detail="Database not available. Task management requires database connection."
        )

class TaskItem(BaseModel):
    id: str
    description: str
    status: str
    due_date: datetime

@router.get("/pending", response_model=List[TaskItem])
async def get_pending_tasks():
    """
    获取所有 status='pending' 的任务
    """
    check_db_connection()
    async with AsyncSessionLocal() as session:
        result = await session.execute(
            select(Task).where(Task.status == "pending")
        )
        pending = result.scalars().all()
        return pending
