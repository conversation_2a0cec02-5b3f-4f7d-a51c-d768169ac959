# server/app/main.py
import platform  
import uvicorn
import asyncio
from fastapi import FastAPI
from fastapi.middleware.cors import CORSMiddleware
import os
from contextlib import asynccontextmanager
from pathlib import Path
import datetime
from cryptography import x509
from cryptography.x509.oid import NameOID
from cryptography.hazmat.primitives import hashes, serialization
from cryptography.hazmat.primitives.asymmetric import rsa


# 数据库相关导入
from .database import init_db
from .databases.postgresql.client import test_db_connection, close_db, init_imdb_tables
# 修改34-3 chart相关: 导入异步redis服务
from .core.services.redis_service import redis_service
from .routers import (
    feature_flags,
    tasks,
    excel_upload,
    serial_data,
    odbc_actions,
    xml_actions,
    client_version,
    counter,
    extra_actions,          # 注释42 再次增加ui基面的功能
    # auth_login,           # 认证功能已迁移到 server3
    # hardware_registration # 硬件注册功能已迁移到 server3
)
from .websocket.flags_ws import router as ws_router
from .modules.task_checker import check_overdue_tasks_loop
from .modules.xml_processor import process_xml_loop

# 全局变量，用于跟踪数据库连接状态
DB_CONNECTED = False

# FastAPI实例将在lifespan定义后创建


def generate_self_signed_cert(cert_path: Path, key_path: Path):
    """Generates a self-signed certificate and key if they don't exist."""
    print("Generating self-signed certificate...")
    # Generate private key
    private_key = rsa.generate_private_key(
        public_exponent=65537,
        key_size=2048,
    )

    # Generate a self-signed certificate
    subject = issuer = x509.Name([
        x509.NameAttribute(NameOID.COUNTRY_NAME, "US"),
        x509.NameAttribute(NameOID.STATE_OR_PROVINCE_NAME, "California"),
        x509.NameAttribute(NameOID.LOCALITY_NAME, "San Francisco"),
        x509.NameAttribute(NameOID.ORGANIZATION_NAME, "My Company"),
        x509.NameAttribute(NameOID.COMMON_NAME, "localhost"),
    ])
    cert = x509.CertificateBuilder().subject_name(
        subject
    ).issuer_name(
        issuer
    ).public_key(
        private_key.public_key()
    ).serial_number(
        x509.random_serial_number()
    ).not_valid_before(
        datetime.datetime.utcnow()
    ).not_valid_after(
        # Certificate valid for 1 year
        datetime.datetime.utcnow() + datetime.timedelta(days=365)
    ).add_extension(
        x509.SubjectAlternativeName([x509.DNSName("localhost")]),
        critical=False,
    ).sign(private_key, hashes.SHA256())

    # Write key to file
    with open(key_path, "wb") as f:
        f.write(private_key.private_bytes(
            encoding=serialization.Encoding.PEM,
            format=serialization.PrivateFormat.TraditionalOpenSSL,
            encryption_algorithm=serialization.NoEncryption(),
        ))
    # Write certificate to file
    with open(cert_path, "wb") as f:
        f.write(cert.public_bytes(serialization.Encoding.PEM))
    print(f"Certificate and key generated at {cert_path} and {key_path}")


@asynccontextmanager
async def lifespan(app: FastAPI):
    global DB_CONNECTED
    # Startup event
    print("=== Server Starting Up ===")
    
    # 修改34-3 chart相关: 连接Redis
    await redis_service.connect()
    
    # 1. 测试数据库连接
    print("Testing database connection...")
    try:
        DB_CONNECTED = await test_db_connection()
        if DB_CONNECTED:
            print("✅ Database connection successful!")
            # 2. 初始化数据库表结构
            await init_db()
            print("✅ Database initialization complete!")
            
            # 初始化IMDB数据库表
            await init_imdb_tables()
            print("✅ IMDB database tables initialization complete!")
            
            # 250618.18：39加入注册 - 初始化硬件注册表
            # from app.routers.hardware_registration import init_hardware_registration_table
            # await init_hardware_registration_table()
            # print("✅ Hardware registration table initialized!")
        else:
            print("❌ Database connection failed - running in limited mode")
    except Exception as e:
        print(f"❌ Database setup failed: {e}")
        print("Server will continue in limited mode (database features disabled)")
        DB_CONNECTED = False
    
    # 3. 启动后台任务（仅当数据库连接成功时）
    if DB_CONNECTED:
        asyncio.create_task(check_overdue_tasks_loop())
        print("✅ Task checker started")
        
        asyncio.create_task(process_xml_loop())
        print("✅ XML processor started")
    else:
        print("⚠️  Background tasks disabled (no database connection)")
    
    mode = "Full Mode" if DB_CONNECTED else "Limited Mode (Database Disabled)"
    print(f"🚀 Server startup complete - Running in {mode}")
    print("=== Server Ready ===")
    
    yield
    
    # Shutdown event
    print("=== Server Shutting Down ===")
    
    # 修改34-3 chart相关: 断开Redis连接
    await redis_service.close()
    
    if DB_CONNECTED:
        await close_db()
        print("✅ Database connections closed")
    print("👋 Server shutdown complete")

# 创建FastAPI实例
app = FastAPI(
    lifespan=lifespan,
    title="MySuite API",
    description="MySuite服务器API - 支持数据库连接自动检测",
    version="1.0.0"
)

# 添加CORS中间件
app.add_middleware(
    CORSMiddleware,
    
    allow_origins=["*"],
    allow_methods=["*"],
    allow_headers=["*"],
)

# 根路径端点 - 显示服务器状态
@app.get("/")
async def root():
    global DB_CONNECTED
    return {
        "message": "MySuite Server is running!",
        "status": "ok",
        "database_connected": DB_CONNECTED,
        #"mode": "full" if DB_CONNECTED else "limited",
        "mode": "full" if DB_CONNECTED else "full",
        "available_endpoints": [
            "/docs",
            "/health",
            "/api/client/version",
            "/api/excel",
            "/api/serial",
            "/api/xml",
            "/api/counter",
            # "/api/auth",  # 认证端点已迁移到 server3
        ] + ([
            "/api/feature_flags",
            "/api/tasks",
            "/api/odbc",
            "/ws/flags"
        ] if DB_CONNECTED else []),
        "disabled_features": [] if DB_CONNECTED else [
            "Feature flags management",
            "Task management", 
            "ODBC operations",
            "WebSocket notifications"
        ]
    }

# 健康检查端点
@app.get("/health")
async def health_check():
    global DB_CONNECTED
    return {
        "status": "healthy", 
        "timestamp": datetime.datetime.now().isoformat(),
        "database_connected": DB_CONNECTED
    }

# 数据库状态检查端点
@app.get("/api/db/status")
async def db_status():
    global DB_CONNECTED
    if DB_CONNECTED:
        # 实时测试数据库连接
        current_status = await test_db_connection()
        if not current_status:
            DB_CONNECTED = False
        return {
            "connected": current_status,
            "message": "Database is connected and responding" if current_status else "Database connection lost"
        }
    else:
        return {
            "connected": False,
            "message": "Database was not connected at startup"
        }

# 包含所有路由
app.include_router(excel_upload.router)
app.include_router(serial_data.router)
app.include_router(xml_actions.router)
app.include_router(client_version.router)
app.include_router(counter.router)
# app.include_router(auth_login.router)  # 认证功能已迁移到 server3
app.include_router(extra_actions.router)
# app.include_router(hardware_registration.router)  # 硬件注册功能已迁移到 server3
# 20250618.20:15 微服务-信息交流 - 聊天功能已迁移到独立微服务器（端口8005）
# app.include_router(chat_websocket.router)  # 聊天路由已移除

# 数据库相关路由 - 总是包含，但会在路由内部检查数据库连接状态
app.include_router(feature_flags.router)
app.include_router(tasks.router)
app.include_router(odbc_actions.router)
app.include_router(ws_router)

if __name__ == "__main__":
    # Assumes the script is run from the `server` directory.
    config_dir = Path("config2")
    config_dir.mkdir(exist_ok=True)
    cert_path = config_dir / "cert.pem"
    key_path = config_dir / "key.pem"

    # Generate self-signed certs if they don't exist
    if os.environ.get("RUN_WITH_NGINX", "false").lower() != "true":
        if not cert_path.exists() or not key_path.exists():
            generate_self_signed_cert(cert_path, key_path)


    print("🚀 Starting MySuite Server...")
    print("📋 Features:")
    print("  • Auto SSL certificate generation")
    print("  • Database connection auto-detection")
    print("  • Graceful degradation if database unavailable")
    print("  • FastAPI automatic documentation")
    print("")
    print("🔗 Access URLs:")
    print("  • Main API: https://localhost:8003/")
    print("  • API Docs: https://localhost:8003/docs")
    print("  • Health Check: https://localhost:8003/health")
    #print("  • DB Status: https://localhost:8003/api/db/status")
    print("  • Login Test: https://localhost:8003/api/auth/test-decrypt")  # 修改14 增加id 密码和跳转界面
    print("")
        
    


    uvicorn.run(
        "app.main:app",
        host="127.0.0.1", # <-- 修改2：只监听本地回环地址
        port=8003,       # <-- 端口可以不变，或改为其他内部端口
        # ssl_certfile=str(cert_path), # <-- 移除
        # ssl_keyfile=str(key_path),   # <-- 移除
        reload=True,     # 开发环境保留，生产环境通常关闭
        workers=1,       # 开发环境保持1个worker，生产环境根据CPU核心数设置
        loop="asyncio",
        http="httptools",
        access_log=False,
        limit_concurrency=1000,
        limit_max_requests=10000,
        timeout_keep_alive=30,
        # ssl_ciphers="ECDHE+AESGCM:ECDHE+CHACHA20:DHE+AESGCM:DHE+CHACHA20:!aNULL:!MD5:!DSS", # <-- 移除
    )