# server/app/auth/jwt_auth.py
# 兼容性存根 - 认证功能已迁移到 server3
from fastapi import Depends, HTTPException, status
from fastapi.security import HTT<PERSON><PERSON><PERSON><PERSON>, HTTPAuthorizationCredentials
from jose import JWTError, jwt
import logging

logger = logging.getLogger(__name__)

# JWT 配置 - 这些值应该与 server3 保持一致
SECRET_KEY = "your-very-secret-signing-key"  # 与客户端保持一致
ALGORITHM = "HS256"

security = HTTPBearer()

async def verify_token(credentials: HTTPAuthorizationCredentials = Depends(security)):
    """
    兼容性函数：验证 JWT token
    现在使用本地验证来保持 server 的独立性
    """
    token = credentials.credentials
    
    try:
        payload = jwt.decode(token, SECRET_KEY, algorithms=[ALGORITHM])
        username = payload.get("sub")
        if username is None:
            raise HTTPException(
                status_code=status.HTTP_401_UNAUTHORIZED,
                detail="Invalid token",
                headers={"WWW-Authenticate": "Bearer"}
            )
        return {"username": username}
    except JWTError:
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="Invalid token",
            headers={"WWW-Authenticate": "Bearer"}
        )

async def verify_jwt_token(token: str):
    """
    兼容性函数：验证 JWT token (用于 WebSocket)
    """
    try:
        payload = jwt.decode(token, SECRET_KEY, algorithms=[ALGORITHM])
        username = payload.get("sub")
        if username is None:
            raise ValueError("Invalid token content")
        return {"username": username}
    except JWTError:
        raise ValueError("Invalid or expired token")

def create_access_token(data: dict):
    """
    兼容性函数：创建访问令牌
    注意：新的用户注册和登录应该通过 server3 进行
    """
    logger.warning("create_access_token called in server - consider using server3 for authentication")
    encoded_jwt = jwt.encode(data, SECRET_KEY, algorithm=ALGORITHM)
    return encoded_jwt 