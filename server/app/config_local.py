# server/app/config_local.py
# 本地开发环境数据库配置
# 用于本地PostgreSQL数据库连接

# PostgreSQL数据库配置，用于存储用户和配置数据
DB_HOST = "localhost"  # 本地数据库主机地址
DB_PORT = 5432         # 数据库端口
DB_NAME = "postgres"   # 数据库名称
DB_USER = "postgres"   # 数据库用户名
DB_PASS = "postgres"   # 数据库密码 (请根据实际情况修改)

# 构建数据库连接URL (使用asyncpg兼容格式)
DATABASE_URL = f"postgresql://{DB_USER}:{DB_PASS}@{DB_HOST}:{DB_PORT}/{DB_NAME}"

# IMDB数据库配置，用于存储entries表数据
IMDB_HOST = "localhost"  # IMDB数据库主机地址
IMDB_PORT = 5432         # IMDB数据库端口
IMDB_NAME = "imdb"       # IMDB数据库名称
IMDB_USER = "postgres"   # IMDB数据库用户名
IMDB_PASS = "postgres"   # IMDB数据库密码 (请根据实际情况修改)

# 构建IMDB数据库连接URL (使用asyncpg兼容格式)
IMDB_DATABASE_URL = f"postgresql://{IMDB_USER}:{IMDB_PASS}@{IMDB_HOST}:{IMDB_PORT}/{IMDB_NAME}"

# MongoDB配置，用于存储传感器数据
MONGO_HOST = "localhost"       # MongoDB主机地址
MONGO_PORT = 27017             # MongoDB端口
MONGO_DB = "sensor_db"         # MongoDB数据库名称
MONGO_COLLECTION = "sensor_data"  # MongoDB集合名称

# Redis配置，用于缓存和实时数据存储
REDIS_HOST = "localhost"       # Redis主机地址（本地）
REDIS_PORT = 6379              # Redis端口
REDIS_DB = 0                   # Redis使用的数据库编号

# 硬件指纹删除功能 - 管理员删除密码
ADMIN_DELETE_PASSWORD = "admin123"  # 删除硬件指纹时需要的管理员密码 

# 表格配置 (更新为实际数据库字段)
TABLE_CONFIG = {
    'entries': {
        'table_name': 'entries',
        'columns': [
            'id', 'employee_id', 'entry_date', 'department', 'model', 
            'number', 'factory_number', 'project_number', 'unit_number',
            'category', 'item', 'duration', 'source', 'ts'
        ],
        'date_column': 'entry_date',
        'employee_column': 'employee_id'
    },
    'timeprotab': {
        'table_name': 'timeprotab',
        'columns': [
            'id', 'employee_id', '日付', '星期', 'ｶﾚﾝﾀﾞ', '不在', '勤務区分',
            '事由', '出勤時刻', 'ＭＣ_出勤', '退勤時刻', 'ＭＣ_退勤', '所定時間',
            '早出残業', '内深夜残業', '遅刻早退', '休出時間', '出張残業',
            '外出時間', '戻り時間', 'コメント', 'created_at', 'updated_at'
        ],
        'date_column': '日付',
        'employee_column': 'employee_id',
        'is_partitioned': True,
        'partition_format': 'timeprotab_{month_code}'
    }
}

# 环境标识
ENVIRONMENT = "local_development" 