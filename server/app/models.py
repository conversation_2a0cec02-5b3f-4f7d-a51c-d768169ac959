# server/app/models.py

from sqlalchemy import Column, String, Boolean, DateTime
from sqlalchemy.sql import func
from .database import Base

class FeatureFlag(Base):
    __tablename__ = "feature_flags"
    name = Column(String, primary_key=True, index=True)
    enabled = Column(Boolean, nullable=False, default=False)
    last_updated = Column(DateTime(timezone=True), server_default=func.now(), onupdate=func.now())

class Task(Base):
    __tablename__ = "tasks"
    id = Column(String, primary_key=True, index=True)
    description = Column(String, nullable=False)
    status = Column(String, nullable=False, default="pending")  # pending/completed
    due_date = Column(DateTime, nullable=False)
