# server/app/databases/odbc/client.py

import os
import platform  # 更改12 odbc按键处理，@client_fixed.py @/server - 添加平台检测

# 更改12 odbc按键处理，@client_fixed.py @/server - 条件导入pywin32相关模块
IS_WINDOWS = platform.system().lower() == 'windows'

if IS_WINDOWS:
    try:
        import pythoncom
        from win32com.client import Dispatch
        PYWIN32_AVAILABLE = True
    except ImportError as e:
        print(f"警告: pywin32导入失败: {e}")
        PYWIN32_AVAILABLE = False
else:
    print("非Windows平台，ODBC功能将被禁用")
    PYWIN32_AVAILABLE = False

# 获取到 1.mdb 路径 server/config/1.mdb
MDB_PATH = os.path.abspath(os.path.join(os.path.dirname(__file__), "../../config/1.mdb"))

class ODBCClient:
    """
    使用 win32com.client 访问 ODBC (ADODB) 读写 .mdb 文件
    更改12 odbc按键处理，@client_fixed.py @/server - 增加平台检测和按需连接
    """
    def __init__(self):
        # 针对 Access 2003 格式的 .mdb 文件使用 Jet.OLEDB.4.0
        self.conn_str = (
            f"Provider=Microsoft.Jet.OLEDB.4.0;"
            f"Data Source={MDB_PATH};"
            "Persist Security Info=False;"
        )
        self.connection = None
        # 更改12 odbc按键处理，@client_fixed.py @/server - 添加状态跟踪
        self.is_connected = False
        self.platform_supported = IS_WINDOWS and PYWIN32_AVAILABLE
        self.connection_attempted = False

    def connect(self):
        """更改12 odbc按键处理，@client_fixed.py @/server - 增加平台检查的连接方法"""
        if not self.platform_supported:
            raise RuntimeError("ODBC功能需要Windows平台和pywin32库支持")
        
        if self.is_connected:
            return  # 已连接，无需重复连接
            
        try:
            pythoncom.CoInitialize()
            conn = Dispatch('ADODB.Connection')
            conn.Open(self.conn_str)
            self.connection = conn
            self.is_connected = True
            self.connection_attempted = True
            print("ODBC连接成功")
        except Exception as e:
            self.is_connected = False
            self.connection_attempted = True
            print(f"ODBC连接失败: {e}")
            raise

    def check_connection_status(self):
        """更改12 odbc按键处理，@client_fixed.py @/server - 检查连接状态"""
        return {
            "platform_supported": self.platform_supported,
            "is_connected": self.is_connected,
            "connection_attempted": self.connection_attempted,
            "platform": platform.system(),
            "pywin32_available": PYWIN32_AVAILABLE if IS_WINDOWS else False
        }

    def execute_query(self, sql: str):
        """
        执行 SELECT 语句并返回结果集列表
        更改12 odbc按键处理，@client_fixed.py @/server - 增加连接检查
        """
        if not self.platform_supported:
            raise RuntimeError("ODBC功能需要Windows平台和pywin32库支持")
            
        if not self.connection:
            self.connect()
        rs = Dispatch('ADODB.Recordset')
        rs.Open(sql, self.connection, 1, 1)  # 1=adOpenKeyset, 1=adLockOptimistic
        results = []
        while not rs.EOF:
            row = {}
            for i in range(rs.Fields.Count):
                field_name = rs.Fields.Item(i).Name
                field_value = rs.Fields.Item(i).Value
                row[field_name] = field_value
            results.append(row)
            rs.MoveNext()
        rs.Close()
        return results

    def execute_non_query(self, sql: str):
        """
        执行 INSERT/UPDATE/DELETE 语句，返回受影响的记录数
        更改12 odbc按键处理，@client_fixed.py @/server - 增加连接检查
        """
        if not self.platform_supported:
            raise RuntimeError("ODBC功能需要Windows平台和pywin32库支持")
            
        if not self.connection:
            self.connect()
        cmd = Dispatch('ADODB.Command')
        cmd.ActiveConnection = self.connection
        cmd.CommandText = sql
        # Execute 返回 (records_affected, ...)
        return cmd.Execute()[1]

    def close(self):
        """更改12 odbc按键处理，@client_fixed.py @/server - 增加状态重置"""
        if self.connection:
            self.connection.Close()
            self.connection = None
            if self.platform_supported:  # 只在支持的平台上调用CoUninitialize
                pythoncom.CoUninitialize()
        self.is_connected = False

# 创建单例供 import 使用
# 更改12 odbc按键处理，@client_fixed.py @/server - 总是创建实例，但不自动连接
odbc_client = ODBCClient()
