# server/app/databases/postgresql/client.py

import os
from sqlalchemy.ext.asyncio import create_async_engine, AsyncSession
from sqlalchemy.orm import sessionmaker, declarative_base
from ...config import DATABASE_URL, IMDB_DATABASE_URL  # 注释42 - 使用配置文件中的数据库URL
from typing import AsyncGenerator # 修改34-2 chart相关


# 数据库配置已移至 app/config.py 文件中统一管理


# PostgreSQL 数据库连接 URL - 使用 asyncpg 驱动
# 注释42 - 改进连接池配置，设置连接池大小和回收时间
engine = create_async_engine(
    DATABASE_URL,
    echo=False,  # 设置为 True 可以看到 SQL 查询日志
    future=True,
    # 连接池配置 - 注释42
    pool_size=10,          # 连接池大小
    max_overflow=20,       # 超出连接池大小时最大额外连接数
    pool_recycle=3600,     # 连接回收时间（秒）- 1小时
    pool_pre_ping=True,    # 连接前检查连接是否有效
    pool_timeout=30,       # 获取连接的超时时间（秒）
    pool_reset_on_return='commit',  # 连接返回池时的重置方式
)

# IMDB 数据库连接 - 用于add25/change25/del25表
imdb_engine = create_async_engine(
    IMDB_DATABASE_URL,
    echo=False,
    future=True,
    pool_size=5,
    max_overflow=10,
    pool_recycle=3600,
    pool_pre_ping=True,
    pool_timeout=30,
    pool_reset_on_return='commit',
)

AsyncSessionLocal = sessionmaker(
    bind=engine,
    class_=AsyncSession,
    expire_on_commit=False
)

# IMDB数据库会话
ImdbAsyncSessionLocal = sessionmaker(
    bind=imdb_engine,
    class_=AsyncSession,
    expire_on_commit=False
)

Base = declarative_base()

#修改34-2  chart相关 - 添加数据库会话依赖项
async def get_db() -> AsyncGenerator[AsyncSession, None]:
    """
    数据库会话依赖项
    """
    async with AsyncSessionLocal() as session:
        yield session

# 添加IMDB数据库会话依赖项
async def get_imdb_db() -> AsyncGenerator[AsyncSession, None]:
    """
    IMDB数据库会话依赖项
    """
    async with ImdbAsyncSessionLocal() as session:
        yield session

async def init_db():
    """
    初始化数据库表结构（基于 Base.metadata）
    """
    try:
        async with engine.begin() as conn:
            await conn.run_sync(Base.metadata.create_all)
        print("Database tables initialized successfully")
    except Exception as e:
        print(f"Database initialization failed: {e}")
        raise

async def init_imdb_tables():
    """
    初始化IMDB数据库表结构
    """
    try:
        from sqlalchemy import text
        async with imdb_engine.begin() as conn:
            # 创建add25表
            await conn.execute(text("""
                CREATE TABLE IF NOT EXISTS add25 (
                    id SERIAL PRIMARY KEY,
                    db_id VARCHAR(255),
                    employee_id VARCHAR(50),
                    date DATE,
                    model VARCHAR(100),
                    number VARCHAR(50),
                    factory_number VARCHAR(100),
                    project_number VARCHAR(100),
                    unit_number VARCHAR(50),
                    category VARCHAR(50),
                    item VARCHAR(100),
                    time DECIMAL(10,2),
                    department VARCHAR(50),
                    ts TIMESTAMPTZ DEFAULT NOW()
                )
            """))
            
            # 创建change25表
            await conn.execute(text("""
                CREATE TABLE IF NOT EXISTS change25 (
                    id SERIAL PRIMARY KEY,
                    db_id VARCHAR(255),
                    employee_id VARCHAR(50),
                    date DATE,
                    model VARCHAR(100),
                    number VARCHAR(50),
                    factory_number VARCHAR(100),
                    project_number VARCHAR(100),
                    unit_number VARCHAR(50),
                    category VARCHAR(50),
                    item VARCHAR(100),
                    time DECIMAL(10,2),
                    department VARCHAR(50),
                    ts TIMESTAMPTZ DEFAULT NOW()
                )
            """))
            
            # 创建del25表
            await conn.execute(text("""
                CREATE TABLE IF NOT EXISTS del25 (
                    id SERIAL PRIMARY KEY,
                    db_id VARCHAR(255),
                    employee_id VARCHAR(50),
                    date DATE,
                    model VARCHAR(100),
                    number VARCHAR(50),
                    factory_number VARCHAR(100),
                    project_number VARCHAR(100),
                    unit_number VARCHAR(50),
                    category VARCHAR(50),
                    item VARCHAR(100),
                    time DECIMAL(10,2),
                    department VARCHAR(50),
                    ts TIMESTAMPTZ DEFAULT NOW()
                )
            """))
            
        print("IMDB database tables (add25, change25, del25) initialized successfully")
    except Exception as e:
        print(f"IMDB database initialization failed: {e}")
        raise

async def close_db():
    """
    关闭数据库连接
    """
    await engine.dispose()
    await imdb_engine.dispose()
    print("Database connections closed")

# 添加数据库连接测试函数
async def test_db_connection():
    """
    测试数据库连接
    """
    try:
        from sqlalchemy import text
        async with engine.begin() as conn:
            result = await conn.execute(text("SELECT 1"))
        
        # 测试IMDB数据库连接
        async with imdb_engine.begin() as conn:
            result = await conn.execute(text("SELECT 1"))
        
        print("Database connection test successful")
        return True
    except Exception as e:
        print(f"Database connection test failed: {e}")
        return False

# 注释42 - 添加连接池状态监控函数
def get_pool_status():
    """
    获取连接池状态信息
    """
    pool = engine.pool
    return {
        "pool_size": pool.size(),
        "checked_in": pool.checkedin(),
        "checked_out": pool.checkedout(),
        "overflow": pool.overflow(),
        "invalid": pool.invalid()
    }
