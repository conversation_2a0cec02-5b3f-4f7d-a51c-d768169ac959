from ..models import FeatureFlag# server/app/modules/task_checker.py

import asyncio
from datetime import datetime

from sqlalchemy.future import select

from ..database import AsyncSessionLocal
from ..models import Task
from ..routers.feature_flags import FeatureFlagItem, set_flag

async def check_overdue_tasks_loop():
    """
    周期性检查逾期任务，并根据结果自动设置 'floating_reminder' 标志。
    """
    while True:
        async with AsyncSessionLocal() as session:
            result = await session.execute(
                select(Task).where(Task.status == "pending", Task.due_date < datetime.utcnow())
            )
            overdue_count = len(result.scalars().all())
        
        # 为避免不必要的写入和通知，先从数据库获取当前 flag 状态
        async with AsyncSessionLocal() as session:
            flag_item = await session.get(FeatureFlag, "floating_reminder")
            current_status = flag_item.enabled if flag_item else False

        new_status = overdue_count > 0
        
        if new_status != current_status:
            # set_flag 是 API 路由处理函数，建议内部调用专用函数
            await _internal_set_flag("floating_reminder", new_status)
            
        await asyncio.sleep(60)

async def _internal_set_flag(name: str, enabled: bool):
    """
    内部函数：设置 flag 并通知所有客户端。
    避免直接调用 API 路由逻辑。
    """
    from ..websocket.manager import notify_all_clients
    from sqlalchemy.exc import IntegrityError

    async with AsyncSessionLocal() as session:
        existing = await session.get(FeatureFlag, name)
        if existing:
            existing.enabled = enabled
        else:
            new_flag = FeatureFlag(name=name, enabled=enabled)
            session.add(new_flag)
        try:
            await session.commit()
        except IntegrityError:
            await session.rollback()
            print(f"Database error while setting flag {name}")
            return
    
    await notify_all_clients(name, enabled)
    print(f"Flag '{name}' set to {enabled} and clients notified.")
