import os
import asyncio
import xml.etree.ElementTree as ET
from datetime import datetime

from ..database import odbc_client

# xml_data 目录路径
XML_FOLDER = os.path.abspath(os.path.join(os.path.dirname(__file__), "../../xml_data"))

async def process_xml_loop():
    """
    每隔 10 秒检查 XML 目录，处理新数据并写入 MDB。
    更改12 odbc按键处理，@client_fixed.py @/server - 增加平台支持检查
    """
    while True:
        try:
            # 更改12 odbc按键处理，@client_fixed.py @/server - 检查ODBC平台支持
            status = odbc_client.check_connection_status()
            if not status["platform_supported"]:
                print("ODBC功能不支持当前平台，XML处理器将跳过MDB写入操作")
                await asyncio.sleep(60)  # 在不支持的平台上延长等待时间
                continue
            
            # 确保目录存在后再列举
            if not os.path.isdir(XML_FOLDER):
                await asyncio.sleep(10)
                continue
            for filename in os.listdir(XML_FOLDER):
                if not filename.lower().endswith(".xml"):
                    continue
                xml_path = os.path.join(XML_FOLDER, filename)
                await process_single_xml(xml_path)
        except Exception as e:
            print(f"XML processor loop error: {e}")
        await asyncio.sleep(10)  # 每10秒检查一次

async def process_single_xml(xml_path: str):
    """
    处理单个 XML 文件的 <Entry> 写入 MDB，并移入 <Processed> 节点
    更改12 odbc按键处理，@client_fixed.py @/server - 增加ODBC连接检查
    """
    try:
        tree = ET.parse(xml_path)
        root = tree.getroot()
    except Exception as e:
        print(f"Failed to parse {xml_path}: {e}")
        return

    entries = root.findall("Entry")
    if not entries:
        return  # 没有需要处理的条目

    # 更改12 odbc按键处理，@client_fixed.py @/server - 检查ODBC连接状态
    status = odbc_client.check_connection_status()
    if not status["platform_supported"]:
        print(f"跳过 {xml_path} 的MDB写入操作: 平台不支持ODBC")
        return
    
    if not status["is_connected"]:
        print(f"跳过 {xml_path} 的MDB写入操作: ODBC未连接")
        return

    processed_entries = []
    # 遍历所有 <Entry>，尝试写入到 MDB
    for entry in entries:
        record = {}
        for field in entry.findall("Field"):
            key = field.attrib.get("key")
            val = field.text or ""
            record[key] = val

        # TODO: 需要按 MDB 表结构生成对应的 INSERT 语句
        # 假设MDB 中有表 ClientData(ID, ClientID, DataJSON, CreatedAt)
        client_id = os.path.splitext(os.path.basename(xml_path))[0]
        data_xml_str = ET.tostring(entry, encoding="unicode", method="xml")
        created_at = datetime.utcnow().strftime("%Y-%m-%d %H:%M:%S")

        # 写入 MDB 的 ClientData 表 (ClientID TEXT, DataXML MEMO, CreatedAt DATETIME)
        # 注意：这里存在 SQL 注入风险，仅用于演示
        sql = "INSERT INTO ClientData (ClientID, DataXML, CreatedAt) VALUES ('" + client_id.replace("'", "''") + "', '" + data_xml_str.replace("'", "''") + "', #" + created_at + "#)"

        try:
            odbc_client.execute_non_query(sql)
            processed_entries.append(entry)
        except Exception as e:
            print(f"Failed to write to MDB for {xml_path}: {e}")
            # 写入失败的条目保留在原位置，等待下次处理
            continue

    # 将处理完的条目移动到已处理区域
    if processed_entries:
        processed_root = root.find("Processed")
        if processed_root is None:
            processed_root = ET.SubElement(root, "Processed")

        for entry in processed_entries:
            # 注意：必须先从原父节点移除，再添加到新的父节点
            root.remove(entry)
            processed_root.append(entry)

        # 保存 XML 文件
        tree.write(xml_path, encoding="utf-8", xml_declaration=True)
