from typing import List
from fastapi import WebSocket

active_connections: List[WebSocket] = []

async def connect(ws: WebSocket):
    await ws.accept()
    active_connections.append(ws)

async def disconnect(ws: WebSocket):
    try:
        active_connections.remove(ws)
    except ValueError:
        pass

async def notify_all_clients(name: str, enabled: bool):
    """
    向所有连接的客户端发送消息：{ "name": <feature_name>, "enabled": <true/false> }
    """
    payload = {"name": name, "enabled": enabled}
    # Create a copy of the list for iteration, as disconnect() might modify the list during the loop.
    for ws in active_connections[:]:
        try:
            await ws.send_json(payload)
        except Exception:
            # If sending fails, the client is likely disconnected.
            await disconnect(ws)
