from fastapi import APIRouter, WebSocket, WebSocketDisconnect, Query, HTTPException
from ..auth.jwt_auth import SECRET_KEY, ALGORITHM
from .manager import connect, disconnect
from jose import JWTError, jwt

router = APIRouter(prefix="/ws")

async def verify_token_from_query(token: str):
    """
    适配 HTTP Bearer 令牌验证逻辑用于 WebSocket 查询参数。
    """
    try:
        payload = jwt.decode(token, SECRET_KEY, algorithms=[ALGORITHM])
        sub = payload.get("sub")
        if sub is None:
            raise HTTPException(status_code=403, detail="Invalid token content")
        return sub
    except JWTError:
        # FastAPI的HTTPException是为HTTP路由设计的。对于WebSocket，我们使用代码关闭连接。
        # 这个函数在连接接受之前调用，所以我们不能在这里关闭它。
        # 让异常继续传播。
        raise ValueError("Invalid or expired token.")


@router.websocket("/feature_flags")
async def websocket_feature_flags(ws: WebSocket, token: str = Query(...)):
    """
    需要在查询中提供 JWT token: ws://<host>/ws/feature_flags?token=<JWT>
    """
    try:
        user_sub = await verify_token_from_query(token)
        if not user_sub:
            await ws.close(code=4001) # 使用自定义代码表示认证失败
            return
    except ValueError as e:
        await ws.close(code=4001, reason=str(e))
        return

    await connect(ws)
    print(f"WebSocket connection established for user: {user_sub}")
    try:
        while True:
            # 可以在这里添加处理客户端消息的逻辑
            # 现在只是保持连接活动
            await ws.receive_text()
    except WebSocketDisconnect:
        print(f"WebSocket connection closed for user: {user_sub}")
        await disconnect(ws)
