# PostgreSQL数据库配置，用于存储用户和配置数据
# 250618.18：39加入注册 - 可根据实际环境调整数据库地址
# 25/06/25 17：00 更改postgres 16到17，端口从5435改为5432
DB_HOST = "************"  # 数据库主机地址 (可改为 "localhost" 或其他地址)
DB_PORT = 5432           # 数据库端口 (25/06/25 17：00 从5435改为5432，postgres17.5标准端口)
DB_NAME = "postgres"     # 数据库名称
DB_USER = "postgres"     # 数据库用户名
DB_PASS = "pojiami0602"  # 数据库密码

# 构建数据库连接URL
DATABASE_URL = f"postgresql+asyncpg://{DB_USER}:{DB_PASS}@{DB_HOST}:{DB_PORT}/{DB_NAME}"

# IMDB数据库配置，用于存储add25/change25/del25表数据
# 25/06/25 17：00 更改postgres 16到17，端口从5435改为5432
IMDB_HOST = "************"  # IMDB数据库主机地址
IMDB_PORT = 5432           # IMDB数据库端口 (25/06/25 17：00 更新端口)
IMDB_NAME = "imdb"          # IMDB数据库名称
IMDB_USER = "postgres"      # IMDB数据库用户名
IMDB_PASS = "pojiami0602"   # IMDB数据库密码

# 构建IMDB数据库连接URL
IMDB_DATABASE_URL = f"postgresql+asyncpg://{IMDB_USER}:{IMDB_PASS}@{IMDB_HOST}:{IMDB_PORT}/{IMDB_NAME}"

# MongoDB配置，用于存储传感器数据 - 注释42
MONGO_HOST = "************"       # MongoDB主机地址
MONGO_PORT = 27017             # MongoDB端口
MONGO_DB = "sensor_db"         # MongoDB数据库名称
MONGO_COLLECTION = "sensor_data"  # MongoDB集合名称

# Redis配置，用于缓存和实时数据存储 - 注释42
REDIS_HOST = "localhost"       # Redis主机地址（本地）
REDIS_PORT = 6379              # Redis端口
REDIS_DB = 0                   # Redis使用的数据库编号

# 250618.18：53 硬件指纹删除功能 - 管理员删除密码
ADMIN_DELETE_PASSWORD = "admin123"  # 删除硬件指纹时需要的管理员密码 

# 2025/07/03 + 15:40 + 表格配置
TABLE_CONFIG = {
    'entries': {
        'table_name': 'entries',
        'columns': [
            'id', 'employee_id', 'date', 'department_code', 'project_code', 
            'task_code', 'start_time', 'end_time', 'break_time', 'work_time',
            'overtime', 'comment', 'source', 'created_at', 'updated_at'
        ],
        'date_column': 'date',
        'employee_column': 'employee_id'
    },
    'timeprotab': {
        'table_name': 'timeprotab',
        'columns': [
            'id', 'employee_id', '日付', '星期', 'ｶﾚﾝﾀﾞ', '不在', '勤務区分',
            '事由', '出勤時刻', 'ＭＣ_出勤', '退勤時刻', 'ＭＣ_退勤', '所定時間',
            '早出残業', '内深夜残業', '遅刻早退', '休出時間', '出張残業',
            '外出時間', '戻り時間', 'コメント', 'created_at', 'updated_at'
        ],
        'date_column': '日付',
        'employee_column': 'employee_id',
        'is_partitioned': True,
        'partition_format': 'timeprotab_{month_code}'
    }
}