# 数据库配置说明

## PostgreSQL 配置

### 1. 配置文件位置
数据库配置位于 `server/app/config.py` 文件中。

### 2. 常见配置选项

#### 本地PostgreSQL (推荐用于开发)
```python
DB_HOST = "localhost"  # 本地数据库
DB_PORT = 5432         # PostgreSQL标准端口
DB_NAME = "postgres"   # 数据库名
DB_USER = "postgres"   # 用户名
DB_PASS = "123456"     # 密码
```

#### 远程PostgreSQL
```python
DB_HOST = "************"  # 远程数据库IP
DB_PORT = 5432            # 自定义端口
DB_NAME = "postgres"      # 数据库名
DB_USER = "postgres"      # 用户名
DB_PASS = "123456"        # 密码
```

### 3. 常见问题解决

#### 问题1: 连接被拒绝 (Connection refused)
- **原因**: PostgreSQL服务未启动或端口错误
- **解决**: 检查PostgreSQL服务状态，确认端口号

#### 问题2: 认证失败 (Authentication failed)
- **原因**: 用户名或密码错误
- **解决**: 检查用户名和密码配置

#### 问题3: 数据库不存在 (Database does not exist)
- **原因**: 指定的数据库名不存在
- **解决**: 创建数据库或使用已存在的数据库名

#### 问题4: 主机无法访问 (Host unreachable)
- **原因**: IP地址错误或网络问题
- **解决**: 检查IP地址，确保网络连通性

### 4. 测试数据库连接

#### 方法1: 使用psql命令行工具
```bash
psql -h localhost -p 5432 -U postgres -d postgres
```

#### 方法2: 使用Python测试脚本
```bash
cd server
python -c "
from app.databases.postgresql.client import test_db_connection
import asyncio
result = asyncio.run(test_db_connection())
print('连接测试结果:', '成功' if result else '失败')
"
```

### 5. 硬件指纹注册功能

新增的硬件指纹注册功能需要PostgreSQL数据库支持：

1. **自动建表**: 系统会自动创建 `hardware_registrations` 表
2. **数据存储**: 存储员工硬件指纹信息
3. **验证功能**: 登录时进行硬件指纹验证

### 6. 配置修改步骤

1. 编辑 `server/app/config.py` 文件
2. 修改 `DB_HOST`, `DB_PORT` 等参数
3. 重启服务器
4. 测试连接是否成功

### 7. 环境变量覆盖 (可选)

可以通过环境变量覆盖配置：
```bash
export DB_HOST="your_database_host"
export DB_PORT="5432"
export DB_USER="your_username"
export DB_PASS="your_password"
```

然后在 `config.py` 中使用：
```python
import os
DB_HOST = os.getenv("DB_HOST", "localhost")
DB_PORT = int(os.getenv("DB_PORT", "5432"))
DB_USER = os.getenv("DB_USER", "postgres")
DB_PASS = os.getenv("DB_PASS", "123456")
``` 