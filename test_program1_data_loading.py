#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试program1.py的数据加载功能
验证数据库连接和数据加载是否正常
"""

import sys
import asyncio
import asyncpg
from pathlib import Path
from datetime import datetime

def test_database_connection():
    """测试数据库连接"""
    print("=== 测试数据库连接 ===")
    
    try:
        # 添加server/app路径
        config_path = Path(__file__).parent / "server" / "app"
        sys.path.append(str(config_path))
        
        # 导入配置
        from config import IMDB_DATABASE_URL, TABLE_CONFIG
        
        print(f"✅ 成功导入server配置")
        print(f"IMDB数据库URL: {IMDB_DATABASE_URL}")
        
        return True
        
    except Exception as e:
        print(f"❌ 导入server配置失败: {e}")
        return False

async def test_entries_data_loading():
    """测试entries数据加载"""
    print("\n=== 测试entries数据加载 ===")
    
    try:
        # 添加server/app路径
        config_path = Path(__file__).parent / "server" / "app"
        sys.path.append(str(config_path))
        
        # 导入配置
        from config import IMDB_DATABASE_URL
        
        # 连接数据库
        conn = await asyncpg.connect(IMDB_DATABASE_URL)
        
        try:
            # 测试查询entries数据
            employee_id = "215829"
            target_month = datetime.now()
            
            # 计算该月的开始和结束日期
            start_date = target_month.replace(day=1).date()
            if target_month.month == 12:
                next_month = target_month.replace(year=target_month.year + 1, month=1, day=1)
            else:
                next_month = target_month.replace(month=target_month.month + 1, day=1)
            end_date = (next_month - timedelta(days=1)).date()
            
            # 查询entries数据
            query = """
                SELECT 
                    id as db_id,
                    employee_id,
                    entry_date,
                    department,
                    model,
                    number,
                    factory_number,
                    project_number,
                    unit_number,
                    category,
                    item,
                    duration,
                    source,
                    ts
                FROM entries
                WHERE employee_id = $1 
                  AND entry_date >= $2 
                  AND entry_date <= $3
                ORDER BY entry_date DESC, ts DESC
                LIMIT 10
            """
            
            rows = await conn.fetch(query, employee_id, start_date, end_date)
            
            print(f"✅ 成功查询到 {len(rows)} 条entries记录")
            
            if rows:
                print("前5条记录:")
                for i, row in enumerate(rows[:5]):
                    print(f"  {i+1}. ID:{row['db_id']}, 日期:{row['entry_date']}, 部门:{row['department']}, 项目:{row['model']}")
            else:
                print("⚠️ 当前月份没有entries数据")
            
            # 测试查询所有entries数据
            all_query = "SELECT COUNT(*) FROM entries WHERE employee_id = $1"
            total_count = await conn.fetchval(all_query, employee_id)
            print(f"✅ 员工 {employee_id} 总共有 {total_count} 条entries记录")
            
        finally:
            await conn.close()
            
        return True
        
    except Exception as e:
        print(f"❌ entries数据加载测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

async def test_timeprotab_data_loading():
    """测试timeprotab数据加载"""
    print("\n=== 测试timeprotab数据加载 ===")
    
    try:
        # 添加server/app路径
        config_path = Path(__file__).parent / "server" / "app"
        sys.path.append(str(config_path))
        
        # 导入配置
        from config import DATABASE_URL, TABLE_CONFIG
        
        # 连接数据库
        conn = await asyncpg.connect(DATABASE_URL)
        
        try:
            # 测试查询timeprotab分区表
            partitions = await conn.fetch("""
                SELECT table_name 
                FROM information_schema.tables 
                WHERE table_name LIKE 'timeprotab_%' 
                  AND table_schema = 'public'
                ORDER BY table_name
            """)
            
            print(f"✅ 找到 {len(partitions)} 个timeprotab分区表:")
            for partition in partitions:
                print(f"  - {partition['table_name']}")
            
            if partitions:
                # 测试查询最新的分区数据
                latest_partition = partitions[0]['table_name']
                print(f"测试查询分区: {latest_partition}")
                
                # 查询分区数据
                partition_query = f"""
                    SELECT COUNT(*) 
                    FROM {latest_partition}
                    WHERE employee_id = $1
                """
                
                count = await conn.fetchval(partition_query, "215829")
                print(f"✅ 分区 {latest_partition} 中员工215829有 {count} 条记录")
                
                if count > 0:
                    # 查询具体数据
                    data_query = f"""
                        SELECT 日付, 出勤時刻, 退勤時刻, 勤務区分
                        FROM {latest_partition}
                        WHERE employee_id = $1
                        ORDER BY 日付
                        LIMIT 5
                    """
                    
                    rows = await conn.fetch(data_query, "215829")
                    print("前5条考勤记录:")
                    for i, row in enumerate(rows):
                        print(f"  {i+1}. 日期:{row['日付']}, 出勤:{row['出勤時刻']}, 退勤:{row['退勤時刻']}")
            else:
                print("⚠️ 没有找到timeprotab分区表")
            
        finally:
            await conn.close()
            
        return True
        
    except Exception as e:
        print(f"❌ timeprotab数据加载测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

async def main():
    """主测试函数"""
    print("开始测试program1.py的数据加载功能...")
    
    # 测试配置导入
    config_ok = test_database_connection()
    if not config_ok:
        print("❌ 配置导入失败，停止测试")
        return
    
    # 测试entries数据加载
    entries_ok = await test_entries_data_loading()
    
    # 测试timeprotab数据加载
    timeprotab_ok = await test_timeprotab_data_loading()
    
    if entries_ok and timeprotab_ok:
        print("\n🎉 所有数据加载测试通过！")
    else:
        print("\n❌ 部分数据加载测试失败")

if __name__ == "__main__":
    from datetime import timedelta
    asyncio.run(main()) 