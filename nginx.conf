server {
    listen 80;
    server_name localhost; # 例如: localhost 或 *************

    # 将所有 HTTP 请求重定向到 HTTPS (如果你的FastAPI应用是HTTPS)
    # 如果你只测试HTTP代理，可以暂时注释掉或删除这一段
    return 301 https://$host$request_uri;
}

server {
    listen 443 ssl;
    server_name localhost; # 例如: localhost 或 *************

    # === SSL 配置 (根据你的证书实际路径进行修改) ===
    # 如果你在本地测试，使用FastAPI生成的cert.pem和key.pem
    # 假设你的 FastAPi 项目在 /home/<USER>/my_suite_server/server 目录下
    # 那么证书可能在 /home/<USER>/my_suite_server/server/config/cert.pem
    
    ssl_certificate      /home/<USER>/Documents/testF/2025/ser25/server0613/MySuiteDatebase2402newuigood4/server/config2/cert.pem;
    ssl_certificate_key  /home/<USER>/Documents/testF/2025/ser25/server0613/MySuiteDatebase2402newuigood4/server/config2/key.pem;

    # 生产环境推荐的SSL配置
    ssl_protocols TLSv1.2 TLSv1.3;
    ssl_ciphers "ECDHE+AESGCM:ECDHE+CHACHA20:DHE+AESGCM:DHE+CHACHA20:!aNULL:!MD5:!DSS";
    ssl_prefer_server_ciphers on;
    ssl_session_cache shared:SSL:10m;
    ssl_session_timeout 1h;
    ssl_stapling on;
    ssl_stapling_verify on;
    add_header Strict-Transport-Security "max-age=31536000; includeSubDomains" always;

    # === 反向代理配置 ===
    location / {
        proxy_pass http://127.0.0.1:8003; # FastAPI Uvicorn 监听的内部地址和端口
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;

        # WebSocket 代理配置
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection "upgrade";
    }

    # === 认证微服务 (server3) 反向代理 ===
    # 将所有 /auth/ 开头的请求转发到认证服务
    location /auth/ {
        proxy_pass http://127.0.0.1:8006/; # 注意结尾的斜杠，会移除 /auth 前缀
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
    }

    # === 数据同步微服务 (server5) 反向代理 ===
    # 20250626.s5，6 - 进行客户端的修改，添加Server5路由
    # 将所有 /sync/ 开头的请求转发到数据同步服务
    location /sync/ {
        proxy_pass http://127.0.0.1:8009/; # Server5监听端口8009
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        
        # 增加超时时间，因为数据同步操作可能较慢
        proxy_read_timeout 300s;
        proxy_connect_timeout 30s;
        proxy_send_timeout 30s;
    }

    # === 静态文件服务 (可选) ===
    # 假设你的静态文件在 FastAPI 项目根目录下的 'static' 文件夹
    # 例如：/home/<USER>/my_suite_server/server/static
    # 如果没有静态文件或不需要Nginx提供，可以删除这段
#    location /static/ {
#        alias /home/<USER>/my_suite_server/server/static/; # 指向你的静态文件实际路径
#        expires 30d;
#        add_header Cache-Control "public";
#    }
}