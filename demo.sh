#!/bin/bash
# MySuite 微服务演示脚本
# 展示完整的启动和测试流程

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
CYAN='\033[0;36m'
NC='\033[0m'

print_step() {
    echo
    echo -e "${CYAN}=== $1 ===${NC}"
    echo
}

print_info() {
    echo -e "${BLUE}ℹ️  $1${NC}"
}

print_success() {
    echo -e "${GREEN}✅ $1${NC}"
}

print_warning() {
    echo -e "${YELLOW}⚠️  $1${NC}"
}

print_error() {
    echo -e "${RED}❌ $1${NC}"
}

# 检查依赖
check_dependencies() {
    print_step "检查系统依赖"
    
    # 检查Python
    if command -v python >/dev/null 2>&1; then
        python_version=$(python --version 2>&1)
        print_success "Python 已安装: $python_version"
    else
        print_error "Python 未安装"
        exit 1
    fi
    
    # 检查curl
    if command -v curl >/dev/null 2>&1; then
        print_success "curl 已安装"
    else
        print_warning "curl 未安装，某些测试可能无法运行"
    fi
    
    # 检查lsof
    if command -v lsof >/dev/null 2>&1; then
        print_success "lsof 已安装"
    else
        print_warning "lsof 未安装，端口检查功能可能受限"
    fi
}

# 显示项目结构
show_project_structure() {
    print_step "项目结构概览"
    
    echo "📁 MySuite 微服务项目"
    echo "├── 🖥️  server/          (主服务 - 端口 8003)"
    echo "│   ├── app/            (应用代码)"
    echo "│   ├── config/         (配置文件)"
    echo "│   └── environment.yml (依赖配置)"
    echo "├── 💬 server2/         (聊天微服务 - 端口 8005)"
    echo "│   ├── app/            (应用代码)"
    echo "│   ├── requirements.txt(依赖配置)"
    echo "│   └── start_chat_service.py"
    echo "├── 🚀 start_microservices.sh (启动脚本)"
    echo "├── 🧪 test_microservices.py  (测试脚本)"
    echo "├── 📋 test_requirements.txt  (测试依赖)"
    echo "└── 📖 MICROSERVICES_README.md"
}

# 演示启动脚本功能
demo_startup_script() {
    print_step "演示启动脚本功能"
    
    print_info "1. 显示帮助信息"
    ./start_microservices.sh help
    
    echo
    print_info "2. 检查当前服务状态"
    ./start_microservices.sh status
    
    echo
    print_info "3. 启动脚本的主要命令："
    echo "   ./start_microservices.sh start    # 启动所有服务"
    echo "   ./start_microservices.sh stop     # 停止所有服务"
    echo "   ./start_microservices.sh restart  # 重启所有服务"
    echo "   ./start_microservices.sh status   # 查看状态"
    echo "   ./start_microservices.sh test     # 运行测试"
    echo "   ./start_microservices.sh logs     # 查看日志"
}

# 演示测试脚本
demo_test_script() {
    print_step "演示测试脚本"
    
    print_info "Python测试脚本提供以下功能："
    echo "   python test_microservices.py                    # 综合测试"
    echo "   python test_microservices.py --service main     # 测试主服务"
    echo "   python test_microservices.py --service chat     # 测试聊天服务"
    
    echo
    print_info "检查测试脚本依赖..."
    if python -c "import aiohttp, websockets, colorama" 2>/dev/null; then
        print_success "测试脚本依赖已安装"
    else
        print_warning "测试脚本依赖未完全安装"
        echo "   运行以下命令安装: pip install -r test_requirements.txt"
    fi
}

# 显示服务信息
show_service_info() {
    print_step "服务信息"
    
    echo "🖥️  主服务 (端口 8003):"
    echo "   🌐 服务地址: http://localhost:8003"
    echo "   📚 API文档: http://localhost:8003/docs"
    echo "   🔍 健康检查: http://localhost:8003/health"
    echo "   🔧 主要功能: 数据库操作、文件处理、任务管理、用户认证"
    echo
    
    echo "💬 聊天微服务 (端口 8005):"
    echo "   🌐 服务地址: http://localhost:8005"
    echo "   📚 API文档: http://localhost:8005/docs"
    echo "   🔍 健康检查: http://localhost:8005/health"
    echo "   💬 WebSocket: ws://localhost:8005/ws/chat"
    echo "   🔧 主要功能: 实时聊天、私人消息、文件共享、消息加密"
}

# 演示手动测试
demo_manual_testing() {
    print_step "手动测试示例"
    
    print_info "如果服务正在运行，您可以使用以下命令进行测试："
    echo
    echo "# 使用curl测试服务"
    echo "curl http://localhost:8003/                    # 主服务根路径"
    echo "curl http://localhost:8003/health              # 主服务健康检查"
    echo "curl http://localhost:8005/                    # 聊天服务根路径"
    echo "curl http://localhost:8005/health              # 聊天服务健康检查"
    echo
    echo "# 在浏览器中访问"
    echo "http://localhost:8003/docs                     # 主服务API文档"
    echo "http://localhost:8005/docs                     # 聊天服务API文档"
}

# 显示日志和调试信息
show_logging_info() {
    print_step "日志和调试"
    
    print_info "日志文件位置："
    echo "   logs/main_service.log     # 主服务日志"
    echo "   logs/chat_service.log     # 聊天服务日志"
    echo
    
    print_info "查看日志的方法："
    echo "   tail -f logs/main_service.log              # 实时查看主服务日志"
    echo "   tail -f logs/chat_service.log              # 实时查看聊天服务日志"
    echo "   ./start_microservices.sh logs              # 使用启动脚本查看日志"
    echo
    
    print_info "调试模式启动："
    echo "   # 主服务调试模式"
    echo "   cd server && python -m uvicorn app.main:app --host 0.0.0.0 --port 8003 --reload --log-level debug"
    echo "   # 聊天服务调试模式"
    echo "   cd server2 && python -m uvicorn app.main:app --host 0.0.0.0 --port 8005 --reload --log-level debug"
}

# 显示故障排除信息
show_troubleshooting() {
    print_step "故障排除"
    
    print_info "常见问题及解决方案："
    echo
    echo "1. 端口被占用："
    echo "   lsof -i :8003                              # 检查8003端口"
    echo "   lsof -i :8005                              # 检查8005端口"
    echo "   pkill -f 'uvicorn.*main:app'               # 停止所有uvicorn进程"
    echo
    echo "2. 依赖问题："
    echo "   cd server && conda env create -f environment.yml    # 主服务依赖"
    echo "   cd server2 && pip install -r requirements.txt      # 聊天服务依赖"
    echo "   pip install -r test_requirements.txt               # 测试脚本依赖"
    echo
    echo "3. 数据库连接问题："
    echo "   # 检查PostgreSQL服务状态"
    echo "   # 验证数据库配置文件"
    echo "   # 检查防火墙设置"
    echo
    echo "4. Redis连接问题："
    echo "   # 检查Redis服务状态"
    echo "   # 验证Redis配置"
}

# 主演示流程
main() {
    echo -e "${CYAN}"
    echo "████████████████████████████████████████████████████████████"
    echo "█                                                          █"
    echo "█          MySuite 微服务启动和测试演示                    █"
    echo "█                                                          █"
    echo "████████████████████████████████████████████████████████████"
    echo -e "${NC}"
    
    check_dependencies
    show_project_structure
    demo_startup_script
    demo_test_script
    show_service_info
    demo_manual_testing
    show_logging_info
    show_troubleshooting
    
    print_step "演示完成"
    print_success "您现在可以开始使用 MySuite 微服务了！"
    echo
    print_info "快速开始："
    echo "1. 启动服务: ./start_microservices.sh start"
    echo "2. 运行测试: python test_microservices.py"
    echo "3. 查看文档: cat MICROSERVICES_README.md"
    echo
    print_info "获取帮助："
    echo "./start_microservices.sh help"
    echo "python test_microservices.py --help"
}

# 运行演示
main "$@" 