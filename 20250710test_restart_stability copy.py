#!/usr/bin/env python3
"""
测试客户端重启稳定性
验证HTTP连接管理修复是否有效
"""

import subprocess
import time
import os
import sys
import signal
import requests
from pathlib import Path

# 测试配置
SERVER5_PORT = 8009
SERVER5_URL = f"http://localhost:{SERVER5_PORT}"
CLIENT_PROGRAM = "client/program1.py"
TEST_EMPLOYEE_ID = "test_employee"
TEST_EMPLOYEE_NAME = "Test Employee"
TEST_TOKEN = "test_token"

class RestartStabilityTest:
    def __init__(self):
        self.server5_process = None
        self.client_processes = []
        self.test_results = []
        
    def check_server5_running(self):
        """检查Server5是否正在运行"""
        try:
            response = requests.get(f"{SERVER5_URL}/health", timeout=5)
            return response.status_code == 200
        except:
            return False
    
    def start_server5(self):
        """启动Server5"""
        print("🚀 启动Server5...")
        
        # 先检查是否已经在运行
        if self.check_server5_running():
            print("✅ Server5已经在运行")
            return True
        
        # 启动Server5
        server5_script = Path("server5/start_server5_with_api.py")
        if not server5_script.exists():
            print(f"❌ Server5启动脚本不存在: {server5_script}")
            return False
        
        try:
            self.server5_process = subprocess.Popen(
                [sys.executable, str(server5_script)],
                stdout=subprocess.PIPE,
                stderr=subprocess.PIPE,
                cwd=str(server5_script.parent)
            )
            
            # 等待Server5启动
            for i in range(30):
                if self.check_server5_running():
                    print("✅ Server5启动成功")
                    return True
                time.sleep(1)
                print(f"⏳ 等待Server5启动... ({i+1}/30)")
            
            print("❌ Server5启动超时")
            return False
            
        except Exception as e:
            print(f"❌ 启动Server5失败: {e}")
            return False
    
    def stop_server5(self):
        """停止Server5"""
        if self.server5_process:
            print("🛑 停止Server5...")
            try:
                self.server5_process.terminate()
                self.server5_process.wait(timeout=10)
                print("✅ Server5已停止")
            except subprocess.TimeoutExpired:
                print("⚠️ Server5未响应，强制杀死...")
                self.server5_process.kill()
                self.server5_process.wait()
            except Exception as e:
                print(f"❌ 停止Server5时出错: {e}")
    
    def test_client_restart(self, iteration):
        """测试单次客户端重启"""
        print(f"\n🔄 测试第{iteration}次客户端重启...")
        
        # 检查Server5状态
        if not self.check_server5_running():
            print("❌ Server5未运行")
            return False
        
        # 启动客户端
        client_script = Path(CLIENT_PROGRAM)
        if not client_script.exists():
            print(f"❌ 客户端程序不存在: {client_script}")
            return False
        
        print("📱 启动客户端...")
        start_time = time.time()
        
        try:
            client_process = subprocess.Popen(
                [sys.executable, str(client_script), TEST_TOKEN, TEST_EMPLOYEE_ID, TEST_EMPLOYEE_NAME],
                stdout=subprocess.PIPE,
                stderr=subprocess.PIPE,
                cwd=str(client_script.parent)
            )
            
            # 等待客户端启动（3秒）
            time.sleep(3)
            
            # 检查客户端是否正常运行
            if client_process.poll() is None:
                startup_time = time.time() - start_time
                print(f"✅ 客户端启动成功，耗时: {startup_time:.2f}秒")
                
                # 让客户端运行一段时间
                time.sleep(2)
                
                # 关闭客户端
                print("🔄 关闭客户端...")
                client_process.terminate()
                
                # 等待客户端关闭
                try:
                    client_process.wait(timeout=10)
                    print("✅ 客户端正常关闭")
                    return True
                except subprocess.TimeoutExpired:
                    print("⚠️ 客户端未正常关闭，强制杀死...")
                    client_process.kill()
                    client_process.wait()
                    return False
                    
            else:
                # 客户端启动失败
                stdout, stderr = client_process.communicate()
                print(f"❌ 客户端启动失败")
                print(f"stdout: {stdout.decode()}")
                print(f"stderr: {stderr.decode()}")
                return False
                
        except Exception as e:
            print(f"❌ 客户端测试失败: {e}")
            return False
    
    def run_stability_test(self, iterations=5):
        """运行稳定性测试"""
        print("🧪 开始客户端重启稳定性测试")
        print(f"📊 测试次数: {iterations}")
        
        # 启动Server5
        if not self.start_server5():
            return False
        
        success_count = 0
        
        try:
            for i in range(1, iterations + 1):
                result = self.test_client_restart(i)
                self.test_results.append({
                    'iteration': i,
                    'success': result,
                    'timestamp': time.time()
                })
                
                if result:
                    success_count += 1
                    print(f"✅ 第{i}次测试成功")
                else:
                    print(f"❌ 第{i}次测试失败")
                
                # 测试间隔
                if i < iterations:
                    print("⏸️ 等待2秒后进行下一次测试...")
                    time.sleep(2)
        
        finally:
            # 清理
            self.stop_server5()
        
        # 输出测试结果
        print(f"\n📊 测试结果统计:")
        print(f"  - 总测试次数: {iterations}")
        print(f"  - 成功次数: {success_count}")
        print(f"  - 失败次数: {iterations - success_count}")
        print(f"  - 成功率: {success_count/iterations*100:.1f}%")
        
        if success_count == iterations:
            print("🎉 所有测试通过！连接管理修复成功！")
            return True
        else:
            print("⚠️ 部分测试失败，可能仍存在连接管理问题")
            return False
    
    def test_port_cleanup(self):
        """测试端口清理效果"""
        print("\n🔧 测试端口清理效果...")
        
        # 检查端口是否被占用
        import socket
        
        def check_port_available(port):
            try:
                with socket.socket(socket.AF_INET, socket.SOCK_STREAM) as s:
                    s.bind(('localhost', port))
                    return True
            except OSError:
                return False
        
        # 启动Server5
        if not self.start_server5():
            return False
        
        print(f"✅ 端口{SERVER5_PORT}被Server5占用（正常）")
        
        # 停止Server5
        self.stop_server5()
        
        # 等待端口释放
        print("⏳ 等待端口释放...")
        time.sleep(3)
        
        # 检查端口是否释放
        if check_port_available(SERVER5_PORT):
            print(f"✅ 端口{SERVER5_PORT}已正确释放")
            return True
        else:
            print(f"❌ 端口{SERVER5_PORT}仍被占用")
            return False

def main():
    """主函数"""
    print("🧪 客户端重启稳定性测试")
    print("=" * 50)
    
    # 检查必要文件
    required_files = [
        "server5/start_server5_with_api.py",
        CLIENT_PROGRAM
    ]
    
    for file_path in required_files:
        if not Path(file_path).exists():
            print(f"❌ 缺少必要文件: {file_path}")
            return False
    
    # 运行测试
    tester = RestartStabilityTest()
    
    try:
        # 1. 测试端口清理
        port_test_result = tester.test_port_cleanup()
        
        # 2. 测试客户端重启稳定性
        stability_test_result = tester.run_stability_test(iterations=3)
        
        # 总结
        print("\n" + "=" * 50)
        print("📊 测试总结:")
        print(f"  - 端口清理测试: {'✅ 通过' if port_test_result else '❌ 失败'}")
        print(f"  - 重启稳定性测试: {'✅ 通过' if stability_test_result else '❌ 失败'}")
        
        if port_test_result and stability_test_result:
            print("\n🎉 所有测试通过！连接管理问题已修复！")
            return True
        else:
            print("\n⚠️ 部分测试失败，请检查修复效果")
            return False
            
    except KeyboardInterrupt:
        print("\n⏹️ 测试被用户中断")
        return False
    except Exception as e:
        print(f"\n❌ 测试过程中出现错误: {e}")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1) 