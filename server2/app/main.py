# server2/app/main.py
# 20250618.20:15 微服务-信息交流 - 独立的信息交流微服务器
# 20250618.20:30 增加加密和文件共享以及数据库 - 扩展服务支持
"""
MySuite 信息交流微服务器
专门处理实时聊天和信息交流功能
端口: 8005
"""

from fastapi import FastAPI, Request
from fastapi.middleware.cors import CORSMiddleware
from fastapi.responses import JSONResponse
import uvicorn
import logging
from contextlib import asynccontextmanager

# 20250618.20:15 微服务-信息交流 - 导入路由模块
from .routers import chat_websocket, chat_management
from .auth import jwt_auth
from .core.services import redis_service, database_service, encryption_service, file_service
from .config import settings

# 20250618.20:15 微服务-信息交流 - 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

@asynccontextmanager
async def lifespan(app: FastAPI):
    """20250618.20:15 微服务-信息交流 - 应用生命周期管理"""
    logger.info("Starting MySuite Chat Microservice...")
    
    # 20250618.20:30 增加加密和文件共享以及数据库 - 初始化数据库连接
    try:
        await database_service.initialize()
        logger.info("Database service initialized successfully")
    except Exception as e:
        logger.error(f"Failed to initialize database: {e}")
        # 数据库连接失败不影响启动，但会记录错误
    
    # 20250618.20:15 微服务-信息交流 - 初始化Redis连接
    try:
        await redis_service.initialize()
        logger.info("Redis service initialized successfully")
    except Exception as e:
        logger.error(f"Failed to initialize Redis: {e}")
    
    # 20250618.20:30 增加加密和文件共享以及数据库 - 初始化加密服务
    try:
        if encryption_service.is_encryption_enabled():
            logger.info("Message encryption service initialized successfully")
        else:
            logger.warning("Message encryption is disabled")
    except Exception as e:
        logger.error(f"Failed to initialize encryption: {e}")
    
    # 20250618.20:30 增加加密和文件共享以及数据库 - 初始化文件服务
    try:
        file_stats = file_service.get_service_stats()
        if file_stats.get("enabled"):
            logger.info(f"File service initialized: {file_stats.get('total_files', 0)} files, "
                       f"{file_stats.get('total_size_mb', 0)}MB")
        else:
            logger.warning("File upload service is disabled")
    except Exception as e:
        logger.error(f"Failed to initialize file service: {e}")
    
    yield
    
    # 20250618.20:15 微服务-信息交流 - 清理资源
    logger.info("Shutting down MySuite Chat Microservice...")
    
    try:
        await database_service.close()
        logger.info("Database service closed successfully")
    except Exception as e:
        logger.error(f"Error closing database: {e}")
    
    try:
        await redis_service.close()
        logger.info("Redis service closed successfully")
    except Exception as e:
        logger.error(f"Error closing Redis: {e}")

# 20250618.20:15 微服务-信息交流 - 创建FastAPI应用
app = FastAPI(
    title="MySuite Chat Microservice",
    description="专门处理实时聊天和信息交流功能的微服务器",
    version="1.0.0",
    docs_url="/docs",
    redoc_url="/redoc",
    lifespan=lifespan
)

# 20250618.20:15 微服务-信息交流 - CORS中间件配置
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],  # 生产环境中应该限制具体域名
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# 20250618.20:15 微服务-信息交流 - 全局异常处理
@app.exception_handler(Exception)
async def global_exception_handler(request: Request, exc: Exception):
    logger.error(f"Global exception: {exc}", exc_info=True)
    return JSONResponse(
        status_code=500,
        content={"detail": "Internal server error", "error": str(exc)}
    )

# 20250618.20:15 微服务-信息交流 - 根路径
@app.get("/")
async def root():
    """20250618.20:15 微服务-信息交流 - 根路径信息"""
    return {
        "service": settings.SERVICE_NAME,
        "version": settings.SERVICE_VERSION,
        "status": "running",
        "port": settings.SERVICE_PORT,
        "features": {
            "chat": True,
            "encryption": encryption_service.is_encryption_enabled(),
            "file_upload": file_service.enabled,
            "database": database_service.connected,
            "redis": redis_service.connected
        },
        "endpoints": {
            "websocket": "/ws/chat",
            "api": "/api/chat",
            "docs": "/docs",
            "health": "/health"
        }
    }

# 20250618.20:15 微服务-信息交流 - 健康检查端点
@app.get("/health")
async def health_check():
    """20250618.20:15 微服务-信息交流 - 健康检查端点"""
    return {
        "status": "healthy",
        "service": settings.SERVICE_NAME,
        "version": settings.SERVICE_VERSION,
        "timestamp": "2025-06-19T12:00:00Z",
        "services": {
            "database": database_service.connected,
            "redis": redis_service.connected,
            "encryption": encryption_service.is_encryption_enabled(),
            "file_upload": file_service.enabled
        }
    }

# 20250618.20:15 微服务-信息交流 - 注册路由
app.include_router(chat_websocket.router, tags=["WebSocket Chat"])
app.include_router(chat_management.router, prefix="/api/chat", tags=["Chat Management"])

# 20250618.20:30 增加加密和文件共享以及数据库 - 添加文件共享路由
from .routers import file_sharing, private_chat, room_management
app.include_router(file_sharing.router, prefix="/api/files", tags=["File Sharing"])
app.include_router(private_chat.router, prefix="/api/private", tags=["Private Chat"])
app.include_router(room_management.router, prefix="/api/rooms", tags=["Room Management"])

# 20250618.20:15 微服务-信息交流 - 启动配置
if __name__ == "__main__":
    uvicorn.run(
        "main:app",
        host="0.0.0.0",
        port=8005,
        reload=True,
        log_level="info",
        ssl_keyfile=None,  # 如需HTTPS，配置SSL证书
        ssl_certfile=None
    ) 