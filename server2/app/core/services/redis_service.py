# server2/app/core/services/redis_service.py
# 20250618.20:15 微服务-信息交流 - Redis服务模块
"""
Redis服务模块 - 用于缓存聊天数据和用户状态
"""

import redis.asyncio as redis
import json
import logging
from typing import Optional, Dict, List, Any
from datetime import datetime, timedelta

from ...config import settings

# 20250618.20:15 微服务-信息交流 - 配置日志
logger = logging.getLogger(__name__)

class RedisService:
    """20250618.20:15 微服务-信息交流 - Redis服务类"""
    
    def __init__(self):
        self.redis_client: Optional[redis.Redis] = None
        self.connected = False
    
    async def initialize(self):
        """20250618.20:15 微服务-信息交流 - 初始化Redis连接"""
        try:
            self.redis_client = redis.Redis(
                host=settings.REDIS_HOST,
                port=settings.REDIS_PORT,
                db=settings.REDIS_DB,
                password=settings.REDIS_PASSWORD,
                decode_responses=settings.REDIS_DECODE_RESPONSES,
                socket_connect_timeout=5,
                socket_timeout=5
            )
            
            # 测试连接
            await self.redis_client.ping()
            self.connected = True
            logger.info(f"Redis connected successfully at {settings.REDIS_HOST}:{settings.REDIS_PORT}")
            
        except Exception as e:
            logger.error(f"Failed to connect to Redis: {e}")
            self.connected = False
            raise e
    
    async def close(self):
        """20250618.20:15 微服务-信息交流 - 关闭Redis连接"""
        if self.redis_client:
            try:
                await self.redis_client.close()
                self.connected = False
                logger.info("Redis connection closed")
            except Exception as e:
                logger.error(f"Error closing Redis connection: {e}")
    
    async def is_connected(self) -> bool:
        """20250618.20:15 微服务-信息交流 - 检查Redis连接状态"""
        if not self.redis_client or not self.connected:
            return False
        
        try:
            await self.redis_client.ping()
            return True
        except Exception:
            self.connected = False
            return False
    
    # 20250618.20:15 微服务-信息交流 - 聊天消息相关方法
    async def save_chat_message(self, message: Dict[str, Any]) -> bool:
        """保存聊天消息到Redis"""
        if not await self.is_connected():
            return False
        
        try:
            message_key = f"chat:message:{message.get('message_id')}"
            message_data = json.dumps(message, ensure_ascii=False)
            
            # 保存消息，设置过期时间（7天）
            await self.redis_client.setex(message_key, 604800, message_data)
            
            # 添加到消息历史列表
            history_key = "chat:history"
            await self.redis_client.lpush(history_key, message_data)
            
            # 保持历史记录数量限制
            await self.redis_client.ltrim(history_key, 0, settings.CHAT_MAX_HISTORY_SIZE - 1)
            
            logger.debug(f"Chat message saved: {message.get('message_id')}")
            return True
            
        except Exception as e:
            logger.error(f"Failed to save chat message: {e}")
            return False
    
    async def get_chat_history(self, limit: int = 20) -> List[Dict[str, Any]]:
        """获取聊天历史记录"""
        if not await self.is_connected():
            return []
        
        try:
            history_key = "chat:history"
            messages_data = await self.redis_client.lrange(history_key, 0, limit - 1)
            
            messages = []
            for msg_data in messages_data:
                try:
                    message = json.loads(msg_data)
                    messages.append(message)
                except json.JSONDecodeError:
                    continue
            
            # 返回时间顺序（最新的在后面）
            return list(reversed(messages))
            
        except Exception as e:
            logger.error(f"Failed to get chat history: {e}")
            return []
    
    # 20250618.20:15 微服务-信息交流 - 用户状态相关方法
    async def save_user_status(self, employee_id: str, status_data: Dict[str, Any]) -> bool:
        """保存用户在线状态"""
        if not await self.is_connected():
            return False
        
        try:
            status_key = f"chat:user:{employee_id}"
            status_json = json.dumps(status_data, ensure_ascii=False, default=str)
            
            # 保存用户状态，设置过期时间（5分钟）
            await self.redis_client.setex(status_key, 300, status_json)
            
            # 添加到在线用户集合
            online_key = "chat:online_users"
            await self.redis_client.sadd(online_key, employee_id)
            await self.redis_client.expire(online_key, 300)
            
            logger.debug(f"User status saved: {employee_id}")
            return True
            
        except Exception as e:
            logger.error(f"Failed to save user status: {e}")
            return False
    
    async def get_user_status(self, employee_id: str) -> Optional[Dict[str, Any]]:
        """获取用户状态"""
        if not await self.is_connected():
            return None
        
        try:
            status_key = f"chat:user:{employee_id}"
            status_data = await self.redis_client.get(status_key)
            
            if status_data:
                return json.loads(status_data)
            return None
            
        except Exception as e:
            logger.error(f"Failed to get user status: {e}")
            return None
    
    async def remove_user_status(self, employee_id: str) -> bool:
        """移除用户状态"""
        if not await self.is_connected():
            return False
        
        try:
            status_key = f"chat:user:{employee_id}"
            online_key = "chat:online_users"
            
            # 删除用户状态和从在线列表移除
            await self.redis_client.delete(status_key)
            await self.redis_client.srem(online_key, employee_id)
            
            logger.debug(f"User status removed: {employee_id}")
            return True
            
        except Exception as e:
            logger.error(f"Failed to remove user status: {e}")
            return False
    
    async def get_online_users(self) -> List[str]:
        """获取在线用户列表"""
        if not await self.is_connected():
            return []
        
        try:
            online_key = "chat:online_users"
            user_ids = await self.redis_client.smembers(online_key)
            return list(user_ids) if user_ids else []
            
        except Exception as e:
            logger.error(f"Failed to get online users: {e}")
            return []
    
    # 20250618.20:15 微服务-信息交流 - 统计相关方法
    async def increment_message_count(self, date: str = None) -> int:
        """增加消息计数"""
        if not await self.is_connected():
            return 0
        
        try:
            if not date:
                date = datetime.now().strftime("%Y-%m-%d")
            
            count_key = f"chat:stats:messages:{date}"
            count = await self.redis_client.incr(count_key)
            
            # 设置过期时间（30天）
            await self.redis_client.expire(count_key, 2592000)
            
            return count
            
        except Exception as e:
            logger.error(f"Failed to increment message count: {e}")
            return 0
    
    async def get_message_count(self, date: str = None) -> int:
        """获取消息计数"""
        if not await self.is_connected():
            return 0
        
        try:
            if not date:
                date = datetime.now().strftime("%Y-%m-%d")
            
            count_key = f"chat:stats:messages:{date}"
            count = await self.redis_client.get(count_key)
            
            return int(count) if count else 0
            
        except Exception as e:
            logger.error(f"Failed to get message count: {e}")
            return 0
    
    async def get_service_stats(self) -> Dict[str, Any]:
        """获取服务统计信息"""
        if not await self.is_connected():
            return {}
        
        try:
            today = datetime.now().strftime("%Y-%m-%d")
            online_users = await self.get_online_users()
            message_count = await self.get_message_count(today)
            
            return {
                "online_users_count": len(online_users),
                "online_users": online_users,
                "messages_today": message_count,
                "redis_connected": True,
                "timestamp": datetime.now().isoformat()
            }
            
        except Exception as e:
            logger.error(f"Failed to get service stats: {e}")
            return {"redis_connected": False, "error": str(e)}

# 20250618.20:15 微服务-信息交流 - 创建全局Redis服务实例
redis_service = RedisService() 