# server2/app/core/services/encryption_service.py
# 20250618.20:30 增加加密和文件共享以及数据库 - 消息加密服务模块
"""
消息加密服务模块 - 提供AES-256-GCM加密和解密功能
"""

from cryptography.hazmat.primitives.ciphers.aead import AESGCM
from cryptography.hazmat.primitives import hashes
from cryptography.hazmat.primitives.kdf.pbkdf2 import PBKDF2HMAC
import os
import base64
import json
import logging
from typing import Optional, Dict, Any, Tuple

from ...config import settings

# 20250618.20:30 增加加密和文件共享以及数据库 - 配置日志
logger = logging.getLogger(__name__)

class MessageEncryptionService:
    """20250618.20:30 增加加密和文件共享以及数据库 - 消息加密服务类"""
    
    def __init__(self):
        self.enabled = settings.ENABLE_MESSAGE_ENCRYPTION
        self.encryption_key = None
        self.aes_gcm = None
        
        if self.enabled:
            self._initialize_encryption()
    
    def _initialize_encryption(self):
        """20250618.20:30 增加加密和文件共享以及数据库 - 初始化加密器"""
        try:
            # 从配置中获取密钥
            master_key = settings.MESSAGE_ENCRYPTION_KEY.encode('utf-8')
            
            # 使用PBKDF2生成32字节的加密密钥
            salt = b"mysuite_chat_salt_2025"  # 固定盐值，生产环境应使用随机盐
            kdf = PBKDF2HMAC(
                algorithm=hashes.SHA256(),
                length=32,
                salt=salt,
                iterations=100000,
            )
            self.encryption_key = kdf.derive(master_key)
            
            # 创建AES-GCM实例
            self.aes_gcm = AESGCM(self.encryption_key)
            
            logger.info("Message encryption initialized successfully")
            
        except Exception as e:
            logger.error(f"Failed to initialize encryption: {e}")
            self.enabled = False
            raise e
    
    def encrypt_message(self, message: str, additional_data: Optional[Dict[str, Any]] = None) -> Optional[Dict[str, str]]:
        """20250618.20:30 增加加密和文件共享以及数据库 - 加密消息"""
        if not self.enabled or not self.aes_gcm:
            return None
        
        try:
            # 生成随机nonce
            nonce = os.urandom(12)  # 96位nonce，适用于GCM
            
            # 准备附加数据
            aad = None
            if additional_data:
                aad = json.dumps(additional_data, sort_keys=True).encode('utf-8')
            
            # 加密消息
            ciphertext = self.aes_gcm.encrypt(nonce, message.encode('utf-8'), aad)
            
            # 返回Base64编码的结果
            return {
                "encrypted_content": base64.b64encode(ciphertext).decode('utf-8'),
                "nonce": base64.b64encode(nonce).decode('utf-8'),
                "aad": base64.b64encode(aad).decode('utf-8') if aad else None
            }
            
        except Exception as e:
            logger.error(f"Failed to encrypt message: {e}")
            return None
    
    def decrypt_message(self, encrypted_data: Dict[str, str]) -> Optional[str]:
        """20250618.20:30 增加加密和文件共享以及数据库 - 解密消息"""
        if not self.enabled or not self.aes_gcm:
            return None
        
        try:
            # 解码Base64数据
            ciphertext = base64.b64decode(encrypted_data["encrypted_content"])
            nonce = base64.b64decode(encrypted_data["nonce"])
            aad = base64.b64decode(encrypted_data["aad"]) if encrypted_data.get("aad") else None
            
            # 解密消息
            plaintext = self.aes_gcm.decrypt(nonce, ciphertext, aad)
            
            return plaintext.decode('utf-8')
            
        except Exception as e:
            logger.error(f"Failed to decrypt message: {e}")
            return None
    
    def encrypt_file_metadata(self, file_info: Dict[str, Any]) -> Optional[Dict[str, str]]:
        """20250618.20:30 增加加密和文件共享以及数据库 - 加密文件元数据"""
        if not self.enabled:
            return None
        
        try:
            file_info_str = json.dumps(file_info, ensure_ascii=False)
            return self.encrypt_message(file_info_str, {"type": "file_metadata"})
            
        except Exception as e:
            logger.error(f"Failed to encrypt file metadata: {e}")
            return None
    
    def decrypt_file_metadata(self, encrypted_data: Dict[str, str]) -> Optional[Dict[str, Any]]:
        """20250618.20:30 增加加密和文件共享以及数据库 - 解密文件元数据"""
        if not self.enabled:
            return None
        
        try:
            decrypted_str = self.decrypt_message(encrypted_data)
            if decrypted_str:
                return json.loads(decrypted_str)
            return None
            
        except Exception as e:
            logger.error(f"Failed to decrypt file metadata: {e}")
            return None
    
    def create_secure_token(self, data: Dict[str, Any], expires_in_minutes: int = 60) -> Optional[str]:
        """20250618.20:30 增加加密和文件共享以及数据库 - 创建安全令牌"""
        if not self.enabled:
            return None
        
        try:
            from datetime import datetime, timedelta
            
            # 添加过期时间
            token_data = data.copy()
            token_data["expires_at"] = (datetime.utcnow() + timedelta(minutes=expires_in_minutes)).isoformat()
            
            # 加密令牌数据
            encrypted = self.encrypt_message(json.dumps(token_data), {"type": "secure_token"})
            if encrypted:
                # 组合所有加密数据为单个令牌
                token_parts = [
                    encrypted["encrypted_content"],
                    encrypted["nonce"],
                    encrypted.get("aad", "")
                ]
                return base64.b64encode("|".join(token_parts).encode('utf-8')).decode('utf-8')
            
            return None
            
        except Exception as e:
            logger.error(f"Failed to create secure token: {e}")
            return None
    
    def verify_secure_token(self, token: str) -> Optional[Dict[str, Any]]:
        """20250618.20:30 增加加密和文件共享以及数据库 - 验证安全令牌"""
        if not self.enabled:
            return None
        
        try:
            from datetime import datetime
            
            # 解码令牌
            token_data = base64.b64decode(token).decode('utf-8')
            parts = token_data.split("|")
            
            if len(parts) != 3:
                return None
            
            # 重构加密数据
            encrypted_data = {
                "encrypted_content": parts[0],
                "nonce": parts[1],
                "aad": parts[2] if parts[2] else None
            }
            
            # 解密令牌
            decrypted_str = self.decrypt_message(encrypted_data)
            if not decrypted_str:
                return None
            
            token_info = json.loads(decrypted_str)
            
            # 检查过期时间
            expires_at = datetime.fromisoformat(token_info.get("expires_at", ""))
            if datetime.utcnow() > expires_at:
                logger.warning("Secure token has expired")
                return None
            
            # 移除过期时间字段
            del token_info["expires_at"]
            return token_info
            
        except Exception as e:
            logger.error(f"Failed to verify secure token: {e}")
            return None
    
    def hash_sensitive_data(self, data: str) -> str:
        """20250618.20:30 增加加密和文件共享以及数据库 - 哈希敏感数据"""
        try:
            import hashlib
            return hashlib.sha256(data.encode('utf-8')).hexdigest()
        except Exception as e:
            logger.error(f"Failed to hash data: {e}")
            return data
    
    def generate_file_access_token(self, file_id: str, user_id: str, expires_in_minutes: int = 60) -> Optional[str]:
        """20250618.20:30 增加加密和文件共享以及数据库 - 生成文件访问令牌"""
        token_data = {
            "file_id": file_id,
            "user_id": user_id,
            "access_type": "download",
            "created_at": datetime.utcnow().isoformat()
        }
        return self.create_secure_token(token_data, expires_in_minutes)
    
    def verify_file_access_token(self, token: str, file_id: str, user_id: str) -> bool:
        """20250618.20:30 增加加密和文件共享以及数据库 - 验证文件访问令牌"""
        try:
            token_data = self.verify_secure_token(token)
            if not token_data:
                return False
            
            return (
                token_data.get("file_id") == file_id and
                token_data.get("user_id") == user_id and
                token_data.get("access_type") == "download"
            )
            
        except Exception as e:
            logger.error(f"Failed to verify file access token: {e}")
            return False
    
    def is_encryption_enabled(self) -> bool:
        """20250618.20:30 增加加密和文件共享以及数据库 - 检查加密是否启用"""
        return self.enabled
    
    def get_encryption_info(self) -> Dict[str, Any]:
        """20250618.20:30 增加加密和文件共享以及数据库 - 获取加密信息"""
        return {
            "enabled": self.enabled,
            "algorithm": settings.ENCRYPTION_ALGORITHM if self.enabled else None,
            "key_length": 256 if self.enabled else None,
            "mode": "GCM" if self.enabled else None
        }

# 20250618.20:30 增加加密和文件共享以及数据库 - 创建全局加密服务实例
encryption_service = MessageEncryptionService() 