# server2/app/core/services/file_service.py
# 20250618.20:30 增加加密和文件共享以及数据库 - 文件共享服务模块
"""
文件共享服务模块 - 提供文件上传、下载、管理等功能
"""

import os
import uuid
import aiofiles
import magic
from pathlib import Path
from typing import Optional, Dict, Any, List, BinaryIO
from datetime import datetime, timedelta
import logging
import hashlib
from PIL import Image
import json

from ...config import settings
from .database_service import database_service, FileShare
from .encryption_service import encryption_service

# 20250618.20:30 增加加密和文件共享以及数据库 - 配置日志
logger = logging.getLogger(__name__)

class FileShareService:
    """20250618.20:30 增加加密和文件共享以及数据库 - 文件共享服务类"""
    
    def __init__(self):
        self.upload_path = Path(settings.FILE_UPLOAD_PATH)
        self.max_file_size = settings.FILE_MAX_SIZE
        self.allowed_extensions = settings.FILE_ALLOWED_EXTENSIONS
        self.enabled = settings.FILE_UPLOAD_ENABLED
        
        # 确保上传目录存在
        if self.enabled:
            self.upload_path.mkdir(parents=True, exist_ok=True)
            
            # 创建子目录
            (self.upload_path / "images").mkdir(exist_ok=True)
            (self.upload_path / "documents").mkdir(exist_ok=True)
            (self.upload_path / "archives").mkdir(exist_ok=True)
            (self.upload_path / "others").mkdir(exist_ok=True)
            (self.upload_path / "thumbnails").mkdir(exist_ok=True)
    
    def _get_file_category(self, extension: str) -> str:
        """20250618.20:30 增加加密和文件共享以及数据库 - 根据扩展名获取文件类别"""
        image_exts = [".jpg", ".jpeg", ".png", ".gif", ".bmp", ".webp"]
        doc_exts = [".pdf", ".doc", ".docx", ".xls", ".xlsx", ".ppt", ".pptx", ".txt", ".md", ".csv", ".json", ".xml"]
        archive_exts = [".zip", ".rar", ".7z", ".tar", ".gz"]
        
        extension = extension.lower()
        if extension in image_exts:
            return "images"
        elif extension in doc_exts:
            return "documents"
        elif extension in archive_exts:
            return "archives"
        else:
            return "others"
    
    def _generate_file_id(self) -> str:
        """20250618.20:30 增加加密和文件共享以及数据库 - 生成文件ID"""
        return f"file_{uuid.uuid4().hex}"
    
    def _generate_stored_filename(self, original_filename: str, file_id: str) -> str:
        """20250618.20:30 增加加密和文件共享以及数据库 - 生成存储文件名"""
        extension = Path(original_filename).suffix
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        return f"{file_id}_{timestamp}{extension}"
    
    async def _calculate_file_hash(self, file_path: Path) -> str:
        """20250618.20:30 增加加密和文件共享以及数据库 - 计算文件哈希"""
        try:
            hash_md5 = hashlib.md5()
            async with aiofiles.open(file_path, 'rb') as f:
                while chunk := await f.read(8192):
                    hash_md5.update(chunk)
            return hash_md5.hexdigest()
        except Exception as e:
            logger.error(f"Error calculating file hash: {e}")
            return ""
    
    async def _create_thumbnail(self, file_path: Path, file_id: str) -> Optional[str]:
        """20250618.20:30 增加加密和文件共享以及数据库 - 创建缩略图"""
        try:
            # 只为图片创建缩略图
            if not self._is_image_file(file_path):
                return None
            
            thumbnail_filename = f"{file_id}_thumb.jpg"
            thumbnail_path = self.upload_path / "thumbnails" / thumbnail_filename
            
            # 使用Pillow创建缩略图
            with Image.open(file_path) as img:
                # 转换为RGB模式（处理RGBA等格式）
                if img.mode in ('RGBA', 'LA', 'P'):
                    rgb_img = Image.new('RGB', img.size, (255, 255, 255))
                    if img.mode == 'P':
                        img = img.convert('RGBA')
                    rgb_img.paste(img, mask=img.split()[-1] if img.mode == 'RGBA' else None)
                    img = rgb_img
                
                # 创建缩略图
                img.thumbnail((200, 200), Image.Resampling.LANCZOS)
                img.save(thumbnail_path, "JPEG", quality=85)
            
            return thumbnail_filename
            
        except Exception as e:
            logger.error(f"Error creating thumbnail: {e}")
            return None
    
    def _is_image_file(self, file_path: Path) -> bool:
        """20250618.20:30 增加加密和文件共享以及数据库 - 检查是否为图片文件"""
        image_exts = [".jpg", ".jpeg", ".png", ".gif", ".bmp", ".webp"]
        return file_path.suffix.lower() in image_exts
    
    def _validate_file(self, filename: str, file_size: int) -> Dict[str, Any]:
        """20250618.20:30 增加加密和文件共享以及数据库 - 验证文件"""
        errors = []
        
        # 检查文件大小
        if file_size > self.max_file_size:
            errors.append(f"文件大小超过限制 ({self.max_file_size / 1024 / 1024:.1f}MB)")
        
        # 检查文件扩展名
        extension = Path(filename).suffix.lower()
        if extension not in self.allowed_extensions:
            errors.append(f"不支持的文件类型: {extension}")
        
        # 检查文件名长度
        if len(filename) > 255:
            errors.append("文件名过长")
        
        return {
            "valid": len(errors) == 0,
            "errors": errors,
            "extension": extension,
            "category": self._get_file_category(extension)
        }
    
    async def upload_file(
        self, 
        file_content: bytes, 
        filename: str, 
        uploader_id: str, 
        uploader_name: str,
        room_id: Optional[str] = None,
        is_private: bool = False
    ) -> Dict[str, Any]:
        """20250618.20:30 增加加密和文件共享以及数据库 - 上传文件"""
        if not self.enabled:
            return {"success": False, "error": "文件上传功能已禁用"}
        
        try:
            # 验证文件
            validation = self._validate_file(filename, len(file_content))
            if not validation["valid"]:
                return {"success": False, "errors": validation["errors"]}
            
            # 生成文件信息
            file_id = self._generate_file_id()
            stored_filename = self._generate_stored_filename(filename, file_id)
            category = validation["category"]
            
            # 确定存储路径
            file_dir = self.upload_path / category
            file_path = file_dir / stored_filename
            
            # 保存文件
            async with aiofiles.open(file_path, 'wb') as f:
                await f.write(file_content)
            
            # 检测MIME类型
            mime_type = magic.from_file(str(file_path), mime=True)
            
            # 计算文件哈希
            file_hash = await self._calculate_file_hash(file_path)
            
            # 创建缩略图
            thumbnail_filename = await self._create_thumbnail(file_path, file_id)
            
            # 准备文件信息
            file_info = {
                "file_id": file_id,
                "original_filename": filename,
                "stored_filename": stored_filename,
                "file_path": str(file_path.relative_to(self.upload_path)),
                "file_size": len(file_content),
                "file_type": validation["extension"],
                "mime_type": mime_type,
                "uploader_id": uploader_id,
                "uploader_name": uploader_name,
                "room_id": room_id,
                "is_private": is_private,
                "file_hash": file_hash,
                "thumbnail": thumbnail_filename,
                "category": category,
                "expires_at": datetime.utcnow() + timedelta(days=settings.FILE_CLEANUP_DAYS)
            }
            
            # 保存到数据库
            async with database_service.get_session() as session:
                file_record = FileShare(
                    file_id=file_id,
                    original_filename=filename,
                    stored_filename=stored_filename,
                    file_path=str(file_path.relative_to(self.upload_path)),
                    file_size=len(file_content),
                    file_type=validation["extension"],
                    mime_type=mime_type,
                    uploader_id=uploader_id,
                    uploader_name=uploader_name,
                    room_id=room_id,
                    is_private=is_private,
                    expires_at=file_info["expires_at"]
                )
                session.add(file_record)
                await session.commit()
            
            # 生成访问令牌
            access_token = encryption_service.generate_file_access_token(
                file_id, uploader_id, expires_in_minutes=settings.FILE_DOWNLOAD_TIMEOUT
            )
            
            logger.info(f"File uploaded successfully: {filename} by {uploader_name}")
            
            return {
                "success": True,
                "file_info": {
                    "file_id": file_id,
                    "filename": filename,
                    "file_size": len(file_content),
                    "file_type": validation["extension"],
                    "mime_type": mime_type,
                    "category": category,
                    "thumbnail": thumbnail_filename,
                    "access_token": access_token,
                    "upload_time": datetime.utcnow().isoformat()
                }
            }
            
        except Exception as e:
            logger.error(f"Error uploading file: {e}")
            return {"success": False, "error": f"上传失败: {str(e)}"}
    
    async def get_file_info(self, file_id: str, user_id: str) -> Optional[Dict[str, Any]]:
        """20250618.20:30 增加加密和文件共享以及数据库 - 获取文件信息"""
        try:
            async with database_service.get_session() as session:
                from sqlalchemy import select
                
                query = select(FileShare).where(
                    FileShare.file_id == file_id,
                    FileShare.is_deleted == False
                )
                result = await session.execute(query)
                file_record = result.scalar()
                
                if not file_record:
                    return None
                
                # 检查访问权限
                if file_record.is_private and file_record.uploader_id != user_id:
                    return None
                
                return {
                    "file_id": file_record.file_id,
                    "original_filename": file_record.original_filename,
                    "file_size": file_record.file_size,
                    "file_type": file_record.file_type,
                    "mime_type": file_record.mime_type,
                    "uploader_id": file_record.uploader_id,
                    "uploader_name": file_record.uploader_name,
                    "room_id": file_record.room_id,
                    "is_private": file_record.is_private,
                    "download_count": file_record.download_count,
                    "created_at": file_record.created_at.isoformat(),
                    "expires_at": file_record.expires_at.isoformat() if file_record.expires_at else None
                }
                
        except Exception as e:
            logger.error(f"Error getting file info: {e}")
            return None
    
    async def download_file(self, file_id: str, user_id: str, access_token: Optional[str] = None) -> Optional[Dict[str, Any]]:
        """20250618.20:30 增加加密和文件共享以及数据库 - 下载文件"""
        try:
            async with database_service.get_session() as session:
                from sqlalchemy import select
                
                query = select(FileShare).where(
                    FileShare.file_id == file_id,
                    FileShare.is_deleted == False
                )
                result = await session.execute(query)
                file_record = result.scalar()
                
                if not file_record:
                    return None
                
                # 检查访问权限
                if file_record.is_private and file_record.uploader_id != user_id:
                    # 如果有访问令牌，验证令牌
                    if access_token:
                        if not encryption_service.verify_file_access_token(access_token, file_id, user_id):
                            return None
                    else:
                        return None
                
                # 检查文件是否存在
                file_path = self.upload_path / file_record.file_path
                if not file_path.exists():
                    logger.warning(f"File not found on disk: {file_path}")
                    return None
                
                # 读取文件内容
                async with aiofiles.open(file_path, 'rb') as f:
                    file_content = await f.read()
                
                # 更新下载计数
                file_record.download_count += 1
                await session.commit()
                
                logger.info(f"File downloaded: {file_record.original_filename} by {user_id}")
                
                return {
                    "file_content": file_content,
                    "filename": file_record.original_filename,
                    "mime_type": file_record.mime_type,
                    "file_size": file_record.file_size
                }
                
        except Exception as e:
            logger.error(f"Error downloading file: {e}")
            return None
    
    async def get_user_files(self, user_id: str, limit: int = 50, offset: int = 0) -> List[Dict[str, Any]]:
        """20250618.20:30 增加加密和文件共享以及数据库 - 获取用户文件列表"""
        try:
            async with database_service.get_session() as session:
                from sqlalchemy import select, desc
                
                query = select(FileShare).where(
                    FileShare.uploader_id == user_id,
                    FileShare.is_deleted == False
                ).order_by(desc(FileShare.created_at)).limit(limit).offset(offset)
                
                result = await session.execute(query)
                files = result.scalars().all()
                
                return [
                    {
                        "file_id": file.file_id,
                        "original_filename": file.original_filename,
                        "file_size": file.file_size,
                        "file_type": file.file_type,
                        "mime_type": file.mime_type,
                        "room_id": file.room_id,
                        "is_private": file.is_private,
                        "download_count": file.download_count,
                        "created_at": file.created_at.isoformat()
                    }
                    for file in files
                ]
                
        except Exception as e:
            logger.error(f"Error getting user files: {e}")
            return []
    
    async def get_room_files(self, room_id: str, user_id: str, limit: int = 50, offset: int = 0) -> List[Dict[str, Any]]:
        """20250618.20:30 增加加密和文件共享以及数据库 - 获取聊天室文件列表"""
        try:
            async with database_service.get_session() as session:
                from sqlalchemy import select, desc, or_
                
                query = select(FileShare).where(
                    FileShare.room_id == room_id,
                    FileShare.is_deleted == False,
                    or_(
                        FileShare.is_private == False,
                        FileShare.uploader_id == user_id
                    )
                ).order_by(desc(FileShare.created_at)).limit(limit).offset(offset)
                
                result = await session.execute(query)
                files = result.scalars().all()
                
                return [
                    {
                        "file_id": file.file_id,
                        "original_filename": file.original_filename,
                        "file_size": file.file_size,
                        "file_type": file.file_type,
                        "mime_type": file.mime_type,
                        "uploader_name": file.uploader_name,
                        "is_private": file.is_private,
                        "download_count": file.download_count,
                        "created_at": file.created_at.isoformat()
                    }
                    for file in files
                ]
                
        except Exception as e:
            logger.error(f"Error getting room files: {e}")
            return []
    
    async def delete_file(self, file_id: str, user_id: str, is_admin: bool = False) -> bool:
        """20250618.20:30 增加加密和文件共享以及数据库 - 删除文件"""
        try:
            async with database_service.get_session() as session:
                from sqlalchemy import select
                
                query = select(FileShare).where(
                    FileShare.file_id == file_id,
                    FileShare.is_deleted == False
                )
                result = await session.execute(query)
                file_record = result.scalar()
                
                if not file_record:
                    return False
                
                # 检查删除权限
                if not is_admin and file_record.uploader_id != user_id:
                    return False
                
                # 标记为已删除
                file_record.is_deleted = True
                await session.commit()
                
                # 删除物理文件
                try:
                    file_path = self.upload_path / file_record.file_path
                    if file_path.exists():
                        file_path.unlink()
                    
                    # 删除缩略图
                    thumbnail_path = self.upload_path / "thumbnails" / f"{file_id}_thumb.jpg"
                    if thumbnail_path.exists():
                        thumbnail_path.unlink()
                        
                except Exception as e:
                    logger.warning(f"Failed to delete physical file: {e}")
                
                logger.info(f"File deleted: {file_record.original_filename} by {user_id}")
                return True
                
        except Exception as e:
            logger.error(f"Error deleting file: {e}")
            return False
    
    async def cleanup_expired_files(self) -> int:
        """20250618.20:30 增加加密和文件共享以及数据库 - 清理过期文件"""
        try:
            cleanup_count = 0
            async with database_service.get_session() as session:
                from sqlalchemy import select
                
                # 查找过期文件
                query = select(FileShare).where(
                    FileShare.expires_at < datetime.utcnow(),
                    FileShare.is_deleted == False
                )
                result = await session.execute(query)
                expired_files = result.scalars().all()
                
                for file_record in expired_files:
                    # 标记为已删除
                    file_record.is_deleted = True
                    
                    # 删除物理文件
                    try:
                        file_path = self.upload_path / file_record.file_path
                        if file_path.exists():
                            file_path.unlink()
                        
                        # 删除缩略图
                        thumbnail_path = self.upload_path / "thumbnails" / f"{file_record.file_id}_thumb.jpg"
                        if thumbnail_path.exists():
                            thumbnail_path.unlink()
                            
                        cleanup_count += 1
                        
                    except Exception as e:
                        logger.warning(f"Failed to delete expired file: {e}")
                
                await session.commit()
                
            if cleanup_count > 0:
                logger.info(f"Cleaned up {cleanup_count} expired files")
                
            return cleanup_count
            
        except Exception as e:
            logger.error(f"Error cleaning up expired files: {e}")
            return 0
    
    def get_service_stats(self) -> Dict[str, Any]:
        """20250618.20:30 增加加密和文件共享以及数据库 - 获取服务统计信息"""
        try:
            # 计算存储使用情况
            total_size = 0
            file_count = 0
            
            for category_dir in ["images", "documents", "archives", "others"]:
                category_path = self.upload_path / category_dir
                if category_path.exists():
                    for file_path in category_path.rglob("*"):
                        if file_path.is_file():
                            total_size += file_path.stat().st_size
                            file_count += 1
            
            return {
                "enabled": self.enabled,
                "total_files": file_count,
                "total_size_mb": round(total_size / 1024 / 1024, 2),
                "max_file_size_mb": round(self.max_file_size / 1024 / 1024, 2),
                "allowed_extensions": self.allowed_extensions,
                "upload_path": str(self.upload_path)
            }
            
        except Exception as e:
            logger.error(f"Error getting service stats: {e}")
            return {"enabled": self.enabled, "error": str(e)}

# 20250618.20:30 增加加密和文件共享以及数据库 - 创建全局文件服务实例
file_service = FileShareService() 