# server2/app/core/services/database_service.py
# 20250618.20:30 增加加密和文件共享以及数据库 - PostgreSQL数据库服务模块
"""
PostgreSQL数据库服务模块 - 用于管理聊天室、成员、消息等数据
"""

from sqlalchemy.ext.asyncio import create_async_engine, AsyncSession, async_sessionmaker
from sqlalchemy.orm import declarative_base, sessionmaker
from sqlalchemy import Column, Integer, String, DateTime, Boolean, Text, ForeignKey, JSON, Index
from sqlalchemy.orm import relationship
from typing import Optional, List, Dict, Any
from datetime import datetime, timedelta
import logging
import asyncio

from ...config import settings

# 20250618.20:30 增加加密和文件共享以及数据库 - 配置日志
logger = logging.getLogger(__name__)

# 20250618.20:30 增加加密和文件共享以及数据库 - 数据库基类
# 20250619.12:30 聊天组 - 配置使用message模式
Base = declarative_base()
Base.metadata.schema = "message"

class ChatRoom(Base):
    """20250618.20:30 增加加密和文件共享以及数据库 - 聊天室模型"""
    __tablename__ = "chat_rooms"
    __table_args__ = {"schema": "message"}
    
    id = Column(Integer, primary_key=True, index=True)
    room_id = Column(String(50), unique=True, index=True, nullable=False)
    room_name = Column(String(100), nullable=False)
    description = Column(Text)
    is_private = Column(Boolean, default=False)
    is_active = Column(Boolean, default=True)
    max_members = Column(Integer, default=100)
    created_at = Column(DateTime, default=datetime.utcnow)
    updated_at = Column(DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)
    created_by = Column(String(50))
    
    # 关系
    members = relationship("ChatRoomMember", back_populates="room", cascade="all, delete-orphan")
    messages = relationship("ChatMessage", back_populates="room", cascade="all, delete-orphan")

class ChatRoomMember(Base):
    """20250618.20:30 增加加密和文件共享以及数据库 - 聊天室成员模型"""
    __tablename__ = "chat_room_members"
    __table_args__ = (
        Index("idx_room_employee", "room_id", "employee_id"),
        {"schema": "message"}
    )
    
    id = Column(Integer, primary_key=True, index=True)
    room_id = Column(String(50), ForeignKey("message.chat_rooms.room_id"), nullable=False)
    employee_id = Column(String(50), nullable=False)
    employee_name = Column(String(100), nullable=False)
    role = Column(String(20), default="member")  # member, admin, moderator
    joined_at = Column(DateTime, default=datetime.utcnow)
    is_active = Column(Boolean, default=True)
    last_read_at = Column(DateTime)
    
    # 关系
    room = relationship("ChatRoom", back_populates="members")

class ChatMessage(Base):
    """20250618.20:30 增加加密和文件共享以及数据库 - 聊天消息模型"""
    __tablename__ = "chat_messages"
    __table_args__ = (
        Index("idx_room_created", "room_id", "created_at"),
        Index("idx_sender_created", "sender_id", "created_at"),
        {"schema": "message"}
    )
    
    id = Column(Integer, primary_key=True, index=True)
    message_id = Column(String(100), unique=True, index=True, nullable=False)
    room_id = Column(String(50), ForeignKey("message.chat_rooms.room_id"), nullable=False)
    sender_id = Column(String(50), nullable=False)
    sender_name = Column(String(100), nullable=False)
    message_type = Column(String(20), default="text")  # text, file, image, system
    content = Column(Text, nullable=False)
    encrypted_content = Column(Text)  # 加密后的内容
    is_encrypted = Column(Boolean, default=False)
    reply_to_id = Column(String(100))  # 回复消息ID
    file_info = Column(JSON)  # 文件信息
    created_at = Column(DateTime, default=datetime.utcnow, index=True)
    updated_at = Column(DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)
    is_deleted = Column(Boolean, default=False)
    
    # 关系
    room = relationship("ChatRoom", back_populates="messages")

class PrivateMessage(Base):
    """20250618.20:30 增加加密和文件共享以及数据库 - 私聊消息模型"""
    __tablename__ = "private_messages"
    __table_args__ = (
        Index("idx_sender_receiver", "sender_id", "receiver_id"),
        Index("idx_receiver_created", "receiver_id", "created_at"),
        {"schema": "message"}
    )
    
    id = Column(Integer, primary_key=True, index=True)
    message_id = Column(String(100), unique=True, index=True, nullable=False)
    sender_id = Column(String(50), nullable=False)
    sender_name = Column(String(100), nullable=False)
    receiver_id = Column(String(50), nullable=False)
    receiver_name = Column(String(100), nullable=False)
    message_type = Column(String(20), default="text")
    content = Column(Text, nullable=False)
    encrypted_content = Column(Text)
    is_encrypted = Column(Boolean, default=False)
    file_info = Column(JSON)
    created_at = Column(DateTime, default=datetime.utcnow, index=True)
    is_read = Column(Boolean, default=False)
    read_at = Column(DateTime)
    is_deleted = Column(Boolean, default=False)

class FileShare(Base):
    """20250618.20:30 增加加密和文件共享以及数据库 - 文件共享模型"""
    __tablename__ = "file_shares"
    __table_args__ = (
        Index("idx_uploader_created", "uploader_id", "created_at"),
        Index("idx_room_created", "room_id", "created_at"),
        {"schema": "message"}
    )
    
    id = Column(Integer, primary_key=True, index=True)
    file_id = Column(String(100), unique=True, index=True, nullable=False)
    original_filename = Column(String(255), nullable=False)
    stored_filename = Column(String(255), nullable=False)
    file_path = Column(String(500), nullable=False)
    file_size = Column(Integer, nullable=False)
    file_type = Column(String(100), nullable=False)
    mime_type = Column(String(100))
    uploader_id = Column(String(50), nullable=False)
    uploader_name = Column(String(100), nullable=False)
    room_id = Column(String(50))  # 如果是聊天室文件
    is_private = Column(Boolean, default=False)
    download_count = Column(Integer, default=0)
    created_at = Column(DateTime, default=datetime.utcnow, index=True)
    expires_at = Column(DateTime)
    is_deleted = Column(Boolean, default=False)

class DatabaseService:
    """20250618.20:30 增加加密和文件共享以及数据库 - 数据库服务类"""
    
    def __init__(self):
        self.engine = None
        self.async_session_maker = None
        self.connected = False
    
    async def initialize(self):
        """20250618.20:30 增加加密和文件共享以及数据库 - 初始化数据库连接"""
        try:
            # 创建异步引擎
            self.engine = create_async_engine(
                settings.database_url,
                pool_size=settings.DB_POOL_SIZE,
                max_overflow=settings.DB_MAX_OVERFLOW,
                pool_timeout=settings.DB_POOL_TIMEOUT,
                pool_recycle=settings.DB_POOL_RECYCLE,
                echo=False  # 生产环境设为False
            )
            
            # 创建会话工厂
            self.async_session_maker = async_sessionmaker(
                self.engine, 
                class_=AsyncSession, 
                expire_on_commit=False
            )
            
            # 20250619.12:30 聊天组 - 创建表（处理索引重复问题）
            async with self.engine.begin() as conn:
                try:
                    await conn.run_sync(Base.metadata.create_all)
                except Exception as e:
                    if "already exists" in str(e) or "すでに存在します" in str(e):
                        logger.warning(f"Some database objects already exist, continuing: {e}")
                    else:
                        raise e
            
            self.connected = True
            logger.info(f"Database connected successfully: {settings.DB_HOST}:{settings.DB_PORT}/{settings.DB_NAME}")
            
            # 初始化默认聊天室
            await self.initialize_default_rooms()
            
        except Exception as e:
            logger.error(f"Failed to connect to database: {e}")
            self.connected = False
            raise e
    
    async def close(self):
        """20250618.20:30 增加加密和文件共享以及数据库 - 关闭数据库连接"""
        if self.engine:
            try:
                await self.engine.dispose()
                self.connected = False
                logger.info("Database connection closed")
            except Exception as e:
                logger.error(f"Error closing database connection: {e}")
    
    async def get_session(self) -> AsyncSession:
        """20250618.20:30 增加加密和文件共享以及数据库 - 获取数据库会话"""
        if not self.connected or not self.async_session_maker:
            raise RuntimeError("Database not initialized")
        return self.async_session_maker()
    
    async def initialize_default_rooms(self):
        """20250619.12:30 聊天组 - 初始化默认聊天室和成员"""
        try:
            session = self.async_session_maker()
            async with session:
                from sqlalchemy import select
                
                # 检查是否已有聊天室
                result = await session.execute(select(ChatRoom))
                existing_rooms = result.scalars().all()
                
                if not existing_rooms:
                    # 20250619.12:30 聊天组 - 创建默认聊天室（包含部门聊天室）
                    default_rooms = [
                        {
                            "room_id": "general",
                            "room_name": "综合讨论",
                            "description": "一般性讨论和交流",
                            "is_private": False,
                            "created_by": "system"
                        },
                        {
                            "room_id": "announcement",
                            "room_name": "公告通知",
                            "description": "重要公告和通知",
                            "is_private": False,
                            "created_by": "system"
                        },
                        {
                            "room_id": "production",
                            "room_name": "生产部",
                            "description": "生产部门内部交流",
                            "is_private": False,
                            "created_by": "system"
                        },
                        {
                            "room_id": "design",
                            "room_name": "设计部",
                            "description": "设计部门内部交流",
                            "is_private": False,
                            "created_by": "system"
                        },
                        {
                            "room_id": "quality",
                            "room_name": "质量部",
                            "description": "质量管理部门交流",
                            "is_private": False,
                            "created_by": "system"
                        },
                        {
                            "room_id": "management",
                            "room_name": "管理层",
                            "description": "管理层讨论区",
                            "is_private": False,
                            "created_by": "system"
                        }
                    ]
                    
                    for room_data in default_rooms:
                        room = ChatRoom(**room_data)
                        session.add(room)
                    
                    await session.commit()
                    logger.info(f"Created {len(default_rooms)} default chat rooms")
                    
                    # 20250619.12:30 聊天组 - 添加预设成员到各个聊天室
                    await self._add_default_members(session)
                
        except Exception as e:
            logger.error(f"Error initializing default rooms: {e}")
    
    async def _add_default_members(self, session):
        """20250619.12:30 聊天组 - 添加预设成员到聊天室"""
        try:
            # 20250619.12:30 聊天组 - 预设成员数据
            default_members = {
                "general": [
                    {"employee_id": "admin", "employee_name": "系统管理员", "role": "admin"},
                    {"employee_id": "215829", "employee_name": "mike", "role": "member"},
                ],
                "announcement": [
                    {"employee_id": "admin", "employee_name": "系统管理员", "role": "admin"},
                ],
                "production": [
                    {"employee_id": "prod001", "employee_name": "张三", "role": "admin"},
                    {"employee_id": "prod002", "employee_name": "李四", "role": "member"},
                    {"employee_id": "prod003", "employee_name": "王五", "role": "member"},
                    {"employee_id": "prod004", "employee_name": "赵六", "role": "member"},
                    {"employee_id": "215829", "employee_name": "mike", "role": "member"},
                ],
                "design": [
                    {"employee_id": "des001", "employee_name": "陈设计", "role": "admin"},
                    {"employee_id": "des002", "employee_name": "林工程", "role": "member"},
                    {"employee_id": "des003", "employee_name": "周美工", "role": "member"},
                    {"employee_id": "215829", "employee_name": "mike", "role": "member"},
                ],
                "quality": [
                    {"employee_id": "qa001", "employee_name": "质检员甲", "role": "admin"},
                    {"employee_id": "qa002", "employee_name": "质检员乙", "role": "member"},
                    {"employee_id": "qa003", "employee_name": "质检员丙", "role": "member"},
                ],
                "management": [
                    {"employee_id": "mgr001", "employee_name": "总经理", "role": "admin"},
                    {"employee_id": "mgr002", "employee_name": "副总经理", "role": "admin"},
                    {"employee_id": "mgr003", "employee_name": "部门经理", "role": "member"},
                ]
            }
            
            for room_id, members in default_members.items():
                for member_data in members:
                    member = ChatRoomMember(
                        room_id=room_id,
                        employee_id=member_data["employee_id"],
                        employee_name=member_data["employee_name"],
                        role=member_data["role"]
                    )
                    session.add(member)
            
            await session.commit()
            total_members = sum(len(members) for members in default_members.values())
            logger.info(f"Added {total_members} default members to chat rooms")
            
        except Exception as e:
            logger.error(f"Error adding default members: {e}")
    
    # 聊天室管理方法
    async def get_chat_rooms(self, include_private: bool = False) -> List[Dict[str, Any]]:
        """20250618.20:30 增加加密和文件共享以及数据库 - 获取聊天室列表"""
        try:
            session = self.async_session_maker()
            async with session:
                from sqlalchemy import select
                
                query = select(ChatRoom).where(ChatRoom.is_active == True)
                if not include_private:
                    query = query.where(ChatRoom.is_private == False)
                
                result = await session.execute(query)
                rooms = result.scalars().all()
                
                return [
                    {
                        "room_id": room.room_id,
                        "room_name": room.room_name,
                        "description": room.description,
                        "is_private": room.is_private,
                        "max_members": room.max_members,
                        "created_at": room.created_at.isoformat(),
                        "member_count": len(room.members)
                    }
                    for room in rooms
                ]
        except Exception as e:
            logger.error(f"Error getting chat rooms: {e}")
            return []
    
    async def get_room_members(self, room_id: str) -> List[Dict[str, Any]]:
        """20250618.20:30 增加加密和文件共享以及数据库 - 获取聊天室成员"""
        try:
            session = self.async_session_maker()
            async with session:
                from sqlalchemy import select
                
                query = select(ChatRoomMember).where(
                    ChatRoomMember.room_id == room_id,
                    ChatRoomMember.is_active == True
                )
                result = await session.execute(query)
                members = result.scalars().all()
                
                return [
                    {
                        "employee_id": member.employee_id,
                        "employee_name": member.employee_name,
                        "role": member.role,
                        "joined_at": member.joined_at.isoformat(),
                        "last_read_at": member.last_read_at.isoformat() if member.last_read_at else None
                    }
                    for member in members
                ]
        except Exception as e:
            logger.error(f"Error getting room members: {e}")
            return []
    
    async def add_member_to_room(self, room_id: str, employee_id: str, employee_name: str, role: str = "member") -> bool:
        """20250618.20:30 增加加密和文件共享以及数据库 - 添加成员到聊天室"""
        try:
            session = self.async_session_maker()
            async with session:
                from sqlalchemy import select
                
                # 检查是否已是成员
                existing = await session.execute(
                    select(ChatRoomMember).where(
                        ChatRoomMember.room_id == room_id,
                        ChatRoomMember.employee_id == employee_id
                    )
                )
                if existing.scalar():
                    return True  # 已是成员
                
                # 添加新成员
                member = ChatRoomMember(
                    room_id=room_id,
                    employee_id=employee_id,
                    employee_name=employee_name,
                    role=role
                )
                session.add(member)
                await session.commit()
                
                logger.info(f"Added member {employee_name} to room {room_id}")
                return True
                
        except Exception as e:
            logger.error(f"Error adding member to room: {e}")
            return False
    
    async def save_chat_message(self, message_data: Dict[str, Any]) -> bool:
        """20250618.20:30 增加加密和文件共享以及数据库 - 保存聊天消息"""
        try:
            session = self.async_session_maker()
            async with session:
                message = ChatMessage(
                    message_id=message_data.get("message_id"),
                    room_id=message_data.get("room_id", "general"),
                    sender_id=message_data.get("user_id"),
                    sender_name=message_data.get("user_name"),
                    message_type=message_data.get("message_type", "text"),
                    content=message_data.get("message"),
                    encrypted_content=message_data.get("encrypted_content"),
                    is_encrypted=message_data.get("is_encrypted", False),
                    file_info=message_data.get("file_info")
                )
                session.add(message)
                await session.commit()
                
                return True
                
        except Exception as e:
            logger.error(f"Error saving chat message: {e}")
            return False
    
    async def get_chat_history(self, room_id: str, limit: int = 50, offset: int = 0) -> List[Dict[str, Any]]:
        """20250618.20:30 增加加密和文件共享以及数据库 - 获取聊天历史"""
        try:
            session = self.async_session_maker()
            async with session:
                from sqlalchemy import select, desc
                
                query = select(ChatMessage).where(
                    ChatMessage.room_id == room_id,
                    ChatMessage.is_deleted == False
                ).order_by(desc(ChatMessage.created_at)).limit(limit).offset(offset)
                
                result = await session.execute(query)
                messages = result.scalars().all()
                
                return [
                    {
                        "message_id": msg.message_id,
                        "sender_id": msg.sender_id,
                        "sender_name": msg.sender_name,
                        "message_type": msg.message_type,
                        "content": msg.content,
                        "is_encrypted": msg.is_encrypted,
                        "file_info": msg.file_info,
                        "created_at": msg.created_at.isoformat(),
                        "reply_to_id": msg.reply_to_id
                    }
                    for msg in reversed(messages)  # 返回时间正序
                ]
                
        except Exception as e:
            logger.error(f"Error getting chat history: {e}")
            return []

# 20250618.20:30 增加加密和文件共享以及数据库 - 创建全局数据库服务实例
database_service = DatabaseService() 