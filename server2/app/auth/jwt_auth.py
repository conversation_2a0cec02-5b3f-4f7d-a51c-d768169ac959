# server2/app/auth/jwt_auth.py
# 20250618.20:15 微服务-信息交流 - JWT认证模块
"""
JWT认证模块 - 与主服务器保持一致的认证逻辑
"""

from jose import JWTError, jwt
from fastapi import HTTPException, status, Depends
from fastapi.security import HTT<PERSON><PERSON><PERSON><PERSON>, HTTPAuthorizationCredentials
from typing import Optional, Dict, Any
from datetime import datetime, timedelta
import logging

from ..config import settings

# 20250618.20:15 微服务-信息交流 - 配置日志
logger = logging.getLogger(__name__)

# 20250618.20:15 微服务-信息交流 - HTTP Bearer token scheme
security = HTTPBearer()

class JWTAuthHandler:
    """20250618.20:15 微服务-信息交流 - JWT认证处理器"""
    
    def __init__(self):
        self.secret_key = settings.JWT_SECRET_KEY
        self.algorithm = settings.JWT_ALGORITHM
        self.access_token_expire_hours = settings.JWT_ACCESS_TOKEN_EXPIRE_HOURS
    
    def create_access_token(self, data: Dict[str, Any]) -> str:
        """20250618.20:15 微服务-信息交流 - 创建访问令牌"""
        to_encode = data.copy()
        expire = datetime.utcnow() + timedelta(hours=self.access_token_expire_hours)
        to_encode.update({"exp": expire})
        
        try:
            encoded_jwt = jwt.encode(to_encode, self.secret_key, algorithm=self.algorithm)
            return encoded_jwt
        except Exception as e:
            logger.error(f"Error creating JWT token: {e}")
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail="Could not create access token"
            )
    
    def verify_token(self, token: str) -> Dict[str, Any]:
        """20250618.20:15 微服务-信息交流 - 验证令牌"""
        try:
            payload = jwt.decode(token, self.secret_key, algorithms=[self.algorithm])
            
            # 检查令牌是否过期
            exp = payload.get("exp")
            if exp is None:
                raise JWTError("Token missing expiration")
            
            if datetime.utcnow() > datetime.fromtimestamp(exp):
                raise JWTError("Token expired")
            
            return payload
            
        except JWTError as e:
            logger.warning(f"JWT verification failed: {e}")
            raise HTTPException(
                status_code=status.HTTP_401_UNAUTHORIZED,
                detail="Could not validate credentials",
                headers={"WWW-Authenticate": "Bearer"},
            )
        except Exception as e:
            logger.error(f"Unexpected error during token verification: {e}")
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail="Token verification error"
            )
    
    def extract_user_info(self, payload: Dict[str, Any]) -> Dict[str, Any]:
        """20250618.20:15 微服务-信息交流 - 从payload中提取用户信息"""
        return {
            "user_id": payload.get("sub"),
            "employee_id": payload.get("employee_id"),
            "employee_name": payload.get("employee_name"),
            "user_type": payload.get("user_type", "employee"),
            "exp": payload.get("exp")
        }

# 20250618.20:15 微服务-信息交流 - 创建全局JWT处理器实例
jwt_handler = JWTAuthHandler()

async def verify_jwt_token(credentials: HTTPAuthorizationCredentials = Depends(security)) -> Dict[str, Any]:
    """20250618.20:15 微服务-信息交流 - 验证JWT令牌的依赖函数"""
    token = credentials.credentials
    payload = jwt_handler.verify_token(token)
    user_info = jwt_handler.extract_user_info(payload)
    
    logger.info(f"JWT token verified for user: {user_info.get('employee_id')}")
    return user_info

async def verify_jwt_token_websocket(token: str) -> Dict[str, Any]:
    """20250618.20:15 微服务-信息交流 - WebSocket专用的JWT验证函数"""
    try:
        payload = jwt_handler.verify_token(token)
        user_info = jwt_handler.extract_user_info(payload)
        
        logger.info(f"WebSocket JWT token verified for user: {user_info.get('employee_id')}")
        return user_info
        
    except HTTPException as e:
        logger.warning(f"WebSocket JWT verification failed: {e.detail}")
        raise e
    except Exception as e:
        logger.error(f"Unexpected error during WebSocket JWT verification: {e}")
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="WebSocket authentication failed"
        )

def create_employee_token(employee_id: str, employee_name: str) -> str:
    """20250618.20:15 微服务-信息交流 - 创建员工令牌"""
    token_data = {
        "sub": f"employee_{employee_id}",
        "employee_id": employee_id,
        "employee_name": employee_name,
        "user_type": "employee"
    }
    
    return jwt_handler.create_access_token(token_data)

# 20250618.20:15 微服务-信息交流 - 用户信息依赖函数
async def get_current_user(user_info: Dict[str, Any] = Depends(verify_jwt_token)) -> Dict[str, Any]:
    """获取当前用户信息（强制认证）"""
    return user_info

# 20250618.20:15 微服务-信息交流 - 可选的用户信息依赖（不强制认证）
async def get_current_user_optional(credentials: Optional[HTTPAuthorizationCredentials] = Depends(security)) -> Optional[Dict[str, Any]]:
    """获取当前用户信息（可选，不强制认证）"""
    if not credentials:
        return None
    
    try:
        return await verify_jwt_token(credentials)
    except HTTPException:
        return None 