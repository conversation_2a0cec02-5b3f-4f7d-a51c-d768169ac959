# server2/app/config.py
# 20250618.20:15 微服务-信息交流 - 微服务器配置文件
# 20250618.20:30 增加加密和文件共享以及数据库 - 扩展配置支持
"""
MySuite 信息交流微服务器配置
"""

# 20250619.13:00 修复pydantic兼容性 - 兼容不同版本的pydantic
try:
    from pydantic_settings import BaseSettings
except ImportError:
    try:
        from pydantic import BaseSettings
    except ImportError:
        # 如果都没有，创建一个基本的BaseSettings类
        class BaseSettings:
            def __init__(self, **kwargs):
                for key, value in kwargs.items():
                    setattr(self, key, value)
            
            class Config:
                env_file = ".env"
                env_file_encoding = "utf-8"

from typing import Optional, List
import os
from pathlib import Path

class Settings(BaseSettings):
    """20250618.20:15 微服务-信息交流 - 配置类"""
    
    # 20250618.20:15 微服务-信息交流 - 服务器基本配置
    SERVICE_NAME: str = "MySuite Chat Microservice"
    SERVICE_VERSION: str = "1.0.0"
    SERVICE_PORT: int = 8005
    SERVICE_HOST: str = "0.0.0.0"
    
    # 20250618.20:15 微服务-信息交流 - JWT配置（与主服务器保持一致）
    JWT_SECRET_KEY: str = "your-very-secret-signing-key"
    JWT_ALGORITHM: str = "HS256"
    JWT_ACCESS_TOKEN_EXPIRE_HOURS: int = 24
    
    # 20250618.20:30 增加加密和文件共享以及数据库 - PostgreSQL数据库配置
    DB_HOST: str = "************"
    DB_PORT: int = 5432
    DB_NAME: str = "imdb"
    DB_USER: str = "postgres"
    DB_PASS: str = "pojiami0602"
    DB_POOL_SIZE: int = 5
    DB_MAX_OVERFLOW: int = 10
    DB_POOL_TIMEOUT: int = 30
    DB_POOL_RECYCLE: int = 3600
    
    # 20250618.20:15 微服务-信息交流 - Redis配置
    REDIS_HOST: str = "localhost"
    REDIS_PORT: int = 6379
    REDIS_DB: int = 1  # 使用不同的数据库索引
    REDIS_PASSWORD: Optional[str] = None
    REDIS_DECODE_RESPONSES: bool = True
    
    # 20250618.20:30 增加加密和文件共享以及数据库 - 消息加密配置
    MESSAGE_ENCRYPTION_KEY: str = "mysuite-chat-encryption-key-2025!"  # 32字符的加密密钥
    ENABLE_MESSAGE_ENCRYPTION: bool = True
    ENCRYPTION_ALGORITHM: str = "AES-256-GCM"
    
    # 20250618.20:30 增加加密和文件共享以及数据库 - 文件共享配置
    FILE_UPLOAD_ENABLED: bool = True
    FILE_UPLOAD_PATH: str = str(Path(__file__).parent.parent / "uploads")
    FILE_MAX_SIZE: int = 10 * 1024 * 1024  # 10MB
    FILE_ALLOWED_EXTENSIONS: List[str] = [
        ".jpg", ".jpeg", ".png", ".gif", ".bmp", ".webp",  # 图片
        ".pdf", ".doc", ".docx", ".xls", ".xlsx", ".ppt", ".pptx",  # 文档
        ".txt", ".md", ".csv", ".json", ".xml",  # 文本
        ".zip", ".rar", ".7z", ".tar", ".gz"  # 压缩包
    ]
    FILE_DOWNLOAD_TIMEOUT: int = 300  # 5分钟
    FILE_CLEANUP_DAYS: int = 30  # 30天后清理文件
    
    # 20250618.20:15 微服务-信息交流 - 聊天功能配置
    CHAT_MAX_MESSAGE_LENGTH: int = 1000
    CHAT_MAX_HISTORY_SIZE: int = 100
    CHAT_MAX_CONNECTIONS: int = 500
    CHAT_HEARTBEAT_INTERVAL: int = 30  # 秒
    CHAT_CONNECTION_TIMEOUT: int = 300  # 秒
    
    # 20250618.20:30 增加加密和文件共享以及数据库 - 聊天室配置
    DEFAULT_CHAT_ROOMS: List[str] = ["general", "work", "break", "announcement"]
    CHAT_ROOM_MAX_MEMBERS: int = 100
    ENABLE_PRIVATE_CHAT: bool = True
    PRIVATE_CHAT_HISTORY_DAYS: int = 7
    
    # 20250618.20:30 增加加密和文件共享以及数据库 - 管理员权限配置
    ADMIN_EMPLOYEE_IDS: List[str] = ["admin", "manager", "supervisor"]
    ADMIN_PASSWORD: str = "admin123"  # 管理员操作密码
    
    # 20250618.20:15 微服务-信息交流 - 跨域配置
    CORS_ORIGINS: list = ["*"]  # 生产环境应限制具体域名
    
    # 20250618.20:15 微服务-信息交流 - 日志配置
    LOG_LEVEL: str = "INFO"
    LOG_FORMAT: str = "%(asctime)s - %(name)s - %(levelname)s - %(message)s"
    
    # 20250618.20:15 微服务-信息交流 - SSL配置（可选）
    SSL_ENABLED: bool = False
    SSL_KEYFILE: Optional[str] = None
    SSL_CERTFILE: Optional[str] = None
    
    class Config:
        env_file = ".env"
        env_file_encoding = "utf-8"
    
    @property
    def database_url(self) -> str:
        """20250618.20:30 增加加密和文件共享以及数据库 - 获取数据库连接URL"""
        return f"postgresql+asyncpg://{self.DB_USER}:{self.DB_PASS}@{self.DB_HOST}:{self.DB_PORT}/{self.DB_NAME}"
    
    def ensure_upload_directory(self):
        """20250618.20:30 增加加密和文件共享以及数据库 - 确保上传目录存在"""
        upload_path = Path(self.FILE_UPLOAD_PATH)
        upload_path.mkdir(parents=True, exist_ok=True)
        return upload_path

# 20250618.20:15 微服务-信息交流 - 创建全局配置实例
settings = Settings()

# 20250618.20:15 微服务-信息交流 - 配置验证函数
def validate_settings():
    """验证配置是否正确"""
    errors = []
    
    if not settings.JWT_SECRET_KEY or len(settings.JWT_SECRET_KEY) < 10:
        errors.append("JWT_SECRET_KEY should be set to a secure value")
    
    if settings.SERVICE_PORT < 1024 or settings.SERVICE_PORT > 65535:
        errors.append("SERVICE_PORT should be between 1024 and 65535")
    
    if settings.CHAT_MAX_CONNECTIONS < 1:
        errors.append("CHAT_MAX_CONNECTIONS should be greater than 0")
    
    # 20250618.20:30 增加加密和文件共享以及数据库 - 新增验证
    if settings.ENABLE_MESSAGE_ENCRYPTION and len(settings.MESSAGE_ENCRYPTION_KEY) < 32:
        errors.append("MESSAGE_ENCRYPTION_KEY should be at least 32 characters long")
    
    if settings.FILE_UPLOAD_ENABLED and not settings.FILE_UPLOAD_PATH:
        errors.append("FILE_UPLOAD_PATH must be set when file upload is enabled")
    
    if settings.FILE_MAX_SIZE <= 0:
        errors.append("FILE_MAX_SIZE must be greater than 0")
    
    if not settings.DB_HOST or not settings.DB_NAME:
        errors.append("Database configuration (DB_HOST, DB_NAME) is required")
    
    if errors:
        raise ValueError(f"Configuration errors: {'; '.join(errors)}")
    
    return True

# 20250618.20:30 增加加密和文件共享以及数据库 - 初始化配置
try:
    validate_settings()
    settings.ensure_upload_directory()
    print(f"Configuration validated successfully")
    print(f"Upload directory: {settings.FILE_UPLOAD_PATH}")
    print(f"Database URL: {settings.database_url}")
except Exception as e:
    print(f"Configuration error: {e}")
    raise 