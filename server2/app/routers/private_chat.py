# server2/app/routers/private_chat.py
# 20250618.20:30 增加加密和文件共享以及数据库 - 私聊路由模块
"""
私聊路由模块 - 提供私聊消息、加密消息等功能
"""

from fastapi import APIRouter, Depends, HTTPException, Query
from pydantic import BaseModel
from typing import Optional, List, Dict, Any
import logging
from datetime import datetime, timedelta

from ..auth.jwt_auth import get_current_user
from ..core.services import redis_service, database_service, encryption_service
from ..config import settings

# 20250618.20:30 增加加密和文件共享以及数据库 - 配置日志
logger = logging.getLogger(__name__)

router = APIRouter()

class PrivateMessageRequest(BaseModel):
    """20250618.20:30 增加加密和文件共享以及数据库 - 私聊消息请求模型"""
    recipient_id: str
    content: str
    message_type: str = "text"  # text, file, image, etc.
    encrypted: bool = False
    reply_to: Optional[str] = None
    metadata: Optional[Dict[str, Any]] = None

class PrivateMessageResponse(BaseModel):
    """20250618.20:30 增加加密和文件共享以及数据库 - 私聊消息响应模型"""
    message_id: str
    sender_id: str
    sender_name: str
    recipient_id: str
    content: str
    message_type: str
    encrypted: bool
    timestamp: str
    read: bool = False
    reply_to: Optional[str] = None
    metadata: Optional[Dict[str, Any]] = None

@router.post("/send")
async def send_private_message(
    message: PrivateMessageRequest,
    user: dict = Depends(get_current_user)
):
    """20250618.20:30 增加加密和文件共享以及数据库 - 发送私聊消息"""
    try:
        sender_id = user["employee_id"]
        sender_name = user["employee_name"]
        
        # 验证接收者存在
        recipient_exists = await _check_user_exists(message.recipient_id)
        if not recipient_exists:
            raise HTTPException(status_code=404, detail="接收者不存在")
        
        # 生成消息ID
        import uuid
        message_id = f"pm_{uuid.uuid4().hex}"
        
        # 准备消息数据
        message_data = {
            "message_id": message_id,
            "sender_id": sender_id,
            "sender_name": sender_name,
            "recipient_id": message.recipient_id,
            "content": message.content,
            "message_type": message.message_type,
            "encrypted": message.encrypted,
            "timestamp": datetime.utcnow().isoformat(),
            "read": False,
            "reply_to": message.reply_to,
            "metadata": message.metadata or {}
        }
        
        # 如果启用加密且用户要求加密
        if message.encrypted and encryption_service.is_encryption_enabled():
            # 加密消息内容
            encrypted_content = encryption_service.encrypt_message(
                message.content,
                {"sender_id": sender_id, "recipient_id": message.recipient_id}
            )
            
            if encrypted_content:
                message_data["content"] = encrypted_content
                message_data["encrypted"] = True
                logger.info(f"Private message encrypted: {message_id}")
            else:
                logger.warning(f"Failed to encrypt message: {message_id}")
                message_data["encrypted"] = False
        
        # 保存到Redis（临时存储，用于实时推送）
        await redis_service.set_hash(
            f"private_message:{message_id}",
            message_data,
            expire=86400  # 24小时过期
        )
        
        # 保存到用户私聊列表
        await redis_service.list_push(
            f"private_messages:{sender_id}:{message.recipient_id}",
            message_id,
            max_length=1000  # 保留最近1000条消息
        )
        
        await redis_service.list_push(
            f"private_messages:{message.recipient_id}:{sender_id}",
            message_id,
            max_length=1000
        )
        
        # 更新未读消息计数
        await redis_service.increment(f"unread_private:{message.recipient_id}")
        
        # 如果接收者在线，实时推送消息
        await _push_private_message_to_user(message.recipient_id, message_data)
        
        logger.info(f"Private message sent: {sender_name} -> {message.recipient_id}")
        
        return {
            "status": "success",
            "message": "私聊消息发送成功",
            "data": {
                "message_id": message_id,
                "timestamp": message_data["timestamp"]
            }
        }
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error sending private message: {e}")
        raise HTTPException(status_code=500, detail=f"发送失败: {str(e)}")

@router.get("/conversations")
async def get_conversations(
    limit: int = Query(20, ge=1, le=100),
    offset: int = Query(0, ge=0),
    user: dict = Depends(get_current_user)
):
    """20250618.20:30 增加加密和文件共享以及数据库 - 获取用户会话列表"""
    try:
        user_id = user["employee_id"]
        conversations = []
        
        # 获取所有私聊对话
        pattern = f"private_messages:{user_id}:*"
        conversation_keys = await redis_service.scan_keys(pattern)
        
        for key in conversation_keys:
            # 提取对方用户ID
            parts = key.split(":")
            if len(parts) >= 3:
                other_user_id = parts[2]
                
                # 获取最近消息
                recent_message_ids = await redis_service.list_range(key, 0, 0)
                if recent_message_ids:
                    message_id = recent_message_ids[0]
                    message_data = await redis_service.get_hash(f"private_message:{message_id}")
                    
                    if message_data:
                        # 解密消息内容（如果需要）
                        content = message_data.get("content", "")
                        if message_data.get("encrypted") and encryption_service.is_encryption_enabled():
                            if isinstance(content, dict):
                                decrypted_content = encryption_service.decrypt_message(content)
                                if decrypted_content:
                                    content = decrypted_content
                        
                        # 获取未读消息数
                        unread_count = await redis_service.get(f"unread_private:{user_id}:{other_user_id}")
                        unread_count = int(unread_count) if unread_count else 0
                        
                        conversations.append({
                            "user_id": other_user_id,
                            "last_message": {
                                "content": content,
                                "timestamp": message_data.get("timestamp"),
                                "sender_id": message_data.get("sender_id"),
                                "message_type": message_data.get("message_type", "text")
                            },
                            "unread_count": unread_count
                        })
        
        # 按最后消息时间排序
        conversations.sort(
            key=lambda x: x["last_message"]["timestamp"],
            reverse=True
        )
        
        # 分页
        paginated = conversations[offset:offset + limit]
        
        return {
            "status": "success",
            "data": {
                "conversations": paginated,
                "total": len(conversations),
                "limit": limit,
                "offset": offset
            }
        }
        
    except Exception as e:
        logger.error(f"Error getting conversations: {e}")
        raise HTTPException(status_code=500, detail=f"获取会话列表失败: {str(e)}")

@router.get("/messages/{other_user_id}")
async def get_private_messages(
    other_user_id: str,
    limit: int = Query(50, ge=1, le=100),
    offset: int = Query(0, ge=0),
    user: dict = Depends(get_current_user)
):
    """20250618.20:30 增加加密和文件共享以及数据库 - 获取与指定用户的私聊消息"""
    try:
        user_id = user["employee_id"]
        
        # 验证对方用户存在
        other_user_exists = await _check_user_exists(other_user_id)
        if not other_user_exists:
            raise HTTPException(status_code=404, detail="用户不存在")
        
        # 获取消息ID列表
        message_ids = await redis_service.list_range(
            f"private_messages:{user_id}:{other_user_id}",
            offset,
            offset + limit - 1
        )
        
        messages = []
        for message_id in reversed(message_ids):  # 反转以获得正确的时间顺序
            message_data = await redis_service.get_hash(f"private_message:{message_id}")
            if message_data:
                # 解密消息内容（如果需要）
                content = message_data.get("content", "")
                if message_data.get("encrypted") and encryption_service.is_encryption_enabled():
                    if isinstance(content, dict):
                        decrypted_content = encryption_service.decrypt_message(content)
                        if decrypted_content:
                            content = decrypted_content
                        else:
                            content = "[解密失败]"
                
                messages.append({
                    "message_id": message_data.get("message_id"),
                    "sender_id": message_data.get("sender_id"),
                    "sender_name": message_data.get("sender_name"),
                    "recipient_id": message_data.get("recipient_id"),
                    "content": content,
                    "message_type": message_data.get("message_type", "text"),
                    "encrypted": message_data.get("encrypted", False),
                    "timestamp": message_data.get("timestamp"),
                    "read": message_data.get("read", False),
                    "reply_to": message_data.get("reply_to"),
                    "metadata": message_data.get("metadata", {})
                })
        
        # 标记消息为已读
        await _mark_messages_as_read(user_id, other_user_id)
        
        return {
            "status": "success",
            "data": {
                "messages": messages,
                "other_user_id": other_user_id,
                "total": len(messages),
                "limit": limit,
                "offset": offset
            }
        }
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error getting private messages: {e}")
        raise HTTPException(status_code=500, detail=f"获取私聊消息失败: {str(e)}")

@router.post("/mark-read/{other_user_id}")
async def mark_messages_as_read(
    other_user_id: str,
    user: dict = Depends(get_current_user)
):
    """20250618.20:30 增加加密和文件共享以及数据库 - 标记消息为已读"""
    try:
        user_id = user["employee_id"]
        
        await _mark_messages_as_read(user_id, other_user_id)
        
        return {
            "status": "success",
            "message": "消息已标记为已读"
        }
        
    except Exception as e:
        logger.error(f"Error marking messages as read: {e}")
        raise HTTPException(status_code=500, detail=f"标记已读失败: {str(e)}")

@router.get("/unread-count")
async def get_unread_count(user: dict = Depends(get_current_user)):
    """20250618.20:30 增加加密和文件共享以及数据库 - 获取未读消息总数"""
    try:
        user_id = user["employee_id"]
        
        unread_count = await redis_service.get(f"unread_private:{user_id}")
        unread_count = int(unread_count) if unread_count else 0
        
        return {
            "status": "success",
            "data": {
                "unread_count": unread_count
            }
        }
        
    except Exception as e:
        logger.error(f"Error getting unread count: {e}")
        raise HTTPException(status_code=500, detail=f"获取未读数失败: {str(e)}")

@router.delete("/message/{message_id}")
async def delete_private_message(
    message_id: str,
    user: dict = Depends(get_current_user)
):
    """20250618.20:30 增加加密和文件共享以及数据库 - 删除私聊消息"""
    try:
        user_id = user["employee_id"]
        
        # 获取消息数据
        message_data = await redis_service.get_hash(f"private_message:{message_id}")
        if not message_data:
            raise HTTPException(status_code=404, detail="消息不存在")
        
        # 检查权限（只有发送者可以删除）
        if message_data.get("sender_id") != user_id:
            raise HTTPException(status_code=403, detail="只能删除自己发送的消息")
        
        # 删除消息
        await redis_service.delete(f"private_message:{message_id}")
        
        # 从消息列表中移除
        recipient_id = message_data.get("recipient_id")
        await redis_service.list_remove(f"private_messages:{user_id}:{recipient_id}", message_id)
        await redis_service.list_remove(f"private_messages:{recipient_id}:{user_id}", message_id)
        
        logger.info(f"Private message deleted: {message_id} by {user_id}")
        
        return {
            "status": "success",
            "message": "消息删除成功"
        }
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error deleting private message: {e}")
        raise HTTPException(status_code=500, detail=f"删除消息失败: {str(e)}")

@router.get("/encryption-status")
async def get_encryption_status(user: dict = Depends(get_current_user)):
    """20250618.20:30 增加加密和文件共享以及数据库 - 获取加密状态"""
    try:
        encryption_info = encryption_service.get_encryption_info()
        
        return {
            "status": "success",
            "data": encryption_info
        }
        
    except Exception as e:
        logger.error(f"Error getting encryption status: {e}")
        raise HTTPException(status_code=500, detail=f"获取加密状态失败: {str(e)}")

# 20250618.20:30 增加加密和文件共享以及数据库 - 辅助函数

async def _check_user_exists(user_id: str) -> bool:
    """检查用户是否存在"""
    try:
        # 这里应该查询用户数据库或缓存
        # 暂时返回True，实际实现中应该查询真实的用户数据
        return True
    except Exception:
        return False

async def _push_private_message_to_user(user_id: str, message_data: Dict[str, Any]):
    """向在线用户推送私聊消息"""
    try:
        # 这里应该通过WebSocket连接管理器推送消息
        # 暂时记录日志，实际实现中需要集成WebSocket管理器
        logger.info(f"Pushing private message to user: {user_id}")
    except Exception as e:
        logger.error(f"Error pushing message to user {user_id}: {e}")

async def _mark_messages_as_read(user_id: str, other_user_id: str):
    """标记消息为已读"""
    try:
        # 重置未读计数
        await redis_service.delete(f"unread_private:{user_id}:{other_user_id}")
        
        # 更新消息的已读状态
        message_ids = await redis_service.list_range(
            f"private_messages:{user_id}:{other_user_id}",
            0, -1
        )
        
        for message_id in message_ids:
            message_data = await redis_service.get_hash(f"private_message:{message_id}")
            if message_data and message_data.get("recipient_id") == user_id:
                message_data["read"] = True
                await redis_service.set_hash(f"private_message:{message_id}", message_data)
                
    except Exception as e:
        logger.error(f"Error marking messages as read: {e}") 