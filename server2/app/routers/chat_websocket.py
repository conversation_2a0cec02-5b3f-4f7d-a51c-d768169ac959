# server2/app/routers/chat_websocket.py
# 20250618.20:15 微服务-信息交流 - WebSocket聊天路由（微服务版本）
"""
实时聊天WebSocket路由 - 微服务版本
支持百人以上在线信息交流，使用Redis缓存
"""

from fastapi import APIRouter, WebSocket, WebSocketDisconnect, HTTPException
from typing import Dict, List, Set, Optional
import json
import asyncio
from datetime import datetime
import logging

from ..auth.jwt_auth import verify_jwt_token_websocket
from ..core.services.redis_service import redis_service
from ..config import settings

# 20250618.20:15 微服务-信息交流 - 配置日志
logger = logging.getLogger(__name__)

router = APIRouter()

class ChatConnectionManager:
    """20250618.20:15 微服务-信息交流 - 聊天连接管理器（增强版）"""
    
    def __init__(self):
        # 活跃连接: {employee_id: {websocket, employee_name, last_seen, room_id}}
        self.active_connections: Dict[str, Dict] = {}
        # 聊天室连接: {room_id: set(employee_ids)}
        self.chat_rooms: Dict[str, Set[str]] = {}
        # 心跳任务
        self.heartbeat_task: Optional[asyncio.Task] = None
        # 最大连接数限制
        self.max_connections = settings.CHAT_MAX_CONNECTIONS
    
    async def start_heartbeat(self):
        """20250618.20:15 微服务-信息交流 - 启动心跳检测"""
        if self.heartbeat_task is None:
            self.heartbeat_task = asyncio.create_task(self._heartbeat_loop())
            logger.info("Chat heartbeat started")
    
    async def stop_heartbeat(self):
        """20250618.20:15 微服务-信息交流 - 停止心跳检测"""
        if self.heartbeat_task:
            self.heartbeat_task.cancel()
            try:
                await self.heartbeat_task
            except asyncio.CancelledError:
                pass
            self.heartbeat_task = None
            logger.info("Chat heartbeat stopped")
    
    async def _heartbeat_loop(self):
        """20250618.20:15 微服务-信息交流 - 心跳检测循环"""
        while True:
            try:
                await asyncio.sleep(settings.CHAT_HEARTBEAT_INTERVAL)
                await self._send_heartbeat()
                await self._cleanup_inactive_connections()
            except asyncio.CancelledError:
                break
            except Exception as e:
                logger.error(f"Heartbeat loop error: {e}")
    
    async def _send_heartbeat(self):
        """20250618.20:15 微服务-信息交流 - 发送心跳包"""
        ping_message = {
            "type": "ping",
            "timestamp": datetime.now().isoformat()
        }
        
        disconnected_users = []
        for employee_id, user_info in self.active_connections.items():
            try:
                await user_info["websocket"].send_text(json.dumps(ping_message))
                # 更新最后活跃时间
                user_info["last_seen"] = datetime.now()
                
                # 更新Redis中的用户状态
                await redis_service.save_user_status(employee_id, {
                    "employee_name": user_info["employee_name"],
                    "status": "online",
                    "last_seen": user_info["last_seen"],
                    "room_id": user_info.get("room_id", "general")
                })
                
            except Exception as e:
                logger.warning(f"Failed to send heartbeat to {employee_id}: {e}")
                disconnected_users.append(employee_id)
        
        # 清理断开的连接
        for employee_id in disconnected_users:
            await self.disconnect(employee_id)
    
    async def _cleanup_inactive_connections(self):
        """20250618.20:15 微服务-信息交流 - 清理不活跃的连接"""
        timeout_threshold = datetime.now().timestamp() - settings.CHAT_CONNECTION_TIMEOUT
        inactive_users = []
        
        for employee_id, user_info in self.active_connections.items():
            last_seen = user_info.get("last_seen", datetime.now())
            if isinstance(last_seen, datetime):
                last_seen_timestamp = last_seen.timestamp()
            else:
                last_seen_timestamp = datetime.now().timestamp()
            
            if last_seen_timestamp < timeout_threshold:
                inactive_users.append(employee_id)
        
        for employee_id in inactive_users:
            logger.info(f"Cleaning up inactive connection: {employee_id}")
            await self.disconnect(employee_id)
    
    async def connect(self, websocket: WebSocket, employee_id: str, employee_name: str):
        """20250618.20:15 微服务-信息交流 - 用户连接（增强版）"""
        # 检查连接数限制
        if len(self.active_connections) >= self.max_connections:
            await websocket.close(code=1008, reason="Server full")
            logger.warning(f"Connection rejected for {employee_id}: server full")
            return False
        
        await websocket.accept()
        
        # 如果用户已连接，断开旧连接
        if employee_id in self.active_connections:
            old_ws = self.active_connections[employee_id]["websocket"]
            try:
                await old_ws.close()
                logger.info(f"Closed old connection for {employee_id}")
            except:
                pass
        
        # 添加新连接
        default_room = "general"
        self.active_connections[employee_id] = {
            "websocket": websocket,
            "employee_name": employee_name,
            "last_seen": datetime.now(),
            "status": "online",
            "room_id": default_room,
            "connection_time": datetime.now()
        }
        
        # 加入默认聊天室
        if default_room not in self.chat_rooms:
            self.chat_rooms[default_room] = set()
        self.chat_rooms[default_room].add(employee_id)
        
        # 保存到Redis
        await redis_service.save_user_status(employee_id, {
            "employee_name": employee_name,
            "status": "online",
            "last_seen": datetime.now(),
            "room_id": default_room
        })
        
        logger.info(f"User {employee_name} ({employee_id}) connected to chat (total: {len(self.active_connections)})")
        
        # 启动心跳检测（如果还没有启动）
        await self.start_heartbeat()
        
        # 发送欢迎消息和在线用户列表
        await self.send_welcome_message(employee_id)
        await self.broadcast_user_list_update()
        
        # 发送最近聊天历史
        await self.send_chat_history(employee_id)
        
        return True
    
    async def disconnect(self, employee_id: str):
        """20250618.20:15 微服务-信息交流 - 用户断开连接（增强版）"""
        if employee_id in self.active_connections:
            user_info = self.active_connections[employee_id]
            del self.active_connections[employee_id]
            
            # 从所有聊天室移除
            for room_users in self.chat_rooms.values():
                room_users.discard(employee_id)
            
            # 从Redis移除用户状态
            await redis_service.remove_user_status(employee_id)
            
            logger.info(f"User {user_info['employee_name']} ({employee_id}) disconnected from chat (remaining: {len(self.active_connections)})")
            
            # 广播用户列表更新
            await self.broadcast_user_list_update()
            
            # 如果没有连接了，停止心跳
            if not self.active_connections:
                await self.stop_heartbeat()
    
    async def send_welcome_message(self, employee_id: str):
        """20250618.20:15 微服务-信息交流 - 发送欢迎消息"""
        if employee_id not in self.active_connections:
            return
        
        user_info = self.active_connections[employee_id]
        welcome_msg = {
            "type": "system_message",
            "message": f"欢迎 {user_info['employee_name']} 加入聊天室！当前在线 {len(self.active_connections)} 人",
            "timestamp": datetime.now().isoformat(),
            "user_id": "system",
            "user_name": "系统"
        }
        
        try:
            await self.active_connections[employee_id]["websocket"].send_text(json.dumps(welcome_msg))
        except Exception as e:
            logger.error(f"Failed to send welcome message to {employee_id}: {e}")
    
    async def send_chat_history(self, employee_id: str):
        """20250618.20:15 微服务-信息交流 - 发送聊天历史（从Redis获取）"""
        if employee_id not in self.active_connections:
            return
        
        try:
            # 从Redis获取聊天历史
            history_messages = await redis_service.get_chat_history(20)
            
            history_msg = {
                "type": "chat_history",
                "messages": history_messages,
                "timestamp": datetime.now().isoformat()
            }
            
            await self.active_connections[employee_id]["websocket"].send_text(json.dumps(history_msg))
            logger.debug(f"Sent {len(history_messages)} history messages to {employee_id}")
            
        except Exception as e:
            logger.error(f"Failed to send chat history to {employee_id}: {e}")
    
    async def broadcast_user_list_update(self):
        """20250618.20:15 微服务-信息交流 - 广播用户列表更新（增强版）"""
        online_users = []
        for emp_id, user_info in self.active_connections.items():
            online_users.append({
                "employee_id": emp_id,
                "employee_name": user_info["employee_name"],
                "status": user_info["status"],
                "last_seen": user_info["last_seen"].isoformat(),
                "room_id": user_info.get("room_id", "general"),
                "connection_time": user_info.get("connection_time", datetime.now()).isoformat()
            })
        
        user_list_msg = {
            "type": "user_list_update",
            "online_users": online_users,
            "total_count": len(online_users),
            "timestamp": datetime.now().isoformat(),
            "server_info": {
                "max_connections": self.max_connections,
                "current_connections": len(self.active_connections)
            }
        }
        
        # 广播给所有在线用户
        disconnected_users = []
        for emp_id, user_info in self.active_connections.items():
            try:
                await user_info["websocket"].send_text(json.dumps(user_list_msg))
            except Exception as e:
                logger.error(f"Failed to send user list to {emp_id}: {e}")
                disconnected_users.append(emp_id)
        
        # 清理断开的连接
        for emp_id in disconnected_users:
            await self.disconnect(emp_id)
    
    async def broadcast_message(self, sender_id: str, message: str, message_type: str = "chat_message"):
        """20250618.20:15 微服务-信息交流 - 广播消息（增强版）"""
        if sender_id not in self.active_connections:
            return False
        
        # 检查消息长度
        if len(message) > settings.CHAT_MAX_MESSAGE_LENGTH:
            return False
        
        sender_info = self.active_connections[sender_id]
        
        # 创建消息对象
        chat_message = {
            "type": message_type,
            "message": message,
            "timestamp": datetime.now().isoformat(),
            "user_id": sender_id,
            "user_name": sender_info["employee_name"],
            "message_id": f"{sender_id}_{int(datetime.now().timestamp() * 1000)}",
            "room_id": sender_info.get("room_id", "general")
        }
        
        # 保存到Redis
        await redis_service.save_chat_message(chat_message)
        await redis_service.increment_message_count()
        
        # 广播给所有在线用户
        disconnected_users = []
        broadcast_count = 0
        
        for emp_id, user_info in self.active_connections.items():
            try:
                await user_info["websocket"].send_text(json.dumps(chat_message))
                broadcast_count += 1
            except Exception as e:
                logger.error(f"Failed to broadcast message to {emp_id}: {e}")
                disconnected_users.append(emp_id)
        
        # 清理断开的连接
        for emp_id in disconnected_users:
            await self.disconnect(emp_id)
        
        logger.info(f"Message from {sender_info['employee_name']} broadcasted to {broadcast_count} users")
        return True
    
    async def send_private_message(self, sender_id: str, recipient_id: str, message: str):
        """20250618.20:15 微服务-信息交流 - 发送私聊消息"""
        if sender_id not in self.active_connections or recipient_id not in self.active_connections:
            return False
        
        sender_info = self.active_connections[sender_id]
        recipient_info = self.active_connections[recipient_id]
        
        private_message = {
            "type": "private_message",
            "message": message,
            "timestamp": datetime.now().isoformat(),
            "sender_id": sender_id,
            "sender_name": sender_info["employee_name"],
            "recipient_id": recipient_id,
            "recipient_name": recipient_info["employee_name"],
            "message_id": f"private_{sender_id}_{recipient_id}_{int(datetime.now().timestamp() * 1000)}"
        }
        
        try:
            # 发送给接收者
            await recipient_info["websocket"].send_text(json.dumps(private_message))
            # 也发送给发送者作为确认
            await sender_info["websocket"].send_text(json.dumps(private_message))
            
            logger.info(f"Private message from {sender_info['employee_name']} to {recipient_info['employee_name']}")
            return True
            
        except Exception as e:
            logger.error(f"Failed to send private message: {e}")
            return False
    
    def get_online_count(self) -> int:
        """20250618.20:15 微服务-信息交流 - 获取在线用户数"""
        return len(self.active_connections)
    
    def get_connection_info(self) -> Dict:
        """20250618.20:15 微服务-信息交流 - 获取连接信息"""
        return {
            "total_connections": len(self.active_connections),
            "max_connections": self.max_connections,
            "chat_rooms": {room: len(users) for room, users in self.chat_rooms.items()},
            "heartbeat_active": self.heartbeat_task is not None and not self.heartbeat_task.done()
        }

# 20250618.20:15 微服务-信息交流 - 创建全局连接管理器
chat_manager = ChatConnectionManager()

@router.websocket("/ws/chat")
async def websocket_chat_endpoint(websocket: WebSocket, token: str):
    """20250618.20:15 微服务-信息交流 - WebSocket聊天端点（微服务版本）"""
    employee_id = None
    
    try:
        # 验证JWT令牌
        user_info = await verify_jwt_token_websocket(token)
        employee_id = user_info.get("employee_id")
        employee_name = user_info.get("employee_name", f"User_{employee_id}")
        
        if not employee_id:
            await websocket.close(code=1008, reason="Invalid user information")
            return
        
        # 建立连接
        connection_success = await chat_manager.connect(websocket, employee_id, employee_name)
        if not connection_success:
            return
        
        # 监听消息
        while True:
            try:
                # 设置接收超时
                data = await asyncio.wait_for(websocket.receive_text(), timeout=60.0)
                message_data = json.loads(data)
                
                message_type = message_data.get("type", "")
                
                if message_type == "chat_message":
                    message = message_data.get("message", "").strip()
                    if message:
                        await chat_manager.broadcast_message(employee_id, message)
                
                elif message_type == "private_message":
                    recipient_id = message_data.get("recipient_id", "")
                    message = message_data.get("message", "").strip()
                    if recipient_id and message:
                        await chat_manager.send_private_message(employee_id, recipient_id, message)
                
                elif message_type == "pong":
                    # 心跳响应，更新最后活跃时间
                    if employee_id in chat_manager.active_connections:
                        chat_manager.active_connections[employee_id]["last_seen"] = datetime.now()
                
                else:
                    logger.warning(f"Unknown message type from {employee_id}: {message_type}")
                    
            except asyncio.TimeoutError:
                # 发送心跳检测
                ping_msg = {
                    "type": "ping",
                    "timestamp": datetime.now().isoformat()
                }
                await websocket.send_text(json.dumps(ping_msg))
                
            except json.JSONDecodeError:
                logger.warning(f"Invalid JSON from {employee_id}")
                
            except Exception as e:
                logger.error(f"Error processing message from {employee_id}: {e}")
                break
                
    except WebSocketDisconnect:
        logger.info(f"WebSocket disconnected: {employee_id}")
        
    except HTTPException as e:
        logger.warning(f"Authentication failed: {e.detail}")
        try:
            await websocket.close(code=1008, reason="Authentication failed")
        except:
            pass
            
    except Exception as e:
        logger.error(f"WebSocket error: {e}")
        try:
            await websocket.close(code=1011, reason="Internal server error")
        except:
            pass
    
    finally:
        # 清理连接
        if employee_id:
            await chat_manager.disconnect(employee_id)

@router.get("/api/chat/status")
async def get_chat_status():
    """20250618.20:15 微服务-信息交流 - 获取聊天服务状态"""
    try:
        connection_info = chat_manager.get_connection_info()
        redis_stats = await redis_service.get_service_stats()
        
        return {
            "status": "active",
            "service": "MySuite Chat Microservice",
            "version": "1.0.0",
            "port": settings.SERVICE_PORT,
            "connections": connection_info,
            "redis": redis_stats,
            "timestamp": datetime.now().isoformat()
        }
        
    except Exception as e:
        logger.error(f"Error getting chat status: {e}")
        return {
            "status": "error",
            "error": str(e),
            "timestamp": datetime.now().isoformat()
        } 