# server2/app/routers/chat_management.py
# 20250618.20:15 微服务-信息交流 - 聊天管理REST API路由
"""
聊天管理REST API路由
提供聊天室管理、用户管理、消息历史等功能
"""

from fastapi import APIRouter, Depends, HTTPException, Query
from typing import List, Dict, Any, Optional
from datetime import datetime, timedelta
import logging
import json

from ..auth.jwt_auth import verify_jwt_token, get_current_user_optional
from ..core.services.redis_service import redis_service
from .chat_websocket import chat_manager
from ..config import settings

# 20250618.20:15 微服务-信息交流 - 配置日志
logger = logging.getLogger(__name__)

router = APIRouter()

@router.get("/status")
async def get_chat_service_status(user: Optional[Dict] = Depends(get_current_user_optional)):
    """20250618.20:15 微服务-信息交流 - 获取聊天服务详细状态"""
    try:
        connection_info = chat_manager.get_connection_info()
        redis_stats = await redis_service.get_service_stats()
        
        # 基础状态信息
        status_info = {
            "service": "MySuite Chat Microservice",
            "version": "1.0.0",
            "status": "running",
            "port": settings.SERVICE_PORT,
            "timestamp": datetime.now().isoformat(),
            "uptime": "N/A",  # 可以添加启动时间跟踪
            "connections": {
                "current": connection_info["total_connections"],
                "max": connection_info["max_connections"],
                "utilization": round(connection_info["total_connections"] / connection_info["max_connections"] * 100, 2)
            },
            "redis": {
                "connected": redis_stats.get("redis_connected", False),
                "online_users": redis_stats.get("online_users_count", 0),
                "messages_today": redis_stats.get("messages_today", 0)
            }
        }
        
        # 如果用户已认证，提供更详细的信息
        if user:
            status_info.update({
                "detailed_connections": connection_info,
                "redis_details": redis_stats,
                "config": {
                    "max_message_length": settings.CHAT_MAX_MESSAGE_LENGTH,
                    "max_history_size": settings.CHAT_MAX_HISTORY_SIZE,
                    "heartbeat_interval": settings.CHAT_HEARTBEAT_INTERVAL,
                    "connection_timeout": settings.CHAT_CONNECTION_TIMEOUT
                }
            })
        
        return status_info
        
    except Exception as e:
        logger.error(f"Error getting chat service status: {e}")
        raise HTTPException(status_code=500, detail="Failed to get service status")

@router.get("/users/online")
async def get_online_users(user: Dict = Depends(verify_jwt_token)):
    """20250618.20:15 微服务-信息交流 - 获取在线用户列表"""
    try:
        online_users = []
        
        # 从连接管理器获取活跃连接
        for emp_id, user_info in chat_manager.active_connections.items():
            online_users.append({
                "employee_id": emp_id,
                "employee_name": user_info["employee_name"],
                "status": user_info["status"],
                "last_seen": user_info["last_seen"].isoformat(),
                "room_id": user_info.get("room_id", "general"),
                "connection_time": user_info.get("connection_time", datetime.now()).isoformat()
            })
        
        # 也从Redis获取用户状态（备份数据）
        redis_users = await redis_service.get_online_users()
        
        return {
            "online_users": online_users,
            "total_count": len(online_users),
            "redis_users_count": len(redis_users),
            "timestamp": datetime.now().isoformat()
        }
        
    except Exception as e:
        logger.error(f"Error getting online users: {e}")
        raise HTTPException(status_code=500, detail="Failed to get online users")

@router.get("/history")
async def get_chat_history(
    limit: int = Query(20, ge=1, le=100),
    user: Dict = Depends(verify_jwt_token)
):
    """20250618.20:15 微服务-信息交流 - 获取聊天历史记录"""
    try:
        # 从Redis获取聊天历史
        history_messages = await redis_service.get_chat_history(limit)
        
        return {
            "messages": history_messages,
            "count": len(history_messages),
            "limit": limit,
            "timestamp": datetime.now().isoformat()
        }
        
    except Exception as e:
        logger.error(f"Error getting chat history: {e}")
        raise HTTPException(status_code=500, detail="Failed to get chat history")

@router.get("/stats")
async def get_chat_statistics(user: Dict = Depends(verify_jwt_token)):
    """20250618.20:15 微服务-信息交流 - 获取聊天统计信息"""
    try:
        # 获取今天的统计
        today = datetime.now().strftime("%Y-%m-%d")
        messages_today = await redis_service.get_message_count(today)
        
        # 获取过去7天的统计
        daily_stats = []
        for i in range(7):
            date = (datetime.now() - timedelta(days=i)).strftime("%Y-%m-%d")
            count = await redis_service.get_message_count(date)
            daily_stats.append({
                "date": date,
                "message_count": count
            })
        
        # 连接统计
        connection_info = chat_manager.get_connection_info()
        
        return {
            "messages": {
                "today": messages_today,
                "daily_stats": daily_stats
            },
            "connections": {
                "current": connection_info["total_connections"],
                "max": connection_info["max_connections"],
                "rooms": connection_info["chat_rooms"]
            },
            "service": {
                "heartbeat_active": connection_info["heartbeat_active"],
                "redis_connected": await redis_service.is_connected()
            },
            "timestamp": datetime.now().isoformat()
        }
        
    except Exception as e:
        logger.error(f"Error getting chat statistics: {e}")
        raise HTTPException(status_code=500, detail="Failed to get chat statistics")

@router.post("/broadcast")
async def broadcast_system_message(
    message: str,
    user: Dict = Depends(verify_jwt_token)
):
    """20250618.20:15 微服务-信息交流 - 广播系统消息（管理员功能）"""
    try:
        # 检查用户权限（这里可以添加管理员权限检查）
        if not message or len(message.strip()) == 0:
            raise HTTPException(status_code=400, detail="Message cannot be empty")
        
        if len(message) > settings.CHAT_MAX_MESSAGE_LENGTH:
            raise HTTPException(status_code=400, detail="Message too long")
        
        # 创建系统消息
        system_message = {
            "type": "system_message",
            "message": f"[系统广播] {message.strip()}",
            "timestamp": datetime.now().isoformat(),
            "user_id": "system",
            "user_name": "系统管理员",
            "message_id": f"system_{int(datetime.now().timestamp() * 1000)}",
            "broadcaster": user.get("employee_name", "Unknown")
        }
        
        # 保存到Redis
        await redis_service.save_chat_message(system_message)
        
        # 广播给所有在线用户
        broadcast_count = 0
        for emp_id, user_info in chat_manager.active_connections.items():
            try:
                await user_info["websocket"].send_text(json.dumps(system_message))
                broadcast_count += 1
            except Exception as e:
                logger.warning(f"Failed to send system message to {emp_id}: {e}")
        
        logger.info(f"System message broadcasted by {user.get('employee_name')} to {broadcast_count} users")
        
        return {
            "status": "success",
            "message": "System message broadcasted successfully",
            "broadcast_count": broadcast_count,
            "timestamp": datetime.now().isoformat()
        }
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error broadcasting system message: {e}")
        raise HTTPException(status_code=500, detail="Failed to broadcast system message")

@router.delete("/user/{employee_id}/disconnect")
async def disconnect_user(
    employee_id: str,
    user: Dict = Depends(verify_jwt_token)
):
    """20250618.20:15 微服务-信息交流 - 强制断开用户连接（管理员功能）"""
    try:
        # 检查用户权限（这里可以添加管理员权限检查）
        
        if employee_id not in chat_manager.active_connections:
            raise HTTPException(status_code=404, detail="User not found or not connected")
        
        # 发送断开通知给用户
        disconnect_message = {
            "type": "system_message",
            "message": "您的连接已被管理员断开",
            "timestamp": datetime.now().isoformat(),
            "user_id": "system",
            "user_name": "系统"
        }
        
        try:
            user_info = chat_manager.active_connections[employee_id]
            await user_info["websocket"].send_text(json.dumps(disconnect_message))
            await user_info["websocket"].close(code=1008, reason="Disconnected by administrator")
        except:
            pass
        
        # 断开连接
        await chat_manager.disconnect(employee_id)
        
        logger.info(f"User {employee_id} disconnected by administrator {user.get('employee_name')}")
        
        return {
            "status": "success",
            "message": f"User {employee_id} disconnected successfully",
            "timestamp": datetime.now().isoformat()
        }
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error disconnecting user {employee_id}: {e}")
        raise HTTPException(status_code=500, detail="Failed to disconnect user")

@router.get("/rooms")
async def get_chat_rooms(user: Dict = Depends(verify_jwt_token)):
    """20250618.20:15 微服务-信息交流 - 获取聊天室信息"""
    try:
        connection_info = chat_manager.get_connection_info()
        rooms_info = []
        
        for room_id, user_count in connection_info["chat_rooms"].items():
            # 获取房间内的用户列表
            room_users = []
            if room_id in chat_manager.chat_rooms:
                for emp_id in chat_manager.chat_rooms[room_id]:
                    if emp_id in chat_manager.active_connections:
                        user_info = chat_manager.active_connections[emp_id]
                        room_users.append({
                            "employee_id": emp_id,
                            "employee_name": user_info["employee_name"],
                            "status": user_info["status"]
                        })
            
            rooms_info.append({
                "room_id": room_id,
                "user_count": user_count,
                "users": room_users
            })
        
        return {
            "rooms": rooms_info,
            "total_rooms": len(rooms_info),
            "timestamp": datetime.now().isoformat()
        }
        
    except Exception as e:
        logger.error(f"Error getting chat rooms: {e}")
        raise HTTPException(status_code=500, detail="Failed to get chat rooms")

@router.get("/health")
async def health_check():
    """20250618.20:15 微服务-信息交流 - 健康检查端点"""
    try:
        redis_connected = await redis_service.is_connected()
        connection_count = len(chat_manager.active_connections)
        
        health_status = "healthy"
        if not redis_connected:
            health_status = "degraded"
        
        return {
            "status": health_status,
            "service": "MySuite Chat Microservice",
            "version": "1.0.0",
            "checks": {
                "redis": "connected" if redis_connected else "disconnected",
                "websocket_manager": "active",
                "connections": f"{connection_count}/{settings.CHAT_MAX_CONNECTIONS}"
            },
            "timestamp": datetime.now().isoformat()
        }
        
    except Exception as e:
        logger.error(f"Health check error: {e}")
        return {
            "status": "unhealthy",
            "error": str(e),
            "timestamp": datetime.now().isoformat()
        } 