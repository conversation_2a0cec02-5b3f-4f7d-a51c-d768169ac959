# server2/app/routers/file_sharing.py
# 20250618.20:30 增加加密和文件共享以及数据库 - 文件共享路由模块
"""
文件共享路由模块 - 提供文件上传、下载、管理等API接口
"""

from fastapi import APIRouter, Depends, HTTPException, UploadFile, File, Form, Query
from fastapi.responses import StreamingResponse
from typing import Optional, List
import io
import logging
from datetime import datetime

from ..auth.jwt_auth import get_current_user
from ..core.services import file_service, database_service
from ..config import settings

# 20250618.20:30 增加加密和文件共享以及数据库 - 配置日志
logger = logging.getLogger(__name__)

router = APIRouter()

@router.post("/upload")
async def upload_file(
    file: UploadFile = File(...),
    room_id: Optional[str] = Form(None),
    is_private: bool = Form(False),
    user: dict = Depends(get_current_user)
):
    """20250618.20:30 增加加密和文件共享以及数据库 - 上传文件"""
    try:
        if not file_service.enabled:
            raise HTTPException(status_code=503, detail="文件上传功能已禁用")
        
        # 读取文件内容
        file_content = await file.read()
        
        # 上传文件
        result = await file_service.upload_file(
            file_content=file_content,
            filename=file.filename,
            uploader_id=user["employee_id"],
            uploader_name=user["employee_name"],
            room_id=room_id,
            is_private=is_private
        )
        
        if result["success"]:
            logger.info(f"File uploaded: {file.filename} by {user['employee_name']}")
            return {
                "status": "success",
                "message": "文件上传成功",
                "data": result["file_info"]
            }
        else:
            error_msg = result.get("error", "上传失败")
            errors = result.get("errors", [])
            if errors:
                error_msg = "; ".join(errors)
            
            raise HTTPException(status_code=400, detail=error_msg)
            
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error uploading file: {e}")
        raise HTTPException(status_code=500, detail=f"上传失败: {str(e)}")

@router.get("/download/{file_id}")
async def download_file(
    file_id: str,
    access_token: Optional[str] = Query(None),
    user: dict = Depends(get_current_user)
):
    """20250618.20:30 增加加密和文件共享以及数据库 - 下载文件"""
    try:
        result = await file_service.download_file(
            file_id=file_id,
            user_id=user["employee_id"],
            access_token=access_token
        )
        
        if not result:
            raise HTTPException(status_code=404, detail="文件不存在或无权限访问")
        
        # 创建文件流响应
        file_stream = io.BytesIO(result["file_content"])
        
        # 设置响应头
        headers = {
            "Content-Disposition": f'attachment; filename="{result["filename"]}"',
            "Content-Length": str(result["file_size"])
        }
        
        return StreamingResponse(
            io.BytesIO(result["file_content"]),
            media_type=result["mime_type"],
            headers=headers
        )
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error downloading file: {e}")
        raise HTTPException(status_code=500, detail=f"下载失败: {str(e)}")

@router.get("/info/{file_id}")
async def get_file_info(
    file_id: str,
    user: dict = Depends(get_current_user)
):
    """20250618.20:30 增加加密和文件共享以及数据库 - 获取文件信息"""
    try:
        file_info = await file_service.get_file_info(file_id, user["employee_id"])
        
        if not file_info:
            raise HTTPException(status_code=404, detail="文件不存在或无权限访问")
        
        return {
            "status": "success",
            "data": file_info
        }
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error getting file info: {e}")
        raise HTTPException(status_code=500, detail=f"获取文件信息失败: {str(e)}")

@router.get("/my-files")
async def get_my_files(
    limit: int = Query(50, ge=1, le=100),
    offset: int = Query(0, ge=0),
    user: dict = Depends(get_current_user)
):
    """20250618.20:30 增加加密和文件共享以及数据库 - 获取用户文件列表"""
    try:
        files = await file_service.get_user_files(
            user_id=user["employee_id"],
            limit=limit,
            offset=offset
        )
        
        return {
            "status": "success",
            "data": {
                "files": files,
                "total": len(files),
                "limit": limit,
                "offset": offset
            }
        }
        
    except Exception as e:
        logger.error(f"Error getting user files: {e}")
        raise HTTPException(status_code=500, detail=f"获取文件列表失败: {str(e)}")

@router.get("/room/{room_id}")
async def get_room_files(
    room_id: str,
    limit: int = Query(50, ge=1, le=100),
    offset: int = Query(0, ge=0),
    user: dict = Depends(get_current_user)
):
    """20250618.20:30 增加加密和文件共享以及数据库 - 获取聊天室文件列表"""
    try:
        files = await file_service.get_room_files(
            room_id=room_id,
            user_id=user["employee_id"],
            limit=limit,
            offset=offset
        )
        
        return {
            "status": "success",
            "data": {
                "room_id": room_id,
                "files": files,
                "total": len(files),
                "limit": limit,
                "offset": offset
            }
        }
        
    except Exception as e:
        logger.error(f"Error getting room files: {e}")
        raise HTTPException(status_code=500, detail=f"获取聊天室文件失败: {str(e)}")

@router.delete("/{file_id}")
async def delete_file(
    file_id: str,
    user: dict = Depends(get_current_user)
):
    """20250618.20:30 增加加密和文件共享以及数据库 - 删除文件"""
    try:
        # 检查是否为管理员
        is_admin = user["employee_id"] in settings.ADMIN_EMPLOYEE_IDS
        
        success = await file_service.delete_file(
            file_id=file_id,
            user_id=user["employee_id"],
            is_admin=is_admin
        )
        
        if success:
            return {
                "status": "success",
                "message": "文件删除成功"
            }
        else:
            raise HTTPException(status_code=404, detail="文件不存在或无权限删除")
            
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error deleting file: {e}")
        raise HTTPException(status_code=500, detail=f"删除文件失败: {str(e)}")

@router.get("/stats")
async def get_file_stats(user: dict = Depends(get_current_user)):
    """20250618.20:30 增加加密和文件共享以及数据库 - 获取文件统计信息"""
    try:
        # 检查是否为管理员
        is_admin = user["employee_id"] in settings.ADMIN_EMPLOYEE_IDS
        
        if not is_admin:
            raise HTTPException(status_code=403, detail="需要管理员权限")
        
        stats = file_service.get_service_stats()
        
        return {
            "status": "success",
            "data": stats
        }
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error getting file stats: {e}")
        raise HTTPException(status_code=500, detail=f"获取统计信息失败: {str(e)}")

@router.post("/cleanup")
async def cleanup_expired_files(user: dict = Depends(get_current_user)):
    """20250618.20:30 增加加密和文件共享以及数据库 - 清理过期文件"""
    try:
        # 检查是否为管理员
        is_admin = user["employee_id"] in settings.ADMIN_EMPLOYEE_IDS
        
        if not is_admin:
            raise HTTPException(status_code=403, detail="需要管理员权限")
        
        cleanup_count = await file_service.cleanup_expired_files()
        
        return {
            "status": "success",
            "message": f"已清理 {cleanup_count} 个过期文件",
            "data": {
                "cleaned_files": cleanup_count
            }
        }
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error cleaning up files: {e}")
        raise HTTPException(status_code=500, detail=f"清理文件失败: {str(e)}")

@router.get("/config")
async def get_file_config(user: dict = Depends(get_current_user)):
    """20250618.20:30 增加加密和文件共享以及数据库 - 获取文件配置信息"""
    try:
        return {
            "status": "success",
            "data": {
                "enabled": file_service.enabled,
                "max_file_size_mb": round(settings.FILE_MAX_SIZE / 1024 / 1024, 2),
                "allowed_extensions": settings.FILE_ALLOWED_EXTENSIONS,
                "cleanup_days": settings.FILE_CLEANUP_DAYS,
                "download_timeout_minutes": settings.FILE_DOWNLOAD_TIMEOUT
            }
        }
        
    except Exception as e:
        logger.error(f"Error getting file config: {e}")
        raise HTTPException(status_code=500, detail=f"获取配置失败: {str(e)}") 