# server2/app/routers/room_management.py
# 20250618.20:30 增加加密和文件共享以及数据库 - 聊天室管理路由模块
"""
聊天室管理路由模块 - 提供多聊天室分组、成员管理等功能
"""

from fastapi import APIRouter, Depends, HTTPException, Query
from pydantic import BaseModel
from typing import Optional, List, Dict, Any
import logging
from datetime import datetime

from ..auth.jwt_auth import get_current_user
from ..core.services import database_service, redis_service
from ..config import settings

# 20250618.20:30 增加加密和文件共享以及数据库 - 配置日志
logger = logging.getLogger(__name__)

router = APIRouter()

class CreateRoomRequest(BaseModel):
    """20250618.20:30 增加加密和文件共享以及数据库 - 创建聊天室请求模型"""
    name: str
    description: Optional[str] = None
    is_private: bool = False
    max_members: int = 100
    category: str = "general"

class UpdateRoomRequest(BaseModel):
    """20250618.20:30 增加加密和文件共享以及数据库 - 更新聊天室请求模型"""
    name: Optional[str] = None
    description: Optional[str] = None
    is_private: Optional[bool] = None
    max_members: Optional[int] = None
    category: Optional[str] = None

class AddMemberRequest(BaseModel):
    """20250618.20:30 增加加密和文件共享以及数据库 - 添加成员请求模型"""
    user_id: str
    role: str = "member"  # member, moderator, admin

@router.get("/")
async def get_rooms(
    category: Optional[str] = Query(None),
    is_private: Optional[bool] = Query(None),
    limit: int = Query(50, ge=1, le=100),
    offset: int = Query(0, ge=0),
    user: dict = Depends(get_current_user)
):
    """20250618.20:30 增加加密和文件共享以及数据库 - 获取聊天室列表"""
    try:
        # 预设聊天室数据
        default_rooms = [
            {
                "room_id": "room_general",
                "name": "通用聊天室",
                "description": "所有员工都可以参与的通用聊天室",
                "category": "general",
                "is_private": False,
                "max_members": 200,
                "member_count": 50,
                "created_by": "system",
                "created_at": "2025-06-18T20:30:00Z",
                "is_member": True,
                "user_role": "member"
            },
            {
                "room_id": "room_work",
                "name": "工作讨论",
                "description": "工作相关问题讨论",
                "category": "work",
                "is_private": False,
                "max_members": 100,
                "member_count": 30,
                "created_by": "system",
                "created_at": "2025-06-18T20:30:00Z",
                "is_member": True,
                "user_role": "member"
            },
            {
                "room_id": "room_project_a",
                "name": "项目A讨论组",
                "description": "项目A相关讨论",
                "category": "project",
                "is_private": True,
                "max_members": 20,
                "member_count": 8,
                "created_by": "system",
                "created_at": "2025-06-18T20:30:00Z",
                "is_member": True,
                "user_role": "member"
            }
        ]
        
        # 应用过滤条件
        filtered_rooms = default_rooms
        if category:
            filtered_rooms = [r for r in filtered_rooms if r["category"] == category]
        if is_private is not None:
            filtered_rooms = [r for r in filtered_rooms if r["is_private"] == is_private]
        
        # 分页
        paginated = filtered_rooms[offset:offset + limit]
        
        return {
            "status": "success",
            "data": {
                "rooms": paginated,
                "total": len(filtered_rooms),
                "limit": limit,
                "offset": offset
            }
        }
        
    except Exception as e:
        logger.error(f"Error getting rooms: {e}")
        raise HTTPException(status_code=500, detail=f"获取聊天室列表失败: {str(e)}")

@router.post("/")
async def create_room(
    room_data: CreateRoomRequest,
    user: dict = Depends(get_current_user)
):
    """20250618.20:30 增加加密和文件共享以及数据库 - 创建聊天室"""
    try:
        user_id = user["employee_id"]
        user_name = user["employee_name"]
        
        # 检查是否有创建权限
        is_admin = user_id in settings.ADMIN_EMPLOYEE_IDS
        if not is_admin and room_data.is_private:
            # 非管理员只能创建公开聊天室，或者有特定权限
            pass  # 这里可以添加更复杂的权限检查
        
        async with database_service.get_session() as session:
            from ..core.services.database_service import ChatRoom, RoomMember
            import uuid
            
            # 生成聊天室ID
            room_id = f"room_{uuid.uuid4().hex}"
            
            # 创建聊天室
            new_room = ChatRoom(
                room_id=room_id,
                name=room_data.name,
                description=room_data.description,
                category=room_data.category,
                is_private=room_data.is_private,
                max_members=room_data.max_members,
                created_by=user_id
            )
            
            session.add(new_room)
            await session.flush()  # 获取生成的ID
            
            # 将创建者添加为管理员
            creator_member = RoomMember(
                room_id=room_id,
                user_id=user_id,
                user_name=user_name,
                role="admin",
                joined_by=user_id
            )
            
            session.add(creator_member)
            await session.commit()
            
            # 更新Redis缓存
            await _update_room_cache(room_id)
            
            logger.info(f"Room created: {room_data.name} by {user_name}")
            
            return {
                "status": "success",
                "message": "聊天室创建成功",
                "data": {
                    "room_id": room_id,
                    "name": room_data.name,
                    "created_at": datetime.utcnow().isoformat()
                }
            }
            
    except Exception as e:
        logger.error(f"Error creating room: {e}")
        raise HTTPException(status_code=500, detail=f"创建聊天室失败: {str(e)}")

@router.get("/{room_id}")
async def get_room_info(
    room_id: str,
    user: dict = Depends(get_current_user)
):
    """20250618.20:30 增加加密和文件共享以及数据库 - 获取聊天室详细信息"""
    try:
        user_id = user["employee_id"]
        
        async with database_service.get_session() as session:
            from sqlalchemy import select, and_
            from ..core.services.database_service import ChatRoom, RoomMember
            
            # 获取聊天室信息
            room_query = select(ChatRoom).where(
                and_(
                    ChatRoom.room_id == room_id,
                    ChatRoom.is_deleted == False
                )
            )
            room_result = await session.execute(room_query)
            room = room_result.scalar()
            
            if not room:
                raise HTTPException(status_code=404, detail="聊天室不存在")
            
            # 检查访问权限
            if room.is_private:
                member_query = select(RoomMember).where(
                    and_(
                        RoomMember.room_id == room_id,
                        RoomMember.user_id == user_id,
                        RoomMember.is_active == True
                    )
                )
                member_result = await session.execute(member_query)
                if not member_result.scalar():
                    raise HTTPException(status_code=403, detail="无权限访问此聊天室")
            
            # 获取成员列表
            members_query = select(RoomMember).where(
                and_(
                    RoomMember.room_id == room_id,
                    RoomMember.is_active == True
                )
            ).order_by(RoomMember.joined_at.desc())
            
            members_result = await session.execute(members_query)
            members = members_result.scalars().all()
            
            member_list = [
                {
                    "user_id": member.user_id,
                    "user_name": member.user_name,
                    "role": member.role,
                    "joined_at": member.joined_at.isoformat(),
                    "last_active": member.last_active.isoformat() if member.last_active else None
                }
                for member in members
            ]
            
            # 获取用户在此聊天室的角色
            user_member = next((m for m in members if m.user_id == user_id), None)
            user_role = user_member.role if user_member else None
            
            room_info = {
                "room_id": room.room_id,
                "name": room.name,
                "description": room.description,
                "category": room.category,
                "is_private": room.is_private,
                "max_members": room.max_members,
                "member_count": len(member_list),
                "created_by": room.created_by,
                "created_at": room.created_at.isoformat(),
                "updated_at": room.updated_at.isoformat() if room.updated_at else None,
                "members": member_list,
                "user_role": user_role
            }
            
            return {
                "status": "success",
                "data": room_info
            }
            
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error getting room info: {e}")
        raise HTTPException(status_code=500, detail=f"获取聊天室信息失败: {str(e)}")

@router.put("/{room_id}")
async def update_room(
    room_id: str,
    room_data: UpdateRoomRequest,
    user: dict = Depends(get_current_user)
):
    """20250618.20:30 增加加密和文件共享以及数据库 - 更新聊天室信息"""
    try:
        user_id = user["employee_id"]
        
        async with database_service.get_session() as session:
            from sqlalchemy import select, and_
            from ..core.services.database_service import ChatRoom, RoomMember
            
            # 获取聊天室
            room_query = select(ChatRoom).where(
                and_(
                    ChatRoom.room_id == room_id,
                    ChatRoom.is_deleted == False
                )
            )
            room_result = await session.execute(room_query)
            room = room_result.scalar()
            
            if not room:
                raise HTTPException(status_code=404, detail="聊天室不存在")
            
            # 检查权限（管理员或聊天室创建者）
            is_admin = user_id in settings.ADMIN_EMPLOYEE_IDS
            is_room_admin = False
            
            if not is_admin:
                member_query = select(RoomMember).where(
                    and_(
                        RoomMember.room_id == room_id,
                        RoomMember.user_id == user_id,
                        RoomMember.is_active == True,
                        RoomMember.role.in_(["admin", "moderator"])
                    )
                )
                member_result = await session.execute(member_query)
                is_room_admin = member_result.scalar() is not None
            
            if not is_admin and not is_room_admin:
                raise HTTPException(status_code=403, detail="需要管理员权限")
            
            # 更新字段
            if room_data.name is not None:
                room.name = room_data.name
            if room_data.description is not None:
                room.description = room_data.description
            if room_data.category is not None:
                room.category = room_data.category
            if room_data.is_private is not None:
                room.is_private = room_data.is_private
            if room_data.max_members is not None:
                room.max_members = room_data.max_members
            
            room.updated_at = datetime.utcnow()
            
            await session.commit()
            
            # 更新Redis缓存
            await _update_room_cache(room_id)
            
            logger.info(f"Room updated: {room_id} by {user_id}")
            
            return {
                "status": "success",
                "message": "聊天室信息更新成功"
            }
            
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error updating room: {e}")
        raise HTTPException(status_code=500, detail=f"更新聊天室失败: {str(e)}")

@router.delete("/{room_id}")
async def delete_room(
    room_id: str,
    user: dict = Depends(get_current_user)
):
    """20250618.20:30 增加加密和文件共享以及数据库 - 删除聊天室"""
    try:
        user_id = user["employee_id"]
        
        async with database_service.get_session() as session:
            from sqlalchemy import select, and_
            from ..core.services.database_service import ChatRoom, RoomMember
            
            # 获取聊天室
            room_query = select(ChatRoom).where(
                and_(
                    ChatRoom.room_id == room_id,
                    ChatRoom.is_deleted == False
                )
            )
            room_result = await session.execute(room_query)
            room = room_result.scalar()
            
            if not room:
                raise HTTPException(status_code=404, detail="聊天室不存在")
            
            # 检查权限（只有系统管理员或聊天室创建者可以删除）
            is_admin = user_id in settings.ADMIN_EMPLOYEE_IDS
            is_creator = room.created_by == user_id
            
            if not is_admin and not is_creator:
                raise HTTPException(status_code=403, detail="只有管理员或创建者可以删除聊天室")
            
            # 软删除聊天室
            room.is_deleted = True
            room.updated_at = datetime.utcnow()
            
            # 将所有成员标记为非活跃
            members_query = select(RoomMember).where(
                and_(
                    RoomMember.room_id == room_id,
                    RoomMember.is_active == True
                )
            )
            members_result = await session.execute(members_query)
            members = members_result.scalars().all()
            
            for member in members:
                member.is_active = False
                member.left_at = datetime.utcnow()
            
            await session.commit()
            
            # 清除Redis缓存
            await _clear_room_cache(room_id)
            
            logger.info(f"Room deleted: {room_id} by {user_id}")
            
            return {
                "status": "success",
                "message": "聊天室删除成功"
            }
            
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error deleting room: {e}")
        raise HTTPException(status_code=500, detail=f"删除聊天室失败: {str(e)}")

@router.post("/{room_id}/members")
async def add_member(
    room_id: str,
    member_data: AddMemberRequest,
    user: dict = Depends(get_current_user)
):
    """20250618.20:30 增加加密和文件共享以及数据库 - 添加聊天室成员"""
    try:
        user_id = user["employee_id"]
        
        async with database_service.get_session() as session:
            from sqlalchemy import select, and_
            from ..core.services.database_service import ChatRoom, RoomMember
            
            # 获取聊天室
            room_query = select(ChatRoom).where(
                and_(
                    ChatRoom.room_id == room_id,
                    ChatRoom.is_deleted == False
                )
            )
            room_result = await session.execute(room_query)
            room = room_result.scalar()
            
            if not room:
                raise HTTPException(status_code=404, detail="聊天室不存在")
            
            # 检查权限
            is_admin = user_id in settings.ADMIN_EMPLOYEE_IDS
            is_room_admin = False
            
            if not is_admin:
                member_query = select(RoomMember).where(
                    and_(
                        RoomMember.room_id == room_id,
                        RoomMember.user_id == user_id,
                        RoomMember.is_active == True,
                        RoomMember.role.in_(["admin", "moderator"])
                    )
                )
                member_result = await session.execute(member_query)
                is_room_admin = member_result.scalar() is not None
            
            if not is_admin and not is_room_admin:
                raise HTTPException(status_code=403, detail="需要管理员权限")
            
            # 检查成员是否已存在
            existing_member_query = select(RoomMember).where(
                and_(
                    RoomMember.room_id == room_id,
                    RoomMember.user_id == member_data.user_id,
                    RoomMember.is_active == True
                )
            )
            existing_member_result = await session.execute(existing_member_query)
            if existing_member_result.scalar():
                raise HTTPException(status_code=400, detail="用户已是聊天室成员")
            
            # 检查聊天室成员数量限制
            current_members_query = select(RoomMember).where(
                and_(
                    RoomMember.room_id == room_id,
                    RoomMember.is_active == True
                )
            )
            current_members_result = await session.execute(current_members_query)
            current_count = len(current_members_result.scalars().all())
            
            if current_count >= room.max_members:
                raise HTTPException(status_code=400, detail="聊天室成员已满")
            
            # 这里应该验证用户存在，暂时跳过
            # user_name = await _get_user_name(member_data.user_id)
            user_name = f"User_{member_data.user_id}"  # 临时处理
            
            # 添加成员
            new_member = RoomMember(
                room_id=room_id,
                user_id=member_data.user_id,
                user_name=user_name,
                role=member_data.role,
                joined_by=user_id
            )
            
            session.add(new_member)
            await session.commit()
            
            # 更新Redis缓存
            await _update_room_cache(room_id)
            
            logger.info(f"Member added to room {room_id}: {member_data.user_id} by {user_id}")
            
            return {
                "status": "success",
                "message": "成员添加成功",
                "data": {
                    "user_id": member_data.user_id,
                    "role": member_data.role,
                    "joined_at": datetime.utcnow().isoformat()
                }
            }
            
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error adding member: {e}")
        raise HTTPException(status_code=500, detail=f"添加成员失败: {str(e)}")

@router.delete("/{room_id}/members/{member_user_id}")
async def remove_member(
    room_id: str,
    member_user_id: str,
    user: dict = Depends(get_current_user)
):
    """20250618.20:30 增加加密和文件共享以及数据库 - 移除聊天室成员"""
    try:
        user_id = user["employee_id"]
        
        async with database_service.get_session() as session:
            from sqlalchemy import select, and_
            from ..core.services.database_service import ChatRoom, RoomMember
            
            # 获取聊天室
            room_query = select(ChatRoom).where(
                and_(
                    ChatRoom.room_id == room_id,
                    ChatRoom.is_deleted == False
                )
            )
            room_result = await session.execute(room_query)
            room = room_result.scalar()
            
            if not room:
                raise HTTPException(status_code=404, detail="聊天室不存在")
            
            # 获取要移除的成员
            target_member_query = select(RoomMember).where(
                and_(
                    RoomMember.room_id == room_id,
                    RoomMember.user_id == member_user_id,
                    RoomMember.is_active == True
                )
            )
            target_member_result = await session.execute(target_member_query)
            target_member = target_member_result.scalar()
            
            if not target_member:
                raise HTTPException(status_code=404, detail="成员不存在")
            
            # 检查权限
            is_admin = user_id in settings.ADMIN_EMPLOYEE_IDS
            is_self_leaving = user_id == member_user_id
            is_room_admin = False
            
            if not is_admin and not is_self_leaving:
                member_query = select(RoomMember).where(
                    and_(
                        RoomMember.room_id == room_id,
                        RoomMember.user_id == user_id,
                        RoomMember.is_active == True,
                        RoomMember.role.in_(["admin", "moderator"])
                    )
                )
                member_result = await session.execute(member_query)
                is_room_admin = member_result.scalar() is not None
            
            if not is_admin and not is_self_leaving and not is_room_admin:
                raise HTTPException(status_code=403, detail="需要管理员权限")
            
            # 不能移除聊天室创建者（除非是系统管理员）
            if target_member.user_id == room.created_by and not is_admin:
                raise HTTPException(status_code=403, detail="不能移除聊天室创建者")
            
            # 移除成员
            target_member.is_active = False
            target_member.left_at = datetime.utcnow()
            
            await session.commit()
            
            # 更新Redis缓存
            await _update_room_cache(room_id)
            
            action = "left" if is_self_leaving else "removed"
            logger.info(f"Member {action} room {room_id}: {member_user_id} by {user_id}")
            
            return {
                "status": "success",
                "message": "成员移除成功" if not is_self_leaving else "已退出聊天室"
            }
            
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error removing member: {e}")
        raise HTTPException(status_code=500, detail=f"移除成员失败: {str(e)}")

@router.get("/categories")
async def get_room_categories(user: dict = Depends(get_current_user)):
    """20250618.20:30 增加加密和文件共享以及数据库 - 获取聊天室分类"""
    try:
        categories = [
            {"id": "general", "name": "通用", "description": "通用聊天室"},
            {"id": "work", "name": "工作", "description": "工作相关讨论"},
            {"id": "project", "name": "项目", "description": "项目协作"},
            {"id": "department", "name": "部门", "description": "部门内部交流"},
            {"id": "announcement", "name": "公告", "description": "重要公告通知"},
            {"id": "social", "name": "社交", "description": "休闲社交"}
        ]
        
        return {
            "status": "success",
            "data": {
                "categories": categories
            }
        }
        
    except Exception as e:
        logger.error(f"Error getting room categories: {e}")
        raise HTTPException(status_code=500, detail=f"获取分类失败: {str(e)}")

# 20250618.20:30 增加加密和文件共享以及数据库 - 辅助函数

async def _update_room_cache(room_id: str):
    """更新聊天室Redis缓存"""
    try:
        # 这里应该实现缓存更新逻辑
        # 暂时记录日志
        logger.info(f"Updating room cache: {room_id}")
    except Exception as e:
        logger.error(f"Error updating room cache: {e}")

async def _clear_room_cache(room_id: str):
    """清除聊天室Redis缓存"""
    try:
        # 这里应该实现缓存清除逻辑
        logger.info(f"Clearing room cache: {room_id}")
    except Exception as e:
        logger.error(f"Error clearing room cache: {e}")

async def _get_user_name(user_id: str) -> str:
    """获取用户名称"""
    try:
        # 这里应该查询用户数据库获取真实用户名
        # 暂时返回临时名称
        return f"User_{user_id}"
    except Exception:
        return f"User_{user_id}" 