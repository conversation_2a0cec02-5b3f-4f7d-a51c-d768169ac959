# server2/init_database.py
# 20250618.20:30 增加加密和文件共享以及数据库 - 数据库初始化脚本
"""
数据库初始化脚本 - 创建表和插入预设数据
"""

import asyncio
import logging
from datetime import datetime
from app.core.services.database_service import database_service, ChatRoom, RoomMember, FileShare

# 配置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

async def init_database():
    """20250618.20:30 增加加密和文件共享以及数据库 - 初始化数据库"""
    try:
        logger.info("Starting database initialization...")
        
        # 初始化数据库连接
        await database_service.initialize()
        
        # 创建所有表
        await database_service.create_tables()
        logger.info("Database tables created successfully")
        
        # 插入预设聊天室
        await insert_default_rooms()
        logger.info("Default chat rooms inserted successfully")
        
        # 插入预设成员
        await insert_default_members()
        logger.info("Default members inserted successfully")
        
        logger.info("Database initialization completed successfully!")
        
    except Exception as e:
        logger.error(f"Database initialization failed: {e}")
        raise e
    finally:
        await database_service.close()

async def insert_default_rooms():
    """20250618.20:30 增加加密和文件共享以及数据库 - 插入预设聊天室"""
    default_rooms = [
        {
            "room_id": "room_general",
            "name": "通用聊天室",
            "description": "所有员工都可以参与的通用聊天室",
            "category": "general",
            "is_private": False,
            "max_members": 200,
            "created_by": "system"
        },
        {
            "room_id": "room_work",
            "name": "工作讨论",
            "description": "工作相关问题讨论和协作",
            "category": "work",
            "is_private": False,
            "max_members": 100,
            "created_by": "system"
        },
        {
            "room_id": "room_project_a",
            "name": "项目A讨论组",
            "description": "项目A相关讨论和进度跟踪",
            "category": "project",
            "is_private": True,
            "max_members": 20,
            "created_by": "system"
        },
        {
            "room_id": "room_department_tech",
            "name": "技术部门",
            "description": "技术部门内部交流",
            "category": "department",
            "is_private": True,
            "max_members": 50,
            "created_by": "system"
        },
        {
            "room_id": "room_announcement",
            "name": "公告通知",
            "description": "重要公告和通知发布",
            "category": "announcement",
            "is_private": False,
            "max_members": 500,
            "created_by": "system"
        },
        {
            "room_id": "room_social",
            "name": "休闲社交",
            "description": "员工休闲交流和社交活动",
            "category": "social",
            "is_private": False,
            "max_members": 200,
            "created_by": "system"
        }
    ]
    
    async with database_service.get_session() as session:
        for room_data in default_rooms:
            # 检查聊天室是否已存在
            from sqlalchemy import select
            existing_query = select(ChatRoom).where(ChatRoom.room_id == room_data["room_id"])
            existing_result = await session.execute(existing_query)
            if existing_result.scalar():
                logger.info(f"Room {room_data['room_id']} already exists, skipping...")
                continue
            
            # 创建新聊天室
            room = ChatRoom(**room_data)
            session.add(room)
            logger.info(f"Created room: {room_data['name']}")
        
        await session.commit()

async def insert_default_members():
    """20250618.20:30 增加加密和文件共享以及数据库 - 插入预设成员"""
    # 预设成员数据（示例用户）
    default_members = [
        # 通用聊天室成员
        {"room_id": "room_general", "user_id": "emp001", "user_name": "张三", "role": "admin"},
        {"room_id": "room_general", "user_id": "emp002", "user_name": "李四", "role": "moderator"},
        {"room_id": "room_general", "user_id": "emp003", "user_name": "王五", "role": "member"},
        {"room_id": "room_general", "user_id": "emp004", "user_name": "赵六", "role": "member"},
        
        # 工作讨论成员
        {"room_id": "room_work", "user_id": "emp001", "user_name": "张三", "role": "admin"},
        {"room_id": "room_work", "user_id": "emp002", "user_name": "李四", "role": "member"},
        {"room_id": "room_work", "user_id": "emp005", "user_name": "钱七", "role": "member"},
        
        # 项目A讨论组成员
        {"room_id": "room_project_a", "user_id": "emp001", "user_name": "张三", "role": "admin"},
        {"room_id": "room_project_a", "user_id": "emp003", "user_name": "王五", "role": "member"},
        {"room_id": "room_project_a", "user_id": "emp006", "user_name": "孙八", "role": "member"},
        
        # 技术部门成员
        {"room_id": "room_department_tech", "user_id": "emp007", "user_name": "周九", "role": "admin"},
        {"room_id": "room_department_tech", "user_id": "emp008", "user_name": "吴十", "role": "moderator"},
        {"room_id": "room_department_tech", "user_id": "emp009", "user_name": "郑十一", "role": "member"},
        
        # 公告通知（系统管理员）
        {"room_id": "room_announcement", "user_id": "system", "user_name": "系统管理员", "role": "admin"},
        {"room_id": "room_announcement", "user_id": "emp001", "user_name": "张三", "role": "moderator"},
        
        # 休闲社交成员
        {"room_id": "room_social", "user_id": "emp002", "user_name": "李四", "role": "admin"},
        {"room_id": "room_social", "user_id": "emp003", "user_name": "王五", "role": "member"},
        {"room_id": "room_social", "user_id": "emp004", "user_name": "赵六", "role": "member"},
        {"room_id": "room_social", "user_id": "emp005", "user_name": "钱七", "role": "member"}
    ]
    
    async with database_service.get_session() as session:
        for member_data in default_members:
            # 检查成员是否已存在
            from sqlalchemy import select, and_
            existing_query = select(RoomMember).where(
                and_(
                    RoomMember.room_id == member_data["room_id"],
                    RoomMember.user_id == member_data["user_id"]
                )
            )
            existing_result = await session.execute(existing_query)
            if existing_result.scalar():
                logger.info(f"Member {member_data['user_id']} in room {member_data['room_id']} already exists, skipping...")
                continue
            
            # 创建新成员
            member = RoomMember(
                room_id=member_data["room_id"],
                user_id=member_data["user_id"],
                user_name=member_data["user_name"],
                role=member_data["role"],
                joined_by="system"
            )
            session.add(member)
            logger.info(f"Added member: {member_data['user_name']} to room {member_data['room_id']}")
        
        await session.commit()

async def reset_database():
    """20250618.20:30 增加加密和文件共享以及数据库 - 重置数据库（谨慎使用）"""
    try:
        logger.warning("Starting database reset...")
        
        await database_service.initialize()
        
        # 删除所有表
        await database_service.drop_tables()
        logger.info("All tables dropped")
        
        # 重新创建表
        await database_service.create_tables()
        logger.info("Tables recreated")
        
        # 插入默认数据
        await insert_default_rooms()
        await insert_default_members()
        
        logger.info("Database reset completed successfully!")
        
    except Exception as e:
        logger.error(f"Database reset failed: {e}")
        raise e
    finally:
        await database_service.close()

if __name__ == "__main__":
    import sys
    
    if len(sys.argv) > 1 and sys.argv[1] == "reset":
        print("WARNING: This will delete all existing data!")
        confirm = input("Are you sure you want to reset the database? (yes/no): ")
        if confirm.lower() == "yes":
            asyncio.run(reset_database())
        else:
            print("Database reset cancelled.")
    else:
        asyncio.run(init_database()) 