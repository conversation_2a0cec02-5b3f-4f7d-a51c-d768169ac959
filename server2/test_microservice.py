# server2/test_microservice.py
# 20250618.20:15 微服务-信息交流 - 微服务器测试脚本
"""
信息交流微服务器测试脚本
用于验证微服务器是否正常运行
"""

import asyncio
import aiohttp
import json
import websockets
import ssl
from datetime import datetime

# 20250618.20:15 微服务-信息交流 - 测试配置
BASE_URL = "http://localhost:8005"
WS_URL = "ws://localhost:8005/ws/chat"
TEST_TOKEN = "test_token_for_development"  # 在实际环境中需要真实的JWT

async def test_health_check():
    """20250618.20:15 微服务-信息交流 - 测试健康检查端点"""
    print("🔍 Testing health check...")
    try:
        async with aiohttp.ClientSession() as session:
            async with session.get(f"{BASE_URL}/health") as response:
                data = await response.json()
                print(f"✅ Health check: {data['status']}")
                print(f"   Service: {data.get('service', 'Unknown')}")
                print(f"   Checks: {data.get('checks', {})}")
                return response.status == 200
    except Exception as e:
        print(f"❌ Health check failed: {e}")
        return False

async def test_service_status():
    """20250618.20:15 微服务-信息交流 - 测试服务状态端点"""
    print("🔍 Testing service status...")
    try:
        async with aiohttp.ClientSession() as session:
            async with session.get(f"{BASE_URL}/api/chat/status") as response:
                if response.status == 200:
                    data = await response.json()
                    print(f"✅ Service status: {data.get('status', 'Unknown')}")
                    print(f"   Port: {data.get('port', 'Unknown')}")
                    print(f"   Connections: {data.get('connections', {})}")
                    return True
                else:
                    print(f"❌ Service status failed: HTTP {response.status}")
                    return False
    except Exception as e:
        print(f"❌ Service status failed: {e}")
        return False

async def test_websocket_connection():
    """20250618.20:15 微服务-信息交流 - 测试WebSocket连接"""
    print("🔍 Testing WebSocket connection...")
    try:
        # 注意：在实际测试中需要真实的JWT token
        ws_url_with_token = f"{WS_URL}?token={TEST_TOKEN}"
        
        async with websockets.connect(
            ws_url_with_token,
            ping_interval=None,  # 禁用ping以简化测试
            close_timeout=5
        ) as websocket:
            print("✅ WebSocket connected successfully")
            
            # 发送测试消息
            test_message = {
                "type": "chat_message",
                "message": "Hello from test script!",
                "timestamp": datetime.now().isoformat()
            }
            
            await websocket.send(json.dumps(test_message))
            print("📤 Test message sent")
            
            # 等待响应（如果有的话）
            try:
                response = await asyncio.wait_for(websocket.recv(), timeout=2.0)
                data = json.loads(response)
                print(f"📥 Received response: {data.get('type', 'unknown')}")
                return True
            except asyncio.TimeoutError:
                print("⚠️  No response received (this might be expected without valid auth)")
                return True
                
    except websockets.exceptions.ConnectionClosed as e:
        print(f"⚠️  WebSocket connection closed: {e}")
        return False
    except Exception as e:
        print(f"❌ WebSocket test failed: {e}")
        return False

async def test_api_endpoints():
    """20250618.20:15 微服务-信息交流 - 测试REST API端点"""
    print("🔍 Testing REST API endpoints...")
    
    endpoints = [
        ("/", "GET", "Root endpoint"),
        ("/docs", "GET", "API documentation"),
        ("/api/chat/users/online", "GET", "Online users"),
        ("/api/chat/history", "GET", "Chat history"),
        ("/api/chat/stats", "GET", "Chat statistics"),
        ("/api/chat/rooms", "GET", "Chat rooms")
    ]
    
    results = []
    
    async with aiohttp.ClientSession() as session:
        for endpoint, method, description in endpoints:
            try:
                url = f"{BASE_URL}{endpoint}"
                async with session.request(method, url) as response:
                    status = response.status
                    if status == 200:
                        print(f"✅ {description}: HTTP {status}")
                        results.append(True)
                    elif status == 401:
                        print(f"🔐 {description}: HTTP {status} (Authentication required)")
                        results.append(True)  # 401 is expected for protected endpoints
                    else:
                        print(f"⚠️  {description}: HTTP {status}")
                        results.append(False)
            except Exception as e:
                print(f"❌ {description}: {e}")
                results.append(False)
    
    return all(results)

async def main():
    """20250618.20:15 微服务-信息交流 - 主测试函数"""
    print("=" * 60)
    print("🧪 MySuite Chat Microservice Test Suite")
    print("=" * 60)
    print(f"🎯 Target: {BASE_URL}")
    print(f"🔗 WebSocket: {WS_URL}")
    print(f"⏰ Test time: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print("-" * 60)
    
    test_results = []
    
    # 1. 健康检查
    health_ok = await test_health_check()
    test_results.append(("Health Check", health_ok))
    print()
    
    # 2. 服务状态
    status_ok = await test_service_status()
    test_results.append(("Service Status", status_ok))
    print()
    
    # 3. REST API端点
    api_ok = await test_api_endpoints()
    test_results.append(("REST API Endpoints", api_ok))
    print()
    
    # 4. WebSocket连接
    ws_ok = await test_websocket_connection()
    test_results.append(("WebSocket Connection", ws_ok))
    print()
    
    # 总结
    print("=" * 60)
    print("📊 Test Results Summary")
    print("=" * 60)
    
    passed = 0
    total = len(test_results)
    
    for test_name, result in test_results:
        status = "✅ PASS" if result else "❌ FAIL"
        print(f"{status:<8} {test_name}")
        if result:
            passed += 1
    
    print("-" * 60)
    print(f"📈 Overall: {passed}/{total} tests passed ({passed/total*100:.1f}%)")
    
    if passed == total:
        print("🎉 All tests passed! Microservice is working correctly.")
        return 0
    else:
        print("⚠️  Some tests failed. Please check the microservice configuration.")
        return 1

def run_tests():
    """20250618.20:15 微服务-信息交流 - 同步运行测试"""
    try:
        return asyncio.run(main())
    except KeyboardInterrupt:
        print("\n🛑 Tests interrupted by user")
        return 1
    except Exception as e:
        print(f"\n❌ Test suite error: {e}")
        return 1

if __name__ == "__main__":
    import sys
    
    print("🚀 Starting microservice tests...")
    print("💡 Make sure the chat microservice is running on port 8005")
    print("   Use: python start_chat_service.py")
    print()
    
    exit_code = run_tests()
    sys.exit(exit_code) 