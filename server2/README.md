# MySuite 信息交流微服务器

## 20250618.20:15 微服务-信息交流 - 独立的聊天微服务器

### 概述

这是 MySuite 系统的信息交流微服务器，专门处理实时聊天和用户交流功能。该微服务器从主服务器中拆分出来，提供更好的扩展性和维护性。

### 功能特性

- 🚀 **高性能**: 支持 500+ 并发WebSocket连接
- 💬 **实时聊天**: 基于WebSocket的实时消息传输
- 👥 **用户管理**: 在线用户状态管理和显示
- 📝 **消息历史**: 聊天记录存储和查询
- 🏠 **聊天室**: 多聊天室支持
- 🔒 **安全认证**: JWT令牌认证
- 📊 **监控统计**: 连接状态和消息统计
- 🛠️ **管理功能**: 系统广播、用户管理等

### 技术栈

- **框架**: FastAPI + WebSockets
- **缓存**: Redis (数据库1)
- **认证**: JWT (与主服务器共享密钥)
- **协议**: HTTP/WebSocket
- **端口**: 8005

### 目录结构

```
server2/
├── app/                          # 应用核心代码
│   ├── __init__.py
│   ├── main.py                   # 主应用文件
│   ├── config.py                 # 配置文件
│   ├── auth/                     # 认证模块
│   │   ├── __init__.py
│   │   └── jwt_auth.py          # JWT认证处理
│   ├── core/                     # 核心服务
│   │   ├── __init__.py
│   │   └── services/
│   │       ├── __init__.py
│   │       └── redis_service.py  # Redis服务
│   └── routers/                  # 路由模块
│       ├── __init__.py
│       ├── chat_websocket.py     # WebSocket聊天路由
│       └── chat_management.py    # REST API管理路由
├── start_chat_service.py         # 启动脚本
├── test_microservice.py          # 测试脚本
├── environment_unified.yml              # 依赖文件
└── README.md                     # 本文档
```

### 快速开始

#### 1. 安装依赖

```bash
cd server2
pip install -r requirements.txt
```

#### 2. 配置环境

确保Redis服务器正在运行：

```bash
# Ubuntu/Debian
sudo systemctl start redis-server

# Windows (如果使用WSL)
redis-server

# macOS
brew services start redis
```

#### 3. 启动微服务器

```bash
# 开发模式
python start_chat_service.py

# 或直接使用uvicorn
uvicorn app.main:app --host 0.0.0.0 --port 8005 --reload
```

#### 4. 验证服务

```bash
# 运行测试套件
python test_microservice.py

# 或手动检查
curl http://localhost:8005/health
curl http://localhost:8005/api/chat/status
```

### API 端点

#### 基础端点

- `GET /` - 服务器状态信息
- `GET /health` - 健康检查
- `GET /docs` - API文档 (Swagger UI)

#### WebSocket 端点

- `WS /ws/chat?token={jwt_token}` - 聊天WebSocket连接

#### REST API 端点

| 端点 | 方法 | 描述 | 认证 |
|------|------|------|------|
| `/api/chat/status` | GET | 获取服务详细状态 | 可选 |
| `/api/chat/users/online` | GET | 获取在线用户列表 | 必需 |
| `/api/chat/history` | GET | 获取聊天历史记录 | 必需 |
| `/api/chat/stats` | GET | 获取聊天统计信息 | 必需 |
| `/api/chat/broadcast` | POST | 发送系统广播消息 | 必需 |
| `/api/chat/user/{id}/disconnect` | DELETE | 强制断开用户连接 | 必需 |
| `/api/chat/rooms` | GET | 获取聊天室信息 | 必需 |

### WebSocket 消息格式

#### 客户端发送消息

```json
{
  "type": "chat_message",
  "message": "Hello, world!",
  "timestamp": "2025-06-18T20:15:00.000Z"
}
```

#### 服务器响应消息

```json
{
  "type": "chat_message",
  "message": "Hello, world!",
  "timestamp": "2025-06-18T20:15:00.000Z",
  "user_id": "emp001",
  "user_name": "张三",
  "message_id": "msg_1718734500000"
}
```

#### 消息类型

- `chat_message` - 普通聊天消息
- `system_message` - 系统消息
- `user_list_update` - 用户列表更新
- `private_message` - 私聊消息
- `chat_history` - 聊天历史
- `pong` - 心跳响应

### 配置选项

在 `app/config.py` 中可以配置：

```python
# 服务器配置
SERVICE_PORT = 8005
SERVICE_HOST = "0.0.0.0"

# JWT配置（需与主服务器一致）
JWT_SECRET_KEY = "your-secret-key"
JWT_ALGORITHM = "HS256"

# Redis配置
REDIS_HOST = "localhost"
REDIS_PORT = 6379
REDIS_DB = 1  # 使用独立的数据库

# 聊天配置
CHAT_MAX_CONNECTIONS = 500
CHAT_MAX_MESSAGE_LENGTH = 1000
CHAT_MAX_HISTORY_SIZE = 1000
CHAT_HEARTBEAT_INTERVAL = 30
CHAT_CONNECTION_TIMEOUT = 300
```

### 监控和维护

#### 健康检查

```bash
# 基础健康检查
curl http://localhost:8005/health

# 详细状态检查
curl http://localhost:8005/api/chat/status
```

#### 日志监控

微服务器使用结构化日志，可以通过以下方式查看：

```bash
# 如果使用systemd
journalctl -u mysuite-chat -f

# 如果直接运行
# 日志会输出到控制台
```

#### 性能监控

- **连接数**: 通过 `/api/chat/status` 查看当前连接数
- **消息统计**: 通过 `/api/chat/stats` 查看消息统计
- **Redis状态**: 通过健康检查端点查看Redis连接状态

### 开发和调试

#### 开发模式

```bash
# 启用自动重载
python start_chat_service.py

# 或使用uvicorn
uvicorn app.main:app --reload --port 8005
```

#### 调试WebSocket

可以使用浏览器开发者工具或WebSocket客户端：

```javascript
// 浏览器控制台中测试
const ws = new WebSocket('ws://localhost:8005/ws/chat?token=your_jwt_token');
ws.onmessage = (event) => console.log(JSON.parse(event.data));
ws.send(JSON.stringify({
  type: 'chat_message',
  message: 'Test message',
  timestamp: new Date().toISOString()
}));
```

#### 测试套件

运行完整的测试套件：

```bash
python test_microservice.py
```

### 部署

#### 生产环境部署

1. **使用systemd服务**:

```ini
# /etc/systemd/system/mysuite-chat.service
[Unit]
Description=MySuite Chat Microservice
After=network.target redis.service

[Service]
Type=simple
User=mysuite
WorkingDirectory=/path/to/server2
ExecStart=/path/to/venv/bin/uvicorn app.main:app --host 0.0.0.0 --port 8005
Restart=always
RestartSec=5

[Install]
WantedBy=multi-user.target
```

2. **使用Docker**:

```dockerfile
FROM python:3.11-slim

WORKDIR /app
COPY requirements.txt .
RUN pip install -r requirements.txt

COPY app/ ./app/
EXPOSE 8005

CMD ["uvicorn", "app.main:app", "--host", "0.0.0.0", "--port", "8005"]
```

3. **使用Nginx反向代理**:

```nginx
upstream chat_backend {
    server 127.0.0.1:8005;
}

server {
    listen 80;
    server_name chat.yourdomain.com;

    location / {
        proxy_pass http://chat_backend;
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection "upgrade";
        proxy_set_header Host $host;
    }
}
```

### 故障排除

#### 常见问题

1. **Redis连接失败**
   - 检查Redis服务是否运行
   - 验证Redis配置（主机、端口、密码）
   - 检查防火墙设置

2. **WebSocket连接被拒绝**
   - 验证JWT令牌是否有效
   - 检查令牌是否过期
   - 确认JWT密钥与主服务器一致

3. **端口冲突**
   - 检查端口8005是否被占用
   - 修改配置文件中的端口设置

4. **性能问题**
   - 检查Redis性能
   - 监控内存使用情况
   - 考虑增加Redis连接池大小

#### 日志分析

查看错误日志：

```bash
# 查找连接错误
grep "connection" /path/to/logs/chat_service.log

# 查找认证错误
grep "auth" /path/to/logs/chat_service.log

# 查找Redis错误
grep "redis" /path/to/logs/chat_service.log
```

### 与主服务器的集成

1. **JWT认证**: 使用与主服务器相同的JWT密钥和算法
2. **用户信息**: 从JWT令牌中获取用户ID和姓名
3. **服务发现**: 客户端需要知道聊天服务的地址（localhost:8005）

### 扩展和定制

#### 添加新的消息类型

1. 在 `chat_websocket.py` 中添加新的消息处理逻辑
2. 更新客户端以支持新的消息类型
3. 如需持久化，在Redis服务中添加相应逻辑

#### 添加新的API端点

1. 在 `chat_management.py` 中添加新的路由
2. 添加相应的认证和权限检查
3. 更新API文档

#### 性能优化

1. **连接池优化**: 调整Redis连接池大小
2. **消息批处理**: 实现消息批量发送
3. **负载均衡**: 部署多个微服务实例

---

**维护者**: MySuite开发团队  
**更新时间**: 2025-06-18 20:15  
**版本**: 1.0.0 