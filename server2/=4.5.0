Collecting redis[hiredis]
  Using cached redis-6.2.0-py3-none-any.whl.metadata (10 kB)
Collecting hiredis>=3.2.0 (from redis[hiredis])
  Using cached hiredis-3.2.1-cp312-cp312-manylinux_2_17_x86_64.manylinux2014_x86_64.whl.metadata (7.2 kB)
Using cached redis-6.2.0-py3-none-any.whl (278 kB)
Using cached hiredis-3.2.1-cp312-cp312-manylinux_2_17_x86_64.manylinux2014_x86_64.whl (173 kB)
Installing collected packages: redis, hiredis
  Attempting uninstall: hiredis
    Found existing installation: hiredis 3.1.0
    Uninstalling hiredis-3.1.0:
      Successfully uninstalled hiredis-3.1.0

Successfully installed hiredis-3.2.1 redis-6.2.0
