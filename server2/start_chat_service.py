# server2/start_chat_service.py
# 20250618.20:15 微服务-信息交流 - 微服务器启动脚本
"""
MySuite 信息交流微服务器启动脚本
使用端口 8005
"""

import uvicorn
import sys
import os
from pathlib import Path

# 20250618.20:15 微服务-信息交流 - 添加应用路径到Python路径
current_dir = Path(__file__).parent
app_dir = current_dir / "app"
sys.path.insert(0, str(current_dir))

def main():
    """20250618.20:15 微服务-信息交流 - 主启动函数"""
    print("=" * 60)
    print("🚀 Starting MySuite Chat Microservice")
    print("=" * 60)
    print(f"📁 Working Directory: {current_dir}")
    print(f"🌐 Service Port: 8005")
    print(f"📝 API Documentation: http://localhost:8005/docs")
    print(f"🔍 Health Check: http://localhost:8005/health")
    print(f"💬 WebSocket: ws://localhost:8005/ws/chat")
    print("=" * 60)
    
    try:
        # 20250618.20:15 微服务-信息交流 - 启动uvicorn服务器
        uvicorn.run(
            "app.main:app",
            host="0.0.0.0",
            port=8005,
            reload=True,
            log_level="info",
            access_log=True,
            reload_dirs=[str(app_dir)],
            workers=1  # WebSocket需要单worker模式
        )
    except KeyboardInterrupt:
        print("\n🛑 Service stopped by user")
    except Exception as e:
        print(f"❌ Error starting service: {e}")
        sys.exit(1)

if __name__ == "__main__":
    main() 