# server2/simple_test.py
# 20250618.20:15 微服务-信息交流 - 简单测试脚本
"""
简单的微服务器测试脚本，不依赖Redis
"""

from fastapi import FastAPI
import uvicorn

# 20250618.20:15 微服务-信息交流 - 创建简单的测试应用
app = FastAPI(
    title="MySuite Chat Microservice Test",
    description="简单测试版本",
    version="1.0.0"
)

@app.get("/")
async def root():
    """根路径"""
    return {
        "message": "MySuite Chat Microservice Test",
        "status": "running",
        "version": "1.0.0"
    }

@app.get("/health")
async def health_check():
    """健康检查"""
    return {
        "status": "healthy",
        "service": "MySuite Chat Microservice Test",
        "version": "1.0.0"
    }

if __name__ == "__main__":
    print("🚀 Starting simple test service on port 8005...")
    uvicorn.run(
        "simple_test:app",
        host="0.0.0.0",
        port=8005,
        reload=False,
        log_level="info"
    ) 