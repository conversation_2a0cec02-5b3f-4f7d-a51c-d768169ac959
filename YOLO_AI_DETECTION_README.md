# MySuite YOLO AI物体检测功能

**20250619.18:30 - YOLO AI物体检测集成完成**

## 🤖 功能概述

MySuite视频监控系统现已集成YOLOv8 AI物体检测功能，能够实时识别视频画面中的80+种物体，包括人员、车辆、动物、家具、电子设备等。

## ✨ 主要特性

- **实时物体检测**: 基于YOLOv8模型的高精度物体识别
- **80+物体类别**: 支持人员、车辆、动物、家具等多种物体类别
- **可视化检测框**: 在视频画面中显示检测框和置信度
- **统计信息**: 实时显示检测统计和物体类别分布
- **GPU/CPU支持**: 自动检测硬件环境，支持GPU加速
- **易用控制**: 客户端一键初始化和启用/禁用检测

## 🛠️ 安装依赖

### 自动安装（推荐）
```bash
./install_yolo_dependencies.sh
```

### 手动安装
```bash
# 安装PyTorch (CPU版本)
pip3 install torch torchvision --index-url https://download.pytorch.org/whl/cpu

# 安装Ultralytics YOLO
pip3 install ultralytics

# 安装图像处理依赖
pip3 install pillow opencv-python numpy pandas

# 安装server4依赖
cd server4
pip3 install -r requirements.txt
```

### GPU加速（可选）
如需GPU加速，请安装CUDA版本的PyTorch：
```bash
# 根据CUDA版本选择对应的PyTorch
pip3 install torch torchvision --index-url https://download.pytorch.org/whl/cu118
```

## 🚀 使用方法

### 1. 启动服务
```bash
# 启动视频监控服务
python3 server4/start_video_service.py
```

### 2. 客户端操作
1. 启动客户端：`python3 client/client_fixed.py`
2. 登录后点击第3个标签页 "视频监控"
3. 点击 "连接视频" 按钮连接视频服务
4. 点击 "初始化YOLO" 按钮加载AI模型（首次使用会下载模型文件）
5. 点击 "启用AI检测" 按钮开始物体检测
6. 观察视频画面中的检测框和底部统计信息

### 3. API调用
```python
import requests

# 初始化YOLO模型
response = requests.post("http://localhost:8007/api/video/yolo/initialize")

# 启用检测
response = requests.post("http://localhost:8007/api/video/yolo/enable")

# 获取检测状态
response = requests.get("http://localhost:8007/api/video/yolo/status")

# 禁用检测
response = requests.post("http://localhost:8007/api/video/yolo/disable")
```

## 🧪 测试功能

### 运行测试脚本
```bash
python3 test_yolo_detection.py
```

测试脚本会自动：
- 检查视频服务状态
- 初始化YOLO模型
- 启用检测功能
- 监控检测结果
- 获取带检测框的快照

## 📊 支持的物体类别

YOLO模型基于COCO数据集训练，支持80种物体类别：

### 人员和动物
- person (人员)
- cat, dog (猫、狗)
- horse, sheep, cow (马、羊、牛)
- elephant, bear, zebra, giraffe (大象、熊、斑马、长颈鹿)
- bird (鸟类)

### 交通工具
- bicycle (自行车)
- car, motorcycle (汽车、摩托车)
- airplane, bus, train, truck, boat (飞机、公交车、火车、卡车、船)

### 日常用品
- bottle, cup, wine glass (瓶子、杯子、酒杯)
- fork, knife, spoon, bowl (叉子、刀子、勺子、碗)
- banana, apple, orange (香蕉、苹果、橙子)
- chair, couch, bed (椅子、沙发、床)
- tv, laptop, mouse, keyboard (电视、笔记本、鼠标、键盘)

### 其他物品
- backpack, umbrella, handbag, suitcase (背包、雨伞、手提包、行李箱)
- sports ball, kite, baseball bat (运动球、风筝、棒球棒)
- book, clock, vase, scissors (书、时钟、花瓶、剪刀)

## ⚙️ 配置说明

### server4/app/config.py
```python
# YOLO配置
YOLO_ENABLED: bool = False          # 默认禁用
YOLO_MODEL_PATH: str = "yolov8n.pt" # 模型文件路径
YOLO_CONFIDENCE: float = 0.5        # 置信度阈值
YOLO_IOU_THRESHOLD: float = 0.5     # IOU阈值
```

### 模型文件
- **yolov8n.pt**: 纳米版本，速度快，精度中等（约6MB）
- **yolov8s.pt**: 小型版本，平衡速度和精度（约22MB）
- **yolov8m.pt**: 中型版本，精度较高（约52MB）
- **yolov8l.pt**: 大型版本，精度很高（约88MB）
- **yolov8x.pt**: 超大版本，精度最高（约136MB）

## 🔧 API端点

### YOLO管理端点
- `POST /api/video/yolo/initialize` - 初始化YOLO模型
- `POST /api/video/yolo/enable` - 启用物体检测
- `POST /api/video/yolo/disable` - 禁用物体检测
- `GET /api/video/yolo/status` - 获取检测状态和统计信息

### 响应示例
```json
{
  "success": true,
  "data": {
    "yolo_available": true,
    "model_loaded": true,
    "is_enabled": true,
    "video_yolo_enabled": true,
    "total_detections": 1250,
    "objects_detected": 3,
    "detection_classes": {
      "person": 450,
      "car": 230,
      "chair": 120
    },
    "last_detections": [
      {
        "class_name": "person",
        "confidence": 0.89,
        "bbox": [100, 150, 200, 350]
      }
    ]
  }
}
```

## 📈 性能说明

### 检测性能
- **YOLOv8n**: ~50-100 FPS (CPU), ~200+ FPS (GPU)
- **YOLOv8s**: ~30-60 FPS (CPU), ~150+ FPS (GPU)
- **YOLOv8m**: ~20-40 FPS (CPU), ~100+ FPS (GPU)

### 内存使用
- **模型加载**: 50-200MB (根据模型大小)
- **推理内存**: 10-50MB (根据图像分辨率)

### 精度指标
- **mAP50**: 37.3% (YOLOv8n) - 53.9% (YOLOv8x)
- **mAP50-95**: 53.2% (YOLOv8n) - 68.9% (YOLOv8x)

## 🐛 故障排除

### 常见问题

1. **YOLO依赖不可用**
   ```
   解决方案: 运行 ./install_yolo_dependencies.sh
   ```

2. **模型加载失败**
   ```
   检查网络连接，首次使用会下载模型文件
   检查磁盘空间是否充足
   ```

3. **检测性能低**
   ```
   使用更小的模型 (yolov8n.pt)
   降低视频分辨率
   启用GPU加速
   ```

4. **内存不足**
   ```
   使用yolov8n.pt纳米模型
   降低视频分辨率到320x240
   增加系统内存
   ```

### 日志查看
```bash
# 查看视频服务日志
tail -f logs/video_service.log

# 查看YOLO相关日志
grep -i yolo logs/video_service.log
```

## 🔮 未来扩展

### 计划功能
- [ ] 自定义物体检测模型
- [ ] 检测区域设置
- [ ] 物体跟踪功能
- [ ] 检测事件录制
- [ ] 检测结果导出
- [ ] 多摄像头检测
- [ ] 实时告警功能

### 模型升级
- [ ] YOLOv9/v10支持
- [ ] 专业领域模型（安防、工业等）
- [ ] 自训练模型支持

## 📄 技术架构

```
Client (PyQt6)
    ↓ HTTP API / WebSocket
Server4 (FastAPI)
    ↓ 
VideoService
    ↓
YOLOService (Ultralytics)
    ↓
PyTorch Backend
    ↓
Camera (OpenCV)
```

## 🎯 总结

MySuite YOLO AI物体检测功能为视频监控系统添加了强大的AI能力，支持实时物体识别和可视化展示。通过简单的界面操作即可启用高精度的物体检测，为安防监控、行为分析等应用场景提供了强有力的技术支持。

---

**开发完成时间**: 2025年6月19日 18:30  
**版本**: v1.0.0  
**状态**: ✅ 已完成并测试 