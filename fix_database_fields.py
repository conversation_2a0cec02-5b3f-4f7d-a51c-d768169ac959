# 250626，第二阶段: 批量修复数据库字段问题
# 修复Server5服务中的字段名称不匹配问题

import asyncio
import asyncpg
import logging

# --- 配置 ---
# 请根据您的实际情况修改数据库连接信息
DB_HOST = '************'
DB_PORT = 5432
DB_USER = 'postgres'
DB_PASSWORD = 'your_password'  # 警告：请在这里填入您的数据库密码
DB_NAME = 'mysuite'

TABLE_NAME = 'entries_push_queue'

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')

async def fix_table_schema():
    """
    连接到PostgreSQL数据库并向entries_push_queue表添加缺失的字段。
    """
    conn = None
    try:
        # 建立数据库连接
        conn = await asyncpg.connect(
            user=DB_USER,
            password=DB_PASSWORD,
            database=DB_NAME,
            host=DB_HOST,
            port=DB_PORT
        )
        logging.info(f"✅ 成功连接到数据库 '{DB_NAME}' on {DB_HOST}")

        # --- 1. 添加 retry_count 字段 ---
        logging.info(f"检查并添加 'retry_count' 字段到 '{TABLE_NAME}' 表...")
        await conn.execute(f"""
            ALTER TABLE public.{TABLE_NAME}
            ADD COLUMN IF NOT EXISTS retry_count INTEGER DEFAULT 0;
        """)
        await conn.execute(f"""
            COMMENT ON COLUMN public.{TABLE_NAME}.retry_count IS '记录任务失败重试的次数';
        """)
        logging.info("✔️ 'retry_count' 字段检查/添加完成。")

        # --- 2. 添加 last_error 字段 ---
        logging.info(f"检查并添加 'last_error' 字段到 '{TABLE_NAME}' 表...")
        await conn.execute(f"""
            ALTER TABLE public.{TABLE_NAME}
            ADD COLUMN IF NOT EXISTS last_error TEXT;
        """)
        await conn.execute(f"""
            COMMENT ON COLUMN public.{TABLE_NAME}.last_error IS '记录最后一次失败的错误信息';
        """)
        logging.info("✔️ 'last_error' 字段检查/添加完成。")

        # --- 3. 为新字段创建索引 ---
        logging.info("为 'retry_count' 字段创建索引...")
        await conn.execute(f"""
            CREATE INDEX IF NOT EXISTS idx_entries_push_queue_retry_count
            ON public.{TABLE_NAME}(retry_count)
            WHERE synced = FALSE;
        """)
        logging.info("✔️ 索引创建完成。")

        # --- 4. 初始化现有记录的重试次数 ---
        logging.info("更新现有记录的 retry_count 为 0 (如果为NULL)...")
        await conn.execute(f"""
            UPDATE public.{TABLE_NAME}
            SET retry_count = 0
            WHERE retry_count IS NULL;
        """)
        logging.info("✔️ 现有记录更新完成。")

        logging.info("🎉 数据库表结构修复成功！")

    except asyncpg.exceptions.InvalidPasswordError:
        logging.error("❌ 数据库密码错误。请在脚本中提供正确的密码。")
    except ConnectionRefusedError:
        logging.error(f"❌ 数据库连接被拒绝。请确认数据库服务器 ({DB_HOST}:{DB_PORT}) 正在运行且网络可达。")
    except Exception as e:
        logging.error(f"❌ 数据库操作失败: {e}")
        logging.error("请检查数据库连接信息是否正确，以及用户是否有权限修改表结构。")
    finally:
        if conn:
            await conn.close()
            logging.info("🔌 数据库连接已关闭。")

if __name__ == "__main__":
    # 在运行此脚本之前，请确保在上面的 DB_PASSWORD 变量中填入正确的密码
    if DB_PASSWORD == 'your_password':
        logging.warning("警告：请在脚本中设置正确的数据库密码 (DB_PASSWORD)。脚本将不会执行。")
    else:
        asyncio.run(fix_table_schema()) 