#!/usr/bin/env python3
"""
测试program1修复的脚本
"""

import sys
import os
sys.path.append(os.path.join(os.path.dirname(__file__), 'client'))

from program1 import Server5APIClient
from datetime import datetime

def test_server5_api():
    """测试Server5 API连接"""
    print("🧪 测试Server5 API连接...")
    
    # 创建Server5APIClient实例 - 使用端口8009
    client = Server5APIClient("http://localhost:8009")
    
    try:
        # 测试获取entries数据
        print("📡 测试获取entries数据...")
        result = client.get_entries(
            employee_id="215829",
            start_date="2025-07-01",
            end_date="2025-07-31",
            limit=5
        )
        
        if result.get("ok"):
            data = result.get("data", [])
            print(f"✅ entries数据获取成功: {len(data)}条记录")
            if data:
                print(f"   第一条记录: {data[0]}")
        else:
            print(f"❌ entries数据获取失败: {result.get('error', '未知错误')}")
        
        # 测试获取timeprotab数据
        print("📡 测试获取timeprotab数据...")
        result = client.get_timeprotab_data(
            employee_id="215829",
            year=2025,
            month=7
        )
        
        if result.get("ok"):
            data = result.get("data", [])
            print(f"✅ timeprotab数据获取成功: {len(data)}条记录")
            if data:
                print(f"   第一条记录: {data[0]}")
        else:
            print(f"❌ timeprotab数据获取失败: {result.get('error', '未知错误')}")
        
        # 测试获取可用月份
        print("📡 测试获取可用月份...")
        result = client.get_available_months("215829")
        
        if result.get("ok"):
            data = result.get("data", {})
            months = data.get("months", [])
            print(f"✅ 可用月份获取成功: {len(months)}个月份")
            if months:
                print(f"   前3个月份: {months[:3]}")
        else:
            print(f"❌ 可用月份获取失败: {result.get('error', '未知错误')}")
        
        print("🎉 Server5 API测试完成！")
        
    except Exception as e:
        print(f"❌ 测试过程中发生异常: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    test_server5_api() 