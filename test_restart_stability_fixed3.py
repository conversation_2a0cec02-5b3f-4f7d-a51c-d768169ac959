#!/usr/bin/env python3
"""
🧪 2025-07-10 稳定性测试脚本（Server5 专用）
验证修复后的 Server5 能否:
1. 健康检查 (/health)
2. 异步接口可用性
3. 超时和重试机制
4. 资源清理
5. 客户端重启稳定性
"""

import sys
import time
import subprocess
import requests
from pathlib import Path

def test_server5_api():
    """1. 测试 Server5 健康检查"""
    print("🔄 测试 Server5 健康检查 (/health)...")
    try:
        r = requests.get("http://localhost:8009/health", timeout=5)
        if r.status_code == 200 and r.json().get("status") == "ok":
            print("✅ Server5 /health 正常")
            return True
        else:
            print(f"❌ /health 异常: status={r.status_code}, body={r.text}")
    except Exception as e:
        print(f"❌ Server5 健康检查失败: {e}")
    return False

def test_async_loading_functionality():
    """2. 测试 Server5 异步加载的各接口"""
    print("\n🔄 测试异步加载接口…")
    endpoints = [
        ("timeprotab",     "http://localhost:8009/api/timeprotab/",     {"employee_id":"TEST001","year":2025,"month":1}),
        ("entries",        "http://localhost:8009/api/entries/",        {"employee_id":"TEST001","start_date":"2025-01-01","end_date":"2025-01-31","limit":1}),
        ("entries months", "http://localhost:8009/api/entries/months", {"employee_id":"TEST001"}),
        ("chart data",     "http://localhost:8009/api/entries/chart-data",{"employee_id":"TEST001","start_date":"2025-01-01","end_date":"2025-01-31","chart_type":"daily"}),
    ]
    ok = True
    for name, url, params in endpoints:
        print(f"→ 测试 {name} …")
        t0 = time.time()
        try:
            resp = requests.get(url, params=params, timeout=10)
            dt = time.time() - t0
            if resp.status_code == 200:
                print(f"  ✅ {name} 响应正常 ({dt:.2f}s)")
            else:
                print(f"  ❌ {name} 返回 {resp.status_code} ({resp.text})")
                ok = False
        except Exception as e:
            print(f"  ❌ {name} 调用失败: {e}")
            ok = False
    return ok

def test_timeout_and_retry():
    """3. 测试 404/超时 异常处理"""
    print("\n🔄 测试超时/404处理…")
    # 不存在的端点应在 2s 内超时或返回 404
    for url in [
        "http://localhost:8009/api/nonexistent",
        "http://localhost:8009/api/entries"  # 缺少必需参数
    ]:
        print(f"→ 访问 {url}")
        t0 = time.time()
        try:
            r = requests.get(url, timeout=2)
            dt = time.time() - t0
            print(f"  ⚠️ 意外响应: {r.status_code} ({dt:.2f}s)")
            return False
        except requests.exceptions.Timeout:
            print(f"  ✅ 超时机制正常 ({time.time()-t0:.2f}s)")
        except requests.exceptions.HTTPError as e:
            print(f"  ✅ HTTP 错误正常: {e}")
        except Exception as e:
            print(f"  ✅ 网络错误正常: {e}")
    return True

def simulate_client_restart():
    """4. 模拟 Program1 客户端重启稳定性"""
    print("\n🔄 客户端重启测试…")
    client = Path(__file__).parent / "program1.py"
    if not client.exists():
        print(f"❌ 找不到 {client}")
        return False

    for i in range(3):
        print(f"  🔄 第 {i+1} 次启动…")
        proc = subprocess.Popen(
            [sys.executable, str(client), "dummy_token", "TEST001", "测试用户"],
            stdout=subprocess.PIPE, stderr=subprocess.PIPE, text=True
        )
        time.sleep(8)
        if proc.poll() is None:
            proc.terminate()
            proc.wait(5)
            print("    ✅ 正常终止")
        else:
            out, err = proc.communicate()
            print(f"    ❌ 进程退出 (code={proc.returncode})\n{out}{err}")
            return False
        time.sleep(2)
    print("✅ 客户端重启稳定")
    return True

def test_resource_cleanup():
    """5. 测试残留进程清理"""
    print("\n🔄 测试资源清理…")
    try:
        import psutil
        procs = []
        for p in psutil.process_iter(['pid','cmdline']):
            cmd = p.info.get('cmdline') or []
            if 'program1.py' in ' '.join(cmd):
                procs.append(p.pid)
        if procs:
            print(f"⚠️ 残留进程: {procs}")
            return False
        else:
            print("✅ 无残留程序")
            return True
    except ImportError:
        print("⚠️ psutil 未安装，跳过清理检测")
        return True

def main():
    print("="*60)
    print("🧪 MySuite Server5 稳定性测试")
    print("="*60)
    results = {}
    results['health'] = test_server5_api()
    results['async']  = test_async_loading_functionality()
    results['error']  = test_timeout_and_retry()
    results['restart']= simulate_client_restart() if results['health'] else None
    results['clean']  = test_resource_cleanup()

    print("\n" + "="*60)
    print("📊 测试结果:")
    print("="*60)
    passed = sum(1 for v in results.values() if v)
    total  = sum(1 for v in results.values() if v is not None)
    for name, ok in results.items():
        status = "⏭️ 跳过" if ok is None else ("✅" if ok else "❌")
        print(f"{name:12}: {status}")
    print(f"\n🎯 通过率: {passed}/{total}")
    sys.exit(0 if passed==total else 1)

if __name__=="__main__":
    main()
